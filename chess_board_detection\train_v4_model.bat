@echo off
echo Starting training of Chess Board Detection v4 model...
echo.

python train_v4.py ^
--data_dir chess_board_detection\data ^
--output_dir chess_board_detection\models\improved_corner_detection ^
--epochs 30 ^
--lr 0.0005 ^
--heatmap_weight 1.5 ^
--geometric_weight 0.4 ^
--separation_weight 0.6 ^
--peak_separation_weight 0.5 ^
--edge_suppression_weight 0.7 ^
--peak_enhancement_weight 0.5 ^
--peak_to_second_ratio_weight 1.0 ^
--detection_rate_weight 1.0 ^
--start_from_epoch 94

echo.
echo Training script completed.
pause
