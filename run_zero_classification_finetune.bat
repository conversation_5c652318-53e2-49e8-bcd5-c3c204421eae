@echo off
echo Zero Classification Loss Fine-tuning for Chess Piece Detection
echo =========================================================
echo.

REM Set paths
REM Choose one of the model paths below by uncommenting it

REM Original best model
REM set MODEL_PATH="C:\Users\<USER>\OneDrive\Desktop\a1 v1\runs\detect\train\weights\best.pt"

REM YOLO v11n base model
REM set MODEL_PATH="C:\Users\<USER>\OneDrive\Desktop\a1 v1\yolo11n.pt"

REM Best model from epoch 86 (recommended)
set MODEL_PATH="C:\Users\<USER>\OneDrive\Desktop\a1 v1\runs\detect\train\weights\best.pt"
set TEST_IMAGES="C:\Users\<USER>\OneDrive\Desktop\New folder (4)"
set OUTPUT_DIR="runs\zero_cls_loss"
set FEEDBACK_DB="zero_cls_feedback.json"
set TRAINING_DATA="C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\dataset"
set EPOCHS=15
set BATCH_SIZE=16
set MIN_FEEDBACK=3
set CLS_WEIGHT=10.0
set TARGET_PRECISION=0.99
set TARGET_RECALL=0.99

REM Enable dynamic weight adjustment
set DYNAMIC_WEIGHTS=true

echo Starting automated feedback loop with:
echo Model: %MODEL_PATH%
echo Test images: %TEST_IMAGES%
echo Training data: %TRAINING_DATA%
echo Initial classification loss weight: %CLS_WEIGHT%
echo Target precision: %TARGET_PRECISION%
echo Target recall: %TARGET_RECALL%
echo Dynamic weight adjustment: %DYNAMIC_WEIGHTS%
echo Matching method: Corner-based nearest neighbor (50px threshold)
echo.

python zero_classification_loss_finetune.py loop ^
    --model %MODEL_PATH% ^
    --images %TEST_IMAGES% ^
    --training-data %TRAINING_DATA% ^
    --automated ^
    --epochs %EPOCHS% ^
    --batch %BATCH_SIZE% ^
    --min-feedback %MIN_FEEDBACK% ^
    --output-dir %OUTPUT_DIR% ^
    --db %FEEDBACK_DB% ^
    --cls-weight %CLS_WEIGHT% ^
    --dynamic-weights ^
    --target-precision %TARGET_PRECISION% ^
    --target-recall %TARGET_RECALL%

echo.
echo Automated feedback loop completed.
pause
