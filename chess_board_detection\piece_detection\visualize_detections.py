"""
Visualize chess piece detections with improved formatting.
This script applies the trained YOLO model with customized display settings.
"""

import os
import sys
import argparse
import logging
import time
from pathlib import Path
from ultralytics import YOLO
import cv2
import numpy as np

# Create logs directory if it doesn't exist
os.makedirs("chess_board_detection/logs", exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("chess_board_detection/logs/piece_detection.log")
    ]
)
logger = logging.getLogger("piece_detection")

def visualize_detections(
    model_path,
    source,
    output_dir=None,
    conf_threshold=0.7,  # Increased from 0.25 to 0.7
    show_labels=True,
    show_conf=False,
    line_width=1,
    font_size=0.4,
    font_thickness=1
):
    """
    Detect chess pieces in images using the trained YOLO model with improved visualization.

    Args:
        model_path: Path to the trained model
        source: Path to image or directory of images
        output_dir: Directory to save results
        conf_threshold: Confidence threshold for detections
        show_labels: Whether to show labels
        show_conf: Whether to show confidence scores
        line_width: Width of bounding box lines
        font_size: Size of font for labels
        font_thickness: Thickness of font for labels
    """
    # Create output directory if needed
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Output directory: {output_dir}")

    logger.info(f"Starting detection with model: {model_path}")
    logger.info(f"Source: {source}")
    logger.info(f"Confidence threshold: {conf_threshold}")
    logger.info(f"Show labels: {show_labels}")
    logger.info(f"Show confidence: {show_conf}")

    # Load the model
    start_time = time.time()
    model = YOLO(model_path)
    logger.info(f"Model loaded in {time.time() - start_time:.2f} seconds")

    # Custom class names with shorter format (color_piece)
    custom_names = {
        0: "w_pawn",
        1: "w_knight",
        2: "w_bishop",
        3: "w_rook",
        4: "w_queen",
        5: "w_king",
        6: "b_pawn",
        7: "b_knight",
        8: "b_bishop",
        9: "b_rook",
        10: "b_queen",
        11: "b_king"
    }

    # We can't directly set model.names, so we'll use the custom names in our visualization

    # Run inference with a much higher NMS IoU threshold to prevent duplicate detections
    logger.info(f"Running inference with IoU threshold: 0.9 (default is 0.45)")
    inference_start = time.time()
    results = model.predict(
        source=source,
        conf=conf_threshold,
        save=False,  # Don't save automatically, we'll create custom visualizations
        verbose=True,
        iou=0.9  # Much higher IoU threshold for NMS (default is 0.45)
    )
    inference_time = time.time() - inference_start
    logger.info(f"Inference completed in {inference_time:.2f} seconds")

    # Additional post-processing to remove overlapping detections
    logger.info("Starting additional post-processing to remove overlapping detections")
    post_processing_start = time.time()
    processed_results = []
    for result in results:
        boxes = result.boxes.xyxy.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy().astype(int)
        confs = result.boxes.conf.cpu().numpy()

        # If no detections, just add the result as is
        if len(boxes) == 0:
            processed_results.append(result)
            continue

        # Calculate areas for each box
        areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

        # Sort by confidence (highest first)
        order = np.argsort(-confs)

        keep = []
        while len(order) > 0:
            i = order[0]
            keep.append(i)

            # Calculate IoU with remaining boxes
            xx1 = np.maximum(boxes[i, 0], boxes[order[1:], 0])
            yy1 = np.maximum(boxes[i, 1], boxes[order[1:], 1])
            xx2 = np.minimum(boxes[i, 2], boxes[order[1:], 2])
            yy2 = np.minimum(boxes[i, 3], boxes[order[1:], 3])

            w = np.maximum(0.0, xx2 - xx1)
            h = np.maximum(0.0, yy2 - yy1)
            inter = w * h

            # Calculate IoU
            ovr = inter / (areas[i] + areas[order[1:]] - inter)

            # Get indices of boxes with IoU <= 0.5 (much stricter than default)
            inds = np.where(ovr <= 0.5)[0]
            order = order[inds + 1]

        # Create a new result with only the kept boxes
        result.boxes = result.boxes[keep]
        processed_results.append(result)

    results = processed_results
    post_processing_time = time.time() - post_processing_start
    logger.info(f"Post-processing completed in {post_processing_time:.2f} seconds")

    # Log detection counts before and after post-processing
    original_detection_count = sum(len(result.boxes) for result in results)
    logger.info(f"Total detections after post-processing: {original_detection_count}")

    # Process and display results
    logger.info("Starting visualization of results")
    visualization_start = time.time()
    for i, result in enumerate(results):
        # Get the original image
        img = result.orig_img

        # Get detections
        boxes = result.boxes.xyxy.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy().astype(int)
        confs = result.boxes.conf.cpu().numpy()

        # Create a clean image without annotations
        clean_img = img.copy()

        # Draw custom annotations
        for box, cls, conf in zip(boxes, classes, confs):
            x1, y1, x2, y2 = box.astype(int)

            # Get class name - use our custom names instead of model.names
            original_name = result.names[cls]
            # Convert from original name to our shorter format
            if original_name.startswith("white_"):
                class_name = "w_" + original_name[6:]
                color = (0, 255, 0)  # Green for white pieces
            elif original_name.startswith("black_"):
                class_name = "b_" + original_name[6:]
                color = (255, 0, 255)  # Magenta for black pieces
            else:
                class_name = original_name
                color = (0, 255, 255)  # Yellow for unknown

            # Draw bounding box
            cv2.rectangle(clean_img, (x1, y1), (x2, y2), color, line_width)

            # Draw label
            if show_labels:
                label = f"{class_name}"
                if show_conf:
                    label += f" {conf:.2f}"

                # Calculate box width
                box_width = x2 - x1

                # Start with the provided font size and adjust to fit the box width
                # Use binary search to find the optimal font size
                max_font_size = font_size * 1.5  # Allow larger font size than default
                min_font_size = 0.1  # Minimum readable font size
                target_width_ratio = 0.95  # Target width is 95% of box width (increased from 90%)
                target_width = box_width * target_width_ratio

                # Initial check with provided font size
                (text_width, text_height), _ = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, max_font_size, font_thickness
                )

                # If text is already smaller than target, we're good
                if text_width <= target_width:
                    adjusted_font_size = max_font_size
                else:
                    # Binary search for optimal font size
                    adjusted_font_size = max_font_size
                    iterations = 0
                    max_iterations = 10  # Prevent infinite loops

                    while iterations < max_iterations:
                        mid_font_size = (min_font_size + max_font_size) / 2
                        (text_width, text_height), _ = cv2.getTextSize(
                            label, cv2.FONT_HERSHEY_SIMPLEX, mid_font_size, font_thickness
                        )

                        if abs(text_width - target_width) < 2:  # Close enough
                            adjusted_font_size = mid_font_size
                            break
                        elif text_width > target_width:
                            max_font_size = mid_font_size
                        else:
                            min_font_size = mid_font_size
                            adjusted_font_size = mid_font_size

                        iterations += 1

                # Get final text dimensions with adjusted font size
                (text_width, text_height), _ = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, adjusted_font_size, font_thickness
                )

                # Center text horizontally in the box
                text_x = x1 + (box_width - text_width) // 2

                # Draw label background
                cv2.rectangle(
                    clean_img,
                    (text_x, y1 - text_height - 5),
                    (text_x + text_width, y1),
                    color,
                    -1
                )

                # Draw text
                cv2.putText(
                    clean_img,
                    label,
                    (text_x, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    adjusted_font_size,
                    (255, 255, 255),
                    font_thickness
                )

        # Save the custom annotated image
        if output_dir:
            source_path = Path(source) if isinstance(source, str) else Path(source[i])
            output_path = Path(output_dir) / f"detection_{source_path.name}"
            cv2.imwrite(str(output_path), clean_img)
            logger.info(f"Saved custom result to {output_path}")

    visualization_time = time.time() - visualization_start
    logger.info(f"Visualization completed in {visualization_time:.2f} seconds")

    # Log detailed detection statistics
    class_counts = {}
    for result in results:
        for cls in result.boxes.cls.cpu().numpy().astype(int):
            class_name = result.names[cls]
            if class_name not in class_counts:
                class_counts[class_name] = 0
            class_counts[class_name] += 1

    logger.info("Detection counts by class:")
    for class_name, count in class_counts.items():
        logger.info(f"  {class_name}: {count}")

    total_time = time.time() - start_time
    logger.info(f"Total processing time: {total_time:.2f} seconds")

    return results

def main():
    parser = argparse.ArgumentParser(description="Visualize chess piece detections")
    parser.add_argument("--model", type=str, required=True, help="Path to trained YOLO model")
    parser.add_argument("--input", type=str, required=True, help="Path to input image or directory")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/outputs/piece_detection", help="Directory to save results")
    parser.add_argument("--conf", type=float, default=0.7, help="Confidence threshold")
    parser.add_argument("--show_labels", action="store_true", default=True, help="Show labels")
    parser.add_argument("--show_conf", action="store_true", default=False, help="Show confidence scores")
    parser.add_argument("--line_width", type=int, default=1, help="Width of bounding box lines")
    parser.add_argument("--font_size", type=float, default=0.4, help="Size of font for labels")
    parser.add_argument("--font_thickness", type=int, default=1, help="Thickness of font for labels")

    args = parser.parse_args()

    visualize_detections(
        args.model,
        args.input,
        args.output_dir,
        args.conf,
        args.show_labels,
        args.show_conf,
        args.line_width,
        args.font_size,
        args.font_thickness
    )

if __name__ == "__main__":
    main()
