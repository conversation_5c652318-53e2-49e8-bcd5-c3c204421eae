# High-Accuracy Chess Piece Detection (99%+ Accuracy)

This document outlines the approach for training a chess piece detection model with 99%+ accuracy and precision, specifically addressing color misclassification issues.

## Overview

The high-accuracy training pipeline consists of:

1. **Enhanced Dataset Creation**: Generate a larger, more diverse dataset with color-preserving augmentations
2. **Two-Phase Training**: Initial training followed by fine-tuning with specialized parameters
3. **Advanced Augmentation**: Carefully designed augmentation that preserves piece color information
4. **Rigorous Evaluation**: Comprehensive testing with focus on problem cases

## Requirements

- NVIDIA GPU with at least 6GB VRAM (RTX 3050 or better)
- PyTorch 2.5.1+ with CUDA support
- Ultralytics 8.3.139+
- Albumentations for advanced augmentation

## Step 1: Create Enhanced Dataset

The enhanced dataset creation focuses on preserving color information while providing diverse training examples:

```bash
python chess_board_detection/piece_detection/create_enhanced_dataset.py \
    --input_images chess_board_detection/piece_detection/dataset/images \
    --input_labels chess_board_detection/piece_detection/dataset/labels \
    --output_dir chess_board_detection/piece_detection/enhanced_dataset_99plus \
    --num_augmentations 20 \
    --train_ratio 0.8 \
    --min_val_examples 3
```

### Key Features of Enhanced Dataset:

- **Stratified Splitting**: Ensures all piece types (including rare ones like queens) are represented in both training and validation sets
- **Color-Preserving Augmentations**: Carefully tuned to maintain the distinction between black and white pieces
- **Lighting Variations**: Simulates different lighting conditions while preserving piece color
- **Geometric Transformations**: Limited to preserve piece orientation
- **20x Augmentation**: Each original image generates 20 augmented variants
- **Class Distribution Analysis**: Detailed reporting of class distribution across train/validation splits

## Step 2: Dynamic Target-Based Training

The training process uses a dynamic approach that continues until target metrics are achieved or no improvement is seen for a set number of epochs:

```bash
python chess_board_detection/piece_detection/train_high_accuracy.py \
    --model yolo11n.pt \
    --data_yaml chess_board_detection/piece_detection/enhanced_dataset_99plus/dataset.yaml \
    --output_dir chess_board_detection/piece_detection/models/high_accuracy_yolo \
    --max_phase1_epochs 100 \
    --max_phase2_epochs 200 \
    --precision_target 0.99 \
    --recall_target 0.99 \
    --map50_target 0.99 \
    --color_accuracy_target 0.99 \
    --patience 50 \
    --check_targets_every 5 \
    --improvement_threshold 0.0005
```

### Key Features of Dynamic Training:

1. **Target-Based Stopping**: Training stops when all target metrics are achieved
2. **Multi-Metric Improvement Tracking**: Uses a weighted combination of metrics to determine if the model is improving
3. **Comprehensive Early Stopping**: Stops training based on multiple criteria:
   - No improvement for a set number of epochs
   - Stagnation in key metrics
   - Diverging loss values
   - Plateauing metrics near target values
4. **Trend Analysis**: Monitors trends in metrics to make intelligent stopping decisions
5. **Regular Metric Checking**: Evaluates progress toward targets every N epochs
6. **Two-Phase Approach**: Only proceeds to Phase 2 if targets aren't met in Phase 1

### Phase 1: Initial Training (max 100 epochs)

Phase 1 uses:
- Higher learning rate (0.01)
- Strong augmentation
- Mosaic and mixup enabled
- Focus on general feature learning
- Stops early if targets are met

### Phase 2: Fine-tuning (max 200 epochs)

Only runs if Phase 1 doesn't achieve targets, using:
- Much lower learning rate (0.001)
- Reduced augmentation
- No mosaic or mixup
- Increased emphasis on classification accuracy
- Longer patience for early stopping

## Step 3: Evaluation with Comprehensive Metrics

After training, evaluate the model with comprehensive metrics to verify it meets the 99%+ accuracy target:

```bash
python chess_board_detection/piece_detection/evaluate_high_accuracy.py \
    --model path/to/trained/model.pt \
    --data_yaml chess_board_detection/piece_detection/enhanced_dataset_99plus/dataset.yaml \
    --output_dir chess_board_detection/piece_detection/evaluation_results
```

### Advanced Metrics Included:

1. **Standard Object Detection Metrics**:
   - Precision, Recall, mAP50, mAP50-95

2. **Color-Specific Metrics**:
   - White piece precision/recall/F1
   - Black piece precision/recall/F1
   - Color classification accuracy (how well the model distinguishes white from black)

3. **F-beta Scores** (different precision-recall trade-offs):
   - F1 Score (balanced precision and recall)
   - F2 Score (weighs recall higher than precision)
   - F0.5 Score (weighs precision higher than recall)

4. **IoU-based Metrics**:
   - Mean IoU
   - IoU@50
   - IoU@75

5. **Visual Analysis**:
   - Confusion matrix
   - Precision-recall curves
   - Problem case analysis

### Visualizing Metrics

To generate visual representations of the metrics:

```bash
chess_board_detection/visualize_metrics.bat path/to/evaluation_metrics.json
```

This creates charts showing:
- Color accuracy comparison
- F-beta scores
- Target vs. actual performance
- Per-class metrics

## Batch Files

For convenience, batch files are provided:

1. **train_high_accuracy_model.bat**: Runs the entire pipeline (dataset creation + training)
2. **evaluate_high_accuracy_model.bat**: Evaluates a trained model with comprehensive metrics
3. **visualize_metrics.bat**: Creates visual representations of evaluation metrics
4. **analyze_dataset.bat**: Analyzes class distribution in a dataset to ensure all piece types are represented

### Dataset Analysis Tool

The dataset analysis tool checks if all chess piece types are properly represented in both training and validation sets:

```bash
python chess_board_detection/piece_detection/analyze_dataset.py \
    chess_board_detection/piece_detection/enhanced_dataset_99plus \
    --output_dir chess_board_detection/piece_detection/dataset_analysis
```

This tool:
- Counts occurrences of each class in training and validation sets
- Identifies any classes missing from either set
- Generates detailed distribution reports (JSON and CSV)
- Creates a visualization of the class distribution
- Provides warnings for imbalanced classes

## Technical Details

### Multi-Metric Improvement Tracking

The training process uses a weighted combination of metrics to determine if the model is improving:

```python
# Weights for combined improvement score
metric_weights = {
    'precision': 0.25,
    'recall': 0.25,
    'map50': 0.3,
    'map': 0.1,
    'f1_score': 0.1,
    'val_box_loss': -0.05,  # Negative weight for losses (lower is better)
    'val_cls_loss': -0.05,
    'val_dfl_loss': -0.05
}
```

The improvement score is calculated as a weighted average of relative improvements across all metrics. Training continues as long as this score exceeds the improvement threshold (default: 0.0005).

#### Perfect Score Handling:

The system includes special handling for perfect or near-perfect scores (≥0.999 for metrics like precision, recall, and mAP50, or ≤0.001 for loss metrics):

1. When metrics reach perfect scores, the improvement calculation is adjusted to prevent division by zero or tiny denominators
2. Perfect scores are clearly marked with a 🌟 indicator in the progress output
3. When all metrics reach perfect scores, training automatically stops with a special "PERFECT SCORES ACHIEVED" message
4. The training summary includes a `training_status` field that can be "perfect", "target_met", or "incomplete"

#### Metrics Monitored:

1. **Primary Metrics**:
   - Precision
   - Recall
   - mAP50
   - mAP50-95
   - F1 Score

2. **Loss Metrics**:
   - Box Loss
   - Classification Loss
   - Distribution Focal Loss

3. **Trend Metrics**:
   - Metric history over last 3 epochs
   - Improvement count (consecutive improvements)
   - Stagnation count (consecutive non-improvements)

### Enhanced Dataset Creation

The dataset creation uses Albumentations with carefully tuned parameters:

```python
A.Compose([
    A.Resize(416, 416),
    A.OneOf([
        A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=1.0),
        A.HueSaturationValue(hue_shift_limit=5, sat_shift_limit=20, val_shift_limit=15, p=1.0),
        A.RGBShift(r_shift_limit=10, g_shift_limit=10, b_shift_limit=10, p=1.0),
    ], p=0.8),
    # Additional transformations...
])
```

Key aspects:
- Limited hue shifts to preserve piece colors
- Controlled brightness/contrast changes
- Minimal geometric distortions

### Training Parameters

The two-phase approach uses different parameters for each phase:

#### Phase 1
- Learning rate: 0.01 → 0.0001 (cosine schedule)
- Augmentation: Strong (mosaic=1.0, mixup=0.5)
- Box/Class/DFL loss weights: 7.5/0.5/1.5

#### Phase 2
- Learning rate: 0.001 → 0.000001 (cosine schedule)
- Augmentation: Mild (no mosaic/mixup)
- Box/Class/DFL loss weights: 7.5/1.0/1.5 (increased class weight)

## Expected Results

With this approach, the model should achieve:

### Primary Targets:
- Precision: ≥99%
- Recall: ≥99%
- mAP50: ≥99%
- Color Classification Accuracy: ≥99%

### Secondary Targets:
- F1 Score: ≥0.99
- Mean IoU: ≥0.85
- White Piece Precision: ≥99%
- Black Piece Precision: ≥99%

### Per-Class Targets:
- All piece types should achieve ≥95% precision and recall
- Critical pieces (kings, queens) should achieve ≥98% precision and recall

## Troubleshooting

### Out of Memory Errors
- Reduce batch size (e.g., from 16 to 8)
- Reduce image size (e.g., from 416 to 384)

### Training Not Converging
- Increase Phase 1 epochs
- Adjust learning rate
- Check dataset for labeling issues

### Color Misclassification Persists
- Increase color-focused augmentations
- Add more examples of problematic pieces
- Adjust class weights to emphasize classification

## References

- [Ultralytics YOLOv8 Documentation](https://docs.ultralytics.com/)
- [Albumentations Documentation](https://albumentations.ai/docs/)
- [PyTorch Documentation](https://pytorch.org/docs/)
