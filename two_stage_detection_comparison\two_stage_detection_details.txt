Chess Piece Detection - Two-Stage Approach
==================================================

Original Best Model (Epoch 86) Detections:
--------------------------------------------------
Detection #1: black_pawn (Confidence: 0.9552)
   Bounding Box: [341, 290, 361, 319]
Detection #2: black_pawn (Confidence: 0.9462)
   Bounding Box: [244, 213, 264, 241]
Detection #3: black_pawn (Confidence: 0.9434)
   Bounding Box: [188, 295, 206, 321]
Detection #4: black_rook (Confidence: 0.9402)
   Bounding Box: [277, 247, 297, 279]
Detection #5: black_queen (Confidence: 0.9397)
   Bounding Box: [245, 327, 271, 359]
Detection #6: black_pawn (Confidence: 0.9348)
   Bounding Box: [159, 295, 177, 323]
Detection #7: white_bishop (Confidence: 0.9326)
   Bounding Box: [243, 127, 261, 160]
Detection #8: black_pawn (Confidence: 0.9319)
   Bounding Box: [131, 296, 148, 322]
Detection #9: black_bishop (Confidence: 0.9280)
   Bounding Box: [342, 326, 362, 358]
Detection #10: white_pawn (Confidence: 0.9257)
   Bounding Box: [152, 100, 169, 128]
Detection #11: black_bishop (Confidence: 0.9190)
   Bounding Box: [247, 247, 265, 281]
Detection #12: white_rook (Confidence: 0.9185)
   Bounding Box: [239, 45, 259, 79]
Detection #13: black_knight (Confidence: 0.9165)
   Bounding Box: [185, 253, 205, 282]
Detection #14: white_pawn (Confidence: 0.9161)
   Bounding Box: [304, 84, 324, 115]
Detection #15: white_pawn (Confidence: 0.9153)
   Bounding Box: [122, 102, 140, 131]
Detection #16: white_pawn (Confidence: 0.9124)
   Bounding Box: [337, 82, 356, 112]
Detection #17: white_pawn (Confidence: 0.9117)
   Bounding Box: [180, 97, 199, 126]
Detection #18: white_knight (Confidence: 0.9027)
   Bounding Box: [338, 164, 359, 196]
Detection #19: white_queen (Confidence: 0.8973)
   Bounding Box: [302, 121, 328, 155]
Detection #20: white_rook (Confidence: 0.8972)
   Bounding Box: [209, 48, 228, 82]
Detection #21: black_queen (Confidence: 0.8955)
   Bounding Box: [158, 328, 180, 361]
Detection #22: black_rook (Confidence: 0.8898)
   Bounding Box: [190, 328, 208, 359]
Detection #23: white_bishop (Confidence: 0.8679)
   Bounding Box: [244, 167, 262, 201]
Detection #24: white_king (Confidence: 0.8506)
   Bounding Box: [148, 52, 168, 89]

Total detections: 24
Average confidence: 0.9162

==================================================

Epoch 107 (Best mAP) Detections:
--------------------------------------------------
Detection #1: white_pawn (Confidence: 0.9515)
   Bounding Box: [303, 84, 324, 115]
Detection #2: white_pawn (Confidence: 0.9437)
   Bounding Box: [152, 100, 169, 128]
Detection #3: black_pawn (Confidence: 0.9423)
   Bounding Box: [341, 290, 361, 319]
Detection #4: white_pawn (Confidence: 0.9399)
   Bounding Box: [122, 102, 140, 131]
Detection #5: black_pawn (Confidence: 0.9383)
   Bounding Box: [159, 296, 177, 322]
Detection #6: black_pawn (Confidence: 0.9373)
   Bounding Box: [130, 296, 148, 322]
Detection #7: black_rook (Confidence: 0.9368)
   Bounding Box: [277, 248, 297, 279]
Detection #8: black_bishop (Confidence: 0.9356)
   Bounding Box: [246, 247, 265, 281]
Detection #9: black_pawn (Confidence: 0.9349)
   Bounding Box: [244, 213, 264, 241]
Detection #10: white_pawn (Confidence: 0.9348)
   Bounding Box: [180, 97, 199, 126]
Detection #11: white_pawn (Confidence: 0.9322)
   Bounding Box: [336, 82, 356, 112]
Detection #12: black_pawn (Confidence: 0.9310)
   Bounding Box: [188, 294, 207, 322]
Detection #13: white_rook (Confidence: 0.9290)
   Bounding Box: [208, 49, 227, 82]
Detection #14: black_bishop (Confidence: 0.9284)
   Bounding Box: [342, 326, 362, 358]
Detection #15: white_rook (Confidence: 0.9249)
   Bounding Box: [239, 45, 260, 79]
Detection #16: white_bishop (Confidence: 0.9248)
   Bounding Box: [242, 127, 262, 160]
Detection #17: white_knight (Confidence: 0.9227)
   Bounding Box: [337, 164, 360, 196]
Detection #18: black_queen (Confidence: 0.9219)
   Bounding Box: [245, 327, 271, 359]
Detection #19: white_queen (Confidence: 0.9122)
   Bounding Box: [302, 121, 327, 155]
Detection #20: white_bishop (Confidence: 0.9061)
   Bounding Box: [244, 167, 263, 201]
Detection #21: black_rook (Confidence: 0.8976)
   Bounding Box: [190, 328, 208, 359]
Detection #22: black_queen (Confidence: 0.8674)
   Bounding Box: [158, 328, 180, 361]
Detection #23: white_king (Confidence: 0.8127)
   Bounding Box: [147, 53, 169, 90]
Detection #24: white_knight (Confidence: 0.4693)
   Bounding Box: [185, 253, 205, 282]

Total detections: 24
Average confidence: 0.9031

==================================================

Epoch 111 (Best Loss) Detections:
--------------------------------------------------
Detection #1: white_pawn (Confidence: 0.9509)
   Bounding Box: [304, 84, 324, 115]
Detection #2: white_bishop (Confidence: 0.9508)
   Bounding Box: [242, 127, 262, 159]
Detection #3: black_pawn (Confidence: 0.9462)
   Bounding Box: [341, 290, 361, 319]
Detection #4: black_pawn (Confidence: 0.9431)
   Bounding Box: [159, 296, 177, 322]
Detection #5: black_bishop (Confidence: 0.9411)
   Bounding Box: [246, 247, 265, 281]
Detection #6: black_bishop (Confidence: 0.9383)
   Bounding Box: [342, 327, 362, 357]
Detection #7: white_pawn (Confidence: 0.9374)
   Bounding Box: [122, 102, 140, 130]
Detection #8: black_pawn (Confidence: 0.9374)
   Bounding Box: [244, 213, 264, 241]
Detection #9: black_pawn (Confidence: 0.9371)
   Bounding Box: [188, 294, 207, 321]
Detection #10: black_pawn (Confidence: 0.9340)
   Bounding Box: [130, 296, 148, 322]
Detection #11: black_rook (Confidence: 0.9331)
   Bounding Box: [277, 248, 297, 279]
Detection #12: black_queen (Confidence: 0.9328)
   Bounding Box: [245, 327, 271, 359]
Detection #13: white_knight (Confidence: 0.9283)
   Bounding Box: [337, 164, 359, 195]
Detection #14: white_pawn (Confidence: 0.9273)
   Bounding Box: [151, 100, 169, 127]
Detection #15: white_pawn (Confidence: 0.9183)
   Bounding Box: [180, 97, 199, 125]
Detection #16: white_queen (Confidence: 0.9132)
   Bounding Box: [302, 121, 328, 155]
Detection #17: black_rook (Confidence: 0.8953)
   Bounding Box: [189, 328, 208, 359]
Detection #18: white_pawn (Confidence: 0.8897)
   Bounding Box: [336, 82, 356, 111]
Detection #19: white_rook (Confidence: 0.8755)
   Bounding Box: [240, 45, 260, 78]
Detection #20: black_king (Confidence: 0.8712)
   Bounding Box: [158, 329, 180, 360]
Detection #21: white_bishop (Confidence: 0.8703)
   Bounding Box: [244, 167, 263, 201]
Detection #22: white_rook (Confidence: 0.8488)
   Bounding Box: [209, 49, 227, 81]
Detection #23: white_knight (Confidence: 0.7768)
   Bounding Box: [185, 253, 206, 282]
Detection #24: white_king (Confidence: 0.5386)
   Bounding Box: [148, 52, 169, 89]

Total detections: 24
Average confidence: 0.8973

==================================================

