"""
Two-stage chess board detection script.
Stage 1: Use segmentation to identify the chess board region
Stage 2: Use corner detection within the segmented region
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import json
from scipy import ndimage
from skimage.measure import regionprops

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """
    Enhance image colors and contrast similar to training augmentations.
    """
    # 1. Adaptive contrast enhancement (similar to CLAHE in training)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    l, a, b = cv2.split(lab)
    l = clahe.apply(l)
    lab = cv2.merge([l, a, b])
    image_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)

    # 2. Gamma correction (similar to RandomGamma in training)
    # Use a gamma value that enhances details without over-brightening
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)]).astype("uint8")
    image_enhanced = cv2.LUT(image_enhanced, table)

    # 3. Saturation enhancement (similar to HueSaturationValue in training)
    hsv = cv2.cvtColor(image_enhanced, cv2.COLOR_RGB2HSV)
    h, s, v = cv2.split(hsv)
    s = np.clip(s * 1.2, 0, 255).astype(np.uint8)  # Increase saturation by 20%
    hsv = cv2.merge([h, s, v])
    image_enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)

    return image_enhanced

def normalize_for_model(image):
    """
    Normalize image for model input using same normalization as during training.
    """
    # Convert to PIL Image first
    image_pil = Image.fromarray(image)

    # Use torchvision transforms which handle the data type correctly
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Apply transform and add batch dimension
    image_tensor = transform(image_pil).unsqueeze(0)

    return image_tensor

def process_segmentation_mask(segmentation_mask, threshold=0.5):
    """
    Process the segmentation mask to get the chess board region.
    Returns the bounding box of the largest connected component.
    """
    # Threshold the segmentation mask
    binary_mask = (segmentation_mask > threshold).astype(np.uint8)

    # Find connected components
    labeled_mask, num_features = ndimage.label(binary_mask)

    if num_features == 0:
        return None

    # Get properties of the labeled regions
    regions = regionprops(labeled_mask)

    # Find the largest region (by area)
    if not regions:
        return None

    largest_region = max(regions, key=lambda r: r.area)

    # Get the bounding box (min_row, min_col, max_row, max_col)
    min_row, min_col, max_row, max_col = largest_region.bbox

    # Add some padding (10% of the region size)
    height = max_row - min_row
    width = max_col - min_col

    padding_y = int(height * 0.1)
    padding_x = int(width * 0.1)

    # Ensure the padded box is within the image bounds
    min_row = max(0, min_row - padding_y)
    min_col = max(0, min_col - padding_x)
    max_row = min(segmentation_mask.shape[0], max_row + padding_y)
    max_col = min(segmentation_mask.shape[1], max_col + padding_x)

    return (min_row, min_col, max_row, max_col)

def detect_corners_in_region(heatmaps, region_bbox):
    """
    Detect corners within the specified region of the heatmaps.
    Only considers peaks within the bounding box.
    """
    min_row, min_col, max_row, max_col = region_bbox
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []

    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()

        # Create a masked heatmap that only considers the region
        masked_heatmap = np.zeros_like(heatmap)
        masked_heatmap[min_row:max_row, min_col:max_col] = heatmap[min_row:max_row, min_col:max_col]

        # Find the location of the maximum value
        idx = np.argmax(masked_heatmap)
        y, x = np.unravel_index(idx, masked_heatmap.shape)
        confidence = masked_heatmap[y, x]

        keypoints.append((x, y, confidence))

    return keypoints

def map_to_original_coordinates(keypoints, preprocess_info):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    Accounts for all preprocessing steps: resize, crop, and final resize.
    """
    mapped_keypoints = []

    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']
    resized_w, resized_h = preprocess_info['resized_size']

    for x, y, conf in keypoints:
        # Step 1: Map from target size to crop size
        crop_scale_x = crop_size / target_w
        crop_scale_y = crop_size / target_h

        x_in_crop = x * crop_scale_x
        y_in_crop = y * crop_scale_y

        # Step 2: Add crop offset to get coordinates in resized image
        x_in_resized = x_in_crop + crop_start_x
        y_in_resized = y_in_crop + crop_start_y

        # Step 3: Scale to original image size
        orig_scale_x = original_w / resized_w
        orig_scale_y = original_h / resized_h

        x_in_original = x_in_resized * orig_scale_x
        y_in_original = y_in_resized * orig_scale_y

        # Ensure coordinates are within image bounds
        x_in_original = max(0, min(x_in_original, original_w - 1))
        y_in_original = max(0, min(y_in_original, original_h - 1))

        mapped_keypoints.append((x_in_original, y_in_original, conf))

    return mapped_keypoints

def visualize_two_stage_detection(image, segmentation, region_bbox, keypoints, output_path):
    """
    Create a visualization showing the two-stage detection process.
    """
    # Create figure with 2x2 grid
    fig, axs = plt.subplots(2, 2, figsize=(15, 15))
    fig.suptitle('Two-Stage Chess Board Detection', fontsize=16)

    # Plot original image
    axs[0, 0].imshow(image)
    axs[0, 0].set_title('Original Image', fontsize=14)
    axs[0, 0].axis('off')

    # Plot segmentation mask
    axs[0, 1].imshow(segmentation[0, 0], cmap='gray')
    axs[0, 1].set_title('Segmentation Mask', fontsize=14)
    axs[0, 1].axis('off')

    # Plot region of interest
    min_row, min_col, max_row, max_col = region_bbox
    roi_img = image.copy()
    cv2.rectangle(roi_img, (min_col, min_row), (max_col, max_row), (0, 255, 0), 2)
    axs[1, 0].imshow(roi_img)
    axs[1, 0].set_title('Region of Interest', fontsize=14)
    axs[1, 0].axis('off')

    # Plot final detection
    final_img = image.copy()
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    # Use matplotlib-compatible colors (0-1 range)
    colors_cv = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # For OpenCV
    colors_plt = ['red', 'green', 'blue', 'yellow']  # For matplotlib

    # Draw keypoints
    for i, (x, y, conf) in enumerate(keypoints):
        cv2.circle(final_img, (int(x), int(y)), 5, colors_cv[i], -1)
        axs[1, 1].text(x+5, y+5, f"{corner_names[i]}\n({int(x)}, {int(y)})\nConf: {conf:.3f}",
                      color=colors_plt[i], fontsize=10,
                      bbox=dict(facecolor='white', alpha=0.7))

    # Draw lines connecting keypoints
    points = [(int(x), int(y)) for x, y, _ in keypoints]
    if len(points) == 4:
        for i in range(4):
            cv2.line(final_img, points[i], points[(i + 1) % 4], (0, 255, 0), 2)

    axs[1, 1].imshow(final_img)
    axs[1, 1].set_title('Final Corner Detection', fontsize=14)
    axs[1, 1].axis('off')

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    return output_path

def two_stage_detection(image_path, model, model_name):
    """
    Apply two-stage detection:
    1. Use segmentation to identify the chess board region
    2. Use corner detection within the segmented region
    """
    # Preprocess image
    preprocessed_image, preprocess_info = preprocess_image(image_path)
    enhanced_image = enhance_image(preprocessed_image)

    # Normalize for model
    input_tensor = normalize_for_model(enhanced_image)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()
    heatmaps = torch.sigmoid(outputs['corner_heatmaps'])

    # Process segmentation to get chess board region
    region_bbox = process_segmentation_mask(segmentation[0, 0])

    if region_bbox is None:
        print(f"No chess board region detected in the image for {model_name}")
        return None

    # Detect corners within the region
    keypoints = detect_corners_in_region(heatmaps, region_bbox)

    # Map keypoints back to original image
    original_keypoints = map_to_original_coordinates(keypoints, preprocess_info)

    return {
        'model_name': model_name,
        'preprocessed_image': enhanced_image,
        'segmentation': segmentation,
        'region_bbox': region_bbox,
        'keypoints': keypoints,
        'original_keypoints': original_keypoints,
        'original_image': preprocess_info['original_image']
    }

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\two_stage"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    os.makedirs(output_dir, exist_ok=True)

    # Process each model
    results = {}

    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} with two-stage detection...")

        # Load model
        model = load_model(model_path)

        # Apply two-stage detection
        detection_results = two_stage_detection(image_path, model, model_name)

        if detection_results is None:
            continue

        # Store results
        results[model_name] = detection_results

        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_two_stage.png")
        visualize_two_stage_detection(
            detection_results['preprocessed_image'],
            detection_results['segmentation'],
            detection_results['region_bbox'],
            detection_results['keypoints'],
            output_path
        )

        print(f"Two-stage detection visualization saved to: {output_path}")

    # Print keypoints
    print("\nDetected Keypoints (Original Image Coordinates):")
    print("===============================================")

    for model_name, detection_results in results.items():
        print(f"\n{model_name}:")
        corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        for i, (x, y, conf) in enumerate(detection_results['original_keypoints']):
            print(f"  {corner_names[i]}: ({int(x)}, {int(y)}) - Conf: {conf:.4f}")

    # Save keypoints to JSON
    keypoints_data = {}
    for model_name, detection_results in results.items():
        keypoints_data[model_name] = {}
        corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        for i, (x, y, conf) in enumerate(detection_results['original_keypoints']):
            keypoints_data[model_name][corner_names[i]] = {
                'x': int(float(x)),
                'y': int(float(y)),
                'confidence': float(conf)
            }

    json_path = os.path.join(output_dir, "two_stage_keypoints.json")
    with open(json_path, 'w') as f:
        json.dump(keypoints_data, f, indent=4)

    print(f"Keypoints saved to: {json_path}")

if __name__ == "__main__":
    main()
