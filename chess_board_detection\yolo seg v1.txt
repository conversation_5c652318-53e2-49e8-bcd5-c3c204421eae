PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_yolo_segmentation.py
Starting YOLO v11n segmentation training...
Using device: cuda
CUDA available: True
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.4 GB
Loading YOLO model: yolo11n-seg.pt
Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n-seg.pt to 'yolo11n-seg.pt'...
100%|██████████████████████████████████████████████████████████████| 5.90M/5.90M [00:34<00:00, 179kB/s]
Adjusted batch size to 8 due to limited GPU memory
Starting training...
Training configuration:
  data: chess_board_detection/segmentation_dataset/dataset.yaml
  epochs: 100
  imgsz: 640
  batch: 8
  device: cuda
  project: runs/segment
  name: chessboard_v1
  save_period: 10
  patience: 20
  save: True
  cache: False
  workers: 4
  optimizer: AdamW
  lr0: 0.01
  lrf: 0.1
  momentum: 0.937
  weight_decay: 0.0005
  warmup_epochs: 3
  warmup_momentum: 0.8
  box: 7.5
  cls: 0.5
  dfl: 1.5
  pose: 12.0
  kobj: 2.0
  label_smoothing: 0.0
  nbs: 64
  hsv_h: 0.015
  hsv_s: 0.7
  hsv_v: 0.4
  degrees: 0.0
  translate: 0.1
  scale: 0.5
  shear: 0.0
  perspective: 0.0
  flipud: 0.0
  fliplr: 0.5
  mosaic: 1.0
  mixup: 0.0
  copy_paste: 0.0
  auto_augment: randaugment
  erasing: 0.4
  crop_fraction: 1.0
New https://pypi.org/project/ultralytics/8.3.143 available  Update with 'pip install -U ultralytics'
WARNING 'crop_fraction' is deprecated and will be removed in in the future.
WARNING 'label_smoothing' is deprecated and will be removed in in the future.
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=8, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=chess_board_detection/segmentation_dataset/dataset.yaml, degrees=0.0, deterministic=True, device=0, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=2.0, line_width=None, lr0=0.01, lrf=0.1, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=yolo11n-seg.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=chessboard_v1, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=20, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=runs/segment, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs\segment\chessboard_v1, save_frames=False, save_json=False, save_period=10, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=segment, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3, warmup_momentum=0.8, weight_decay=0.0005, workers=4, workspace=None
Overriding model.yaml nc=80 with nc=1

                   from  n    params  module                                       arguments            

  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]        

  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]       

  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]       

  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]     

  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]  

  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]     

  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]  

  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]        

 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]        

 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest'] 

 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                  

 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False] 

 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest'] 

 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                  

 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]  

 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]       

 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]                  

 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False] 

 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]     

 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]                  

 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]  

 23        [16, 19, 22]  1    683635  ultralytics.nn.modules.head.Segment          [1, 32, 64, [64, 128, 256]]
YOLO11n-seg summary: 203 layers, 2,842,803 parameters, 2,842,787 gradients, 10.4 GFLOPs

Transferred 510/561 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.10.0 ms, read: 159.9156.7 MB/s, size: 3671.5 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\segmentation_datase
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\segmentation_dataset\labels\train.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 352.2200.5 MB/s, size: 4793.9 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\segmentation_dataset\
val: New cache created: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\segmentation_dataset\labels\val.cache
Plotting labels to runs\segment\chessboard_v1\labels.jpg... 
optimizer: AdamW(lr=0.01, momentum=0.937) with parameter groups 90 weight(decay=0.0), 101 weight(decay=0.0005), 100 bias(decay=0.0)
Image sizes 640 train, 640 val
Using 4 dataloader workers
Logging results to runs\segment\chessboard_v1
Starting training for 100 epochs...

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      1/100       1.4G      1.704      6.111      3.104       1.83          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00333          1     0.0916     0.0155    0.00333          1     0.0365    0.00823

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      2/100       1.4G      1.704       6.52      3.309      1.852          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00333          1      0.169     0.0598    0.00267        0.8     0.0928     0.0389

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      3/100      1.42G      0.724     0.6001      2.517      1.208          3        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00333          1      0.407      0.239    0.00333          1      0.407      0.215

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      4/100      1.43G     0.6519     0.4612      1.987      1.047          6        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00333          1      0.695       0.51    0.00333          1      0.695      0.524

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      5/100      1.43G     0.7662      1.911      1.648      1.053          3        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P     
                   all          5          5    0.00333          1      0.299      0.126    0.00333          1      0.299      0.129

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      6/100      1.43G     0.7113     0.9003      1.433      1.092          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00859          1      0.263      0.161    0.00859          1      0.263      0.171

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      7/100      1.43G     0.7602     0.5345      1.309      1.044          6        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00717          1     0.0413    0.00507    0.00717          1     0.0429    0.00548

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      8/100      1.43G      0.828     0.6524      1.379      1.202          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00251        0.4    0.00412   0.000412    0.00251        0.4     0.0117    0.00158

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
      9/100      1.43G     0.8376     0.5882      1.315      1.151          7        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5     0.0216        0.4     0.0186    0.00582     0.0071        0.8     0.0127    0.00519

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     10/100      1.43G     0.9858     0.5682      1.407      1.196          8        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00189        0.4    0.00437    0.00171          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     11/100      1.43G     0.8728     0.4858      1.364      1.213          7        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5      0.002        0.6    0.00305   0.000972          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     12/100      1.43G     0.9487     0.4149      1.323      1.242          8        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5          0          0          0          0          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     13/100      1.43G      1.142      1.046      1.694      1.563          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5      0.039        0.6     0.0263     0.0042          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     14/100      1.43G     0.8773     0.4254      1.134      1.165          8        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00918        0.2     0.0133    0.00281          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     15/100      1.43G      1.005      0.257      1.206      1.317          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00918        0.2     0.0133    0.00281          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     16/100      1.43G      1.054     0.4655      1.207      1.336          7        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5       0.45        0.2      0.155     0.0521    0.00153        0.2     0.0198    0.00316

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     17/100      1.43G     0.9447     0.4448      1.228      1.311          3        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P     
                   all          5          5     0.0232        0.6     0.0645     0.0149     0.0232        0.6     0.0645     0.0207

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     18/100      1.43G     0.9782     0.4633      1.184      1.337          6        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5     0.0232        0.6     0.0645     0.0149     0.0232        0.6     0.0645     0.0207

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     19/100      1.43G     0.9674     0.4866      1.062       1.13          8        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00434        0.8    0.00822    0.00151    0.00434        0.8    0.00822    0.00151

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     20/100      1.43G      1.131     0.2944      1.183      1.437          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P     
                   all          5          5    0.00466        0.8    0.00963    0.00188    0.00466        0.8    0.00963    0.00162

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     21/100      1.43G      1.118      0.871      1.311       1.41          6        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00466        0.8    0.00963    0.00188    0.00466        0.8    0.00963    0.00162

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     22/100      1.43G     0.9535     0.4916      1.277      1.289          4        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5          0          0          0          0          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     23/100      1.43G      0.938     0.5081     0.9997      1.292          5        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5          0          0          0          0          0          0          0          0

      Epoch    GPU_mem   box_loss   seg_loss   cls_loss   dfl_loss  Instances       Size
     24/100      1.43G     0.8743     0.5306      1.111      1.162          4        640: 100%|████████
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00214        0.6    0.00265    0.00045     0.0079        0.2    0.00546   0.000796
EarlyStopping: Training stopped early as no improvement observed in last 20 epochs. Best results observed at epoch 4, best model saved as best.pt.
To update EarlyStopping(patience=20) pass a new patience value, i.e. `patience=300` or use `patience=0` to disable EarlyStopping.

24 epochs completed in 0.008 hours.
Optimizer stripped from runs\segment\chessboard_v1\weights\last.pt, 6.0MB
Optimizer stripped from runs\segment\chessboard_v1\weights\best.pt, 6.0MB

Validating runs\segment\chessboard_v1\weights\best.pt...
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
YOLO11n-seg summary (fused): 113 layers, 2,834,763 parameters, 0 gradients, 10.2 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P     
                   all          5          5    0.00333          1      0.695      0.509    0.00333          1      0.695      0.522
Speed: 0.5ms preprocess, 3.6ms inference, 0.0ms loss, 2.0ms postprocess per image
Results saved to runs\segment\chessboard_v1
Training completed!
Best model saved at: runs\segment\chessboard_v1\weights\best.pt

Validating trained model...
Validating model: runs\segment\chessboard_v1\weights\best.pt
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
YOLO11n-seg summary (fused): 113 layers, 2,834,763 parameters, 0 gradients, 10.2 GFLOPs
val: Fast image access  (ping: 0.10.0 ms, read: 1412.8607.4 MB/s, size: 4793.9 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\segmentation_dataset\
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
WARNING Limiting validation plots to first 50 items per image for speed...
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95)     Mask(P    
                   all          5          5    0.00333          1      0.695      0.511    0.00333          1      0.695      0.525
Speed: 6.8ms preprocess, 64.4ms inference, 0.0ms loss, 4.2ms postprocess per image
Results saved to runs\segment\val
Validation results:
  mAP50: 0.6950
  mAP50-95: 0.5112

Testing inference...
Testing inference on: chess_board_detection/data/real/9.jpg

image 1/1 C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real\9.jpg: 512x640 (no detections), 195.5ms
Speed: 4.2ms preprocess, 195.5ms inference, 1.4ms postprocess per image at shape (1, 3, 512, 640)
Result saved to: chess_board_detection/segmentation_test_outputs\result_0.jpg

Training pipeline completed!
Best model: runs\segment\chessboard_v1\weights\best.pt
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 
