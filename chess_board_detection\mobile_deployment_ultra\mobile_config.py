"""
Mobile Configuration for Chess FEN Generation
Optimized paths and settings for mobile deployment
"""

MOBILE_CONFIG = {
    "v6_model": "models/v6_mobile.pth",
    "piece_model": "models/yolo_mobile.pt", 
    "input_size": 512,
    "piece_input_size": 416,
    "confidence_threshold": 0.5,
    "iou_threshold": 0.7,
    "use_fp16": True,
    "fen_symbols": {
        "white_pawn": "P", "white_knight": "N", "white_bishop": "B", 
        "white_rook": "R", "white_queen": "Q", "white_king": "K",
        "black_pawn": "p", "black_knight": "n", "black_bishop": "b", 
        "black_rook": "r", "black_queen": "q", "black_king": "k"
    }
}
