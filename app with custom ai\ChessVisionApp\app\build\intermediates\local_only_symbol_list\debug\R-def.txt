R_DEF: Internal format may change without notice
local
color md_theme_light_background
color md_theme_light_error
color md_theme_light_errorContainer
color md_theme_light_inverseOnSurface
color md_theme_light_inversePrimary
color md_theme_light_inverseSurface
color md_theme_light_onBackground
color md_theme_light_onError
color md_theme_light_onErrorContainer
color md_theme_light_onPrimary
color md_theme_light_onPrimaryContainer
color md_theme_light_onSecondary
color md_theme_light_onSecondaryContainer
color md_theme_light_onSurface
color md_theme_light_onSurfaceVariant
color md_theme_light_onTertiary
color md_theme_light_onTertiaryContainer
color md_theme_light_outline
color md_theme_light_outlineVariant
color md_theme_light_primary
color md_theme_light_primaryContainer
color md_theme_light_scrim
color md_theme_light_secondary
color md_theme_light_secondaryContainer
color md_theme_light_surface
color md_theme_light_surfaceVariant
color md_theme_light_tertiary
color md_theme_light_tertiaryContainer
drawable bb
drawable bk
drawable bn
drawable bp
drawable bq
drawable br
drawable chessboard_background
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable wb
drawable wk
drawable wn
drawable wp
drawable wq
drawable wr
mipmap ic_launcher
mipmap ic_launcher_round
string about
string analysis_text
string analysis_wait_message
string analyze_position
string analyzing_board
string app_name
string auto_analysis
string back
string best_move
string best_moves
string board_theme
string camera_permission_description
string camera_permission_rationale
string camera_permission_required
string cancel
string capture
string capture_board_subtitle
string capture_board_title
string chess_board_title
string color_black
string color_white
string deeper_analysis
string delete
string engine_strength
string error
string error_camera_permission
string error_fen_detection
string error_invalid_fen
string error_network
string error_unknown
string evaluation
string grant_camera_permission
string home_title
string loading
string new_game
string no_recent_games
string ok
string piece_bishop
string piece_king
string piece_knight
string piece_pawn
string piece_queen
string piece_rook
string piece_style
string play_vs_ai
string position_analysis
string position_camera_instruction
string recent_games
string retry
string save
string save_game
string scan_board
string settings_title
string share
string share_analysis
string sound_effects
string start_scanning
style Theme.ChessVisionApp
xml backup_rules
xml data_extraction_rules
xml file_paths
