"""
Run the entire chess board detection pipeline:
1. Generate synthetic data
2. Train the model
3. Test on a sample image
"""

import os
import argparse
import subprocess
import time

from config import SYNTHETIC_DATA_DIR, MODELS_DIR


def run_command(command, description):
    """
    Run a command and print its output.
    
    Args:
        command (str): Command to run.
        description (str): Description of the command.
    """
    print(f"\n{'=' * 80}")
    print(f"Running: {description}")
    print(f"{'=' * 80}\n")
    
    start_time = time.time()
    process = subprocess.Popen(
        command,
        shell=True,
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True
    )
    
    # Print output in real-time
    for line in process.stdout:
        print(line, end='')
    
    process.wait()
    end_time = time.time()
    
    print(f"\nCompleted in {end_time - start_time:.2f} seconds")
    
    if process.returncode != 0:
        print(f"Error: Command failed with return code {process.returncode}")
        return False
    
    return True


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Chess Board Detection Pipeline')
    parser.add_argument('--skip-data-generation', action='store_true',
                        help='Skip synthetic data generation')
    parser.add_argument('--skip-training', action='store_true',
                        help='Skip model training')
    parser.add_argument('--test-image', type=str, default=None,
                        help='Path to test image (if not provided, will use a synthetic test image)')
    args = parser.parse_args()
    
    # Step 1: Generate synthetic data
    if not args.skip_data_generation:
        success = run_command(
            'python generate_synthetic_data.py',
            'Generating synthetic data'
        )
        if not success:
            print("Data generation failed. Exiting.")
            return
    else:
        print("Skipping synthetic data generation.")
    
    # Step 2: Train the model
    if not args.skip_training:
        success = run_command(
            'python train.py',
            'Training the model'
        )
        if not success:
            print("Training failed. Exiting.")
            return
    else:
        print("Skipping model training.")
    
    # Step 3: Test on a sample image
    if args.test_image:
        test_image = args.test_image
    else:
        # Use a synthetic test image
        test_index_file = os.path.join(SYNTHETIC_DATA_DIR, 'test_index.json')
        if os.path.exists(test_index_file):
            import json
            with open(test_index_file, 'r') as f:
                test_data = json.load(f)
            if test_data:
                test_image = test_data[0]['image']
            else:
                print("No test images found. Exiting.")
                return
        else:
            print("Test index file not found. Exiting.")
            return
    
    output_path = os.path.join(MODELS_DIR, 'test_result.png')
    success = run_command(
        f'python inference.py --image "{test_image}" --output "{output_path}"',
        'Testing the model'
    )
    if not success:
        print("Testing failed.")
        return
    
    print(f"\nPipeline completed successfully!")
    print(f"Test result saved to: {output_path}")


if __name__ == "__main__":
    main()
