"""
Enhanced loss functions for chess board detection with improved corner localization.
This module addresses the issue of the model focusing on edges rather than corners.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class EnhancedDiceLoss(nn.Module):
    """
    Enhanced Dice Loss for segmentation with boundary weighting.
    """
    def __init__(self, smooth=1.0):
        super(EnhancedDiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = torch.sigmoid(pred)

        # Ensure target has the same dimensions as pred
        if len(target.shape) != len(pred.shape):
            if len(target.shape) == 3 and len(pred.shape) == 4:
                # If target is [B, H, W] and pred is [B, C, H, W]
                target = target.unsqueeze(1)
            elif len(target.shape) == 4 and target.shape[1] != pred.shape[1]:
                # If channel dimensions don't match
                if target.shape[1] == 1 and pred.shape[1] > 1:
                    # Expand target to match pred's channels
                    target = target.expand(-1, pred.shape[1], -1, -1)

        # Calculate standard Dice loss
        # Use the last two dimensions (height and width)
        dims = tuple(range(2, len(pred.shape)))
        intersection = (pred * target).sum(dim=dims)
        union = pred.sum(dim=dims) + target.sum(dim=dims)
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)

        return 1.0 - dice.mean()


class CornerFocusedHeatmapLoss(nn.Module):
    """
    Enhanced heatmap loss that specifically focuses on corners rather than edges.

    This loss function:
    1. Uses MSE for basic heatmap prediction
    2. Adds a separation penalty to ensure corners are distinct
    3. Adds an edge suppression term to penalize activations along edges that aren't near corners
    4. Includes a peak enhancement term to make corner peaks sharper
    """
    def __init__(self,
                 separation_weight=0.4,
                 peak_separation_weight=0.3,
                 edge_suppression_weight=0.5,
                 peak_enhancement_weight=0.3):
        super(CornerFocusedHeatmapLoss, self).__init__()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight
        self.edge_suppression_weight = edge_suppression_weight
        self.peak_enhancement_weight = peak_enhancement_weight

        # Small epsilon to prevent numerical instability
        self.eps = 1e-6

    def forward(self, pred_heatmaps, target_heatmaps):
        """
        Args:
            pred_heatmaps: Predicted heatmaps (B, 4, H, W)
            target_heatmaps: Target heatmaps (B, 4, H, W)
        """
        batch_size = pred_heatmaps.size(0)

        # Apply sigmoid to predicted heatmaps to ensure values are in [0, 1]
        pred_heatmaps = torch.sigmoid(pred_heatmaps)

        # Clip target heatmaps to [0, 1] for stability
        target_heatmaps = torch.clamp(target_heatmaps, 0.0, 1.0)

        # Basic MSE loss for heatmap prediction
        mse_loss = F.mse_loss(pred_heatmaps, target_heatmaps)

        # Separation loss: penalize overlap between different corner heatmaps
        separation_loss = 0.0
        for i in range(4):
            for j in range(i+1, 4):
                # Use positive overlap only
                overlap = torch.clamp(torch.min(pred_heatmaps[:, i], pred_heatmaps[:, j]), min=0.0)
                separation_loss += overlap.sum() / (batch_size + self.eps)

        # Peak separation loss: ensure peaks are well separated
        peak_separation_loss = 0.0
        for i in range(batch_size):
            for c in range(4):
                # Get the location of the maximum value (peak) for this heatmap
                heatmap = pred_heatmaps[i, c]
                max_val, _ = torch.max(heatmap.view(-1), dim=0)

                # Skip if max_val is too small (no clear peak)
                if max_val < 0.1:
                    continue

                # Create a mask of values that are close to the maximum
                # Use a smaller threshold to create sharper peaks
                peak_mask = (heatmap > 0.5 * max_val).float()

                # Penalize if the peak area is too large
                peak_size = peak_mask.sum()
                ideal_peak_size = torch.tensor(9.0, device=pred_heatmaps.device)  # 3x3 peak
                peak_separation_loss += torch.abs(peak_size - ideal_peak_size) / (ideal_peak_size + self.eps)

        peak_separation_loss = peak_separation_loss / (batch_size * 4 + self.eps)

        # Edge suppression loss: penalize activations along edges that aren't near corners
        edge_suppression_loss = 0.0
        for i in range(batch_size):
            # Create edge masks (top, right, bottom, left)
            h, w = pred_heatmaps.shape[2], pred_heatmaps.shape[3]

            # Define corner regions (where activations are allowed)
            corner_size = max(3, min(h, w) // 8)  # Size of corner region, at least 3 pixels

            # Create masks for the four corner regions
            corner_masks = torch.zeros((4, h, w), device=pred_heatmaps.device)

            # Top-left corner
            corner_masks[0, :corner_size, :corner_size] = 1.0
            # Top-right corner
            corner_masks[1, :corner_size, -corner_size:] = 1.0
            # Bottom-right corner
            corner_masks[2, -corner_size:, -corner_size:] = 1.0
            # Bottom-left corner
            corner_masks[3, -corner_size:, :corner_size] = 1.0

            # Create edge masks (excluding corner regions)
            edge_masks = torch.zeros((4, h, w), device=pred_heatmaps.device)

            # Ensure we have enough pixels for edge masks
            if h > 2*corner_size and w > 2*corner_size:
                # Top edge (excluding corners)
                edge_masks[0, :3, corner_size:-corner_size] = 1.0
                # Right edge (excluding corners)
                edge_masks[1, corner_size:-corner_size, -3:] = 1.0
                # Bottom edge (excluding corners)
                edge_masks[2, -3:, corner_size:-corner_size] = 1.0
                # Left edge (excluding corners)
                edge_masks[3, corner_size:-corner_size, :3] = 1.0

            # Calculate edge activations for each heatmap
            for c in range(4):
                heatmap = pred_heatmaps[i, c]

                # Penalize activations along edges that aren't in the corresponding corner
                for e in range(4):
                    # Skip if this is the corresponding corner
                    if c == e:
                        continue

                    # Penalize activations along this edge
                    edge_activations = (heatmap * edge_masks[e]).sum()
                    edge_suppression_loss += edge_activations

        edge_suppression_loss = edge_suppression_loss / (batch_size * 4 + self.eps)

        # Peak enhancement loss: encourage sharper peaks at corner locations
        peak_enhancement_loss = 0.0
        valid_peaks = 0
        for i in range(batch_size):
            for c in range(4):
                # Get target and predicted heatmaps
                target_hm = target_heatmaps[i, c]
                pred_hm = pred_heatmaps[i, c]

                # Find the location of the maximum value in the target heatmap
                max_val, max_idx = torch.max(target_hm.view(-1), dim=0)

                # Skip if max_val is too small (no clear peak in target)
                if max_val < 0.1:
                    continue

                y, x = max_idx // target_hm.size(1), max_idx % target_hm.size(1)

                # Calculate the value at this location in the predicted heatmap
                pred_val = pred_hm[y, x]

                # Penalize if the predicted value is not high enough
                peak_enhancement_loss += torch.abs(pred_val - max_val) / (max_val + self.eps)
                valid_peaks += 1

        peak_enhancement_loss = peak_enhancement_loss / (valid_peaks + self.eps)

        # Scale the loss components to similar magnitudes
        mse_loss = mse_loss
        separation_loss = separation_loss * 0.1
        peak_separation_loss = peak_separation_loss * 0.1
        edge_suppression_loss = edge_suppression_loss * 0.1
        peak_enhancement_loss = peak_enhancement_loss * 0.1

        # Combine all losses with weights
        total_loss = (mse_loss +
                     self.separation_weight * separation_loss +
                     self.peak_separation_weight * peak_separation_loss +
                     self.edge_suppression_weight * edge_suppression_loss +
                     self.peak_enhancement_weight * peak_enhancement_loss)

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("WARNING: NaN or Inf detected in loss calculation. Using fallback loss.")
            total_loss = mse_loss

        return total_loss, {
            'mse_loss': mse_loss.item(),
            'separation_loss': separation_loss.item(),
            'peak_separation_loss': peak_separation_loss.item(),
            'edge_suppression_loss': edge_suppression_loss.item(),
            'peak_enhancement_loss': peak_enhancement_loss.item()
        }


class GeometricConsistencyLoss(nn.Module):
    """
    Loss function that enforces geometric consistency between detected corners.
    Ensures that the four corners form a proper quadrilateral.
    """
    def __init__(self, weight=0.2):
        super(GeometricConsistencyLoss, self).__init__()
        self.weight = weight
        self.eps = 1e-6

    def forward(self, pred_heatmaps):
        batch_size = pred_heatmaps.size(0)
        h, w = pred_heatmaps.shape[2], pred_heatmaps.shape[3]

        # Apply sigmoid to predicted heatmaps to ensure values are in [0, 1]
        pred_heatmaps = torch.sigmoid(pred_heatmaps)

        # Extract corner coordinates from heatmaps
        corners = []
        for c in range(4):
            heatmaps_c = pred_heatmaps[:, c]  # (B, H, W)

            # Get indices of maximum values
            max_vals, _ = torch.max(heatmaps_c.view(batch_size, -1), dim=1)
            max_indices = torch.argmax(heatmaps_c.view(batch_size, -1), dim=1)

            # Convert to y, x coordinates
            y = max_indices // w
            x = max_indices % w

            # Normalize to [0, 1]
            x_norm = x.float() / max(w - 1, 1)
            y_norm = y.float() / max(h - 1, 1)

            corners.append(torch.stack([x_norm, y_norm], dim=1))  # (B, 2)

        # Calculate geometric consistency loss
        loss = 0.0

        # 1. Aspect ratio consistency
        # The width/height ratio should be consistent
        width_top = torch.norm(corners[1] - corners[0] + self.eps, dim=1)
        width_bottom = torch.norm(corners[2] - corners[3] + self.eps, dim=1)
        height_left = torch.norm(corners[3] - corners[0] + self.eps, dim=1)
        height_right = torch.norm(corners[2] - corners[1] + self.eps, dim=1)

        # Width should be consistent
        width_diff = F.mse_loss(width_top, width_bottom)
        # Height should be consistent
        height_diff = F.mse_loss(height_left, height_right)

        # 2. Parallelogram check
        # Opposite sides should be parallel
        top_vector = corners[1] - corners[0]  # top edge vector
        bottom_vector = corners[2] - corners[3]  # bottom edge vector
        left_vector = corners[3] - corners[0]  # left edge vector
        right_vector = corners[2] - corners[1]  # right edge vector

        # Add small epsilon to avoid division by zero
        top_vector = top_vector + self.eps
        bottom_vector = bottom_vector + self.eps
        left_vector = left_vector + self.eps
        right_vector = right_vector + self.eps

        # Normalize vectors safely
        top_vector_norm = top_vector / (torch.norm(top_vector, dim=1, keepdim=True) + self.eps)
        bottom_vector_norm = bottom_vector / (torch.norm(bottom_vector, dim=1, keepdim=True) + self.eps)
        left_vector_norm = left_vector / (torch.norm(left_vector, dim=1, keepdim=True) + self.eps)
        right_vector_norm = right_vector / (torch.norm(right_vector, dim=1, keepdim=True) + self.eps)

        # Dot product of normalized vectors should be close to 1 for parallel lines
        # Clamp to avoid numerical issues
        parallel_top_bottom = 1.0 - torch.clamp(torch.sum(top_vector_norm * bottom_vector_norm, dim=1), -1.0, 1.0).mean()
        parallel_left_right = 1.0 - torch.clamp(torch.sum(left_vector_norm * right_vector_norm, dim=1), -1.0, 1.0).mean()

        # Combine all geometric constraints
        loss = width_diff + height_diff + parallel_top_bottom + parallel_left_right

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(loss) or torch.isinf(loss):
            print("WARNING: NaN or Inf detected in geometric loss calculation. Using fallback loss.")
            return torch.tensor(0.01, device=pred_heatmaps.device)

        return self.weight * loss
