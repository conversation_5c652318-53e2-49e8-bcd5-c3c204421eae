"""
Simple inference script for chess board detection model deployment.

This script provides a simple interface for running inference with
the chess board detection model in various deployment formats.
"""

import os
import argparse
import time
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from config import INPUT_SIZE


class ChessBoardDetector:
    """
    Chess board detector for deployment.
    
    This class provides a simple interface for detecting chess boards
    in images using various deployment formats.
    """
    
    def __init__(self, model_path, model_type='pytorch', device=None):
        """
        Initialize the chess board detector.
        
        Args:
            model_path: Path to the model file
            model_type: Type of model ('pytorch', 'quantized', 'onnx', 'torchscript')
            device: Device to run inference on ('cpu', 'cuda', None for auto)
        """
        self.model_path = model_path
        self.model_type = model_type
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"Using device: {self.device}")
        
        # Load model based on type
        self.model = self._load_model()
        
        # Set input size
        self.input_size = INPUT_SIZE
        
        # Set mean and std for normalization
        self.mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1).to(self.device)
        self.std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1).to(self.device)
    
    def _load_model(self):
        """
        Load model based on model type.
        
        Returns:
            Loaded model
        """
        if self.model_type == 'pytorch':
            # Load regular PyTorch model
            model = EnhancedChessBoardUNetV5_2(n_channels=3)
            model.load_state_dict(torch.load(self.model_path, map_location=self.device))
            model = model.to(self.device)
            model.eval()
            return model
        
        elif self.model_type == 'quantized':
            # Load quantized PyTorch model
            model = EnhancedChessBoardUNetV5_2(n_channels=3)
            
            # Set quantization backend
            if self.device.type == 'cpu':
                # Use appropriate backend based on architecture
                import platform
                if platform.machine() in ('arm64', 'aarch64'):
                    torch.backends.quantized.engine = 'qnnpack'
                else:
                    torch.backends.quantized.engine = 'fbgemm'
            
            model.load_state_dict(torch.load(self.model_path, map_location=self.device))
            model.eval()
            return model
        
        elif self.model_type == 'torchscript':
            # Load TorchScript model
            model = torch.jit.load(self.model_path, map_location=self.device)
            model.eval()
            return model
        
        elif self.model_type == 'onnx':
            # Load ONNX model
            try:
                import onnxruntime as ort
                
                # Set up ONNX Runtime session
                if self.device.type == 'cuda':
                    providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
                else:
                    providers = ['CPUExecutionProvider']
                
                session = ort.InferenceSession(self.model_path, providers=providers)
                return session
            except ImportError:
                raise ImportError("ONNX Runtime not installed. Please install with: pip install onnxruntime")
        
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
    
    def preprocess_image(self, image):
        """
        Preprocess image for inference.
        
        Args:
            image: Input image (numpy array, BGR format)
            
        Returns:
            Preprocessed tensor
        """
        # Convert BGR to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize to input size
        h, w = self.input_size
        image_resized = cv2.resize(image_rgb, (w, h))
        
        # Convert to float and normalize
        image_float = image_resized.astype(np.float32) / 255.0
        
        # Convert to tensor and add batch dimension
        if self.model_type == 'onnx':
            # For ONNX, return numpy array
            image_normalized = (image_float - np.array([0.485, 0.456, 0.406]).reshape(1, 1, 3)) / np.array([0.229, 0.224, 0.225]).reshape(1, 1, 3)
            image_chw = np.transpose(image_normalized, (2, 0, 1))
            return np.expand_dims(image_chw, axis=0).astype(np.float32)
        else:
            # For PyTorch models
            image_tensor = torch.from_numpy(image_float).permute(2, 0, 1).unsqueeze(0)
            image_tensor = (image_tensor - self.mean) / self.std
            return image_tensor.to(self.device)
    
    def detect(self, image):
        """
        Detect chess board in image.
        
        Args:
            image: Input image (numpy array, BGR format)
            
        Returns:
            Dictionary with detection results
        """
        # Preprocess image
        input_tensor = self.preprocess_image(image)
        
        # Measure inference time
        start_time = time.time()
        
        # Run inference
        if self.model_type == 'onnx':
            # ONNX inference
            input_name = self.model.get_inputs()[0].name
            segmentation, heatmaps = self.model.run(None, {input_name: input_tensor})
            
            # Convert to torch tensors
            segmentation = torch.from_numpy(segmentation)
            heatmaps = torch.from_numpy(heatmaps)
        else:
            # PyTorch inference
            with torch.no_grad():
                segmentation, heatmaps = self.model(input_tensor)
        
        # Calculate inference time
        inference_time = time.time() - start_time
        
        # Extract corners from heatmaps
        corners = self._extract_corners(heatmaps)
        
        # Scale corners to original image size
        orig_h, orig_w = image.shape[:2]
        model_h, model_w = self.input_size
        
        scaled_corners = []
        for corner in corners:
            if corner is not None:
                x, y = corner
                scaled_x = x * (orig_w / model_w)
                scaled_y = y * (orig_h / model_h)
                scaled_corners.append((scaled_x, scaled_y))
            else:
                scaled_corners.append(None)
        
        return {
            'segmentation': segmentation.cpu().numpy(),
            'heatmaps': heatmaps.cpu().numpy(),
            'corners': corners,
            'scaled_corners': scaled_corners,
            'inference_time': inference_time
        }
    
    def _extract_corners(self, heatmaps):
        """
        Extract corner coordinates from heatmaps.
        
        Args:
            heatmaps: Heatmaps tensor
            
        Returns:
            List of corner coordinates (x, y) or None if not detected
        """
        corners = []
        
        # Process each corner heatmap
        for c in range(4):
            hm = heatmaps[0, c].cpu().numpy()
            
            # Find peak location
            peak_idx = np.argmax(hm)
            y, x = peak_idx // hm.shape[1], peak_idx % hm.shape[1]
            
            # Get peak value
            peak_val = hm[y, x]
            
            # Only consider peaks above threshold
            if peak_val > 0.3:
                corners.append((x, y))
            else:
                corners.append(None)
        
        return corners
    
    def visualize_detection(self, image, results, save_path=None):
        """
        Visualize detection results.
        
        Args:
            image: Original input image
            results: Detection results from detect()
            save_path: Path to save visualization (None to display)
            
        Returns:
            Visualization image
        """
        # Create a copy of the image
        vis_img = image.copy()
        
        # Convert BGR to RGB for visualization
        vis_img = cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB)
        
        # Draw detected corners
        corner_colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # BGR format
        corner_names = ['TL', 'TR', 'BR', 'BL']
        
        for i, corner in enumerate(results['scaled_corners']):
            if corner is not None:
                x, y = int(corner[0]), int(corner[1])
                cv2.circle(vis_img, (x, y), 5, corner_colors[i], -1)
                cv2.putText(vis_img, corner_names[i], (x + 5, y + 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, corner_colors[i], 2)
        
        # Create figure
        plt.figure(figsize=(12, 8))
        
        # Plot original image with corners
        plt.subplot(2, 2, 1)
        plt.imshow(vis_img)
        plt.title(f"Detected Corners (Inference: {results['inference_time']*1000:.1f} ms)")
        plt.axis('off')
        
        # Plot segmentation mask
        plt.subplot(2, 2, 2)
        plt.imshow(results['segmentation'][0, 0], cmap='gray')
        plt.title("Segmentation Mask")
        plt.axis('off')
        
        # Plot combined heatmap
        plt.subplot(2, 2, 3)
        combined_hm = np.zeros((results['heatmaps'].shape[2], results['heatmaps'].shape[3], 3))
        
        for c in range(4):
            hm = results['heatmaps'][0, c]
            
            # Normalize heatmap
            if np.max(hm) > 0:
                hm = hm / np.max(hm)
            
            # Add to combined heatmap with color
            color_idx = c / 3.0
            color = plt.cm.jet(color_idx)[:3]
            
            for ch in range(3):
                combined_hm[:, :, ch] += hm * color[ch]
        
        # Normalize combined heatmap
        if np.max(combined_hm) > 0:
            combined_hm = combined_hm / np.max(combined_hm)
        
        plt.imshow(combined_hm)
        plt.title("Combined Heatmaps")
        plt.axis('off')
        
        # Plot individual heatmaps
        plt.subplot(2, 2, 4)
        fig, axs = plt.subplots(2, 2, figsize=(8, 8))
        fig.suptitle("Individual Corner Heatmaps")
        
        for c in range(4):
            ax = axs[c // 2, c % 2]
            hm = results['heatmaps'][0, c]
            ax.imshow(hm, cmap='jet')
            ax.set_title(corner_names[c])
            ax.axis('off')
        
        plt.tight_layout()
        
        # Save or show visualization
        if save_path:
            plt.savefig(save_path, dpi=150, bbox_inches='tight')
            plt.close()
            return vis_img
        else:
            plt.show()
            return vis_img


def main():
    parser = argparse.ArgumentParser(description='Chess board detection inference')
    parser.add_argument('--model', type=str, required=True,
                        help='Path to model file')
    parser.add_argument('--model_type', type=str, default='pytorch',
                        choices=['pytorch', 'quantized', 'onnx', 'torchscript'],
                        help='Type of model')
    parser.add_argument('--image', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--output', type=str, default=None,
                        help='Path to save output visualization')
    parser.add_argument('--device', type=str, default=None,
                        choices=['cpu', 'cuda'],
                        help='Device to run inference on (default: auto)')
    args = parser.parse_args()

    # Check if image exists
    if not os.path.exists(args.image):
        print(f"Error: Image not found at {args.image}")
        return

    # Load image
    image = cv2.imread(args.image)
    if image is None:
        print(f"Error: Could not load image from {args.image}")
        return

    # Create detector
    detector = ChessBoardDetector(
        model_path=args.model,
        model_type=args.model_type,
        device=args.device
    )

    # Run detection
    results = detector.detect(image)

    # Print results
    print("\nDetection Results:")
    print(f"Inference time: {results['inference_time']*1000:.2f} ms")
    
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i, corner in enumerate(results['scaled_corners']):
        if corner is not None:
            print(f"{corner_names[i]} corner: ({corner[0]:.1f}, {corner[1]:.1f})")
        else:
            print(f"{corner_names[i]} corner: Not detected")

    # Visualize results
    detector.visualize_detection(image, results, args.output)
    
    if args.output:
        print(f"Visualization saved to: {args.output}")


if __name__ == "__main__":
    main()
