package com.chessvision.app

import androidx.compose.runtime.*
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect

class ChessBoardState {
    var board = Array(8) { Array<ChessPiece?>(8) { null } }
    var selectedSquare by mutableStateOf<ChessPosition?>(null)
    var isEditMode by mutableStateOf(false)
    var currentFENString by mutableStateOf("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    var selectedPieceForPlacement by mutableStateOf<ChessPiece?>(null)
    var isBoardFlipped by mutableStateOf(false)
    var draggedPiece by mutableStateOf<ChessPiece?>(null)
    var dragOffset by mutableStateOf(Offset.Zero)
    var isDragging by mutableStateOf(false)
    var dragStartPosition by mutableStateOf<ChessPosition?>(null)
    var isDraggingFromTray by mutableStateOf(false)
    var draggedFromTrayPiece by mutableStateOf<ChessPiece?>(null)
    var dragStartOffset by mutableStateOf(Offset.Zero)
    var boardBounds by mutableStateOf(Rect.Zero)

    // Animation states for Material 3 Expressive features
    var lastMovedSquare by mutableStateOf<ChessPosition?>(null)
    var isAnimatingPiece by mutableStateOf(false)
    var animationTrigger by mutableStateOf(0)

    // FEN update callback for real-time synchronization
    private var onFENUpdated: ((String) -> Unit)? = null

    fun setFENUpdateCallback(callback: (String) -> Unit) {
        onFENUpdated = callback
    }

    init {
        setupInitialPosition()
    }

    // Enhanced FEN update that always triggers when board changes
    private fun updateFEN() {
        val newFEN = getCurrentFEN()
        if (newFEN != currentFENString) {
            currentFENString = newFEN
            onFENUpdated?.invoke(newFEN)
            // Trigger animation update
            animationTrigger++
        }
    }

    fun setupInitialPosition() {
        // Clear board
        board = Array(8) { Array<ChessPiece?>(8) { null } }

        // Setup initial chess position
        val initialFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        loadFromFEN(initialFEN)
    }

    fun loadFromFEN(fen: String) {
        currentFENString = fen
        val parts = fen.split(" ")
        val position = parts[0]

        // Clear board
        board = Array(8) { Array<ChessPiece?>(8) { null } }

        val ranks = position.split("/")
        for (rankIndex in ranks.indices) {
            var fileIndex = 0
            for (char in ranks[rankIndex]) {
                if (char.isDigit()) {
                    fileIndex += char.digitToInt()
                } else {
                    val piece = charToPiece(char)
                    if (piece != null && fileIndex < 8) {
                        board[7 - rankIndex][fileIndex] = piece
                    }
                    fileIndex++
                }
            }
        }
        // Always update FEN after loading
        updateFEN()
    }

    fun getCurrentFEN(): String {
        val fenBuilder = StringBuilder()

        for (rank in 7 downTo 0) {
            var emptyCount = 0
            for (file in 0..7) {
                val piece = board[rank][file]
                if (piece == null) {
                    emptyCount++
                } else {
                    if (emptyCount > 0) {
                        fenBuilder.append(emptyCount)
                        emptyCount = 0
                    }
                    fenBuilder.append(pieceToChar(piece))
                }
            }
            if (emptyCount > 0) {
                fenBuilder.append(emptyCount)
            }
            if (rank > 0) fenBuilder.append("/")
        }

        fenBuilder.append(" w KQkq - 0 1") // Default game state
        return fenBuilder.toString()
    }

    private fun charToPiece(char: Char): ChessPiece? {
        val color = if (char.isUpperCase()) PieceColor.WHITE else PieceColor.BLACK
        val type = when (char.lowercaseChar()) {
            'k' -> PieceType.KING
            'q' -> PieceType.QUEEN
            'r' -> PieceType.ROOK
            'b' -> PieceType.BISHOP
            'n' -> PieceType.KNIGHT
            'p' -> PieceType.PAWN
            else -> return null
        }
        val drawableRes = getPieceDrawable(type, color)
        return ChessPiece(type, color, drawableRes)
    }

    private fun pieceToChar(piece: ChessPiece): Char {
        val baseChar = when (piece.type) {
            PieceType.KING -> 'k'
            PieceType.QUEEN -> 'q'
            PieceType.ROOK -> 'r'
            PieceType.BISHOP -> 'b'
            PieceType.KNIGHT -> 'n'
            PieceType.PAWN -> 'p'
        }
        return if (piece.color == PieceColor.WHITE) baseChar.uppercaseChar() else baseChar
    }

    private fun getPieceDrawable(type: PieceType, color: PieceColor): Int {
        return when (type to color) {
            PieceType.KING to PieceColor.WHITE -> R.drawable.wk
            PieceType.KING to PieceColor.BLACK -> R.drawable.bk
            PieceType.QUEEN to PieceColor.WHITE -> R.drawable.wq
            PieceType.QUEEN to PieceColor.BLACK -> R.drawable.bq
            PieceType.ROOK to PieceColor.WHITE -> R.drawable.wr
            PieceType.ROOK to PieceColor.BLACK -> R.drawable.br
            PieceType.BISHOP to PieceColor.WHITE -> R.drawable.wb
            PieceType.BISHOP to PieceColor.BLACK -> R.drawable.bb
            PieceType.KNIGHT to PieceColor.WHITE -> R.drawable.wn
            PieceType.KNIGHT to PieceColor.BLACK -> R.drawable.bn
            PieceType.PAWN to PieceColor.WHITE -> R.drawable.wp
            PieceType.PAWN to PieceColor.BLACK -> R.drawable.bp
            else -> R.drawable.wp // Default fallback
        }
    }

    fun onSquareClick(position: ChessPosition) {
        if (isEditMode) {
            // Edit mode: place selected piece or clear square
            if (selectedPieceForPlacement != null) {
                // Place the selected piece
                board[position.rank][position.file] = selectedPieceForPlacement
            } else {
                // Clear the square if no piece is selected
                board[position.rank][position.file] = null
            }
            // Always update FEN after any board change in edit mode
            updateFEN()
            // Set animation state for expressive feedback
            lastMovedSquare = position
            isAnimatingPiece = true
        } else {
            // Play mode: select and move pieces
            handleMove(position)
        }
    }

    fun updateFromFEN(fen: String) {
        loadFromFEN(fen)
    }

    fun flipBoard() {
        isBoardFlipped = !isBoardFlipped
    }

    // Helper function to check if a piece can be selected (for turn-based play)
    private fun canSelectPiece(piece: ChessPiece?): Boolean {
        // For now, allow selecting any piece (no turn restrictions)
        // Later you can add turn-based logic here
        return piece != null
    }

    private fun handleMove(position: ChessPosition) {
        val selected = selectedSquare
        if (selected == null) {
            // Select piece if there's one at this position and it can be selected
            val piece = board[position.rank][position.file]
            if (piece != null && canSelectPiece(piece)) {
                selectedSquare = position
                android.util.Log.d("ChessMove", "Selected ${piece.color} ${piece.type} at ${position.file},${position.rank}")
            }
        } else {
            if (selected == position) {
                // Deselect if clicking same square
                selectedSquare = null
            } else {
                // Check if the move is legal before executing
                val legalMoves = getLegalMoves(selected)
                if (legalMoves.contains(position)) {
                    // Legal move - execute it
                    val movingPiece = board[selected.rank][selected.file]
                    val capturedPiece = board[position.rank][position.file]

                    // Execute the move
                    board[position.rank][position.file] = movingPiece
                    board[selected.rank][selected.file] = null
                    selectedSquare = null
                    lastMovedSquare = position
                    isAnimatingPiece = true

                    // Log the move for debugging
                    android.util.Log.d("ChessMove", "Legal move: ${movingPiece?.type} from ${selected.file},${selected.rank} to ${position.file},${position.rank}")
                    if (capturedPiece != null) {
                        android.util.Log.d("ChessMove", "Captured: ${capturedPiece.type}")
                    }

                    // Always update FEN after any legal move
                    updateFEN()
                } else {
                    // Illegal move - just deselect or select new piece
                    val pieceAtTarget = board[position.rank][position.file]
                    if (pieceAtTarget != null) {
                        // Select the piece at the target position instead
                        selectedSquare = position
                        android.util.Log.d("ChessMove", "Illegal move attempted, selecting piece at target instead")
                    } else {
                        // Deselect if clicking on empty square with illegal move
                        selectedSquare = null
                        android.util.Log.d("ChessMove", "Illegal move to empty square, deselecting")
                    }
                }
            }
        }
    }

    fun getLegalMoves(position: ChessPosition): List<ChessPosition> {
        val piece = board[position.rank][position.file] ?: return emptyList()
        val moves = mutableListOf<ChessPosition>()

        when (piece.type) {
            PieceType.PAWN -> {
                val direction = if (piece.color == PieceColor.WHITE) 1 else -1
                val startRank = if (piece.color == PieceColor.WHITE) 1 else 6

                // Forward move
                val oneStep = ChessPosition(position.file, position.rank + direction)
                if (oneStep.isValid() && board[oneStep.rank][oneStep.file] == null) {
                    moves.add(oneStep)

                    // Two steps from starting position
                    if (position.rank == startRank) {
                        val twoStep = ChessPosition(position.file, position.rank + 2 * direction)
                        if (twoStep.isValid() && board[twoStep.rank][twoStep.file] == null) {
                            moves.add(twoStep)
                        }
                    }
                }

                // Captures (diagonal attacks)
                for (fileOffset in listOf(-1, 1)) {
                    val capturePos = ChessPosition(position.file + fileOffset, position.rank + direction)
                    if (capturePos.isValid()) {
                        val targetPiece = board[capturePos.rank][capturePos.file]
                        if (targetPiece != null && targetPiece.color != piece.color) {
                            moves.add(capturePos)
                        }
                    }
                }
            }

            PieceType.ROOK -> {
                // Horizontal and vertical moves
                val directions = listOf(
                    Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0)
                )
                for ((dx, dy) in directions) {
                    for (i in 1..7) {
                        val newPos = ChessPosition(position.file + dx * i, position.rank + dy * i)
                        if (!newPos.isValid()) break

                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            moves.add(newPos)
                        } else {
                            if (targetPiece.color != piece.color) {
                                moves.add(newPos)
                            }
                            break
                        }
                    }
                }
            }

            PieceType.KNIGHT -> {
                val knightMoves = listOf(
                    Pair(2, 1), Pair(2, -1), Pair(-2, 1), Pair(-2, -1),
                    Pair(1, 2), Pair(1, -2), Pair(-1, 2), Pair(-1, -2)
                )
                for ((dx, dy) in knightMoves) {
                    val newPos = ChessPosition(position.file + dx, position.rank + dy)
                    if (newPos.isValid()) {
                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null || targetPiece.color != piece.color) {
                            moves.add(newPos)
                        }
                    }
                }
            }

            PieceType.BISHOP -> {
                val directions = listOf(
                    Pair(1, 1), Pair(1, -1), Pair(-1, 1), Pair(-1, -1)
                )
                for ((dx, dy) in directions) {
                    for (i in 1..7) {
                        val newPos = ChessPosition(position.file + dx * i, position.rank + dy * i)
                        if (!newPos.isValid()) break

                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            moves.add(newPos)
                        } else {
                            if (targetPiece.color != piece.color) {
                                moves.add(newPos)
                            }
                            break
                        }
                    }
                }
            }

            PieceType.QUEEN -> {
                val directions = listOf(
                    Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0),
                    Pair(1, 1), Pair(1, -1), Pair(-1, 1), Pair(-1, -1)
                )
                for ((dx, dy) in directions) {
                    for (i in 1..7) {
                        val newPos = ChessPosition(position.file + dx * i, position.rank + dy * i)
                        if (!newPos.isValid()) break

                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            moves.add(newPos)
                        } else {
                            if (targetPiece.color != piece.color) {
                                moves.add(newPos)
                            }
                            break
                        }
                    }
                }
            }

            PieceType.KING -> {
                val kingMoves = listOf(
                    Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0),
                    Pair(1, 1), Pair(1, -1), Pair(-1, 1), Pair(-1, -1)
                )
                for ((dx, dy) in kingMoves) {
                    val newPos = ChessPosition(position.file + dx, position.rank + dy)
                    if (newPos.isValid()) {
                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null || targetPiece.color != piece.color) {
                            moves.add(newPos)
                        }
                    }
                }
            }
        }

        return moves
    }

    // Drag and drop functionality
    fun startDrag(piece: ChessPiece, position: ChessPosition) {
        draggedPiece = piece
        isDragging = true
        dragStartPosition = position
        isDraggingFromTray = false
        board[position.rank][position.file] = null
        android.util.Log.d("ChessDrag", "Started dragging ${piece.type} from board position ${position.file},${position.rank}")
    }

    fun startDragFromTray(piece: ChessPiece, startOffset: Offset) {
        draggedFromTrayPiece = piece
        isDraggingFromTray = true
        isDragging = true
        dragStartOffset = startOffset
        dragOffset = startOffset
        android.util.Log.d("ChessDrag", "Started dragging ${piece.type} from tray")
    }

    fun updateDragPosition(newOffset: Offset) {
        dragOffset = newOffset
    }

    fun updateBoardBounds(bounds: Rect) {
        boardBounds = bounds
    }

    fun isPositionOnBoard(offset: Offset): Boolean {
        return boardBounds.contains(offset)
    }

    fun getSquareFromOffset(offset: Offset): ChessPosition? {
        if (!isPositionOnBoard(offset)) return null

        val squareSize = boardBounds.width / 8f
        val relativeX = offset.x - boardBounds.left
        val relativeY = offset.y - boardBounds.top

        val file = (relativeX / squareSize).toInt().coerceIn(0, 7)
        val rank = (7 - (relativeY / squareSize).toInt()).coerceIn(0, 7)

        return ChessPosition(file, rank)
    }

    fun endDrag(targetPosition: ChessPosition? = null) {
        val finalTargetPosition = targetPosition ?: getSquareFromOffset(dragOffset)

        if (isDraggingFromTray) {
            draggedFromTrayPiece?.let { piece ->
                if (finalTargetPosition != null && finalTargetPosition.isValid()) {
                    board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                    lastMovedSquare = finalTargetPosition
                    isAnimatingPiece = true
                    android.util.Log.d("ChessMove", "Placed ${piece.type} from tray to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                }
            }
        } else {
            draggedPiece?.let { piece ->
                if (finalTargetPosition != null && finalTargetPosition.isValid()) {
                    if (isEditMode) {
                        board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                        lastMovedSquare = finalTargetPosition
                        isAnimatingPiece = true
                        android.util.Log.d("ChessMove", "Edit mode drag: ${piece.type} to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                    } else {
                        val originalPosition = dragStartPosition
                        if (originalPosition != null) {
                            val legalMoves = getLegalMoves(originalPosition)
                            if (legalMoves.contains(finalTargetPosition)) {
                                board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                                lastMovedSquare = finalTargetPosition
                                isAnimatingPiece = true
                                selectedSquare = null
                                android.util.Log.d("ChessMove", "Legal drag move: ${piece.type} to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                            } else {
                                board[originalPosition.rank][originalPosition.file] = piece
                                android.util.Log.d("ChessMove", "Illegal drag move attempted, piece returned to original position")
                            }
                        } else {
                            board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                        }
                    }
                } else {
                    val originalPosition = dragStartPosition
                    if (originalPosition != null) {
                        board[originalPosition.rank][originalPosition.file] = piece
                    } else {
                        // Piece is lost if we can't find original position
                        android.util.Log.w("ChessMove", "Could not find original position for dragged piece")
                    }
                }
            }
        }

        // Clean up all drag state
        draggedPiece = null
        draggedFromTrayPiece = null
        isDragging = false
        isDraggingFromTray = false
        dragOffset = Offset.Zero
        dragStartPosition = null
        dragStartOffset = Offset.Zero
        updateFEN()
    }
}
