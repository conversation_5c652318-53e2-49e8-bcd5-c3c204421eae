"""
Advanced loss functions for enhanced segmentation training.
Includes focal loss, boundary-aware loss, and multi-scale supervision.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from scipy import ndimage
from scipy.ndimage import distance_transform_edt

class FocalLoss(nn.Module):
    """Focal Loss for addressing class imbalance and hard examples."""

    def __init__(self, alpha=1, gamma=2, reduction='mean'):
        super(Focal<PERSON>oss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs, targets):
        ce_loss = F.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss

        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class DiceLoss(nn.Module):
    """Dice Loss for segmentation."""

    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, inputs, targets):
        inputs = torch.sigmoid(inputs)

        # Flatten
        inputs = inputs.view(-1)
        targets = targets.view(-1)

        intersection = (inputs * targets).sum()
        dice = (2. * intersection + self.smooth) / (inputs.sum() + targets.sum() + self.smooth)

        return 1 - dice

class IoULoss(nn.Module):
    """IoU Loss for segmentation."""

    def __init__(self, smooth=1e-6):
        super(IoULoss, self).__init__()
        self.smooth = smooth

    def forward(self, inputs, targets):
        inputs = torch.sigmoid(inputs)

        # Flatten
        inputs = inputs.view(-1)
        targets = targets.view(-1)

        intersection = (inputs * targets).sum()
        total = (inputs + targets).sum()
        union = total - intersection

        iou = (intersection + self.smooth) / (union + self.smooth)
        return 1 - iou

class TverskyLoss(nn.Module):
    """Tversky Loss - generalization of Dice loss."""

    def __init__(self, alpha=0.5, beta=0.5, smooth=1e-6):
        super(TverskyLoss, self).__init__()
        self.alpha = alpha
        self.beta = beta
        self.smooth = smooth

    def forward(self, inputs, targets):
        inputs = torch.sigmoid(inputs)

        # Flatten
        inputs = inputs.view(-1)
        targets = targets.view(-1)

        # True Positives, False Positives & False Negatives
        tp = (inputs * targets).sum()
        fp = ((1 - targets) * inputs).sum()
        fn = (targets * (1 - inputs)).sum()

        tversky = (tp + self.smooth) / (tp + self.alpha * fp + self.beta * fn + self.smooth)
        return 1 - tversky

class BoundaryLoss(nn.Module):
    """Boundary-aware loss for sharp edge detection."""

    def __init__(self, theta0=3, theta=5):
        super(BoundaryLoss, self).__init__()
        self.theta0 = theta0
        self.theta = theta

    def forward(self, inputs, targets):
        """
        inputs: [B, 1, H, W] - predicted logits
        targets: [B, H, W] - ground truth masks
        """
        # Convert to probabilities
        probs = torch.sigmoid(inputs).squeeze(1)  # [B, H, W]

        # Compute distance transforms on CPU (scipy doesn't support GPU)
        device = inputs.device
        batch_size = targets.shape[0]

        boundary_loss = 0
        for b in range(batch_size):
            target_np = targets[b].cpu().numpy()
            prob_np = probs[b].detach().cpu().numpy()

            # Compute distance transform for boundaries
            # Distance to boundary (both inside and outside)
            boundary = self._get_boundary(target_np)
            if boundary.sum() == 0:
                continue

            # Distance transform
            dist_inside = distance_transform_edt(target_np)
            dist_outside = distance_transform_edt(1 - target_np)

            # Combine distances (negative inside, positive outside)
            dist_map = np.where(target_np > 0.5, -dist_inside, dist_outside)

            # Convert back to tensor
            dist_tensor = torch.from_numpy(dist_map).float().to(device)

            # Boundary loss computation
            boundary_term = torch.tanh(dist_tensor / self.theta0)
            prob_tensor = torch.from_numpy(prob_np).float().to(device)
            loss_b = torch.mean(prob_tensor * boundary_term)
            boundary_loss += loss_b

        return boundary_loss / batch_size

    def _get_boundary(self, mask):
        """Extract boundary from mask."""
        # Sobel edge detection
        sobel_x = ndimage.sobel(mask, axis=0)
        sobel_y = ndimage.sobel(mask, axis=1)
        boundary = np.sqrt(sobel_x**2 + sobel_y**2)
        return boundary > 0

class ComboLoss(nn.Module):
    """Combination of multiple losses for comprehensive training."""

    def __init__(self,
                 focal_weight=1.0,
                 dice_weight=1.0,
                 iou_weight=0.5,
                 boundary_weight=0.5,
                 tversky_weight=0.5):
        super(ComboLoss, self).__init__()
        self.focal_weight = focal_weight
        self.dice_weight = dice_weight
        self.iou_weight = iou_weight
        self.boundary_weight = boundary_weight
        self.tversky_weight = tversky_weight

        self.focal_loss = FocalLoss(alpha=1, gamma=2)
        self.dice_loss = DiceLoss()
        self.iou_loss = IoULoss()
        self.boundary_loss = BoundaryLoss()
        self.tversky_loss = TverskyLoss(alpha=0.7, beta=0.3)  # Focus on recall

    def forward(self, inputs, targets):
        # Ensure targets have channel dimension for boundary loss
        if targets.dim() == 3:
            targets_for_boundary = targets
            targets = targets.unsqueeze(1)
        else:
            targets_for_boundary = targets.squeeze(1)

        focal = self.focal_loss(inputs, targets)
        dice = self.dice_loss(inputs, targets)
        iou = self.iou_loss(inputs, targets)
        tversky = self.tversky_loss(inputs, targets)

        # Boundary loss (computationally expensive, use sparingly)
        boundary = 0
        if self.boundary_weight > 0:
            try:
                boundary = self.boundary_loss(inputs, targets_for_boundary)
            except:
                boundary = 0  # Fallback if boundary loss fails

        total_loss = (self.focal_weight * focal +
                     self.dice_weight * dice +
                     self.iou_weight * iou +
                     self.boundary_weight * boundary +
                     self.tversky_weight * tversky)

        return total_loss, {
            'focal': focal.item(),
            'dice': dice.item(),
            'iou': iou.item(),
            'boundary': boundary.item() if isinstance(boundary, torch.Tensor) else boundary,
            'tversky': tversky.item(),
            'total': total_loss.item()
        }

class DeepSupervisionLoss(nn.Module):
    """Deep supervision loss for multi-scale training."""

    def __init__(self, base_loss, weights=[1.0, 0.8, 0.6, 0.4]):
        super(DeepSupervisionLoss, self).__init__()
        self.base_loss = base_loss
        self.weights = weights

    def forward(self, outputs, targets):
        """
        outputs: tuple of (main_output, ds1, ds2, ds3) or single output
        targets: ground truth masks
        """
        if isinstance(outputs, tuple):
            # Deep supervision mode
            main_output = outputs[0]
            ds_outputs = outputs[1:]

            # Main loss
            if hasattr(self.base_loss, 'forward') and len(self.base_loss.forward.__code__.co_varnames) > 3:
                # ComboLoss returns tuple
                main_loss, main_metrics = self.base_loss(main_output, targets)
            else:
                main_loss = self.base_loss(main_output, targets)
                main_metrics = {'main': main_loss.item()}

            total_loss = self.weights[0] * main_loss

            # Deep supervision losses
            for i, (ds_output, weight) in enumerate(zip(ds_outputs, self.weights[1:])):
                if hasattr(self.base_loss, 'forward') and len(self.base_loss.forward.__code__.co_varnames) > 3:
                    ds_loss, _ = self.base_loss(ds_output, targets)
                else:
                    ds_loss = self.base_loss(ds_output, targets)

                total_loss += weight * ds_loss
                main_metrics[f'ds_{i+1}'] = ds_loss.item()

            main_metrics['total'] = total_loss.item()
            return total_loss, main_metrics
        else:
            # Single output mode
            if hasattr(self.base_loss, 'forward') and len(self.base_loss.forward.__code__.co_varnames) > 3:
                return self.base_loss(outputs, targets)
            else:
                loss = self.base_loss(outputs, targets)
                return loss, {'total': loss.item()}

class AdaptiveLoss(nn.Module):
    """Adaptive loss that adjusts weights based on training progress."""

    def __init__(self, base_loss, adaptation_schedule='cosine'):
        super(AdaptiveLoss, self).__init__()
        self.base_loss = base_loss
        self.adaptation_schedule = adaptation_schedule
        self.current_epoch = 0
        self.max_epochs = 100

    def update_epoch(self, epoch, max_epochs):
        """Update current epoch for adaptive weighting."""
        self.current_epoch = epoch
        self.max_epochs = max_epochs

        # Adapt loss weights based on training progress
        progress = epoch / max_epochs

        if self.adaptation_schedule == 'cosine':
            # Start with boundary focus, end with dice focus
            boundary_weight = 0.5 * (1 + np.cos(np.pi * progress))
            dice_weight = 0.5 * (1 + np.cos(np.pi * (1 - progress)))
        elif self.adaptation_schedule == 'linear':
            boundary_weight = 1.0 - progress
            dice_weight = progress
        else:
            boundary_weight = 0.5
            dice_weight = 1.0

        # Update base loss weights if it's a ComboLoss
        if hasattr(self.base_loss, 'boundary_weight'):
            self.base_loss.boundary_weight = boundary_weight
        if hasattr(self.base_loss, 'dice_weight'):
            self.base_loss.dice_weight = dice_weight

    def forward(self, outputs, targets):
        return self.base_loss(outputs, targets)

def get_loss_function(loss_type='combo', deep_supervision=True, adaptive=True):
    """Get the appropriate loss function."""

    if loss_type == 'combo':
        base_loss = ComboLoss(
            focal_weight=1.0,
            dice_weight=2.0,
            iou_weight=1.0,
            boundary_weight=0.5,
            tversky_weight=1.0
        )
    elif loss_type == 'focal_dice':
        class FocalDiceLoss(nn.Module):
            def __init__(self):
                super().__init__()
                self.focal = FocalLoss()
                self.dice = DiceLoss()

            def forward(self, inputs, targets):
                focal = self.focal(inputs, targets)
                dice = self.dice(inputs, targets)
                total = focal + dice
                return total, {'focal': focal.item(), 'dice': dice.item(), 'total': total.item()}

        base_loss = FocalDiceLoss()
    else:
        base_loss = ComboLoss()

    # Wrap with deep supervision if needed
    if deep_supervision:
        loss_fn = DeepSupervisionLoss(base_loss)
    else:
        loss_fn = base_loss

    # Wrap with adaptive scheduling if needed
    if adaptive:
        loss_fn = AdaptiveLoss(loss_fn)

    return loss_fn

if __name__ == "__main__":
    # Test loss functions
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    # Test data
    batch_size, height, width = 2, 256, 256
    inputs = torch.randn(batch_size, 1, height, width).to(device)
    targets = torch.randint(0, 2, (batch_size, height, width)).float().to(device)

    # Test ComboLoss
    print("Testing ComboLoss...")
    combo_loss = ComboLoss()
    loss, metrics = combo_loss(inputs, targets)
    print(f"ComboLoss: {loss.item():.4f}")
    print(f"Metrics: {metrics}")

    # Test DeepSupervisionLoss
    print("\nTesting DeepSupervisionLoss...")
    ds_outputs = (inputs, inputs, inputs, inputs)  # Simulate deep supervision outputs
    ds_loss = DeepSupervisionLoss(combo_loss)
    loss, metrics = ds_loss(ds_outputs, targets)
    print(f"DeepSupervisionLoss: {loss.item():.4f}")
    print(f"Metrics: {metrics}")

    print("\nLoss functions test completed!")
