"""
Train U-Net for chess board segmentation using the existing augmented dataset.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.chessboard_segmentation_unet import get_model
from chess_board_detection.dataset.augmented_segmentation_dataset import create_augmented_dataloaders

class DiceLoss(nn.Module):
    """Dice loss for segmentation."""
    
    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
    
    def forward(self, predictions, targets):
        predictions = torch.sigmoid(predictions)
        
        # Flatten
        predictions = predictions.view(-1)
        targets = targets.view(-1)
        
        intersection = (predictions * targets).sum()
        dice = (2. * intersection + self.smooth) / (predictions.sum() + targets.sum() + self.smooth)
        
        return 1 - dice

def calculate_iou(predictions, targets, threshold=0.5):
    """Calculate IoU metric."""
    predictions = (torch.sigmoid(predictions) > threshold).float()
    targets = (targets > threshold).float()
    
    intersection = (predictions * targets).sum()
    union = predictions.sum() + targets.sum() - intersection
    
    if union == 0:
        return 1.0 if intersection == 0 else 0.0
    
    return (intersection / union).item()

def calculate_dice(predictions, targets, threshold=0.5):
    """Calculate Dice coefficient."""
    predictions = (torch.sigmoid(predictions) > threshold).float()
    targets = (targets > threshold).float()
    
    intersection = (predictions * targets).sum()
    total = predictions.sum() + targets.sum()
    
    if total == 0:
        return 1.0 if intersection == 0 else 0.0
    
    return (2 * intersection / total).item()

def train_epoch(model, train_loader, criterion, optimizer, device):
    """Train for one epoch."""
    model.train()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(train_loader)
    
    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device)
        masks = masks.to(device).unsqueeze(1)  # Add channel dimension
        
        optimizer.zero_grad()
        
        outputs = model(images)
        loss = criterion(outputs, masks)
        
        loss.backward()
        optimizer.step()
        
        # Calculate metrics
        with torch.no_grad():
            iou = calculate_iou(outputs, masks)
            dice = calculate_dice(outputs, masks)
        
        total_loss += loss.item()
        total_iou += iou
        total_dice += dice
        
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'IoU': f'{iou:.4f}',
            'Dice': f'{dice:.4f}'
        })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def validate_epoch(model, val_loader, criterion, device):
    """Validate for one epoch."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device)
            masks = masks.to(device).unsqueeze(1)  # Add channel dimension
            
            outputs = model(images)
            loss = criterion(outputs, masks)
            
            # Calculate metrics
            iou = calculate_iou(outputs, masks)
            dice = calculate_dice(outputs, masks)
            
            total_loss += loss.item()
            total_iou += iou
            total_dice += dice
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{iou:.4f}',
                'Dice': f'{dice:.4f}'
            })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def train_unet_segmentation():
    """Train U-Net for chess board segmentation."""
    
    # Configuration
    DATASET_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326"
    SAVE_DIR = "chess_board_detection/unet_segmentation_results"
    
    EPOCHS = 50
    BATCH_SIZE = 8
    LEARNING_RATE = 1e-4
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Create save directory
    save_dir = Path(SAVE_DIR)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Create dataloaders
    print("Creating dataloaders...")
    train_loader, val_loader = create_augmented_dataloaders(
        DATASET_DIR,
        batch_size=BATCH_SIZE,
        train_split=0.8,
        num_workers=0  # Avoid multiprocessing issues
    )
    
    # Create model
    print("Creating U-Net model...")
    model = get_model(model_type="standard", n_channels=3, n_classes=1)
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,}")
    
    # Loss and optimizer
    bce_loss = nn.BCEWithLogitsLoss()
    dice_loss = DiceLoss()
    
    def combined_loss(outputs, targets):
        return bce_loss(outputs, targets) + dice_loss(outputs, targets)
    
    optimizer = optim.AdamW(model.parameters(), lr=LEARNING_RATE, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
    
    # Training history
    history = {
        'train_loss': [], 'val_loss': [],
        'train_iou': [], 'val_iou': [],
        'train_dice': [], 'val_dice': []
    }
    
    best_val_dice = 0
    patience = 15
    patience_counter = 0
    
    print(f"Starting training for {EPOCHS} epochs...")
    start_time = time.time()
    
    for epoch in range(EPOCHS):
        print(f"\nEpoch {epoch+1}/{EPOCHS}")
        
        # Train
        train_loss, train_iou, train_dice = train_epoch(model, train_loader, combined_loss, optimizer, device)
        
        # Validate
        val_loss, val_iou, val_dice = validate_epoch(model, val_loader, combined_loss, device)
        
        # Update scheduler
        scheduler.step(val_loss)
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_iou'].append(train_iou)
        history['val_iou'].append(val_iou)
        history['train_dice'].append(train_dice)
        history['val_dice'].append(val_dice)
        
        # Print epoch results
        print(f"Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}, Dice: {train_dice:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}, Dice: {val_dice:.4f}")
        print(f"LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # Save best model
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            torch.save(model.state_dict(), save_dir / "best_model.pth")
            print(f"New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_dice': val_dice,
                'history': history
            }, save_dir / f"checkpoint_epoch_{epoch+1}.pth")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"Early stopping triggered after {patience} epochs without improvement")
            break
    
    # Save final results
    torch.save(model.state_dict(), save_dir / "final_model.pth")
    
    with open(save_dir / "training_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time/3600:.2f} hours")
    print(f"Best validation Dice: {best_val_dice:.4f}")
    print(f"Results saved to: {save_dir}")
    
    return model, history, best_val_dice

def test_trained_model(model_path, dataset_dir, num_samples=5):
    """Test the trained model on some samples."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Load model
    model = get_model(model_type="standard", n_channels=3, n_classes=1)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    # Create test dataloader
    _, val_loader = create_augmented_dataloaders(
        dataset_dir, batch_size=1, train_split=0.8, num_workers=0
    )
    
    print(f"Testing model on {num_samples} samples...")
    
    with torch.no_grad():
        for i, (image, mask) in enumerate(val_loader):
            if i >= num_samples:
                break
                
            image = image.to(device)
            mask = mask.to(device).unsqueeze(1)
            
            output = model(image)
            pred_mask = torch.sigmoid(output) > 0.5
            
            iou = calculate_iou(output, mask)
            dice = calculate_dice(output, mask)
            
            print(f"Sample {i+1}: IoU={iou:.4f}, Dice={dice:.4f}")

if __name__ == "__main__":
    print("Starting U-Net segmentation training with augmented dataset...")
    
    try:
        model, history, best_dice = train_unet_segmentation()
        
        # Test the best model
        print("\nTesting the best model...")
        test_trained_model(
            "chess_board_detection/unet_segmentation_results/best_model.pth",
            r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326"
        )
        
    except Exception as e:
        print(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()
