{"logs": [{"outputFile": "com.chessvision.app-mergeDebugResources-51:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\app with custom ai\\ChessVisionApp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "25", "endColumns": "12", "endOffsets": "1196"}, "to": {"startLines": "1885", "startColumns": "4", "startOffsets": "124166", "endLines": "1908", "endColumns": "12", "endOffsets": "125071"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4713635adcc2bac18369e016a04ea54e\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,272,273,274,275,277,307,367,368,372,373,375,386,387,449,452,453,455,466,467,474,481,482,483,1558,1561,1564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15595,15654,15713,15773,15833,15893,15953,16013,16073,16133,16193,16253,16313,16372,16432,16492,16552,16612,16672,16732,16792,16852,16912,16972,17031,17091,17151,17210,17269,17328,17387,17446,17710,17784,17842,17897,17982,19510,23675,23740,23937,24003,24146,24888,24940,29414,29582,29636,29706,30269,30319,30656,31045,31092,31128,99487,99599,99710", "endLines": "236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,272,273,274,275,277,307,367,368,372,373,375,386,387,449,452,453,455,466,467,474,481,482,483,1560,1563,1567", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "15649,15708,15768,15828,15888,15948,16008,16068,16128,16188,16248,16308,16367,16427,16487,16547,16607,16667,16727,16787,16847,16907,16967,17026,17086,17146,17205,17264,17323,17382,17441,17500,17779,17837,17892,17943,18032,19558,23735,23789,23998,24099,24199,24935,24995,29471,29631,29667,29735,30314,30368,30697,31087,31123,31213,99594,99705,99900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2559031d7e9fe48b3590293cf5c6448f\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397,398,401,402,403,404,405,406,407,408,409,410,411,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25047,25135,25221,25302,25386,25455,25520,25603,25709,25795,25915,25969,26038,26099,26168,26257,26352,26426,26523,26616,26714,26863,26954,27042,27138,27236,27300,27368,27455,27549,27616,27688,27760,27861,27970,28046,28115,28163,28229,28293,28350,28407,28479,28529,28583,28654,28725,28795,28864,28922,28998,29069,29143,29229,29279,29349", "endLines": "389,390,391,392,393,394,395,396,397,400,401,402,403,404,405,406,407,408,409,410,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "25130,25216,25297,25381,25450,25515,25598,25704,25790,25910,25964,26033,26094,26163,26252,26347,26421,26518,26611,26709,26858,26949,27037,27133,27231,27295,27363,27450,27544,27611,27683,27755,27856,27965,28041,28110,28158,28224,28288,28345,28402,28474,28524,28578,28649,28720,28790,28859,28917,28993,29064,29138,29224,29274,29344,29409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bca7edd7dea9a9293306bd31b1a5bbbe\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,201,202,203,204,205,206,207,208,209,225,226,227,228,229,230,231,232,268,269,270,271,276,282,283,285,302,308,309,310,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,473,486,487,488,489,490,491,499,500,504,508,512,517,523,530,534,538,543,547,551,555,559,563,567,573,577,583,587,593,597,602,606,609,613,619,623,629,633,639,642,646,650,654,658,662,663,664,665,668,671,674,677,681,682,683,684,685,688,690,692,694,699,700,704,710,714,715,717,728,729,733,739,743,744,745,749,776,780,781,785,813,983,1009,1180,1206,1237,1245,1251,1265,1287,1292,1297,1307,1316,1325,1329,1336,1344,1351,1352,1361,1364,1367,1371,1375,1379,1382,1383,1388,1393,1403,1408,1415,1421,1422,1425,1429,1434,1436,1438,1441,1444,1446,1450,1453,1460,1463,1466,1470,1472,1476,1478,1480,1482,1486,1494,1502,1514,1520,1529,1532,1543,1546,1547,1552,1553,1568,1637,1707,1708,1718,1727,1728,1730,1734,1737,1740,1743,1746,1749,1752,1755,1759,1762,1765,1768,1772,1775,1779,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1805,1807,1808,1809,1810,1811,1812,1813,1814,1816,1817,1819,1820,1822,1824,1825,1827,1828,1829,1830,1831,1832,1834,1835,1836,1837,1838,1850,1852,1854,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1870,1871,1872,1873,1874,1875,1877,1881,1909,1910,1911,1912,1913,1914,1918,1919,1920,1921,1923,1925,1927,1929,1931,1932,1933,1934,1936,1938,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1954,1955,1956,1957,1959,1961,1962,1964,1965,1967,1969,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1982,1984,1985,1986,1987,1989,1990,1991,1992,1993,1995,1997,1999,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,1962,2039,2117,2223,2329,2408,2488,2545,2734,2808,2883,2948,3014,3074,3135,3207,3280,3347,3415,3474,3533,3592,3651,3710,3764,3818,3871,3925,3979,4033,5961,6035,6114,6187,6261,6332,6404,6476,6549,6606,6664,6737,6811,6885,6960,7032,7105,7175,7246,7306,7367,7436,7505,7575,7649,7725,7789,7866,7942,8019,8084,8153,8230,8305,8374,8442,8519,8585,8646,8743,8808,8877,8976,9047,9106,9164,9221,9280,9344,9415,9487,9559,9631,9703,9770,9838,9906,9965,10028,10092,10182,10273,10333,10399,10466,10532,10602,10666,10719,10786,10847,10914,11027,11085,11148,11213,11278,11353,11426,11498,11547,11608,11669,11730,11792,11856,11920,11984,12049,12112,12172,12233,12299,12358,12418,12480,12551,12611,13167,13253,13340,13430,13517,13605,13687,13770,13860,14929,14981,15039,15084,15150,15214,15271,15328,17505,17562,17610,17659,17948,18228,18275,18366,19271,19563,19627,19689,19749,19876,19950,20020,20098,20152,20222,20307,20355,20401,20462,20525,20591,20655,20726,20789,20854,20918,20979,21040,21092,21165,21239,21308,21383,21457,21531,21672,30603,31329,31407,31497,31585,31681,31771,32353,32442,32689,32970,33222,33507,33900,34377,34599,34821,35097,35324,35554,35784,36014,36244,36471,36890,37116,37541,37771,38199,38418,38701,38909,39040,39267,39693,39918,40345,40566,40991,41111,41387,41688,42012,42303,42617,42754,42885,42990,43232,43399,43603,43811,44082,44194,44306,44411,44528,44742,44888,45028,45114,45462,45550,45796,46214,46463,46545,46643,47235,47335,47587,48011,48266,48360,48449,48686,50710,50952,51054,51307,53463,63995,65511,76142,77670,79427,80053,80473,81534,82799,83055,83291,83838,84332,84937,85135,85715,86279,86654,86772,87310,87467,87663,87936,88192,88362,88503,88567,88932,89299,89975,90239,90577,90930,91024,91210,91516,91778,91903,92030,92269,92480,92599,92792,92969,93424,93605,93727,93986,94099,94286,94388,94495,94624,94899,95407,95903,96780,97074,97644,97793,98525,98697,98781,99117,99209,99905,105151,110540,110602,111180,111764,111855,111968,112197,112357,112509,112680,112846,113015,113182,113345,113588,113758,113931,114102,114376,114575,114780,115110,115194,115290,115386,115484,115584,115686,115788,115890,115992,116094,116194,116290,116402,116531,116654,116785,116916,117014,117128,117222,117362,117496,117592,117704,117804,117920,118016,118128,118228,118368,118504,118668,118798,118956,119106,119247,119391,119526,119638,119788,119916,120044,120180,120312,120442,120572,120684,121582,121728,121872,122010,122076,122166,122242,122346,122436,122538,122646,122754,122854,122934,123026,123124,123234,123312,123418,123510,123614,123724,123846,124009,125076,125156,125256,125346,125456,125546,125787,125881,125987,126079,126179,126291,126405,126521,126637,126731,126845,126957,127059,127179,127301,127383,127487,127607,127733,127831,127925,128013,128125,128241,128363,128475,128650,128766,128852,128944,129056,129180,129247,129373,129441,129569,129713,129841,129910,130005,130120,130233,130332,130441,130552,130663,130764,130869,130969,131099,131190,131313,131407,131519,131605,131709,131805,131893,132011,132115,132219,132345,132433,132541,132641,132731,132841,132925,133027,133111,133165,133229,133335,133421,133531,133615", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,201,202,203,204,205,206,207,208,209,225,226,227,228,229,230,231,232,268,269,270,271,276,282,283,285,302,308,309,310,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,473,486,487,488,489,490,498,499,503,507,511,516,522,529,533,537,542,546,550,554,558,562,566,572,576,582,586,592,596,601,605,608,612,618,622,628,632,638,641,645,649,653,657,661,662,663,664,667,670,673,676,680,681,682,683,684,687,689,691,693,698,699,703,709,713,714,716,727,728,732,738,742,743,744,748,775,779,780,784,812,982,1008,1179,1205,1236,1244,1250,1264,1286,1291,1296,1306,1315,1324,1328,1335,1343,1350,1351,1360,1363,1366,1370,1374,1378,1381,1382,1387,1392,1402,1407,1414,1420,1421,1424,1428,1433,1435,1437,1440,1443,1445,1449,1452,1459,1462,1465,1469,1471,1475,1477,1479,1481,1485,1493,1501,1513,1519,1528,1531,1542,1545,1546,1551,1552,1557,1636,1706,1707,1717,1726,1727,1729,1733,1736,1739,1742,1745,1748,1751,1754,1758,1761,1764,1767,1771,1774,1778,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1804,1806,1807,1808,1809,1810,1811,1812,1813,1815,1816,1818,1819,1821,1823,1824,1826,1827,1828,1829,1830,1831,1833,1834,1835,1836,1837,1838,1851,1853,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1869,1870,1871,1872,1873,1874,1876,1880,1884,1909,1910,1911,1912,1913,1917,1918,1919,1920,1922,1924,1926,1928,1930,1931,1932,1933,1935,1937,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1953,1954,1955,1956,1958,1960,1961,1963,1964,1966,1968,1970,1971,1972,1973,1974,1975,1976,1977,1978,1979,1980,1981,1983,1984,1985,1986,1988,1989,1990,1991,1992,1994,1996,1998,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2014,2015", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2034,2112,2218,2324,2403,2483,2540,2598,2803,2878,2943,3009,3069,3130,3202,3275,3342,3410,3469,3528,3587,3646,3705,3759,3813,3866,3920,3974,4028,4082,6030,6109,6182,6256,6327,6399,6471,6544,6601,6659,6732,6806,6880,6955,7027,7100,7170,7241,7301,7362,7431,7500,7570,7644,7720,7784,7861,7937,8014,8079,8148,8225,8300,8369,8437,8514,8580,8641,8738,8803,8872,8971,9042,9101,9159,9216,9275,9339,9410,9482,9554,9626,9698,9765,9833,9901,9960,10023,10087,10177,10268,10328,10394,10461,10527,10597,10661,10714,10781,10842,10909,11022,11080,11143,11208,11273,11348,11421,11493,11542,11603,11664,11725,11787,11851,11915,11979,12044,12107,12167,12228,12294,12353,12413,12475,12546,12606,12674,13248,13335,13425,13512,13600,13682,13765,13855,13946,14976,15034,15079,15145,15209,15266,15323,15377,17557,17605,17654,17705,17977,18270,18319,18407,19298,19622,19684,19744,19801,19945,20015,20093,20147,20217,20302,20350,20396,20457,20520,20586,20650,20721,20784,20849,20913,20974,21035,21087,21160,21234,21303,21378,21452,21526,21667,21737,30651,31402,31492,31580,31676,31766,32348,32437,32684,32965,33217,33502,33895,34372,34594,34816,35092,35319,35549,35779,36009,36239,36466,36885,37111,37536,37766,38194,38413,38696,38904,39035,39262,39688,39913,40340,40561,40986,41106,41382,41683,42007,42298,42612,42749,42880,42985,43227,43394,43598,43806,44077,44189,44301,44406,44523,44737,44883,45023,45109,45457,45545,45791,46209,46458,46540,46638,47230,47330,47582,48006,48261,48355,48444,48681,50705,50947,51049,51302,53458,63990,65506,76137,77665,79422,80048,80468,81529,82794,83050,83286,83833,84327,84932,85130,85710,86274,86649,86767,87305,87462,87658,87931,88187,88357,88498,88562,88927,89294,89970,90234,90572,90925,91019,91205,91511,91773,91898,92025,92264,92475,92594,92787,92964,93419,93600,93722,93981,94094,94281,94383,94490,94619,94894,95402,95898,96775,97069,97639,97788,98520,98692,98776,99112,99204,99482,105146,110535,110597,111175,111759,111850,111963,112192,112352,112504,112675,112841,113010,113177,113340,113583,113753,113926,114097,114371,114570,114775,115105,115189,115285,115381,115479,115579,115681,115783,115885,115987,116089,116189,116285,116397,116526,116649,116780,116911,117009,117123,117217,117357,117491,117587,117699,117799,117915,118011,118123,118223,118363,118499,118663,118793,118951,119101,119242,119386,119521,119633,119783,119911,120039,120175,120307,120437,120567,120679,120819,121723,121867,122005,122071,122161,122237,122341,122431,122533,122641,122749,122849,122929,123021,123119,123229,123307,123413,123505,123609,123719,123841,124004,124161,125151,125251,125341,125451,125541,125782,125876,125982,126074,126174,126286,126400,126516,126632,126726,126840,126952,127054,127174,127296,127378,127482,127602,127728,127826,127920,128008,128120,128236,128358,128470,128645,128761,128847,128939,129051,129175,129242,129368,129436,129564,129708,129836,129905,130000,130115,130228,130327,130436,130547,130658,130759,130864,130964,131094,131185,131308,131402,131514,131600,131704,131800,131888,132006,132110,132214,132340,132428,132536,132636,132726,132836,132920,133022,133106,133160,133224,133330,133416,133526,133610,133730"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8cb0f06ef6b767d925a284333d566c08\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "19460", "endColumns": "49", "endOffsets": "19505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0cdd27c7332ff07aa654c6c65661598f\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "303", "startColumns": "4", "startOffsets": "19303", "endColumns": "42", "endOffsets": "19341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\39850584ec2c63dfe3ab909b2301a2f7\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "305", "startColumns": "4", "startOffsets": "19406", "endColumns": "53", "endOffsets": "19455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5b68b955b795828701719ac184f2bfb\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,28,29,42,43,94,95,194,195,196,197,198,199,200,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,233,234,235,279,280,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,312,352,353,354,355,356,357,358,480,1839,1840,1844,1845,1849,2016,2017", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2603,2668,5829,5898,12679,12749,12817,12889,12959,13020,13094,13951,14012,14073,14135,14199,14261,14322,14390,14490,14550,14616,14689,14758,14815,14867,15382,15454,15530,18091,18126,18412,18467,18530,18585,18643,18701,18762,18825,18882,18933,18983,19044,19101,19167,19201,19236,19806,22425,22492,22564,22633,22702,22776,22848,30974,120824,120941,121142,121252,121453,133735,133807", "endLines": "9,28,29,42,43,94,95,194,195,196,197,198,199,200,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,233,234,235,279,280,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,312,352,353,354,355,356,357,358,480,1839,1843,1844,1848,1849,2016,2017", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "506,1550,1638,2663,2729,5893,5956,12744,12812,12884,12954,13015,13089,13162,14007,14068,14130,14194,14256,14317,14385,14485,14545,14611,14684,14753,14810,14862,14924,15449,15525,15590,18121,18156,18462,18525,18580,18638,18696,18757,18820,18877,18928,18978,19039,19096,19162,19196,19231,19266,19871,22487,22559,22628,22697,22771,22843,22931,31040,120936,121137,121247,121448,121577,133802,133869"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b46d61e5c41c267a16ee81b65178c624\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "278,281", "startColumns": "4,4", "startOffsets": "18037,18161", "endColumns": "53,66", "endOffsets": "18086,18223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ead5545371d24235bc023f33a0e98494\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "484,485", "startColumns": "4,4", "startOffsets": "31218,31274", "endColumns": "55,54", "endOffsets": "31269,31324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\460cd7bf85acb2de05de5e63bf256cd0\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "4,10", "startColumns": "4,4", "startOffsets": "250,511", "endLines": "7,17", "endColumns": "11,11", "endOffsets": "397,813"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\app with custom ai\\ChessVisionApp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "340,341,342,343,344,346,347,348,349,350,351,359,360,361,362,363,364,365,366,369,370,371,374,376,377,378,379,380,381,382,383,384,385,388,450,451,454,456,457,458,459,460,461,462,463,464,465,468,469,470,471,472,475,476,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21742,21782,21834,21912,21974,22126,22179,22235,22273,22322,22373,22936,23112,23283,23365,23407,23451,23549,23617,23794,23840,23886,24104,24204,24264,24304,24407,24487,24565,24630,24701,24757,24833,25000,29476,29522,29672,29740,29788,29832,29880,29924,29970,30014,30066,30116,30180,30373,30427,30467,30505,30553,30702,30754,30794,30843,30899", "endColumns": "39,51,77,61,68,52,55,37,48,50,51,175,170,81,41,43,97,67,57,45,45,50,41,59,39,102,79,77,64,70,55,75,54,46,45,59,33,47,43,47,43,45,43,51,49,63,88,53,39,37,47,49,51,39,48,55,74", "endOffsets": "21777,21829,21907,21969,22038,22174,22230,22268,22317,22368,22420,23107,23278,23360,23402,23446,23544,23612,23670,23835,23881,23932,24141,24259,24299,24402,24482,24560,24625,24696,24752,24828,24883,25042,29517,29577,29701,29783,29827,29875,29919,29965,30009,30061,30111,30175,30264,30422,30462,30500,30548,30598,30749,30789,30838,30894,30969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\61baf198e7b4f8aa28c1bc6e34bcfaed\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "345", "startColumns": "4", "startOffsets": "22043", "endColumns": "82", "endOffsets": "22121"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\app with custom ai\\ChessVisionApp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4087,4147,4202,4266,4332,4396,4460,4522,4579,4645,4704,4772,4833,4903,4962,5028,5088,5157,5214,5278,5335,5401,5456,5515,5583,5640,5704,5762", "endColumns": "59,54,63,65,63,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,54,58,67,56,63,57,66", "endOffsets": "4142,4197,4261,4327,4391,4455,4517,4574,4640,4699,4767,4828,4898,4957,5023,5083,5152,5209,5273,5330,5396,5451,5510,5578,5635,5699,5757,5824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b68849d71562fcbe2f5dc61ad8364bd7\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "284,304", "startColumns": "4,4", "startOffsets": "18324,19346", "endColumns": "41,59", "endOffsets": "18361,19401"}}]}, {"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-mergeDebugResources-51:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\app with custom ai\\ChessVisionApp\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "9", "endColumns": "12", "endOffsets": "419"}, "to": {"startLines": "1885", "startColumns": "4", "startOffsets": "124166", "endLines": "1892", "endColumns": "12", "endOffsets": "124440"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4713635adcc2bac18369e016a04ea54e\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,272,273,274,275,277,307,367,368,372,373,375,386,387,449,452,453,455,466,467,474,481,482,483,1558,1561,1564", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15595,15654,15713,15773,15833,15893,15953,16013,16073,16133,16193,16253,16313,16372,16432,16492,16552,16612,16672,16732,16792,16852,16912,16972,17031,17091,17151,17210,17269,17328,17387,17446,17710,17784,17842,17897,17982,19510,23675,23740,23937,24003,24146,24888,24940,29414,29582,29636,29706,30269,30319,30656,31045,31092,31128,99487,99599,99710", "endLines": "236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,272,273,274,275,277,307,367,368,372,373,375,386,387,449,452,453,455,466,467,474,481,482,483,1560,1563,1567", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "15649,15708,15768,15828,15888,15948,16008,16068,16128,16188,16248,16308,16367,16427,16487,16547,16607,16667,16727,16787,16847,16907,16967,17026,17086,17146,17205,17264,17323,17382,17441,17500,17779,17837,17892,17943,18032,19558,23735,23789,23998,24099,24199,24935,24995,29471,29631,29667,29735,30314,30368,30697,31087,31123,31213,99594,99705,99900"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2559031d7e9fe48b3590293cf5c6448f\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,143,229,310,394,463,528,611,717,803,923,977,1046,1107,1176,1265,1360,1434,1531,1624,1722,1871,1962,2050,2146,2244,2308,2376,2463,2557,2624,2696,2768,2869,2978,3054,3123,3171,3237,3301,3358,3415,3487,3537,3591,3662,3733,3803,3872,3930,4006,4077,4151,4237,4287,4357", "endLines": "2,3,4,5,6,7,8,9,10,13,14,15,16,17,18,19,20,21,22,23,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "138,224,305,389,458,523,606,712,798,918,972,1041,1102,1171,1260,1355,1429,1526,1619,1717,1866,1957,2045,2141,2239,2303,2371,2458,2552,2619,2691,2763,2864,2973,3049,3118,3166,3232,3296,3353,3410,3482,3532,3586,3657,3728,3798,3867,3925,4001,4072,4146,4232,4282,4352,4417"}, "to": {"startLines": "389,390,391,392,393,394,395,396,397,398,401,402,403,404,405,406,407,408,409,410,411,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "25047,25135,25221,25302,25386,25455,25520,25603,25709,25795,25915,25969,26038,26099,26168,26257,26352,26426,26523,26616,26714,26863,26954,27042,27138,27236,27300,27368,27455,27549,27616,27688,27760,27861,27970,28046,28115,28163,28229,28293,28350,28407,28479,28529,28583,28654,28725,28795,28864,28922,28998,29069,29143,29229,29279,29349", "endLines": "389,390,391,392,393,394,395,396,397,400,401,402,403,404,405,406,407,408,409,410,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448", "endColumns": "87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64", "endOffsets": "25130,25216,25297,25381,25450,25515,25598,25704,25790,25910,25964,26033,26094,26163,26252,26347,26421,26518,26611,26709,26858,26949,27037,27133,27231,27295,27363,27450,27544,27611,27683,27755,27856,27965,28041,28110,28158,28224,28288,28345,28402,28474,28524,28578,28649,28720,28790,28859,28917,28993,29064,29138,29224,29274,29344,29409"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bca7edd7dea9a9293306bd31b1a5bbbe\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,201,202,203,204,205,206,207,208,209,225,226,227,228,229,230,231,232,268,269,270,271,276,282,283,285,302,308,309,310,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,473,486,487,488,489,490,491,499,500,504,508,512,517,523,530,534,538,543,547,551,555,559,563,567,573,577,583,587,593,597,602,606,609,613,619,623,629,633,639,642,646,650,654,658,662,663,664,665,668,671,674,677,681,682,683,684,685,688,690,692,694,699,700,704,710,714,715,717,728,729,733,739,743,744,745,749,776,780,781,785,813,983,1009,1180,1206,1237,1245,1251,1265,1287,1292,1297,1307,1316,1325,1329,1336,1344,1351,1352,1361,1364,1367,1371,1375,1379,1382,1383,1388,1393,1403,1408,1415,1421,1422,1425,1429,1434,1436,1438,1441,1444,1446,1450,1453,1460,1463,1466,1470,1472,1476,1478,1480,1482,1486,1494,1502,1514,1520,1529,1532,1543,1546,1547,1552,1553,1568,1637,1707,1708,1718,1727,1728,1730,1734,1737,1740,1743,1746,1749,1752,1755,1759,1762,1765,1768,1772,1775,1779,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1803,1805,1807,1808,1809,1810,1811,1812,1813,1814,1816,1817,1819,1820,1822,1824,1825,1827,1828,1829,1830,1831,1832,1834,1835,1836,1837,1838,1850,1852,1854,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1868,1870,1871,1872,1873,1874,1875,1877,1881,1893,1894,1895,1896,1897,1898,1902,1903,1904,1905,1907,1909,1911,1913,1915,1916,1917,1918,1920,1922,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1938,1939,1940,1941,1943,1945,1946,1948,1949,1951,1953,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1968,1969,1970,1971,1973,1974,1975,1976,1977,1979,1981,1983,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2002,2077,2080,2083,2086,2100,2106,2148,2177,2204,2213,2275,2634,2654,2682,2793,2817,2823,2829,2850,2974,2994,3000,3008,3014,3049,3081,3147,3167,3222,3234,3260", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,1962,2039,2117,2223,2329,2408,2488,2545,2734,2808,2883,2948,3014,3074,3135,3207,3280,3347,3415,3474,3533,3592,3651,3710,3764,3818,3871,3925,3979,4033,5961,6035,6114,6187,6261,6332,6404,6476,6549,6606,6664,6737,6811,6885,6960,7032,7105,7175,7246,7306,7367,7436,7505,7575,7649,7725,7789,7866,7942,8019,8084,8153,8230,8305,8374,8442,8519,8585,8646,8743,8808,8877,8976,9047,9106,9164,9221,9280,9344,9415,9487,9559,9631,9703,9770,9838,9906,9965,10028,10092,10182,10273,10333,10399,10466,10532,10602,10666,10719,10786,10847,10914,11027,11085,11148,11213,11278,11353,11426,11498,11547,11608,11669,11730,11792,11856,11920,11984,12049,12112,12172,12233,12299,12358,12418,12480,12551,12611,13167,13253,13340,13430,13517,13605,13687,13770,13860,14929,14981,15039,15084,15150,15214,15271,15328,17505,17562,17610,17659,17948,18228,18275,18366,19271,19563,19627,19689,19749,19876,19950,20020,20098,20152,20222,20307,20355,20401,20462,20525,20591,20655,20726,20789,20854,20918,20979,21040,21092,21165,21239,21308,21383,21457,21531,21672,30603,31329,31407,31497,31585,31681,31771,32353,32442,32689,32970,33222,33507,33900,34377,34599,34821,35097,35324,35554,35784,36014,36244,36471,36890,37116,37541,37771,38199,38418,38701,38909,39040,39267,39693,39918,40345,40566,40991,41111,41387,41688,42012,42303,42617,42754,42885,42990,43232,43399,43603,43811,44082,44194,44306,44411,44528,44742,44888,45028,45114,45462,45550,45796,46214,46463,46545,46643,47235,47335,47587,48011,48266,48360,48449,48686,50710,50952,51054,51307,53463,63995,65511,76142,77670,79427,80053,80473,81534,82799,83055,83291,83838,84332,84937,85135,85715,86279,86654,86772,87310,87467,87663,87936,88192,88362,88503,88567,88932,89299,89975,90239,90577,90930,91024,91210,91516,91778,91903,92030,92269,92480,92599,92792,92969,93424,93605,93727,93986,94099,94286,94388,94495,94624,94899,95407,95903,96780,97074,97644,97793,98525,98697,98781,99117,99209,99905,105151,110540,110602,111180,111764,111855,111968,112197,112357,112509,112680,112846,113015,113182,113345,113588,113758,113931,114102,114376,114575,114780,115110,115194,115290,115386,115484,115584,115686,115788,115890,115992,116094,116194,116290,116402,116531,116654,116785,116916,117014,117128,117222,117362,117496,117592,117704,117804,117920,118016,118128,118228,118368,118504,118668,118798,118956,119106,119247,119391,119526,119638,119788,119916,120044,120180,120312,120442,120572,120684,121582,121728,121872,122010,122076,122166,122242,122346,122436,122538,122646,122754,122854,122934,123026,123124,123234,123312,123418,123510,123614,123724,123846,124009,124445,124525,124625,124715,124825,124915,125156,125250,125356,125448,125548,125660,125774,125890,126006,126100,126214,126326,126428,126548,126670,126752,126856,126976,127102,127200,127294,127382,127494,127610,127732,127844,128019,128135,128221,128313,128425,128549,128616,128742,128810,128938,129082,129210,129279,129374,129489,129602,129701,129810,129921,130032,130133,130238,130338,130468,130559,130682,130776,130888,130974,131078,131174,131262,131380,131484,131588,131714,131802,131910,132010,132100,132210,132294,132396,132480,132534,132598,132704,132790,132900,132984,133243,135859,135977,136092,136172,136533,136766,138170,139514,140875,141263,144038,153942,154581,155938,159771,160522,160784,160984,161363,165641,166247,166476,166770,166985,168068,168918,171944,172688,174819,175159,176470", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,34,35,36,37,38,39,40,41,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,201,202,203,204,205,206,207,208,209,225,226,227,228,229,230,231,232,268,269,270,271,276,282,283,285,302,308,309,310,311,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,473,486,487,488,489,490,498,499,503,507,511,516,522,529,533,537,542,546,550,554,558,562,566,572,576,582,586,592,596,601,605,608,612,618,622,628,632,638,641,645,649,653,657,661,662,663,664,667,670,673,676,680,681,682,683,684,687,689,691,693,698,699,703,709,713,714,716,727,728,732,738,742,743,744,748,775,779,780,784,812,982,1008,1179,1205,1236,1244,1250,1264,1286,1291,1296,1306,1315,1324,1328,1335,1343,1350,1351,1360,1363,1366,1370,1374,1378,1381,1382,1387,1392,1402,1407,1414,1420,1421,1424,1428,1433,1435,1437,1440,1443,1445,1449,1452,1459,1462,1465,1469,1471,1475,1477,1479,1481,1485,1493,1501,1513,1519,1528,1531,1542,1545,1546,1551,1552,1557,1636,1706,1707,1717,1726,1727,1729,1733,1736,1739,1742,1745,1748,1751,1754,1758,1761,1764,1767,1771,1774,1778,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1795,1796,1797,1798,1799,1800,1801,1802,1804,1806,1807,1808,1809,1810,1811,1812,1813,1815,1816,1818,1819,1821,1823,1824,1826,1827,1828,1829,1830,1831,1833,1834,1835,1836,1837,1838,1851,1853,1855,1856,1857,1858,1859,1860,1861,1862,1863,1864,1865,1866,1867,1869,1870,1871,1872,1873,1874,1876,1880,1884,1893,1894,1895,1896,1897,1901,1902,1903,1904,1906,1908,1910,1912,1914,1915,1916,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1937,1938,1939,1940,1942,1944,1945,1947,1948,1950,1952,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1967,1968,1969,1970,1972,1973,1974,1975,1976,1978,1980,1982,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2076,2079,2082,2085,2099,2105,2115,2176,2203,2212,2274,2633,2637,2681,2699,2816,2822,2828,2849,2973,2993,2999,3003,3013,3048,3060,3146,3166,3221,3233,3259,3266", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2034,2112,2218,2324,2403,2483,2540,2598,2803,2878,2943,3009,3069,3130,3202,3275,3342,3410,3469,3528,3587,3646,3705,3759,3813,3866,3920,3974,4028,4082,6030,6109,6182,6256,6327,6399,6471,6544,6601,6659,6732,6806,6880,6955,7027,7100,7170,7241,7301,7362,7431,7500,7570,7644,7720,7784,7861,7937,8014,8079,8148,8225,8300,8369,8437,8514,8580,8641,8738,8803,8872,8971,9042,9101,9159,9216,9275,9339,9410,9482,9554,9626,9698,9765,9833,9901,9960,10023,10087,10177,10268,10328,10394,10461,10527,10597,10661,10714,10781,10842,10909,11022,11080,11143,11208,11273,11348,11421,11493,11542,11603,11664,11725,11787,11851,11915,11979,12044,12107,12167,12228,12294,12353,12413,12475,12546,12606,12674,13248,13335,13425,13512,13600,13682,13765,13855,13946,14976,15034,15079,15145,15209,15266,15323,15377,17557,17605,17654,17705,17977,18270,18319,18407,19298,19622,19684,19744,19801,19945,20015,20093,20147,20217,20302,20350,20396,20457,20520,20586,20650,20721,20784,20849,20913,20974,21035,21087,21160,21234,21303,21378,21452,21526,21667,21737,30651,31402,31492,31580,31676,31766,32348,32437,32684,32965,33217,33502,33895,34372,34594,34816,35092,35319,35549,35779,36009,36239,36466,36885,37111,37536,37766,38194,38413,38696,38904,39035,39262,39688,39913,40340,40561,40986,41106,41382,41683,42007,42298,42612,42749,42880,42985,43227,43394,43598,43806,44077,44189,44301,44406,44523,44737,44883,45023,45109,45457,45545,45791,46209,46458,46540,46638,47230,47330,47582,48006,48261,48355,48444,48681,50705,50947,51049,51302,53458,63990,65506,76137,77665,79422,80048,80468,81529,82794,83050,83286,83833,84327,84932,85130,85710,86274,86649,86767,87305,87462,87658,87931,88187,88357,88498,88562,88927,89294,89970,90234,90572,90925,91019,91205,91511,91773,91898,92025,92264,92475,92594,92787,92964,93419,93600,93722,93981,94094,94281,94383,94490,94619,94894,95402,95898,96775,97069,97639,97788,98520,98692,98776,99112,99204,99482,105146,110535,110597,111175,111759,111850,111963,112192,112352,112504,112675,112841,113010,113177,113340,113583,113753,113926,114097,114371,114570,114775,115105,115189,115285,115381,115479,115579,115681,115783,115885,115987,116089,116189,116285,116397,116526,116649,116780,116911,117009,117123,117217,117357,117491,117587,117699,117799,117915,118011,118123,118223,118363,118499,118663,118793,118951,119101,119242,119386,119521,119633,119783,119911,120039,120175,120307,120437,120567,120679,120819,121723,121867,122005,122071,122161,122237,122341,122431,122533,122641,122749,122849,122929,123021,123119,123229,123307,123413,123505,123609,123719,123841,124004,124161,124520,124620,124710,124820,124910,125151,125245,125351,125443,125543,125655,125769,125885,126001,126095,126209,126321,126423,126543,126665,126747,126851,126971,127097,127195,127289,127377,127489,127605,127727,127839,128014,128130,128216,128308,128420,128544,128611,128737,128805,128933,129077,129205,129274,129369,129484,129597,129696,129805,129916,130027,130128,130233,130333,130463,130554,130677,130771,130883,130969,131073,131169,131257,131375,131479,131583,131709,131797,131905,132005,132095,132205,132289,132391,132475,132529,132593,132699,132785,132895,132979,133099,135854,135972,136087,136167,136528,136761,137278,139509,140870,141258,144033,153937,154072,155933,156505,160517,160779,160979,161358,165636,166242,166471,166622,166980,168063,168375,171939,172683,174814,175154,176465,176668"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8cb0f06ef6b767d925a284333d566c08\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "306", "startColumns": "4", "startOffsets": "19460", "endColumns": "49", "endOffsets": "19505"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0cdd27c7332ff07aa654c6c65661598f\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "303", "startColumns": "4", "startOffsets": "19303", "endColumns": "42", "endOffsets": "19341"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\9f9e2408b4519dc2b043569ff95761de\\transformed\\appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2116,2132,2138,3061,3077", "startColumns": "4,4,4,4,4", "startOffsets": "137283,137708,137886,168380,168791", "endLines": "2131,2137,2147,3076,3080", "endColumns": "24,24,24,24,24", "endOffsets": "137703,137881,138165,168786,168913"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\39850584ec2c63dfe3ab909b2301a2f7\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "305", "startColumns": "4", "startOffsets": "19406", "endColumns": "53", "endOffsets": "19455"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5b68b955b795828701719ac184f2bfb\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,42,43,94,95,194,195,196,197,198,199,200,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,233,234,235,279,280,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,312,352,353,354,355,356,357,358,480,1839,1840,1844,1845,1849,2000,2001,2638,2644,2700,2733,2754,2787", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2603,2668,5829,5898,12679,12749,12817,12889,12959,13020,13094,13951,14012,14073,14135,14199,14261,14322,14390,14490,14550,14616,14689,14758,14815,14867,15382,15454,15530,18091,18126,18412,18467,18530,18585,18643,18701,18762,18825,18882,18933,18983,19044,19101,19167,19201,19236,19806,22425,22492,22564,22633,22702,22776,22848,30974,120824,120941,121142,121252,121453,133104,133176,154077,154280,156510,158241,158922,159604", "endLines": "9,28,29,42,43,94,95,194,195,196,197,198,199,200,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,233,234,235,279,280,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,312,352,353,354,355,356,357,358,480,1839,1843,1844,1848,1849,2000,2001,2643,2653,2732,2753,2786,2792", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1550,1638,2663,2729,5893,5956,12744,12812,12884,12954,13015,13089,13162,14007,14068,14130,14194,14256,14317,14385,14485,14545,14611,14684,14753,14810,14862,14924,15449,15525,15590,18121,18156,18462,18525,18580,18638,18696,18757,18820,18877,18928,18978,19039,19096,19162,19196,19231,19266,19871,22487,22559,22628,22697,22771,22843,22931,31040,120936,121137,121247,121448,121577,133171,133238,154275,154576,158236,158917,159599,159766"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b46d61e5c41c267a16ee81b65178c624\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "278,281", "startColumns": "4,4", "startOffsets": "18037,18161", "endColumns": "53,66", "endOffsets": "18086,18223"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ead5545371d24235bc023f33a0e98494\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "484,485", "startColumns": "4,4", "startOffsets": "31218,31274", "endColumns": "55,54", "endOffsets": "31269,31324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\460cd7bf85acb2de05de5e63bf256cd0\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3004", "startColumns": "4,4,4", "startOffsets": "250,511,166627", "endLines": "7,17,3007", "endColumns": "11,11,24", "endOffsets": "397,813,166765"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\app with custom ai\\ChessVisionApp\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "46,35,21,27,20,2,44,49,30,34,42,17,16,15,50,22,7,6,25,76,75,36,53,41,56,60,61,62,63,64,29,18,5,55,9,11,51,70,72,69,67,71,68,43,26,33,19,10,57,52,28,8,40,54,37,45,12", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2484,1970,1339,1606,1270,55,2372,2549,1772,1919,2268,929,758,676,2587,1417,261,193,1498,3716,3670,2022,2701,2208,2830,2943,3046,3126,3204,3269,1716,1105,138,2783,409,509,2629,3507,3601,3459,3371,3555,3415,2320,1556,1855,1181,455,2870,2663,1668,359,2156,2743,2073,2428,569", "endColumns": "39,51,77,61,68,52,55,37,48,50,51,175,170,81,41,43,97,67,57,45,45,50,41,59,39,102,79,77,64,70,55,75,54,46,45,59,33,47,43,47,43,45,43,51,49,63,88,53,39,37,47,49,51,39,48,55,74", "endOffsets": "2519,2017,1412,1663,1334,103,2423,2582,1816,1965,2315,1100,924,753,2624,1456,354,256,1551,3757,3711,2068,2738,2263,2865,3041,3121,3199,3264,3335,1767,1176,188,2825,450,564,2658,3550,3640,3502,3410,3596,3454,2367,1601,1914,1265,504,2905,2696,1711,404,2203,2778,2117,2479,639"}, "to": {"startLines": "340,341,342,343,344,346,347,348,349,350,351,359,360,361,362,363,364,365,366,369,370,371,374,376,377,378,379,380,381,382,383,384,385,388,450,451,454,456,457,458,459,460,461,462,463,464,465,468,469,470,471,472,475,476,477,478,479", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "21742,21782,21834,21912,21974,22126,22179,22235,22273,22322,22373,22936,23112,23283,23365,23407,23451,23549,23617,23794,23840,23886,24104,24204,24264,24304,24407,24487,24565,24630,24701,24757,24833,25000,29476,29522,29672,29740,29788,29832,29880,29924,29970,30014,30066,30116,30180,30373,30427,30467,30505,30553,30702,30754,30794,30843,30899", "endColumns": "39,51,77,61,68,52,55,37,48,50,51,175,170,81,41,43,97,67,57,45,45,50,41,59,39,102,79,77,64,70,55,75,54,46,45,59,33,47,43,47,43,45,43,51,49,63,88,53,39,37,47,49,51,39,48,55,74", "endOffsets": "21777,21829,21907,21969,22038,22174,22230,22268,22317,22368,22420,23107,23278,23360,23402,23446,23544,23612,23670,23835,23881,23932,24141,24259,24299,24402,24482,24560,24625,24696,24752,24828,24883,25042,29517,29577,29701,29783,29827,29875,29919,29965,30009,30061,30111,30175,30264,30422,30462,30500,30548,30598,30749,30789,30838,30894,30969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\61baf198e7b4f8aa28c1bc6e34bcfaed\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "345", "startColumns": "4", "startOffsets": "22043", "endColumns": "82", "endOffsets": "22121"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\app with custom ai\\ChessVisionApp\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "23,18,20,35,36,34,24,19,21,4,6,9,11,26,29,14,16,30,31,3,5,33,8,10,25,28,13,15", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1123,876,988,1741,1807,1677,1183,931,1052,156,281,413,542,1302,1430,675,802,1496,1553,99,215,1622,354,474,1245,1366,617,735", "endColumns": "59,54,63,65,63,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,54,58,67,56,63,57,66", "endOffsets": "1178,926,1047,1802,1866,1736,1240,983,1113,210,344,469,607,1356,1491,730,866,1548,1612,151,276,1672,408,537,1297,1425,670,797"}, "to": {"startLines": "66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4087,4147,4202,4266,4332,4396,4460,4522,4579,4645,4704,4772,4833,4903,4962,5028,5088,5157,5214,5278,5335,5401,5456,5515,5583,5640,5704,5762", "endColumns": "59,54,63,65,63,63,61,56,65,58,67,60,69,58,65,59,68,56,63,56,65,54,58,67,56,63,57,66", "endOffsets": "4142,4197,4261,4327,4391,4455,4517,4574,4640,4699,4767,4828,4898,4957,5023,5083,5152,5209,5273,5330,5396,5451,5510,5578,5635,5699,5757,5824"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\b68849d71562fcbe2f5dc61ad8364bd7\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "284,304", "startColumns": "4,4", "startOffsets": "18324,19346", "endColumns": "41,59", "endOffsets": "18361,19401"}}]}]}