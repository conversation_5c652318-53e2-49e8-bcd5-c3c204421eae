"""
Inference script for enhanced chess board detection model.
This script uses the enhanced U-Net model for improved corner detection.
"""

import os
import argparse
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter
from scipy.ndimage.measurements import maximum_position
import albumentations as A
from albumentations.pytorch import ToTensorV2

from models.enhanced_unet import EnhancedChessBoardUNet
from config import MODELS_DIR, INPUT_SIZE, DEVICE


def preprocess_image(image_path, input_size=INPUT_SIZE):
    """
    Preprocess an image for inference.

    Args:
        image_path (str): Path to the image.
        input_size (tuple): Target size for the image.

    Returns:
        tensor: Preprocessed image tensor.
        original: Original image.
    """
    # Read image
    original = cv2.imread(image_path)
    original = cv2.cvtColor(original, cv2.COLOR_BGR2RGB)

    # Define transforms
    transform = A.Compose([
        A.Resize(height=input_size[0], width=input_size[1]),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])

    # Apply transforms
    transformed = transform(image=original)
    tensor = transformed['image'].unsqueeze(0)

    return tensor, original


def find_corners(heatmaps, threshold=0.3, gaussian_sigma=1.0):
    """
    Find corners from heatmaps with improved detection.

    Args:
        heatmaps (numpy.ndarray): Corner heatmaps (4, H, W).
        threshold (float): Confidence threshold.
        gaussian_sigma (float): Sigma for Gaussian filtering.

    Returns:
        corners: List of corner coordinates (x, y).
        confidences: List of corner confidences.
    """
    corners = []
    confidences = []

    for i in range(4):
        heatmap = heatmaps[i]

        # Apply Gaussian filter to smooth the heatmap
        smoothed = gaussian_filter(heatmap, sigma=gaussian_sigma)

        # Find the maximum value and its position
        max_value = np.max(smoothed)
        y, x = np.unravel_index(np.argmax(smoothed), smoothed.shape)

        # Check if the confidence is above the threshold
        if max_value > threshold:
            corners.append((x, y))
            confidences.append(max_value)
        else:
            # If below threshold, still add the best guess but mark it
            corners.append((x, y))
            confidences.append(max_value)
            print(f"Warning: Corner {i} has low confidence: {max_value:.4f}")

    return corners, confidences


def order_corners(corners):
    """
    Order corners as [top-left, top-right, bottom-right, bottom-left].

    Args:
        corners (list): List of corner coordinates (x, y).

    Returns:
        ordered_corners: Ordered list of corner coordinates.
    """
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / 4
    center_y = sum(y for x, y in corners) / 4

    # Order corners based on their position relative to the center
    ordered_corners = []
    for i in range(4):
        if corners[i][0] < center_x and corners[i][1] < center_y:
            # Top-left
            ordered_corners.append(corners[i])
            break

    for i in range(4):
        if corners[i][0] > center_x and corners[i][1] < center_y:
            # Top-right
            ordered_corners.append(corners[i])
            break

    for i in range(4):
        if corners[i][0] > center_x and corners[i][1] > center_y:
            # Bottom-right
            ordered_corners.append(corners[i])
            break

    for i in range(4):
        if corners[i][0] < center_x and corners[i][1] > center_y:
            # Bottom-left
            ordered_corners.append(corners[i])
            break

    return ordered_corners


def visualize_results(image, segmentation, heatmaps, corners, confidences, output_path=None):
    """
    Visualize the results of chess board detection.

    Args:
        image (numpy.ndarray): Original image.
        segmentation (numpy.ndarray): Segmentation mask.
        heatmaps (numpy.ndarray): Corner heatmaps.
        corners (list): List of corner coordinates.
        confidences (list): List of corner confidences.
        output_path (str): Path to save the visualization.
    """
    # Create figure
    plt.figure(figsize=(15, 10))

    # Plot original image
    plt.subplot(2, 3, 1)
    plt.imshow(image)
    plt.title("Original Image")
    plt.axis('off')

    # Plot segmentation mask
    plt.subplot(2, 3, 2)
    plt.imshow(segmentation, cmap='gray')
    plt.title("Segmentation Mask")
    plt.axis('off')

    # Plot combined heatmap
    plt.subplot(2, 3, 3)
    combined_heatmap = np.max(heatmaps, axis=0)
    plt.imshow(combined_heatmap, cmap='hot')
    plt.title("Combined Heatmap")
    plt.axis('off')

    # Plot individual corner heatmaps
    corner_names = ["Top-Left Corner", "Top-Right Corner", "Bottom-Right Corner", "Bottom-Left Corner"]
    for i in range(4):
        plt.subplot(2, 4, 5 + i)
        plt.imshow(heatmaps[i], cmap='hot')
        plt.title(f"{corner_names[i]} (Conf: {confidences[i]:.4f})")
        plt.axis('off')

    # Plot image with detected corners
    plt.subplot(2, 3, 4)
    plt.imshow(image)

    # Plot corners
    colors = ['r', 'g', 'b', 'y']
    for i, (x, y) in enumerate(corners):
        plt.plot(x, y, 'o', color=colors[i], markersize=10)
        plt.text(x + 5, y + 5, f"{i+1}", color=colors[i], fontsize=12)

    # Connect corners to form a quadrilateral
    corners_cycle = corners + [corners[0]]
    xs, ys = zip(*corners_cycle)
    plt.plot(xs, ys, 'r-', linewidth=2)

    plt.title("Detected Corners")
    plt.axis('off')

    # Adjust layout
    plt.tight_layout()

    # Save or show
    if output_path:
        plt.savefig(output_path)
        print(f"Visualization saved to {output_path}")
    else:
        plt.show()

    plt.close()


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Inference for enhanced chess board detection')
    parser.add_argument('--image_path', type=str, required=True, help='Path to input image')
    parser.add_argument('--model_path', type=str, default=None,
                        help='Path to model checkpoint (default: best model in enhanced_model directory)')
    parser.add_argument('--output_dir', type=str, default='outputs', help='Output directory')
    parser.add_argument('--threshold', type=float, default=0.05,
                        help='Confidence threshold for corner detection')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    args = parser.parse_args()

    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set model path
    if args.model_path is None:
        model_path = os.path.join(MODELS_DIR, 'improved_corner_detection', 'checkpoints', 'v3', 'best_model.pth')
    else:
        model_path = args.model_path

    # Check if model exists
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"Model not found at {model_path}")

    # Initialize model
    print("Initializing model...")
    model = EnhancedChessBoardUNet(n_channels=3, bilinear=True)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model = model.to(device)
    model.eval()
    print(f"Model loaded from {model_path}")

    # Preprocess image
    print("Preprocessing image...")
    image_tensor, original_image = preprocess_image(args.image_path)
    image_tensor = image_tensor.to(device)

    # Run inference
    print("Running inference...")
    with torch.no_grad():
        outputs = model(image_tensor)

    # Get outputs
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]
    corner_heatmaps = outputs['corner_heatmaps'].cpu().numpy()[0]

    # Find corners
    print("Finding corners...")
    corners, confidences = find_corners(corner_heatmaps, threshold=args.threshold)

    # Order corners
    ordered_corners = order_corners(corners)

    # Print results
    print("Detected corners:")
    corner_names = ["Top-Left", "Top-Right", "Bottom-Right", "Bottom-Left"]
    for i, ((x, y), conf) in enumerate(zip(corners, confidences)):
        print(f"{corner_names[i]}: ({x}, {y}), Confidence: {conf:.4f}")

    # Visualize results
    print("Visualizing results...")
    output_path = os.path.join(args.output_dir, os.path.basename(args.image_path).split('.')[0] + '_detection.png')
    visualize_results(
        original_image,
        segmentation,
        corner_heatmaps,
        corners,
        confidences,
        output_path
    )

    print("Done!")


if __name__ == "__main__":
    main()
