"""
Inference script for the v5 chess board detection model.
This version includes:
1. Improved post-processing with segmentation guidance
2. Enhanced visualization of results
3. Confidence metrics display
"""

import os
import argparse
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn.functional as F
import cv2
from PIL import Image
from torchvision import transforms

from config import DEVICE, INPUT_SIZE
from models.enhanced_unet_v5 import EnhancedChessBoardUNetV5
from post_processing import post_process_model_output


def load_image(image_path, size=INPUT_SIZE):
    """
    Load and preprocess an image.
    
    Args:
        image_path: Path to the image
        size: Size to resize the image to
        
    Returns:
        image: Original image
        image_tensor: Preprocessed image tensor
    """
    # Load image
    image = Image.open(image_path).convert('RGB')
    
    # Resize image
    image = image.resize(size)
    
    # Convert to numpy array
    image_np = np.array(image)
    
    # Preprocess image
    preprocess = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    image_tensor = preprocess(image_np).unsqueeze(0)
    
    return image_np, image_tensor


def predict(model, image_tensor, device, use_post_processing=True, use_segmentation_guidance=True):
    """
    Run inference on an image.
    
    Args:
        model: Model to use for inference
        image_tensor: Input image tensor
        device: Device to run inference on
        use_post_processing: Whether to use post-processing
        use_segmentation_guidance: Whether to use segmentation guidance
        
    Returns:
        outputs: Dictionary of outputs
    """
    # Move image to device
    image_tensor = image_tensor.to(device)
    
    # Run inference
    with torch.no_grad():
        model_outputs = model(image_tensor)
    
    # Get outputs
    segmentation = model_outputs['segmentation'].cpu().numpy()[0, 0]
    heatmaps = model_outputs['corner_heatmaps'].cpu().numpy()[0]
    
    # Post-process outputs
    if use_post_processing:
        corners = post_process_model_output(
            segmentation,
            heatmaps,
            use_segmentation_guidance=use_segmentation_guidance
        )
    else:
        # Simple post-processing (just find the maximum in each heatmap)
        corners = []
        for i in range(4):
            heatmap = heatmaps[i]
            max_idx = np.argmax(heatmap)
            y, x = np.unravel_index(max_idx, heatmap.shape)
            corners.append((x, y))
    
    # Calculate confidence metrics
    confidence_metrics = calculate_confidence_metrics(heatmaps)
    
    return {
        'segmentation': segmentation,
        'heatmaps': heatmaps,
        'corners': corners,
        'confidence_metrics': confidence_metrics
    }


def calculate_confidence_metrics(heatmaps):
    """
    Calculate confidence metrics for the heatmaps.
    
    Args:
        heatmaps: Corner heatmaps (4, H, W)
        
    Returns:
        metrics: Dictionary of confidence metrics
    """
    metrics = {}
    
    # Calculate peak values
    peak_values = []
    peak_to_mean_ratios = []
    peak_to_second_ratios = []
    
    for i in range(4):
        hm = heatmaps[i]
        
        # Find peak value
        peak_value = np.max(hm)
        peak_values.append(peak_value)
        
        # Calculate peak-to-mean ratio
        mean_value = np.mean(hm)
        if mean_value > 0:
            peak_to_mean_ratio = peak_value / mean_value
        else:
            peak_to_mean_ratio = 0
        peak_to_mean_ratios.append(peak_to_mean_ratio)
        
        # Calculate peak-to-second ratio
        hm_flat = hm.flatten()
        sorted_indices = np.argsort(hm_flat)[::-1]
        if len(sorted_indices) > 1:
            second_value = hm_flat[sorted_indices[1]]
            if second_value > 0:
                peak_to_second_ratio = peak_value / second_value
            else:
                peak_to_second_ratio = 0
        else:
            peak_to_second_ratio = 0
        peak_to_second_ratios.append(peak_to_second_ratio)
    
    # Calculate detection rate (proportion of corners with peak value above threshold)
    detection_threshold = 0.5
    detection_rate = sum(1 for v in peak_values if v >= detection_threshold) / 4
    
    # Store metrics
    metrics['avg_peak_value'] = np.mean(peak_values)
    metrics['avg_peak_to_mean_ratio'] = np.mean(peak_to_mean_ratios)
    metrics['avg_peak_to_second_ratio'] = np.mean(peak_to_second_ratios)
    metrics['detection_rate'] = detection_rate
    metrics['overall_confidence'] = np.mean([
        metrics['avg_peak_value'],
        metrics['avg_peak_to_mean_ratio'] / 5,  # Normalize to roughly 0-1 range
        metrics['avg_peak_to_second_ratio'] / 1.5,  # Normalize to roughly 0-1 range
        metrics['detection_rate']
    ])
    
    return metrics


def visualize_results(image, segmentation, heatmaps, corners, confidence_metrics, output_path=None):
    """
    Visualize the results.
    
    Args:
        image: Original image
        segmentation: Segmentation mask
        heatmaps: Corner heatmaps
        corners: Corner coordinates
        confidence_metrics: Confidence metrics
        output_path: Path to save the visualization
    """
    plt.figure(figsize=(15, 12))
    
    # Plot original image
    plt.subplot(2, 3, 1)
    plt.imshow(image)
    plt.title('Original Image')
    plt.axis('off')
    
    # Plot segmentation mask
    plt.subplot(2, 3, 2)
    plt.imshow(segmentation, cmap='gray')
    plt.title('Segmentation Mask')
    plt.axis('off')
    
    # Plot combined heatmap
    plt.subplot(2, 3, 3)
    combined_heatmap = np.max(heatmaps, axis=0)
    plt.imshow(combined_heatmap, cmap='hot')
    plt.title('Combined Heatmap')
    plt.axis('off')
    
    # Plot individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        plt.subplot(3, 4, 9 + i)
        plt.imshow(heatmaps[i], cmap='hot')
        plt.title(f'{corner_names[i]} Corner')
        plt.axis('off')
    
    # Plot image with corners and segmentation overlay
    plt.subplot(2, 3, 4)
    plt.imshow(image)
    
    # Create a segmentation boundary overlay
    if segmentation is not None:
        # Create a binary mask
        binary_mask = (segmentation > 0.5).astype(np.uint8)
        
        # Find contours in the binary mask
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Draw contours on the image
        if contours:
            # Convert to RGB for overlay if grayscale
            overlay = np.zeros_like(image)
            cv2.drawContours(overlay, contours, -1, (0, 255, 0), 2)
            
            # Overlay the contours on the image
            alpha = 0.5
            plt.imshow(overlay, alpha=alpha)
    
    # Draw corners and connections
    valid_corners = [c for c in corners if c is not None]
    if len(valid_corners) == 4:
        # Draw corners
        for i, corner in enumerate(corners):
            plt.plot(corner[0], corner[1], 'o', markersize=10, 
                     color=['red', 'green', 'blue', 'purple'][i])
        
        # Draw connections
        for i in range(4):
            j = (i + 1) % 4
            plt.plot([corners[i][0], corners[j][0]], 
                     [corners[i][1], corners[j][1]], 
                     '-', linewidth=2, color='yellow')
    
    plt.title('Segmentation-Guided Corner Detection')
    plt.axis('off')
    
    # Plot confidence metrics
    plt.subplot(2, 3, 5)
    metrics_text = "\n".join([
        f"Avg Peak Value: {confidence_metrics['avg_peak_value']:.4f}",
        f"Avg Peak-to-Mean Ratio: {confidence_metrics['avg_peak_to_mean_ratio']:.4f}",
        f"Avg Peak-to-Second Ratio: {confidence_metrics['avg_peak_to_second_ratio']:.4f}",
        f"Detection Rate: {confidence_metrics['detection_rate']:.4f}",
        f"Overall Confidence: {confidence_metrics['overall_confidence']:.4f}"
    ])
    plt.text(0.5, 0.5, metrics_text, ha='center', va='center', fontsize=12)
    plt.title('Confidence Metrics')
    plt.axis('off')
    
    # Plot confidence gauge
    plt.subplot(2, 3, 6)
    confidence = confidence_metrics['overall_confidence']
    gauge_colors = plt.cm.RdYlGn(np.linspace(0, 1, 100))
    gauge_idx = min(int(confidence * 100), 99)
    plt.pie([1], colors=[gauge_colors[gauge_idx]], startangle=90, 
            wedgeprops=dict(width=0.3, edgecolor='w'))
    plt.text(0, 0, f"{confidence:.2f}", ha='center', va='center', fontsize=20, 
             bbox=dict(facecolor='white', alpha=0.8))
    plt.title('Confidence Gauge')
    plt.axis('equal')
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path)
        print(f"Visualization saved to {output_path}")
    
    plt.show()


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Run inference with v5 chess board detection model')
    parser.add_argument('--image_path', type=str, required=True, help='Path to input image')
    parser.add_argument('--model_path', type=str, required=True, help='Path to model checkpoint')
    parser.add_argument('--output_path', type=str, default=None, help='Path to save output visualization')
    parser.add_argument('--use_post_processing', action='store_true', help='Use post-processing')
    parser.add_argument('--use_segmentation_guidance', action='store_true', help='Use segmentation guidance')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    args = parser.parse_args()
    
    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")
    
    # Load model
    print("Loading model...")
    model = EnhancedChessBoardUNetV5(n_channels=3)
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model = model.to(device)
    model.eval()
    
    # Load image
    print("Loading image...")
    image, image_tensor = load_image(args.image_path)
    
    # Run inference
    print("Running inference...")
    outputs = predict(model, image_tensor, device, 
                     args.use_post_processing, 
                     args.use_segmentation_guidance)
    
    # Visualize results
    print("Visualizing results...")
    visualize_results(
        image=image,
        segmentation=outputs['segmentation'],
        heatmaps=outputs['heatmaps'],
        corners=outputs['corners'],
        confidence_metrics=outputs['confidence_metrics'],
        output_path=args.output_path
    )
    
    # Print confidence metrics
    print("\nConfidence Metrics:")
    for key, value in outputs['confidence_metrics'].items():
        print(f"  {key}: {value:.4f}")
    
    print("\nInference completed!")


if __name__ == "__main__":
    main()
