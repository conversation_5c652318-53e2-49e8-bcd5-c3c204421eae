PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_enhanced_unet_v2_stable.py
🚀 Starting Enhanced U-Net V2 Stable Training...
Configuration: {
  "dataset_dir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\data\\augmented\\v5.2\\augmented_20250518_153326",
  "save_dir": "chess_board_detection/enhanced_unet_v2_stable_results",
  "epochs": 50,
  "batch_size": 4,
  "learning_rate": 5e-05,
  "image_size": [
    384,
    384
  ],
  "num_workers": 0
}
Using device: cuda
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.4 GB
Creating dataloaders...
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Creating Enhanced U-Net V2...
Model parameters: 41,035,699
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_enhanced_unet_v2_stable.py:278: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
Starting training for 50 epochs...

Epoch 1/50
Training:   0%|                                                                                         | 0/21 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  image = torch.load(image_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  mask = torch.load(mask_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_enhanced_unet_v2_stable.py:133: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.51it/s, Loss=0.4084, Dice=0.9755, IoU=0.9522]
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.09it/s, Loss=0.5700, Dice=0.0000, IoU=0.0000] 
Train - Loss: 0.5353, Dice: 0.6969, IoU: 0.6042
Val   - Loss: 0.6180, Dice: 0.2835, IoU: 0.1836
LR: 0.000050
✅ New best model saved! Val Dice: 0.2835

Epoch 2/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.2400, Dice=0.9833, IoU=0.9672] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.50it/s, Loss=0.4109, Dice=0.8654, IoU=0.7628] 
Train - Loss: 0.3957, Dice: 0.8716, IoU: 0.7798
Val   - Loss: 0.5251, Dice: 0.6717, IoU: 0.5173
LR: 0.000050
✅ New best model saved! Val Dice: 0.6717

Epoch 3/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.90it/s, Loss=0.3666, Dice=0.7255, IoU=0.5692] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.72it/s, Loss=0.3698, Dice=0.8374, IoU=0.7204] 
Train - Loss: 0.2523, Dice: 0.9013, IoU: 0.8264
Val   - Loss: 0.5370, Dice: 0.6661, IoU: 0.5083
LR: 0.000050

Epoch 4/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.92it/s, Loss=0.1501, Dice=0.9223, IoU=0.8559] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.81it/s, Loss=0.3602, Dice=0.8448, IoU=0.7313] 
Train - Loss: 0.1620, Dice: 0.9246, IoU: 0.8647
Val   - Loss: 0.6192, Dice: 0.6603, IoU: 0.5032
LR: 0.000049

Epoch 5/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0658, Dice=0.9695, IoU=0.9408] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.73it/s, Loss=0.4008, Dice=0.8658, IoU=0.7633] 
Train - Loss: 0.1148, Dice: 0.9422, IoU: 0.8938
Val   - Loss: 0.4373, Dice: 0.7648, IoU: 0.6226
LR: 0.000049
✅ New best model saved! Val Dice: 0.7648

Epoch 6/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.0690, Dice=0.9683, IoU=0.9385] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.73it/s, Loss=0.5672, Dice=0.0372, IoU=0.0190] 
Train - Loss: 0.0986, Dice: 0.9450, IoU: 0.8999
Val   - Loss: 0.5373, Dice: 0.4346, IoU: 0.3091
LR: 0.000048

Epoch 7/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.91it/s, Loss=0.0536, Dice=0.9692, IoU=0.9402] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.75it/s, Loss=0.6782, Dice=0.7741, IoU=0.6315] 
Train - Loss: 0.1409, Dice: 0.9148, IoU: 0.8692
Val   - Loss: 1.1082, Dice: 0.6321, IoU: 0.4677
LR: 0.000048

Epoch 8/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.90it/s, Loss=0.0510, Dice=0.9662, IoU=0.9346] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.63it/s, Loss=0.4299, Dice=0.8122, IoU=0.6838] 
Train - Loss: 0.0928, Dice: 0.9453, IoU: 0.9035
Val   - Loss: 0.6343, Dice: 0.6807, IoU: 0.5230
LR: 0.000047

Epoch 9/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.90it/s, Loss=0.0451, Dice=0.9805, IoU=0.9617] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.74it/s, Loss=0.6679, Dice=0.7804, IoU=0.6399] 
Train - Loss: 0.0831, Dice: 0.9498, IoU: 0.9123
Val   - Loss: 0.9519, Dice: 0.6472, IoU: 0.4848
LR: 0.000046

Epoch 10/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.0269, Dice=0.9897, IoU=0.9795] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.90it/s, Loss=0.2663, Dice=0.9049, IoU=0.8264] 
Train - Loss: 0.0511, Dice: 0.9722, IoU: 0.9473
Val   - Loss: 0.3600, Dice: 0.7782, IoU: 0.6420
LR: 0.000045
✅ New best model saved! Val Dice: 0.7782

Epoch 11/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0196, Dice=0.9897, IoU=0.9795] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.42it/s, Loss=0.4437, Dice=0.8211, IoU=0.6965] 
Train - Loss: 0.0761, Dice: 0.9542, IoU: 0.9226
Val   - Loss: 0.7128, Dice: 0.6796, IoU: 0.5223
LR: 0.000044

Epoch 12/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.0434, Dice=0.9779, IoU=0.9567] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.74it/s, Loss=0.5007, Dice=0.8138, IoU=0.6860] 
Train - Loss: 0.0542, Dice: 0.9708, IoU: 0.9449
Val   - Loss: 0.7977, Dice: 0.6714, IoU: 0.5131
LR: 0.000043

Epoch 13/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0392, Dice=0.9733, IoU=0.9480] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.72it/s, Loss=0.6234, Dice=0.8059, IoU=0.6749] 
Train - Loss: 0.0599, Dice: 0.9661, IoU: 0.9376
Val   - Loss: 0.9848, Dice: 0.6611, IoU: 0.5000
LR: 0.000042

Epoch 14/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.89it/s, Loss=0.0238, Dice=0.9853, IoU=0.9710] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.68it/s, Loss=0.6734, Dice=0.8102, IoU=0.6810] 
Train - Loss: 0.0474, Dice: 0.9723, IoU: 0.9474
Val   - Loss: 1.0160, Dice: 0.6775, IoU: 0.5189
LR: 0.000041

Epoch 15/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.89it/s, Loss=0.0416, Dice=0.9700, IoU=0.9417] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.63it/s, Loss=0.8694, Dice=0.7901, IoU=0.6530] 
Train - Loss: 0.0370, Dice: 0.9804, IoU: 0.9621
Val   - Loss: 1.2913, Dice: 0.6443, IoU: 0.4813
LR: 0.000040

Epoch 16/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.0173, Dice=0.9927, IoU=0.9855] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.73it/s, Loss=0.2362, Dice=0.9519, IoU=0.9083] 
Train - Loss: 0.0458, Dice: 0.9753, IoU: 0.9540
Val   - Loss: 0.3219, Dice: 0.7997, IoU: 0.6739
LR: 0.000039
✅ New best model saved! Val Dice: 0.7997

Epoch 17/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.90it/s, Loss=0.0134, Dice=0.9945, IoU=0.9890] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.79it/s, Loss=0.7608, Dice=0.0000, IoU=0.0000] 
Train - Loss: 0.0612, Dice: 0.9663, IoU: 0.9413
Val   - Loss: 0.4558, Dice: 0.4004, IoU: 0.2918
LR: 0.000037

Epoch 18/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0225, Dice=0.9885, IoU=0.9773] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.68it/s, Loss=0.3189, Dice=0.8523, IoU=0.7426] 
Train - Loss: 0.0461, Dice: 0.9747, IoU: 0.9539
Val   - Loss: 0.6502, Dice: 0.6941, IoU: 0.5385
LR: 0.000036

Epoch 19/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0226, Dice=0.9917, IoU=0.9835] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.68it/s, Loss=0.4863, Dice=0.7704, IoU=0.6266] 
Train - Loss: 0.0372, Dice: 0.9810, IoU: 0.9636
Val   - Loss: 0.3273, Dice: 0.8066, IoU: 0.6774
LR: 0.000035
✅ New best model saved! Val Dice: 0.8066

Epoch 20/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0229, Dice=0.9894, IoU=0.9790] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.55it/s, Loss=0.2324, Dice=0.8841, IoU=0.7923] 
Train - Loss: 0.0427, Dice: 0.9749, IoU: 0.9535
Val   - Loss: 0.4945, Dice: 0.7350, IoU: 0.5876
LR: 0.000033

Epoch 21/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0628, Dice=0.9558, IoU=0.9153] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.57it/s, Loss=0.2697, Dice=0.8798, IoU=0.7854] 
Train - Loss: 0.0496, Dice: 0.9705, IoU: 0.9468
Val   - Loss: 0.3707, Dice: 0.7736, IoU: 0.6340
LR: 0.000032

Epoch 22/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.0175, Dice=0.9888, IoU=0.9779] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.54it/s, Loss=0.2721, Dice=0.8648, IoU=0.7618] 
Train - Loss: 0.0350, Dice: 0.9799, IoU: 0.9619
Val   - Loss: 0.3792, Dice: 0.7734, IoU: 0.6332
LR: 0.000030

Epoch 23/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0130, Dice=0.9956, IoU=0.9913] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.75it/s, Loss=0.3484, Dice=0.8299, IoU=0.7093] 
Train - Loss: 0.0269, Dice: 0.9853, IoU: 0.9713
Val   - Loss: 0.3274, Dice: 0.8068, IoU: 0.6796
LR: 0.000029
✅ New best model saved! Val Dice: 0.8068

Epoch 24/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0152, Dice=0.9893, IoU=0.9789] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.50it/s, Loss=0.6146, Dice=0.6613, IoU=0.4940] 
Train - Loss: 0.0289, Dice: 0.9849, IoU: 0.9714
Val   - Loss: 0.3804, Dice: 0.7928, IoU: 0.6738
LR: 0.000027

Epoch 25/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.77it/s, Loss=0.0321, Dice=0.9831, IoU=0.9668] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.62it/s, Loss=0.2521, Dice=0.8765, IoU=0.7802] 
Train - Loss: 0.0379, Dice: 0.9788, IoU: 0.9603
Val   - Loss: 0.4445, Dice: 0.7532, IoU: 0.6083
LR: 0.000026

Epoch 26/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.0150, Dice=0.9914, IoU=0.9829] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.69it/s, Loss=0.2462, Dice=0.8893, IoU=0.8007]
Train - Loss: 0.0211, Dice: 0.9881, IoU: 0.9766
Val   - Loss: 0.4545, Dice: 0.7435, IoU: 0.5987
LR: 0.000024

Epoch 27/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0316, Dice=0.9839, IoU=0.9684] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.69it/s, Loss=0.2200, Dice=0.8872, IoU=0.7972] 
Train - Loss: 0.0289, Dice: 0.9856, IoU: 0.9724
Val   - Loss: 0.3229, Dice: 0.7948, IoU: 0.6626
LR: 0.000022

Epoch 28/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0353, Dice=0.9793, IoU=0.9594] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.65it/s, Loss=0.2248, Dice=0.8970, IoU=0.8133] 
Train - Loss: 0.0348, Dice: 0.9823, IoU: 0.9664
Val   - Loss: 0.3654, Dice: 0.7696, IoU: 0.6329
LR: 0.000021

Epoch 29/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0159, Dice=0.9855, IoU=0.9713] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.59it/s, Loss=0.2500, Dice=0.8928, IoU=0.8063] 
Train - Loss: 0.0330, Dice: 0.9824, IoU: 0.9671
Val   - Loss: 0.4697, Dice: 0.7490, IoU: 0.6081
LR: 0.000019

Epoch 30/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0110, Dice=0.9930, IoU=0.9860] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.65it/s, Loss=0.2091, Dice=0.9119, IoU=0.8381] 
Train - Loss: 0.0175, Dice: 0.9900, IoU: 0.9803
Val   - Loss: 0.4019, Dice: 0.7698, IoU: 0.6319
LR: 0.000018

Epoch 31/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.0184, Dice=0.9899, IoU=0.9801] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.68it/s, Loss=0.4321, Dice=0.7538, IoU=0.6049] 
Train - Loss: 0.0191, Dice: 0.9894, IoU: 0.9791
Val   - Loss: 0.3406, Dice: 0.8024, IoU: 0.6747
LR: 0.000016

Epoch 32/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0296, Dice=0.9862, IoU=0.9727] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.58it/s, Loss=0.6155, Dice=0.6409, IoU=0.4716] 
Train - Loss: 0.0368, Dice: 0.9782, IoU: 0.9600
Val   - Loss: 0.3628, Dice: 0.7821, IoU: 0.6541
LR: 0.000015

Epoch 33/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0230, Dice=0.9877, IoU=0.9757] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.47it/s, Loss=0.3151, Dice=0.8479, IoU=0.7360] 
Train - Loss: 0.0190, Dice: 0.9893, IoU: 0.9790
Val   - Loss: 0.3008, Dice: 0.8111, IoU: 0.6832
LR: 0.000014
✅ New best model saved! Val Dice: 0.8111

Epoch 34/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0076, Dice=0.9969, IoU=0.9939] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.46it/s, Loss=0.3208, Dice=0.8394, IoU=0.7233] 
Train - Loss: 0.0263, Dice: 0.9863, IoU: 0.9737
Val   - Loss: 0.2664, Dice: 0.8256, IoU: 0.7034
LR: 0.000012
✅ New best model saved! Val Dice: 0.8256

Epoch 35/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.0173, Dice=0.9912, IoU=0.9825] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.60it/s, Loss=0.2415, Dice=0.8684, IoU=0.7674] 
Train - Loss: 0.0178, Dice: 0.9903, IoU: 0.9810
Val   - Loss: 0.4151, Dice: 0.7544, IoU: 0.6113
LR: 0.000011

Epoch 36/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0088, Dice=0.9958, IoU=0.9916] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.77it/s, Loss=0.2219, Dice=0.8930, IoU=0.8066] 
Train - Loss: 0.0199, Dice: 0.9884, IoU: 0.9775
Val   - Loss: 0.3422, Dice: 0.7844, IoU: 0.6493
LR: 0.000010

Epoch 37/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.91it/s, Loss=0.0420, Dice=0.9802, IoU=0.9612] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.75it/s, Loss=0.2424, Dice=0.8680, IoU=0.7668] 
Train - Loss: 0.0172, Dice: 0.9905, IoU: 0.9814
Val   - Loss: 0.4826, Dice: 0.7210, IoU: 0.5727
LR: 0.000009

Epoch 38/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.92it/s, Loss=0.0096, Dice=0.9951, IoU=0.9903] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.83it/s, Loss=0.2167, Dice=0.8952, IoU=0.8103] 
Train - Loss: 0.0160, Dice: 0.9917, IoU: 0.9836
Val   - Loss: 0.4450, Dice: 0.7411, IoU: 0.5978
LR: 0.000008

Epoch 39/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0095, Dice=0.9961, IoU=0.9922] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.59it/s, Loss=0.1954, Dice=0.9078, IoU=0.8312] 
Train - Loss: 0.0147, Dice: 0.9922, IoU: 0.9845
Val   - Loss: 0.3728, Dice: 0.7598, IoU: 0.6216
LR: 0.000007

Epoch 40/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0123, Dice=0.9948, IoU=0.9896] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.82it/s, Loss=0.2413, Dice=0.8914, IoU=0.8040] 
Train - Loss: 0.0154, Dice: 0.9915, IoU: 0.9833
Val   - Loss: 0.4191, Dice: 0.7486, IoU: 0.6066
LR: 0.000006

Epoch 41/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0171, Dice=0.9889, IoU=0.9780] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.69it/s, Loss=0.2033, Dice=0.9043, IoU=0.8254] 
Train - Loss: 0.0191, Dice: 0.9889, IoU: 0.9789
Val   - Loss: 0.3855, Dice: 0.7562, IoU: 0.6173
LR: 0.000005

Epoch 42/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.77it/s, Loss=0.0115, Dice=0.9952, IoU=0.9904] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.38it/s, Loss=0.2818, Dice=0.8789, IoU=0.7839] 
Train - Loss: 0.0127, Dice: 0.9931, IoU: 0.9864
Val   - Loss: 0.4928, Dice: 0.7331, IoU: 0.5877
LR: 0.000004

Epoch 43/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.76it/s, Loss=0.0065, Dice=0.9975, IoU=0.9950] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.35it/s, Loss=0.2168, Dice=0.8904, IoU=0.8024] 
Train - Loss: 0.0123, Dice: 0.9932, IoU: 0.9866
Val   - Loss: 0.3879, Dice: 0.7653, IoU: 0.6259
LR: 0.000003

Epoch 44/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.76it/s, Loss=0.0176, Dice=0.9854, IoU=0.9711] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.43it/s, Loss=0.2974, Dice=0.8664, IoU=0.7643] 
Train - Loss: 0.0172, Dice: 0.9907, IoU: 0.9819
Val   - Loss: 0.5845, Dice: 0.7140, IoU: 0.5639
LR: 0.000003

Epoch 45/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.76it/s, Loss=0.0096, Dice=0.9952, IoU=0.9905] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.50it/s, Loss=0.2390, Dice=0.8916, IoU=0.8044] 
Train - Loss: 0.0148, Dice: 0.9918, IoU: 0.9840
Val   - Loss: 0.4464, Dice: 0.7454, IoU: 0.6025
LR: 0.000002

Epoch 46/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.76it/s, Loss=0.0082, Dice=0.9969, IoU=0.9937] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.63it/s, Loss=0.2668, Dice=0.8761, IoU=0.7795] 
Train - Loss: 0.0129, Dice: 0.9931, IoU: 0.9864
Val   - Loss: 0.5239, Dice: 0.7289, IoU: 0.5812
LR: 0.000002

Epoch 47/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0082, Dice=0.9974, IoU=0.9949] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.57it/s, Loss=0.2439, Dice=0.8867, IoU=0.7964] 
Train - Loss: 0.0122, Dice: 0.9936, IoU: 0.9874
Val   - Loss: 0.4832, Dice: 0.7346, IoU: 0.5899
LR: 0.000001

Epoch 48/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.0263, Dice=0.9859, IoU=0.9721] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.69it/s, Loss=0.1962, Dice=0.9048, IoU=0.8261] 
Train - Loss: 0.0125, Dice: 0.9933, IoU: 0.9868
Val   - Loss: 0.2949, Dice: 0.8005, IoU: 0.6718
LR: 0.000001

Epoch 49/50
Training: 100%|██████████████████████████████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.0119, Dice=0.9935, IoU=0.9871] 
Validation: 100%|██████████████████████████████████████████| 6/6 [00:00<00:00,  8.54it/s, Loss=0.2149, Dice=0.8942, IoU=0.8087] 
Train - Loss: 0.0121, Dice: 0.9934, IoU: 0.9870
Val   - Loss: 0.2978, Dice: 0.8032, IoU: 0.6740
LR: 0.000001
Early stopping triggered after 15 epochs without improvement

🎉 Training completed in 0.08 hours
🏆 Best validation Dice: 0.8256
📁 Results saved to: chess_board_detection\enhanced_unet_v2_stable_results

============================================================
🎉 ENHANCED U-NET V2 TRAINING COMPLETED!
============================================================
🏆 Best Dice Score: 0.8256

📊 COMPARISON WITH V1:
V1 Best Dice: 0.9100
V2 Best Dice: 0.8256
📉 Decline: -9.27%
⚠️ V2 needs further optimization

🎯 NEXT STEPS:
⚠️ Needs improvement - try different hyperparameters
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 