<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="md_theme_light_background">#FFFBFF</color>
    <color name="md_theme_light_error">#BA1A1A</color>
    <color name="md_theme_light_errorContainer">#FFDAD6</color>
    <color name="md_theme_light_inverseOnSurface">#F9EFE7</color>
    <color name="md_theme_light_inversePrimary">#FFB59D</color>
    <color name="md_theme_light_inverseSurface">#34302A</color>
    <color name="md_theme_light_onBackground">#1F1B16</color>
    <color name="md_theme_light_onError">#FFFFFF</color>
    <color name="md_theme_light_onErrorContainer">#410002</color>
    <color name="md_theme_light_onPrimary">#FFFFFF</color>
    <color name="md_theme_light_onPrimaryContainer">#2D1810</color>
    <color name="md_theme_light_onSecondary">#FFFFFF</color>
    <color name="md_theme_light_onSecondaryContainer">#241A0E</color>
    <color name="md_theme_light_onSurface">#1F1B16</color>
    <color name="md_theme_light_onSurfaceVariant">#4F4539</color>
    <color name="md_theme_light_onTertiary">#FFFFFF</color>
    <color name="md_theme_light_onTertiaryContainer">#141F00</color>
    <color name="md_theme_light_outline">#817567</color>
    <color name="md_theme_light_outlineVariant">#D3C4B4</color>
    <color name="md_theme_light_primary">#8B4513</color>
    <color name="md_theme_light_primaryContainer">#DEB887</color>
    <color name="md_theme_light_scrim">#000000</color>
    <color name="md_theme_light_secondary">#6B4423</color>
    <color name="md_theme_light_secondaryContainer">#F5DEB3</color>
    <color name="md_theme_light_surface">#FFFBFF</color>
    <color name="md_theme_light_surfaceVariant">#F0E0CF</color>
    <color name="md_theme_light_tertiary">#4A5D23</color>
    <color name="md_theme_light_tertiaryContainer">#CBD896</color>
    <string name="about">About</string>
    <string name="analysis_text">Analysis:</string>
    <string name="analysis_wait_message">This may take a few seconds</string>
    <string name="analyze_position">Analyze Position</string>
    <string name="analyzing_board">Analyzing chess board...</string>
    <string name="app_name">Chess Vision AI</string>
    <string name="auto_analysis">Auto Analysis</string>
    <string name="back">Back</string>
    <string name="best_move">Best: %1$s</string>
    <string name="best_moves">Best Moves:</string>
    <string name="board_theme">Board Theme</string>
    <string name="camera_permission_description">To detect chess positions, we need access to your camera. This allows you to capture images of physical chess boards.</string>
    <string name="camera_permission_rationale">Camera access is needed to capture chess board images for position detection. Please grant permission to continue.</string>
    <string name="camera_permission_required">Camera Permission Required</string>
    <string name="cancel">Cancel</string>
    <string name="capture">Capture</string>
    <string name="capture_board_subtitle">Point camera at chess board to detect position</string>
    <string name="capture_board_title">Capture Chess Board</string>
    <string name="chess_board_title">Chess Board</string>
    <string name="color_black">Black</string>
    <string name="color_white">White</string>
    <string name="deeper_analysis">Deeper</string>
    <string name="delete">Delete</string>
    <string name="engine_strength">Engine Strength</string>
    <string name="error">Error</string>
    <string name="error_camera_permission">Camera permission is required to scan chess boards</string>
    <string name="error_fen_detection">Failed to detect chess position</string>
    <string name="error_invalid_fen">Invalid chess position detected</string>
    <string name="error_network">Network error occurred</string>
    <string name="error_unknown">An unexpected error occurred</string>
    <string name="evaluation">Evaluation: %1$s</string>
    <string name="grant_camera_permission">Grant Camera Permission</string>
    <string name="home_title">Chess Vision AI</string>
    <string name="loading">Loading...</string>
    <string name="new_game">New Game</string>
    <string name="no_recent_games">No recent games</string>
    <string name="ok">OK</string>
    <string name="piece_bishop">Bishop</string>
    <string name="piece_king">King</string>
    <string name="piece_knight">Knight</string>
    <string name="piece_pawn">Pawn</string>
    <string name="piece_queen">Queen</string>
    <string name="piece_rook">Rook</string>
    <string name="piece_style">Piece Style</string>
    <string name="play_vs_ai">Play vs AI</string>
    <string name="position_analysis">Position Analysis</string>
    <string name="position_camera_instruction">Position Camera Over Chess Board</string>
    <string name="recent_games">Recent Games</string>
    <string name="retry">Retry</string>
    <string name="save">Save</string>
    <string name="save_game">Save Game</string>
    <string name="scan_board">Scan Board</string>
    <string name="settings_title">Settings</string>
    <string name="share">Share</string>
    <string name="share_analysis">Share</string>
    <string name="sound_effects">Sound Effects</string>
    <string name="start_scanning">Start by scanning a chess board</string>
    <style name="Theme.ChessVisionApp" parent="android:Theme.Material.NoActionBar">
        
        <item name="android:colorPrimary">#1A1A2E</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>

        
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowFullscreen">false</item>
        <item name="android:fitsSystemWindows">false</item>

        
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>