"""
Analyze the class distribution in a YOLO dataset.
This script checks if all classes are represented in both training and validation sets,
and generates a detailed report of the class distribution.
"""

import os
import sys
import argparse
import json
import yaml
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from tqdm import tqdm
from collections import Counter, defaultdict

def analyze_dataset(dataset_path, output_dir=None):
    """
    Analyze the class distribution in a YOLO dataset.
    
    Args:
        dataset_path: Path to the dataset YAML file or directory
        output_dir: Directory to save analysis results (defaults to dataset directory)
    """
    # Determine if input is a YAML file or directory
    if dataset_path.endswith('.yaml'):
        yaml_path = dataset_path
        dataset_dir = os.path.dirname(dataset_path)
    else:
        # Look for dataset.yaml in the directory
        dataset_dir = dataset_path
        yaml_path = os.path.join(dataset_dir, 'dataset.yaml')
        if not os.path.exists(yaml_path):
            # Try data.yaml as an alternative
            yaml_path = os.path.join(dataset_dir, 'data.yaml')
            if not os.path.exists(yaml_path):
                raise FileNotFoundError(f"Could not find dataset.yaml or data.yaml in {dataset_dir}")
    
    # Set output directory
    if output_dir is None:
        output_dir = dataset_dir
    os.makedirs(output_dir, exist_ok=True)
    
    # Load dataset configuration
    with open(yaml_path, 'r') as f:
        dataset_config = yaml.safe_load(f)
    
    # Get class names
    class_names = {}
    if 'names' in dataset_config:
        if isinstance(dataset_config['names'], list):
            class_names = {i: name for i, name in enumerate(dataset_config['names'])}
        elif isinstance(dataset_config['names'], dict):
            class_names = dataset_config['names']
    
    # Get train and validation paths
    train_path = os.path.join(dataset_dir, dataset_config.get('train', 'images/train'))
    val_path = os.path.join(dataset_dir, dataset_config.get('val', 'images/val'))
    
    # Ensure paths are absolute
    if not os.path.isabs(train_path):
        train_path = os.path.join(dataset_dir, train_path)
    if not os.path.isabs(val_path):
        val_path = os.path.join(dataset_dir, val_path)
    
    # Get label paths
    train_label_path = train_path.replace('images', 'labels')
    val_label_path = val_path.replace('images', 'labels')
    
    # Analyze class distribution
    train_class_counts = Counter()
    val_class_counts = Counter()
    
    # Process training labels
    print(f"Analyzing training labels in {train_label_path}...")
    train_class_counts = count_classes_in_directory(train_label_path)
    
    # Process validation labels
    print(f"Analyzing validation labels in {val_label_path}...")
    val_class_counts = count_classes_in_directory(val_label_path)
    
    # Combine counts
    all_classes = set(train_class_counts.keys()) | set(val_class_counts.keys())
    
    # Print summary
    print("\nDataset Analysis Summary:")
    print(f"Total classes: {len(all_classes)}")
    print(f"Classes in training set: {len(train_class_counts)}")
    print(f"Classes in validation set: {len(val_class_counts)}")
    
    # Check if all classes are in both sets
    missing_in_train = all_classes - set(train_class_counts.keys())
    missing_in_val = all_classes - set(val_class_counts.keys())
    
    if missing_in_train:
        print(f"\n⚠️ WARNING: {len(missing_in_train)} classes missing from training set:")
        for class_id in sorted(missing_in_train):
            class_name = class_names.get(class_id, f"Class {class_id}")
            print(f"  - {class_name} (ID: {class_id})")
    
    if missing_in_val:
        print(f"\n⚠️ WARNING: {len(missing_in_val)} classes missing from validation set:")
        for class_id in sorted(missing_in_val):
            class_name = class_names.get(class_id, f"Class {class_id}")
            print(f"  - {class_name} (ID: {class_id})")
    
    # Print detailed class distribution
    print("\nDetailed Class Distribution:")
    print(f"{'Class ID':<8} {'Class Name':<20} {'Train':<10} {'Val':<10} {'Total':<10} {'Train %':<10} {'Val %':<10}")
    print("-" * 80)
    
    class_distribution = {}
    
    for class_id in sorted(all_classes):
        train_count = train_class_counts.get(class_id, 0)
        val_count = val_class_counts.get(class_id, 0)
        total = train_count + val_count
        
        train_percent = train_count / total * 100 if total > 0 else 0
        val_percent = val_count / total * 100 if total > 0 else 0
        
        class_name = class_names.get(class_id, f"Class {class_id}")
        if len(class_name) > 19:
            class_name = class_name[:16] + "..."
        
        print(f"{class_id:<8} {class_name:<20} {train_count:<10} {val_count:<10} {total:<10} {train_percent:.1f}%{'':<5} {val_percent:.1f}%")
        
        class_distribution[class_id] = {
            "name": class_names.get(class_id, f"Class {class_id}"),
            "train": train_count,
            "val": val_count,
            "total": total,
            "train_percent": round(train_percent, 1),
            "val_percent": round(val_percent, 1)
        }
    
    # Save analysis results
    results = {
        "dataset_path": dataset_path,
        "total_classes": len(all_classes),
        "classes_in_train": len(train_class_counts),
        "classes_in_val": len(val_class_counts),
        "missing_in_train": list(sorted(missing_in_train)),
        "missing_in_val": list(sorted(missing_in_val)),
        "class_distribution": class_distribution
    }
    
    with open(os.path.join(output_dir, 'dataset_analysis.json'), 'w') as f:
        json.dump(results, f, indent=2)
    
    # Save as CSV for easy import into spreadsheets
    with open(os.path.join(output_dir, 'class_distribution.csv'), 'w') as f:
        f.write("class_id,class_name,train,val,total,train_percent,val_percent\n")
        for class_id, data in class_distribution.items():
            f.write(f"{class_id},{data['name']},{data['train']},{data['val']},{data['total']},{data['train_percent']},{data['val_percent']}\n")
    
    # Create visualization
    create_distribution_chart(class_distribution, class_names, os.path.join(output_dir, 'class_distribution.png'))
    
    print(f"\nAnalysis results saved to {output_dir}")
    return results

def count_classes_in_directory(label_dir):
    """Count class occurrences in a directory of YOLO label files."""
    class_counts = Counter()
    
    if not os.path.exists(label_dir):
        print(f"Warning: Label directory {label_dir} does not exist")
        return class_counts
    
    label_files = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
    
    for label_file in tqdm(label_files, desc="Counting classes"):
        label_path = os.path.join(label_dir, label_file)
        
        try:
            with open(label_path, 'r') as f:
                for line in f:
                    if line.strip():
                        parts = line.strip().split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            class_counts[class_id] += 1
        except Exception as e:
            print(f"Error processing {label_path}: {e}")
    
    return class_counts

def create_distribution_chart(class_distribution, class_names, output_path):
    """Create a bar chart of class distribution."""
    # Prepare data
    class_ids = sorted(class_distribution.keys())
    train_counts = [class_distribution[class_id]['train'] for class_id in class_ids]
    val_counts = [class_distribution[class_id]['val'] for class_id in class_ids]
    
    # Get class names for labels
    labels = [class_names.get(class_id, f"Class {class_id}") for class_id in class_ids]
    
    # Create figure
    plt.figure(figsize=(14, 8))
    
    # Set up bar positions
    x = np.arange(len(class_ids))
    width = 0.35
    
    # Create bars
    plt.bar(x - width/2, train_counts, width, label='Train')
    plt.bar(x + width/2, val_counts, width, label='Validation')
    
    # Add labels and title
    plt.xlabel('Class')
    plt.ylabel('Count')
    plt.title('Class Distribution in Dataset')
    plt.xticks(x, labels, rotation=45, ha='right')
    plt.legend()
    
    # Add grid
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(output_path, dpi=150)
    plt.close()

def main():
    parser = argparse.ArgumentParser(description="Analyze class distribution in a YOLO dataset")
    parser.add_argument("dataset_path", type=str, help="Path to dataset YAML file or directory")
    parser.add_argument("--output_dir", type=str, default=None, help="Directory to save analysis results")
    
    args = parser.parse_args()
    
    analyze_dataset(args.dataset_path, args.output_dir)

if __name__ == "__main__":
    main()
