"""
Improved training script for the v5.1 chess board detection model.
This version implements enhanced modules and loss functions to address the issues identified in v5.

Key improvements:
1. Curriculum-based peak-to-second ratio loss with gradual target increase
2. Gradual peak sharpening module that increases effect over training epochs
3. Stabilized geometric loss with dynamic weighting to prevent loss explosion
4. Enhanced segmentation-heatmap integration with soft attention mechanism
5. Improved training stability with gradient normalization and clipping
6. Better epoch tracking for curriculum learning components
7. Safeguards to prevent any single loss component from dominating
8. Learning rate scheduling with warm restarts
"""

# Fix for albumentations ShiftScaleRotate warning - use Affine instead

import os
import argparse
import time
import json
import inspect
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, ConcatDataset
from tqdm import tqdm
from sklearn.model_selection import KFold
import albumentations as A
from albumentations.pytorch import ToTensorV2

from config import MODELS_DIR, DATA_DIR, DEVICE, INPUT_SIZE
from models.enhanced_unet_v5 import EnhancedChessBoardUNetV5
from enhanced_loss_v5 import EnhancedCornerFocusedHeatmapLossV5, RobustSegmentationGuidanceLoss, StabilizedGeometricLoss, CurriculumPeakToSecondRatioLoss
from enhanced_loss_v4 import EnhancedDiceLoss, ImprovedGeometricConsistencyLoss
from models.improved_enhanced_unet_v5 import ImprovedEnhancedChessBoardUNetV5, GradualPeakSharpening
from utils.real_dataset import RealChessBoardDataset
from utils.metrics import calculate_corner_confidence


# Improved peak sharpening module
class ImprovedPeakSharpeningModule(nn.Module):
    """
    Improved peak sharpening module with stronger suppression of secondary peaks
    and enhanced primary peak amplification.
    """
    def __init__(self, channels=4):
        super(ImprovedPeakSharpeningModule, self).__init__()

        # Peak detection with larger receptive field
        self.peak_detector = nn.Sequential(
            nn.Conv2d(channels, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=3, padding=1),
        )

        # Attention mechanism with stronger focus
        self.attention = nn.Sequential(
            nn.Conv2d(channels, channels*2, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels*2, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=1),
            nn.Sigmoid()  # Stronger bounding of attention values
        )

        # More aggressive suppression mechanism
        self.suppressor = nn.Sequential(
            nn.Conv2d(channels*2, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=3, padding=1),
            nn.Tanh()  # Allow both enhancement and suppression
        )

        # Secondary peak identification
        self.secondary_peak_detector = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
        )

    def forward(self, heatmaps):
        # Detect peaks
        peak_map = self.peak_detector(heatmaps)

        # Generate attention weights (stronger focus on primary peaks)
        attention = self.attention(heatmaps) * 2.0  # Amplify attention effect

        # Apply attention to enhance primary peaks
        enhanced = heatmaps * (1 + attention)

        # Identify potential secondary peaks
        secondary_peaks = self.secondary_peak_detector(heatmaps)

        # Concatenate original, enhanced, and secondary peak info
        combined = torch.cat([heatmaps, enhanced], dim=1)

        # Apply suppression with awareness of secondary peaks
        suppression = self.suppressor(combined)

        # Apply suppression more aggressively to secondary peaks
        output = heatmaps + suppression - (secondary_peaks * 0.5)

        return output


# Enhanced separation loss
class EnhancedSeparationLoss(nn.Module):
    """
    Enhanced separation loss that more effectively penalizes poorly separated corners.
    """
    def __init__(self, weight=1.0, target_separation=30.0):
        super(EnhancedSeparationLoss, self).__init__()
        self.weight = weight
        self.target_separation = target_separation
        self.eps = 1e-6

    def forward(self, heatmaps):
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)

        for i in range(batch_size):
            # Find peak locations for all corners
            peak_locations = []
            for c in range(num_corners):
                hm = heatmaps[i, c]
                max_val, max_idx = torch.max(hm.view(-1), dim=0)
                y, x = max_idx // w, max_idx % w
                peak_locations.append((y, x))

            # Calculate pairwise distances between peaks
            for c1 in range(num_corners):
                for c2 in range(c1+1, num_corners):
                    y1, x1 = peak_locations[c1]
                    y2, x2 = peak_locations[c2]

                    # Euclidean distance
                    distance = torch.sqrt(torch.pow(y1 - y2, 2) + torch.pow(x1 - x2, 2) + self.eps)

                    # Penalize if distance is too small (corners too close)
                    if distance < self.target_separation:
                        penalty = torch.pow(self.target_separation - distance, 2) / self.target_separation
                        loss += penalty

        # Normalize by number of corner pairs
        num_pairs = batch_size * num_corners * (num_corners - 1) / 2
        loss = loss / (num_pairs + self.eps)

        return self.weight * loss


# Enhanced geometric consistency loss
class EnhancedGeometricConsistencyLoss(nn.Module):
    """
    Enhanced geometric consistency loss that enforces better spatial relationships.
    """
    def __init__(self, weight=1.0):
        super(EnhancedGeometricConsistencyLoss, self).__init__()
        self.weight = weight
        self.eps = 1e-6

    def forward(self, heatmaps):
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)

        # Ensure we have exactly 4 corners for chess board
        if num_corners != 4:
            return loss

        for i in range(batch_size):
            # Find peak locations for all corners
            peak_locations = []
            for c in range(num_corners):
                hm = heatmaps[i, c]
                max_val, max_idx = torch.max(hm.view(-1), dim=0)
                y, x = max_idx // w, max_idx % w
                peak_locations.append((y.float(), x.float()))

            # Check if corners form a quadrilateral
            # Calculate side lengths
            sides = []
            for j in range(4):
                next_j = (j + 1) % 4
                y1, x1 = peak_locations[j]
                y2, x2 = peak_locations[next_j]
                side_length = torch.sqrt(torch.pow(y1 - y2, 2) + torch.pow(x1 - x2, 2) + self.eps)
                sides.append(side_length)

            # Calculate diagonals
            diag1 = torch.sqrt(torch.pow(peak_locations[0][0] - peak_locations[2][0], 2) +
                              torch.pow(peak_locations[0][1] - peak_locations[2][1], 2) + self.eps)
            diag2 = torch.sqrt(torch.pow(peak_locations[1][0] - peak_locations[3][0], 2) +
                              torch.pow(peak_locations[1][1] - peak_locations[3][1], 2) + self.eps)

            # Penalize if diagonals are too different (non-parallelogram)
            diag_ratio = torch.max(diag1, diag2) / (torch.min(diag1, diag2) + self.eps)
            if diag_ratio > 1.2:  # Allow some flexibility
                loss += torch.pow(diag_ratio - 1.2, 2)

            # Penalize if opposite sides are too different (non-parallelogram)
            for j in range(2):
                side_ratio = torch.max(sides[j], sides[j+2]) / (torch.min(sides[j], sides[j+2]) + self.eps)
                if side_ratio > 1.3:  # Allow some flexibility
                    loss += torch.pow(side_ratio - 1.3, 2)

        # Normalize by batch size
        loss = loss / batch_size

        return self.weight * loss


# Aggressive peak-to-second ratio loss
class AggressivePeakToSecondRatioLoss(nn.Module):
    """
    More aggressive loss function specifically targeting the peak-to-second ratio.
    Uses exponential penalty and higher target ratio.
    """
    def __init__(self, weight=15.0, target_ratio=2.0):
        super(AggressivePeakToSecondRatioLoss, self).__init__()
        self.weight = weight
        self.target_ratio = target_ratio
        self.eps = 1e-6

    def forward(self, heatmaps):
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)
        valid_corners = 0

        for i in range(batch_size):
            for c in range(num_corners):
                hm = heatmaps[i, c]

                # Flatten heatmap
                hm_flat = hm.view(-1)

                # Find the maximum value (primary peak)
                max_val, max_idx = torch.max(hm_flat, dim=0)

                # Skip if max_val is too small
                if max_val < 0.1:
                    continue

                # Create a mask to exclude the main peak and its immediate surroundings
                mask = torch.ones_like(hm_flat)
                y, x = max_idx // w, max_idx % w

                # Create a larger exclusion zone around the primary peak
                for dy in range(-5, 6):
                    for dx in range(-5, 6):
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < h and 0 <= nx < w:
                            mask[ny * w + nx] = 0

                # Find second peak
                masked_hm = hm_flat * mask
                second_max_val = torch.max(masked_hm)

                # Ensure second_max_val is positive
                second_max_val = torch.clamp(second_max_val, min=self.eps)

                # Calculate ratio
                ratio = max_val / second_max_val

                # More aggressive penalty if ratio is below target
                if ratio < self.target_ratio:
                    # Exponential penalty for more aggressive optimization
                    penalty = torch.exp(self.target_ratio - ratio) - 1.0
                    loss += penalty
                    valid_corners += 1

        # Ensure loss is not too large
        if valid_corners > 0:
            loss = loss / valid_corners

        loss = torch.clamp(loss, max=100.0)

        return self.weight * loss


# Improved corner focused heatmap loss
class ImprovedCornerFocusedHeatmapLossV5(nn.Module):
    """
    Improved loss function for corner heatmap prediction with enhanced components.
    """
    def __init__(self,
                 separation_weight=1.0,
                 peak_separation_weight=0.8,
                 edge_suppression_weight=1.0,
                 peak_enhancement_weight=1.0,
                 peak_to_second_ratio_weight=15.0,
                 detection_rate_weight=8.0,
                 segmentation_guidance_weight=2.0):
        super(ImprovedCornerFocusedHeatmapLossV5, self).__init__()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight
        self.edge_suppression_weight = edge_suppression_weight
        self.peak_enhancement_weight = peak_enhancement_weight
        self.peak_to_second_ratio_weight = peak_to_second_ratio_weight
        self.detection_rate_weight = detection_rate_weight
        self.segmentation_guidance_weight = segmentation_guidance_weight

        # Enhanced components
        self.separation_loss = EnhancedSeparationLoss(weight=1.0)
        self.peak_to_second_ratio_loss = AggressivePeakToSecondRatioLoss(weight=1.0, target_ratio=2.0)
        self.geometric_loss = EnhancedGeometricConsistencyLoss(weight=1.0)
        self.segmentation_guidance_loss = RobustSegmentationGuidanceLoss(weight=1.0)

        self.eps = 1e-6

    def forward(self, pred_heatmaps, target_heatmaps, segmentation=None):
        """
        Calculate the loss between predicted and target heatmaps.

        Args:
            pred_heatmaps: Predicted heatmaps (B, 4, H, W)
            target_heatmaps: Target heatmaps (B, 4, H, W)
            segmentation: Optional segmentation mask (B, 1, H, W)
        """
        # Basic MSE loss
        mse_loss = F.mse_loss(pred_heatmaps, target_heatmaps)

        # Enhanced separation loss
        separation_loss = self.separation_loss(pred_heatmaps)

        # Peak separation loss
        peak_separation_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]
                max_val, _ = torch.max(heatmap.view(-1), dim=0)

                # Skip if max_val is too small
                if max_val < 0.1:
                    continue

                # Create a mask of values that are close to the maximum
                peak_mask = (heatmap > 0.5 * max_val).float()

                # Penalize if the peak area is too large
                peak_size = peak_mask.sum()
                ideal_peak_size = torch.tensor(9.0, device=pred_heatmaps.device)  # 3x3 peak
                peak_separation_loss += torch.abs(peak_size - ideal_peak_size) / (ideal_peak_size + self.eps)

        # Normalize peak separation loss
        peak_separation_loss = peak_separation_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Edge suppression loss
        edge_suppression_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]

                # Create edge mask (border of width 5)
                edge_mask = torch.zeros_like(heatmap)
                h, w = heatmap.shape
                edge_width = 5

                edge_mask[:edge_width, :] = 1.0  # Top
                edge_mask[-edge_width:, :] = 1.0  # Bottom
                edge_mask[:, :edge_width] = 1.0  # Left
                edge_mask[:, -edge_width:] = 1.0  # Right

                # Penalize activations along edges
                edge_activations = (heatmap * edge_mask).sum()
                edge_suppression_loss += edge_activations / (h * w + self.eps)

        # Normalize edge suppression loss
        edge_suppression_loss = edge_suppression_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Peak enhancement loss
        peak_enhancement_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]
                target = target_heatmaps[i, c]

                # Find the location of the maximum value in the target
                _, max_idx = torch.max(target.view(-1), dim=0)
                y, x = max_idx // target.size(1), max_idx % target.size(1)

                # Get the predicted value at the target peak location
                pred_at_peak = heatmap[y, x]

                # Penalize if the predicted value at the peak is too low
                target_peak_value = torch.tensor(1.0, device=pred_heatmaps.device)
                if pred_at_peak < target_peak_value:
                    peak_enhancement_loss += (target_peak_value - pred_at_peak) ** 2

        # Normalize peak enhancement loss
        peak_enhancement_loss = peak_enhancement_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Peak-to-second ratio loss
        peak_to_second_ratio_loss = self.peak_to_second_ratio_loss(pred_heatmaps)

        # Detection rate loss
        detection_rate_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]
                max_val = torch.max(heatmap)

                # Penalize if max_val is below detection threshold
                detection_threshold = torch.tensor(0.5, device=pred_heatmaps.device)
                if max_val < detection_threshold:
                    # Exponential penalty for more aggressive optimization
                    detection_rate_loss += torch.exp(detection_threshold - max_val) - 1.0

        # Normalize detection rate loss
        detection_rate_loss = detection_rate_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Segmentation guidance loss
        segmentation_guidance_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        if segmentation is not None:
            segmentation_guidance_loss = self.segmentation_guidance_loss(pred_heatmaps, segmentation)

        # Combine all losses with weights
        total_loss = (mse_loss +
                     self.separation_weight * separation_loss +
                     self.peak_separation_weight * peak_separation_loss +
                     self.edge_suppression_weight * edge_suppression_loss +
                     self.peak_enhancement_weight * peak_enhancement_loss +
                     self.peak_to_second_ratio_weight * peak_to_second_ratio_loss +
                     self.detection_rate_weight * detection_rate_loss +
                     self.segmentation_guidance_weight * segmentation_guidance_loss)

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("WARNING: NaN or Inf detected in loss calculation. Using fallback loss.")
            total_loss = mse_loss

        return total_loss, {
            'mse_loss': mse_loss.item(),
            'separation_loss': separation_loss.item(),
            'peak_separation_loss': peak_separation_loss.item(),
            'edge_suppression_loss': edge_suppression_loss.item(),
            'peak_enhancement_loss': peak_enhancement_loss.item(),
            'peak_to_second_ratio_loss': peak_to_second_ratio_loss.item(),
            'detection_rate_loss': detection_rate_loss.item(),
            'segmentation_guidance_loss': segmentation_guidance_loss.item() if segmentation is not None else 0.0
        }


# Improved model with enhanced modules and regularization
class ImprovedEnhancedChessBoardUNetV5(EnhancedChessBoardUNetV5):
    """
    Improved version of the EnhancedChessBoardUNetV5 with:
    - Improved peak sharpening module
    - Enhanced geometric refinement
    - Increased regularization
    - Batch normalization
    """
    def __init__(self, n_channels=3, dropout_rate=0.3, use_batch_norm=True, spatial_dropout=True):
        super(ImprovedEnhancedChessBoardUNetV5, self).__init__(n_channels, dropout_rate)

        # Replace peak sharpening module with improved version
        self.peak_sharpener = ImprovedPeakSharpeningModule()

        # Add batch normalization if requested
        self.use_batch_norm = use_batch_norm
        if use_batch_norm:
            self.bn_seg = nn.BatchNorm2d(1)  # For segmentation output
            self.bn_heatmap = nn.BatchNorm2d(4)  # For heatmap output

        # Use spatial dropout if requested
        self.spatial_dropout = spatial_dropout
        if spatial_dropout:
            self.spatial_dropout_layer = nn.Dropout2d(dropout_rate)

    def forward(self, x):
        # Get base outputs
        outputs = super(ImprovedEnhancedChessBoardUNetV5, self).forward(x)

        # Apply batch normalization if enabled
        if self.use_batch_norm:
            outputs['segmentation'] = self.bn_seg(outputs['segmentation'])
            outputs['corner_heatmaps'] = self.bn_heatmap(outputs['corner_heatmaps'])

        # Apply spatial dropout if enabled
        if self.spatial_dropout:
            outputs['corner_heatmaps'] = self.spatial_dropout_layer(outputs['corner_heatmaps'])

        return outputs


# Enhanced data augmentation
def get_enhanced_training_augmentation():
    """
    Enhanced training augmentation with more aggressive transformations
    to improve model generalization.
    """
    return A.Compose([
        # Spatial augmentations
        A.RandomResizedCrop(size=(256, 256), scale=(0.7, 1.0), p=1.0),
        A.Affine(
            scale=(0.8, 1.2),
            translate_percent=(0.12, 0.12),
            rotate=(-45, 45),
            shear=(-10, 10),
            p=0.9
        ),
        A.Perspective(scale=(0.05, 0.2), p=0.8),
        A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.6),
        A.ElasticTransform(alpha=1, sigma=50, p=0.4),

        # Color augmentations
        A.RandomBrightnessContrast(brightness_limit=0.4, contrast_limit=0.4, p=0.9),
        A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.7),
        A.CLAHE(clip_limit=4.0, p=0.5),
        A.RandomGamma(gamma_limit=(80, 120), p=0.6),

        # Noise and blur
        A.OneOf([
            A.GaussNoise(p=1.0),
            A.GaussianBlur(blur_limit=7, p=1.0),
            A.MotionBlur(blur_limit=7, p=1.0),
            # Skip image compression as it's causing compatibility issues
            A.Blur(blur_limit=5, p=1.0)  # Use another blur transform instead
        ], p=0.7),

        # Normalization
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def get_enhanced_validation_augmentation():
    """
    Enhanced validation augmentation with light transformations
    to better evaluate model robustness.
    """
    return A.Compose([
        A.Resize(height=256, width=256),
        A.Affine(
            scale=(0.9, 1.1),
            translate_percent=(0.05, 0.05),
            rotate=(-15, 15),
            p=0.5
        ),
        A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def train_model_improved(model, dataloaders, criterion_seg, criterion_heatmap, criterion_geometric,
                        optimizer, output_dir, num_epochs=40, heatmap_weight=1.5, geometric_weight=0.8,
                        save_interval=5, phase_name='', use_scheduler=True):
    """
    Improved training function with learning rate scheduling and gradient clipping.

    Args:
        model: Model to train
        dataloaders: Dictionary of dataloaders ('train', 'val')
        criterion_seg: Loss function for segmentation
        criterion_heatmap: Loss function for heatmaps
        criterion_geometric: Loss function for geometric consistency
        optimizer: Optimizer
        output_dir: Output directory
        num_epochs: Number of epochs
        heatmap_weight: Weight for heatmap loss
        geometric_weight: Weight for geometric loss
        save_interval: Interval to save model checkpoints
        phase_name: Name of the training phase
        use_scheduler: Whether to use learning rate scheduler
    """
    since = time.time()

    # Create directories for outputs
    checkpoints_dir = os.path.join(output_dir, 'checkpoints', 'v5.1')
    logs_dir = os.path.join(output_dir, 'logs', 'v5.1')
    vis_dir = os.path.join(output_dir, 'visualizations', 'v5.1')

    os.makedirs(checkpoints_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)

    print(f"Saving outputs to v5.1 folders in {output_dir}")

    # Initialize learning rate scheduler if requested
    if use_scheduler:
        # Use warm restart scheduler instead of ReduceLROnPlateau
        scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
            optimizer,
            T_0=10,  # Restart every 10 epochs
            T_mult=2,  # Double the restart period after each restart
            eta_min=optimizer.param_groups[0]['lr'] * 0.01  # Minimum LR is 1% of initial LR
        )
        print("Learning rate scheduler with warm restarts initialized")

    # Initialize best model and loss
    best_model_wts = model.state_dict()
    best_loss = float('inf')

    # Initialize history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_seg_loss': [],
        'val_seg_loss': [],
        'train_heatmap_loss': [],
        'val_heatmap_loss': [],
        'train_geometric_loss': [],
        'val_geometric_loss': [],
        'train_heatmap_components': [],
        'val_heatmap_components': [],
        'train_corner_confidence': [],
        'val_corner_confidence': [],
        'learning_rates': []
    }

    # Training loop
    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)

        # Update epoch for curriculum learning in model and loss function
        if hasattr(model, 'set_epoch'):
            model.set_epoch(epoch)
        if hasattr(criterion_heatmap, 'set_epoch'):
            criterion_heatmap.set_epoch(epoch)

        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode

            running_loss = 0.0
            running_seg_loss = 0.0
            running_heatmap_loss = 0.0
            running_geometric_loss = 0.0
            running_heatmap_components = {
                'mse_loss': 0.0,
                'separation_loss': 0.0,
                'peak_separation_loss': 0.0,
                'edge_suppression_loss': 0.0,
                'peak_enhancement_loss': 0.0,
                'peak_to_second_ratio_loss': 0.0,
                'detection_rate_loss': 0.0,
                'segmentation_guidance_loss': 0.0
            }
            running_corner_confidence = {
                'avg_peak_value': 0.0,
                'avg_peak_to_mean_ratio': 0.0,
                'avg_peak_to_second_ratio': 0.0,
                'detection_rate': 0.0
            }

            # Iterate over data
            for batch_idx, batch in enumerate(tqdm(dataloaders[phase])):
                # Skip None batches
                if batch is None:
                    print("Warning: Received None batch. Skipping.")
                    continue
                # Get inputs - handle both tensor and list cases
                if isinstance(batch['image'], torch.Tensor):
                    inputs = batch['image'].to(DEVICE)
                else:
                    # If it's a list, process each item individually
                    processed_inputs = []
                    for img in batch['image']:
                        # Convert numpy arrays to tensors if needed
                        if isinstance(img, np.ndarray):
                            img = torch.from_numpy(img).float()
                        # Move to device
                        if isinstance(img, torch.Tensor):
                            processed_inputs.append(img.to(DEVICE))
                        else:
                            print(f"Warning: Unexpected image type: {type(img)}. Skipping batch.")
                            continue

                    # Stack them if they have the same shape
                    if len(processed_inputs) > 0 and all(img.shape == processed_inputs[0].shape for img in processed_inputs):
                        inputs = torch.stack(processed_inputs)
                    else:
                        print(f"Warning: Images have different shapes or empty list. Skipping batch.")
                        continue

                # Get masks - handle both tensor and list cases
                if isinstance(batch['mask'], torch.Tensor):
                    masks = batch['mask'].to(DEVICE)
                else:
                    # If it's a list, process each item individually
                    processed_masks = []
                    for mask in batch['mask']:
                        # Convert numpy arrays to tensors if needed
                        if isinstance(mask, np.ndarray):
                            mask = torch.from_numpy(mask).float()
                        # Move to device
                        if isinstance(mask, torch.Tensor):
                            processed_masks.append(mask.to(DEVICE))
                        else:
                            print(f"Warning: Unexpected mask type: {type(mask)}. Skipping batch.")
                            continue

                    # Stack them if they have the same shape
                    if len(processed_masks) > 0 and all(mask.shape == processed_masks[0].shape for mask in processed_masks):
                        masks = torch.stack(processed_masks)
                    else:
                        print(f"Warning: Masks have different shapes or empty list. Skipping batch.")
                        continue

                # Get heatmaps (different datasets might have different keys)
                heatmap_key = None
                if 'heatmaps' in batch:
                    heatmap_key = 'heatmaps'
                elif 'corner_heatmaps' in batch:
                    heatmap_key = 'corner_heatmaps'
                else:
                    print(f"Neither 'heatmaps' nor 'corner_heatmaps' found in batch with keys: {batch.keys()}")
                    continue

                # Handle both tensor and list cases for heatmaps
                if isinstance(batch[heatmap_key], torch.Tensor):
                    heatmaps = batch[heatmap_key].to(DEVICE)
                else:
                    # If it's a list, process each item individually
                    processed_heatmaps = []
                    for hm in batch[heatmap_key]:
                        # Convert numpy arrays to tensors if needed
                        if isinstance(hm, np.ndarray):
                            hm = torch.from_numpy(hm).float()
                        # Move to device
                        if isinstance(hm, torch.Tensor):
                            processed_heatmaps.append(hm.to(DEVICE))
                        else:
                            print(f"Warning: Unexpected heatmap type: {type(hm)}. Skipping batch.")
                            continue

                    # Stack them if they have the same shape
                    if len(processed_heatmaps) > 0 and all(hm.shape == processed_heatmaps[0].shape for hm in processed_heatmaps):
                        heatmaps = torch.stack(processed_heatmaps)
                    else:
                        print(f"Warning: Heatmaps have different shapes or empty list. Skipping batch.")
                        continue

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    heatmap_outputs = outputs['corner_heatmaps']

                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    # Pass segmentation to heatmap loss for segmentation-guided corner detection
                    heatmap_loss, heatmap_components = criterion_heatmap(heatmap_outputs, heatmaps, seg_outputs)

                    # Pass heatmap loss to stabilized geometric loss if supported
                    if hasattr(criterion_geometric, 'forward') and len(inspect.signature(criterion_geometric.forward).parameters) > 1:
                        geometric_loss = criterion_geometric(heatmap_outputs, heatmap_loss)
                    else:
                        geometric_loss = criterion_geometric(heatmap_outputs)

                    # Combined loss with custom weights
                    loss = seg_loss + heatmap_weight * heatmap_loss + geometric_weight * geometric_loss

                    # Print loss values for debugging in first few batches
                    if batch_idx < 3 and epoch < 3:
                        print(f"Batch {batch_idx}, {phase} - Seg Loss: {seg_loss.item():.4f}, "
                              f"Heatmap Loss: {heatmap_loss.item():.4f}, "
                              f"Geometric Loss: {geometric_loss.item():.4f}, "
                              f"Total: {loss.item():.4f}")

                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        loss.backward()
                        # Apply gradient clipping
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()

                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_heatmap_loss += heatmap_loss.item() * inputs.size(0)
                running_geometric_loss += geometric_loss.item() * inputs.size(0)

                # Track heatmap loss components
                for key, value in heatmap_components.items():
                    running_heatmap_components[key] += value * inputs.size(0)

                # Calculate and track corner confidence metrics
                confidence_metrics = calculate_corner_confidence(heatmap_outputs)
                for key, value in confidence_metrics.items():
                    running_corner_confidence[key] += value * inputs.size(0)

            # Calculate epoch losses
            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_heatmap_loss = running_heatmap_loss / len(dataloaders[phase].dataset)
            epoch_geometric_loss = running_geometric_loss / len(dataloaders[phase].dataset)

            # Calculate average heatmap components
            epoch_heatmap_components = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_heatmap_components.items()
            }

            # Calculate average corner confidence metrics
            epoch_corner_confidence = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_corner_confidence.items()
            }

            # Print epoch losses
            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, '
                  f'Heatmap Loss: {epoch_heatmap_loss:.4f}, Geometric Loss: {epoch_geometric_loss:.4f}')

            # Print corner confidence metrics with more prominence
            print(f'=== {phase} Corner Confidence Metrics ===')
            for key, value in epoch_corner_confidence.items():
                print(f'  {key}: {value:.4f}')

            # Calculate overall confidence score (average of metrics)
            avg_confidence = sum(epoch_corner_confidence.values()) / len(epoch_corner_confidence)
            print(f'  Overall Confidence Score: {avg_confidence:.4f}')

            # Print heatmap components
            print(f'{phase} Heatmap Components:')
            for key, value in epoch_heatmap_components.items():
                print(f'  {key}: {value:.4f}')

            # Save history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_heatmap_loss'].append(epoch_heatmap_loss)
            history[f'{phase}_geometric_loss'].append(epoch_geometric_loss)
            history[f'{phase}_heatmap_components'].append(epoch_heatmap_components)
            history[f'{phase}_corner_confidence'].append(epoch_corner_confidence)

            # Save best model
            if phase == 'val' and epoch_loss < best_loss:
                best_loss = epoch_loss
                best_model_wts = model.state_dict().copy()
                torch.save(best_model_wts, os.path.join(checkpoints_dir, f'best_model_{phase_name}.pth'))
                print(f'New best model saved with loss: {best_loss:.4f}')

        # Update learning rate if using scheduler
        if use_scheduler:
            # For CosineAnnealingWarmRestarts, step is called after each epoch without parameters
            scheduler.step()
            # Record current learning rate
            current_lr = optimizer.param_groups[0]['lr']
            history['learning_rates'].append(current_lr)
            print(f'Current learning rate: {current_lr:.6f}')

        # Save checkpoint at specified intervals
        if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': epoch_loss,
                'history': history
            }, os.path.join(checkpoints_dir, f'checkpoint_epoch_{epoch+1}_{phase_name}.pth'))

            # Save history to JSON
            with open(os.path.join(logs_dir, f'history_epoch_{epoch+1}_{phase_name}.json'), 'w') as f:
                # Convert numpy values to Python types for JSON serialization
                json_history = {}
                for key, value in history.items():
                    if isinstance(value[0], dict):
                        json_history[key] = [{k: float(v) for k, v in d.items()} for d in value]
                    else:
                        json_history[key] = [float(v) for v in value]

                json.dump(json_history, f, indent=4)

            # Plot losses
            plt.figure(figsize=(15, 5))

            # Plot total loss
            plt.subplot(1, 3, 1)
            plt.plot(history['train_loss'], label='Train')
            plt.plot(history['val_loss'], label='Validation')
            plt.title('Total Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot segmentation loss
            plt.subplot(1, 3, 2)
            plt.plot(history['train_seg_loss'], label='Train')
            plt.plot(history['val_seg_loss'], label='Validation')
            plt.title('Segmentation Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot heatmap loss
            plt.subplot(1, 3, 3)
            plt.plot(history['train_heatmap_loss'], label='Train')
            plt.plot(history['val_heatmap_loss'], label='Validation')
            plt.title('Heatmap Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'losses_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot corner confidence metrics
            plt.figure(figsize=(15, 12))

            confidence_keys = list(history['train_corner_confidence'][0].keys())
            for i, key in enumerate(confidence_keys):
                plt.subplot(2, 3, i+1)
                plt.plot(
                    [conf[key] for conf in history['train_corner_confidence']],
                    label='Train'
                )
                plt.plot(
                    [conf[key] for conf in history['val_corner_confidence']],
                    label='Validation'
                )
                plt.title(key)
                plt.xlabel('Epoch')
                plt.ylabel('Value')
                plt.legend()

            # Add overall confidence score plot
            plt.subplot(2, 3, 5)
            train_overall = [sum(conf.values()) / len(conf) for conf in history['train_corner_confidence']]
            val_overall = [sum(conf.values()) / len(conf) for conf in history['val_corner_confidence']]
            plt.plot(train_overall, label='Train')
            plt.plot(val_overall, label='Validation')
            plt.title('Overall Confidence Score')
            plt.xlabel('Epoch')
            plt.ylabel('Value')
            plt.legend()

            # Plot learning rate if using scheduler
            if use_scheduler:
                plt.subplot(2, 3, 6)
                plt.plot(history['learning_rates'])
                plt.title('Learning Rate')
                plt.xlabel('Epoch')
                plt.ylabel('Learning Rate')

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'metrics_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

        print()

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')

    # Load best model weights
    model.load_state_dict(best_model_wts)

    return model, history


def create_expanded_validation_set_improved(data_dir, annotation_file, fold_idx=0, n_folds=5):
    """
    Create an expanded validation set using k-fold cross-validation with enhanced augmentations.

    Args:
        data_dir: Directory containing the images
        annotation_file: Path to the annotation file
        fold_idx: Index of the fold to use as validation
        n_folds: Number of folds

    Returns:
        train_dataset: Training dataset
        val_dataset: Validation dataset
    """
    # Load dataset
    full_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_enhanced_training_augmentation()
    )

    # Create indices for k-fold cross-validation
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

    # Get train and validation indices
    indices = list(range(len(full_dataset)))
    splits = list(kf.split(indices))
    train_indices, val_indices = splits[fold_idx]

    # Create train and validation datasets
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)

    # Create multiple validation datasets with different augmentations
    val_datasets = []

    # Original validation set
    val_dataset_orig = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_enhanced_validation_augmentation()
    )
    val_dataset_orig = torch.utils.data.Subset(val_dataset_orig, val_indices)
    val_datasets.append(val_dataset_orig)

    # Create additional validation sets with different augmentations
    for i in range(3):  # Create 3 additional validation sets
        # Create custom augmentation for this validation set
        custom_aug = A.Compose([
            A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
            A.Affine(
                scale=(0.9 + 0.1*i, 1.1 + 0.1*i),
                translate_percent=(0.05*i, 0.05*i),
                rotate=(-15*i, 15*i),
                shear=(-5*i, 5*i),
                p=0.8
            ),
            A.RandomBrightnessContrast(brightness_limit=0.1*i, contrast_limit=0.1*i, p=0.7),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))

        val_dataset_aug = RealChessBoardDataset(
            data_dir=data_dir,
            annotation_file=annotation_file,
            transform=custom_aug
        )
        val_dataset_aug = torch.utils.data.Subset(val_dataset_aug, val_indices)
        val_datasets.append(val_dataset_aug)

    # Combine validation datasets
    val_dataset = ConcatDataset(val_datasets)

    return train_dataset, val_dataset


# Custom collate function to handle different batch sizes
def custom_collate_fn(batch):
    """
    Custom collate function to handle batches with different sizes.
    """
    # Filter out None values
    batch = [item for item in batch if item is not None]
    if len(batch) == 0:
        return None

    # Create a batch dictionary
    batch_dict = {}
    for key in batch[0].keys():
        batch_dict[key] = [item[key] for item in batch]

    # Stack tensors with error handling
    for key in batch_dict.keys():
        if isinstance(batch_dict[key][0], torch.Tensor):
            try:
                # Check if all tensors have the same shape
                shapes = [t.shape for t in batch_dict[key]]
                if all(s == shapes[0] for s in shapes):
                    batch_dict[key] = torch.stack(batch_dict[key])
                else:
                    # If tensors have different shapes, keep them as a list
                    print(f"Warning: Tensors with key '{key}' have different shapes: {shapes}. Keeping as list.")
            except Exception as e:
                print(f"Warning: Could not stack tensors with key '{key}': {e}. Keeping as list.")

    return batch_dict


def main():
    """
    Main function to train the improved v5.1 model with curriculum learning.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train improved v5.1 chess board detection model with curriculum learning')
    parser.add_argument('--data_dir', type=str, default=os.path.join(DATA_DIR, 'real'),
                        help='Data directory')
    parser.add_argument('--annotation_file', type=str, default=os.path.join(DATA_DIR, 'real_annotations.json'),
                        help='Annotation file')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--lr_phase1', type=float, default=0.001, help='Learning rate for phase 1')
    parser.add_argument('--lr_phase2', type=float, default=0.0005, help='Learning rate for phase 2')
    parser.add_argument('--epochs_phase1', type=int, default=40, help='Number of epochs for phase 1')
    parser.add_argument('--epochs_phase2', type=int, default=80, help='Number of epochs for phase 2')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--dropout_rate', type=float, default=0.3, help='Dropout rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_interval', type=int, default=5, help='Interval to save model checkpoints')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--continue_from_v4', action='store_true', help='Continue training from v4 checkpoint')
    parser.add_argument('--continue_from_v5', action='store_true', help='Continue training from v5 checkpoint')
    parser.add_argument('--continue_from_v5_1', action='store_true', help='Continue training from v5.1 checkpoint')
    parser.add_argument('--from_scratch', action='store_true', help='Train the model from scratch without loading any pre-trained weights')
    parser.add_argument('--skip_phase1', action='store_true', help='Skip phase 1 and go directly to phase 2')
    parser.add_argument('--fold_idx', type=int, default=0, help='Index of the fold to use as validation')
    parser.add_argument('--n_folds', type=int, default=5, help='Number of folds for cross-validation')
    parser.add_argument('--use_scheduler', action='store_true', help='Use learning rate scheduler')
    args = parser.parse_args()

    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")

    # Create expanded validation set
    print("Creating expanded validation set with enhanced augmentations...")
    train_dataset, val_dataset = create_expanded_validation_set_improved(
        data_dir=args.data_dir,
        annotation_file=args.annotation_file,
        fold_idx=args.fold_idx,
        n_folds=args.n_folds
    )

    # Create data loaders with custom collate function
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        collate_fn=custom_collate_fn
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        collate_fn=custom_collate_fn
    )

    dataloaders = {
        'train': train_loader,
        'val': val_loader
    }

    print(f"Train dataset size: {len(train_dataset)}")
    print(f"Validation dataset size: {len(val_dataset)}")

    # Initialize improved model
    print("Initializing improved v5.1 model...")
    model = ImprovedEnhancedChessBoardUNetV5(
        n_channels=3,
        dropout_rate=args.dropout_rate,
        use_batch_norm=True,
        spatial_dropout=True
    )

    # Load from checkpoint if requested
    if args.continue_from_v4:
        v4_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v4')
        best_model_path = os.path.join(v4_checkpoints_dir, 'best_model.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v4 best model: {best_model_path}")
            # Load state dict into base model
            model.base_model.load_state_dict(torch.load(best_model_path, map_location=device))
            print("Loaded v4 best model into base model")
        else:
            print(f"Warning: v4 best model not found at {best_model_path}")

    elif args.continue_from_v5:
        v5_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v5')
        best_model_path = os.path.join(v5_checkpoints_dir, 'best_model_phase1.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v5 best model: {best_model_path}")
            # Create a temporary EnhancedChessBoardUNetV5 to load the state dict
            from models.enhanced_unet_v5 import EnhancedChessBoardUNetV5 as TempModelV5
            temp_model = TempModelV5(n_channels=3, dropout_rate=args.dropout_rate)
            temp_model.load_state_dict(torch.load(best_model_path, map_location=device))

            # Copy the base model weights
            model.base_model.load_state_dict(temp_model.base_model.state_dict())
            print("Loaded v5 best model into base model")
        else:
            print(f"Warning: v5 best model not found at {best_model_path}")

    # Add option to continue from v5.1
    elif args.continue_from_v5_1:
        v5_1_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v5.1')
        best_model_path = os.path.join(v5_1_checkpoints_dir, 'best_model_phase1.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v5.1 best model: {best_model_path}")
            # Create a temporary ImprovedEnhancedChessBoardUNetV5 to load the state dict
            from models.improved_enhanced_unet_v5 import ImprovedEnhancedChessBoardUNetV5 as TempModel
            temp_model = TempModel(n_channels=3, dropout_rate=args.dropout_rate)
            temp_model.load_state_dict(torch.load(best_model_path, map_location=device))

            # Copy the base model weights
            model.base_model.load_state_dict(temp_model.base_model.state_dict())
            print("Loaded v5.1 best model into base model")
        else:
            print(f"Warning: v5.1 best model not found at {best_model_path}")

    model = model.to(device)
    print(f"Model moved to {device}")

    # Define loss functions
    criterion_seg = EnhancedDiceLoss()
    criterion_geometric = StabilizedGeometricLoss(base_weight=0.8, max_weight=2.0)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Phase 1: Focus on peak-to-second ratio and detection rate
    if not args.skip_phase1:
        print("\n=== Phase 1: Focus on peak-to-second ratio and detection rate ===")

        # Phase 1 configuration
        criterion_heatmap_phase1 = EnhancedCornerFocusedHeatmapLossV5(
            separation_weight=1.0,
            peak_separation_weight=0.8,
            edge_suppression_weight=1.0,
            peak_enhancement_weight=1.0,
            peak_to_second_ratio_weight=15.0,  # Higher weight for peak-to-second ratio
            detection_rate_weight=8.0,         # High weight for detection rate
            segmentation_guidance_weight=2.0,  # Higher weight for segmentation guidance
            use_curriculum=True                # Use curriculum learning for peak-to-second ratio
        )

        optimizer_phase1 = optim.AdamW(
            model.parameters(),
            lr=args.lr_phase1,
            weight_decay=args.weight_decay,
            amsgrad=True
        )

        # Train phase 1
        model, history_phase1 = train_model_improved(
            model=model,
            dataloaders=dataloaders,
            criterion_seg=criterion_seg,
            criterion_heatmap=criterion_heatmap_phase1,
            criterion_geometric=criterion_geometric,
            optimizer=optimizer_phase1,
            output_dir=args.output_dir,
            num_epochs=args.epochs_phase1,
            heatmap_weight=1.5,
            geometric_weight=0.8,
            save_interval=args.save_interval,
            phase_name='phase1',
            use_scheduler=args.use_scheduler
        )

    # Phase 2: Fine-tune with balanced weights
    print("\n=== Phase 2: Fine-tune with balanced weights ===")

    # Phase 2 configuration
    criterion_heatmap_phase2 = EnhancedCornerFocusedHeatmapLossV5(
        separation_weight=0.8,
        peak_separation_weight=0.6,
        edge_suppression_weight=0.8,
        peak_enhancement_weight=0.8,
        peak_to_second_ratio_weight=10.0,   # Slightly reduced from phase 1
        detection_rate_weight=5.0,          # Reduced from phase 1
        segmentation_guidance_weight=1.5,   # Slightly reduced from phase 1
        use_curriculum=True                 # Continue using curriculum learning
    )

    optimizer_phase2 = optim.AdamW(
        model.parameters(),
        lr=args.lr_phase2,  # Lower learning rate for fine-tuning
        weight_decay=args.weight_decay,
        amsgrad=True
    )

    # Train phase 2
    model, history_phase2 = train_model_improved(
        model=model,
        dataloaders=dataloaders,
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap_phase2,
        criterion_geometric=criterion_geometric,
        optimizer=optimizer_phase2,
        output_dir=args.output_dir,
        num_epochs=args.epochs_phase2,
        heatmap_weight=1.5,
        geometric_weight=0.8,
        save_interval=args.save_interval,
        phase_name='phase2',
        use_scheduler=args.use_scheduler
    )

    print("Training completed!")


if __name__ == "__main__":
    main()
