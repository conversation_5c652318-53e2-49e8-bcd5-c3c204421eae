PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_breakthrough_v5_fixed.py
🚀 BREAKTHROUGH U-NET V5 TRAINING (FIXED)
============================================================
🎯 TARGET: Exceed V4's 0.8721 Dice and achieve 0.95+ performance!
🏆 V4 Baseline: 0.8721 Dice
🚀 V5 Target: 0.9500 Dice
Using device: cuda
Creating dataloaders...
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Creating Breakthrough U-Net V5...
V5 Model parameters: 4,290,977
Efficiency vs V4: 1.30x
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v5_fixed.py:269: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
🚀 Starting V5 training for 100 epochs...

🔥 Epoch 1/100
🚀 V5 Training:   0%|                                                               | 0/21 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  image = torch.load(image_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  mask = torch.load(mask_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v5_fixed.py:116: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
🚀 V5 Training: 100%|████████████████| 21/21 [00:06<00:00,  3.14it/s, Loss=1.5055, Dice=0.3057, IoU=0.1805]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  8.27it/s, Loss=0.8739, Dice=0.7635, IoU=0.6174] 
Train - Loss: 11.0309, Dice: 0.5921, IoU: 0.4324
Val   - Loss: 13.6527, Dice: 0.6005, IoU: 0.4379
LR: 0.000032
✅ New best model saved! Val Dice: 0.6005

🔥 Epoch 2/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.5572, Dice=0.8146, IoU=0.6872] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.24it/s, Loss=0.8111, Dice=0.7635, IoU=0.6174] 
Train - Loss: 10.8339, Dice: 0.6273, IoU: 0.4620
Val   - Loss: 12.1108, Dice: 0.6142, IoU: 0.4504
LR: 0.000032
✅ New best model saved! Val Dice: 0.6142

🔥 Epoch 3/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=1.2029, Dice=0.4866, IoU=0.3216] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.21it/s, Loss=0.7258, Dice=0.7634, IoU=0.6174] 
Train - Loss: 10.0813, Dice: 0.6187, IoU: 0.4517
Val   - Loss: 8.2541, Dice: 0.6165, IoU: 0.4530
LR: 0.000032
✅ New best model saved! Val Dice: 0.6165

🔥 Epoch 4/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.1295, Dice=0.9635, IoU=0.9296] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.94it/s, Loss=0.6799, Dice=0.7634, IoU=0.6174] 
Train - Loss: 4.6290, Dice: 0.6356, IoU: 0.4748
Val   - Loss: 3.9326, Dice: 0.6153, IoU: 0.4502
LR: 0.000033

🔥 Epoch 5/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=1.2546, Dice=0.4589, IoU=0.2978] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.23it/s, Loss=0.6762, Dice=0.7635, IoU=0.6174] 
Train - Loss: 2.7708, Dice: 0.6158, IoU: 0.4503
Val   - Loss: 2.5202, Dice: 0.6203, IoU: 0.4551
LR: 0.000033
✅ New best model saved! Val Dice: 0.6203

🔥 Epoch 6/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=1.5208, Dice=0.3057, IoU=0.1805] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.27it/s, Loss=0.6957, Dice=0.7635, IoU=0.6174] 
Train - Loss: 1.8382, Dice: 0.6168, IoU: 0.4520
Val   - Loss: 1.9443, Dice: 0.6251, IoU: 0.4601
LR: 0.000034
✅ New best model saved! Val Dice: 0.6251

🔥 Epoch 7/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.8244, Dice=0.6882, IoU=0.5247] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.20it/s, Loss=0.6766, Dice=0.7637, IoU=0.6177] 
Train - Loss: 1.4623, Dice: 0.6311, IoU: 0.4662
Val   - Loss: 1.8474, Dice: 0.6231, IoU: 0.4581
LR: 0.000034

🔥 Epoch 8/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.2369, Dice=0.9068, IoU=0.8295] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.14it/s, Loss=0.6310, Dice=0.7653, IoU=0.6198] 
Train - Loss: 1.2761, Dice: 0.6395, IoU: 0.4761
Val   - Loss: 1.3237, Dice: 0.6252, IoU: 0.4613
LR: 0.000035
✅ New best model saved! Val Dice: 0.6252

🔥 Epoch 9/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.8829, Dice=0.6329, IoU=0.4630] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.21it/s, Loss=0.6264, Dice=0.7679, IoU=0.6233] 
Train - Loss: 1.0353, Dice: 0.6367, IoU: 0.4692
Val   - Loss: 1.1144, Dice: 0.6331, IoU: 0.4698
LR: 0.000036
✅ New best model saved! Val Dice: 0.6331

🔥 Epoch 10/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.7872, Dice=0.6476, IoU=0.4789] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.27it/s, Loss=0.5679, Dice=0.7858, IoU=0.6472] 
Train - Loss: 0.9892, Dice: 0.6387, IoU: 0.4736
Val   - Loss: 1.5050, Dice: 0.6397, IoU: 0.4776
LR: 0.000036
✅ New best model saved! Val Dice: 0.6397

🔥 Epoch 11/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.5251, Dice=0.9131, IoU=0.8400] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.36it/s, Loss=0.5082, Dice=0.9167, IoU=0.8461] 
Train - Loss: 1.4402, Dice: 0.6944, IoU: 0.5408
Val   - Loss: 0.9974, Dice: 0.7324, IoU: 0.5897
LR: 0.000037
✅ New best model saved! Val Dice: 0.7324

🔥 Epoch 12/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=1.2416, Dice=0.4857, IoU=0.3208] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.29it/s, Loss=0.3544, Dice=0.9375, IoU=0.8823] 
Train - Loss: 0.7716, Dice: 0.8328, IoU: 0.7247
Val   - Loss: 0.8874, Dice: 0.7635, IoU: 0.6293
LR: 0.000038
✅ New best model saved! Val Dice: 0.7635

🔥 Epoch 13/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.3409, Dice=0.9276, IoU=0.8649] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.15it/s, Loss=0.3686, Dice=0.9235, IoU=0.8579] 
Train - Loss: 0.6750, Dice: 0.8477, IoU: 0.7408
Val   - Loss: 0.7651, Dice: 0.8351, IoU: 0.7226
LR: 0.000039
✅ New best model saved! Val Dice: 0.8351

🔥 Epoch 14/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.89it/s, Loss=0.4462, Dice=0.8340, IoU=0.7152] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.15it/s, Loss=0.1921, Dice=0.9419, IoU=0.8902] 
Train - Loss: 0.5341, Dice: 0.8856, IoU: 0.7978
Val   - Loss: 0.7857, Dice: 0.7871, IoU: 0.6582
LR: 0.000040

🔥 Epoch 15/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.88it/s, Loss=0.1798, Dice=0.9662, IoU=0.9346] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.31it/s, Loss=0.2333, Dice=0.9305, IoU=0.8701] 
Train - Loss: 0.4271, Dice: 0.8897, IoU: 0.8038
Val   - Loss: 0.7316, Dice: 0.8294, IoU: 0.7137
LR: 0.000042

🔥 Epoch 16/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.1627, Dice=0.9555, IoU=0.9149] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.28it/s, Loss=0.3018, Dice=0.8997, IoU=0.8177] 
Train - Loss: 0.3722, Dice: 0.8914, IoU: 0.8076
Val   - Loss: 0.5480, Dice: 0.8463, IoU: 0.7380
LR: 0.000043
✅ New best model saved! Val Dice: 0.8463

🔥 Epoch 17/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.3723, Dice=0.8768, IoU=0.7806] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.34it/s, Loss=0.1717, Dice=0.9429, IoU=0.8920] 
Train - Loss: 0.3239, Dice: 0.9015, IoU: 0.8222
Val   - Loss: 0.5719, Dice: 0.8251, IoU: 0.7069
LR: 0.000044

🔥 Epoch 18/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.2540, Dice=0.9133, IoU=0.8404] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.18it/s, Loss=0.2246, Dice=0.9267, IoU=0.8634] 
Train - Loss: 0.3390, Dice: 0.8956, IoU: 0.8134
Val   - Loss: 0.5198, Dice: 0.8430, IoU: 0.7333
LR: 0.000046

🔥 Epoch 19/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.1783, Dice=0.9455, IoU=0.8967] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.36it/s, Loss=0.3984, Dice=0.8621, IoU=0.7577] 
Train - Loss: 0.3039, Dice: 0.8974, IoU: 0.8163
Val   - Loss: 0.5066, Dice: 0.8520, IoU: 0.7479
LR: 0.000048
✅ New best model saved! Val Dice: 0.8520

🔥 Epoch 20/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1825, Dice=0.9350, IoU=0.8779]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.20it/s, Loss=0.1523, Dice=0.9446, IoU=0.8950] 
Train - Loss: 0.3279, Dice: 0.8890, IoU: 0.8035
Val   - Loss: 1.0696, Dice: 0.8097, IoU: 0.6864
LR: 0.000049

🔥 Epoch 21/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1505, Dice=0.9675, IoU=0.9371] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.15it/s, Loss=0.3442, Dice=0.8819, IoU=0.7887] 
Train - Loss: 0.3563, Dice: 0.8756, IoU: 0.7874
Val   - Loss: 0.4768, Dice: 0.8575, IoU: 0.7557
LR: 0.000051
✅ New best model saved! Val Dice: 0.8575

🔥 Epoch 22/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.2312, Dice=0.9137, IoU=0.8411] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.06it/s, Loss=0.2639, Dice=0.9097, IoU=0.8343] 
Train - Loss: 0.2884, Dice: 0.9026, IoU: 0.8247
Val   - Loss: 0.5166, Dice: 0.8535, IoU: 0.7497
LR: 0.000053

🔥 Epoch 23/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.2031, Dice=0.9301, IoU=0.8694] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.18it/s, Loss=0.1226, Dice=0.9558, IoU=0.9153] 
Train - Loss: 0.2957, Dice: 0.8963, IoU: 0.8156
Val   - Loss: 0.6150, Dice: 0.8222, IoU: 0.7061
LR: 0.000055

🔥 Epoch 24/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.1509, Dice=0.9496, IoU=0.9040] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.19it/s, Loss=0.2120, Dice=0.9270, IoU=0.8639] 
Train - Loss: 0.2730, Dice: 0.9065, IoU: 0.8320
Val   - Loss: 0.4908, Dice: 0.8549, IoU: 0.7517
LR: 0.000057

🔥 Epoch 25/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.1159, Dice=0.9677, IoU=0.9374] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.02it/s, Loss=0.1150, Dice=0.9618, IoU=0.9264] 
Train - Loss: 0.2407, Dice: 0.9221, IoU: 0.8570
Val   - Loss: 0.5350, Dice: 0.8495, IoU: 0.7434
LR: 0.000059

🔥 Epoch 26/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.2298, Dice=0.9388, IoU=0.8846] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.01it/s, Loss=0.1531, Dice=0.9510, IoU=0.9065] 
Train - Loss: 0.2451, Dice: 0.9268, IoU: 0.8644
Val   - Loss: 0.4668, Dice: 0.8600, IoU: 0.7582
LR: 0.000061
✅ New best model saved! Val Dice: 0.8600

🔥 Epoch 27/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.2463, Dice=0.9562, IoU=0.9162] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.13it/s, Loss=0.2131, Dice=0.9300, IoU=0.8691] 
Train - Loss: 0.2687, Dice: 0.9279, IoU: 0.8668
Val   - Loss: 0.5445, Dice: 0.8699, IoU: 0.7736
LR: 0.000063
✅ New best model saved! Val Dice: 0.8699

🔥 Epoch 28/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.2761, Dice=0.9426, IoU=0.8915] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.16it/s, Loss=0.1901, Dice=0.9404, IoU=0.8875] 
Train - Loss: 0.2712, Dice: 0.9258, IoU: 0.8628
Val   - Loss: 0.3300, Dice: 0.8950, IoU: 0.8108
LR: 0.000066
🎉 V4 EXCEEDED! Val Dice: 0.8950 > 0.8721
✅ New best model saved! Val Dice: 0.8950

🔥 Epoch 29/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1842, Dice=0.9464, IoU=0.8983] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.29it/s, Loss=0.1941, Dice=0.9384, IoU=0.8840] 
Train - Loss: 0.2350, Dice: 0.9297, IoU: 0.8696
Val   - Loss: 0.3141, Dice: 0.8996, IoU: 0.8183
LR: 0.000068
✅ New best model saved! Val Dice: 0.8996

🔥 Epoch 30/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.1518, Dice=0.9527, IoU=0.9096] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.19it/s, Loss=0.1564, Dice=0.9507, IoU=0.9060] 
Train - Loss: 0.2172, Dice: 0.9324, IoU: 0.8750
Val   - Loss: 0.3333, Dice: 0.8934, IoU: 0.8092
LR: 0.000070

🔥 Epoch 31/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.1055, Dice=0.9713, IoU=0.9442] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.17it/s, Loss=0.1159, Dice=0.9634, IoU=0.9293] 
Train - Loss: 0.2035, Dice: 0.9328, IoU: 0.8761
Val   - Loss: 0.3641, Dice: 0.8812, IoU: 0.7917
LR: 0.000073

🔥 Epoch 32/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.0200, Dice=0.9929, IoU=0.9859] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.16it/s, Loss=0.1173, Dice=0.9629, IoU=0.9285] 
Train - Loss: 0.1746, Dice: 0.9425, IoU: 0.8925
Val   - Loss: 0.3407, Dice: 0.8878, IoU: 0.8017
LR: 0.000076

🔥 Epoch 33/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.87it/s, Loss=0.0936, Dice=0.9686, IoU=0.9392] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.94it/s, Loss=0.1016, Dice=0.9671, IoU=0.9363] 
Train - Loss: 0.2131, Dice: 0.9271, IoU: 0.8658
Val   - Loss: 0.3457, Dice: 0.8828, IoU: 0.7943
LR: 0.000078

🔥 Epoch 34/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=1.1964, Dice=0.9192, IoU=0.8505] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.02it/s, Loss=0.2528, Dice=0.9132, IoU=0.8403] 
Train - Loss: 0.2145, Dice: 0.9427, IoU: 0.8931
Val   - Loss: 0.3980, Dice: 0.8928, IoU: 0.8089
LR: 0.000081

🔥 Epoch 35/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.2796, Dice=0.9016, IoU=0.8208] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.22it/s, Loss=0.0871, Dice=0.9692, IoU=0.9402] 
Train - Loss: 0.1762, Dice: 0.9451, IoU: 0.8969
Val   - Loss: 0.3656, Dice: 0.8738, IoU: 0.7805
LR: 0.000084

🔥 Epoch 36/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.1702, Dice=0.9426, IoU=0.8915] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.26it/s, Loss=0.1148, Dice=0.9602, IoU=0.9234] 
Train - Loss: 0.1880, Dice: 0.9362, IoU: 0.8822
Val   - Loss: 0.5039, Dice: 0.8398, IoU: 0.7308
LR: 0.000087

🔥 Epoch 37/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.77it/s, Loss=0.1303, Dice=0.9512, IoU=0.9069] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.92it/s, Loss=0.0997, Dice=0.9692, IoU=0.9402] 
Train - Loss: 0.3087, Dice: 0.8905, IoU: 0.8107
Val   - Loss: 0.2821, Dice: 0.9022, IoU: 0.8240
LR: 0.000090
✅ New best model saved! Val Dice: 0.9022

🔥 Epoch 38/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.77it/s, Loss=0.4540, Dice=0.7906, IoU=0.6537] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.21it/s, Loss=0.0789, Dice=0.9716, IoU=0.9447] 
Train - Loss: 0.1747, Dice: 0.9392, IoU: 0.8877
Val   - Loss: 0.3682, Dice: 0.8744, IoU: 0.7818
LR: 0.000093

🔥 Epoch 39/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0570, Dice=0.9817, IoU=0.9641] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.90it/s, Loss=0.5212, Dice=0.8211, IoU=0.6965] 
Train - Loss: 0.2764, Dice: 0.9023, IoU: 0.8277
Val   - Loss: 0.3894, Dice: 0.8658, IoU: 0.7720
LR: 0.000096

🔥 Epoch 40/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.74it/s, Loss=0.1140, Dice=0.9624, IoU=0.9276] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.73it/s, Loss=0.1863, Dice=0.9364, IoU=0.8804] 
Train - Loss: 0.1830, Dice: 0.9368, IoU: 0.8830
Val   - Loss: 0.2566, Dice: 0.9100, IoU: 0.8363
LR: 0.000099
✅ New best model saved! Val Dice: 0.9100

🔥 Epoch 41/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.78it/s, Loss=0.4260, Dice=0.8697, IoU=0.7695] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.53it/s, Loss=0.0775, Dice=0.9730, IoU=0.9474] 
Train - Loss: 0.1809, Dice: 0.9392, IoU: 0.8870
Val   - Loss: 0.3518, Dice: 0.8825, IoU: 0.7934
LR: 0.000103

🔥 Epoch 42/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.76it/s, Loss=0.0970, Dice=0.9639, IoU=0.9303] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.82it/s, Loss=0.6937, Dice=0.7569, IoU=0.6088] 
Train - Loss: 0.3670, Dice: 0.8655, IoU: 0.7759
Val   - Loss: 0.4789, Dice: 0.8383, IoU: 0.7354
LR: 0.000106

🔥 Epoch 43/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.0833, Dice=0.9728, IoU=0.9471] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.91it/s, Loss=0.1242, Dice=0.9592, IoU=0.9215] 
Train - Loss: 0.4030, Dice: 0.8678, IoU: 0.7785
Val   - Loss: 0.2479, Dice: 0.9115, IoU: 0.8386
LR: 0.000109
✅ New best model saved! Val Dice: 0.9115

🔥 Epoch 44/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.0337, Dice=0.9871, IoU=0.9746] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.94it/s, Loss=0.1066, Dice=0.9651, IoU=0.9326] 
Train - Loss: 0.1655, Dice: 0.9419, IoU: 0.8917
Val   - Loss: 0.3965, Dice: 0.9015, IoU: 0.8228
LR: 0.000113

🔥 Epoch 45/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.72it/s, Loss=0.0887, Dice=0.9704, IoU=0.9426] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.86it/s, Loss=0.1758, Dice=0.9408, IoU=0.8883] 
Train - Loss: 0.2565, Dice: 0.9336, IoU: 0.8781
Val   - Loss: 0.6526, Dice: 0.8996, IoU: 0.8227
LR: 0.000117

🔥 Epoch 46/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.77it/s, Loss=0.1605, Dice=0.9447, IoU=0.8952] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.03it/s, Loss=0.1651, Dice=0.9472, IoU=0.8997] 
Train - Loss: 0.2281, Dice: 0.9534, IoU: 0.9114
Val   - Loss: 0.2479, Dice: 0.9213, IoU: 0.8545
LR: 0.000120
✅ New best model saved! Val Dice: 0.9213

🔥 Epoch 47/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.79it/s, Loss=0.0900, Dice=0.9700, IoU=0.9418] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.11it/s, Loss=0.1145, Dice=0.9633, IoU=0.9293] 
Train - Loss: 0.2418, Dice: 0.9370, IoU: 0.8823
Val   - Loss: 0.4282, Dice: 0.8839, IoU: 0.7942
LR: 0.000124

🔥 Epoch 48/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1160, Dice=0.9578, IoU=0.9191] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.14it/s, Loss=0.4209, Dice=0.8569, IoU=0.7496] 
Train - Loss: 0.2270, Dice: 0.9402, IoU: 0.8883
Val   - Loss: 0.3840, Dice: 0.8828, IoU: 0.7951
LR: 0.000128

🔥 Epoch 49/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.8507, Dice=0.7119, IoU=0.5527] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.88it/s, Loss=0.8370, Dice=0.7047, IoU=0.5440] 
Train - Loss: 0.3075, Dice: 0.9009, IoU: 0.8272
Val   - Loss: 0.6427, Dice: 0.7925, IoU: 0.6653
LR: 0.000132

🔥 Epoch 50/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.3546, Dice=0.8808, IoU=0.7871] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.13it/s, Loss=0.2435, Dice=0.9164, IoU=0.8457] 
Train - Loss: 0.3969, Dice: 0.8741, IoU: 0.7835
Val   - Loss: 0.3575, Dice: 0.8814, IoU: 0.7918
LR: 0.000135

🔥 Epoch 51/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0705, Dice=0.9805, IoU=0.9617] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.15it/s, Loss=0.1793, Dice=0.9387, IoU=0.8845] 
Train - Loss: 0.2051, Dice: 0.9371, IoU: 0.8831
Val   - Loss: 0.9835, Dice: 0.8857, IoU: 0.8016
LR: 0.000139

🔥 Epoch 52/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0560, Dice=0.9876, IoU=0.9755]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.00it/s, Loss=0.0646, Dice=0.9786, IoU=0.9581] 
Train - Loss: 0.5300, Dice: 0.9419, IoU: 0.8930
Val   - Loss: 0.4063, Dice: 0.8852, IoU: 0.7977
LR: 0.000143

🔥 Epoch 53/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.1416, Dice=0.9442, IoU=0.8943] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.01it/s, Loss=0.3772, Dice=0.8695, IoU=0.7691] 
Train - Loss: 0.1582, Dice: 0.9481, IoU: 0.9024
Val   - Loss: 0.3460, Dice: 0.8799, IoU: 0.7946
LR: 0.000148

🔥 Epoch 54/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.4592, Dice=0.8285, IoU=0.7073] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.30it/s, Loss=0.3850, Dice=0.8679, IoU=0.7667] 
Train - Loss: 0.1690, Dice: 0.9447, IoU: 0.8976
Val   - Loss: 0.3167, Dice: 0.8876, IoU: 0.8052
LR: 0.000152


🔥 Epoch 55/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.1056, Dice=0.9599, IoU=0.9229] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.99it/s, Loss=0.0649, Dice=0.9773, IoU=0.9556] 
Train - Loss: 0.3805, Dice: 0.8760, IoU: 0.7893
Val   - Loss: 0.3656, Dice: 0.8830, IoU: 0.7939
LR: 0.000156

🔥 Epoch 56/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1755, Dice=0.9409, IoU=0.8885] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.03it/s, Loss=0.1129, Dice=0.9635, IoU=0.9295] 
Train - Loss: 0.3158, Dice: 0.8936, IoU: 0.8168
Val   - Loss: 0.4852, Dice: 0.8444, IoU: 0.7380
LR: 0.000160

🔥 Epoch 57/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.73it/s, Loss=0.5543, Dice=0.9214, IoU=0.8543] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.91it/s, Loss=0.1580, Dice=0.9459, IoU=0.8973] 
Train - Loss: 0.1752, Dice: 0.9454, IoU: 0.8980
Val   - Loss: 0.3270, Dice: 0.9081, IoU: 0.8361
LR: 0.000165

🔥 Epoch 58/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.74it/s, Loss=0.0881, Dice=0.9659, IoU=0.9340] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.03it/s, Loss=0.0928, Dice=0.9683, IoU=0.9386] 
Train - Loss: 0.2425, Dice: 0.9244, IoU: 0.8642
Val   - Loss: 0.3401, Dice: 0.9090, IoU: 0.8370
LR: 0.000169

🔥 Epoch 59/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.77it/s, Loss=1.4497, Dice=0.4749, IoU=0.3114] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.98it/s, Loss=0.3187, Dice=0.8901, IoU=0.8019] 
Train - Loss: 0.2994, Dice: 0.9266, IoU: 0.8777
Val   - Loss: 0.5685, Dice: 0.8860, IoU: 0.8061
LR: 0.000173

🔥 Epoch 60/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.0695, Dice=0.9776, IoU=0.9562] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.10it/s, Loss=0.1179, Dice=0.9612, IoU=0.9254] 
Train - Loss: 0.3163, Dice: 0.9249, IoU: 0.8636
Val   - Loss: 0.3515, Dice: 0.9144, IoU: 0.8461
LR: 0.000178

🔥 Epoch 61/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.9423, Dice=0.6558, IoU=0.4878] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.20it/s, Loss=0.1021, Dice=0.9683, IoU=0.9386] 
Train - Loss: 0.3081, Dice: 0.9361, IoU: 0.8864
Val   - Loss: 0.6748, Dice: 0.8356, IoU: 0.7235
LR: 0.000182

🔥 Epoch 62/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=1.0983, Dice=0.6364, IoU=0.4667] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.18it/s, Loss=0.1111, Dice=0.9672, IoU=0.9365] 
Train - Loss: 0.4419, Dice: 0.8715, IoU: 0.7796
Val   - Loss: 0.6514, Dice: 0.8361, IoU: 0.7249
LR: 0.000187

🔥 Epoch 63/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.0584, Dice=0.9830, IoU=0.9665]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.15it/s, Loss=0.0601, Dice=0.9794, IoU=0.9597] 
Train - Loss: 0.9786, Dice: 0.8068, IoU: 0.7066
Val   - Loss: 0.8373, Dice: 0.8780, IoU: 0.7867
LR: 0.000192

🔥 Epoch 64/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.0660, Dice=0.9758, IoU=0.9527] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.09it/s, Loss=0.3098, Dice=0.8967, IoU=0.8128] 
Train - Loss: 0.3406, Dice: 0.9166, IoU: 0.8493
Val   - Loss: 0.3378, Dice: 0.8878, IoU: 0.8070
LR: 0.000196

🔥 Epoch 65/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=2.3664, Dice=0.8714, IoU=0.7720]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.76it/s, Loss=0.3743, Dice=0.8726, IoU=0.7740] 
Train - Loss: 0.4135, Dice: 0.9185, IoU: 0.8540
Val   - Loss: 0.4624, Dice: 0.8747, IoU: 0.7854
LR: 0.000201

🔥 Epoch 66/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.1960, Dice=0.9319, IoU=0.8725] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.65it/s, Loss=0.1903, Dice=0.9363, IoU=0.8803] 
Train - Loss: 0.5493, Dice: 0.9204, IoU: 0.8563
Val   - Loss: 0.3165, Dice: 0.8933, IoU: 0.8164
LR: 0.000206

🔥 Epoch 67/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.3349, Dice=0.8826, IoU=0.7899] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.18it/s, Loss=0.6445, Dice=0.7844, IoU=0.6452] 
Train - Loss: 0.1935, Dice: 0.9366, IoU: 0.8831
Val   - Loss: 0.4307, Dice: 0.8525, IoU: 0.7620
LR: 0.000211

🔥 Epoch 68/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.86it/s, Loss=0.1572, Dice=0.9446, IoU=0.8950] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.15it/s, Loss=0.4317, Dice=0.8567, IoU=0.7493] 
Train - Loss: 0.2533, Dice: 0.9132, IoU: 0.8454
Val   - Loss: 0.3252, Dice: 0.8902, IoU: 0.8111
LR: 0.000216

🔥 Epoch 69/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.1682, Dice=0.9411, IoU=0.8887] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.08it/s, Loss=0.1214, Dice=0.9594, IoU=0.9220] 
Train - Loss: 0.1618, Dice: 0.9451, IoU: 0.8967
Val   - Loss: 0.2314, Dice: 0.9238, IoU: 0.8607
LR: 0.000221
✅ New best model saved! Val Dice: 0.9238

🔥 Epoch 70/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.2076, Dice=0.9348, IoU=0.8775] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.07it/s, Loss=0.2799, Dice=0.9245, IoU=0.8596] 
Train - Loss: 0.1567, Dice: 0.9465, IoU: 0.9028
Val   - Loss: 0.2600, Dice: 0.9178, IoU: 0.8503
LR: 0.000226

🔥 Epoch 71/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.0318, Dice=0.9894, IoU=0.9791] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.08it/s, Loss=0.1015, Dice=0.9663, IoU=0.9349] 
Train - Loss: 0.1170, Dice: 0.9621, IoU: 0.9278
Val   - Loss: 0.2102, Dice: 0.9280, IoU: 0.8684
LR: 0.000231
✅ New best model saved! Val Dice: 0.9280

🔥 Epoch 72/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.0581, Dice=0.9840, IoU=0.9685] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.24it/s, Loss=0.0653, Dice=0.9798, IoU=0.9604] 
Train - Loss: 0.1191, Dice: 0.9600, IoU: 0.9239
Val   - Loss: 0.2758, Dice: 0.9107, IoU: 0.8385
LR: 0.000236

🔥 Epoch 73/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1172, Dice=0.9609, IoU=0.9248] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  8.67it/s, Loss=0.2967, Dice=0.9032, IoU=0.8234] 
Train - Loss: 0.1439, Dice: 0.9517, IoU: 0.9094
Val   - Loss: 0.2751, Dice: 0.9044, IoU: 0.8318
LR: 0.000241

🔥 Epoch 74/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.7411, Dice=0.7682, IoU=0.6236]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.12it/s, Loss=0.7348, Dice=0.7971, IoU=0.6626] 
Train - Loss: 0.3487, Dice: 0.8924, IoU: 0.8137
Val   - Loss: 0.5172, Dice: 0.8557, IoU: 0.7608
LR: 0.000246

🔥 Epoch 75/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.1577, Dice=0.9461, IoU=0.8976] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.98it/s, Loss=0.0669, Dice=0.9769, IoU=0.9548] 
Train - Loss: 0.1881, Dice: 0.9404, IoU: 0.8900
Val   - Loss: 0.3391, Dice: 0.8952, IoU: 0.8136
LR: 0.000251

🔥 Epoch 76/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.74it/s, Loss=0.1192, Dice=0.9597, IoU=0.9225] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.88it/s, Loss=0.1089, Dice=0.9662, IoU=0.9347] 
Train - Loss: 0.1728, Dice: 0.9436, IoU: 0.8950
Val   - Loss: 0.8809, Dice: 0.7819, IoU: 0.6603
LR: 0.000256

🔥 Epoch 77/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.78it/s, Loss=0.1289, Dice=0.9549, IoU=0.9137] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.04it/s, Loss=0.1194, Dice=0.9611, IoU=0.9251] 
Train - Loss: 0.2984, Dice: 0.9184, IoU: 0.8537
Val   - Loss: 0.3657, Dice: 0.8869, IoU: 0.8007
LR: 0.000262

🔥 Epoch 78/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=1.0648, Dice=0.5142, IoU=0.3461] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.13it/s, Loss=0.2241, Dice=0.9263, IoU=0.8627] 
Train - Loss: 0.2798, Dice: 0.9128, IoU: 0.8578
Val   - Loss: 0.3878, Dice: 0.8672, IoU: 0.7728
LR: 0.000267

🔥 Epoch 79/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.1402, Dice=0.9476, IoU=0.9004] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.93it/s, Loss=0.0469, Dice=0.9848, IoU=0.9700] 
Train - Loss: 0.1473, Dice: 0.9502, IoU: 0.9063
Val   - Loss: 0.3591, Dice: 0.8776, IoU: 0.7887
LR: 0.000272

🔥 Epoch 80/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.1386, Dice=0.9619, IoU=0.9266] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.57it/s, Loss=0.8897, Dice=0.6905, IoU=0.5272] 
Train - Loss: 0.2055, Dice: 0.9299, IoU: 0.8775
Val   - Loss: 0.6814, Dice: 0.7825, IoU: 0.6567
LR: 0.000278

🔥 Epoch 81/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.1461, Dice=0.9563, IoU=0.9162] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.11it/s, Loss=0.0862, Dice=0.9732, IoU=0.9479] 
Train - Loss: 0.2153, Dice: 0.9263, IoU: 0.8682
Val   - Loss: 0.4771, Dice: 0.8564, IoU: 0.7554
LR: 0.000283

🔥 Epoch 82/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0443, Dice=0.9857, IoU=0.9718] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.10it/s, Loss=0.1081, Dice=0.9630, IoU=0.9287] 
Train - Loss: 0.1683, Dice: 0.9458, IoU: 0.8993
Val   - Loss: 0.2291, Dice: 0.9294, IoU: 0.8696
LR: 0.000289
✅ New best model saved! Val Dice: 0.9294

🔥 Epoch 83/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.1189, Dice=0.9606, IoU=0.9242] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.30it/s, Loss=0.2492, Dice=0.9189, IoU=0.8500] 
Train - Loss: 0.1402, Dice: 0.9546, IoU: 0.9139
Val   - Loss: 0.3744, Dice: 0.8929, IoU: 0.8106
LR: 0.000294

🔥 Epoch 84/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.2450, Dice=0.9067, IoU=0.8293] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.01it/s, Loss=1.5389, Dice=0.4926, IoU=0.3268] 
Train - Loss: 0.3600, Dice: 0.8857, IoU: 0.8081
Val   - Loss: 0.9215, Dice: 0.7212, IoU: 0.5998
LR: 0.000300

🔥 Epoch 85/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.1514, Dice=0.9465, IoU=0.8984] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.25it/s, Loss=0.1014, Dice=0.9646, IoU=0.9316] 
Train - Loss: 0.4367, Dice: 0.8515, IoU: 0.7522
Val   - Loss: 0.4108, Dice: 0.8578, IoU: 0.7572
LR: 0.000305

🔥 Epoch 72/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.0581, Dice=0.9840, IoU=0.9685] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.24it/s, Loss=0.0653, Dice=0.9798, IoU=0.9604] 
Train - Loss: 0.1191, Dice: 0.9600, IoU: 0.9239
Val   - Loss: 0.2758, Dice: 0.9107, IoU: 0.8385
LR: 0.000236

🔥 Epoch 73/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.84it/s, Loss=0.1172, Dice=0.9609, IoU=0.9248] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  8.67it/s, Loss=0.2967, Dice=0.9032, IoU=0.8234] 
Train - Loss: 0.1439, Dice: 0.9517, IoU: 0.9094
Val   - Loss: 0.2751, Dice: 0.9044, IoU: 0.8318
LR: 0.000241

🔥 Epoch 74/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.7411, Dice=0.7682, IoU=0.6236]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.12it/s, Loss=0.7348, Dice=0.7971, IoU=0.6626] 
Train - Loss: 0.3487, Dice: 0.8924, IoU: 0.8137
Val   - Loss: 0.5172, Dice: 0.8557, IoU: 0.7608
LR: 0.000246

🔥 Epoch 75/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.1577, Dice=0.9461, IoU=0.8976] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.98it/s, Loss=0.0669, Dice=0.9769, IoU=0.9548] 
Train - Loss: 0.1881, Dice: 0.9404, IoU: 0.8900
Val   - Loss: 0.3391, Dice: 0.8952, IoU: 0.8136
LR: 0.000251

🔥 Epoch 76/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.74it/s, Loss=0.1192, Dice=0.9597, IoU=0.9225] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.88it/s, Loss=0.1089, Dice=0.9662, IoU=0.9347] 
Train - Loss: 0.1728, Dice: 0.9436, IoU: 0.8950
Val   - Loss: 0.8809, Dice: 0.7819, IoU: 0.6603
LR: 0.000256

🔥 Epoch 77/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.78it/s, Loss=0.1289, Dice=0.9549, IoU=0.9137] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.04it/s, Loss=0.1194, Dice=0.9611, IoU=0.9251] 
Train - Loss: 0.2984, Dice: 0.9184, IoU: 0.8537
Val   - Loss: 0.3657, Dice: 0.8869, IoU: 0.8007
LR: 0.000262

🔥 Epoch 78/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=1.0648, Dice=0.5142, IoU=0.3461] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.13it/s, Loss=0.2241, Dice=0.9263, IoU=0.8627] 
Train - Loss: 0.2798, Dice: 0.9128, IoU: 0.8578
Val   - Loss: 0.3878, Dice: 0.8672, IoU: 0.7728
LR: 0.000267

🔥 Epoch 79/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.1402, Dice=0.9476, IoU=0.9004] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.93it/s, Loss=0.0469, Dice=0.9848, IoU=0.9700] 
Train - Loss: 0.1473, Dice: 0.9502, IoU: 0.9063
Val   - Loss: 0.3591, Dice: 0.8776, IoU: 0.7887
LR: 0.000272

🔥 Epoch 80/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.1386, Dice=0.9619, IoU=0.9266] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.57it/s, Loss=0.8897, Dice=0.6905, IoU=0.5272] 
Train - Loss: 0.2055, Dice: 0.9299, IoU: 0.8775
Val   - Loss: 0.6814, Dice: 0.7825, IoU: 0.6567
LR: 0.000278

🔥 Epoch 81/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.1461, Dice=0.9563, IoU=0.9162] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.11it/s, Loss=0.0862, Dice=0.9732, IoU=0.9479] 
Train - Loss: 0.2153, Dice: 0.9263, IoU: 0.8682
Val   - Loss: 0.4771, Dice: 0.8564, IoU: 0.7554
LR: 0.000283

🔥 Epoch 82/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=0.0443, Dice=0.9857, IoU=0.9718] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.10it/s, Loss=0.1081, Dice=0.9630, IoU=0.9287] 
Train - Loss: 0.1683, Dice: 0.9458, IoU: 0.8993
Val   - Loss: 0.2291, Dice: 0.9294, IoU: 0.8696
LR: 0.000289
✅ New best model saved! Val Dice: 0.9294

🔥 Epoch 83/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.1189, Dice=0.9606, IoU=0.9242] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.30it/s, Loss=0.2492, Dice=0.9189, IoU=0.8500] 
Train - Loss: 0.1402, Dice: 0.9546, IoU: 0.9139
Val   - Loss: 0.3744, Dice: 0.8929, IoU: 0.8106
LR: 0.000294

🔥 Epoch 84/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.82it/s, Loss=0.2450, Dice=0.9067, IoU=0.8293] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.01it/s, Loss=1.5389, Dice=0.4926, IoU=0.3268] 
Train - Loss: 0.3600, Dice: 0.8857, IoU: 0.8081
Val   - Loss: 0.9215, Dice: 0.7212, IoU: 0.5998
LR: 0.000300

🔥 Epoch 85/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.1514, Dice=0.9465, IoU=0.8984] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.25it/s, Loss=0.1014, Dice=0.9646, IoU=0.9316] 
Train - Loss: 0.4367, Dice: 0.8515, IoU: 0.7522
Val   - Loss: 0.4108, Dice: 0.8578, IoU: 0.7572
LR: 0.000305

🔥 Epoch 86/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.1828, Dice=0.9351, IoU=0.8781] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.97it/s, Loss=0.1293, Dice=0.9551, IoU=0.9140] 
Train - Loss: 0.2297, Dice: 0.9241, IoU: 0.8635
Val   - Loss: 0.4351, Dice: 0.8409, IoU: 0.7335
LR: 0.000311

🔥 Epoch 87/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.1975, Dice=0.9389, IoU=0.8848]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.20it/s, Loss=0.1549, Dice=0.9508, IoU=0.9063] 
Train - Loss: 0.2192, Dice: 0.9245, IoU: 0.8641
Val   - Loss: 0.2701, Dice: 0.9225, IoU: 0.8588
LR: 0.000316

🔥 Epoch 88/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=1.3032, Dice=0.5193, IoU=0.3507] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.06it/s, Loss=0.0978, Dice=0.9674, IoU=0.9369] 
Train - Loss: 0.2100, Dice: 0.9315, IoU: 0.8830
Val   - Loss: 0.4442, Dice: 0.8622, IoU: 0.7643
LR: 0.000322

🔥 Epoch 89/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.0790, Dice=0.9695, IoU=0.9409] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.00it/s, Loss=0.0772, Dice=0.9755, IoU=0.9522] 
Train - Loss: 0.2004, Dice: 0.9379, IoU: 0.8860
Val   - Loss: 0.3130, Dice: 0.9132, IoU: 0.8458
LR: 0.000327

🔥 Epoch 90/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.73it/s, Loss=0.0685, Dice=0.9789, IoU=0.9587] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.77it/s, Loss=0.0702, Dice=0.9759, IoU=0.9529] 
Train - Loss: 0.1585, Dice: 0.9555, IoU: 0.9167
Val   - Loss: 0.3494, Dice: 0.8937, IoU: 0.8106
LR: 0.000333

🔥 Epoch 91/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.75it/s, Loss=0.0775, Dice=0.9724, IoU=0.9462] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.92it/s, Loss=0.2120, Dice=0.9308, IoU=0.8706] 
Train - Loss: 0.1730, Dice: 0.9543, IoU: 0.9146
Val   - Loss: 0.2136, Dice: 0.9341, IoU: 0.8778
LR: 0.000339
✅ New best model saved! Val Dice: 0.9341

🔥 Epoch 92/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.74it/s, Loss=0.8672, Dice=0.6482, IoU=0.4795] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.86it/s, Loss=0.0550, Dice=0.9821, IoU=0.9649] 
Train - Loss: 0.6757, Dice: 0.9224, IoU: 0.8648
Val   - Loss: 0.8832, Dice: 0.8774, IoU: 0.7863
LR: 0.000344

🔥 Epoch 93/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.6966, Dice=0.8013, IoU=0.6684] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.18it/s, Loss=0.0729, Dice=0.9773, IoU=0.9556] 
Train - Loss: 0.2773, Dice: 0.9332, IoU: 0.8784
Val   - Loss: 0.5105, Dice: 0.8646, IoU: 0.7670
LR: 0.000350

🔥 Epoch 94/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.2129, Dice=0.9244, IoU=0.8594]
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.17it/s, Loss=0.2386, Dice=0.9237, IoU=0.8582] 
Train - Loss: 0.1796, Dice: 0.9439, IoU: 0.8962
Val   - Loss: 0.2350, Dice: 0.9200, IoU: 0.8545
LR: 0.000356

🔥 Epoch 95/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.83it/s, Loss=0.0358, Dice=0.9887, IoU=0.9776] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.98it/s, Loss=0.0487, Dice=0.9839, IoU=0.9683] 
Train - Loss: 0.1269, Dice: 0.9573, IoU: 0.9194
Val   - Loss: 0.4998, Dice: 0.9096, IoU: 0.8378
LR: 0.000361

🔥 Epoch 96/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.79it/s, Loss=0.0489, Dice=0.9840, IoU=0.9684] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00,  9.84it/s, Loss=0.0601, Dice=0.9798, IoU=0.9604] 
Train - Loss: 0.4992, Dice: 0.9524, IoU: 0.9126
Val   - Loss: 0.3111, Dice: 0.9094, IoU: 0.8396
LR: 0.000367

🔥 Epoch 97/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.71it/s, Loss=0.0844, Dice=0.9704, IoU=0.9426] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.08it/s, Loss=0.0810, Dice=0.9732, IoU=0.9477] 
Train - Loss: 0.2431, Dice: 0.9365, IoU: 0.8853
Val   - Loss: 0.6377, Dice: 0.8820, IoU: 0.7969
LR: 0.000373

🔥 Epoch 98/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.85it/s, Loss=1.5612, Dice=0.7912, IoU=0.6545] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.25it/s, Loss=0.0701, Dice=0.9749, IoU=0.9511] 
Train - Loss: 0.5149, Dice: 0.8959, IoU: 0.8177
Val   - Loss: 0.3137, Dice: 0.9086, IoU: 0.8345
LR: 0.000379

🔥 Epoch 99/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.81it/s, Loss=0.1181, Dice=0.9594, IoU=0.9220] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.11it/s, Loss=0.0877, Dice=0.9686, IoU=0.9391] 
Train - Loss: 0.1929, Dice: 0.9544, IoU: 0.9142
Val   - Loss: 0.4149, Dice: 0.9079, IoU: 0.8347
LR: 0.000384

🔥 Epoch 100/100
🚀 V5 Training: 100%|████████████████| 21/21 [00:05<00:00,  3.80it/s, Loss=0.2679, Dice=0.8965, IoU=0.8124] 
🎯 V5 Validation: 100%|████████████████| 6/6 [00:00<00:00, 10.04it/s, Loss=0.0601, Dice=0.9810, IoU=0.9628] 
Train - Loss: 0.3198, Dice: 0.9480, IoU: 0.9042
Val   - Loss: 0.3442, Dice: 0.8978, IoU: 0.8184
LR: 0.000390

🎉 V5 Training completed in 0.17 hours
🏆 Best validation Dice: 0.9341
🚀 V4 EXCEEDED! V5 surpasses V4 performance!
🎯 Improvement: +7.11%

🏆 Final Result: 0.9341 Dice
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 
