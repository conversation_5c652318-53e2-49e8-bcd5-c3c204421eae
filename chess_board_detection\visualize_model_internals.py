"""
Visualize model internals to understand why the model is not detecting the chess board correctly.
This script creates visualizations of the segmentation mask and heatmaps.
"""

import os
import sys
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
from torchvision import transforms

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size,
        'preprocessed_image': image_final
    }

    return image_final, preprocess_info

def enhance_image(image):
    """
    Enhance image colors and contrast similar to training augmentations.
    """
    # 1. Adaptive contrast enhancement (similar to CLAHE in training)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    l, a, b = cv2.split(lab)
    l = clahe.apply(l)
    lab = cv2.merge([l, a, b])
    image_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)

    # 2. Gamma correction (similar to RandomGamma in training)
    # Use a gamma value that enhances details without over-brightening
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)]).astype("uint8")
    image_enhanced = cv2.LUT(image_enhanced, table)

    # 3. Saturation enhancement (similar to HueSaturationValue in training)
    hsv = cv2.cvtColor(image_enhanced, cv2.COLOR_RGB2HSV)
    h, s, v = cv2.split(hsv)
    s = np.clip(s * 1.2, 0, 255).astype(np.uint8)  # Increase saturation by 20%
    hsv = cv2.merge([h, s, v])
    image_enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)

    return image_enhanced

def normalize_for_model(image):
    """
    Normalize image for model input using same normalization as during training.
    """
    # Convert to PIL Image first
    image_pil = Image.fromarray(image)

    # Use torchvision transforms which handle the data type correctly
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Apply transform and add batch dimension
    image_tensor = transform(image_pil).unsqueeze(0)

    return image_tensor

def visualize_model_internals(image_path, model, model_name, output_dir):
    """
    Visualize model internals to understand why the model is not detecting the chess board correctly.
    """
    # Preprocess image
    preprocessed_image, preprocess_info = preprocess_image(image_path)
    enhanced_image = enhance_image(preprocessed_image)

    # Normalize for model
    input_tensor = normalize_for_model(enhanced_image)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()
    heatmaps = torch.sigmoid(outputs['corner_heatmaps']).cpu().numpy()

    # Create figure with 2x3 grid
    fig, axs = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle(f'Model Internals Visualization - {model_name}', fontsize=16)

    # Plot original image
    axs[0, 0].imshow(preprocessed_image)
    axs[0, 0].set_title('Original Image (256x256)', fontsize=14)
    axs[0, 0].axis('off')

    # Plot enhanced image
    axs[0, 1].imshow(enhanced_image)
    axs[0, 1].set_title('Enhanced Image (Model Input)', fontsize=14)
    axs[0, 1].axis('off')

    # Plot segmentation mask
    axs[0, 2].imshow(segmentation[0, 0], cmap='gray')
    axs[0, 2].set_title('Segmentation Mask', fontsize=14)
    axs[0, 2].axis('off')

    # Plot heatmaps for each corner
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        row = 1
        col = i % 3

        # Plot heatmap
        axs[row, col].imshow(heatmaps[0, i], cmap='hot')
        axs[row, col].set_title(f'{corner_names[i]} Heatmap', fontsize=14)
        axs[row, col].axis('off')

        # Find peak location
        peak_y, peak_x = np.unravel_index(np.argmax(heatmaps[0, i]), heatmaps[0, i].shape)
        peak_value = heatmaps[0, i, peak_y, peak_x]

        # Mark peak
        axs[row, col].scatter(peak_x, peak_y, c='cyan', s=100, marker='x')
        axs[row, col].text(peak_x+5, peak_y+5, f"Peak: ({peak_x}, {peak_y})\nValue: {peak_value:.4f}",
                          color='white', fontsize=10,
                          bbox=dict(facecolor='black', alpha=0.7))

    # Save figure
    os.makedirs(output_dir, exist_ok=True)
    output_path = os.path.join(output_dir, f"{model_name}_internals.png")
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    # Create a visualization of the peak-to-second ratio
    visualize_peak_to_second_ratio(heatmaps, model_name, output_dir)

    # Create a visualization of the segmentation threshold
    visualize_segmentation_thresholds(segmentation[0, 0], enhanced_image, model_name, output_dir)

    return output_path

def visualize_peak_to_second_ratio(heatmaps, model_name, output_dir):
    """
    Visualize the peak-to-second ratio for each corner heatmap.
    This helps understand if the model is producing clear, distinct peaks.
    """
    # Create figure with 2x2 grid
    fig, axs = plt.subplots(2, 2, figsize=(15, 15))
    fig.suptitle(f'Peak-to-Second Ratio Analysis - {model_name}', fontsize=16)

    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    positions = [(0, 0), (0, 1), (1, 1), (1, 0)]  # Clockwise from top-left

    for i, (corner, pos) in enumerate(zip(corner_names, positions)):
        # Get heatmap
        heatmap = heatmaps[0, i]

        # Find peak location
        peak_idx = np.argmax(heatmap)
        peak_y, peak_x = np.unravel_index(peak_idx, heatmap.shape)
        peak_value = heatmap[peak_y, peak_x]

        # Create a mask to exclude the peak and its immediate neighbors
        mask = np.ones_like(heatmap, dtype=bool)
        y_min, y_max = max(0, peak_y-3), min(heatmap.shape[0], peak_y+4)
        x_min, x_max = max(0, peak_x-3), min(heatmap.shape[1], peak_x+4)
        mask[y_min:y_max, x_min:x_max] = False

        # Find second highest peak
        masked_heatmap = np.where(mask, heatmap, 0)
        second_idx = np.argmax(masked_heatmap)
        second_y, second_x = np.unravel_index(second_idx, heatmap.shape)
        second_value = heatmap[second_y, second_x]

        # Calculate peak-to-second ratio
        ratio = peak_value / max(second_value, 1e-6)  # Avoid division by zero

        # Plot heatmap
        im = axs[pos].imshow(heatmap, cmap='hot')
        axs[pos].set_title(f'{corner} Heatmap\nPeak-to-Second Ratio: {ratio:.4f}', fontsize=14)

        # Mark peaks
        axs[pos].scatter(peak_x, peak_y, c='cyan', s=100, marker='x')
        axs[pos].text(peak_x+5, peak_y+5, f"Peak: {peak_value:.4f}",
                     color='white', fontsize=10,
                     bbox=dict(facecolor='black', alpha=0.7))

        axs[pos].scatter(second_x, second_y, c='lime', s=100, marker='x')
        axs[pos].text(second_x+5, second_y+5, f"Second: {second_value:.4f}",
                     color='white', fontsize=10,
                     bbox=dict(facecolor='black', alpha=0.7))

        # Add colorbar
        plt.colorbar(im, ax=axs[pos])

    # Save figure
    output_path = os.path.join(output_dir, f"{model_name}_peak_to_second_ratio.png")
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    return output_path

def visualize_segmentation_thresholds(segmentation, image, model_name, output_dir):
    """
    Visualize the segmentation mask at different thresholds.
    This helps understand if the segmentation is capturing the chess board.
    """
    # Create figure with 3x4 grid to fit all thresholds
    fig, axs = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle(f'Segmentation Threshold Analysis - {model_name}', fontsize=16)

    # Plot original image
    axs[0, 0].imshow(image)
    axs[0, 0].set_title('Original Image', fontsize=14)
    axs[0, 0].axis('off')

    # Plot raw segmentation
    im = axs[0, 1].imshow(segmentation, cmap='viridis')
    axs[0, 1].set_title('Raw Segmentation Values', fontsize=14)
    axs[0, 1].axis('off')
    plt.colorbar(im, ax=axs[0, 1])

    # Plot histogram of segmentation values
    axs[0, 2].hist(segmentation.flatten(), bins=50)
    axs[0, 2].set_title('Histogram of Segmentation Values', fontsize=14)
    axs[0, 2].set_xlabel('Value')
    axs[0, 2].set_ylabel('Frequency')

    # Hide the extra subplot in the first row
    axs[0, 3].axis('off')

    # Plot segmentation at different thresholds
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8]
    positions = [(1, 0), (1, 1), (1, 2), (1, 3),
                 (2, 0), (2, 1), (2, 2), (2, 3)]

    for i, (threshold, pos) in enumerate(zip(thresholds, positions)):
        row, col = pos

        # Apply threshold
        binary = (segmentation > threshold).astype(np.uint8) * 255

        # Create overlay
        overlay = image.copy()
        mask_rgb = cv2.cvtColor(binary, cv2.COLOR_GRAY2RGB)
        mask_rgb[binary > 0] = [255, 0, 0]  # Red for segmentation
        overlay = cv2.addWeighted(overlay, 0.7, mask_rgb, 0.3, 0)

        # Plot
        axs[row, col].imshow(overlay)
        axs[row, col].set_title(f'Threshold: {threshold}', fontsize=14)
        axs[row, col].axis('off')

    # Save figure
    output_path = os.path.join(output_dir, f"{model_name}_segmentation_thresholds.png")
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\model_internals"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    # Process each model
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name}...")

        # Load model
        model = load_model(model_path)

        # Visualize model internals
        output_path = visualize_model_internals(image_path, model, model_name, output_dir)

        print(f"Model internals visualization saved to: {output_path}")

if __name__ == "__main__":
    main()
