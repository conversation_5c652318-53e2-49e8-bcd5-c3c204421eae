{"logs": [{"outputFile": "com.chessvision.app-mergeDebugResources-51:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bca7edd7dea9a9293306bd31b1a5bbbe\\transformed\\appcompat-1.1.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,880,971,1063,1155,1249,1350,1443,1538,1634,1725,1816,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,77,90,91,91,93,100,92,94,95,90,90,79,106,103,97,102,103,103,156,98,80", "endOffsets": "203,306,419,504,608,719,797,875,966,1058,1150,1244,1345,1438,1533,1629,1720,1811,1891,1998,2102,2200,2303,2407,2511,2668,2767,2848"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,880,971,1063,1155,1249,1350,1443,1538,1634,1725,1816,1896,2003,2107,2205,2308,2412,2516,2673,10566", "endColumns": "102,102,112,84,103,110,77,77,90,91,91,93,100,92,94,95,90,90,79,106,103,97,102,103,103,156,98,80", "endOffsets": "203,306,419,504,608,719,797,875,966,1058,1150,1244,1345,1438,1533,1629,1720,1811,1891,1998,2102,2200,2303,2407,2511,2668,2767,10642"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4713635adcc2bac18369e016a04ea54e\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "36,37,38,39,40,41,42,99,100,101,102,103,104,106,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3478,3568,3645,3754,3852,3941,4030,10102,10188,10271,10336,10402,10482,10647,10822,10900,10966", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "3563,3640,3749,3847,3936,4025,4115,10183,10266,10331,10397,10477,10561,10716,10895,10961,11082"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ead5545371d24235bc023f33a0e98494\\transformed\\foundation-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,84", "endOffsets": "136,221"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "11087,11173", "endColumns": "85,84", "endOffsets": "11168,11253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5b68b955b795828701719ac184f2bfb\\transformed\\core-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "29,30,31,32,33,34,35,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2772,2868,2971,3070,3168,3269,3367,10721", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "2863,2966,3065,3163,3264,3362,3473,10817"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2559031d7e9fe48b3590293cf5c6448f\\transformed\\material3-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,391,502,599,695,808,937,1058,1189,1274,1374,1464,1564,1682,1802,1907,2034,2159,2289,2437,2558,2672,2791,2903,2994,3093,3206,3331,3425,3541,3647,3774,3908,4018,4115,4195,4293,4389,4475,4561,4666,4752,4839,4942,5044,5139,5242,5328,5429,5527,5629,5756,5842,5942", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "164,276,386,497,594,690,803,932,1053,1184,1269,1369,1459,1559,1677,1797,1902,2029,2154,2284,2432,2553,2667,2786,2898,2989,3088,3201,3326,3420,3536,3642,3769,3903,4013,4110,4190,4288,4384,4470,4556,4661,4747,4834,4937,5039,5134,5237,5323,5424,5522,5624,5751,5837,5937,6032"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4120,4234,4346,4456,4567,4664,4760,4873,5002,5123,5254,5339,5439,5529,5629,5747,5867,5972,6099,6224,6354,6502,6623,6737,6856,6968,7059,7158,7271,7396,7490,7606,7712,7839,7973,8083,8180,8260,8358,8454,8540,8626,8731,8817,8904,9007,9109,9204,9307,9393,9494,9592,9694,9821,9907,10007", "endColumns": "113,111,109,110,96,95,112,128,120,130,84,99,89,99,117,119,104,126,124,129,147,120,113,118,111,90,98,112,124,93,115,105,126,133,109,96,79,97,95,85,85,104,85,86,102,101,94,102,85,100,97,101,126,85,99,94", "endOffsets": "4229,4341,4451,4562,4659,4755,4868,4997,5118,5249,5334,5434,5524,5624,5742,5862,5967,6094,6219,6349,6497,6618,6732,6851,6963,7054,7153,7266,7391,7485,7601,7707,7834,7968,8078,8175,8255,8353,8449,8535,8621,8726,8812,8899,9002,9104,9199,9302,9388,9489,9587,9689,9816,9902,10002,10097"}}]}]}