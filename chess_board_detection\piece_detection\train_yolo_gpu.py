"""
Train a YOLO model for chess piece detection using GPU acceleration.
This script is optimized for RTX 3050 laptop GPU and implements:
1. Extended training with high epochs
2. Mixed precision training
3. Advanced learning rate scheduling
4. Checkpoint ensemble creation
5. Memory optimization techniques
"""

import os
import sys
import math
import argparse
import torch
import platform
from pathlib import Path
from datetime import datetime
import yaml
import random
import numpy as np
from ultralytics import YOL<PERSON>

def set_seed(seed=42):
    """Set all random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def optimize_gpu():
    """Optimize GPU settings for training."""
    # Enable cuDNN auto-tuner
    torch.backends.cudnn.benchmark = True
    
    # Clear GPU cache
    torch.cuda.empty_cache()
    
    # Set TensorFloat32 precision if available (Ampere GPUs like RTX 3050)
    torch.set_float32_matmul_precision('high')
    
    # Print GPU info
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def create_ensemble(model_dir, output_path):
    """
    Create an ensemble model from multiple checkpoints.
    
    Args:
        model_dir: Directory containing model checkpoints
        output_path: Path to save the ensemble model
    """
    # Find all .pt files in the directory
    checkpoint_files = list(Path(model_dir).glob('*.pt'))
    
    if len(checkpoint_files) < 2:
        print(f"Not enough checkpoints found in {model_dir} for ensemble creation")
        return None
    
    print(f"Creating ensemble from {len(checkpoint_files)} checkpoints...")
    
    # Load models
    models = [YOLO(ckpt) for ckpt in checkpoint_files]
    
    # Create ensemble (simple averaging of weights)
    ensemble_model = models[0]
    
    # Average the weights
    for key in ensemble_model.model.state_dict():
        # Skip batch norm running stats
        if 'running_mean' in key or 'running_var' in key or 'num_batches_tracked' in key:
            continue
            
        # Average the weights
        ensemble_model.model.state_dict()[key] = sum(model.model.state_dict()[key] 
                                                    for model in models) / len(models)
    
    # Save the ensemble model
    ensemble_model.save(output_path)
    print(f"Ensemble model saved to {output_path}")
    return output_path

def train_extended(
    model_path,
    data_yaml,
    epochs=800,
    batch_size=16,
    img_size=416,
    device='0',
    workers=4,
    patience=100,
    output_dir=None,
    save_period=100
):
    """
    Train a YOLO model on the chess pieces dataset with extended epochs.
    
    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or GPU device id)
        workers: Number of worker threads
        patience: Early stopping patience
        output_dir: Directory to save results
        save_period: Save checkpoints every N epochs
    """
    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join('chess_board_detection/piece_detection/models', f'gpu_run_{timestamp}')
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Print system info
    print(f"Python version: {platform.python_version()}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    print(f"Training on: {device}")
    
    # Optimize GPU settings
    if device != 'cpu':
        optimize_gpu()
    
    # Load the model
    model = YOLO(model_path)
    
    # Train the model with extended configuration
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        imgsz=img_size,
        batch=batch_size,
        patience=patience,
        device=device,
        workers=workers,
        project=output_dir,
        name=f'chess_pieces_{timestamp}',
        exist_ok=True,
        pretrained=True,
        verbose=True,
        seed=42,
        cache=True,
        close_mosaic=epochs-50,  # Disable mosaic for final epochs
        amp=True,  # Enable mixed precision
        # Strong augmentation
        augment=True,
        mosaic=1.0,
        mixup=0.5,
        degrees=15.0,
        translate=0.2,
        scale=0.5,
        shear=2.0,
        fliplr=0.5,
        perspective=0.0005,
        # Save checkpoints periodically
        save_period=save_period
    )
    
    # Create ensemble from saved checkpoints
    checkpoints_dir = os.path.join(output_dir, f'chess_pieces_{timestamp}', 'weights')
    ensemble_path = os.path.join(output_dir, f'ensemble_model_{timestamp}.pt')
    create_ensemble(checkpoints_dir, ensemble_path)
    
    # Export the model to ONNX format
    model.export(format='onnx', dynamic=True, simplify=True)
    
    print(f"Training complete. Model saved to {output_dir}")
    return results

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description="Train YOLO model on chess pieces dataset using GPU")
    parser.add_argument("--model", type=str, default="yolo11n.pt", help="Path to pre-trained model")
    parser.add_argument("--data_yaml", type=str, default="chess_board_detection/piece_detection/augmented_dataset_416x416/dataset.yaml", 
                        help="Path to dataset YAML file")
    parser.add_argument("--epochs", type=int, default=800, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16, help="Batch size")
    parser.add_argument("--img-size", type=int, default=416, help="Image size for training")
    parser.add_argument("--device", type=str, default="0", help="Device to train on ('cpu' or GPU device id)")
    parser.add_argument("--workers", type=int, default=4, help="Number of worker threads")
    parser.add_argument("--patience", type=int, default=100, help="Early stopping patience")
    parser.add_argument("--output_dir", type=str, default=None, help="Directory to save trained model")
    parser.add_argument("--save_period", type=int, default=100, help="Save checkpoints every N epochs")
    
    args = parser.parse_args()
    
    # Set random seed for reproducibility
    set_seed(42)
    
    # Train model with extended configuration
    print(f"Training model {args.model} on dataset {args.data_yaml} for {args.epochs} epochs")
    train_extended(
        args.model,
        args.data_yaml,
        args.epochs,
        args.batch,
        args.img_size,
        args.device,
        args.workers,
        args.patience,
        args.output_dir,
        args.save_period
    )

if __name__ == "__main__":
    main()
