PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_unet_augmented.py
Starting U-Net segmentation training with augmented dataset...
Using device: cuda
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.4 GB
Creating dataloaders...
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Creating U-Net model...
Model parameters: 17,262,977
Starting training for 50 epochs...

Epoch 1/50
Training:   0%|                                                                                   | 0/11 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  image = torch.load(image_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  mask = torch.load(mask_path, map_location='cpu')
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.22it/s, Loss=0.6417, IoU=0.8503, Dice=0.9191]
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.38it/s, Loss=1.2001, IoU=0.2108, Dice=0.3482] 
Train - Loss: 0.9733, IoU: 0.5544, Dice: 0.6620
Val   - Loss: 1.2448, IoU: 0.2658, Dice: 0.4159
LR: 0.000100
New best model saved! Val Dice: 0.4159

Epoch 2/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.41it/s, Loss=1.0674, IoU=0.4313, Dice=0.6027] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.64it/s, Loss=1.2142, IoU=0.4704, Dice=0.6398] 
Train - Loss: 0.7429, IoU: 0.7358, Dice: 0.8360
Val   - Loss: 1.3980, IoU: 0.3819, Dice: 0.5476
LR: 0.000100
New best model saved! Val Dice: 0.5476

Epoch 3/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.44it/s, Loss=0.4471, IoU=0.8952, Dice=0.9447] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.63it/s, Loss=1.3062, IoU=0.4759, Dice=0.6449] 
Train - Loss: 0.5566, IoU: 0.8174, Dice: 0.8988
Val   - Loss: 1.5491, IoU: 0.3858, Dice: 0.5516
LR: 0.000100
New best model saved! Val Dice: 0.5516

Epoch 4/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.4313, IoU=0.8883, Dice=0.9409] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.38it/s, Loss=1.3227, IoU=0.4998, Dice=0.6665] 
Train - Loss: 0.5661, IoU: 0.7968, Dice: 0.8777
Val   - Loss: 1.4799, IoU: 0.4164, Dice: 0.5834
LR: 0.000100
New best model saved! Val Dice: 0.5834

Epoch 5/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.3795, IoU=0.9513, Dice=0.9750] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.48it/s, Loss=1.0532, IoU=0.5343, Dice=0.6965] 
Train - Loss: 0.5382, IoU: 0.8111, Dice: 0.8915
Val   - Loss: 1.1841, IoU: 0.4626, Dice: 0.6263
LR: 0.000100
New best model saved! Val Dice: 0.6263

Epoch 6/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.3669, IoU=0.9725, Dice=0.9861] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.34it/s, Loss=0.9960, IoU=0.5577, Dice=0.7161] 
Train - Loss: 0.4456, IoU: 0.8765, Dice: 0.9327
Val   - Loss: 1.1775, IoU: 0.4810, Dice: 0.6457
LR: 0.000100
New best model saved! Val Dice: 0.6457

Epoch 7/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.37it/s, Loss=0.4694, IoU=0.8216, Dice=0.9020] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.44it/s, Loss=1.0294, IoU=0.5373, Dice=0.6990] 
Train - Loss: 0.4905, IoU: 0.8338, Dice: 0.9023
Val   - Loss: 1.2909, IoU: 0.4668, Dice: 0.6328
LR: 0.000100

Epoch 8/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.5742, IoU=0.7067, Dice=0.8281] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.48it/s, Loss=1.2967, IoU=0.5164, Dice=0.6811] 
Train - Loss: 0.4275, IoU: 0.8643, Dice: 0.9254
Val   - Loss: 1.3758, IoU: 0.4511, Dice: 0.6177
LR: 0.000100

Epoch 9/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.39it/s, Loss=0.4948, IoU=0.7727, Dice=0.8718] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.28it/s, Loss=1.1278, IoU=0.5264, Dice=0.6898] 
Train - Loss: 0.4719, IoU: 0.8121, Dice: 0.8936
Val   - Loss: 1.3221, IoU: 0.4704, Dice: 0.6377
LR: 0.000100

Epoch 10/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.41it/s, Loss=0.6433, IoU=0.6469, Dice=0.7856] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.38it/s, Loss=1.2512, IoU=0.5215, Dice=0.6855] 
Train - Loss: 0.4352, IoU: 0.8522, Dice: 0.9182
Val   - Loss: 1.3595, IoU: 0.4500, Dice: 0.6157
LR: 0.000100

Epoch 11/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.40it/s, Loss=1.0885, IoU=0.3524, Dice=0.5212] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.46it/s, Loss=1.3927, IoU=0.5155, Dice=0.6803] 
Train - Loss: 0.5268, IoU: 0.7740, Dice: 0.8518
Val   - Loss: 1.4530, IoU: 0.4597, Dice: 0.6252
LR: 0.000100

Epoch 12/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.44it/s, Loss=0.3030, IoU=0.9662, Dice=0.9828] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.64it/s, Loss=1.3281, IoU=0.5135, Dice=0.6786] 
Train - Loss: 0.4390, IoU: 0.8439, Dice: 0.9135
Val   - Loss: 1.4087, IoU: 0.4506, Dice: 0.6180
LR: 0.000050

Epoch 13/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.3771, IoU=0.8904, Dice=0.9420] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.36it/s, Loss=1.3324, IoU=0.5265, Dice=0.6898] 
Train - Loss: 0.4117, IoU: 0.8570, Dice: 0.9207
Val   - Loss: 1.4101, IoU: 0.4629, Dice: 0.6295
LR: 0.000050

Epoch 14/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.3553, IoU=0.8947, Dice=0.9444] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.28it/s, Loss=1.0238, IoU=0.5602, Dice=0.7181] 
Train - Loss: 0.3724, IoU: 0.8815, Dice: 0.9358
Val   - Loss: 1.1179, IoU: 0.4873, Dice: 0.6504
LR: 0.000050
New best model saved! Val Dice: 0.6504

Epoch 15/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.4460, IoU=0.7780, Dice=0.8751] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.43it/s, Loss=0.8205, IoU=0.6144, Dice=0.7612] 
Train - Loss: 0.3765, IoU: 0.8723, Dice: 0.9306
Val   - Loss: 0.8744, IoU: 0.5514, Dice: 0.7068
LR: 0.000050
New best model saved! Val Dice: 0.7068

Epoch 16/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.3132, IoU=0.9351, Dice=0.9665] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.43it/s, Loss=0.7474, IoU=0.7868, Dice=0.8807] 
Train - Loss: 0.3411, IoU: 0.9047, Dice: 0.9496
Val   - Loss: 0.8089, IoU: 0.7351, Dice: 0.8468
LR: 0.000050
New best model saved! Val Dice: 0.8468

Epoch 17/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.35it/s, Loss=0.2676, IoU=0.9702, Dice=0.9849] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.43it/s, Loss=0.8765, IoU=0.2424, Dice=0.3903] 
Train - Loss: 0.3201, IoU: 0.9191, Dice: 0.9576
Val   - Loss: 0.8264, IoU: 0.3290, Dice: 0.4916
LR: 0.000050

Epoch 18/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.35it/s, Loss=0.2623, IoU=0.9658, Dice=0.9826] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.26it/s, Loss=0.7455, IoU=0.6753, Dice=0.8062] 
Train - Loss: 0.3150, IoU: 0.9204, Dice: 0.9583
Val   - Loss: 0.8048, IoU: 0.6082, Dice: 0.7548
LR: 0.000050

Epoch 19/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.37it/s, Loss=0.3348, IoU=0.8953, Dice=0.9447] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.46it/s, Loss=0.9755, IoU=0.2205, Dice=0.3613] 
Train - Loss: 0.3329, IoU: 0.9038, Dice: 0.9472
Val   - Loss: 0.9044, IoU: 0.2989, Dice: 0.4527
LR: 0.000050

Epoch 20/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.2416, IoU=0.9738, Dice=0.9867] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.38it/s, Loss=0.7446, IoU=0.7132, Dice=0.8326] 
Train - Loss: 0.3191, IoU: 0.9106, Dice: 0.9527
Val   - Loss: 0.8447, IoU: 0.6579, Dice: 0.7928
LR: 0.000050

Epoch 21/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.40it/s, Loss=0.2924, IoU=0.9405, Dice=0.9693] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.72it/s, Loss=0.8076, IoU=0.6490, Dice=0.7871] 
Train - Loss: 0.3291, IoU: 0.8968, Dice: 0.9449
Val   - Loss: 0.8206, IoU: 0.6107, Dice: 0.7575
LR: 0.000050

Epoch 22/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.42it/s, Loss=0.2414, IoU=0.9677, Dice=0.9836] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.53it/s, Loss=0.8401, IoU=0.7067, Dice=0.8282] 
Train - Loss: 0.2892, IoU: 0.9296, Dice: 0.9632
Val   - Loss: 0.8427, IoU: 0.6729, Dice: 0.8042
LR: 0.000050

Epoch 23/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.39it/s, Loss=0.2528, IoU=0.9754, Dice=0.9876] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.07it/s, Loss=0.7570, IoU=0.7356, Dice=0.8477] 
Train - Loss: 0.2823, IoU: 0.9351, Dice: 0.9662
Val   - Loss: 0.7757, IoU: 0.7101, Dice: 0.8303
LR: 0.000050

Epoch 24/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.2301, IoU=0.9701, Dice=0.9848] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.44it/s, Loss=0.8081, IoU=0.3083, Dice=0.4713] 
Train - Loss: 0.2843, IoU: 0.9253, Dice: 0.9607
Val   - Loss: 0.7869, IoU: 0.4232, Dice: 0.5850
LR: 0.000050

Epoch 25/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.3759, IoU=0.8821, Dice=0.9374] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.35it/s, Loss=0.6562, IoU=0.7227, Dice=0.8390] 
Train - Loss: 0.3023, IoU: 0.9125, Dice: 0.9537
Val   - Loss: 0.6957, IoU: 0.6677, Dice: 0.7992
LR: 0.000050

Epoch 26/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.41it/s, Loss=0.2592, IoU=0.9516, Dice=0.9752] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.50it/s, Loss=1.1021, IoU=0.5530, Dice=0.7122] 
Train - Loss: 0.2785, IoU: 0.9305, Dice: 0.9636
Val   - Loss: 1.2139, IoU: 0.4868, Dice: 0.6525
LR: 0.000050

Epoch 27/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.44it/s, Loss=0.2767, IoU=0.9317, Dice=0.9646] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.55it/s, Loss=0.8817, IoU=0.6024, Dice=0.7519] 
Train - Loss: 0.3221, IoU: 0.8922, Dice: 0.9413
Val   - Loss: 0.9789, IoU: 0.5240, Dice: 0.6844
LR: 0.000050

Epoch 28/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.37it/s, Loss=0.2177, IoU=0.9798, Dice=0.9898] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.29it/s, Loss=1.1802, IoU=0.2228, Dice=0.3644] 
Train - Loss: 0.2929, IoU: 0.9158, Dice: 0.9550
Val   - Loss: 1.0978, IoU: 0.2528, Dice: 0.4023
LR: 0.000050

Epoch 29/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.37it/s, Loss=0.2476, IoU=0.9520, Dice=0.9754] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.24it/s, Loss=1.0306, IoU=0.2217, Dice=0.3629] 
Train - Loss: 0.2679, IoU: 0.9278, Dice: 0.9624
Val   - Loss: 0.9740, IoU: 0.2700, Dice: 0.4219
LR: 0.000050

Epoch 30/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.38it/s, Loss=0.9137, IoU=0.4499, Dice=0.6206] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.66it/s, Loss=0.5528, IoU=0.7926, Dice=0.8843] 
Train - Loss: 0.3411, IoU: 0.8732, Dice: 0.9248
Val   - Loss: 0.6674, IoU: 0.7001, Dice: 0.8216
LR: 0.000050

Epoch 31/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.44it/s, Loss=0.2118, IoU=0.9811, Dice=0.9904] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.53it/s, Loss=0.5857, IoU=0.8063, Dice=0.8927] 
Train - Loss: 0.2692, IoU: 0.9263, Dice: 0.9612
Val   - Loss: 0.6323, IoU: 0.7457, Dice: 0.8534
LR: 0.000050
New best model saved! Val Dice: 0.8534

Epoch 32/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.45it/s, Loss=0.2069, IoU=0.9688, Dice=0.9841] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.67it/s, Loss=0.7289, IoU=0.7905, Dice=0.8830] 
Train - Loss: 0.2563, IoU: 0.9327, Dice: 0.9648
Val   - Loss: 0.7667, IoU: 0.7623, Dice: 0.8593
LR: 0.000050
New best model saved! Val Dice: 0.8593

Epoch 33/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.44it/s, Loss=0.2493, IoU=0.9413, Dice=0.9698] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.57it/s, Loss=0.6483, IoU=0.7619, Dice=0.8649] 
Train - Loss: 0.2854, IoU: 0.9067, Dice: 0.9493
Val   - Loss: 0.7712, IoU: 0.6856, Dice: 0.8121
LR: 0.000050

Epoch 34/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.45it/s, Loss=0.1921, IoU=0.9822, Dice=0.9910] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.66it/s, Loss=0.6657, IoU=0.8229, Dice=0.9029] 
Train - Loss: 0.2430, IoU: 0.9402, Dice: 0.9691
Val   - Loss: 0.7083, IoU: 0.7820, Dice: 0.8768
LR: 0.000050
New best model saved! Val Dice: 0.8768

Epoch 35/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.45it/s, Loss=0.2155, IoU=0.9716, Dice=0.9856] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.64it/s, Loss=0.7465, IoU=0.4979, Dice=0.6648] 
Train - Loss: 0.2520, IoU: 0.9302, Dice: 0.9636
Val   - Loss: 0.6841, IoU: 0.6093, Dice: 0.7497
LR: 0.000050

Epoch 36/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.45it/s, Loss=0.3733, IoU=0.8085, Dice=0.8941] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.30it/s, Loss=1.1220, IoU=0.2222, Dice=0.3636] 
Train - Loss: 0.2452, IoU: 0.9317, Dice: 0.9640
Val   - Loss: 0.9717, IoU: 0.2746, Dice: 0.4275
LR: 0.000050

Epoch 37/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.40it/s, Loss=0.4757, IoU=0.7443, Dice=0.8534] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.69it/s, Loss=0.5923, IoU=0.8507, Dice=0.9194] 
Train - Loss: 0.2751, IoU: 0.9112, Dice: 0.9525
Val   - Loss: 0.6431, IoU: 0.7935, Dice: 0.8839
LR: 0.000025
New best model saved! Val Dice: 0.8839

Epoch 38/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.40it/s, Loss=0.2038, IoU=0.9754, Dice=0.9875] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.63it/s, Loss=1.4116, IoU=0.2175, Dice=0.3573] 
Train - Loss: 0.2432, IoU: 0.9333, Dice: 0.9653
Val   - Loss: 1.2088, IoU: 0.2673, Dice: 0.4184
LR: 0.000025

Epoch 39/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.42it/s, Loss=0.1912, IoU=0.9796, Dice=0.9897] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.68it/s, Loss=1.0675, IoU=0.2212, Dice=0.3623] 
Train - Loss: 0.2235, IoU: 0.9471, Dice: 0.9727
Val   - Loss: 0.9616, IoU: 0.3101, Dice: 0.4633
LR: 0.000025

Epoch 40/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.41it/s, Loss=0.1915, IoU=0.9726, Dice=0.9861] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.57it/s, Loss=0.6211, IoU=0.8143, Dice=0.8976] 
Train - Loss: 0.2420, IoU: 0.9314, Dice: 0.9641
Val   - Loss: 0.6256, IoU: 0.7956, Dice: 0.8849
LR: 0.000025
New best model saved! Val Dice: 0.8849

Epoch 41/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.39it/s, Loss=0.2027, IoU=0.9572, Dice=0.9781] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.34it/s, Loss=0.5296, IoU=0.8393, Dice=0.9126] 
Train - Loss: 0.2200, IoU: 0.9492, Dice: 0.9739
Val   - Loss: 0.5897, IoU: 0.7857, Dice: 0.8791
LR: 0.000025

Epoch 42/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.2684, IoU=0.9218, Dice=0.9593] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.36it/s, Loss=1.7377, IoU=0.2201, Dice=0.3608] 
Train - Loss: 0.2242, IoU: 0.9446, Dice: 0.9714
Val   - Loss: 1.4979, IoU: 0.2736, Dice: 0.4255
LR: 0.000025

Epoch 43/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.37it/s, Loss=0.2728, IoU=0.9031, Dice=0.9491] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.36it/s, Loss=0.7740, IoU=0.4227, Dice=0.5942] 
Train - Loss: 0.2396, IoU: 0.9339, Dice: 0.9656
Val   - Loss: 0.6709, IoU: 0.5754, Dice: 0.7234
LR: 0.000025

Epoch 44/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.35it/s, Loss=0.2494, IoU=0.9365, Dice=0.9672] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.42it/s, Loss=0.5294, IoU=0.8901, Dice=0.9418] 
Train - Loss: 0.2152, IoU: 0.9518, Dice: 0.9753
Val   - Loss: 0.5373, IoU: 0.8362, Dice: 0.9100
LR: 0.000025
New best model saved! Val Dice: 0.9100

Epoch 45/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.2119, IoU=0.9555, Dice=0.9772] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.28it/s, Loss=0.6629, IoU=0.6715, Dice=0.8034] 
Train - Loss: 0.2115, IoU: 0.9519, Dice: 0.9753
Val   - Loss: 0.6618, IoU: 0.6929, Dice: 0.8155
LR: 0.000025

Epoch 46/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.35it/s, Loss=0.2155, IoU=0.9460, Dice=0.9722] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.47it/s, Loss=1.3887, IoU=0.2228, Dice=0.3644] 
Train - Loss: 0.2112, IoU: 0.9502, Dice: 0.9744
Val   - Loss: 1.2021, IoU: 0.2605, Dice: 0.4113
LR: 0.000025

Epoch 47/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.36it/s, Loss=0.2963, IoU=0.9095, Dice=0.9526] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.62it/s, Loss=1.4978, IoU=0.5159, Dice=0.6807] 
Train - Loss: 0.2048, IoU: 0.9565, Dice: 0.9777
Val   - Loss: 1.6479, IoU: 0.4383, Dice: 0.6052
LR: 0.000025

Epoch 48/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.42it/s, Loss=0.1712, IoU=0.9813, Dice=0.9906] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.56it/s, Loss=1.3997, IoU=0.5329, Dice=0.6953] 
Train - Loss: 0.2104, IoU: 0.9506, Dice: 0.9745
Val   - Loss: 1.5302, IoU: 0.4648, Dice: 0.6318
LR: 0.000025

Epoch 49/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.40it/s, Loss=0.1846, IoU=0.9646, Dice=0.9820] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.62it/s, Loss=0.9908, IoU=0.5827, Dice=0.7363] 
Train - Loss: 0.1970, IoU: 0.9587, Dice: 0.9788
Val   - Loss: 1.0983, IoU: 0.5086, Dice: 0.6715
LR: 0.000025

Epoch 50/50
Training: 100%|████████████████████████████████████| 11/11 [00:04<00:00,  2.40it/s, Loss=0.1956, IoU=0.9747, Dice=0.9872] 
Validation: 100%|████████████████████████████████████| 3/3 [00:00<00:00,  7.54it/s, Loss=0.5665, IoU=0.7455, Dice=0.8542] 
Train - Loss: 0.2012, IoU: 0.9563, Dice: 0.9776
Val   - Loss: 0.6545, IoU: 0.6876, Dice: 0.8142
LR: 0.000013

Training completed in 0.07 hours
Best validation Dice: 0.9100
Results saved to: chess_board_detection\unet_segmentation_results

Testing the best model...
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_unet_augmented.py:271: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  model.load_state_dict(torch.load(model_path, map_location=device))
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Testing model on 5 samples...
Sample 1: IoU=0.8561, Dice=0.9225
Sample 2: IoU=0.9489, Dice=0.9738
Sample 3: IoU=0.8216, Dice=0.9020
Sample 4: IoU=0.6808, Dice=0.8101
Sample 5: IoU=0.8165, Dice=0.8990
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 
