@echo off
REM Visualize evaluation metrics for chess piece detection models

set METRICS_FILE=%1
if "%METRICS_FILE%"=="" (
    echo Error: Please provide the path to the metrics JSON file
    echo Usage: %0 path/to/metrics.json [output_directory]
    exit /b 1
)

set OUTPUT_DIR=%2
if "%OUTPUT_DIR%"=="" (
    set OUTPUT_DIR=chess_board_detection/piece_detection/metric_visualizations
)

echo Visualizing metrics from %METRICS_FILE%...

python chess_board_detection/piece_detection/visualize_metrics.py ^
    --metrics %METRICS_FILE% ^
    --output_dir %OUTPUT_DIR%

echo.
echo Visualizations saved to %OUTPUT_DIR%
echo.
echo Opening visualizations...

start "" "%OUTPUT_DIR%"

pause
