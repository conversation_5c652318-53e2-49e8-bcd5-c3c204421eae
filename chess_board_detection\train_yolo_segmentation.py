"""
Train YOLO v11n for chess board segmentation.
"""

import os
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
from pathlib import Path
import yaml

def train_yolo_segmentation(
    data_yaml,
    model_name="yolo11n-seg.pt",
    epochs=100,
    imgsz=640,
    batch_size=16,
    device=None,
    project="runs/segment",
    name="chessboard_segmentation"
):
    """
    Train YOLO v11n for segmentation.
    
    Args:
        data_yaml: Path to dataset.yaml file
        model_name: YOLO model to use (yolo11n-seg.pt for segmentation)
        epochs: Number of training epochs
        imgsz: Image size for training
        batch_size: Batch size
        device: Device to use (None for auto-detection)
        project: Project directory
        name: Experiment name
    """
    
    # Check if CUDA is available
    if device is None:
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    
    print(f"Using device: {device}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Load YOLO model
    print(f"Loading YOLO model: {model_name}")
    model = YOLO(model_name)
    
    # Adjust batch size based on available memory
    if device == 'cuda':
        gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1e9
        if gpu_memory_gb < 8:  # Less than 8GB
            batch_size = min(batch_size, 8)
            print(f"Adjusted batch size to {batch_size} due to limited GPU memory")
    
    # Training configuration
    train_config = {
        'data': data_yaml,
        'epochs': epochs,
        'imgsz': imgsz,
        'batch': batch_size,
        'device': device,
        'project': project,
        'name': name,
        'save_period': 10,  # Save checkpoint every 10 epochs
        'patience': 20,     # Early stopping patience
        'save': True,       # Save train checkpoints
        'cache': False,     # Don't cache images (to save memory)
        'workers': 4,       # Number of worker threads
        'optimizer': 'AdamW',  # Optimizer
        'lr0': 0.01,        # Initial learning rate
        'lrf': 0.1,         # Final learning rate factor
        'momentum': 0.937,  # Momentum
        'weight_decay': 0.0005,  # Weight decay
        'warmup_epochs': 3,  # Warmup epochs
        'warmup_momentum': 0.8,  # Warmup momentum
        'box': 7.5,         # Box loss gain
        'cls': 0.5,         # Class loss gain
        'dfl': 1.5,         # DFL loss gain
        'pose': 12.0,       # Pose loss gain
        'kobj': 2.0,        # Keypoint obj loss gain
        'label_smoothing': 0.0,  # Label smoothing
        'nbs': 64,          # Nominal batch size
        'hsv_h': 0.015,     # HSV-Hue augmentation
        'hsv_s': 0.7,       # HSV-Saturation augmentation
        'hsv_v': 0.4,       # HSV-Value augmentation
        'degrees': 0.0,     # Rotation degrees (disabled for chessboards)
        'translate': 0.1,   # Translation fraction
        'scale': 0.5,       # Scale fraction
        'shear': 0.0,       # Shear degrees (disabled)
        'perspective': 0.0, # Perspective (disabled for chessboards)
        'flipud': 0.0,      # Vertical flip probability (disabled)
        'fliplr': 0.5,      # Horizontal flip probability
        'mosaic': 1.0,      # Mosaic probability
        'mixup': 0.0,       # Mixup probability (disabled)
        'copy_paste': 0.0,  # Copy-paste probability (disabled)
        'auto_augment': 'randaugment',  # Auto augmentation policy
        'erasing': 0.4,     # Random erasing probability
        'crop_fraction': 1.0,  # Crop fraction
    }
    
    print("Starting training...")
    print(f"Training configuration:")
    for key, value in train_config.items():
        print(f"  {key}: {value}")
    
    # Train the model
    results = model.train(**train_config)
    
    print("Training completed!")
    
    # Get the best model path
    best_model_path = Path(project) / name / "weights" / "best.pt"
    print(f"Best model saved at: {best_model_path}")
    
    return results, best_model_path

def validate_model(model_path, data_yaml):
    """Validate the trained model."""
    print(f"Validating model: {model_path}")
    
    # Load the trained model
    model = YOLO(model_path)
    
    # Run validation
    results = model.val(data=data_yaml)
    
    print("Validation results:")
    print(f"  mAP50: {results.box.map50:.4f}")
    print(f"  mAP50-95: {results.box.map:.4f}")
    
    return results

def test_inference(model_path, test_image_path, output_dir="test_outputs"):
    """Test inference on a sample image."""
    print(f"Testing inference on: {test_image_path}")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load model
    model = YOLO(model_path)
    
    # Run inference
    results = model(test_image_path)
    
    # Save results
    for i, result in enumerate(results):
        # Save annotated image
        output_path = os.path.join(output_dir, f"result_{i}.jpg")
        result.save(output_path)
        print(f"Result saved to: {output_path}")
        
        # Print detection info
        if result.masks is not None:
            print(f"  Detected {len(result.masks)} objects")
            for j, (box, conf, cls) in enumerate(zip(result.boxes.xyxy, result.boxes.conf, result.boxes.cls)):
                print(f"    Object {j}: class={int(cls)}, confidence={conf:.3f}")

if __name__ == "__main__":
    # Configuration
    DATA_YAML = "chess_board_detection/segmentation_dataset/dataset.yaml"
    
    # Check if dataset exists
    if not os.path.exists(DATA_YAML):
        print(f"Error: Dataset file not found: {DATA_YAML}")
        print("Please run prepare_segmentation_dataset.py first")
        exit(1)
    
    print("Starting YOLO v11n segmentation training...")
    
    # Train the model
    results, best_model_path = train_yolo_segmentation(
        data_yaml=DATA_YAML,
        model_name="yolo11n-seg.pt",
        epochs=100,
        imgsz=640,
        batch_size=16,
        name="chessboard_v1"
    )
    
    # Validate the model
    print("\nValidating trained model...")
    val_results = validate_model(best_model_path, DATA_YAML)
    
    # Test inference on a sample image
    test_image = "chess_board_detection/data/real/9.jpg"
    if os.path.exists(test_image):
        print(f"\nTesting inference...")
        test_inference(best_model_path, test_image, "chess_board_detection/segmentation_test_outputs")
    
    print("\nTraining pipeline completed!")
    print(f"Best model: {best_model_path}")
