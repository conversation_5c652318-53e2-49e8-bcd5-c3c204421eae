"""
Inference script for chess board detection.
"""

import os
import argparse
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from PIL import Image
import albumentations as A
from albumentations.pytorch import ToTensorV2

from models.unet import ChessBoardUNet
from config import MODELS_DIR, INPUT_SIZE, DEVICE


def load_model(model_path):
    """
    Load a trained model.
    
    Args:
        model_path (str): Path to the model checkpoint.
    
    Returns:
        model: Loaded PyTorch model.
    """
    # Initialize model
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Set model to evaluation mode
    model.eval()
    model = model.to(DEVICE)
    
    return model


def preprocess_image(image_path):
    """
    Preprocess an image for inference.
    
    Args:
        image_path (str): Path to the image.
    
    Returns:
        tuple: (preprocessed_image, original_image, original_height, original_width)
    """
    # Read image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Store original dimensions
    original_height, original_width = image.shape[:2]
    original_image = image.copy()
    
    # Define preprocessing
    transform = A.Compose([
        A.Resize(INPUT_SIZE[0], INPUT_SIZE[1]),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ])
    
    # Apply preprocessing
    preprocessed = transform(image=image)
    preprocessed_image = preprocessed['image']
    
    # Add batch dimension
    preprocessed_image = preprocessed_image.unsqueeze(0)
    
    return preprocessed_image, original_image, original_height, original_width


def detect_chessboard(model, image_path, output_path=None):
    """
    Detect a chess board in an image.
    
    Args:
        model: PyTorch model.
        image_path (str): Path to the input image.
        output_path (str, optional): Path to save the output visualization.
    
    Returns:
        dict: Detection results.
    """
    # Preprocess image
    preprocessed_image, original_image, original_height, original_width = preprocess_image(image_path)
    
    # Move to device
    preprocessed_image = preprocessed_image.to(DEVICE)
    
    # Perform inference
    with torch.no_grad():
        outputs = model(preprocessed_image)
    
    # Get segmentation mask
    segmentation = outputs['segmentation']
    segmentation = torch.sigmoid(segmentation).cpu().numpy()[0, 0]
    
    # Resize mask to original dimensions
    segmentation = cv2.resize(segmentation, (original_width, original_height))
    
    # Threshold mask
    binary_mask = (segmentation > 0.5).astype(np.uint8) * 255
    
    # Get keypoints
    keypoints = outputs['keypoints'].cpu().numpy()[0]
    
    # Reshape keypoints to (4, 2) and scale to original dimensions
    keypoints = keypoints.reshape(-1, 2)
    keypoints[:, 0] *= original_width / INPUT_SIZE[1]
    keypoints[:, 1] *= original_height / INPUT_SIZE[0]
    
    # Visualize results
    if output_path is not None:
        plt.figure(figsize=(15, 5))
        
        # Original image
        plt.subplot(1, 3, 1)
        plt.imshow(original_image)
        plt.title('Original Image')
        plt.axis('off')
        
        # Segmentation mask
        plt.subplot(1, 3, 2)
        plt.imshow(binary_mask, cmap='gray')
        plt.title('Segmentation Mask')
        plt.axis('off')
        
        # Keypoints
        plt.subplot(1, 3, 3)
        plt.imshow(original_image)
        plt.scatter(keypoints[:, 0], keypoints[:, 1], c='r', s=50)
        
        # Draw the chess board outline
        keypoints_int = keypoints.astype(np.int32)
        cv2.polylines(original_image, [keypoints_int], True, (0, 255, 0), 2)
        plt.imshow(original_image)
        plt.title('Detected Chess Board')
        plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
    
    return {
        'segmentation': binary_mask,
        'keypoints': keypoints.tolist()
    }


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Chess Board Detection')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--model', type=str, default=os.path.join(MODELS_DIR, 'best_model.pth'),
                        help='Path to model checkpoint')
    parser.add_argument('--output', type=str, default=None, help='Path to save output visualization')
    args = parser.parse_args()
    
    # Load model
    model = load_model(args.model)
    
    # Detect chess board
    results = detect_chessboard(model, args.image, args.output)
    
    print(f"Chess board detection completed. Results saved to {args.output}")


if __name__ == "__main__":
    main()
