"""
Training script for chess board detection model using real images.
"""

import os
import time
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import matplotlib.pyplot as plt

from models.unet import ChessBoardUNet
from utils.real_dataset import get_data_loaders
from config import (
    DATA_DIR, MODELS_DIR, DEVICE,
    LEARNING_RATE, NUM_EPOCHS, INPUT_SIZE
)


class DiceLoss(nn.Module):
    """
    Dice loss for segmentation tasks.
    """
    def __init__(self, smooth=1.0):
        super(DiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = torch.sigmoid(pred)

        # Flatten
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)

        # Calculate Dice coefficient
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()

        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        return 1.0 - dice


class HeatmapLoss(nn.Module):
    """
    Enhanced loss for heatmap regression with strong spatial separation.
    Implements Samobot's approach to ensure distinct corner predictions.
    """
    def __init__(self, separation_weight=0.5, peak_separation_weight=0.3):
        super(HeatmapLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight

    def forward(self, pred, target):
        # Basic MSE loss for matching ground truth
        mse_loss = self.mse_loss(pred, target)

        # Add spatial separation loss to ensure corners are distinct
        batch_size = pred.size(0)
        separation_loss = 0.0
        peak_separation_loss = 0.0

        # For each pair of corner heatmaps, encourage separation
        for i in range(4):
            for j in range(i+1, 4):
                # Calculate overlap between different corner heatmaps
                # We want to minimize this overlap
                overlap = torch.sum(pred[:, i] * pred[:, j]) / batch_size
                separation_loss += overlap

                # Find peak locations for each heatmap
                peak_i = self._find_peak_locations(pred[:, i])
                peak_j = self._find_peak_locations(pred[:, j])

                # Calculate distance between peaks
                # We want to maximize this distance (minimize negative distance)
                peak_distance = self._calculate_peak_distance(peak_i, peak_j)
                peak_separation_loss += torch.exp(-peak_distance * 0.1)  # Exponential decay with distance

        # Combine losses
        total_loss = mse_loss + self.separation_weight * separation_loss + self.peak_separation_weight * peak_separation_loss
        return total_loss

    def _find_peak_locations(self, heatmaps):
        """Find the peak location in each heatmap in the batch."""
        batch_size = heatmaps.size(0)
        height = heatmaps.size(1)
        width = heatmaps.size(2)

        # Flatten and find max indices
        flat_indices = torch.argmax(heatmaps.view(batch_size, -1), dim=1)

        # Convert to 2D coordinates
        y = flat_indices // width
        x = flat_indices % width

        # Stack coordinates
        return torch.stack([x, y], dim=1).float()

    def _calculate_peak_distance(self, peaks1, peaks2):
        """Calculate Euclidean distance between peak locations."""
        # Calculate squared distance
        squared_dist = torch.sum((peaks1 - peaks2) ** 2, dim=1)

        # Take square root and mean across batch
        return torch.mean(torch.sqrt(squared_dist + 1e-6))  # Small epsilon to avoid numerical issues


def train_model(model, dataloaders, criterion_seg, criterion_heatmap, optimizer, num_epochs=25):
    """
    Train the model.

    Args:
        model: PyTorch model
        dataloaders: Dictionary with 'train' and 'val' dataloaders
        criterion_seg: Loss function for segmentation
        criterion_heatmap: Loss function for heatmap regression
        optimizer: PyTorch optimizer
        num_epochs: Number of epochs to train for

    Returns:
        model: Trained model
        history: Training history
    """
    since = time.time()

    history = {
        'train_loss': [],
        'val_loss': [],
        'train_seg_loss': [],
        'val_seg_loss': [],
        'train_heatmap_loss': [],
        'val_heatmap_loss': []
    }

    best_model_wts = model.state_dict()
    best_loss = float('inf')

    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)

        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode

            running_loss = 0.0
            running_seg_loss = 0.0
            running_heatmap_loss = 0.0

            # Iterate over data
            for batch_idx, batch in enumerate(tqdm(dataloaders[phase], desc=phase)):
                # Print debug info for first batch
                if batch_idx == 0 and epoch == 0:
                    print(f"\nBatch shapes before moving to device:")
                    print(f"Image: {batch['image'].shape}, device: {batch['image'].device}")
                    print(f"Mask: {batch['mask'].shape}, device: {batch['mask'].device}")
                    print(f"Heatmaps: {batch['corner_heatmaps'].shape}, device: {batch['corner_heatmaps'].device}\n")

                # Move data to device
                inputs = batch['image'].to(DEVICE)
                masks = batch['mask'].to(DEVICE)
                heatmaps = batch['corner_heatmaps'].to(DEVICE)

                # Print debug info for first batch after moving to device
                if batch_idx == 0 and epoch == 0:
                    print(f"\nBatch shapes after moving to device:")
                    print(f"Image: {inputs.shape}, device: {inputs.device}")
                    print(f"Mask: {masks.shape}, device: {masks.device}")
                    print(f"Heatmaps: {heatmaps.shape}, device: {heatmaps.device}\n")

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    heatmap_outputs = outputs['corner_heatmaps']

                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    heatmap_loss = criterion_heatmap(heatmap_outputs, heatmaps)

                    # Combined loss with increased weight for heatmap loss
                    # This puts more emphasis on accurate corner detection
                    loss = seg_loss + 1.5 * heatmap_loss

                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()

                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_heatmap_loss += heatmap_loss.item() * inputs.size(0)

            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_heatmap_loss = running_heatmap_loss / len(dataloaders[phase].dataset)

            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, Heatmap Loss: {epoch_heatmap_loss:.4f}')

            # Record history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_heatmap_loss'].append(epoch_heatmap_loss)

            # Deep copy the model if it's the best so far
            if phase == 'val' and epoch_loss < best_loss:
                best_loss = epoch_loss
                best_model_wts = model.state_dict().copy()

                # Save the best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': best_model_wts,
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': best_loss,
                }, os.path.join(MODELS_DIR, 'best_model_real.pth'))

        print()

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')

    # Load best model weights
    model.load_state_dict(best_model_wts)

    # Save training history
    with open(os.path.join(MODELS_DIR, 'training_history_real.json'), 'w') as f:
        json.dump(history, f)

    # Plot training history
    plot_training_history(history)

    return model, history


def plot_training_history(history):
    """
    Plot training history.

    Args:
        history: Dictionary with training history
    """
    plt.figure(figsize=(12, 4))

    # Plot total loss
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='Train')
    plt.plot(history['val_loss'], label='Validation')
    plt.title('Total Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    # Plot segmentation loss
    plt.subplot(1, 3, 2)
    plt.plot(history['train_seg_loss'], label='Train')
    plt.plot(history['val_seg_loss'], label='Validation')
    plt.title('Segmentation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    # Plot heatmap loss
    plt.subplot(1, 3, 3)
    plt.plot(history['train_heatmap_loss'], label='Train')
    plt.plot(history['val_heatmap_loss'], label='Validation')
    plt.title('Heatmap Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    plt.tight_layout()
    plt.savefig(os.path.join(MODELS_DIR, 'training_history_real.png'))
    plt.close()


def main():
    """
    Main function.
    """
    # Print GPU information
    print("\n" + "="*50)
    print("CUDA Available:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("GPU Device Count:", torch.cuda.device_count())
        print("GPU Device Name:", torch.cuda.get_device_name(0))
        print("Using Device:", DEVICE)
    print("="*50 + "\n")

    # Create directories if they don't exist
    os.makedirs(MODELS_DIR, exist_ok=True)
    os.makedirs(os.path.join(DATA_DIR, 'real'), exist_ok=True)

    # Check if annotation file exists
    annotation_file = os.path.join(DATA_DIR, 'real_annotations.json')
    if not os.path.exists(annotation_file):
        print(f"Annotation file not found: {annotation_file}")
        print("Please run annotate_real_images.py first to create annotations")
        return

    # Get data loaders
    dataloaders = get_data_loaders(
        data_dir=os.path.join(DATA_DIR, 'real'),
        annotation_file=annotation_file
    )

    # Initialize model
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    model = model.to(DEVICE)
    print(f"Model moved to {DEVICE}")

    # Define loss functions and optimizer
    criterion_seg = DiceLoss()
    criterion_heatmap = HeatmapLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Train model
    model, history = train_model(
        model=model,
        dataloaders={'train': dataloaders['train'], 'val': dataloaders['val']},
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap,
        optimizer=optimizer,
        num_epochs=NUM_EPOCHS
    )

    print("Training completed!")


if __name__ == "__main__":
    main()
