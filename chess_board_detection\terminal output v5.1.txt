PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_v5_improved.py" --continue_from_v4 --data_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real" --annotation_file "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json" --output_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection" --epochs_phase1 40 --epochs_phase2 80 --lr_phase1 0.001 --lr_phase2 0.0005 --dropout_rate 0.3 --weight_decay 1e-4 --batch_size 4 --save_interval 5 --cpu --use_scheduler
CUDA not available. Using CPU.
Using device: cpu
Creating expanded validation set with enhanced augmentations...
Train dataset size: 14
Validation dataset size: 16
Initializing improved v5 model...
Loading v4 best model: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v4\best_model.pth
Loaded v4 best model into base model
Model moved to cpu
Model parameters: 17485782
Trainable parameters: 17485782

=== Phase 1: Focus on peak-to-second ratio and detection rate ===
Saving outputs to v5.1 folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Learning rate scheduler initialized
Epoch 1/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
Batch 0, train - Seg Loss: 0.2907, Heatmap Loss: 1286.1458, Geometric Loss: 14464549888.0000, Total: 11571642368.0000
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.22s/it]Batch 1, train - Seg Loss: 0.5151, Heatmap Loss: 1110.2865, Geometric Loss: 1811558016.0000, Total: 1449248128.0000
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.03s/it]Batch 2, train - Seg Loss: 0.5211, Heatmap Loss: 261.1395, Geometric Loss: 43637477376.0000, Total: 34909982720.0000
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.24s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.70s/it]
train Loss: 14533125010.2857, Seg Loss: 0.4493, Heatmap Loss: 912.8430, Geometric Loss: 18166404205.7143
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4286
  avg_peak_to_mean_ratio: 8.0834
  avg_peak_to_second_ratio: 1.0039
  detection_rate: 0.6250
  Overall Confidence Score: 3.0352
train Heatmap Components:
  mse_loss: 1.2698
  separation_loss: 5.0754
  peak_separation_loss: 1095.1922
  edge_suppression_loss: -0.0075
  peak_enhancement_loss: 2.1920
  peak_to_second_ratio_loss: 1.6512
  detection_rate_loss: 0.2433
  segmentation_guidance_loss: 0.7230
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.0865, Heatmap Loss: 571.2217, Geometric Loss: 1165.0884, Total: 1788.9899
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.65s/it]Batch 1, val - Seg Loss: 0.0753, Heatmap Loss: 498.1133, Geometric Loss: 1182.9436, Total: 1693.6002
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.59s/it]Batch 2, val - Seg Loss: 0.0888, Heatmap Loss: 442.5518, Geometric Loss: 352.1268, Total: 945.6179
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.56s/it]
val Loss: 1291.7285, Seg Loss: 0.0843, Heatmap Loss: 499.8914, Geometric Loss: 677.2586
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.3733
  avg_peak_to_mean_ratio: 0.0000
  avg_peak_to_second_ratio: 1.0467
  detection_rate: 0.8125
  Overall Confidence Score: 0.8081
val Heatmap Components:
  mse_loss: 5.6037
  separation_loss: 0.9171
  peak_separation_loss: 573.0311
  edge_suppression_loss: -0.1561
  peak_enhancement_loss: 12.2802
  peak_to_second_ratio_loss: 1.4880
  detection_rate_loss: 0.0627
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 1291.7285
Current learning rate: 0.001000
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 2/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 0, train - Seg Loss: 0.4076, Heatmap Loss: 865.3912, Geometric Loss: 13558721536.0000, Total: 10846978048.0000
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.11s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 1, train - Seg Loss: 0.3426, Heatmap Loss: 950.4127, Geometric Loss: 52985200640.0000, Total: 42388160512.0000
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.46s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 2, train - Seg Loss: 0.4393, Heatmap Loss: 845.4738, Geometric Loss: 1658374400.0000, Total: 1326700800.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.58s/it]
train Loss: 20354802322.2857, Seg Loss: 0.3901, Heatmap Loss: 881.2413, Geometric Loss: 25443501568.0000
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2772
  avg_peak_to_mean_ratio: 6.1473
  avg_peak_to_second_ratio: 1.0039
  detection_rate: 0.6964
  Overall Confidence Score: 2.5312
train Heatmap Components:
  mse_loss: 1.3518
  separation_loss: 5.4493
  peak_separation_loss: 1056.9145
  edge_suppression_loss: -0.0065
  peak_enhancement_loss: 2.4152
  peak_to_second_ratio_loss: 1.6572
  detection_rate_loss: 0.1969
  segmentation_guidance_loss: 0.0330
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.1580, Heatmap Loss: 1119.7911, Geometric Loss: 114.5965, Total: 1771.5220
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.57s/it]Batch 1, val - Seg Loss: 0.1450, Heatmap Loss: 1038.2455, Geometric Loss: 96.0131, Total: 1634.3236
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.51s/it]Batch 2, val - Seg Loss: 0.1125, Heatmap Loss: 1062.4006, Geometric Loss: 24.0738, Total: 1612.9725
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.50s/it]
val Loss: 1708.8817, Seg Loss: 0.1348, Heatmap Loss: 1085.7906, Geometric Loss: 100.0762
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1844
  avg_peak_to_mean_ratio: 2.6058
  avg_peak_to_second_ratio: 1.0081
  detection_rate: 1.0000
  Overall Confidence Score: 1.6996
val Heatmap Components:
  mse_loss: 3.7915
  separation_loss: 0.9647
  peak_separation_loss: 1310.4858
  edge_suppression_loss: -0.1152
  peak_enhancement_loss: 8.1267
  peak_to_second_ratio_loss: 1.6423
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.001000
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 3/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
Batch 0, train - Seg Loss: 0.3762, Heatmap Loss: 914.9936, Geometric Loss: 9065193472.0000, Total: 7252156416.0000
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.63s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
Batch 1, train - Seg Loss: 0.3231, Heatmap Loss: 729.4510, Geometric Loss: 6420299264.0000, Total: 5136240640.0000
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 2, train - Seg Loss: 0.3709, Heatmap Loss: 980.0161, Geometric Loss: 30105122816.0000, Total: 24084101120.0000
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.26s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.63s/it]
train Loss: 10420713960.5848, Seg Loss: 0.3694, Heatmap Loss: 870.4601, Geometric Loss: 13025890176.4997
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5319
  avg_peak_to_mean_ratio: 27.8064
  avg_peak_to_second_ratio: 1.0081
  detection_rate: 0.7321
  Overall Confidence Score: 8.0196
train Heatmap Components:
  mse_loss: 1.4737
  separation_loss: 3.0987
  peak_separation_loss: 1046.1585
  edge_suppression_loss: -0.0050
  peak_enhancement_loss: 2.9905
  peak_to_second_ratio_loss: 1.6390
  detection_rate_loss: 0.1738
  segmentation_guidance_loss: 0.0000
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.1712, Heatmap Loss: 851.3350, Geometric Loss: 1176.1586, Total: 2218.1006
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.56s/it]Batch 1, val - Seg Loss: 0.1235, Heatmap Loss: 759.5582, Geometric Loss: 2060.5249, Total: 2787.8809
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.52s/it]Batch 2, val - Seg Loss: 0.1154, Heatmap Loss: 733.1781, Geometric Loss: 7543.1357, Total: 7134.3911
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.52s/it]
val Loss: 3348.7498, Seg Loss: 0.1276, Heatmap Loss: 790.1548, Geometric Loss: 2704.2373
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3566
  avg_peak_to_mean_ratio: 1.4317
  avg_peak_to_second_ratio: 1.0092
  detection_rate: 1.0000
  Overall Confidence Score: 1.4494
val Heatmap Components:
  mse_loss: 4.4732
  separation_loss: 2.6936
  peak_separation_loss: 939.3140
  edge_suppression_loss: -0.0865
  peak_enhancement_loss: 8.1627
  peak_to_second_ratio_loss: 1.5640
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.001000
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 4/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.02s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.60s/it]
train Loss: 8473394699.1938, Seg Loss: 0.4210, Heatmap Loss: 758.4854, Geometric Loss: 10591742328.8436
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6551
  avg_peak_to_mean_ratio: 24.5751
  avg_peak_to_second_ratio: 1.0068
  detection_rate: 0.7321
  Overall Confidence Score: 7.2423
train Heatmap Components:
  mse_loss: 1.4666
  separation_loss: 2.7905
  peak_separation_loss: 906.7240
  edge_suppression_loss: -0.0010
  peak_enhancement_loss: 2.4066
  peak_to_second_ratio_loss: 1.6084
  detection_rate_loss: 0.1738
  segmentation_guidance_loss: 0.4637
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.50s/it] 
val Loss: 4038.9712, Seg Loss: 0.1587, Heatmap Loss: 935.3444, Geometric Loss: 3294.7447
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.0147
  avg_peak_to_mean_ratio: 1.0894
  avg_peak_to_second_ratio: 1.0071
  detection_rate: 1.0000
  Overall Confidence Score: 1.5278
val Heatmap Components:
  mse_loss: 9.0415
  separation_loss: 3.1452
  peak_separation_loss: 1113.1474
  edge_suppression_loss: -0.0583
  peak_enhancement_loss: 8.2135
  peak_to_second_ratio_loss: 1.6323
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.001000
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Epoch 5/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.30s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.73s/it]
train Loss: 30330226230.8571, Seg Loss: 0.4093, Heatmap Loss: 837.4965, Geometric Loss: 37912781165.7143
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6792
  avg_peak_to_mean_ratio: 2.9747
  avg_peak_to_second_ratio: 1.0074
  detection_rate: 0.6071
  Overall Confidence Score: 1.5671
train Heatmap Components:
  mse_loss: 1.2029
  separation_loss: 5.1404
  peak_separation_loss: 1002.8609
  edge_suppression_loss: -0.0091
  peak_enhancement_loss: 1.9556
  peak_to_second_ratio_loss: 1.6246
  detection_rate_loss: 0.2549
  segmentation_guidance_loss: 0.2548
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.53s/it] 
val Loss: 3188.8310, Seg Loss: 0.1979, Heatmap Loss: 1214.5635, Geometric Loss: 1708.4848
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.9747
  avg_peak_to_mean_ratio: 3.9900
  avg_peak_to_second_ratio: 1.0034
  detection_rate: 1.0000
  Overall Confidence Score: 2.2420
val Heatmap Components:
  mse_loss: 3.3861
  separation_loss: 2.3095
  peak_separation_loss: 1476.1473
  edge_suppression_loss: -0.0217
  peak_enhancement_loss: 3.2815
  peak_to_second_ratio_loss: 1.6460
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.001000
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 4

Epoch 6/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.78s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.27s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.69s/it]
train Loss: 3072333649.3827, Seg Loss: 0.3618, Heatmap Loss: 973.7103, Geometric Loss: 3840415188.6193
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1425
  avg_peak_to_mean_ratio: 13.4029
  avg_peak_to_second_ratio: 1.0039
  detection_rate: 0.7500
  Overall Confidence Score: 4.3248
train Heatmap Components:
  mse_loss: 1.4368
  separation_loss: 1.5035
  peak_separation_loss: 1176.6743
  edge_suppression_loss: -0.0059
  peak_enhancement_loss: 3.0700
  peak_to_second_ratio_loss: 1.6339
  detection_rate_loss: 0.1622
  segmentation_guidance_loss: 0.2801
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.53s/it] 
val Loss: 4406.1447, Seg Loss: 0.2111, Heatmap Loss: 1632.7471, Geometric Loss: 2446.0160
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3273
  avg_peak_to_mean_ratio: 8.5360
  avg_peak_to_second_ratio: 1.0061
  detection_rate: 1.0000
  Overall Confidence Score: 3.2174
val Heatmap Components:
  mse_loss: 2.1034
  separation_loss: 1.7800
  peak_separation_loss: 2001.6281
  edge_suppression_loss: -0.0102
  peak_enhancement_loss: 2.7487
  peak_to_second_ratio_loss: 1.6549
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.001000
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 5

Epoch 7/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.68s/it]
train Loss: 19722277613.7143, Seg Loss: 0.4483, Heatmap Loss: 782.3180, Geometric Loss: 24652846226.2857
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5756
  avg_peak_to_mean_ratio: 4.9831
  avg_peak_to_second_ratio: 1.0064
  detection_rate: 0.5536
  Overall Confidence Score: 2.0297
train Heatmap Components:
  mse_loss: 1.1000
  separation_loss: 4.7295
  peak_separation_loss: 935.1089
  edge_suppression_loss: -0.0049
  peak_enhancement_loss: 2.1965
  peak_to_second_ratio_loss: 1.5929
  detection_rate_loss: 0.2896
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.47s/it] 
val Loss: 3553.0643, Seg Loss: 0.2433, Heatmap Loss: 1544.8149, Geometric Loss: 1544.4981
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3267
  avg_peak_to_mean_ratio: 11.8637
  avg_peak_to_second_ratio: 1.0095
  detection_rate: 1.0000
  Overall Confidence Score: 4.0500
val Heatmap Components:
  mse_loss: 2.0649
  separation_loss: 1.8363
  peak_separation_loss: 1890.5967
  edge_suppression_loss: 0.0068
  peak_enhancement_loss: 2.0728
  peak_to_second_ratio_loss: 1.5680
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 1.4182
Current learning rate: 0.000500
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 8/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.01s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.64s/it]
train Loss: 13604476679.7250, Seg Loss: 0.4439, Heatmap Loss: 653.2398, Geometric Loss: 17005594164.9818
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4029
  avg_peak_to_mean_ratio: 85.0791
  avg_peak_to_second_ratio: 1.0074
  detection_rate: 0.7321
  Overall Confidence Score: 22.3054
train Heatmap Components:
  mse_loss: 1.4342
  separation_loss: 3.1324
  peak_separation_loss: 776.1268
  edge_suppression_loss: -0.0101
  peak_enhancement_loss: 2.4687
  peak_to_second_ratio_loss: 1.5949
  detection_rate_loss: 0.1738
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.49s/it] 
val Loss: 3582.3619, Seg Loss: 0.2730, Heatmap Loss: 1303.2608, Geometric Loss: 2033.9970
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6617
  avg_peak_to_mean_ratio: 6.0034
  avg_peak_to_second_ratio: 1.0084
  detection_rate: 1.0000
  Overall Confidence Score: 2.6684
val Heatmap Components:
  mse_loss: 2.3756
  separation_loss: 2.1963
  peak_separation_loss: 1592.0153
  edge_suppression_loss: 0.0131
  peak_enhancement_loss: 1.6360
  peak_to_second_ratio_loss: 1.5618
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 9/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.12s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.30s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.61s/it]
train Loss: 31704354112.7943, Seg Loss: 0.3838, Heatmap Loss: 549.5486, Geometric Loss: 39630440791.9543
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1723
  avg_peak_to_mean_ratio: 6.4317
  avg_peak_to_second_ratio: 1.0029
  detection_rate: 0.6071
  Overall Confidence Score: 2.5535
train Heatmap Components:
  mse_loss: 1.2012
  separation_loss: 5.0525
  peak_separation_loss: 644.1586
  edge_suppression_loss: -0.0101
  peak_enhancement_loss: 1.6475
  peak_to_second_ratio_loss: 1.6195
  detection_rate_loss: 0.2549
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.50s/it] 
val Loss: 3575.4807, Seg Loss: 0.3156, Heatmap Loss: 781.5756, Geometric Loss: 3003.5019
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.9111
  avg_peak_to_mean_ratio: 14.2221
  avg_peak_to_second_ratio: 1.0081
  detection_rate: 1.0000
  Overall Confidence Score: 4.7853
val Heatmap Components:
  mse_loss: 1.7169
  separation_loss: 2.2959
  peak_separation_loss: 939.1665
  edge_suppression_loss: 0.0191
  peak_enhancement_loss: 2.0177
  peak_to_second_ratio_loss: 1.6129
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 10/40
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.23s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.62s/it]
train Loss: 45134910610.2857, Seg Loss: 0.4489, Heatmap Loss: 480.2432, Geometric Loss: 56418635483.4286
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3584
  avg_peak_to_mean_ratio: 13.6500
  avg_peak_to_second_ratio: 1.0056
  detection_rate: 0.6429
  Overall Confidence Score: 4.4142
train Heatmap Components:
  mse_loss: 1.3732
  separation_loss: 5.8256
  peak_separation_loss: 557.9919
  edge_suppression_loss: -0.0155
  peak_enhancement_loss: 0.9599
  peak_to_second_ratio_loss: 1.5067
  detection_rate_loss: 0.2317
  segmentation_guidance_loss: 0.6262
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.53s/it] 
val Loss: 4413.8662, Seg Loss: 0.3040, Heatmap Loss: 808.8043, Geometric Loss: 4000.4445
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5966
  avg_peak_to_mean_ratio: 173.2177
  avg_peak_to_second_ratio: 1.0077
  detection_rate: 1.0000
  Overall Confidence Score: 44.4555
val Heatmap Components:
  mse_loss: 1.2768
  separation_loss: 2.3408
  peak_separation_loss: 973.5067
  edge_suppression_loss: 0.0174
  peak_enhancement_loss: 2.2116
  peak_to_second_ratio_loss: 1.6102
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Epoch 11/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.51s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.37s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 35370130977.0932, Seg Loss: 0.4515, Heatmap Loss: 531.3035, Geometric Loss: 44212663543.7954
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2766
  avg_peak_to_mean_ratio: 8.3210
  avg_peak_to_second_ratio: 1.0057
  detection_rate: 0.6964
  Overall Confidence Score: 3.0749
train Heatmap Components:
  mse_loss: 1.3404
  separation_loss: 3.9691
  peak_separation_loss: 624.1129
  edge_suppression_loss: -0.0135
  peak_enhancement_loss: 2.3695
  peak_to_second_ratio_loss: 1.5034
  detection_rate_loss: 0.1969
  segmentation_guidance_loss: 0.1110
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.56s/it] 
val Loss: 1815.2901, Seg Loss: 0.3250, Heatmap Loss: 650.6807, Geometric Loss: 1048.6801
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5166
  avg_peak_to_mean_ratio: 64.6261
  avg_peak_to_second_ratio: 1.0122
  detection_rate: 1.0000
  Overall Confidence Score: 17.2887
val Heatmap Components:
  mse_loss: 1.0403
  separation_loss: 3.3038
  peak_separation_loss: 775.4147
  edge_suppression_loss: 0.0124
  peak_enhancement_loss: 2.2809
  peak_to_second_ratio_loss: 1.5808
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 4

Epoch 12/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.22s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.15s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.69s/it]
train Loss: 9855119947.4258, Seg Loss: 0.4442, Heatmap Loss: 621.9514, Geometric Loss: 12318897901.5419
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2596
  avg_peak_to_mean_ratio: 5.3404
  avg_peak_to_second_ratio: 1.0070
  detection_rate: 0.7500
  Overall Confidence Score: 2.3392
train Heatmap Components:
  mse_loss: 1.4533
  separation_loss: 3.0831
  peak_separation_loss: 739.0851
  edge_suppression_loss: -0.0102
  peak_enhancement_loss: 1.7126
  peak_to_second_ratio_loss: 1.5431
  detection_rate_loss: 0.1622
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.52s/it] 
val Loss: 3669.1962, Seg Loss: 0.3312, Heatmap Loss: 560.4136, Geometric Loss: 3535.3056
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3618
  avg_peak_to_mean_ratio: 32.0245
  avg_peak_to_second_ratio: 1.0120
  detection_rate: 1.0000
  Overall Confidence Score: 9.0996
val Heatmap Components:
  mse_loss: 0.9767
  separation_loss: 3.8454
  peak_separation_loss: 661.8801
  edge_suppression_loss: 0.0117
  peak_enhancement_loss: 2.5220
  peak_to_second_ratio_loss: 1.5702
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 5

Epoch 13/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.25s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.68s/it]
train Loss: 20596204184.4946, Seg Loss: 0.4557, Heatmap Loss: 334.5292, Geometric Loss: 25745254109.0005
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9777
  avg_peak_to_mean_ratio: 6.9666
  avg_peak_to_second_ratio: 1.0074
  detection_rate: 0.6071
  Overall Confidence Score: 2.6397
train Heatmap Components:
  mse_loss: 1.1311
  separation_loss: 4.1670
  peak_separation_loss: 378.0634
  edge_suppression_loss: -0.0075
  peak_enhancement_loss: 2.1678
  peak_to_second_ratio_loss: 1.5054
  detection_rate_loss: 0.2549
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.49s/it] 
val Loss: 2686.0262, Seg Loss: 0.3184, Heatmap Loss: 622.6926, Geometric Loss: 2189.5860
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3535
  avg_peak_to_mean_ratio: 7.1259
  avg_peak_to_second_ratio: 1.0150
  detection_rate: 1.0000
  Overall Confidence Score: 2.8736
val Heatmap Components:
  mse_loss: 1.0182
  separation_loss: 3.0542
  peak_separation_loss: 741.9096
  edge_suppression_loss: 0.0081
  peak_enhancement_loss: 1.7751
  peak_to_second_ratio_loss: 1.5540
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000250
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 14/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.13s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.21s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.10s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.57s/it]
train Loss: 21270183748.5831, Seg Loss: 0.4113, Heatmap Loss: 393.2257, Geometric Loss: 26587728169.3068
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1857
  avg_peak_to_mean_ratio: 7.7463
  avg_peak_to_second_ratio: 1.0062
  detection_rate: 0.6607
  Overall Confidence Score: 2.8997
train Heatmap Components:
  mse_loss: 1.2243
  separation_loss: 6.4495
  peak_separation_loss: 447.8808
  edge_suppression_loss: -0.0009
  peak_enhancement_loss: 1.3071
  peak_to_second_ratio_loss: 1.6120
  detection_rate_loss: 0.2201
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.49s/it] 
val Loss: 2268.8673, Seg Loss: 0.3014, Heatmap Loss: 420.6643, Geometric Loss: 2046.9618
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5413
  avg_peak_to_mean_ratio: 10.4894
  avg_peak_to_second_ratio: 1.0112
  detection_rate: 1.0000
  Overall Confidence Score: 3.7605
val Heatmap Components:
  mse_loss: 1.0886
  separation_loss: 3.3636
  peak_separation_loss: 488.3089
  edge_suppression_loss: 0.0099
  peak_enhancement_loss: 2.0936
  peak_to_second_ratio_loss: 1.5641
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000250
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 15/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.09s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.59s/it]
train Loss: 3179990461.8727, Seg Loss: 0.4142, Heatmap Loss: 479.7341, Geometric Loss: 3974987163.6418
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5412
  avg_peak_to_mean_ratio: 6.0964
  avg_peak_to_second_ratio: 1.0109
  detection_rate: 0.7857
  Overall Confidence Score: 2.6085
train Heatmap Components:
  mse_loss: 1.6088
  separation_loss: 2.4377
  peak_separation_loss: 562.1586
  edge_suppression_loss: -0.0103
  peak_enhancement_loss: 2.0777
  peak_to_second_ratio_loss: 1.5235
  detection_rate_loss: 0.1301
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.55s/it] 
val Loss: 2372.4382, Seg Loss: 0.3035, Heatmap Loss: 303.6934, Geometric Loss: 2395.7431
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6275
  avg_peak_to_mean_ratio: 55.2901
  avg_peak_to_second_ratio: 1.0125
  detection_rate: 1.0000
  Overall Confidence Score: 14.9825
val Heatmap Components:
  mse_loss: 0.8923
  separation_loss: 3.8356
  peak_separation_loss: 341.8836
  edge_suppression_loss: 0.0064
  peak_enhancement_loss: 2.3166
  peak_to_second_ratio_loss: 1.5424
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000250
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 16/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.48s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.77s/it]
train Loss: 6128560889.2753, Seg Loss: 0.4205, Heatmap Loss: 330.1053, Geometric Loss: 7660700249.5039
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5459
  avg_peak_to_mean_ratio: 21.6934
  avg_peak_to_second_ratio: 1.0097
  detection_rate: 0.6786
  Overall Confidence Score: 6.4819
train Heatmap Components:
  mse_loss: 1.2377
  separation_loss: 3.5286
  peak_separation_loss: 373.7023
  edge_suppression_loss: -0.0062
  peak_enhancement_loss: 1.5992
  peak_to_second_ratio_loss: 1.4734
  detection_rate_loss: 0.2119
  segmentation_guidance_loss: 0.4942
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.56s/it] 
val Loss: 5365.3131, Seg Loss: 0.3122, Heatmap Loss: 224.2779, Geometric Loss: 6285.7300
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7770
  avg_peak_to_mean_ratio: 19.4427
  avg_peak_to_second_ratio: 1.0145
  detection_rate: 1.0000
  Overall Confidence Score: 6.0586
val Heatmap Components:
  mse_loss: 0.8001
  separation_loss: 4.0734
  peak_separation_loss: 242.6545
  edge_suppression_loss: 0.0050
  peak_enhancement_loss: 2.5770
  peak_to_second_ratio_loss: 1.5133
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000250
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Epoch 17/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.04s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:11,  5.52s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.22s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.65s/it]
train Loss: 38966771712.0000, Seg Loss: 0.4364, Heatmap Loss: 238.7821, Geometric Loss: 48708464640.0000
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4874
  avg_peak_to_mean_ratio: 12.6510
  avg_peak_to_second_ratio: 1.0137
  detection_rate: 0.6429
  Overall Confidence Score: 4.1987
train Heatmap Components:
  mse_loss: 1.3044
  separation_loss: 5.6052
  peak_separation_loss: 259.9484
  edge_suppression_loss: -0.0106
  peak_enhancement_loss: 1.0248
  peak_to_second_ratio_loss: 1.4001
  detection_rate_loss: 0.2317
  segmentation_guidance_loss: 0.0226
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.52s/it] 
val Loss: 5526.1211, Seg Loss: 0.3066, Heatmap Loss: 209.8841, Geometric Loss: 6513.7352
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8287
  avg_peak_to_mean_ratio: 81.4002
  avg_peak_to_second_ratio: 1.0153
  detection_rate: 1.0000
  Overall Confidence Score: 21.5610
val Heatmap Components:
  mse_loss: 0.8046
  separation_loss: 3.6605
  peak_separation_loss: 224.5347
  edge_suppression_loss: 0.0035
  peak_enhancement_loss: 3.0732
  peak_to_second_ratio_loss: 1.5143
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000250
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 4

Epoch 18/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.01s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.07s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.14s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.54s/it]
train Loss: 29168523345.3896, Seg Loss: 0.4102, Heatmap Loss: 320.8100, Geometric Loss: 36460652898.7288
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4255
  avg_peak_to_mean_ratio: 4.7479
  avg_peak_to_second_ratio: 1.0090
  detection_rate: 0.6429
  Overall Confidence Score: 2.2063
train Heatmap Components:
  mse_loss: 1.3018
  separation_loss: 4.7792
  peak_separation_loss: 360.7360
  edge_suppression_loss: -0.0065
  peak_enhancement_loss: 1.7186
  peak_to_second_ratio_loss: 1.5050
  detection_rate_loss: 0.2317
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.49s/it] 
val Loss: 3217.6382, Seg Loss: 0.3075, Heatmap Loss: 300.5607, Geometric Loss: 3458.1120
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6556
  avg_peak_to_mean_ratio: 20.4405
  avg_peak_to_second_ratio: 1.0113
  detection_rate: 1.0000
  Overall Confidence Score: 6.2769
val Heatmap Components:
  mse_loss: 0.8263
  separation_loss: 3.3279
  peak_separation_loss: 337.3697
  edge_suppression_loss: 0.0049
  peak_enhancement_loss: 3.6197
  peak_to_second_ratio_loss: 1.5257
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000250
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 5

Epoch 19/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.35s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.25s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.59s/it]
train Loss: 29313693110.8571, Seg Loss: 0.3844, Heatmap Loss: 175.1157, Geometric Loss: 36642115145.1429
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5050
  avg_peak_to_mean_ratio: 40.4408
  avg_peak_to_second_ratio: 1.0180
  detection_rate: 0.6071
  Overall Confidence Score: 11.1427
train Heatmap Components:
  mse_loss: 1.1451
  separation_loss: 7.3264
  peak_separation_loss: 178.3353
  edge_suppression_loss: -0.0105
  peak_enhancement_loss: 2.3683
  peak_to_second_ratio_loss: 1.3005
  detection_rate_loss: 0.2549
  segmentation_guidance_loss: 0.0356
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.50s/it] 
val Loss: 3255.6524, Seg Loss: 0.3020, Heatmap Loss: 223.9418, Geometric Loss: 3649.2971
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7593
  avg_peak_to_mean_ratio: 42.0525
  avg_peak_to_second_ratio: 1.0195
  detection_rate: 1.0000
  Overall Confidence Score: 11.7078
val Heatmap Components:
  mse_loss: 0.7860
  separation_loss: 3.9111
  peak_separation_loss: 241.9687
  edge_suppression_loss: 0.0024
  peak_enhancement_loss: 3.3101
  peak_to_second_ratio_loss: 1.4905
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000125
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 20/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.61s/it]
train Loss: 2003157310.8004, Seg Loss: 0.4076, Heatmap Loss: 402.4630, Geometric Loss: 2503945827.3557
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8467
  avg_peak_to_mean_ratio: 13.9007
  avg_peak_to_second_ratio: 1.0124
  detection_rate: 0.7857
  Overall Confidence Score: 4.6364
train Heatmap Components:
  mse_loss: 1.3228
  separation_loss: 2.7510
  peak_separation_loss: 465.5217
  edge_suppression_loss: -0.0050
  peak_enhancement_loss: 1.8117
  peak_to_second_ratio_loss: 1.5369
  detection_rate_loss: 0.1390
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 3642.4900, Seg Loss: 0.2842, Heatmap Loss: 375.2458, Geometric Loss: 3849.1714
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8216
  avg_peak_to_mean_ratio: 27.1407
  avg_peak_to_second_ratio: 1.0214
  detection_rate: 1.0000
  Overall Confidence Score: 7.9959
val Heatmap Components:
  mse_loss: 1.0669
  separation_loss: 3.3832
  peak_separation_loss: 431.1388
  edge_suppression_loss: 0.0031
  peak_enhancement_loss: 3.3054
  peak_to_second_ratio_loss: 1.5051
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000125
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 21/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.25s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.61s/it]
train Loss: 12490785632.0000, Seg Loss: 0.4217, Heatmap Loss: 403.6782, Geometric Loss: 15613481474.2857
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1876
  avg_peak_to_mean_ratio: 6.0260
  avg_peak_to_second_ratio: 1.0072
  detection_rate: 0.6071
  Overall Confidence Score: 2.4570
train Heatmap Components:
  mse_loss: 1.1335
  separation_loss: 4.3029
  peak_separation_loss: 465.4265
  edge_suppression_loss: -0.0028
  peak_enhancement_loss: 1.7445
  peak_to_second_ratio_loss: 1.4748
  detection_rate_loss: 0.2546
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.49s/it] 
val Loss: 2409.7473, Seg Loss: 0.2919, Heatmap Loss: 387.8224, Geometric Loss: 2284.6522
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7719
  avg_peak_to_mean_ratio: 12.2836
  avg_peak_to_second_ratio: 1.0196
  detection_rate: 1.0000
  Overall Confidence Score: 4.2687
val Heatmap Components:
  mse_loss: 1.1133
  separation_loss: 3.1777
  peak_separation_loss: 448.2794
  edge_suppression_loss: 0.0009
  peak_enhancement_loss: 3.1913
  peak_to_second_ratio_loss: 1.4477
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000125
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 22/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.48s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.50s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.69s/it]
train Loss: 15637160640.0000, Seg Loss: 0.4745, Heatmap Loss: 376.4459, Geometric Loss: 19546449462.8571
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4597
  avg_peak_to_mean_ratio: 17.1975
  avg_peak_to_second_ratio: 1.0077
  detection_rate: 0.6964
  Overall Confidence Score: 5.3403
train Heatmap Components:
  mse_loss: 1.4233
  separation_loss: 2.9967
  peak_separation_loss: 432.4324
  edge_suppression_loss: -0.0115
  peak_enhancement_loss: 1.6720
  peak_to_second_ratio_loss: 1.5229
  detection_rate_loss: 0.1969
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.51s/it] 
val Loss: 4040.3298, Seg Loss: 0.3061, Heatmap Loss: 372.2581, Geometric Loss: 4352.0457
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7662
  avg_peak_to_mean_ratio: 13.9495
  avg_peak_to_second_ratio: 1.0147
  detection_rate: 1.0000
  Overall Confidence Score: 4.6826
val Heatmap Components:
  mse_loss: 1.0013
  separation_loss: 3.5726
  peak_separation_loss: 427.0728
  edge_suppression_loss: 0.0036
  peak_enhancement_loss: 3.2279
  peak_to_second_ratio_loss: 1.5196
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000125
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Epoch 23/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.23s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.67s/it]
train Loss: 27044690597.7426, Seg Loss: 0.4769, Heatmap Loss: 216.2781, Geometric Loss: 33805862543.4892
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3443
  avg_peak_to_mean_ratio: 29.7920
  avg_peak_to_second_ratio: 1.0088
  detection_rate: 0.6071
  Overall Confidence Score: 8.4381
train Heatmap Components:
  mse_loss: 1.1776
  separation_loss: 5.9501
  peak_separation_loss: 229.7520
  edge_suppression_loss: -0.0161
  peak_enhancement_loss: 1.5987
  peak_to_second_ratio_loss: 1.4485
  detection_rate_loss: 0.2549
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.52s/it] 
val Loss: 3069083725.2759, Seg Loss: 0.3194, Heatmap Loss: 392.0334, Geometric Loss: 3836353607.8286
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8760
  avg_peak_to_mean_ratio: 12.2787
  avg_peak_to_second_ratio: 1.0236
  detection_rate: 1.0000
  Overall Confidence Score: 4.2946
val Heatmap Components:
  mse_loss: 0.9528
  separation_loss: 5.4659
  peak_separation_loss: 451.9773
  edge_suppression_loss: -0.0025
  peak_enhancement_loss: 4.2490
  peak_to_second_ratio_loss: 1.3191
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000125
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 4

Epoch 24/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.28s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.14s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.58s/it]
train Loss: 43670600685.6524, Seg Loss: 0.4294, Heatmap Loss: 234.2665, Geometric Loss: 54588250214.7906
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9581
  avg_peak_to_mean_ratio: 16.6820
  avg_peak_to_second_ratio: 1.0117
  detection_rate: 0.6786
  Overall Confidence Score: 5.3326
train Heatmap Components:
  mse_loss: 1.3352
  separation_loss: 6.1816
  peak_separation_loss: 252.3432
  edge_suppression_loss: -0.0062
  peak_enhancement_loss: 1.6750
  peak_to_second_ratio_loss: 1.3878
  detection_rate_loss: 0.2085
  segmentation_guidance_loss: 0.3607
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.50s/it] 
val Loss: 4626.4823, Seg Loss: 0.3224, Heatmap Loss: 386.5151, Geometric Loss: 5057.9839
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7758
  avg_peak_to_mean_ratio: 6.7744
  avg_peak_to_second_ratio: 1.0158
  detection_rate: 1.0000
  Overall Confidence Score: 2.8915
val Heatmap Components:
  mse_loss: 1.0099
  separation_loss: 2.7552
  peak_separation_loss: 447.5485
  edge_suppression_loss: -0.0009
  peak_enhancement_loss: 2.7146
  peak_to_second_ratio_loss: 1.4665
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000125
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 5

Epoch 25/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.30s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.07s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.58s/it]
train Loss: 9857282471.1810, Seg Loss: 0.4054, Heatmap Loss: 347.4921, Geometric Loss: 12321602187.4522
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.7966
  avg_peak_to_mean_ratio: 3.2904
  avg_peak_to_second_ratio: 1.0100
  detection_rate: 0.6429
  Overall Confidence Score: 1.9350
train Heatmap Components:
  mse_loss: 1.1700
  separation_loss: 8.2549
  peak_separation_loss: 393.2618
  edge_suppression_loss: -0.0055
  peak_enhancement_loss: 1.6514
  peak_to_second_ratio_loss: 1.3306
  detection_rate_loss: 0.2317
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.50s/it] 
val Loss: 7857.9625, Seg Loss: 0.3210, Heatmap Loss: 353.6705, Geometric Loss: 9158.9197
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8784
  avg_peak_to_mean_ratio: 31.3553
  avg_peak_to_second_ratio: 1.0150
  detection_rate: 1.0000
  Overall Confidence Score: 9.0622
val Heatmap Components:
  mse_loss: 1.0265
  separation_loss: 3.0925
  peak_separation_loss: 405.0815
  edge_suppression_loss: 0.0076
  peak_enhancement_loss: 3.4951
  peak_to_second_ratio_loss: 1.4656
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000063
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 26/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.19s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.43s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:18<00:06,  6.58s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:21<00:00,  5.41s/it]
train Loss: 13860018176.2857, Seg Loss: 0.4135, Heatmap Loss: 171.2345, Geometric Loss: 17325021572.0357
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.2739
  avg_peak_to_mean_ratio: 20.4218
  avg_peak_to_second_ratio: 1.0171
  detection_rate: 0.7143
  Overall Confidence Score: 6.3568
train Heatmap Components:
  mse_loss: 1.4117
  separation_loss: 5.2645
  peak_separation_loss: 175.2083
  edge_suppression_loss: -0.0106
  peak_enhancement_loss: 3.7589
  peak_to_second_ratio_loss: 1.2774
  detection_rate_loss: 0.1853
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.87s/it] 
val Loss: 3063.7129, Seg Loss: 0.3194, Heatmap Loss: 288.8368, Geometric Loss: 3287.6726
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.0668
  avg_peak_to_mean_ratio: 18.6140
  avg_peak_to_second_ratio: 1.0222
  detection_rate: 1.0000
  Overall Confidence Score: 5.9258
val Heatmap Components:
  mse_loss: 0.9254
  separation_loss: 3.3622
  peak_separation_loss: 325.7482
  edge_suppression_loss: 0.0001
  peak_enhancement_loss: 3.3771
  peak_to_second_ratio_loss: 1.3716
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000063
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 27/40
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.93s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:17<00:05,  5.76s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.02s/it]
train Loss: 3182969229.2573, Seg Loss: 0.3998, Heatmap Loss: 366.4722, Geometric Loss: 3978711029.3854
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.4057
  avg_peak_to_mean_ratio: 16.2514
  avg_peak_to_second_ratio: 1.0136
  detection_rate: 0.8214
  Overall Confidence Score: 5.3730
train Heatmap Components:
  mse_loss: 1.5510
  separation_loss: 2.9367
  peak_separation_loss: 424.1785
  edge_suppression_loss: -0.0192
  peak_enhancement_loss: 1.5004
  peak_to_second_ratio_loss: 1.3489
  detection_rate_loss: 0.1158
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.82s/it] 
val Loss: 3836.4411, Seg Loss: 0.3078, Heatmap Loss: 319.2323, Geometric Loss: 4196.6059
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.0952
  avg_peak_to_mean_ratio: 17.6231
  avg_peak_to_second_ratio: 1.0232
  detection_rate: 1.0000
  Overall Confidence Score: 5.6854
val Heatmap Components:
  mse_loss: 1.0209
  separation_loss: 3.5164
  peak_separation_loss: 361.5694
  edge_suppression_loss: 0.0002
  peak_enhancement_loss: 3.8757
  peak_to_second_ratio_loss: 1.4376
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000063
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 28/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.91s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.79s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.10s/it]
train Loss: 9338805196.9660, Seg Loss: 0.4671, Heatmap Loss: 378.0054, Geometric Loss: 11673505660.0123
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0480
  avg_peak_to_mean_ratio: 16.7214
  avg_peak_to_second_ratio: 1.0131
  detection_rate: 0.7857
  Overall Confidence Score: 5.3921
train Heatmap Components:
  mse_loss: 1.5215
  separation_loss: 3.4049
  peak_separation_loss: 435.4939
  edge_suppression_loss: -0.0170
  peak_enhancement_loss: 1.6091
  peak_to_second_ratio_loss: 1.4653
  detection_rate_loss: 0.1390
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 5730.1569, Seg Loss: 0.3092, Heatmap Loss: 329.6405, Geometric Loss: 6544.2338
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.1186
  avg_peak_to_mean_ratio: 19.3261
  avg_peak_to_second_ratio: 1.0220
  detection_rate: 1.0000
  Overall Confidence Score: 6.1167
val Heatmap Components:
  mse_loss: 0.9733
  separation_loss: 4.7156
  peak_separation_loss: 375.2864
  edge_suppression_loss: -0.0007
  peak_enhancement_loss: 3.5192
  peak_to_second_ratio_loss: 1.3469
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000063
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Epoch 29/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.17s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.68s/it]
train Loss: 9197515587.1907, Seg Loss: 0.4368, Heatmap Loss: 323.9973, Geometric Loss: 11496893658.1553
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8066
  avg_peak_to_mean_ratio: 6.5604
  avg_peak_to_second_ratio: 1.0179
  detection_rate: 0.6786
  Overall Confidence Score: 2.7658
train Heatmap Components:
  mse_loss: 1.2957
  separation_loss: 4.6889
  peak_separation_loss: 368.3075
  edge_suppression_loss: -0.0153
  peak_enhancement_loss: 1.4785
  peak_to_second_ratio_loss: 1.3490
  detection_rate_loss: 0.2085
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.52s/it] 
val Loss: 5679.4235, Seg Loss: 0.3108, Heatmap Loss: 319.7000, Geometric Loss: 6499.4529
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.2888
  avg_peak_to_mean_ratio: 21.3688
  avg_peak_to_second_ratio: 1.0277
  detection_rate: 1.0000
  Overall Confidence Score: 6.6713
val Heatmap Components:
  mse_loss: 1.0670
  separation_loss: 4.3373
  peak_separation_loss: 363.2378
  edge_suppression_loss: -0.0001
  peak_enhancement_loss: 4.7256
  peak_to_second_ratio_loss: 1.2653
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000063
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 4

Epoch 30/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:04<00:14,  5.00s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.20s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.64s/it]
train Loss: 1246685734.8185, Seg Loss: 0.4208, Heatmap Loss: 239.9212, Geometric Loss: 1558356845.3961
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.3561
  avg_peak_to_mean_ratio: 19.1369
  avg_peak_to_second_ratio: 1.0195
  detection_rate: 0.7679
  Overall Confidence Score: 6.0701
train Heatmap Components:
  mse_loss: 1.4595
  separation_loss: 4.4536
  peak_separation_loss: 265.8968
  edge_suppression_loss: -0.0215
  peak_enhancement_loss: 1.8023
  peak_to_second_ratio_loss: 1.2203
  detection_rate_loss: 0.1506
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.50s/it] 
val Loss: 2576.5184, Seg Loss: 0.2965, Heatmap Loss: 295.9717, Geometric Loss: 2665.3304
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.1331
  avg_peak_to_mean_ratio: 26.2840
  avg_peak_to_second_ratio: 1.0222
  detection_rate: 1.0000
  Overall Confidence Score: 7.8598
val Heatmap Components:
  mse_loss: 0.9239
  separation_loss: 3.1320
  peak_separation_loss: 336.2013
  edge_suppression_loss: -0.0051
  peak_enhancement_loss: 3.2604
  peak_to_second_ratio_loss: 1.3133
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000063
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 5

Epoch 31/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.93s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.39s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.77s/it]
train Loss: 13900922905.4762, Seg Loss: 0.3956, Heatmap Loss: 169.2231, Geometric Loss: 17376152929.8538
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.1126
  avg_peak_to_mean_ratio: 19.1483
  avg_peak_to_second_ratio: 1.0168
  detection_rate: 0.7321
  Overall Confidence Score: 6.0025
train Heatmap Components:
  mse_loss: 1.3836
  separation_loss: 4.9970
  peak_separation_loss: 173.1746
  edge_suppression_loss: -0.0211
  peak_enhancement_loss: 2.2912
  peak_to_second_ratio_loss: 1.3728
  detection_rate_loss: 0.1801
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 4794.9666, Seg Loss: 0.3158, Heatmap Loss: 323.3283, Geometric Loss: 5387.0729
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.0583
  avg_peak_to_mean_ratio: 15.1419
  avg_peak_to_second_ratio: 1.0263
  detection_rate: 1.0000
  Overall Confidence Score: 5.0566
val Heatmap Components:
  mse_loss: 0.8503
  separation_loss: 4.1257
  peak_separation_loss: 367.8645
  edge_suppression_loss: -0.0014
  peak_enhancement_loss: 3.1951
  peak_to_second_ratio_loss: 1.3911
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000031
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 32/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.36s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 15071184603.4286, Seg Loss: 0.4336, Heatmap Loss: 240.9634, Geometric Loss: 18838979291.4286
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8113
  avg_peak_to_mean_ratio: 6.9138
  avg_peak_to_second_ratio: 1.0121
  detection_rate: 0.6429
  Overall Confidence Score: 2.8450
train Heatmap Components:
  mse_loss: 1.2369
  separation_loss: 4.1522
  peak_separation_loss: 262.8630
  edge_suppression_loss: -0.0171
  peak_enhancement_loss: 3.5650
  peak_to_second_ratio_loss: 1.3255
  detection_rate_loss: 0.2317
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 5621.9728, Seg Loss: 0.3085, Heatmap Loss: 341.4371, Geometric Loss: 6386.8856
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.9865
  avg_peak_to_mean_ratio: 23.8355
  avg_peak_to_second_ratio: 1.0254
  detection_rate: 1.0000
  Overall Confidence Score: 7.2119
val Heatmap Components:
  mse_loss: 0.8330
  separation_loss: 4.5122
  peak_separation_loss: 388.3871
  edge_suppression_loss: -0.0105
  peak_enhancement_loss: 4.4319
  peak_to_second_ratio_loss: 1.3974
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000031
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 33/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.59s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.58s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.84s/it]
train Loss: 47581518062.2159, Seg Loss: 0.4339, Heatmap Loss: 170.9107, Geometric Loss: 59476895483.7457
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6858
  avg_peak_to_mean_ratio: 37.5438
  avg_peak_to_second_ratio: 1.0177
  detection_rate: 0.5714
  Overall Confidence Score: 10.4547
train Heatmap Components:
  mse_loss: 1.0731
  separation_loss: 8.9862
  peak_separation_loss: 169.2976
  edge_suppression_loss: -0.0132
  peak_enhancement_loss: 1.5965
  peak_to_second_ratio_loss: 1.4404
  detection_rate_loss: 0.2780
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.53s/it] 
val Loss: 7770.0717, Seg Loss: 0.2949, Heatmap Loss: 297.3701, Geometric Loss: 9154.6516
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.2771
  avg_peak_to_mean_ratio: 26.8994
  avg_peak_to_second_ratio: 1.0250
  detection_rate: 1.0000
  Overall Confidence Score: 8.0504
val Heatmap Components:
  mse_loss: 0.9704
  separation_loss: 5.0274
  peak_separation_loss: 332.4635
  edge_suppression_loss: -0.0066
  peak_enhancement_loss: 4.3010
  peak_to_second_ratio_loss: 1.4071
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000031
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 34/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.26s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.17s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.61s/it]
train Loss: 49141702948.5714, Seg Loss: 0.4392, Heatmap Loss: 102.6662, Geometric Loss: 61427129490.2857
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4919
  avg_peak_to_mean_ratio: 12.3123
  avg_peak_to_second_ratio: 1.0151
  detection_rate: 0.5536
  Overall Confidence Score: 4.0932
train Heatmap Components:
  mse_loss: 1.1082
  separation_loss: 6.8751
  peak_separation_loss: 92.2103
  edge_suppression_loss: -0.0205
  peak_enhancement_loss: 1.2101
  peak_to_second_ratio_loss: 1.1605
  detection_rate_loss: 0.2896
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 3227.4647, Seg Loss: 0.3106, Heatmap Loss: 310.1306, Geometric Loss: 3452.4475
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.0070
  avg_peak_to_mean_ratio: 11.3365
  avg_peak_to_second_ratio: 1.0199
  detection_rate: 1.0000
  Overall Confidence Score: 4.0909
val Heatmap Components:
  mse_loss: 0.8265
  separation_loss: 3.4503
  peak_separation_loss: 350.6839
  edge_suppression_loss: -0.0086
  peak_enhancement_loss: 5.0539
  peak_to_second_ratio_loss: 1.3508
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000031
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Epoch 35/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.54s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.60s/it]
train Loss: 29268370576.9791, Seg Loss: 0.4237, Heatmap Loss: 158.8240, Geometric Loss: 36585462852.2291
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0756
  avg_peak_to_mean_ratio: 12.8703
  avg_peak_to_second_ratio: 1.0103
  detection_rate: 0.6607
  Overall Confidence Score: 4.4042
train Heatmap Components:
  mse_loss: 1.2526
  separation_loss: 4.8043
  peak_separation_loss: 162.8412
  edge_suppression_loss: -0.0160
  peak_enhancement_loss: 1.8787
  peak_to_second_ratio_loss: 1.2580
  detection_rate_loss: 0.2201
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.49s/it] 
val Loss: 3379.2084, Seg Loss: 0.3021, Heatmap Loss: 229.5793, Geometric Loss: 3793.1717
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.5172
  avg_peak_to_mean_ratio: 73.1754
  avg_peak_to_second_ratio: 1.0284
  detection_rate: 1.0000
  Overall Confidence Score: 19.6803
val Heatmap Components:
  mse_loss: 0.9652
  separation_loss: 4.2262
  peak_separation_loss: 250.3819
  edge_suppression_loss: -0.0082
  peak_enhancement_loss: 4.3009
  peak_to_second_ratio_loss: 1.3193
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000031
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 4

Epoch 36/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.61s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.22s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.57s/it]
train Loss: 10332530102.8571, Seg Loss: 0.3953, Heatmap Loss: 242.5270, Geometric Loss: 12915662409.1429
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.7473
  avg_peak_to_mean_ratio: 15.4576
  avg_peak_to_second_ratio: 1.0188
  detection_rate: 0.6250
  Overall Confidence Score: 4.9622
train Heatmap Components:
  mse_loss: 1.1843
  separation_loss: 4.1317
  peak_separation_loss: 262.5158
  edge_suppression_loss: -0.0026
  peak_enhancement_loss: 3.2302
  peak_to_second_ratio_loss: 1.4683
  detection_rate_loss: 0.2433
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.55s/it] 
val Loss: 4241.7109, Seg Loss: 0.3028, Heatmap Loss: 223.6249, Geometric Loss: 4882.4631
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.2734
  avg_peak_to_mean_ratio: 14.6594
  avg_peak_to_second_ratio: 1.0275
  detection_rate: 1.0000
  Overall Confidence Score: 4.9901
val Heatmap Components:
  mse_loss: 0.8654
  separation_loss: 4.2860
  peak_separation_loss: 243.9861
  edge_suppression_loss: -0.0020
  peak_enhancement_loss: 3.4341
  peak_to_second_ratio_loss: 1.3235
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000031
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 5

Epoch 37/40
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 33783643446.8571, Seg Loss: 0.4220, Heatmap Loss: 227.3130, Geometric Loss: 42229553705.1429
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9491
  avg_peak_to_mean_ratio: 8.3241
  avg_peak_to_second_ratio: 1.0183
  detection_rate: 0.6429
  Overall Confidence Score: 3.2336
train Heatmap Components:
  mse_loss: 1.2495
  separation_loss: 6.9859
  peak_separation_loss: 250.7202
  edge_suppression_loss: -0.0189
  peak_enhancement_loss: 1.7009
  peak_to_second_ratio_loss: 0.9977
  detection_rate_loss: 0.2317
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 3195.9706, Seg Loss: 0.3013, Heatmap Loss: 341.0692, Geometric Loss: 3355.0818
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.1288
  avg_peak_to_mean_ratio: 32.4631
  avg_peak_to_second_ratio: 1.0247
  detection_rate: 1.0000
  Overall Confidence Score: 9.4041
val Heatmap Components:
  mse_loss: 0.8808
  separation_loss: 3.4897
  peak_separation_loss: 390.1128
  edge_suppression_loss: -0.0046
  peak_enhancement_loss: 3.5757
  peak_to_second_ratio_loss: 1.4025
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000016
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 0

Epoch 38/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.09s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.63s/it]
train Loss: 33501943954.2857, Seg Loss: 0.4433, Heatmap Loss: 177.2183, Geometric Loss: 41877428516.5714
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.1056
  avg_peak_to_mean_ratio: 121.7287
  avg_peak_to_second_ratio: 1.0218
  detection_rate: 0.6250
  Overall Confidence Score: 31.6203
train Heatmap Components:
  mse_loss: 1.2259
  separation_loss: 6.3170
  peak_separation_loss: 185.2420
  edge_suppression_loss: -0.0204
  peak_enhancement_loss: 1.2794
  peak_to_second_ratio_loss: 1.2184
  detection_rate_loss: 0.2433
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.51s/it] 
val Loss: 3324.8352, Seg Loss: 0.3031, Heatmap Loss: 207.9876, Geometric Loss: 3765.6885
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.2578
  avg_peak_to_mean_ratio: 19.3717
  avg_peak_to_second_ratio: 1.0219
  detection_rate: 1.0000
  Overall Confidence Score: 6.1628
val Heatmap Components:
  mse_loss: 0.9250
  separation_loss: 3.9241
  peak_separation_loss: 223.9635
  edge_suppression_loss: -0.0069
  peak_enhancement_loss: 3.5351
  peak_to_second_ratio_loss: 1.3626
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000016
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 1

Epoch 39/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.19s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.57s/it]
train Loss: 6008673431.2258, Seg Loss: 0.4111, Heatmap Loss: 206.1569, Geometric Loss: 7510841030.0867
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8018
  avg_peak_to_mean_ratio: 9.6344
  avg_peak_to_second_ratio: 1.0117
  detection_rate: 0.6429
  Overall Confidence Score: 3.5227
train Heatmap Components:
  mse_loss: 1.2927
  separation_loss: 6.4180
  peak_separation_loss: 221.4841
  edge_suppression_loss: -0.0219
  peak_enhancement_loss: 2.8700
  peak_to_second_ratio_loss: 1.1100
  detection_rate_loss: 0.2202
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.50s/it] 
val Loss: 3789.1093, Seg Loss: 0.3056, Heatmap Loss: 200.7361, Geometric Loss: 4359.6245
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.3308
  avg_peak_to_mean_ratio: 62.5890
  avg_peak_to_second_ratio: 1.0239
  detection_rate: 1.0000
  Overall Confidence Score: 16.9859
val Heatmap Components:
  mse_loss: 0.9761
  separation_loss: 3.0119
  peak_separation_loss: 215.3090
  edge_suppression_loss: -0.0068
  peak_enhancement_loss: 3.3193
  peak_to_second_ratio_loss: 1.4126
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000016
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 2

Epoch 40/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.13s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.58s/it]
train Loss: 8135673874.2857, Seg Loss: 0.4629, Heatmap Loss: 346.3942, Geometric Loss: 10169591570.2857
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9594
  avg_peak_to_mean_ratio: 5.4258
  avg_peak_to_second_ratio: 1.0210
  detection_rate: 0.7143
  Overall Confidence Score: 2.5301
train Heatmap Components:
  mse_loss: 1.3412
  separation_loss: 4.0472
  peak_separation_loss: 397.4781
  edge_suppression_loss: -0.0199
  peak_enhancement_loss: 2.2232
  peak_to_second_ratio_loss: 1.2891
  detection_rate_loss: 0.1853
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:09<00:00,  2.45s/it] 
val Loss: 4663.1689, Seg Loss: 0.2986, Heatmap Loss: 237.6388, Geometric Loss: 5383.0149
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.3934
  avg_peak_to_mean_ratio: 14.8497
  avg_peak_to_second_ratio: 1.0286
  detection_rate: 1.0000
  Overall Confidence Score: 5.0679
val Heatmap Components:
  mse_loss: 0.8681
  separation_loss: 4.9304
  peak_separation_loss: 260.6753
  edge_suppression_loss: -0.0049
  peak_enhancement_loss: 3.9161
  peak_to_second_ratio_loss: 1.2926
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000016
Scheduler state - best: 1291.7284545898438, num_bad_epochs: 3

Training complete in 19m 25s
Best val loss: 1291.7285

