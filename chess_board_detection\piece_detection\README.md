# Chess Piece Detection with YOLO11n

This module implements chess piece detection using YOLO11n within the detected chess board area, with the goal of generating FEN notation for chess positions.

## Overview

The chess piece detection pipeline works as follows:
1. Detect the chess board using the distilled segmentation model
2. Extract and normalize the chess board region
3. Detect chess pieces within the normalized board using YOLO11n
4. Map detected pieces to chess board coordinates (A1-H8)
5. Generate chess position notation (FEN)

## Directory Structure

```
chess_board_detection/piece_detection/
├── dataset/                  # Dataset directory
│   ├── images/               # Original labeled images
│   └── labels/               # YOLO format labels
├── dataset_split/            # Split dataset for training
│   ├── images/
│   │   ├── train/            # Training images
│   │   └── val/              # Validation images
│   └── labels/
│       ├── train/            # Training labels
│       └── val/              # Validation labels
├── models/                   # Trained models
├── outputs/                  # Detection outputs
├── chess_pieces.yaml         # Dataset configuration
├── detect_pieces.py          # Piece detection script
├── label_tool.py             # Labeling tool
├── run_labeling.py           # Script to run the labeling tool
├── split_dataset.py          # Script to split the dataset
└── train_yolo_model.py       # Training script
```

## Setup Instructions

### 1. Install Dependencies

```bash
pip install ultralytics  # For YOLO models
```

### 2. Dataset Preparation

We've created a custom labeling tool for chess pieces:

```bash
python chess_board_detection/piece_detection/run_labeling.py --mode label --image_dir "chess_board_detection/data/real" --corner_annotations "chess_board_detection/data/real_annotations.json"
```

### Labeling Controls:
- **Draw Box**: Click and drag to draw a bounding box
- **Adjust Box**: Select a box and adjust its size by dragging the edges or corners
- **Delete**: Select a box and press Delete or use the Delete Selected button
- **Class Selection**: Select the piece type from the dropdown menu
- **Navigation**: Use Next/Previous buttons to navigate between images
- **Save**: Click Save or Save & Next to save annotations

### 3. Training

Split your dataset and train the YOLO11n model:

```bash
# Split dataset
python chess_board_detection/piece_detection/train_yolo_model.py --model "yolo11n.pt" --input_dir "chess_board_detection/piece_detection/dataset/images" --output_dir "chess_board_detection/piece_detection/dataset_split" --epochs 100 --batch 4 --device cpu
```

### Training Parameters:
- `--model`: Path to the pre-trained model (default: "yolo11n.pt")
- `--input_dir`: Directory containing labeled images
- `--output_dir`: Directory to save split dataset
- `--epochs`: Number of training epochs (default: 100)
- `--batch`: Batch size (default: 4)
- `--img-size`: Image size for training (default: 416)
- `--device`: Device to train on ('cpu' or 'cuda')
- `--workers`: Number of worker threads (default: 1)
- `--patience`: Early stopping patience (default: 20)
- `--model_dir`: Directory to save trained model

### 4. Inference

Run inference on new images:

```bash
# Using the detection script
python chess_board_detection/piece_detection/detect_pieces.py --yolo_model "path/to/trained/model.pt" --input "path/to/image.jpg" --output_dir "chess_board_detection/outputs/piece_detection"

# Using the visualization script with improved formatting
python chess_board_detection/piece_detection/visualize_detections.py --model "path/to/trained/model.pt" --input "path/to/image.jpg" --output_dir "chess_board_detection/outputs/piece_detection" --font_size 0.4 --line_width 1

# Or use the convenience batch file
chess_board_detection/run_yolo_detection.bat "path/to/image.jpg"
```

## Model Architecture

We use YOLO11n for chess piece detection with the following classes:
- w_pawn (white pawn)
- w_knight (white knight)
- w_bishop (white bishop)
- w_rook (white rook)
- w_queen (white queen)
- w_king (white king)
- b_pawn (black pawn)
- b_knight (black knight)
- b_bishop (black bishop)
- b_rook (black rook)
- b_queen (black queen)
- b_king (black king)

This compact naming convention reduces visual clutter in detection visualizations while maintaining clarity.

## Model Performance

The YOLO11n model is a lightweight model (5.35 MB) that provides a good balance between accuracy and inference speed:

- **Size**: 5.35 MB
- **Parameters**: 2.6 million
- **Inference Time**:
  - CPU: 20-40ms per image (mid-range CPU)
  - Mobile: 70-100ms per image (Snapdragon 680), 180-250ms (Helio P35)
- **Accuracy**: >90% for chess piece detection in good lighting conditions

## Tips for Best Results

1. **Labeling**: Label pieces tightly around their shape, not the entire square
2. **Training Data**: Include a variety of lighting conditions and board angles
3. **Inference**: Use a confidence threshold of 0.25-0.4 for best results
4. **Resolution**: Use 416×416 resolution for a good balance of accuracy and speed
5. **Post-processing**: Apply chess-specific validation rules to improve accuracy
6. **Visualization**: Use smaller font sizes (0.4-0.5) and compact labels for cleaner detection displays

## Deployment

The trained model can be exported to ONNX format for deployment on various platforms:

```python
# Export to ONNX (done automatically during training)
from ultralytics import YOLO
model = YOLO("path/to/trained/model.pt")
model.export(format="onnx", dynamic=True, simplify=True)
```

For mobile deployment, consider using:
- **Android**: ONNX Runtime or TFLite
- **iOS**: CoreML or ONNX Runtime

## Integration

This module integrates with the chess board detection pipeline to provide a complete chess position recognition system. The detected pieces are mapped to chess board coordinates and converted to FEN notation, which can be used with chess engines for analysis.
