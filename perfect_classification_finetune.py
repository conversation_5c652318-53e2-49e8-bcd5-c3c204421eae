"""
Fine-tune a YOLOv11n model for chess piece detection with a focus on achieving perfect classification accuracy.
This script uses an extremely high classification loss weight to eliminate all classification errors.
"""

import os
import sys
import argparse
import yaml
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from ultralytics import YOLO
from tqdm import tqdm

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def finetune_perfect_classification(
    model_path,
    data_yaml,
    output_dir=None,
    epochs=10,
    batch_size=16,
    img_size=416,
    device='0',
    workers=4,
    patience=10,
    save_period=2,
    conf_threshold=0.001,
    iou_threshold=0.7,
    cls_loss_weight=15.0,  # Extremely high weight for classification loss
    box_loss_weight=0.5,   # Minimal weight for box loss
    dfl_loss_weight=0.5,   # Minimal weight for DFL loss
    seed=42
):
    """
    Fine-tune a YOLO model with extreme focus on classification accuracy to achieve perfect classification.
    
    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        output_dir: Directory to save results
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or GPU device id)
        workers: Number of worker threads
        patience: Stop training if no improvement for this many epochs
        save_period: Save checkpoints every N epochs
        conf_threshold: Confidence threshold for training
        iou_threshold: IoU threshold for NMS
        cls_loss_weight: Weight for classification loss (very high to focus on classification)
        box_loss_weight: Weight for box loss (minimal)
        dfl_loss_weight: Weight for DFL loss (minimal)
        seed: Random seed for reproducibility
    """
    # Set random seed
    set_seed(seed)

    # Print system information
    print_system_info()
    print(f"Training on: {device}")

    if torch.cuda.is_available():
        device_info = torch.cuda.get_device_properties(0)
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {device_info.total_memory / 1e9:.2f} GB")

    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f'perfect_cls_{timestamp}'

    # Create output directory
    if output_dir is None:
        output_dir = f"runs/perfect_cls"

    run_dir = os.path.join(output_dir, run_name)
    os.makedirs(run_dir, exist_ok=True)

    # Save training configuration
    config = {
        "model_path": model_path,
        "data_yaml": data_yaml,
        "epochs": epochs,
        "batch_size": batch_size,
        "img_size": img_size,
        "device": device,
        "workers": workers,
        "patience": patience,
        "save_period": save_period,
        "conf_threshold": conf_threshold,
        "iou_threshold": iou_threshold,
        "cls_loss_weight": cls_loss_weight,
        "box_loss_weight": box_loss_weight,
        "dfl_loss_weight": dfl_loss_weight,
        "seed": seed,
        "timestamp": timestamp
    }

    with open(os.path.join(run_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)

    # Load model
    model = YOLO(model_path)

    # Train with extreme focus on classification loss
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        imgsz=img_size,
        batch=batch_size,
        patience=patience,
        device=device,
        workers=workers,
        project=output_dir,
        name=run_name,
        exist_ok=True,
        pretrained=False,  # We're using a pre-trained model
        verbose=True,
        seed=seed,
        cache=True,
        close_mosaic=0,  # Disable mosaic completely for fine-tuning
        amp=True,  # Enable mixed precision
        # No augmentation for final fine-tuning
        augment=False,
        mosaic=0.0,  # Disable mosaic
        mixup=0.0,  # Disable mixup
        degrees=0.0,  # Disable rotation
        translate=0.0,  # No translation
        scale=0.0,  # No scaling
        shear=0.0,  # No shear
        fliplr=0.0,  # No flipping
        perspective=0.0,  # No perspective
        # Learning rate settings - extremely low for final fine-tuning
        lr0=0.0001,  # Very low initial learning rate for fine-tuning
        lrf=0.00001,  # Very low final learning rate
        # Save checkpoints
        save_period=save_period,
        # Loss weights - extreme focus on classification
        box=box_loss_weight,
        cls=cls_loss_weight,  # Extremely high class loss gain
        dfl=dfl_loss_weight,
        # Validation settings
        val=True,
        # NMS settings
        conf=conf_threshold,
        iou=iou_threshold
    )

    # Get the best model
    best_model_path = os.path.join(run_dir, 'weights', 'best.pt')
    final_model = YOLO(best_model_path)

    # Validate the final model
    print("\nValidating final model...")
    metrics = final_model.val(data=data_yaml, conf=0.7, iou=0.7)

    # Get validation results
    val_results = metrics.box

    # Extract metrics
    p = val_results.p  # Precision for each class
    r = val_results.r  # Recall for each class
    map50 = val_results.map50()  # mAP50
    map = val_results.map()  # mAP50-95

    # Calculate overall precision and recall
    precision = sum(p) / len(p) if p else 0
    recall = sum(r) / len(r) if r else 0

    # Print final metrics
    print("\nFinal Model Performance:")
    print(f"mAP50: {map50:.4f}")
    print(f"mAP50-95: {map:.4f}")
    print(f"Average Precision: {precision:.4f}")
    print(f"Average Recall: {recall:.4f}")

    # Check if we achieved perfect classification
    perfect_precision = all(x >= 0.99 for x in p)
    if perfect_precision:
        print("\n🎉 Perfect classification precision achieved!")
    else:
        print("\nClassification precision still needs improvement.")

    # Save metrics to file
    metrics_dict = {
        "map50": float(map50),
        "map": float(map),
        "precision": float(precision),
        "recall": float(recall),
        "perfect_precision": perfect_precision,
        "training_completed": True
    }

    # Save metrics to the appropriate directory
    metrics_path = os.path.join(run_dir, 'final_metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics_dict, f, indent=2)

    print(f"Training complete. Final model saved to {best_model_path}")
    return best_model_path, metrics_dict

def main():
    parser = argparse.ArgumentParser(description="Fine-tune chess piece detection model for perfect classification")
    parser.add_argument("--model", type=str, default="runs/cls_focused/cls_focused_20250522_133104/weights/best.pt", help="Path to pre-trained model")
    parser.add_argument("--data_yaml", type=str, default="chess_board_detection/piece_detection/targeted_dataset/dataset.yaml", help="Path to dataset YAML file")
    parser.add_argument("--output_dir", type=str, default=None, help="Directory to save results")
    parser.add_argument("--epochs", type=int, default=10, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16, help="Batch size")
    parser.add_argument("--img-size", type=int, default=416, help="Image size for training")
    parser.add_argument("--device", type=str, default="0", help="Device to train on ('cpu' or GPU device id)")
    parser.add_argument("--workers", type=int, default=4, help="Number of worker threads")
    parser.add_argument("--patience", type=int, default=10, help="Stop if no improvement for this many epochs")
    parser.add_argument("--save_period", type=int, default=2, help="Save checkpoints every N epochs")
    parser.add_argument("--cls_weight", type=float, default=15.0, help="Weight for classification loss")
    parser.add_argument("--box_weight", type=float, default=0.5, help="Weight for box loss")
    parser.add_argument("--dfl_weight", type=float, default=0.5, help="Weight for DFL loss")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    
    args = parser.parse_args()
    
    # Fine-tune model with focus on classification
    finetune_perfect_classification(
        args.model,
        args.data_yaml,
        args.output_dir,
        args.epochs,
        args.batch,
        args.img_size,
        args.device,
        args.workers,
        args.patience,
        args.save_period,
        cls_loss_weight=args.cls_weight,
        box_loss_weight=args.box_weight,
        dfl_loss_weight=args.dfl_weight,
        seed=args.seed
    )

if __name__ == "__main__":
    main()
