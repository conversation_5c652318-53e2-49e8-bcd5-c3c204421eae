package com.chessvision.app.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat
import androidx.compose.ui.graphics.Brush
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf

// Modern Chess-themed Material 3 color palette
private val ChessLightColors = lightColorScheme(
    primary = Color(0xFF769656), // Chess.com green
    onPrimary = Color.White,
    primaryContainer = Color(0xFFE8F5E8), // Light green container
    onPrimaryContainer = Color(0xFF1B5E20),

    secondary = Color(0xFF8B4513), // Chess brown
    onSecondary = Color.White,
    secondaryContainer = Color(0xFFF5DEB3), // Light wood
    onSecondaryContainer = Color(0xFF2D1810),

    tertiary = Color(0xFF6A4C93), // Royal purple
    onTertiary = Color.White,
    tertiaryContainer = Color(0xFFE1BEE7),
    onTertiaryContainer = Color(0xFF21005D),

    error = Color(0xFFBA1A1A),
    onError = Color.White,
    errorContainer = Color(0xFFFFDAD6),
    onErrorContainer = Color(0xFF410002),

    background = Color(0xFFFFFBFF),
    onBackground = Color(0xFF1A1C18),
    surface = Color(0xFFFFFBFF),
    onSurface = Color(0xFF1A1C18),

    surfaceVariant = Color(0xFFE0E3DC),
    onSurfaceVariant = Color(0xFF43483E),
    outline = Color(0xFF74796D),
    outlineVariant = Color(0xFFC4C7C0),

    scrim = Color(0xFF000000),
    inverseSurface = Color(0xFF2F312D),
    inverseOnSurface = Color(0xFFF1F1EB),
    inversePrimary = Color(0xFF9CCC7C),
)

private val ChessDarkColors = darkColorScheme(
    primary = Color(0xFF9CCC7C), // Light chess green
    onPrimary = Color(0xFF003A00),
    primaryContainer = Color(0xFF1B5E20),
    onPrimaryContainer = Color(0xFFB8E6B8),

    secondary = Color(0xFFFFB59D), // Light wood tone
    onSecondary = Color(0xFF4A2C17),
    secondaryContainer = Color(0xFF663F2A),
    onSecondaryContainer = Color(0xFFFFDBCF),

    tertiary = Color(0xFFC4A2E8), // Light purple
    onTertiary = Color(0xFF372274),
    tertiaryContainer = Color(0xFF4F378B),
    onTertiaryContainer = Color(0xFFE1BEE7),

    error = Color(0xFFFFB4AB),
    onError = Color(0xFF690005),
    errorContainer = Color(0xFF93000A),
    onErrorContainer = Color(0xFFFFDAD6),

    background = Color(0xFF312e2b), // Chess.com dark background
    onBackground = Color(0xFFE3E3DD),
    surface = Color(0xFF312e2b),
    onSurface = Color(0xFFE3E3DD),

    surfaceVariant = Color(0xFF43483E),
    onSurfaceVariant = Color(0xFFC4C7C0),
    outline = Color(0xFF8E9286),
    outlineVariant = Color(0xFF43483E),

    scrim = Color(0xFF000000),
    inverseSurface = Color(0xFFE3E3DD),
    inverseOnSurface = Color(0xFF2F312D),
    inversePrimary = Color(0xFF769656),
)

// Material 3 Expressive surface treatments and gradients
data class ChessExpressiveSurfaces(
    val primaryGradient: Brush = Brush.horizontalGradient(
        colors = listOf(
            Color(0xFF769656),
            Color(0xFF4a5c2a)
        )
    ),
    val cardGradient: Brush = Brush.verticalGradient(
        colors = listOf(
            Color(0xFF262421).copy(alpha = 0.95f),
            Color(0xFF1a1a1a).copy(alpha = 0.98f)
        )
    ),
    val heroGradient: Brush = Brush.radialGradient(
        colors = listOf(
            Color(0xFF769656).copy(alpha = 0.2f),
            Color(0xFF4a5c2a).copy(alpha = 0.1f),
            Color.Transparent
        )
    ),
    val glassEffect: Brush = Brush.verticalGradient(
        colors = listOf(
            Color.White.copy(alpha = 0.15f),
            Color.White.copy(alpha = 0.05f)
        )
    ),
    val shimmerGradient: Brush = Brush.horizontalGradient(
        colors = listOf(
            Color.Transparent,
            Color.White.copy(alpha = 0.1f),
            Color.Transparent
        )
    )
)

// Composition local for expressive surfaces
val LocalChessExpressiveSurfaces = staticCompositionLocalOf { ChessExpressiveSurfaces() }

@Composable
fun ChessVisionAppTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    // Dynamic color enabled for Material 3 Expressive features
    dynamicColor: Boolean = true,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }

        darkTheme -> ChessDarkColors
        else -> ChessLightColors
    }

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.primary.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = darkTheme
        }
    }

    // Provide expressive surfaces through composition local
    CompositionLocalProvider(
        LocalChessExpressiveSurfaces provides ChessExpressiveSurfaces()
    ) {
        MaterialTheme(
            colorScheme = colorScheme,
            typography = Typography,
            content = content
        )
    }
}
