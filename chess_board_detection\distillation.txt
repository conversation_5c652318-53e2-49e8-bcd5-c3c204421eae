PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/distill_segmentation_model.py --teacher_model "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth" --model_type tiny --epochs 10 --data_dir "chess_board_detection/data/augmented/v5.2/augmented_20250518_153326"
Loading teacher model from chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth
Creating tiny student model
Teacher model parameters: 17,874,848
Student model parameters: 107,073
Size reduction: 166.94x
Loading data from chess_board_detection/data/augmented/v5.2/augmented_20250518_153326
Loaded 85 augmented samples from chess_board_detection/data/augmented/v5.2/augmented_20250518_153326
Created dataloaders with 68 training samples and 17 validation samples
Starting training for 10 epochs
Epoch 1/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:45<00:00,  5.05s/it]
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  4.12it/s]
Train Loss: 4899.5178, Hard Loss: 0.6504, Soft Loss: 2449.5963
Val IoU: 0.4263, Val Dice: 0.5871
Saved new best IoU model: 0.4263
Saved new best Dice model: 0.5871
Epoch 2/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:43<00:00,  4.88s/it]
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  7.62it/s]
Train Loss: 4082.0720, Hard Loss: 0.5915, Soft Loss: 2040.8881
Val IoU: 0.0064, Val Dice: 0.0125
Epoch 3/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:43<00:00,  4.84s/it] 
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  7.97it/s] 
Train Loss: 2554.8824, Hard Loss: 0.5465, Soft Loss: 1277.3046
Val IoU: 0.8101, Val Dice: 0.8597
Saved new best IoU model: 0.8101
Saved new best Dice model: 0.8597
Epoch 4/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:42<00:00,  4.74s/it] 
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  8.49it/s] 
Train Loss: 1883.4264, Hard Loss: 0.5186, Soft Loss: 941.5836
Val IoU: 0.8411, Val Dice: 0.8930
Saved new best IoU model: 0.8411
Saved new best Dice model: 0.8930
Epoch 5/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:44<00:00,  4.92s/it] 
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  8.20it/s] 
Train Loss: 773.3845, Hard Loss: 0.4994, Soft Loss: 386.5674
Val IoU: 0.8458, Val Dice: 0.9067
Saved new best IoU model: 0.8458
Saved new best Dice model: 0.9067
Epoch 6/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:42<00:00,  4.76s/it] 
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  8.29it/s] 
Train Loss: 391.7627, Hard Loss: 0.4942, Soft Loss: 195.7578
Val IoU: 0.8429, Val Dice: 0.9048
Epoch 7/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:43<00:00,  4.82s/it] 
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  8.10it/s] 
Train Loss: -25.0575, Hard Loss: 0.4752, Soft Loss: -12.6476
Val IoU: 0.8440, Val Dice: 0.9050
Epoch 10/10
Training: 100%|███████████████████████████████████████████| 9/9 [00:44<00:00,  4.95s/it] 
Validation: 100%|█████████████████████████████████████████| 3/3 [00:00<00:00,  6.77it/s] 
Train Loss: -842.8451, Hard Loss: 0.4745, Soft Loss: -421.5412
Val IoU: 0.8566, Val Dice: 0.9153
Saved new best IoU model: 0.8566
Saved new best Dice model: 0.9153
Training completed. Models saved to chess_board_detection/models/segmentation_only\tiny_20250519_091307
Best IoU: 0.8566, Best Dice: 0.9153
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 




