"""
Metrics for evaluating chess board detection models.
"""

import torch
import numpy as np


def calculate_corner_confidence(heatmaps):
    """
    Calculate confidence metrics for corner heatmaps.

    Args:
        heatmaps: Corner heatmaps (B, 4, H, W)

    Returns:
        metrics: Dictionary of confidence metrics
    """
    batch_size = heatmaps.size(0)
    metrics = {
        'avg_peak_value': 0.0,
        'avg_peak_to_mean_ratio': 0.0,
        'avg_peak_to_second_ratio': 0.0,
        'detection_rate': 0.0,
        # New v5.2 detailed metrics
        'per_corner_p2s_ratio': [0.0, 0.0, 0.0, 0.0],  # Per-corner type P2S ratio
        'p2s_ratio_min': 0.0,                          # Minimum P2S ratio
        'p2s_ratio_max': 0.0,                          # Maximum P2S ratio
        'p2s_ratio_std': 0.0,                          # Standard deviation of P2S ratios
        'secondary_peak_distance': 0.0,                # Average distance between primary and secondary peaks
        'secondary_peak_relative_position': [0.0, 0.0], # Average x,y offset of secondary peak from primary
        'primary_peak_sharpness': 0.0,                 # Gradient magnitude around primary peak
        'primary_peak_isolation': 0.0                  # Distance to nearest significant activation
    }

    # Lists to collect data for calculating statistics
    all_p2s_ratios = []
    all_secondary_distances = []
    all_secondary_positions = []
    all_peak_sharpness = []
    all_peak_isolation = []
    per_corner_p2s_ratios = [[] for _ in range(4)]

    # Calculate metrics for each batch
    for b in range(batch_size):
        batch_peak_values = []
        batch_peak_to_mean_ratios = []
        batch_peak_to_second_ratios = []
        batch_detection_rate = 0.0

        # Calculate metrics for each corner
        for c in range(4):
            hm = heatmaps[b, c].detach().cpu()
            h, w = hm.shape

            # Find peak value and location
            peak_value, peak_idx = torch.max(hm.view(-1), dim=0)
            peak_value = peak_value.item()
            peak_y, peak_x = peak_idx.item() // w, peak_idx.item() % w
            batch_peak_values.append(peak_value)

            # Calculate peak-to-mean ratio
            mean_value = torch.mean(hm).item()
            if mean_value > 0:
                peak_to_mean_ratio = peak_value / mean_value
            else:
                peak_to_mean_ratio = 0.0
            batch_peak_to_mean_ratios.append(peak_to_mean_ratio)

            # Calculate peak-to-second ratio and secondary peak metrics
            hm_flat = hm.flatten()

            # Create a mask to exclude the main peak and its immediate surroundings
            mask = torch.ones_like(hm_flat)
            for y_offset in range(-3, 4):
                for x_offset in range(-3, 4):
                    y_pos = peak_y + y_offset
                    x_pos = peak_x + x_offset
                    if 0 <= y_pos < h and 0 <= x_pos < w:
                        mask[y_pos * w + x_pos] = 0

            # Find second peak
            masked_hm = hm_flat * mask
            second_max_val, second_max_idx = torch.max(masked_hm, dim=0)
            second_max_val = second_max_val.item()
            second_y, second_x = second_max_idx.item() // w, second_max_idx.item() % w

            # Calculate peak-to-second ratio
            if second_max_val > 0:
                peak_to_second_ratio = peak_value / second_max_val
            else:
                peak_to_second_ratio = 1.0

            batch_peak_to_second_ratios.append(peak_to_second_ratio)
            all_p2s_ratios.append(peak_to_second_ratio)
            per_corner_p2s_ratios[c].append(peak_to_second_ratio)

            # Calculate secondary peak distance and relative position
            if second_max_val > 0.1:  # Only consider significant secondary peaks
                distance = torch.sqrt(torch.tensor(
                    (peak_y - second_y)**2 + (peak_x - second_x)**2, dtype=torch.float32))
                all_secondary_distances.append(distance.item())
                all_secondary_positions.append([second_x - peak_x, second_y - peak_y])

            # Calculate primary peak sharpness (gradient magnitude)
            if 0 < peak_y < h-1 and 0 < peak_x < w-1:
                dx = (hm[peak_y, peak_x+1] - hm[peak_y, peak_x-1]) / 2
                dy = (hm[peak_y+1, peak_x] - hm[peak_y-1, peak_x]) / 2
                gradient_magnitude = torch.sqrt(dx**2 + dy**2).item()
                all_peak_sharpness.append(gradient_magnitude)

            # Calculate primary peak isolation (distance to nearest significant activation)
            # Create a binary mask of significant activations (>20% of peak value)
            significant_mask = (hm > 0.2 * peak_value).float()
            # Set the peak and its immediate surroundings to 0
            for y_offset in range(-2, 3):
                for x_offset in range(-2, 3):
                    y_pos = peak_y + y_offset
                    x_pos = peak_x + x_offset
                    if 0 <= y_pos < h and 0 <= x_pos < w:
                        significant_mask[y_pos, x_pos] = 0

            # If there are other significant activations, calculate minimum distance
            if torch.sum(significant_mask) > 0:
                # Get coordinates of significant activations
                sig_coords = torch.nonzero(significant_mask)
                if len(sig_coords) > 0:
                    # Calculate distances to peak
                    distances = torch.sqrt(
                        (sig_coords[:, 0] - peak_y).float()**2 +
                        (sig_coords[:, 1] - peak_x).float()**2)
                    min_distance = torch.min(distances).item()
                    all_peak_isolation.append(min_distance)

            # Calculate detection rate
            if peak_value >= 0.5:
                batch_detection_rate += 1.0

        # Average metrics for this batch
        metrics['avg_peak_value'] += sum(batch_peak_values) / 4
        metrics['avg_peak_to_mean_ratio'] += sum(batch_peak_to_mean_ratios) / 4
        metrics['avg_peak_to_second_ratio'] += sum(batch_peak_to_second_ratios) / 4
        metrics['detection_rate'] += batch_detection_rate / 4

    # Average metrics across batches
    for key in ['avg_peak_value', 'avg_peak_to_mean_ratio', 'avg_peak_to_second_ratio', 'detection_rate']:
        metrics[key] /= batch_size

    # Calculate statistics for detailed metrics
    if all_p2s_ratios:
        metrics['p2s_ratio_min'] = min(all_p2s_ratios)
        metrics['p2s_ratio_max'] = max(all_p2s_ratios)
        metrics['p2s_ratio_std'] = torch.tensor(all_p2s_ratios).std().item() if len(all_p2s_ratios) > 1 else 0.0

    # Calculate per-corner P2S ratios
    for c in range(4):
        if per_corner_p2s_ratios[c]:
            metrics['per_corner_p2s_ratio'][c] = sum(per_corner_p2s_ratios[c]) / len(per_corner_p2s_ratios[c])

    # Calculate secondary peak metrics
    if all_secondary_distances:
        metrics['secondary_peak_distance'] = sum(all_secondary_distances) / len(all_secondary_distances)

    if all_secondary_positions:
        avg_x = sum(pos[0] for pos in all_secondary_positions) / len(all_secondary_positions)
        avg_y = sum(pos[1] for pos in all_secondary_positions) / len(all_secondary_positions)
        metrics['secondary_peak_relative_position'] = [avg_x, avg_y]

    # Calculate primary peak metrics
    if all_peak_sharpness:
        metrics['primary_peak_sharpness'] = sum(all_peak_sharpness) / len(all_peak_sharpness)

    if all_peak_isolation:
        metrics['primary_peak_isolation'] = sum(all_peak_isolation) / len(all_peak_isolation)

    return metrics


def calculate_corner_confidence_numpy(heatmaps):
    """
    Calculate confidence metrics for corner heatmaps (numpy version).

    Args:
        heatmaps: Corner heatmaps (4, H, W)

    Returns:
        metrics: Dictionary of confidence metrics
    """
    metrics = {}

    # Calculate peak values
    peak_values = []
    peak_to_mean_ratios = []
    peak_to_second_ratios = []

    for i in range(4):
        hm = heatmaps[i]

        # Find peak value
        peak_value = np.max(hm)
        peak_values.append(peak_value)

        # Calculate peak-to-mean ratio
        mean_value = np.mean(hm)
        if mean_value > 0:
            peak_to_mean_ratio = peak_value / mean_value
        else:
            peak_to_mean_ratio = 0
        peak_to_mean_ratios.append(peak_to_mean_ratio)

        # Calculate peak-to-second ratio
        hm_flat = hm.flatten()
        sorted_indices = np.argsort(hm_flat)[::-1]
        if len(sorted_indices) > 1:
            second_value = hm_flat[sorted_indices[1]]
            if second_value > 0:
                peak_to_second_ratio = peak_value / second_value
            else:
                peak_to_second_ratio = 1.0
        else:
            peak_to_second_ratio = 1.0
        peak_to_second_ratios.append(peak_to_second_ratio)

    # Calculate detection rate (proportion of corners with peak value above threshold)
    detection_threshold = 0.5
    detection_rate = sum(1 for v in peak_values if v >= detection_threshold) / 4

    # Store metrics
    metrics['avg_peak_value'] = np.mean(peak_values)
    metrics['avg_peak_to_mean_ratio'] = np.mean(peak_to_mean_ratios)
    metrics['avg_peak_to_second_ratio'] = np.mean(peak_to_second_ratios)
    metrics['detection_rate'] = detection_rate

    return metrics


def calculate_iou(pred_mask, true_mask):
    """
    Calculate IoU (Intersection over Union) for segmentation masks.

    Args:
        pred_mask: Predicted segmentation mask (H, W)
        true_mask: Ground truth segmentation mask (H, W)

    Returns:
        iou: IoU score
    """
    # Convert to binary masks
    pred_mask = (pred_mask > 0.5).astype(np.float32)
    true_mask = (true_mask > 0.5).astype(np.float32)

    # Calculate intersection and union
    intersection = np.sum(pred_mask * true_mask)
    union = np.sum(pred_mask) + np.sum(true_mask) - intersection

    # Calculate IoU
    if union > 0:
        iou = intersection / union
    else:
        iou = 0.0

    return iou


def calculate_corner_accuracy(pred_corners, true_corners, threshold=10.0):
    """
    Calculate corner detection accuracy.

    Args:
        pred_corners: Predicted corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        true_corners: Ground truth corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        threshold: Distance threshold for a corner to be considered correct

    Returns:
        accuracy: Proportion of correctly detected corners
    """
    # Check if corners are valid
    if pred_corners is None or true_corners is None:
        return 0.0

    # Calculate distances between predicted and ground truth corners
    correct_corners = 0
    for i in range(4):
        if pred_corners[i] is None or true_corners[i] is None:
            continue

        # Calculate Euclidean distance
        pred_x, pred_y = pred_corners[i]
        true_x, true_y = true_corners[i]
        distance = np.sqrt((pred_x - true_x)**2 + (pred_y - true_y)**2)

        # Check if distance is below threshold
        if distance < threshold:
            correct_corners += 1

    # Calculate accuracy
    accuracy = correct_corners / 4

    return accuracy
