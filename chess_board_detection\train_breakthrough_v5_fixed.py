"""
Breakthrough U-Net V5 Training (Fixed): Target 0.95+ Dice to exceed V4's 0.8721 performance.
Simplified and stable training approach.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.breakthrough_unet_v5 import get_breakthrough_v5_model
from chess_board_detection.dataset.augmented_segmentation_dataset import create_augmented_dataloaders

class SimpleBreakthroughLoss(nn.Module):
    """Simplified loss function for stable V5 training."""
    
    def __init__(self, alpha=0.25, gamma=2.0, dice_weight=2.0, bce_weight=1.0, smooth=1e-6):
        super(SimpleBreakthroughLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.dice_weight = dice_weight
        self.bce_weight = bce_weight
        self.smooth = smooth
        
    def focal_loss(self, inputs, targets):
        """Focal loss for hard example mining."""
        bce_loss = nn.functional.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        return focal_loss.mean()
    
    def dice_loss(self, inputs, targets):
        """Dice loss for segmentation."""
        inputs = torch.sigmoid(inputs)
        inputs = inputs.view(-1)
        targets = targets.view(-1)
        
        intersection = (inputs * targets).sum()
        dice = (2. * intersection + self.smooth) / (inputs.sum() + targets.sum() + self.smooth)
        return 1 - dice
    
    def forward(self, inputs, targets):
        focal = self.focal_loss(inputs, targets)
        dice = self.dice_loss(inputs, targets)
        
        total_loss = self.bce_weight * focal + self.dice_weight * dice
        
        return total_loss, {
            'focal': focal.item(),
            'dice': dice.item(),
            'total': total_loss.item()
        }

def calculate_metrics(predictions, targets, threshold=0.5):
    """Calculate comprehensive metrics."""
    with torch.no_grad():
        predictions = torch.sigmoid(predictions)
        pred_binary = (predictions > threshold).float()
        targets_binary = (targets > threshold).float()
        
        # Flatten
        pred_flat = pred_binary.view(-1)
        target_flat = targets_binary.view(-1)
        
        # Basic metrics
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum() - intersection
        
        iou = (intersection + 1e-6) / (union + 1e-6)
        dice = (2 * intersection + 1e-6) / (pred_flat.sum() + target_flat.sum() + 1e-6)
        
        # Advanced metrics
        tp = intersection
        fp = pred_flat.sum() - intersection
        fn = target_flat.sum() - intersection
        
        precision = (tp + 1e-6) / (tp + fp + 1e-6)
        recall = (tp + 1e-6) / (tp + fn + 1e-6)
        f1 = 2 * (precision * recall) / (precision + recall + 1e-6)
        
        return {
            'iou': torch.clamp(iou, 0, 1).item(),
            'dice': torch.clamp(dice, 0, 1).item(),
            'precision': torch.clamp(precision, 0, 1).item(),
            'recall': torch.clamp(recall, 0, 1).item(),
            'f1': torch.clamp(f1, 0, 1).item()
        }

def train_epoch(model, train_loader, criterion, optimizer, scaler, device, accumulation_steps=2):
    """Training epoch for V5."""
    model.train()
    total_loss = 0
    total_metrics = {}
    num_batches = len(train_loader)
    
    optimizer.zero_grad()
    
    pbar = tqdm(train_loader, desc="🚀 V5 Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device, non_blocking=True)
        masks = masks.to(device, non_blocking=True)
        
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)
        
        with autocast():
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            loss = loss / accumulation_steps
        
        # Backward pass
        scaler.scale(loss).backward()
        
        # Gradient accumulation
        if (batch_idx + 1) % accumulation_steps == 0:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()
        
        # Calculate metrics
        batch_metrics = calculate_metrics(outputs, masks)
        
        # Accumulate metrics
        total_loss += loss.item() * accumulation_steps
        for key, value in {**loss_metrics, **batch_metrics}.items():
            if key not in total_metrics:
                total_metrics[key] = 0
            total_metrics[key] += value
        
        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{loss.item() * accumulation_steps:.4f}',
            'Dice': f'{batch_metrics["dice"]:.4f}',
            'IoU': f'{batch_metrics["iou"]:.4f}'
        })
    
    # Average metrics
    avg_loss = total_loss / num_batches
    avg_metrics = {key: value / num_batches for key, value in total_metrics.items()}
    
    return avg_loss, avg_metrics

def validate_epoch(model, val_loader, criterion, device):
    """Validation epoch for V5."""
    model.eval()
    total_loss = 0
    total_metrics = {}
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="🎯 V5 Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            
            if masks.dim() == 3:
                masks = masks.unsqueeze(1)
            
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            
            batch_metrics = calculate_metrics(outputs, masks)
            
            # Accumulate
            total_loss += loss.item()
            for key, value in {**loss_metrics, **batch_metrics}.items():
                if key not in total_metrics:
                    total_metrics[key] = 0
                total_metrics[key] += value
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{batch_metrics["dice"]:.4f}',
                'IoU': f'{batch_metrics["iou"]:.4f}'
            })
    
    avg_loss = total_loss / num_batches
    avg_metrics = {key: value / num_batches for key, value in total_metrics.items()}
    
    return avg_loss, avg_metrics

def train_v5_breakthrough():
    """Train V5 model to achieve 0.95+ Dice."""
    
    # V5 Configuration
    config = {
        'dataset_dir': r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326",
        'save_dir': "chess_board_detection/breakthrough_v5_fixed_results",
        'epochs': 100,
        'batch_size': 4,
        'learning_rate': 8e-4,  # Stable learning rate
        'base_channels': 32,  # V5-32
        'accumulation_steps': 2,
        'num_workers': 0,
        'v4_baseline': 0.8721,
        'target_dice': 0.95,
    }
    
    print("🚀 BREAKTHROUGH U-NET V5 TRAINING (FIXED)")
    print("=" * 60)
    print("🎯 TARGET: Exceed V4's 0.8721 Dice and achieve 0.95+ performance!")
    print(f"🏆 V4 Baseline: {config['v4_baseline']:.4f} Dice")
    print(f"🚀 V5 Target: {config['target_dice']:.4f} Dice")
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create save directory
    save_dir = Path(config['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Create dataloaders
    print("Creating dataloaders...")
    train_loader, val_loader = create_augmented_dataloaders(
        config['dataset_dir'],
        batch_size=config['batch_size'],
        train_split=0.8,
        num_workers=config['num_workers']
    )
    
    # Create V5 model
    print("Creating Breakthrough U-Net V5...")
    model = get_breakthrough_v5_model(base_channels=config['base_channels'])
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    v4_params = 3296335
    efficiency = total_params / v4_params
    
    print(f"V5 Model parameters: {total_params:,}")
    print(f"Efficiency vs V4: {efficiency:.2f}x")
    
    # Loss function
    criterion = SimpleBreakthroughLoss(alpha=0.25, gamma=2.0, dice_weight=2.0, bce_weight=1.0)
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=1e-4,
        betas=(0.9, 0.999)
    )
    
    # Scheduler
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'],
        epochs=config['epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )
    
    # Mixed precision
    scaler = GradScaler()
    
    # Training variables
    best_val_dice = 0
    v4_exceeded = False
    breakthrough_achieved = False
    patience_counter = 0
    patience = 25
    
    print(f"🚀 Starting V5 training for {config['epochs']} epochs...")
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        print(f"\n🔥 Epoch {epoch+1}/{config['epochs']}")
        
        # Train
        train_loss, train_metrics = train_epoch(
            model, train_loader, criterion, optimizer, scaler, device, config['accumulation_steps']
        )
        
        # Validate
        val_loss, val_metrics = validate_epoch(model, val_loader, criterion, device)
        
        # Update scheduler
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # Print results
        val_dice = val_metrics.get('dice', 0)
        val_iou = val_metrics.get('iou', 0)
        
        print(f"Train - Loss: {train_loss:.4f}, Dice: {train_metrics.get('dice', 0):.4f}, IoU: {train_metrics.get('iou', 0):.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}, IoU: {val_iou:.4f}")
        print(f"LR: {current_lr:.6f}")
        
        # Check milestones
        if val_dice > config['v4_baseline'] and not v4_exceeded:
            v4_exceeded = True
            print(f"🎉 V4 EXCEEDED! Val Dice: {val_dice:.4f} > {config['v4_baseline']:.4f}")
        
        if val_dice >= config['target_dice'] and not breakthrough_achieved:
            breakthrough_achieved = True
            print(f"🎉 BREAKTHROUGH ACHIEVED! Val Dice: {val_dice:.4f} >= {config['target_dice']:.4f}")
        
        # Save best model
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            torch.save(model.state_dict(), save_dir / "best_model.pth")
            print(f"✅ New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Early stopping
        if breakthrough_achieved and patience_counter >= patience:
            print(f"Early stopping: Breakthrough achieved")
            break
    
    # Final results
    training_time = time.time() - start_time
    print(f"\n🎉 V5 Training completed in {training_time/3600:.2f} hours")
    print(f"🏆 Best validation Dice: {best_val_dice:.4f}")
    
    if breakthrough_achieved:
        print(f"🎉 BREAKTHROUGH ACHIEVED! V5 reached 0.95+ Dice!")
    elif v4_exceeded:
        print(f"🚀 V4 EXCEEDED! V5 surpasses V4 performance!")
        improvement = ((best_val_dice - config['v4_baseline']) / config['v4_baseline']) * 100
        print(f"🎯 Improvement: {improvement:+.2f}%")
    else:
        print(f"🎯 Strong performance achieved!")
    
    return model, best_val_dice, v4_exceeded, breakthrough_achieved

if __name__ == "__main__":
    try:
        model, best_dice, v4_exceeded, breakthrough = train_v5_breakthrough()
        print(f"\n🏆 Final Result: {best_dice:.4f} Dice")
        
    except Exception as e:
        print(f"❌ V5 training failed: {e}")
        import traceback
        traceback.print_exc()
