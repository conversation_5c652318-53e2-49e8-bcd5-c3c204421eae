"""
Three-Stage Chess Position Analysis:
1. Detect chessboard using segmentation model
2. Detect chess pieces using YOLO model
3. Generate FEN notation from detected pieces

Assumes a1 is at bottom left and h8 is at top right.
"""

import os
import sys
import cv2
import numpy as np
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
import argparse

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the models
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

# Configuration
CONFIG = {
    # Chess piece detection model
    "piece_model": "runs/detect/simple_continue/continue_epoch111/weights/best.pt",

    # Segmentation model - using teacher model for better accuracy
    "segmentation_model": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",

    # Class names
    "class_names": [
        "white_pawn", "white_knight", "white_bishop", "white_rook",
        "white_queen", "white_king", "black_pawn", "black_knight",
        "black_bishop", "black_rook", "black_queen", "black_king"
    ],

    # FEN piece symbols
    "fen_symbols": {
        "white_pawn": "P", "white_knight": "N", "white_bishop": "B", "white_rook": "R",
        "white_queen": "Q", "white_king": "K", "black_pawn": "p", "black_knight": "n",
        "black_bishop": "b", "black_rook": "r", "black_queen": "q", "black_king": "k"
    }
}

def preprocess_image(image_path):
    """
    Load the image at its original size for the segmentation model.
    No resizing is applied at this stage.
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Store original image and its size
    original_image = image.copy()
    original_size = image.shape[:2]  # (height, width)

    # Convert to RGB if needed
    if len(image.shape) == 3 and image.shape[2] == 3:
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    else:
        image_rgb = image

    # Store preprocessing info for later use
    preprocess_info = {
        'original_image': original_image,
        'original_size': original_size
    }

    return image_rgb, preprocess_info

def normalize_for_model(image):
    """
    Normalize image for model input.
    The image is already in RGB format from the preprocess_image function.
    """
    # Convert to float and normalize
    image_float = image.astype(np.float32) / 255.0

    # Transpose to (C, H, W) format
    image_transposed = np.transpose(image_float, (2, 0, 1))

    # Create tensor
    tensor = torch.from_numpy(image_transposed).unsqueeze(0)

    return tensor

def find_corners_from_segmentation(segmentation, threshold=0.5):
    """
    Find corners of the chess board using the segmentation mask.
    Exact copy from distilled_segmentation_corner_detection.py

    Args:
        segmentation: Segmentation mask (H, W)
        threshold: Threshold for binary mask

    Returns:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    """
    # Create binary mask
    binary_mask = (segmentation > threshold).astype(np.uint8)

    # Apply morphological operations to clean up the mask
    kernel = np.ones((5, 5), np.uint8)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)

    # Find contours
    contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        return None

    # Find the largest contour (the chess board)
    largest_contour = max(contours, key=cv2.contourArea)

    # Approximate the contour to get a polygon
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx_polygon = cv2.approxPolyDP(largest_contour, epsilon, True)

    # If we don't get exactly 4 corners, try to find the best 4 corners
    if len(approx_polygon) != 4:
        # If we have more than 4 corners, find the 4 corners that form the largest quadrilateral
        if len(approx_polygon) > 4:
            # Convert to a more convenient format
            points = [point[0] for point in approx_polygon]

            # Find the convex hull
            hull = cv2.convexHull(np.array(points))

            # Approximate the hull to get 4 corners
            epsilon = 0.02 * cv2.arcLength(hull, True)
            approx_polygon = cv2.approxPolyDP(hull, epsilon, True)

            # If we still don't have 4 corners, use the minimum area rectangle
            if len(approx_polygon) != 4:
                rect = cv2.minAreaRect(hull)
                box = cv2.boxPoints(rect)
                approx_polygon = np.int0(box)
        else:
            # If we have fewer than 4 corners, use the minimum area rectangle
            rect = cv2.minAreaRect(largest_contour)
            box = cv2.boxPoints(rect)
            approx_polygon = np.int0(box)

    # Extract corners
    corners = [(point[0][0], point[0][1]) for point in approx_polygon]

    # Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left
    corners = sort_corners(corners)

    return corners

def sort_corners(corners):
    """
    Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left.
    Exact copy from distilled_segmentation_corner_detection.py

    Args:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]

    Returns:
        sorted_corners: Sorted list of corner coordinates
    """
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)

    # Sort corners based on their angle from the center
    def get_angle(point):
        return np.arctan2(point[1] - center_y, point[0] - center_x)

    # Sort corners by angle
    sorted_corners = sorted(corners, key=get_angle)

    # Rearrange to get top-left, top-right, bottom-right, bottom-left
    # This assumes that the first corner after sorting is the top-left
    # We need to rotate the list to get the desired order
    # Find the top-left corner (minimum sum of x and y)
    min_sum_idx = np.argmin([x + y for x, y in sorted_corners])

    # Rotate the list so that the top-left corner is first
    sorted_corners = sorted_corners[min_sum_idx:] + sorted_corners[:min_sum_idx]

    return sorted_corners

def extract_and_normalize_board(image, corners, output_size=(416, 416)):
    """
    Extract and normalize the chess board region with minimal distortion.
    Uses the highest quality interpolation method and preserves the original appearance as much as possible.
    """
    # Sort corners in clockwise order to ensure consistent mapping
    corners = sort_corners(corners)

    # Add a small margin to the corners to ensure pieces aren't cut off
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)

    # Expand corners slightly outward from center (3% expansion)
    expanded_corners = []
    for x, y in corners:
        # Vector from center to corner
        dx = x - center_x
        dy = y - center_y

        # Expand by 3%
        expanded_x = center_x + dx * 1.03
        expanded_y = center_y + dy * 1.03

        expanded_corners.append((expanded_x, expanded_y))

    # Convert expanded corners to numpy array
    corners_np = np.array(expanded_corners, dtype=np.float32)

    # Define the destination points (normalized square)
    # Add a small margin inside the output image to avoid cutting off pieces at the edges
    margin = int(output_size[0] * 0.02)  # 2% margin
    dst_points = np.array([
        [margin, margin],  # Top-left with margin
        [output_size[0] - margin, margin],  # Top-right with margin
        [output_size[0] - margin, output_size[1] - margin],  # Bottom-right with margin
        [margin, output_size[1] - margin]  # Bottom-left with margin
    ], dtype=np.float32)

    # Get perspective transform
    M = cv2.getPerspectiveTransform(corners_np, dst_points)

    # Apply perspective transform with the highest quality interpolation
    normalized = cv2.warpPerspective(
        image,
        M,
        output_size,
        flags=cv2.INTER_LANCZOS4,  # Use Lanczos interpolation for highest quality
        borderMode=cv2.BORDER_REPLICATE  # Replicate border pixels to avoid artifacts
    )

    return normalized

def preprocess_image_for_segmentation(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    Exact copy from distilled_segmentation_corner_detection.py
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """Apply basic image enhancement. Exact copy from distilled script."""
    # Convert to float32 for processing
    image_float = image.astype(np.float32) / 255.0

    # Apply contrast stretching
    p2, p98 = np.percentile(image_float, (2, 98))
    enhanced = np.clip((image_float - p2) / (p98 - p2), 0, 1)

    # Convert back to uint8
    enhanced = (enhanced * 255).astype(np.uint8)

    return enhanced

def normalize_for_model(image):
    """Normalize image for model input. Exact copy from distilled script."""
    from torchvision import transforms
    from PIL import Image

    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)

    return input_tensor

def map_to_original_coordinates(keypoints, preprocess_info):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    Accounts for all preprocessing steps: resize, crop, and final resize.
    """
    mapped_keypoints = []

    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']
    resized_w, resized_h = preprocess_info['resized_size']

    for kp in keypoints:
        if kp is None:
            mapped_keypoints.append(None)
            continue

        x, y = kp

        # Map from target size to crop size
        x_crop = x * crop_size / target_w
        y_crop = y * crop_size / target_h

        # Map from crop to resized image
        x_resized = x_crop + crop_start_x
        y_resized = y_crop + crop_start_y

        # Map from resized to original image
        x_original = x_resized * original_w / resized_w
        y_original = y_resized * original_h / resized_h

        mapped_keypoints.append((int(x_original), int(y_original)))

    return mapped_keypoints

def map_to_original_coordinates_enhanced(keypoints, preprocess_info):
    """
    Enhanced mapping that handles two-step preprocessing:
    1. Initial crop based on segmentation
    2. Distilled-style preprocessing on the cropped region
    """
    mapped_keypoints = []

    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    region_w, region_h = preprocess_info['original_size']  # Size of the cropped region
    resized_w, resized_h = preprocess_info['resized_size']

    for kp in keypoints:
        if kp is None:
            mapped_keypoints.append(None)
            continue

        x, y = kp

        # Step 1: Map from target size (256x256) to crop size
        x_crop = x * crop_size / target_w
        y_crop = y * crop_size / target_h

        # Step 2: Map from crop to resized region
        x_resized = x_crop + crop_start_x
        y_resized = y_crop + crop_start_y

        # Step 3: Map from resized to cropped region coordinates
        x_region = x_resized * region_w / resized_w
        y_region = y_resized * region_h / resized_h

        # Step 4: Map from cropped region to original image coordinates
        if 'initial_crop' in preprocess_info:
            crop_x, crop_y, crop_w, crop_h = preprocess_info['initial_crop']
            x_original = x_region + crop_x
            y_original = y_region + crop_y
        else:
            # No initial crop, coordinates are already in original space
            x_original = x_region
            y_original = y_region

        mapped_keypoints.append((int(x_original), int(y_original)))

    return mapped_keypoints

def detect_chessboard(model, image_path):
    """
    Detect the chessboard in an image and extract it.
    Uses the segmentation model to identify the chessboard region and detect corners.

    Args:
        model: The segmentation model
        image_path: Path to the image file

    Returns:
        Dictionary containing normalized board and grid points
    """
    print(f"Processing image: {image_path}")

    # Load the image
    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"Could not load image from {image_path}")
        return None

    # Save original image for debugging
    cv2.imwrite('original_image.png', original_image)

    if model is not None:
        print("Using segmentation model for chessboard detection...")

        # Use the exact same approach as distilled_segmentation_corner_detection.py
        # Preprocess image
        preprocessed_image, preprocess_info = preprocess_image_for_segmentation(image_path)
        enhanced_image = enhance_image(preprocessed_image)

        # Save preprocessed image for debugging
        cv2.imwrite('preprocessed_enhanced.png', cv2.cvtColor(enhanced_image, cv2.COLOR_RGB2BGR))

        # Normalize for model
        input_tensor = normalize_for_model(enhanced_image)

        # Run inference
        with torch.no_grad():
            outputs = model(input_tensor)

        # Extract segmentation
        segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]

        print(f"Segmentation - min: {segmentation.min():.4f}, max: {segmentation.max():.4f}, mean: {segmentation.mean():.4f}")

        # Save segmentation for debugging
        segmentation_vis = (segmentation * 255).astype(np.uint8)
        cv2.imwrite('segmentation_distilled_approach.png', segmentation_vis)

        # Create a colored segmentation visualization
        segmentation_colored = cv2.applyColorMap(segmentation_vis, cv2.COLORMAP_JET)
        cv2.imwrite('segmentation_colored.png', segmentation_colored)

        # Find corners from segmentation (exact same as distilled script)
        corners = find_corners_from_segmentation(segmentation, threshold=0.5)

        if corners is not None:
            print(f"Found corners on preprocessed image: {corners}")

            # Create corners visualization on the preprocessed image
            corners_on_preprocessed = enhanced_image.copy()
            corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
            colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # RGB format for enhanced_image

            for i, ((x, y), name, color) in enumerate(zip(corners, corner_names, colors)):
                cv2.circle(corners_on_preprocessed, (x, y), 8, color, -1)
                cv2.putText(corners_on_preprocessed, f"{i}: {name}", (x + 10, y + 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

            # Draw lines connecting the corners
            for i in range(4):
                j = (i + 1) % 4
                cv2.line(corners_on_preprocessed, corners[i], corners[j], (255, 255, 255), 2)

            cv2.imwrite('corners_on_preprocessed.png', cv2.cvtColor(corners_on_preprocessed, cv2.COLOR_RGB2BGR))

            # Create combined segmentation + corners visualization
            combined_vis = cv2.cvtColor(segmentation_colored, cv2.COLOR_BGR2RGB)

            # Draw corners on the colored segmentation
            for i, ((x, y), name, color) in enumerate(zip(corners, corner_names, colors)):
                cv2.circle(combined_vis, (x, y), 8, color, -1)
                cv2.putText(combined_vis, f"{i}", (x + 10, y + 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            # Draw lines connecting the corners
            for i in range(4):
                j = (i + 1) % 4
                cv2.line(combined_vis, corners[i], corners[j], (255, 255, 255), 3)

            cv2.imwrite('segmentation_with_corners.png', cv2.cvtColor(combined_vis, cv2.COLOR_RGB2BGR))

            # Map corners back to original image coordinates (exact same as distilled script)
            scaled_corners = map_to_original_coordinates(corners, preprocess_info)
            print(f"Mapped corners to original image: {scaled_corners}")

            # Draw corners on original image for debugging
            corners_vis = original_image.copy()
            corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
            colors_bgr = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (255, 255, 0)]  # BGR format

            for i, ((x, y), name, color) in enumerate(zip(scaled_corners, corner_names, colors_bgr)):
                cv2.circle(corners_vis, (x, y), 15, color, -1)
                cv2.putText(corners_vis, f"{i}: {name}", (x + 20, y + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)

            # Draw lines connecting the corners
            for i in range(4):
                j = (i + 1) % 4
                cv2.line(corners_vis, scaled_corners[i], scaled_corners[j], (0, 255, 255), 3)

            cv2.imwrite('detected_corners_on_original.png', corners_vis)

            # Don't crop or normalize the image - work directly with the original image
            # This preserves the natural perspective and avoids any cropping issues

            print("Working directly with original image - no cropping or normalization")

            # Use the original image as the "board" for piece detection
            board_for_detection = original_image.copy()

            # Save the board for debugging
            cv2.imwrite('board_for_detection.png', board_for_detection)

            # Create grid from the actual detected corners on the original image
            print("Creating grid from detected corners on original image...")
            grid_points = create_grid_from_corners(scaled_corners)

            # Draw grid on original image for debugging
            grid_vis = board_for_detection.copy()

            # Draw horizontal lines
            for row in range(9):
                for col in range(8):
                    pt1 = grid_points[row][col]
                    pt2 = grid_points[row][col+1]
                    cv2.line(grid_vis, pt1, pt2, (0, 255, 0), 3)

            # Draw vertical lines
            for row in range(8):
                for col in range(9):
                    pt1 = grid_points[row][col]
                    pt2 = grid_points[row+1][col]
                    cv2.line(grid_vis, pt1, pt2, (0, 255, 0), 3)

            # Draw grid points
            for row in range(9):
                for col in range(9):
                    cv2.circle(grid_vis, grid_points[row][col], 8, (0, 0, 255), -1)

            # Draw the corner points
            colors_bgr = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (255, 255, 0)]  # BGR format
            for i, (x, y) in enumerate(scaled_corners):
                cv2.circle(grid_vis, (x, y), 15, colors_bgr[i], -1)
                cv2.putText(grid_vis, str(i), (x + 20, y + 20),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

            cv2.imwrite('grid_from_corners.png', grid_vis)

            return {
                'board_for_detection': board_for_detection,
                'grid_points': grid_points,
                'original_image': original_image,
                'corners': scaled_corners
            }

        # Fallback to bounding box approach if corner detection fails
        print("Corner detection failed, falling back to bounding box approach...")

        # Create binary mask with threshold 0.5
        binary_mask = (segmentation > 0.5).astype(np.uint8) * 255
        cv2.imwrite('binary_mask_0.5.png', binary_mask)

        # Find contours in the binary mask
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            print("No contours found in segmentation mask, falling back to direct detection...")
        else:
            # Find the largest contour (the chess board)
            largest_contour = max(contours, key=cv2.contourArea)

            # Get the bounding rectangle of the largest contour
            x_seg, y_seg, w_seg, h_seg = cv2.boundingRect(largest_contour)

            # Scale to original image size
            orig_h, orig_w = original_image.shape[:2]
            x = int(x_seg * orig_w / 256)
            y = int(y_seg * orig_h / 256)
            w = int(w_seg * orig_w / 256)
            h = int(h_seg * orig_h / 256)

            # Draw the bounding rectangle on the original image for debugging
            rect_vis = original_image.copy()
            cv2.rectangle(rect_vis, (x, y), (x + w, y + h), (0, 255, 0), 2)
            cv2.imwrite('bounding_rect_segmentation.png', rect_vis)

            # Extract the chessboard region
            chessboard_region = original_image[y:y+h, x:x+w]

            # Resize to a square shape for piece detection
            normalized_board = cv2.resize(chessboard_region, (416, 416), interpolation=cv2.INTER_LANCZOS4)

            # Save normalized board for debugging
            cv2.imwrite('normalized_board.png', normalized_board)

            # Create a simple regular grid on the normalized board
            print("Creating regular grid on normalized board...")
            grid_points = []
            for row in range(9):
                row_points = []
                for col in range(9):
                    x = int(col * 416 / 8)
                    y = int(row * 416 / 8)
                    row_points.append((x, y))
                grid_points.append(row_points)

            # Draw grid on original image for debugging
            grid_vis = board_for_detection.copy()

            # Draw horizontal lines
            for row in range(9):
                for col in range(8):
                    pt1 = grid_points[row][col]
                    pt2 = grid_points[row][col+1]
                    cv2.line(grid_vis, pt1, pt2, (0, 255, 0), 3)

            # Draw vertical lines
            for row in range(8):
                for col in range(9):
                    pt1 = grid_points[row][col]
                    pt2 = grid_points[row+1][col]
                    cv2.line(grid_vis, pt1, pt2, (0, 255, 0), 3)

            # Draw grid points
            for row in range(9):
                for col in range(9):
                    cv2.circle(grid_vis, grid_points[row][col], 8, (0, 0, 255), -1)

            cv2.imwrite('grid_on_original.png', grid_vis)

            return {
                'board_for_detection': board_for_detection,
                'grid_points': grid_points,
                'original_image': original_image
            }

    # Fallback to direct detection if segmentation model is not provided or fails
    print("Using direct chessboard detection...")

    # Convert to grayscale for better chessboard detection
    gray = cv2.cvtColor(original_image, cv2.COLOR_BGR2GRAY)

    # Apply adaptive thresholding to get a binary image
    binary = cv2.adaptiveThreshold(
        gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
        cv2.THRESH_BINARY, 11, 2
    )

    # Save binary image for debugging
    cv2.imwrite('binary.png', binary)

    # Find contours in the binary image
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # Find the largest contour (likely the chessboard)
    largest_contour = max(contours, key=cv2.contourArea)

    # Get the bounding rectangle of the largest contour
    x, y, w, h = cv2.boundingRect(largest_contour)

    # Draw the bounding rectangle on the original image for debugging
    rect_vis = original_image.copy()
    cv2.rectangle(rect_vis, (x, y), (x + w, y + h), (0, 255, 0), 2)
    cv2.imwrite('bounding_rect_direct.png', rect_vis)

    # Extract the chessboard region
    chessboard_region = original_image[y:y+h, x:x+w]

    # Resize to a square shape for piece detection
    normalized_board = cv2.resize(chessboard_region, (416, 416), interpolation=cv2.INTER_LANCZOS4)

    # Save normalized board for debugging
    cv2.imwrite('normalized_board.png', normalized_board)

    # Create a grid on the normalized board
    print("Creating grid on normalized board...")
    # Create evenly spaced grid points on the normalized board
    grid_points = []
    for row in range(9):
        row_points = []
        for col in range(9):
            x = int(col * 416 / 8)
            y = int(row * 416 / 8)
            row_points.append((x, y))
        grid_points.append(row_points)

    # Draw grid on normalized board for debugging
    grid_vis = normalized_board.copy()

    # Draw horizontal lines
    for row in range(9):
        for col in range(8):
            pt1 = grid_points[row][col]
            pt2 = grid_points[row][col+1]
            cv2.line(grid_vis, pt1, pt2, (0, 255, 0), 2)

    # Draw vertical lines
    for row in range(8):
        for col in range(9):
            pt1 = grid_points[row][col]
            pt2 = grid_points[row+1][col]
            cv2.line(grid_vis, pt1, pt2, (0, 255, 0), 2)

    # Draw grid points
    for row in range(9):
        for col in range(9):
            cv2.circle(grid_vis, grid_points[row][col], 5, (0, 0, 255), -1)
            # Add coordinates for debugging
            cv2.putText(grid_vis, f"{row},{col}", grid_points[row][col],
                       cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

    cv2.imwrite('grid_on_normalized.png', grid_vis)

    return {
        'board_for_detection': original_image,  # Use original image for fallback too
        'grid_points': grid_points,
        'original_image': original_image
    }

def detect_pieces(model_path, chessboard_img):
    """
    Detect chess pieces on the normalized chessboard.
    Returns the piece types and their positions.
    """
    # Load model
    model = YOLO(model_path)

    # Convert to RGB if needed
    chessboard_img_rgb = cv2.cvtColor(chessboard_img, cv2.COLOR_BGR2RGB) if len(chessboard_img.shape) == 3 else chessboard_img

    # Run inference
    results = model(chessboard_img_rgb, imgsz=416, iou=0.7)[0]

    # Get detections
    boxes = results.boxes.xyxy.cpu().numpy()
    scores = results.boxes.conf.cpu().numpy()
    class_ids = results.boxes.cls.cpu().numpy()

    # Process results
    pieces = []

    for i in range(len(boxes)):
        x1, y1, x2, y2 = boxes[i]
        cls_id = int(class_ids[i])
        conf = scores[i]

        # Get class name
        if cls_id < len(CONFIG["class_names"]):
            class_name = CONFIG["class_names"][cls_id]
        else:
            class_name = f"unknown_{cls_id}"

        # Calculate center point of the piece
        center_x = (x1 + x2) / 2
        center_y = (y1 + y2) / 2

        pieces.append({
            'class': class_name,
            'confidence': conf,
            'box': (x1, y1, x2, y2),
            'center': (center_x, center_y)
        })

    return pieces

def create_grid_from_corners(corners):
    """
    Create an 8x8 grid that follows the actual chessboard lines as closely as possible.
    Instead of creating perfect squares, this uses bilinear interpolation to follow
    the perspective distortion of the actual board.

    Args:
        corners: List of four corner points [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
                in order: top-left, top-right, bottom-right, bottom-left
    """
    # Ensure corners are in the correct order: top-left, top-right, bottom-right, bottom-left
    corners = sort_corners(corners)

    print(f"Creating grid from corners: {corners}")

    # Create a grid of 9x9 points (for 8x8 squares) using bilinear interpolation
    # This follows the actual perspective distortion of the board
    grid_points = []

    for row in range(9):
        row_points = []
        for col in range(9):
            # Calculate normalized position (0-1) for bilinear interpolation
            u = col / 8.0  # horizontal parameter (0 to 1)
            v = row / 8.0  # vertical parameter (0 to 1)

            # Bilinear interpolation between the four corners
            # This naturally follows the perspective distortion of the board

            # Top edge interpolation (between top-left and top-right)
            top_x = corners[0][0] * (1 - u) + corners[1][0] * u
            top_y = corners[0][1] * (1 - u) + corners[1][1] * u

            # Bottom edge interpolation (between bottom-left and bottom-right)
            bottom_x = corners[3][0] * (1 - u) + corners[2][0] * u
            bottom_y = corners[3][1] * (1 - u) + corners[2][1] * u

            # Final interpolation between top and bottom edges
            x = int(top_x * (1 - v) + bottom_x * v)
            y = int(top_y * (1 - v) + bottom_y * v)

            row_points.append((x, y))
        grid_points.append(row_points)

    print(f"Generated grid with {len(grid_points)} rows and {len(grid_points[0])} columns")
    return grid_points

def calculate_box_overlap(box, square_corners):
    """
    Calculate the overlap area between a bounding box and a grid square.

    Args:
        box: Tuple of (x1, y1, x2, y2) for the bounding box
        square_corners: List of four corner points [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
                        in order: top-left, top-right, bottom-right, bottom-left

    Returns:
        Overlap area as a percentage of the box area
    """
    # Convert square corners to a contour format for cv2.contourArea
    square_contour = np.array(square_corners).reshape((-1, 1, 2)).astype(np.int32)

    # Create a mask for the square
    mask_size = (max(int(box[2]), int(square_corners[2][0])) + 10,
                max(int(box[3]), int(square_corners[2][1])) + 10)
    square_mask = np.zeros(mask_size, dtype=np.uint8)
    cv2.drawContours(square_mask, [square_contour], 0, 255, -1)

    # Create a mask for the bounding box
    box_mask = np.zeros_like(square_mask, dtype=np.uint8)
    box_contour = np.array([
        [[int(box[0]), int(box[1])]],
        [[int(box[2]), int(box[1])]],
        [[int(box[2]), int(box[3])]],
        [[int(box[0]), int(box[3])]]
    ]).astype(np.int32)
    cv2.drawContours(box_mask, [box_contour], 0, 255, -1)

    # Calculate intersection
    intersection = cv2.bitwise_and(square_mask, box_mask)
    intersection_area = cv2.countNonZero(intersection)

    # Calculate box area
    box_area = (box[2] - box[0]) * (box[3] - box[1])

    # Return overlap as percentage of box area
    return intersection_area / box_area if box_area > 0 else 0

def map_pieces_to_grid(pieces, board_img, grid_points):
    """
    Map detected pieces to a chess board grid (8x8) based on their positions.
    Uses a simple grid division approach since the board is already normalized.
    Assumes a1 is at bottom left and h8 is at top right.

    Args:
        pieces: List of detected chess pieces
        board_img: The normalized chessboard image
        grid_points: 9x9 grid points for the 8x8 squares
    """
    # Initialize empty 8x8 grid
    grid = [[None for _ in range(8)] for _ in range(8)]

    # First, sort pieces by confidence (highest first)
    sorted_pieces = sorted(pieces, key=lambda p: p['confidence'], reverse=True)

    for piece in sorted_pieces:
        # Get the center of the piece
        center_x, center_y = piece['center']

        # Find the grid square that contains this center point
        for row in range(8):
            for col in range(8):
                # Get the four corners of this grid square
                square_corners = [
                    grid_points[row][col],      # top-left
                    grid_points[row][col+1],    # top-right
                    grid_points[row+1][col+1],  # bottom-right
                    grid_points[row+1][col]     # bottom-left
                ]

                # Check if the center point is inside this square
                # We can use a simple bounding box check since our grid is regular
                min_x = min(x for x, y in square_corners)
                max_x = max(x for x, y in square_corners)
                min_y = min(y for x, y in square_corners)
                max_y = max(y for x, y in square_corners)

                if min_x <= center_x <= max_x and min_y <= center_y <= max_y:
                    # Convert to chess coordinates (a1 is bottom-left)
                    chess_row = 7 - row  # Flip row for chess coordinates
                    chess_col = col

                    # If the square is empty, place the piece
                    if grid[chess_row][chess_col] is None:
                        # Store the piece and its grid position
                        piece_with_grid = piece.copy()
                        piece_with_grid['grid_position'] = (chess_row, chess_col)
                        piece_with_grid['grid_square'] = (row, col)  # Original grid position for visualization
                        grid[chess_row][chess_col] = piece_with_grid

                    # Break out of the inner loop
                    break

            # If we found a match, break out of the outer loop
            if grid[7-row][col] is not None:
                break

    return grid

def generate_fen_position(grid):
    """
    Generate FEN notation for the piece positions.
    Only includes the first field of FEN (piece placement).
    """
    fen = []

    # Process each row (from 8 to 1 in chess notation)
    for row in grid:
        empty_count = 0
        row_fen = ""

        for square in row:
            if square is None:
                # Empty square
                empty_count += 1
            else:
                # If there were empty squares before this piece, add the count
                if empty_count > 0:
                    row_fen += str(empty_count)
                    empty_count = 0

                # Add the piece symbol
                piece_class = square['class']
                row_fen += CONFIG["fen_symbols"][piece_class]

        # If there are empty squares at the end of the row
        if empty_count > 0:
            row_fen += str(empty_count)

        fen.append(row_fen)

    # Join rows with '/' separator
    return "/".join(fen)

def visualize_board_with_grid(chessboard_img, grid, pieces, grid_points):
    """
    Visualize the detected pieces on the chessboard with grid overlay and FEN position.
    Uses the grid points created from the detected corners.
    """
    # Create a copy of the image for visualization
    vis_img = chessboard_img.copy()

    # Convert to RGB if needed
    if len(vis_img.shape) == 3 and vis_img.shape[2] == 3:
        vis_img_rgb = cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB)
    else:
        vis_img_rgb = vis_img

    # Create a semi-transparent overlay for the grid
    overlay = vis_img_rgb.copy()

    # Color alternating squares for better visualization
    for row in range(8):
        for col in range(8):
            # Determine if this is a light or dark square
            is_light = (row + col) % 2 == 0

            # Get the four corners of this grid square
            square_corners = [
                grid_points[row][col],      # top-left
                grid_points[row][col+1],    # top-right
                grid_points[row+1][col+1],  # bottom-right
                grid_points[row+1][col]     # bottom-left
            ]

            # Convert to numpy array for drawing
            square_contour = np.array(square_corners).reshape((-1, 1, 2)).astype(np.int32)

            # Draw colored overlay for the square
            if is_light:
                cv2.drawContours(overlay, [square_contour], 0, (240, 240, 240), -1)  # Light gray
            else:
                cv2.drawContours(overlay, [square_contour], 0, (180, 180, 180), -1)  # Dark gray

    # Apply the overlay with transparency
    alpha = 0.3  # Transparency factor
    cv2.addWeighted(overlay, alpha, vis_img_rgb, 1 - alpha, 0, vis_img_rgb)

    # Draw grid lines
    for row in range(9):
        for col in range(8):
            # Draw horizontal lines
            pt1 = grid_points[row][col]
            pt2 = grid_points[row][col+1]
            cv2.line(vis_img_rgb, pt1, pt2, (0, 0, 0), 1)

    for row in range(8):
        for col in range(9):
            # Draw vertical lines
            pt1 = grid_points[row][col]
            pt2 = grid_points[row+1][col]
            cv2.line(vis_img_rgb, pt1, pt2, (0, 0, 0), 1)

    # Draw pieces and their grid positions
    for row in range(8):
        for col in range(8):
            piece = grid[row][col]
            if piece is not None:
                # Get the four corners of this grid square
                square_corners = [
                    grid_points[7-row][col],      # top-left (flip row for chess coordinates)
                    grid_points[7-row][col+1],    # top-right
                    grid_points[7-row+1][col+1],  # bottom-right
                    grid_points[7-row+1][col]     # bottom-left
                ]

                # Calculate the center of the square
                center_x = int(sum(x for x, y in square_corners) / 4)
                center_y = int(sum(y for x, y in square_corners) / 4)

                # Get piece information
                class_name = piece['class']
                conf = piece['confidence']
                x1, y1, x2, y2 = piece['box']
                overlap = piece.get('overlap', 0)

                # Determine color based on piece type
                if 'white' in class_name:
                    color = (0, 150, 0)  # Green for white pieces
                else:
                    color = (150, 0, 0)  # Red for black pieces

                # Draw bounding box
                cv2.rectangle(vis_img_rgb, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)

                # Draw piece label with FEN symbol and confidence
                piece_type = class_name.split('_')[1]  # Extract 'pawn', 'knight', etc.
                fen_symbol = CONFIG["fen_symbols"][class_name]
                label = f"{fen_symbol} ({conf:.2f})"

                # Draw label with background for better visibility
                text_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]
                cv2.rectangle(vis_img_rgb,
                             (center_x - text_size[0]//2 - 2, center_y - text_size[1]//2 - 2),
                             (center_x + text_size[0]//2 + 2, center_y + text_size[1]//2 + 2),
                             (255, 255, 255), -1)
                cv2.putText(vis_img_rgb, label,
                           (center_x - text_size[0]//2, center_y + text_size[1]//2),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

                # Draw grid coordinates
                coord = f"{chr(97 + col)}{row + 1}"  # e.g., "a1", "e4", etc.
                cv2.putText(vis_img_rgb, coord, (int(x1), int(y1) - 5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 0, 0), 1)

                # Draw overlap percentage
                if overlap > 0:
                    overlap_text = f"{overlap:.1%}"
                    cv2.putText(vis_img_rgb, overlap_text, (int(x2) - 30, int(y2) + 15),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, color, 1)

    # Add coordinate labels around the board
    font = cv2.FONT_HERSHEY_SIMPLEX
    font_scale = 0.6
    font_color = (0, 0, 0)  # Black
    font_thickness = 2

    # File labels (a-h) at the bottom
    for col in range(8):
        # Calculate position for the label (center of bottom edge)
        x = int((grid_points[8][col][0] + grid_points[8][col+1][0]) / 2)
        y = grid_points[8][col][1] + 20
        cv2.putText(vis_img_rgb, chr(97 + col), (x, y), font, font_scale, font_color, font_thickness)

    # Rank labels (1-8) on the left
    for row in range(8):
        # Calculate position for the label (left of the left edge)
        x = grid_points[row][0][0] - 20
        y = int((grid_points[row][0][1] + grid_points[row+1][0][1]) / 2)
        cv2.putText(vis_img_rgb, str(8 - row), (x, y), font, font_scale, font_color, font_thickness)

    return vis_img_rgb

def main():
    """Main function to generate FEN from a chess board image."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Generate FEN notation from a chess board image.')
    parser.add_argument('image_path', type=str, help='Path to the chess board image')
    parser.add_argument('--output', type=str, default='fen_output.png', help='Path to save the visualization')
    args = parser.parse_args()

    # Stage 1: Detect chessboard
    print("Stage 1: Detecting chessboard...")

    # First, let's visualize the segmentation output
    print("Visualizing segmentation output...")
    # Load the teacher model (EnhancedChessBoardUNetV5_2)
    segmentation_model = EnhancedChessBoardUNetV5_2(n_channels=3)
    segmentation_model.load_state_dict(torch.load(CONFIG["segmentation_model"], map_location=torch.device('cpu')))
    segmentation_model.eval()

    # Load the image
    original_image = cv2.imread(args.image_path)
    if original_image is None:
        print(f"Could not load image from {args.image_path}")
        return

    # Save original image for debugging
    cv2.imwrite('original_image.png', original_image)

    # Resize to the correct input size for the segmentation model (256x256)
    # This is important as the model was trained on this specific size
    resized_image = cv2.resize(original_image, (256, 256))
    cv2.imwrite('resized_256x256.png', resized_image)

    # Convert to RGB (from BGR) and normalize to 0-1 range
    rgb_image = cv2.cvtColor(resized_image, cv2.COLOR_BGR2RGB)
    normalized_image = rgb_image.astype(np.float32) / 255.0

    # Transpose from (H,W,C) to (C,H,W) for PyTorch
    transposed_image = normalized_image.transpose(2, 0, 1)

    # Create tensor and add batch dimension
    input_tensor = torch.from_numpy(transposed_image).unsqueeze(0)

    # Run inference
    with torch.no_grad():
        outputs = segmentation_model(input_tensor)

    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]

    # Print segmentation statistics to understand the values
    print(f"Segmentation min: {segmentation.min()}, max: {segmentation.max()}, mean: {segmentation.mean()}")

    # Save raw segmentation for visualization
    segmentation_vis = (segmentation * 255).astype(np.uint8)
    cv2.imwrite('segmentation_raw.png', segmentation_vis)

    # Create a binary mask with different thresholds to see what works
    thresholds = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
    for threshold in thresholds:
        binary_mask = (segmentation > threshold).astype(np.uint8) * 255
        cv2.imwrite(f'segmentation_binary_{threshold:.1f}.png', binary_mask)

    # Create a heatmap visualization to better see the gradients
    segmentation_heatmap = cv2.applyColorMap(segmentation_vis, cv2.COLORMAP_JET)
    cv2.imwrite('segmentation_heatmap.png', segmentation_heatmap)

    # Normalize the segmentation to use full range 0-255 for better visualization
    segmentation_normalized = cv2.normalize(segmentation, None, 0, 255, cv2.NORM_MINMAX)
    segmentation_normalized = segmentation_normalized.astype(np.uint8)
    cv2.imwrite('segmentation_normalized.png', segmentation_normalized)

    # Create a heatmap of the normalized segmentation
    normalized_heatmap = cv2.applyColorMap(segmentation_normalized, cv2.COLORMAP_JET)
    cv2.imwrite('segmentation_normalized_heatmap.png', normalized_heatmap)

    # Overlay segmentation on original image
    overlay = original_image.copy()
    segmentation_resized = cv2.resize(segmentation_normalized, (original_image.shape[1], original_image.shape[0]))
    segmentation_heatmap_resized = cv2.applyColorMap(segmentation_resized, cv2.COLORMAP_JET)

    # Create alpha blend
    alpha = 0.5
    overlay = cv2.addWeighted(original_image, 1-alpha, segmentation_heatmap_resized, alpha, 0)
    cv2.imwrite('segmentation_overlay.png', overlay)

    print("Segmentation visualizations saved.")

    # Now proceed with chessboard detection using the segmentation model
    chessboard_results = detect_chessboard(segmentation_model, args.image_path)

    if chessboard_results is None:
        print("No chessboard detected. Exiting.")
        return

    board_for_detection = chessboard_results['board_for_detection']

    # Stage 2: Detect chess pieces
    print("Stage 2: Detecting chess pieces...")
    pieces = detect_pieces(CONFIG["piece_model"], board_for_detection)

    print(f"Detected {len(pieces)} chess pieces")

    # Stage 3: Generate FEN notation
    print("Stage 3: Generating FEN notation...")

    # Map pieces to grid squares
    print("Mapping pieces to grid squares...")
    grid_points = chessboard_results['grid_points']
    grid = map_pieces_to_grid(pieces, board_for_detection, grid_points)
    fen_position = generate_fen_position(grid)

    print(f"FEN position: {fen_position}")

    # Visualize the results
    vis_img = visualize_board_with_grid(board_for_detection, grid, pieces, grid_points)

    # Create a figure with two subplots: visualization and FEN representation
    plt.figure(figsize=(15, 10))

    # Plot the visualization
    plt.subplot(1, 2, 1)
    plt.imshow(vis_img)
    plt.title("Chess Piece Detection with Grid")
    plt.axis('off')

    # Create a text representation of the board with FEN symbols
    plt.subplot(1, 2, 2)
    plt.axis('off')

    # Create a visual representation of the FEN
    fen_rows = fen_position.split('/')
    board_text = ""

    # Add column labels (a-h)
    board_text += "    a b c d e f g h\n"
    board_text += "    ---------------\n"

    # Add each row with rank label
    for i, row in enumerate(fen_rows):
        rank = 8 - i
        board_text += f"{rank} | "

        # Process each character in the FEN row
        for char in row:
            if char.isdigit():
                # Empty squares
                board_text += ". " * int(char)
            else:
                # Piece
                board_text += char + " "

        board_text += f"| {rank}\n"

    board_text += "    ---------------\n"
    board_text += "    a b c d e f g h\n\n"

    # Add the FEN string
    board_text += f"FEN: {fen_position}"

    plt.text(0.1, 0.5, board_text, fontsize=14, family='monospace')
    plt.title("FEN Representation")

    plt.tight_layout()
    plt.savefig(args.output, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"Visualization saved to {args.output}")

    # Also save the FEN to a text file
    fen_file = os.path.splitext(args.output)[0] + ".fen"
    with open(fen_file, 'w') as f:
        f.write(fen_position)

    print(f"FEN notation saved to {fen_file}")

if __name__ == "__main__":
    main()
