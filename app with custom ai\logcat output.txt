--------- beginning of main
---------------------------- PROCESS STARTED (15118) for package com.chessvision.app ----------------------------
2025-05-27 08:50:45.702 15118-15118 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/base.dm': No such file or directory
2025-05-27 08:50:45.702 15118-15118 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/base.dm': No such file or directory
2025-05-27 08:50:46.896 15118-15118 nativeloader            com.chessvision.app                  D  Configuring clns-4 for other apk /data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/lib/arm64:/data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.chessvision.app
2025-05-27 08:50:46.928 15118-15118 GraphicsEnvironment     com.chessvision.app                  V  ANGLE Developer option for 'com.chessvision.app' set to: 'default'
2025-05-27 08:50:46.929 15118-15118 GraphicsEnvironment     com.chessvision.app                  V  Neither updatable production driver nor prerelease driver is supported.
2025-05-27 08:50:46.937 15118-15118 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 08:50:46.940 15118-15118 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 08:50:46.981 15118-15118 libc                    com.chessvision.app                  W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-27 08:50:47.084 15118-15118 ChessVisionApp          com.chessvision.app                  D  Application initialized
2025-05-27 08:50:47.243 15118-15118 chessvision.ap          com.chessvision.app                  E  Invalid ID 0x00000000.
2025-05-27 08:50:47.644 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 38 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:50:47.650 15118-15118 XDR::VRT                com.chessvision.app                  I  sc is not valid!
2025-05-27 08:50:49.140 15118-15118 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-27 08:50:49.141 15118-15118 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 08:50:49.141 15118-15118 chessvision.ap          com.chessvision.app                  W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 08:50:49.142 15118-15118 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 08:50:49.142 15118-15118 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 08:50:49.321 15118-15118 Compatibil...geReporter com.chessvision.app                  D  Compat change id reported: 171228096; UID 10719; state: ENABLED
2025-05-27 08:50:49.604 15118-15124 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 415KB AllocSpace bytes, 0(0B) LOS objects, 36% free, 3639KB/5687KB, paused 389us,70us total 113.970ms
2025-05-27 08:50:49.889 15118-15118 BufferQueueConsumer     com.chessvision.app                  I  [](id:3b0e00000000,api:0,p:-1,c:15118) connect: controlledByApp=false
2025-05-27 08:50:49.895 15118-15118 BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0] constructor()
2025-05-27 08:50:49.944 15118-15175 hw-ProcessState         com.chessvision.app                  D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-27 08:50:49.978 15118-15175 BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0(BLAST Consumer)0](id:3b0e00000000,api:1,p:15118,c:15118) connect: api=1 producerControlledByApp=true
2025-05-27 08:50:49.981 15118-15123 chessvision.ap          com.chessvision.app                  I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-27 08:50:49.994 15118-15189 ion                     com.chessvision.app                  E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-27 08:50:50.057 15118-15175 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-27 08:50:50.057 15118-15175 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-27 08:50:50.076 15118-15128 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3053ms; Flags=1, FrameTimelineVsyncId=19004152, IntendedVsync=356137136154851, Vsync=356137769488197, InputEventId=0, HandleInputStart=356137779495304, AnimationStart=356137779527611, PerformTraversalsStart=356137780110996, DrawStart=356140114236304, FrameDeadline=356137156154851, FrameInterval=356137778947380, FrameStartTime=16666667, SyncQueued=356140131274381, SyncStart=356140138187073, IssueDrawCommandsStart=356140139461150, SwapBuffers=356140182923611, FrameCompleted=356140196752611, DequeueBufferDuration=0, QueueBufferDuration=2701000, GpuCompleted=356140195198381, SwapBuffersCompleted=356140196752611, DisplayPresentTime=0, 
2025-05-27 08:50:50.180 15118-15118 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:50:50.181 15118-15118 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:50:50.182 15118-15118 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=null
2025-05-27 08:50:50.183 15118-15118 ImeFocusController      com.chessvision.app                  V  checkFocus: view=null next=DecorView@17fb6e3[MainActivity] force=true package=<none>
2025-05-27 08:50:50.401 15118-15201 ChessAI                 com.chessvision.app                  D  🚀 Initializing enterprise-grade Chess AI...
2025-05-27 08:50:50.402 15118-15201 ChessAI                 com.chessvision.app                  D  🎯 V6 Segmentation: EXACT same weights (Dice: 0.9391)
2025-05-27 08:50:50.402 15118-15201 ChessAI                 com.chessvision.app                  D  🎯 YOLO Detection: EXACT same weights (mAP50: 97.3%)
2025-05-27 08:50:50.402 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking guaranteed
2025-05-27 08:50:50.406 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 Initializing ONNX Chess AI with enterprise-grade resource management...
2025-05-27 08:50:50.406 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔬 V6 Model: EXACT same weights as Python (Dice: 0.9391)
2025-05-27 08:50:50.406 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model: EXACT same weights as Python (mAP50: 97.3%)
2025-05-27 08:50:50.411 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ V6 model already exists
2025-05-27 08:50:50.412 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO model already exists
2025-05-27 08:50:50.512 15118-15201 nativeloader            com.chessvision.app                  D  Load /data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/base.apk!/lib/arm64-v8a/libonnxruntime4j_jni.so using ns clns-4 from class loader (caller=/data/app/~~LOcUPHqrHYRojlamOBXw4Q==/com.chessvision.app-nqKfmNjSHFzo1kVU-1pETQ==/base.apk): ok
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔍 Hardware Profile Detected:
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  📱 Device: V2147 (vivo)
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔧 Hardware: mt6765
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  💾 Total RAM: 2GB (2800MB)
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧠 CPU Cores: 8
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Helio P35: true
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎮 PowerVR GE8320: true
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⚡ Optimization Level: MEMORY_OPTIMIZED
2025-05-27 08:50:50.588 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔋 Low Memory Device: true
2025-05-27 08:50:50.590 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 Cortex-A53 optimizations applied for maximum performance
2025-05-27 08:50:50.590 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 MEMORY_OPTIMIZED mode: Helio P35 + 2.8GB RAM BREAKTHROUGH optimizations
2025-05-27 08:50:50.590 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎮 PowerVR GE8320 detected - using CPU-optimized path
2025-05-27 08:50:50.590 15118-15201 ONNXChessAI             com.chessvision.app                  D  💾 Memory pattern optimization disabled for 3GB RAM
2025-05-27 08:50:50.590 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⚠️ CPU Memory Arena not available in this ONNX Runtime version
2025-05-27 08:50:50.821 15118-15201 libc                    com.chessvision.app                  W  Access denied finding property "ro.hardware.chipname"
2025-05-27 08:50:51.827 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 84 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:50:51.828 15118-15128 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1436ms; Flags=0, FrameTimelineVsyncId=19004239, IntendedVsync=356140519097891, Vsync=356140535764558, InputEventId=0, HandleInputStart=356140538149842, AnimationStart=356140538154688, PerformTraversalsStart=356141247889535, DrawStart=356141248212919, FrameDeadline=356140555764558, FrameInterval=356140538113534, FrameStartTime=16666667, SyncQueued=356141947769842, SyncStart=356141947939073, IssueDrawCommandsStart=356141948477458, SwapBuffers=356141950337842, FrameCompleted=356141955396304, DequeueBufferDuration=44231, QueueBufferDuration=1955077, GpuCompleted=356141954983611, SwapBuffersCompleted=356141955396304, DisplayPresentTime=1630609416544980717, 
2025-05-27 08:50:51.910 15118-15128 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1477ms; Flags=0, FrameTimelineVsyncId=19004245, IntendedVsync=356140552416993, Vsync=356141952417021, InputEventId=0, HandleInputStart=356141962008842, AnimationStart=356141962013458, PerformTraversalsStart=356142015477535, DrawStart=356142015725150, FrameDeadline=356140589083660, FrameInterval=356141961729458, FrameStartTime=16666667, SyncQueued=356142023994150, SyncStart=356142024210996, IssueDrawCommandsStart=356142024584304, SwapBuffers=356142026034381, FrameCompleted=356142030525535, DequeueBufferDuration=47077, QueueBufferDuration=2559769, GpuCompleted=356142029674458, SwapBuffersCompleted=356142030525535, DisplayPresentTime=1646090776860825301, 
2025-05-27 08:50:52.734 15118-15218 ProfileInstaller        com.chessvision.app                  D  Installing profile for com.chessvision.app
2025-05-27 08:50:53.252 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX Runtime sessions created successfully!
2025-05-27 08:50:53.252 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 V6 Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/v6_mobile.onnx
2025-05-27 08:50:53.252 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/yolo_mobile.onnx
2025-05-27 08:50:53.252 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade resource management active
2025-05-27 08:50:53.252 15118-15201 ChessAI                 com.chessvision.app                  D  ✅ Enterprise-grade Chess AI initialized successfully!
2025-05-27 08:50:53.252 15118-15201 ChessAI                 com.chessvision.app                  D  🚀 Ready for zero-latency processing
2025-05-27 08:51:07.385 15118-15118 CameraStateManager      com.chessvision.app                  W  ⚠️ Camera provider not available
2025-05-27 08:51:07.385 15118-15118 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 08:51:07.527 15118-15118 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 08:51:07.616 15118-15340 CameraManagerGlobal     com.chessvision.app                  I  Connecting to camera service
2025-05-27 08:51:07.734 15118-15340 CameraRepository        com.chessvision.app                  D  Added camera: 0
2025-05-27 08:51:07.841 15118-15132 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=809ms; Flags=0, FrameTimelineVsyncId=19004310, IntendedVsync=356157149365267, Vsync=356157216031935, InputEventId=**********, HandleInputStart=**********72843, AnimationStart=**********77766, PerformTraversalsStart=356157729371382, DrawStart=356157849194689, FrameDeadline=356157169365267, FrameInterval=**********13766, FrameStartTime=16666667, SyncQueued=356157891424228, SyncStart=356157892388843, IssueDrawCommandsStart=356157893377536, SwapBuffers=356157954527689, FrameCompleted=356157960290151, DequeueBufferDuration=176923, QueueBufferDuration=2408230, GpuCompleted=356157960290151, SwapBuffersCompleted=356157958886689, DisplayPresentTime=1106761872131690330, 
2025-05-27 08:51:07.842 15118-15340 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 08:51:07.863 15118-15340 CameraRepository        com.chessvision.app                  D  Added camera: 1
2025-05-27 08:51:07.865 15118-15340 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 08:51:07.867 15118-15340 CameraValidator         com.chessvision.app                  D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-27 08:51:07.872 15118-15118 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
--------- beginning of system
2025-05-27 08:51:28.959 15118-15118 skia                    com.chessvision.app                  D  libjpeg error 116 <Corrupt JPEG data: 130816 extraneous bytes before marker 0xdb> from output_message
2025-05-27 08:51:28.960 15118-15118 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:51:29.171 15118-15118 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:51:29.410 15118-15124 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 813KB AllocSpace bytes, 1(24KB) LOS objects, 26% free, 5594KB/7642KB, paused 471us,58us total 152.619ms
2025-05-27 08:51:29.429 15118-15118 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 08:51:29.430 15118-15118 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 08:51:29.430 15118-15118 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 08:51:29.431 15118-15118 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 08:51:29.565 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 41 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:51:29.639 15118-15118 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:51:29.639 15118-15118 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:51:29.640 15118-15118 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 08:51:29.640 15118-15118 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 08:51:29.655 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=961ms; Flags=0, FrameTimelineVsyncId=19005620, IntendedVsync=356178812751125, Vsync=356178812751125, InputEventId=0, HandleInputStart=356178814275075, AnimationStart=356178814282691, PerformTraversalsStart=356179571094768, DrawStart=356179650719614, FrameDeadline=356178832751125, FrameInterval=356178814207691, FrameStartTime=16666667, SyncQueued=356179685214075, SyncStart=356179685388845, IssueDrawCommandsStart=356179686021229, SwapBuffers=356179766298998, FrameCompleted=356179774208306, DequeueBufferDuration=68000, QueueBufferDuration=2721846, GpuCompleted=356179774208306, SwapBuffersCompleted=356179770844921, DisplayPresentTime=548613421434406811, 
2025-05-27 08:51:29.670 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=702ms; Flags=0, FrameTimelineVsyncId=19005653, IntendedVsync=356179012610802, Vsync=356179695944149, InputEventId=0, HandleInputStart=356179699826691, AnimationStart=356179699831383, PerformTraversalsStart=356179699833845, DrawStart=356179704481229, FrameDeadline=356179049277469, FrameInterval=356179699586460, FrameStartTime=16666667, SyncQueued=356179704853460, SyncStart=356179770944614, IssueDrawCommandsStart=356179771283075, SwapBuffers=356179773854998, FrameCompleted=356179780879921, DequeueBufferDuration=44846, QueueBufferDuration=1216615, GpuCompleted=356179780879921, SwapBuffersCompleted=356179775776306, DisplayPresentTime=570558479528953832, 
2025-05-27 08:51:33.860 15118-15118 CameraStateManager      com.chessvision.app                  W  ⚠️ Camera provider not available
2025-05-27 08:51:33.860 15118-15118 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 08:51:34.014 15118-15118 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 08:51:34.229 15118-15118 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 08:51:41.829 15118-15118 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:51:41.993 15118-15118 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:51:42.206 15118-15124 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 741KB AllocSpace bytes, 2(48KB) LOS objects, 26% free, 5597KB/7645KB, paused 544us,120us total 117.031ms
2025-05-27 08:51:42.207 15118-15118 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 08:51:42.207 15118-15118 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 08:51:42.207 15118-15118 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 08:51:42.207 15118-15118 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 08:51:42.338 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 32 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:51:42.347 15118-15118 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:51:42.347 15118-15118 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:51:42.347 15118-15118 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 08:51:42.348 15118-15118 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 08:51:43.444 15118-15118 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:51:43.444 15118-15118 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:51:43.449 15118-15201 ChessAI                 com.chessvision.app                  D  🔍 Starting operation #1 with zero main thread impact...
2025-05-27 08:51:43.449 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 08:51:43.455 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #1 with zero main thread blocking...
2025-05-27 08:51:43.486 15118-15201 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:51:43.929 15118-15201 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:51:43.931 15118-15201 ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 08:51:43.931 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 08:51:44.004 15118-15126 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 08:51:44.005 15118-15126 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 08:51:44.066 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 135ms (LIGHTNING FAST)
2025-05-27 08:51:44.067 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 08:51:44.289 15118-15124 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 91KB AllocSpace bytes, 1(1028KB) LOS objects, 24% free, 11MB/15MB, paused 224us,67us total 185.029ms
2025-05-27 08:51:46.144 15118-15118 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:51:46.145 15118-15118 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:51:54.236 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 10169ms (LIGHTNING FAST)
2025-05-27 08:51:54.377 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-26.139517, 11.8973055]
2025-05-27 08:51:54.377 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 08:51:54.510 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 273ms (LIGHTNING FAST)
2025-05-27 08:51:54.510 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 08:51:54.510 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 08:51:54.510 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 V6 resources tracked: 2
2025-05-27 08:51:54.524 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 08:51:54.649 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 124ms (LIGHTNING FAST)
2025-05-27 08:51:54.649 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-27 08:51:54.943 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO inference: 294ms (LIGHTNING FAST)
2025-05-27 08:51:54.944 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-27 08:51:54.944 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-27 08:51:54.945 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-27 08:51:54.945 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed with zero memory leaks
2025-05-27 08:51:54.945 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-27 08:51:54.945 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 YOLO resources tracked: 0
2025-05-27 08:51:54.946 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #1 - Segmentation: 10593ms, Detection: 421ms
2025-05-27 08:51:54.946 15118-15201 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-27 08:51:54.947 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:54.947 15118-15201 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 08:51:54.947 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 08:51:54.947 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ Operation #1 completed - EXACT same results as Python!
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 11492ms
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 11492ms (target: <1000ms)
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 10593ms (target: <300ms)
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ YOLO performance below target. Detection: 421ms (target: <200ms)
2025-05-27 08:51:54.948 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 08:51:54.959 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 Operation #1 - Zero-leak cleanup completed
2025-05-27 08:51:54.961 15118-15201 ChessAI                 com.chessvision.app                  D  ✅ Operation #1 completed successfully!
2025-05-27 08:51:54.961 15118-15201 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:54.961 15118-15201 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:51:54.961 15118-15201 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 11492ms
2025-05-27 08:51:54.961 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 08:51:54.964 15118-15118 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:54.965 15118-15118 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:51:54.965 15118-15118 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 11492ms
2025-05-27 08:51:54.968 15118-15201 ChessAI                 com.chessvision.app                  D  🔍 Starting operation #2 with zero main thread impact...
2025-05-27 08:51:54.968 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 08:51:54.969 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #2 with zero main thread blocking...
2025-05-27 08:51:54.993 15118-15118 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
2025-05-27 08:51:54.994 15118-15201 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:51:54.995 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][0]
2025-05-27 08:51:54.995 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][1]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][2]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK QUEEN at [7][3]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KING at [7][4]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][5]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][6]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][7]
2025-05-27 08:51:54.996 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][0]
2025-05-27 08:51:54.997 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][1]
2025-05-27 08:51:54.997 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][2]
2025-05-27 08:51:54.997 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][3]
2025-05-27 08:51:54.997 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][4]
2025-05-27 08:51:54.997 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][5]
2025-05-27 08:51:54.997 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][6]
2025-05-27 08:51:54.998 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][7]
2025-05-27 08:51:54.999 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][0]
2025-05-27 08:51:54.999 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][1]
2025-05-27 08:51:54.999 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][2]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][3]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][4]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][5]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][6]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][7]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][0]
2025-05-27 08:51:55.000 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][1]
2025-05-27 08:51:55.001 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][2]
2025-05-27 08:51:55.001 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE QUEEN at [0][3]
2025-05-27 08:51:55.001 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KING at [0][4]
2025-05-27 08:51:55.001 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][5]
2025-05-27 08:51:55.001 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][6]
2025-05-27 08:51:55.001 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][7]
2025-05-27 08:51:55.002 15118-15118 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:51:55.223 15118-15201 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:51:55.226 15118-15201 ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 08:51:55.226 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 08:51:55.246 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 19ms (LIGHTNING FAST)
2025-05-27 08:51:55.246 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 08:51:55.340 15118-15124 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 157KB AllocSpace bytes, 8(11MB) LOS objects, 25% free, 12MB/16MB, paused 184us,49us total 116.146ms
2025-05-27 08:51:57.530 15118-15118 ChessBoardState         com.chessvision.app                  D  🎯 Updating board from FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:57.530 15118-15118 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:57.531 15118-15118 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:51:57.532 15118-15118 ChessBoardState         com.chessvision.app                  D  🎯 Board update complete, current FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:57.532 15118-15118 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:51:57.805 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 164 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:51:57.914 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2930ms; Flags=0, FrameTimelineVsyncId=19006819, IntendedVsync=356205109559576, Vsync=356205109559576, InputEventId=0, HandleInputStart=356205110656000, AnimationStart=356205110660923, PerformTraversalsStart=356205238306154, DrawStart=356207509853769, FrameDeadline=356205129559576, FrameInterval=356205110637231, FrameStartTime=16666667, SyncQueued=356207657111000, SyncStart=356207657259385, IssueDrawCommandsStart=356207658403385, SwapBuffers=356208031447692, FrameCompleted=356208040259385, DequeueBufferDuration=36000, QueueBufferDuration=4199077, GpuCompleted=356208040259385, SwapBuffersCompleted=356208036798385, DisplayPresentTime=1896607328712530511, 
2025-05-27 08:51:58.998 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3921ms; Flags=0, FrameTimelineVsyncId=19006821, IntendedVsync=356205191380413, Vsync=356207924713801, InputEventId=0, HandleInputStart=356207939892923, AnimationStart=356207939897538, PerformTraversalsStart=356209051205846, DrawStart=356209051448923, FrameDeadline=356205228047080, FrameInterval=356207939557769, FrameStartTime=16666667, SyncQueued=356209097367385, SyncStart=356209097606692, IssueDrawCommandsStart=356209098387000, SwapBuffers=356209103851077, FrameCompleted=356209112953846, DequeueBufferDuration=46538, QueueBufferDuration=1332308, GpuCompleted=356209112953846, SwapBuffersCompleted=356209106898769, DisplayPresentTime=1901110997060427440, 
2025-05-27 08:51:59.166 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 80 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:51:59.230 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1397ms; Flags=0, FrameTimelineVsyncId=19006823, IntendedVsync=356207958036800, Vsync=356209291370160, InputEventId=0, HandleInputStart=356209300983308, AnimationStart=356209300988923, PerformTraversalsStart=356209317005539, DrawStart=356209317223616, FrameDeadline=356207994703467, FrameInterval=356209300662769, FrameStartTime=16666667, SyncQueued=356209343092231, SyncStart=356209343292616, IssueDrawCommandsStart=356209343796308, SwapBuffers=356209347195077, FrameCompleted=356209356092000, DequeueBufferDuration=42462, QueueBufferDuration=1143308, GpuCompleted=356209356092000, SwapBuffersCompleted=356209349681154, DisplayPresentTime=1182494416469230787, 
2025-05-27 08:52:05.853 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 10607ms (LIGHTNING FAST)
2025-05-27 08:52:05.859 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-26.139517, 11.8973055]
2025-05-27 08:52:05.860 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 08:52:05.955 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 102ms (LIGHTNING FAST)
2025-05-27 08:52:05.955 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 08:52:05.955 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 08:52:05.955 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 V6 resources tracked: 2
2025-05-27 08:52:05.968 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 08:52:05.977 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 9ms (LIGHTNING FAST)
2025-05-27 08:52:05.977 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-27 08:52:06.088 15118-15124 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 482KB AllocSpace bytes, 6(9916KB) LOS objects, 25% free, 10MB/13MB, paused 179us,51us total 117.206ms
2025-05-27 08:52:06.194 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO inference: 217ms (LIGHTNING FAST)
2025-05-27 08:52:06.195 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-27 08:52:06.195 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-27 08:52:06.196 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-27 08:52:06.196 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed with zero memory leaks
2025-05-27 08:52:06.196 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-27 08:52:06.196 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 YOLO resources tracked: 0
2025-05-27 08:52:06.196 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #2 - Segmentation: 10742ms, Detection: 228ms
2025-05-27 08:52:06.196 15118-15201 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ Operation #2 completed - EXACT same results as Python!
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 11228ms
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 11228ms (target: <1000ms)
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 10742ms (target: <300ms)
2025-05-27 08:52:06.197 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ YOLO performance below target. Detection: 228ms (target: <200ms)
2025-05-27 08:52:06.198 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 08:52:06.208 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 Operation #2 - Zero-leak cleanup completed
2025-05-27 08:52:06.209 15118-15201 ChessAI                 com.chessvision.app                  D  ✅ Operation #2 completed successfully!
2025-05-27 08:52:06.209 15118-15201 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:06.209 15118-15201 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:52:06.209 15118-15201 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 11228ms
2025-05-27 08:52:06.209 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 08:52:06.211 15118-15118 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:06.211 15118-15118 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:52:06.211 15118-15118 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 11228ms
2025-05-27 08:52:39.752 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 44 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:52:40.776 15118-15118 CameraStateManager      com.chessvision.app                  W  ⚠️ Camera provider not available
2025-05-27 08:52:40.776 15118-15118 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 08:52:40.861 15118-15118 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 08:52:40.985 15118-15118 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 08:52:46.726 15118-15118 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:52:47.042 15118-15118 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:52:47.297 15118-15118 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 08:52:47.298 15118-15118 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 08:52:47.299 15118-15118 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 08:52:47.300 15118-15118 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 08:52:47.320 15118-15124 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 1650KB AllocSpace bytes, 5(4476KB) LOS objects, 25% free, 6065KB/8113KB, paused 386us,164us total 176.616ms
2025-05-27 08:52:47.508 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 49 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:52:47.520 15118-15118 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:52:47.520 15118-15118 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:52:47.521 15118-15118 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 08:52:47.522 15118-15118 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 08:52:47.526 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=969ms; Flags=0, FrameTimelineVsyncId=19007502, IntendedVsync=356256666376859, Vsync=356256666376859, InputEventId=0, HandleInputStart=356256667635388, AnimationStart=356256667641772, PerformTraversalsStart=356257448516311, DrawStart=356257573558157, FrameDeadline=356256686376859, FrameInterval=356256667601234, FrameStartTime=16666667, SyncQueued=356257618248311, SyncStart=356257618550157, IssueDrawCommandsStart=356257619326465, SwapBuffers=356257628651926, FrameCompleted=356257636606080, DequeueBufferDuration=108308, QueueBufferDuration=2793307, GpuCompleted=356257636606080, SwapBuffersCompleted=356257633475388, DisplayPresentTime=1585854212707718655, 
2025-05-27 08:52:47.545 15118-15132 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=843ms; Flags=0, FrameTimelineVsyncId=19007528, IntendedVsync=356256816376010, Vsync=356257633042693, InputEventId=0, HandleInputStart=356257642672003, AnimationStart=356257642678388, PerformTraversalsStart=356257642681849, DrawStart=356257648965541, FrameDeadline=356256853042677, FrameInterval=356257642356618, FrameStartTime=16666667, SyncQueued=356257649310388, SyncStart=356257649503618, IssueDrawCommandsStart=356257649826157, SwapBuffers=356257652485849, FrameCompleted=356257660240003, DequeueBufferDuration=40692, QueueBufferDuration=2285846, GpuCompleted=356257660240003, SwapBuffersCompleted=356257656604311, DisplayPresentTime=26459047248793304, 
2025-05-27 08:52:48.562 15118-15118 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:52:48.563 15118-15118 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:52:48.565 15118-15201 ChessAI                 com.chessvision.app                  D  🔍 Starting operation #3 with zero main thread impact...
2025-05-27 08:52:48.566 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 08:52:48.568 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #3 with zero main thread blocking...
2025-05-27 08:52:48.606 15118-15201 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:52:48.813 15118-15201 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:52:48.814 15118-15201 ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 08:52:48.815 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 08:52:48.828 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 13ms (LIGHTNING FAST)
2025-05-27 08:52:48.828 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 08:52:48.959 15118-15124 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 351KB AllocSpace bytes, 2(1048KB) LOS objects, 24% free, 11MB/15MB, paused 124us,44us total 145.323ms
2025-05-27 08:52:48.963 15118-15126 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 08:52:48.963 15118-15126 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 08:52:59.320 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 10492ms (LIGHTNING FAST)
2025-05-27 08:52:59.325 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-30.12541, 15.027585]
2025-05-27 08:52:59.325 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 08:52:59.409 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 89ms (LIGHTNING FAST)
2025-05-27 08:52:59.409 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 08:52:59.409 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 08:52:59.409 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 V6 resources tracked: 2
2025-05-27 08:52:59.422 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 08:52:59.432 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 9ms (LIGHTNING FAST)
2025-05-27 08:52:59.432 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-27 08:52:59.666 15118-15201 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO inference: 234ms (LIGHTNING FAST)
2025-05-27 08:52:59.667 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-27 08:52:59.667 15118-15201 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-27 08:52:59.668 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-27 08:52:59.668 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed with zero memory leaks
2025-05-27 08:52:59.668 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-27 08:52:59.668 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 YOLO resources tracked: 0
2025-05-27 08:52:59.668 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #3 - Segmentation: 10607ms, Detection: 246ms
2025-05-27 08:52:59.668 15118-15201 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-27 08:52:59.670 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:59.670 15118-15201 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 08:52:59.670 15118-15201 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 08:52:59.670 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  ✅ Operation #3 completed - EXACT same results as Python!
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 11102ms
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 11102ms (target: <1000ms)
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 10607ms (target: <300ms)
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  W  ⚠️ YOLO performance below target. Detection: 246ms (target: <200ms)
2025-05-27 08:52:59.671 15118-15201 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 08:52:59.682 15118-15201 ONNXChessAI             com.chessvision.app                  D  🧹 Operation #3 - Zero-leak cleanup completed
2025-05-27 08:52:59.684 15118-15201 ChessAI                 com.chessvision.app                  D  ✅ Operation #3 completed successfully!
2025-05-27 08:52:59.684 15118-15201 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:59.684 15118-15201 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:52:59.684 15118-15201 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 11102ms
2025-05-27 08:52:59.684 15118-15201 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 08:52:59.686 15118-15118 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:52:59.686 15118-15118 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:52:59.687 15118-15118 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 11102ms
2025-05-27 08:52:59.703 15118-15118 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][0]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][1]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][2]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK QUEEN at [7][3]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KING at [7][4]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][5]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][6]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][7]
2025-05-27 08:52:59.704 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][0]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][1]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][2]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][3]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][4]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][5]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][6]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][7]
2025-05-27 08:52:59.705 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][0]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][1]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][2]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][3]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][4]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][5]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][6]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][7]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][0]
2025-05-27 08:52:59.706 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][1]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][2]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE QUEEN at [0][3]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KING at [0][4]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][5]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][6]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][7]
2025-05-27 08:52:59.707 15118-15118 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:53:01.053 15118-15124 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 495KB AllocSpace bytes, 6(4440KB) LOS objects, 24% free, 7816KB/10MB, paused 145us,532us total 144.938ms
2025-05-27 08:53:01.151 15118-15118 ChessBoardState         com.chessvision.app                  D  🎯 Updating board from FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:53:01.151 15118-15118 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:53:01.152 15118-15118 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:53:01.152 15118-15118 ChessBoardState         com.chessvision.app                  D  🎯 Board update complete, current FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:53:01.152 15118-15118 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:53:01.254 15118-15211 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1544ms; Flags=0, FrameTimelineVsyncId=19007657, IntendedVsync=356269832518602, Vsync=356269832518602, InputEventId=0, HandleInputStart=356269834050773, AnimationStart=356269834055388, PerformTraversalsStart=356269902652619, DrawStart=356271154835081, FrameDeadline=356269852518602, FrameInterval=356269834035696, FrameStartTime=16666667, SyncQueued=356271279370696, SyncStart=356271279549465, IssueDrawCommandsStart=356271281727004, SwapBuffers=356271367993773, FrameCompleted=356271377135696, DequeueBufferDuration=56231, QueueBufferDuration=1814154, GpuCompleted=356271377135696, SwapBuffersCompleted=356271371070388, DisplayPresentTime=394915414321988985, 
2025-05-27 08:53:01.298 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 93 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:53:02.040 15118-15132 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2276ms; Flags=0, FrameTimelineVsyncId=19007659, IntendedVsync=356269880624108, Vsync=356271430624139, InputEventId=0, HandleInputStart=356271432703927, AnimationStart=356271432708004, PerformTraversalsStart=356272110427927, DrawStart=356272110718465, FrameDeadline=356269917290775, FrameInterval=356271432487619, FrameStartTime=16666667, SyncQueued=356272139640312, SyncStart=356272140221850, IssueDrawCommandsStart=356272141639081, SwapBuffers=356272148577465, FrameCompleted=356272157903927, DequeueBufferDuration=57308, QueueBufferDuration=2233231, GpuCompleted=356272157903927, SwapBuffersCompleted=356272157155927, DisplayPresentTime=418559672787469772, 
2025-05-27 08:53:02.101 15118-15118 Choreographer           com.chessvision.app                  I  Skipped 47 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:53:02.155 15118-15132 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=833ms; Flags=0, FrameTimelineVsyncId=19007665, IntendedVsync=356271447560055, Vsync=356272230893404, InputEventId=0, HandleInputStart=356272235793235, AnimationStart=356272235799619, PerformTraversalsStart=356272245724004, DrawStart=356272245899312, FrameDeadline=356271484226722, FrameInterval=356272235459388, FrameStartTime=16666667, SyncQueued=356272266563312, SyncStart=356272266982696, IssueDrawCommandsStart=356272267616235, SwapBuffers=356272271588235, FrameCompleted=356272281201158, DequeueBufferDuration=52615, QueueBufferDuration=1704847, GpuCompleted=356272281201158, SwapBuffersCompleted=356272275058158, DisplayPresentTime=1197682597482598557, 
