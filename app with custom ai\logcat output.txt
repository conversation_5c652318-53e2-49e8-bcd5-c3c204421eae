--------- beginning of main
---------------------------- PROCESS STARTED (12379) for package com.chessvision.app ----------------------------
2025-05-27 08:23:16.344 12379-12379 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/base.dm': No such file or directory
2025-05-27 08:23:16.344 12379-12379 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/base.dm': No such file or directory
2025-05-27 08:23:17.478 12379-12379 nativeloader            com.chessvision.app                  D  Configuring clns-4 for other apk /data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/lib/arm64:/data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.chessvision.app
2025-05-27 08:23:17.503 12379-12379 GraphicsEnvironment     com.chessvision.app                  V  ANGLE Developer option for 'com.chessvision.app' set to: 'default'
2025-05-27 08:23:17.503 12379-12379 GraphicsEnvironment     com.chessvision.app                  V  Neither updatable production driver nor prerelease driver is supported.
2025-05-27 08:23:17.509 12379-12379 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 08:23:17.511 12379-12379 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 08:23:17.540 12379-12379 libc                    com.chessvision.app                  W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-27 08:23:17.617 12379-12379 ChessVisionApp          com.chessvision.app                  D  Application initialized
2025-05-27 08:23:17.734 12379-12379 chessvision.ap          com.chessvision.app                  E  Invalid ID 0x00000000.
2025-05-27 08:23:18.039 12379-12379 XDR::VRT                com.chessvision.app                  I  sc is not valid!
2025-05-27 08:23:19.288 12379-12379 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-27 08:23:19.289 12379-12379 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 08:23:19.289 12379-12379 chessvision.ap          com.chessvision.app                  W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 08:23:19.290 12379-12379 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 08:23:19.290 12379-12379 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 08:23:19.422 12379-12379 Compatibil...geReporter com.chessvision.app                  D  Compat change id reported: 171228096; UID 10719; state: ENABLED
2025-05-27 08:23:19.928 12379-12379 BufferQueueConsumer     com.chessvision.app                  I  [](id:305b00000000,api:0,p:-1,c:12379) connect: controlledByApp=false
2025-05-27 08:23:19.932 12379-12379 BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0] constructor()
2025-05-27 08:23:19.951 12379-12413 hw-ProcessState         com.chessvision.app                  D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-27 08:23:19.976 12379-12413 BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0(BLAST Consumer)0](id:305b00000000,api:1,p:12379,c:12379) connect: api=1 producerControlledByApp=true
2025-05-27 08:23:19.987 12379-12419 ion                     com.chessvision.app                  E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-27 08:23:20.014 12379-12413 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-27 08:23:20.014 12379-12413 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-27 08:23:20.040 12379-12417 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2453ms; Flags=1, FrameTimelineVsyncId=18973957, IntendedVsync=354487693762589, Vsync=354488160429265, InputEventId=0, HandleInputStart=354488170185128, AnimationStart=354488170202282, PerformTraversalsStart=354488170641051, DrawStart=354490114219898, FrameDeadline=354487713762589, FrameInterval=354488170016513, FrameStartTime=16666667, SyncQueued=354490129165436, SyncStart=354490134337744, IssueDrawCommandsStart=354490135011898, SwapBuffers=354490147756436, FrameCompleted=354490152732205, DequeueBufferDuration=0, QueueBufferDuration=2331769, GpuCompleted=354490151875205, SwapBuffersCompleted=354490152732205, DisplayPresentTime=1899369992, 
2025-05-27 08:23:20.062 12379-12386 chessvision.ap          com.chessvision.app                  I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-27 08:23:20.140 12379-12379 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:23:20.140 12379-12379 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:23:20.141 12379-12379 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=null
2025-05-27 08:23:20.142 12379-12379 ImeFocusController      com.chessvision.app                  V  checkFocus: view=null next=DecorView@17fb6e3[MainActivity] force=true package=<none>
2025-05-27 08:23:20.355 12379-12428 ChessAI                 com.chessvision.app                  D  🚀 Initializing enterprise-grade Chess AI...
2025-05-27 08:23:20.355 12379-12428 ChessAI                 com.chessvision.app                  D  🎯 V6 Segmentation: EXACT same weights (Dice: 0.9391)
2025-05-27 08:23:20.355 12379-12428 ChessAI                 com.chessvision.app                  D  🎯 YOLO Detection: EXACT same weights (mAP50: 97.3%)
2025-05-27 08:23:20.355 12379-12428 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking guaranteed
2025-05-27 08:23:20.359 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 Initializing ONNX Chess AI with enterprise-grade resource management...
2025-05-27 08:23:20.359 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔬 V6 Model: EXACT same weights as Python (Dice: 0.9391)
2025-05-27 08:23:20.359 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model: EXACT same weights as Python (mAP50: 97.3%)
2025-05-27 08:23:20.361 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ V6 model already exists
2025-05-27 08:23:20.362 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO model already exists
2025-05-27 08:23:20.439 12379-12428 nativeloader            com.chessvision.app                  D  Load /data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/base.apk!/lib/arm64-v8a/libonnxruntime4j_jni.so using ns clns-4 from class loader (caller=/data/app/~~821sWxnzxbpZuXVHfYBpkw==/com.chessvision.app-6McsH78dxdohE1L0Zy84QA==/base.apk): ok
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔍 Hardware Profile Detected:
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  📱 Device: V2147 (vivo)
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔧 Hardware: mt6765
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  💾 Total RAM: 2GB (2800MB)
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  🧠 CPU Cores: 8
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 Helio P35: true
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎮 PowerVR GE8320: true
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  ⚡ Optimization Level: MEMORY_OPTIMIZED
2025-05-27 08:23:20.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔋 Low Memory Device: true
2025-05-27 08:23:20.516 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 MEMORY_OPTIMIZED mode: Helio P35 + 2.8GB RAM ultra-optimizations
2025-05-27 08:23:20.516 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎮 PowerVR GE8320 detected - using CPU-optimized path
2025-05-27 08:23:20.516 12379-12428 ONNXChessAI             com.chessvision.app                  D  💾 Memory pattern optimization disabled for 3GB RAM
2025-05-27 08:23:20.516 12379-12428 ONNXChessAI             com.chessvision.app                  D  ⚠️ CPU Memory Arena not available in this ONNX Runtime version
2025-05-27 08:23:20.685 12379-12428 libc                    com.chessvision.app                  W  Access denied finding property "ro.hardware.chipname"
2025-05-27 08:23:21.666 12379-12379 Choreographer           com.chessvision.app                  I  Skipped 78 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:23:21.699 12379-12417 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1320ms; Flags=0, FrameTimelineVsyncId=18974056, IntendedVsync=354490476456876, Vsync=354490476456876, InputEventId=0, HandleInputStart=354490490835821, AnimationStart=354490490840744, PerformTraversalsStart=354491186728205, DrawStart=354491186981128, FrameDeadline=354490496456876, FrameInterval=354490490810667, FrameStartTime=16666667, SyncQueued=354491791924205, SyncStart=354491792193359, IssueDrawCommandsStart=354491792635513, SwapBuffers=354491793649744, FrameCompleted=354491797396129, DequeueBufferDuration=46154, QueueBufferDuration=2384308, GpuCompleted=354491796791590, SwapBuffersCompleted=354491797396129, DisplayPresentTime=1899444776, 
2025-05-27 08:23:21.768 12379-12417 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1378ms; Flags=0, FrameTimelineVsyncId=18974059, IntendedVsync=354490493107107, Vsync=354491793107133, InputEventId=0, HandleInputStart=354491801180436, AnimationStart=354491801186359, PerformTraversalsStart=354491858660205, DrawStart=354491858886282, FrameDeadline=354490529773774, FrameInterval=354491800929436, FrameStartTime=16666667, SyncQueued=354491866872513, SyncStart=354491867140513, IssueDrawCommandsStart=354491867439821, SwapBuffers=354491868448129, FrameCompleted=354491871637052, DequeueBufferDuration=36000, QueueBufferDuration=2050462, GpuCompleted=354491871389052, SwapBuffersCompleted=354491871637052, DisplayPresentTime=1899681576, 
2025-05-27 08:23:23.018 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX Runtime sessions created successfully!
2025-05-27 08:23:23.018 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 V6 Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/v6_mobile.onnx
2025-05-27 08:23:23.018 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/yolo_mobile.onnx
2025-05-27 08:23:23.018 12379-12428 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade resource management active
2025-05-27 08:23:23.018 12379-12428 ChessAI                 com.chessvision.app                  D  ✅ Enterprise-grade Chess AI initialized successfully!
2025-05-27 08:23:23.018 12379-12428 ChessAI                 com.chessvision.app                  D  🚀 Ready for zero-latency processing
2025-05-27 08:23:24.019 12379-12440 ProfileInstaller        com.chessvision.app                  D  Installing profile for com.chessvision.app
2025-05-27 08:23:34.091 12379-12379 CameraStateManager      com.chessvision.app                  W  ⚠️ Camera provider not available
2025-05-27 08:23:34.091 12379-12379 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 08:23:34.219 12379-12379 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 08:23:34.275 12379-12450 CameraManagerGlobal     com.chessvision.app                  I  Connecting to camera service
2025-05-27 08:23:34.359 12379-12450 CameraRepository        com.chessvision.app                  D  Added camera: 0
2025-05-27 08:23:34.448 12379-12450 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 08:23:34.471 12379-12450 CameraRepository        com.chessvision.app                  D  Added camera: 1
2025-05-27 08:23:34.473 12379-12450 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 08:23:34.475 12379-12450 CameraValidator         com.chessvision.app                  D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-27 08:23:34.503 12379-12379 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:23:34.503 12379-12379 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:23:34.504 12379-12379 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 08:23:34.508 12379-12379 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 08:23:34.519 12379-12379 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 08:23:34.527 12379-12399 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=863ms; Flags=0, FrameTimelineVsyncId=18974319, IntendedVsync=354503774262547, Vsync=354503774262547, InputEventId=0, HandleInputStart=354503780358283, AnimationStart=354503780363822, PerformTraversalsStart=354504398790668, DrawStart=354504525586899, FrameDeadline=354503794262547, FrameInterval=354503780329668, FrameStartTime=16666667, SyncQueued=354504583562591, SyncStart=354504583815283, IssueDrawCommandsStart=354504584297975, SwapBuffers=354504632225129, FrameCompleted=354504637795206, DequeueBufferDuration=48923, QueueBufferDuration=898462, GpuCompleted=354504637795206, SwapBuffersCompleted=354504634276437, DisplayPresentTime=**********, 
--------- beginning of system
2025-05-27 08:23:48.478 12379-12379 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:23:48.637 12379-12379 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:23:48.798 12379-12379 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 08:23:48.798 12379-12379 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 08:23:48.799 12379-12379 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 08:23:48.799 12379-12379 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 08:23:48.980 12379-12379 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 08:23:48.980 12379-12379 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 08:23:48.980 12379-12379 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 08:23:48.981 12379-12379 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 08:23:50.132 12379-12379 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:23:50.133 12379-12379 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 08:23:50.138 12379-12428 ChessAI                 com.chessvision.app                  D  🔍 Starting operation #1 with zero main thread impact...
2025-05-27 08:23:50.138 12379-12428 ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 08:23:50.141 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #1 with enterprise-grade processing...
2025-05-27 08:23:50.174 12379-12428 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 08:23:50.457 12379-12428 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 08:23:50.458 12379-12428 ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 08:23:50.459 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 08:23:50.547 12379-12389 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 08:23:50.548 12379-12389 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 08:23:50.601 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 142ms (LIGHTNING FAST)
2025-05-27 08:23:50.601 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 08:24:04.514 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 13913ms (LIGHTNING FAST)
2025-05-27 08:24:04.655 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-30.029545, 14.933038]
2025-05-27 08:24:04.655 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 08:24:04.782 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 268ms (LIGHTNING FAST)
2025-05-27 08:24:04.783 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 08:24:04.783 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 08:24:04.796 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 08:24:04.922 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 126ms (LIGHTNING FAST)
2025-05-27 08:24:04.922 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-27 08:24:05.238 12379-12428 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO inference: 316ms (LIGHTNING FAST)
2025-05-27 08:24:05.239 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-27 08:24:05.239 12379-12428 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-27 08:24:05.240 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-27 08:24:05.240 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed with zero memory leaks
2025-05-27 08:24:05.240 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-27 08:24:05.240 12379-12428 ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #1 - Segmentation: 14337ms, Detection: 444ms
2025-05-27 08:24:05.240 12379-12428 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  ✅ Operation #1 completed - EXACT same results as Python!
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 15101ms
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 15101ms (target: <1000ms)
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 14337ms (target: <300ms)
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  W  ⚠️ YOLO performance below target. Detection: 444ms (target: <200ms)
2025-05-27 08:24:05.242 12379-12428 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 08:24:05.252 12379-12428 ONNXChessAI             com.chessvision.app                  D  🧹 Operation #1 resources cleaned up
2025-05-27 08:24:05.253 12379-12428 ChessAI                 com.chessvision.app                  D  ✅ Operation #1 completed successfully!
2025-05-27 08:24:05.253 12379-12428 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:05.253 12379-12428 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:24:05.253 12379-12428 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 15101ms
2025-05-27 08:24:05.253 12379-12428 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 08:24:05.260 12379-12379 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:05.260 12379-12379 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 08:24:05.261 12379-12379 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 15101ms
2025-05-27 08:24:05.319 12379-12379 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
2025-05-27 08:24:05.323 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][0]
2025-05-27 08:24:05.324 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][1]
2025-05-27 08:24:05.324 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][2]
2025-05-27 08:24:05.325 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK QUEEN at [7][3]
2025-05-27 08:24:05.325 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KING at [7][4]
2025-05-27 08:24:05.326 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][5]
2025-05-27 08:24:05.326 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][6]
2025-05-27 08:24:05.327 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][7]
2025-05-27 08:24:05.327 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][0]
2025-05-27 08:24:05.327 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][1]
2025-05-27 08:24:05.328 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][2]
2025-05-27 08:24:05.329 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][3]
2025-05-27 08:24:05.329 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][4]
2025-05-27 08:24:05.330 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][5]
2025-05-27 08:24:05.330 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][6]
2025-05-27 08:24:05.331 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][7]
2025-05-27 08:24:05.333 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][0]
2025-05-27 08:24:05.334 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][1]
2025-05-27 08:24:05.335 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][2]
2025-05-27 08:24:05.335 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][3]
2025-05-27 08:24:05.336 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][4]
2025-05-27 08:24:05.336 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][5]
2025-05-27 08:24:05.337 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][6]
2025-05-27 08:24:05.337 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][7]
2025-05-27 08:24:05.338 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][0]
2025-05-27 08:24:05.339 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][1]
2025-05-27 08:24:05.339 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][2]
2025-05-27 08:24:05.340 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE QUEEN at [0][3]
2025-05-27 08:24:05.340 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KING at [0][4]
2025-05-27 08:24:05.341 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][5]
2025-05-27 08:24:05.341 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][6]
2025-05-27 08:24:05.341 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][7]
2025-05-27 08:24:05.342 12379-12379 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:24:07.352 12379-12379 ChessBoardState         com.chessvision.app                  D  🎯 Updating board from FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:07.352 12379-12379 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:07.353 12379-12379 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:24:07.353 12379-12379 ChessBoardState         com.chessvision.app                  D  🎯 Board update complete, current FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:07.353 12379-12379 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 08:24:07.537 12379-12379 Choreographer           com.chessvision.app                  I  Skipped 128 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:24:07.709 12379-12394 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2418ms; Flags=0, FrameTimelineVsyncId=18975280, IntendedVsync=354535402413698, Vsync=354535402413698, InputEventId=0, HandleInputStart=354535406993670, AnimationStart=354535407002900, PerformTraversalsStart=354535582354054, DrawStart=354537353470670, FrameDeadline=354535422413698, FrameInterval=354535406932746, FrameStartTime=16666667, SyncQueued=354537478819824, SyncStart=354537479465977, IssueDrawCommandsStart=354537481882977, SwapBuffers=354537812617670, FrameCompleted=354537821590362, DequeueBufferDuration=66462, QueueBufferDuration=1906231, GpuCompleted=354537821590362, SwapBuffersCompleted=354537815472208, DisplayPresentTime=543141887864, 
2025-05-27 08:24:08.511 12379-12394 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3077ms; Flags=0, FrameTimelineVsyncId=18975282, IntendedVsync=354535535502975, Vsync=354537668836351, InputEventId=0, HandleInputStart=354537672497439, AnimationStart=354537672502285, PerformTraversalsStart=354538564417747, DrawStart=354538564623516, FrameDeadline=354535572169642, FrameInterval=354537672227593, FrameStartTime=16666667, SyncQueued=354538600270285, SyncStart=354538600651208, IssueDrawCommandsStart=354538601302593, SwapBuffers=354538604106054, FrameCompleted=354538612990901, DequeueBufferDuration=33539, QueueBufferDuration=1755077, GpuCompleted=354538612990901, SwapBuffersCompleted=354538606934285, DisplayPresentTime=1904965472, 
2025-05-27 08:24:08.582 12379-12379 Choreographer           com.chessvision.app                  I  Skipped 61 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:24:08.657 12379-12394 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1082ms; Flags=0, FrameTimelineVsyncId=18975289, IntendedVsync=354537685503022, Vsync=354538702169709, InputEventId=0, HandleInputStart=354538717083824, AnimationStart=354538717088131, PerformTraversalsStart=354538728664439, DrawStart=354538728841362, FrameDeadline=354537722169689, FrameInterval=354538716863901, FrameStartTime=16666667, SyncQueued=354538752869362, SyncStart=354538753843901, IssueDrawCommandsStart=354538754845054, SwapBuffers=354538759888747, FrameCompleted=354538769386670, DequeueBufferDuration=59615, QueueBufferDuration=1946538, GpuCompleted=354538769386670, SwapBuffersCompleted=354538763434285, DisplayPresentTime=1904490312, 
2025-05-27 08:24:19.790 12379-12387 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 2146KB AllocSpace bytes, 8(11MB) LOS objects, 24% free, 6297KB/8396KB, paused 364us,40us total 121.821ms
2025-05-27 08:24:20.166 12379-12379 Choreographer           com.chessvision.app                  I  Skipped 50 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:24:23.657 12379-12379 ChessBoardState         com.chessvision.app                  D  🔄 Loading FEN: rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1
2025-05-27 08:24:23.659 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][0]
2025-05-27 08:24:23.660 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][1]
2025-05-27 08:24:23.660 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][2]
2025-05-27 08:24:23.660 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK QUEEN at [7][3]
2025-05-27 08:24:23.660 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KING at [7][4]
2025-05-27 08:24:23.662 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK BISHOP at [7][5]
2025-05-27 08:24:23.662 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK KNIGHT at [7][6]
2025-05-27 08:24:23.662 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK ROOK at [7][7]
2025-05-27 08:24:23.662 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][0]
2025-05-27 08:24:23.662 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][1]
2025-05-27 08:24:23.663 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][2]
2025-05-27 08:24:23.663 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][3]
2025-05-27 08:24:23.663 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][4]
2025-05-27 08:24:23.663 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][5]
2025-05-27 08:24:23.663 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][6]
2025-05-27 08:24:23.664 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed BLACK PAWN at [6][7]
2025-05-27 08:24:23.664 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][0]
2025-05-27 08:24:23.664 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][1]
2025-05-27 08:24:23.664 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][2]
2025-05-27 08:24:23.664 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][3]
2025-05-27 08:24:23.664 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][4]
2025-05-27 08:24:23.665 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][5]
2025-05-27 08:24:23.665 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][6]
2025-05-27 08:24:23.665 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE PAWN at [1][7]
2025-05-27 08:24:23.665 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][0]
2025-05-27 08:24:23.665 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][1]
2025-05-27 08:24:23.665 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][2]
2025-05-27 08:24:23.666 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE QUEEN at [0][3]
2025-05-27 08:24:23.666 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KING at [0][4]
2025-05-27 08:24:23.666 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE BISHOP at [0][5]
2025-05-27 08:24:23.666 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE KNIGHT at [0][6]
2025-05-27 08:24:23.666 12379-12379 ChessBoardState         com.chessvision.app                  D  🔧 Placed WHITE ROOK at [0][7]
2025-05-27 08:24:23.667 12379-12379 ChessBoardState         com.chessvision.app                  D  ✅ FEN loaded successfully, board updated
2025-05-27 08:24:25.088 12379-12387 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 356KB AllocSpace bytes, 0(0B) LOS objects, 24% free, 8061KB/10MB, paused 334us,95us total 177.729ms
2025-05-27 08:24:25.458 12379-12399 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1820ms; Flags=0, FrameTimelineVsyncId=18975827, IntendedVsync=354553749613214, Vsync=354553782946548, InputEventId=694101855, HandleInputStart=354553785238594, AnimationStart=354553785305748, PerformTraversalsStart=354553906473132, DrawStart=354555342706902, FrameDeadline=354553769613214, FrameInterval=354553785217440, FrameStartTime=16666667, SyncQueued=354555477074671, SyncStart=354555477510671, IssueDrawCommandsStart=354555480930363, SwapBuffers=354555561210902, FrameCompleted=354555570172209, DequeueBufferDuration=84308, QueueBufferDuration=1566846, GpuCompleted=354555570172209, SwapBuffersCompleted=354555563765055, DisplayPresentTime=1898822512, 
2025-05-27 08:24:25.501 12379-12379 Choreographer           com.chessvision.app                  I  Skipped 107 frames!  The application may be doing too much work on its main thread.
2025-05-27 08:24:26.944 12379-12379 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 4,1
2025-05-27 08:24:27.646 12379-12394 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=719ms; Flags=0, FrameTimelineVsyncId=18975855, IntendedVsync=354557048615084, Vsync=354557048615084, InputEventId=297790944, HandleInputStart=354557049657748, AnimationStart=354557085954055, PerformTraversalsStart=354557501109132, DrawStart=354557501491209, FrameDeadline=354557068615084, FrameInterval=354557049629671, FrameStartTime=16666667, SyncQueued=354557567648440, SyncStart=354557568115825, IssueDrawCommandsStart=354557569713363, SwapBuffers=354557759879902, FrameCompleted=354557768260209, DequeueBufferDuration=56846, QueueBufferDuration=1279385, GpuCompleted=354557768260209, SwapBuffersCompleted=354557762381440, DisplayPresentTime=1899075520, 
2025-05-27 08:24:27.728 12379-12399 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=765ms; Flags=0, FrameTimelineVsyncId=18975857, IntendedVsync=354557081894404, Vsync=354557565227747, InputEventId=134772636, HandleInputStart=354557572353748, AnimationStart=354557648680209, PerformTraversalsStart=354557787809979, DrawStart=354557788115286, FrameDeadline=354557118561071, FrameInterval=354557572313440, FrameStartTime=16666667, SyncQueued=354557833051286, SyncStart=354557833209748, IssueDrawCommandsStart=354557834067825, SwapBuffers=354557838429748, FrameCompleted=354557847961363, DequeueBufferDuration=47077, QueueBufferDuration=1118769, GpuCompleted=354557847961363, SwapBuffersCompleted=354557840626056, DisplayPresentTime=1900176104, 
