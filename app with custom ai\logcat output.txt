--------- beginning of main
---------------------------- PROCESS STARTED (32041) for package com.chessvision.app ----------------------------
2025-05-27 00:44:03.757 32041-32041 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/base.dm': No such file or directory
2025-05-27 00:44:03.757 32041-32041 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/base.dm': No such file or directory
2025-05-27 00:44:05.180 32041-32041 nativeloader            com.chessvision.app                  D  Configuring clns-4 for other apk /data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/lib/arm64:/data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.chessvision.app
2025-05-27 00:44:05.218 32041-32041 GraphicsEnvironment     com.chessvision.app                  V  ANGLE Developer option for 'com.chessvision.app' set to: 'default'
2025-05-27 00:44:05.219 32041-32041 GraphicsEnvironment     com.chessvision.app                  V  Neither updatable production driver nor prerelease driver is supported.
2025-05-27 00:44:05.228 32041-32041 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 00:44:05.232 32041-32041 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 00:44:05.271 32041-32041 libc                    com.chessvision.app                  W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-27 00:44:05.392 32041-32041 ChessVisionApp          com.chessvision.app                  D  Application initialized
2025-05-27 00:44:05.556 32041-32041 chessvision.ap          com.chessvision.app                  E  Invalid ID 0x00000000.
2025-05-27 00:44:05.990 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 42 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:05.994 32041-32041 XDR::VRT                com.chessvision.app                  I  sc is not valid!
2025-05-27 00:44:07.811 32041-32041 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-27 00:44:07.812 32041-32041 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 00:44:07.812 32041-32041 chessvision.ap          com.chessvision.app                  W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 00:44:07.812 32041-32041 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 00:44:07.813 32041-32041 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 00:44:08.011 32041-32041 Compatibil...geReporter com.chessvision.app                  D  Compat change id reported: 171228096; UID 10718; state: ENABLED
2025-05-27 00:44:08.333 32041-32048 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 423KB AllocSpace bytes, 0(0B) LOS objects, 36% free, 3637KB/5685KB, paused 1.115ms,64us total 116.678ms
2025-05-27 00:44:08.713 32041-32041 BufferQueueConsumer     com.chessvision.app                  I  [](id:7d2900000000,api:0,p:-1,c:32041) connect: controlledByApp=false
2025-05-27 00:44:08.722 32041-32041 BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0] constructor()
2025-05-27 00:44:08.751 32041-32060 hw-ProcessState         com.chessvision.app                  D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-27 00:44:08.791 32041-32060 BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0(BLAST Consumer)0](id:7d2900000000,api:1,p:32041,c:32041) connect: api=1 producerControlledByApp=true
2025-05-27 00:44:08.805 32041-32067 ion                     com.chessvision.app                  E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-27 00:44:08.839 32041-32060 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-27 00:44:08.839 32041-32060 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-27 00:44:08.856 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3557ms; Flags=1, FrameTimelineVsyncId=18456618, IntendedVsync=333772762475346, Vsync=333773462475360, InputEventId=0, HandleInputStart=333773471420894, AnimationStart=333773471450817, PerformTraversalsStart=333773472296971, DrawStart=333776274491432, FrameDeadline=333772782475346, FrameInterval=333773470766740, FrameStartTime=16666667, SyncQueued=333776293093509, SyncStart=333776298268894, IssueDrawCommandsStart=333776300103971, SwapBuffers=333776319349278, FrameCompleted=333776325444048, DequeueBufferDuration=0, QueueBufferDuration=2604308, GpuCompleted=333776324290740, SwapBuffersCompleted=333776325444048, DisplayPresentTime=1898757024, 
2025-05-27 00:44:08.977 32041-32041 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 00:44:08.977 32041-32041 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 00:44:08.977 32041-32041 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=null
2025-05-27 00:44:08.978 32041-32041 ImeFocusController      com.chessvision.app                  V  checkFocus: view=null next=DecorView@17fb6e3[MainActivity] force=true package=<none>
2025-05-27 00:44:09.145 32041-32046 chessvision.ap          com.chessvision.app                  I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-27 00:44:09.181 32041-32077 ChessAI                 com.chessvision.app                  D  🚀 Initializing enterprise-grade Chess AI...
2025-05-27 00:44:09.182 32041-32077 ChessAI                 com.chessvision.app                  D  🎯 V6 Segmentation: EXACT same weights (Dice: 0.9391)
2025-05-27 00:44:09.182 32041-32077 ChessAI                 com.chessvision.app                  D  🎯 YOLO Detection: EXACT same weights (mAP50: 97.3%)
2025-05-27 00:44:09.182 32041-32077 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking guaranteed
2025-05-27 00:44:09.185 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 Initializing ONNX Chess AI with enterprise-grade resource management...
2025-05-27 00:44:09.186 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔬 V6 Model: EXACT same weights as Python (Dice: 0.9391)
2025-05-27 00:44:09.186 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model: EXACT same weights as Python (mAP50: 97.3%)
2025-05-27 00:44:09.188 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ V6 model already exists
2025-05-27 00:44:09.188 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO model already exists
2025-05-27 00:44:09.296 32041-32077 nativeloader            com.chessvision.app                  D  Load /data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/base.apk!/lib/arm64-v8a/libonnxruntime4j_jni.so using ns clns-4 from class loader (caller=/data/app/~~V14W5u9sEIG-OlrpZpaqcA==/com.chessvision.app-cZgU1A6P1piGC3mevh7Zew==/base.apk): ok
2025-05-27 00:44:09.376 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 NNAPI GPU acceleration enabled!
2025-05-27 00:44:09.541 32041-32077 Manager                 com.chessvision.app                  I  DeviceManager::DeviceManager
2025-05-27 00:44:09.542 32041-32077 Manager                 com.chessvision.app                  I  findAvailableDevices
2025-05-27 00:44:09.553 32041-32077 libc                    com.chessvision.app                  W  Access denied finding property "ro.hardware.chipname"
2025-05-27 00:44:10.605 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 84 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:10.630 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1437ms; Flags=0, FrameTimelineVsyncId=18456712, IntendedVsync=333776645134580, Vsync=333776661801247, InputEventId=0, HandleInputStart=333776664804586, AnimationStart=333776664810125, PerformTraversalsStart=333777434360278, DrawStart=333777434646432, FrameDeadline=333776681801247, FrameInterval=333776664774509, FrameStartTime=16666667, SyncQueued=333778077304586, SyncStart=333778077581586, IssueDrawCommandsStart=333778078022509, SwapBuffers=333778079169278, FrameCompleted=333778082948663, DequeueBufferDuration=48616, QueueBufferDuration=2255153, GpuCompleted=333778082713817, SwapBuffersCompleted=333778082948663, DisplayPresentTime=1898679416, 
2025-05-27 00:44:10.692 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1476ms; Flags=0, FrameTimelineVsyncId=18456718, IntendedVsync=333776678444659, Vsync=333778078444687, InputEventId=0, HandleInputStart=333778086193202, AnimationStart=333778086197509, PerformTraversalsStart=333778141229279, DrawStart=333778141505202, FrameDeadline=333776715111326, FrameInterval=333778085974125, FrameStartTime=16666667, SyncQueued=333778150106125, SyncStart=333778150259048, IssueDrawCommandsStart=333778150634740, SwapBuffers=333778152057279, FrameCompleted=333778155288586, DequeueBufferDuration=33615, QueueBufferDuration=1719770, GpuCompleted=333778155058509, SwapBuffersCompleted=333778155288586, DisplayPresentTime=**********, 
2025-05-27 00:44:11.921 32041-32077 onnxruntime             com.chessvision.app                  W   [W:onnxruntime:ort-java, nnapi_execution_provider.cc:225 GetCapability] NnapiExecutionProvider::GetCapability, number of partitions supported by NNAPI: 12 number of nodes in the graph: 320 number of nodes supported by NNAPI: 309
2025-05-27 00:44:11.971 32041-32087 ProfileInstaller        com.chessvision.app                  D  Installing profile for com.chessvision.app
2025-05-27 00:44:12.251 32041-32077 onnxruntime             com.chessvision.app                  W   [W:onnxruntime:ort-java, nnapi_execution_provider.cc:225 GetCapability] NnapiExecutionProvider::GetCapability, number of partitions supported by NNAPI: 12 number of nodes in the graph: 328 number of nodes supported by NNAPI: 317
2025-05-27 00:44:12.265 32041-32077 TypeManager             com.chessvision.app                  I  Failed to read /vendor/etc/nnapi_extensions_app_allowlist ; No app allowlisted for vendor extensions use.
2025-05-27 00:44:12.268 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.275 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.286 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.309 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.340 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.345 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.361 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.368 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.380 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.405 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.433 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.436 32041-32077 ExecutionPlan           com.chessvision.app                  I  ExecutionPlan::SimpleBody::finish: compilation finished successfully on nnapi-reference
2025-05-27 00:44:12.452 32041-32077 onnxruntime             com.chessvision.app                  W   [W:onnxruntime:, session_state.cc:1162 VerifyEachNodeIsAssignedToAnEp] Some nodes were not assigned to the preferred execution providers which may or may not have an negative impact on performance. e.g. ORT explicitly assigns shape related ops to CPU to improve perf.
2025-05-27 00:44:12.452 32041-32077 onnxruntime             com.chessvision.app                  W   [W:onnxruntime:, session_state.cc:1164 VerifyEachNodeIsAssignedToAnEp] Rerunning with verbose output on a non-minimal build will show node assignments.
2025-05-27 00:44:12.485 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX Runtime sessions created successfully!
2025-05-27 00:44:12.485 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 V6 Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/v6_mobile.onnx
2025-05-27 00:44:12.485 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/yolo_mobile.onnx
2025-05-27 00:44:12.485 32041-32077 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade resource management active
2025-05-27 00:44:12.485 32041-32077 ChessAI                 com.chessvision.app                  D  ✅ Enterprise-grade Chess AI initialized successfully!
2025-05-27 00:44:12.486 32041-32077 ChessAI                 com.chessvision.app                  D  🚀 Ready for zero-latency processing
2025-05-27 00:44:13.948 32041-32048 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 784KB AllocSpace bytes, 0(0B) LOS objects, 28% free, 5101KB/7149KB, paused 438us,52us total 106.221ms
2025-05-27 00:44:15.169 32041-32048 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 258KB AllocSpace bytes, 3(152KB) LOS objects, 24% free, 6222KB/8297KB, paused 126us,119us total 140.583ms
2025-05-27 00:44:16.421 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 152 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:16.603 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2855ms; Flags=0, FrameTimelineVsyncId=18456767, IntendedVsync=333781210786661, Vsync=333781210786661, InputEventId=905088873, HandleInputStart=333781223094125, AnimationStart=333781223098202, PerformTraversalsStart=333781448933894, DrawStart=333783551071356, FrameDeadline=333781230786661, FrameInterval=333781223076663, FrameStartTime=16666667, SyncQueued=333783696293817, SyncStart=333783696830433, IssueDrawCommandsStart=333783699695125, SwapBuffers=333784057855663, FrameCompleted=333784066849817, DequeueBufferDuration=58769, QueueBufferDuration=1275307, GpuCompleted=333784066849817, SwapBuffersCompleted=333784059872894, DisplayPresentTime=1899477368, 
2025-05-27 00:44:16.998 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 34 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:20.136 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 63 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:21.175 32041-32041 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 00:44:21.312 32041-32041 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 00:44:21.382 32041-32098 CameraManagerGlobal     com.chessvision.app                  I  Connecting to camera service
2025-05-27 00:44:21.489 32041-32098 CameraRepository        com.chessvision.app                  D  Added camera: 0
2025-05-27 00:44:21.574 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:21.589 32041-32098 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 00:44:21.609 32041-32098 CameraRepository        com.chessvision.app                  D  Added camera: 1
2025-05-27 00:44:21.612 32041-32098 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 00:44:21.614 32041-32098 CameraValidator         com.chessvision.app                  D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-27 00:44:21.619 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
--------- beginning of system
2025-05-27 00:44:34.732 32041-32048 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 2478KB AllocSpace bytes, 1(164KB) LOS objects, 26% free, 5607KB/7655KB, paused 272us,41us total 125.642ms
2025-05-27 00:44:34.822 32041-32041 skia                    com.chessvision.app                  D  libjpeg error 116 <Corrupt JPEG data: 130816 extraneous bytes before marker 0xdb> from output_message
2025-05-27 00:44:34.823 32041-32041 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 00:44:34.986 32041-32041 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 00:44:35.179 32041-32041 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 00:44:35.179 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 00:44:35.179 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 00:44:35.179 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 00:44:35.317 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 34 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:35.362 32041-32041 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 00:44:35.362 32041-32041 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 00:44:35.363 32041-32041 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 00:44:35.364 32041-32041 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 00:44:35.369 32041-32065 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=802ms; Flags=0, FrameTimelineVsyncId=18457934, IntendedVsync=333802040871359, Vsync=333802040871359, InputEventId=0, HandleInputStart=333802046503665, AnimationStart=333802046514741, PerformTraversalsStart=333802666129588, DrawStart=333802748181280, FrameDeadline=333802060871359, FrameInterval=333802046453818, FrameStartTime=16666667, SyncQueued=333802782866203, SyncStart=333802783068357, IssueDrawCommandsStart=333802783566742, SwapBuffers=333802835131665, FrameCompleted=333802843557818, DequeueBufferDuration=70692, QueueBufferDuration=2219385, GpuCompleted=333802843557818, SwapBuffersCompleted=333802840847049, DisplayPresentTime=**********, 
2025-05-27 00:44:38.632 32041-32041 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 00:44:38.710 32041-32041 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 00:44:38.835 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 00:44:40.414 32041-32041 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 00:44:40.414 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 00:44:40.414 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 00:44:40.414 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 00:44:40.973 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:40.984 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=714ms; Flags=0, FrameTimelineVsyncId=18458239, IntendedVsync=333807739889611, Vsync=333807739889611, InputEventId=18541105, HandleInputStart=333807754711973, AnimationStart=333807754717896, PerformTraversalsStart=333807903627126, DrawStart=333808400233588, FrameDeadline=333807759889611, FrameInterval=333807754683973, FrameStartTime=16666667, SyncQueued=333808441414126, SyncStart=333808441664050, IssueDrawCommandsStart=333808442174819, SwapBuffers=333808449206434, FrameCompleted=333808454158203, DequeueBufferDuration=28692, QueueBufferDuration=1745539, GpuCompleted=333808454158203, SwapBuffersCompleted=333808452203050, DisplayPresentTime=**********, 
2025-05-27 00:44:42.183 32041-32041 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 00:44:42.288 32041-32041 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 00:44:42.450 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 00:44:53.716 32041-32041 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 00:44:53.894 32041-32041 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 00:44:54.052 32041-32041 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 00:44:54.052 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 00:44:54.052 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 00:44:54.052 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 00:44:54.163 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:44:54.173 32041-32041 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 00:44:54.173 32041-32041 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 00:44:54.173 32041-32041 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 00:44:54.174 32041-32041 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 00:44:55.478 32041-32041 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 00:44:55.479 32041-32041 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 00:44:55.484 32041-32077 ChessAI                 com.chessvision.app                  D  🔍 Starting operation #1 with zero main thread impact...
2025-05-27 00:44:55.484 32041-32077 ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 00:44:55.488 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #1 with enterprise-grade processing...
2025-05-27 00:44:55.525 32041-32077 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 00:44:55.783 32041-32077 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 00:44:55.786 32041-32077 ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 00:44:55.786 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 00:44:55.853 32041-32050 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 00:44:55.854 32041-32050 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 00:44:55.928 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 142ms (LIGHTNING FAST)
2025-05-27 00:44:55.929 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 00:45:10.580 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 14651ms (LIGHTNING FAST)
2025-05-27 00:45:10.729 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-30.029545, 14.933038]
2025-05-27 00:45:10.729 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 00:45:10.867 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 286ms (LIGHTNING FAST)
2025-05-27 00:45:10.867 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 00:45:10.867 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 00:45:10.881 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 00:45:11.011 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 129ms (LIGHTNING FAST)
2025-05-27 00:45:11.011 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-27 00:45:12.339 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO inference: 1327ms (LIGHTNING FAST)
2025-05-27 00:45:12.340 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-27 00:45:12.341 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-27 00:45:12.343 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-27 00:45:12.343 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed with zero memory leaks
2025-05-27 00:45:12.343 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-27 00:45:12.344 32041-32077 ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #1 - Segmentation: 15095ms, Detection: 1463ms
2025-05-27 00:45:12.345 32041-32077 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-27 00:45:12.348 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:45:12.349 32041-32077 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 00:45:12.349 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 00:45:12.350 32041-32077 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 00:45:12.350 32041-32077 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 00:45:12.350 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 00:45:12.350 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 00:45:12.352 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ Operation #1 completed - EXACT same results as Python!
2025-05-27 00:45:12.353 32041-32077 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:45:12.353 32041-32077 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 16863ms
2025-05-27 00:45:12.354 32041-32077 ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 16863ms (target: <1000ms)
2025-05-27 00:45:12.354 32041-32077 ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 15095ms (target: <300ms)
2025-05-27 00:45:12.354 32041-32077 ONNXChessAI             com.chessvision.app                  W  ⚠️ YOLO performance below target. Detection: 1463ms (target: <200ms)
2025-05-27 00:45:12.355 32041-32077 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 00:45:12.380 32041-32077 ONNXChessAI             com.chessvision.app                  D  🧹 Operation #1 resources cleaned up
2025-05-27 00:45:12.381 32041-32077 ChessAI                 com.chessvision.app                  D  ✅ Operation #1 completed successfully!
2025-05-27 00:45:12.381 32041-32077 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:45:12.382 32041-32077 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 00:45:12.382 32041-32077 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 16863ms
2025-05-27 00:45:12.382 32041-32077 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 00:45:12.386 32041-32041 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:45:12.386 32041-32041 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 00:45:12.387 32041-32041 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 16863ms
2025-05-27 00:45:13.935 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:45:14.028 32041-32065 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1616ms; Flags=0, FrameTimelineVsyncId=18459271, IntendedVsync=333839884583376, Vsync=333839884583376, InputEventId=0, HandleInputStart=333839886032667, AnimationStart=333839886042436, PerformTraversalsStart=333839997264667, DrawStart=333841297765590, FrameDeadline=333839904583376, FrameInterval=333839885993898, FrameStartTime=16666667, SyncQueued=333841407821205, SyncStart=333841408456052, IssueDrawCommandsStart=333841410639359, SwapBuffers=333841493036898, FrameCompleted=333841501688667, DequeueBufferDuration=59154, QueueBufferDuration=1254154, GpuCompleted=333841501688667, SwapBuffersCompleted=333841495280975, DisplayPresentTime=0, 
2025-05-27 00:45:14.091 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 96 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:45:14.897 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2386ms; Flags=0, FrameTimelineVsyncId=18459273, IntendedVsync=333839967771541, Vsync=333841567771573, InputEventId=0, HandleInputStart=333841573860975, AnimationStart=333841573872590, PerformTraversalsStart=333842304119128, DrawStart=333842304354128, FrameDeadline=333840004438208, FrameInterval=333841571844590, FrameStartTime=16666667, SyncQueued=333842335651128, SyncStart=333842337819359, IssueDrawCommandsStart=333842339946205, SwapBuffers=333842346600359, FrameCompleted=333842356531052, DequeueBufferDuration=69923, QueueBufferDuration=2649923, GpuCompleted=333842356531052, SwapBuffersCompleted=333842352424590, DisplayPresentTime=0, 
2025-05-27 00:45:14.953 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 50 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:45:15.015 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=903ms; Flags=0, FrameTimelineVsyncId=18459281, IntendedVsync=333841584556998, Vsync=333842417890348, InputEventId=0, HandleInputStart=333842434214744, AnimationStart=333842434219128, PerformTraversalsStart=333842445648359, DrawStart=333842445826436, FrameDeadline=333841621223665, FrameInterval=333842433931975, FrameStartTime=16666667, SyncQueued=333842467939436, SyncStart=333842468200282, IssueDrawCommandsStart=333842469002821, SwapBuffers=333842478507436, FrameCompleted=333842487912359, DequeueBufferDuration=63615, QueueBufferDuration=2299077, GpuCompleted=333842487912359, SwapBuffersCompleted=333842483397898, DisplayPresentTime=0, 
2025-05-27 00:47:04.524 32041-32048 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 2171KB AllocSpace bytes, 8(11MB) LOS objects, 24% free, 6325KB/8433KB, paused 158us,261us total 129.868ms
2025-05-27 00:47:04.677 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 45 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:47:07.819 32041-32041 Compatibil...geReporter com.chessvision.app                  D  Compat change id reported: 147798919; UID 10718; state: ENABLED
2025-05-27 00:47:51.099 32041-32041 CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 00:47:51.161 32041-32041 CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 00:47:51.259 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 00:48:08.013 32041-32041 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 00:48:08.216 32041-32041 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 00:48:08.378 32041-32041 CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 00:48:08.378 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 00:48:08.379 32041-32041 CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 00:48:08.379 32041-32041 CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 00:48:08.410 32041-32048 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 971KB AllocSpace bytes, 1(24KB) LOS objects, 25% free, 6074KB/8122KB, paused 273us,45us total 112.280ms
2025-05-27 00:48:08.504 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:48:08.513 32041-32041 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 00:48:08.513 32041-32041 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 00:48:08.513 32041-32041 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 00:48:08.513 32041-32041 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 00:48:09.363 32041-32041 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 00:48:09.363 32041-32041 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 00:48:09.365 32041-32077 ChessAI                 com.chessvision.app                  D  🔍 Starting operation #2 with zero main thread impact...
2025-05-27 00:48:09.366 32041-32077 ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 00:48:09.367 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #2 with enterprise-grade processing...
2025-05-27 00:48:09.424 32041-32077 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 00:48:09.810 32041-32077 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 00:48:09.812 32041-32077 ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 00:48:09.812 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 00:48:09.827 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 15ms (LIGHTNING FAST)
2025-05-27 00:48:09.827 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 00:48:09.898 32041-32050 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 00:48:09.898 32041-32050 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 00:48:25.874 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 16046ms (LIGHTNING FAST)
2025-05-27 00:48:25.880 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-30.029545, 14.933038]
2025-05-27 00:48:25.881 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 00:48:25.972 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 98ms (LIGHTNING FAST)
2025-05-27 00:48:25.973 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 00:48:25.973 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 00:48:25.988 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 00:48:25.996 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 8ms (LIGHTNING FAST)
2025-05-27 00:48:25.997 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-27 00:48:27.536 32041-32077 ONNXChessAI             com.chessvision.app                  D  🚀 YOLO inference: 1538ms (LIGHTNING FAST)
2025-05-27 00:48:27.538 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-27 00:48:27.539 32041-32077 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-27 00:48:27.540 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-27 00:48:27.541 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed with zero memory leaks
2025-05-27 00:48:27.541 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-27 00:48:27.541 32041-32077 ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #2 - Segmentation: 16176ms, Detection: 1553ms
2025-05-27 00:48:27.542 32041-32077 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-27 00:48:27.546 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:48:27.547 32041-32077 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 00:48:27.547 32041-32077 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 00:48:27.547 32041-32077 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 00:48:27.548 32041-32077 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 00:48:27.549 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 00:48:27.549 32041-32077 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 00:48:27.550 32041-32077 ONNXChessAI             com.chessvision.app                  D  ✅ Operation #2 completed - EXACT same results as Python!
2025-05-27 00:48:27.550 32041-32077 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:48:27.550 32041-32077 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 18181ms
2025-05-27 00:48:27.550 32041-32077 ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 18181ms (target: <1000ms)
2025-05-27 00:48:27.551 32041-32077 ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 16176ms (target: <300ms)
2025-05-27 00:48:27.551 32041-32077 ONNXChessAI             com.chessvision.app                  W  ⚠️ YOLO performance below target. Detection: 1553ms (target: <200ms)
2025-05-27 00:48:27.551 32041-32077 ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 00:48:27.573 32041-32077 ONNXChessAI             com.chessvision.app                  D  🧹 Operation #2 resources cleaned up
2025-05-27 00:48:27.574 32041-32077 ChessAI                 com.chessvision.app                  D  ✅ Operation #2 completed successfully!
2025-05-27 00:48:27.574 32041-32077 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:48:27.575 32041-32077 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 00:48:27.576 32041-32077 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 18181ms
2025-05-27 00:48:27.576 32041-32077 ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 00:48:27.580 32041-32041 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:48:27.581 32041-32041 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 00:48:27.581 32041-32041 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 18181ms
2025-05-27 00:48:29.045 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 00:48:29.146 32041-32065 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1534ms; Flags=0, FrameTimelineVsyncId=18461021, IntendedVsync=334035086163005, Vsync=334035086163005, InputEventId=0, HandleInputStart=334035088353909, AnimationStart=334035088365294, PerformTraversalsStart=334035214204986, DrawStart=334036414984986, FrameDeadline=334035106163005, FrameInterval=334035088298525, FrameStartTime=16666667, SyncQueued=334036516776602, SyncStart=334036517242832, IssueDrawCommandsStart=334036519628294, SwapBuffers=334036611679679, FrameCompleted=334036620661602, DequeueBufferDuration=259385, QueueBufferDuration=1943384, GpuCompleted=334036620661602, SwapBuffersCompleted=334036614804986, DisplayPresentTime=0, 
2025-05-27 00:48:29.186 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 89 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:48:29.816 32041-32065 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2112ms; Flags=0, FrameTimelineVsyncId=18461023, IntendedVsync=334035168851476, Vsync=334036652184839, InputEventId=0, HandleInputStart=334036667362140, AnimationStart=334036667368986, PerformTraversalsStart=334037236588371, DrawStart=334037236887063, FrameDeadline=334035205518143, FrameInterval=334036667036602, FrameStartTime=16666667, SyncQueued=334037264984371, SyncStart=334037265658525, IssueDrawCommandsStart=334037267008679, SwapBuffers=334037271989294, FrameCompleted=334037281694832, DequeueBufferDuration=55769, QueueBufferDuration=2129769, GpuCompleted=334037281694832, SwapBuffersCompleted=334037276535371, DisplayPresentTime=0, 
2025-05-27 00:48:29.876 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 40 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:48:29.930 32041-32065 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=716ms; Flags=0, FrameTimelineVsyncId=18461031, IntendedVsync=334036685596805, Vsync=334037352263485, InputEventId=0, HandleInputStart=334037357164602, AnimationStart=334037357172679, PerformTraversalsStart=334037368396679, DrawStart=334037368585602, FrameDeadline=334036722263472, FrameInterval=334037356869832, FrameStartTime=16666667, SyncQueued=334037387931371, SyncStart=334037388485217, IssueDrawCommandsStart=334037389136063, SwapBuffers=334037393370140, FrameCompleted=334037402698832, DequeueBufferDuration=51615, QueueBufferDuration=2006769, GpuCompleted=334037402698832, SwapBuffersCompleted=334037396963448, DisplayPresentTime=0, 
2025-05-27 00:48:47.772 32041-32048 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 2485KB AllocSpace bytes, 8(7544KB) LOS objects, 24% free, 6833KB/9111KB, paused 279us,65us total 122.686ms
2025-05-27 00:48:48.079 32041-32065 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=712ms; Flags=0, FrameTimelineVsyncId=18461121, IntendedVsync=334054832143777, Vsync=334054832143777, InputEventId=644781225, HandleInputStart=334054845602526, AnimationStart=334054845610141, PerformTraversalsStart=334055127774833, DrawStart=334055127994833, FrameDeadline=334054852143777, FrameInterval=334054845579987, FrameStartTime=16666667, SyncQueued=334055508668987, SyncStart=334055508873526, IssueDrawCommandsStart=334055511542141, SwapBuffers=334055534598449, FrameCompleted=334055544860064, DequeueBufferDuration=74770, QueueBufferDuration=2633462, GpuCompleted=334055544860064, SwapBuffersCompleted=334055539666757, DisplayPresentTime=1904398480, 
2025-05-27 00:48:48.109 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:48:53.278 32041-32056 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=789ms; Flags=0, FrameTimelineVsyncId=18461193, IntendedVsync=334059950448359, Vsync=334059967115026, InputEventId=1015917184, HandleInputStart=334059975875757, AnimationStart=334059975883757, PerformTraversalsStart=334060559923065, DrawStart=334060560440372, FrameDeadline=334059970448359, FrameInterval=334059975848295, FrameStartTime=16666667, SyncQueued=334060704332680, SyncStart=334060705042141, IssueDrawCommandsStart=334060707283141, SwapBuffers=334060729872295, FrameCompleted=334060740926526, DequeueBufferDuration=120385, QueueBufferDuration=2796077, GpuCompleted=334060740926526, SwapBuffersCompleted=334060735900603, DisplayPresentTime=1899477368, 
2025-05-27 00:49:00.127 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 4,1
2025-05-27 00:49:03.837 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging BISHOP from board position 5,0
2025-05-27 00:49:09.694 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 2,1
2025-05-27 00:49:10.208 32041-32041 Choreographer           com.chessvision.app                  I  Skipped 30 frames!  The application may be doing too much work on its main thread.
2025-05-27 00:49:10.504 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 3,1
2025-05-27 00:49:10.799 32041-32048 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 1239KB AllocSpace bytes, 6(224KB) LOS objects, 24% free, 7643KB/10191KB, paused 191us,790us total 131.927ms
2025-05-27 00:49:11.169 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: rnbqkbnr/pppppppp/8/8/8/8/PP1P1PPP/RNBQK1NR w KQkq - 0 1
2025-05-27 00:49:11.328 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging BISHOP from board position 2,0
2025-05-27 00:49:11.938 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging KNIGHT from board position 1,0
2025-05-27 00:49:20.696 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging BISHOP from tray
2025-05-27 00:49:21.282 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: rnbqkbnr/pppppppp/8/8/8/8/PP1P1PPP/R2QK1NR w KQkq - 0 1
2025-05-27 00:49:21.559 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging KING from board position 4,0
2025-05-27 00:49:27.565 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 1,1
2025-05-27 00:49:28.194 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 0,1
2025-05-27 00:49:28.718 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: rnbqkbnr/pppppppp/8/8/8/8/P2P1PPP/R2Q2NR w KQkq - 0 1
2025-05-27 00:49:29.136 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 1,6
2025-05-27 00:49:29.600 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 3,6
2025-05-27 00:49:30.086 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 5,6
2025-05-27 00:49:30.100 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: rnbqkbnr/p1pppppp/8/8/8/8/P2P1PPP/R2Q2NR w KQkq - 0 1
2025-05-27 00:49:30.379 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging PAWN from board position 6,6
2025-05-27 00:49:30.611 32041-32048 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 2306KB AllocSpace bytes, 5(136KB) LOS objects, 24% free, 7705KB/10MB, paused 209us,99us total 130.084ms
2025-05-27 00:49:30.658 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging BISHOP from board position 5,7
2025-05-27 00:49:30.966 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging QUEEN from board position 3,7
2025-05-27 00:49:31.236 32041-32041 ChessDrag               com.chessvision.app                  D  Started dragging KNIGHT from board position 1,7
2025-05-27 00:49:35.845 32041-32041 ChessMove               com.chessvision.app                  D  Selected BLACK PAWN at 2,6
2025-05-27 00:49:36.161 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move to empty square, deselecting
2025-05-27 00:49:36.937 32041-32041 ChessMove               com.chessvision.app                  D  Selected BLACK PAWN at 2,6
2025-05-27 00:49:37.263 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move to empty square, deselecting
2025-05-27 00:49:37.884 32041-32041 ChessMove               com.chessvision.app                  D  Selected BLACK PAWN at 3,6
2025-05-27 00:49:39.191 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move attempted, selecting piece at target instead
2025-05-27 00:49:41.859 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move attempted, selecting piece at target instead
2025-05-27 00:49:43.291 32041-32041 ChessMove               com.chessvision.app                  D  Legal move: PAWN from 3,6 to 3,4
2025-05-27 00:49:43.584 32041-32048 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 2553KB AllocSpace bytes, 7(180KB) LOS objects, 24% free, 7580KB/10107KB, paused 344us,117us total 198.380ms
2025-05-27 00:49:43.666 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: r1b1k1nr/p1p1pppp/8/3p4/8/8/P2P1PPP/R2Q2NR w KQkq - 0 1
2025-05-27 00:49:44.504 32041-32041 ChessMove               com.chessvision.app                  D  Selected BLACK PAWN at 3,4
2025-05-27 00:49:45.018 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move attempted, selecting piece at target instead
2025-05-27 00:49:45.739 32041-32041 ChessMove               com.chessvision.app                  D  Legal move: PAWN from 3,1 to 3,2
2025-05-27 00:49:46.025 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: r1b1k1nr/p1p1pppp/8/3p4/8/3P4/P4PPP/R2Q2NR w KQkq - 0 1
2025-05-27 00:49:47.491 32041-32041 ChessMove               com.chessvision.app                  D  Selected WHITE PAWN at 3,2
2025-05-27 00:49:47.846 32041-32041 ChessMove               com.chessvision.app                  D  Legal move: PAWN from 3,2 to 3,3
2025-05-27 00:49:47.955 32041-32046 chessvision.ap          com.chessvision.app                  I  Compiler allocated 4597KB to compile void com.chessvision.app.MainActivityKt.ChessBoardScreen(kotlin.jvm.functions.Function0, java.lang.String, kotlin.jvm.functions.Function1, androidx.compose.runtime.Composer, int, int)
2025-05-27 00:49:48.086 32041-32041 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: r1b1k1nr/p1p1pppp/8/3p4/3P4/8/P4PPP/R2Q2NR w KQkq - 0 1
2025-05-27 00:49:48.673 32041-32041 ChessMove               com.chessvision.app                  D  Selected BLACK PAWN at 3,4
2025-05-27 00:49:49.019 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move attempted, selecting piece at target instead
2025-05-27 00:49:49.955 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move attempted, selecting piece at target instead
2025-05-27 00:49:50.302 32041-32041 ChessMove               com.chessvision.app                  D  Illegal move attempted, selecting piece at target instead
