--------- beginning of main
---------------------------- PROCESS STARTED (11795) for package com.chessvision.app ----------------------------
2025-05-26 23:40:43.645 11795-11795 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/base.dm': No such file or directory
2025-05-26 23:40:43.645 11795-11795 ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/base.dm': No such file or directory
2025-05-26 23:40:44.843 11795-11795 nativeloader            com.chessvision.app                  D  Configuring clns-4 for other apk /data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/lib/arm64:/data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.chessvision.app
2025-05-26 23:40:44.872 11795-11795 GraphicsEnvironment     com.chessvision.app                  V  ANGLE Developer option for 'com.chessvision.app' set to: 'default'
2025-05-26 23:40:44.873 11795-11795 GraphicsEnvironment     com.chessvision.app                  V  Neither updatable production driver nor prerelease driver is supported.
2025-05-26 23:40:44.880 11795-11795 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-26 23:40:44.882 11795-11795 NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-26 23:40:44.917 11795-11795 libc                    com.chessvision.app                  W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-26 23:40:45.032 11795-11795 ChessVisionApp          com.chessvision.app                  D  Application initialized
2025-05-26 23:40:45.191 11795-11795 chessvision.ap          com.chessvision.app                  E  Invalid ID 0x00000000.
2025-05-26 23:40:45.547 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 37 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:40:45.552 11795-11795 XDR::VRT                com.chessvision.app                  I  sc is not valid!
2025-05-26 23:40:47.221 11795-11795 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-26 23:40:47.222 11795-11795 chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-26 23:40:47.222 11795-11795 chessvision.ap          com.chessvision.app                  W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-26 23:40:47.223 11795-11795 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-26 23:40:47.223 11795-11795 chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-26 23:40:47.409 11795-11795 Compatibil...geReporter com.chessvision.app                  D  Compat change id reported: 171228096; UID 10718; state: ENABLED
2025-05-26 23:40:48.014 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [](id:2e1300000000,api:0,p:-1,c:11795) connect: controlledByApp=false
2025-05-26 23:40:48.018 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0] constructor()
2025-05-26 23:40:48.042 11795-11831 hw-ProcessState         com.chessvision.app                  D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-26 23:40:48.077 11795-11831 BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0(BLAST Consumer)0](id:2e1300000000,api:1,p:11795,c:11795) connect: api=1 producerControlledByApp=true
2025-05-26 23:40:48.089 11795-11846 ion                     com.chessvision.app                  E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-26 23:40:48.128 11795-11831 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-26 23:40:48.128 11795-11831 OpenGLRenderer          com.chessvision.app                  E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-26 23:40:48.150 11795-11807 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3201ms; Flags=1, FrameTimelineVsyncId=18425705, IntendedVsync=329972405700027, Vsync=329973022366706, InputEventId=0, HandleInputStart=329973029204129, AnimationStart=329973029227513, PerformTraversalsStart=329973029695513, DrawStart=329975560204052, FrameDeadline=329972425700027, FrameInterval=329973028699513, FrameStartTime=16666667, SyncQueued=329975579288129, SyncStart=329975586671744, IssueDrawCommandsStart=329975587680975, SwapBuffers=329975608390821, FrameCompleted=329975614694283, DequeueBufferDuration=0, QueueBufferDuration=2579000, GpuCompleted=329975613586590, SwapBuffersCompleted=329975614694283, DisplayPresentTime=0, 
2025-05-26 23:40:48.181 11795-11800 chessvision.ap          com.chessvision.app                  I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-26 23:40:48.240 11795-11795 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-26 23:40:48.240 11795-11795 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-26 23:40:48.241 11795-11795 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=null
2025-05-26 23:40:48.241 11795-11795 ImeFocusController      com.chessvision.app                  V  checkFocus: view=null next=DecorView@17fb6e3[MainActivity] force=true package=<none>
2025-05-26 23:40:48.486 11795-11859 ChessAI                 com.chessvision.app                  D  🚀 Initializing Chess AI with ONNX models (EXACT same as Python)...
2025-05-26 23:40:48.486 11795-11859 ChessAI                 com.chessvision.app                  D  🎯 V6 Segmentation: EXACT same weights (Dice: 0.9391)
2025-05-26 23:40:48.487 11795-11859 ChessAI                 com.chessvision.app                  D  🎯 YOLO Detection: EXACT same weights (mAP50: 97.3%)
2025-05-26 23:40:48.490 11795-11859 ONNXChessAI             com.chessvision.app                  D  🚀 Initializing ONNX Chess AI with ACTUAL trained models...
2025-05-26 23:40:48.490 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔬 V6 Model: EXACT same weights as Python (Dice: 0.9391)
2025-05-26 23:40:48.490 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model: EXACT same weights as Python (mAP50: 97.3%)
2025-05-26 23:40:48.593 11795-11859 nativeloader            com.chessvision.app                  D  Load /data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/base.apk!/lib/arm64-v8a/libonnxruntime4j_jni.so using ns clns-4 from class loader (caller=/data/app/~~nhiKILGLKZPVAGjz7xh-qg==/com.chessvision.app-01aNbp0AZqXANsoCP2Q_Gg==/base.apk): ok
2025-05-26 23:40:48.827 11795-11859 libc                    com.chessvision.app                  W  Access denied finding property "ro.hardware.chipname"
2025-05-26 23:40:49.883 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 83 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:40:49.903 11795-11807 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1424ms; Flags=0, FrameTimelineVsyncId=18425821, IntendedVsync=329975938441756, Vsync=329975955108423, InputEventId=0, HandleInputStart=329975959260360, AnimationStart=329975959265360, PerformTraversalsStart=329976692504052, DrawStart=329976692794283, FrameDeadline=329975975108423, FrameInterval=329975959232129, FrameStartTime=16666667, SyncQueued=329977355089360, SyncStart=329977355447821, IssueDrawCommandsStart=329977356376667, SwapBuffers=329977358422821, FrameCompleted=329977362853283, DequeueBufferDuration=71923, QueueBufferDuration=2293538, GpuCompleted=329977362260129, SwapBuffersCompleted=329977362853283, DisplayPresentTime=1630609416544980717, 
2025-05-26 23:40:49.966 11795-11807 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1460ms; Flags=0, FrameTimelineVsyncId=18425826, IntendedVsync=329975971769744, Vsync=329977355103105, InputEventId=0, HandleInputStart=329977365141052, AnimationStart=329977365145975, PerformTraversalsStart=329977415827206, DrawStart=329977416140360, FrameDeadline=329976008436411, FrameInterval=329977364848129, FrameStartTime=16666667, SyncQueued=329977423987821, SyncStart=329977424281283, IssueDrawCommandsStart=329977424750898, SwapBuffers=329977428105975, FrameCompleted=329977432965360, DequeueBufferDuration=56692, QueueBufferDuration=2672077, GpuCompleted=329977431563513, SwapBuffersCompleted=329977432965360, DisplayPresentTime=1646090776860825301, 
2025-05-26 23:40:51.018 11795-11874 ProfileInstaller        com.chessvision.app                  D  Installing profile for com.chessvision.app
2025-05-26 23:40:51.271 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX Runtime sessions created successfully!
2025-05-26 23:40:51.271 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 V6 Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/v6_mobile.onnx
2025-05-26 23:40:51.272 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/yolo_mobile.onnx
2025-05-26 23:40:51.272 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX models loaded successfully!
2025-05-26 23:40:51.272 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 These are the EXACT same models as Python - same accuracy guaranteed!
2025-05-26 23:40:51.272 11795-11859 ChessAI                 com.chessvision.app                  D  ✅ ONNX Chess AI initialized successfully!
2025-05-26 23:40:54.417 11795-11877 CameraManagerGlobal     com.chessvision.app                  I  Connecting to camera service
2025-05-26 23:40:54.487 11795-11877 CameraRepository        com.chessvision.app                  D  Added camera: 0
2025-05-26 23:40:54.586 11795-11877 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-26 23:40:54.607 11795-11877 CameraRepository        com.chessvision.app                  D  Added camera: 1
2025-05-26 23:40:54.609 11795-11877 Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-26 23:40:54.611 11795-11877 CameraValidator         com.chessvision.app                  D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-26 23:40:54.781 11795-11807 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=733ms; Flags=0, FrameTimelineVsyncId=18425925, IntendedVsync=329981520631138, Vsync=329981553964472, InputEventId=389090730, HandleInputStart=329981563155206, AnimationStart=329981563160129, PerformTraversalsStart=329982050989898, DrawStart=329982180224052, FrameDeadline=329981540631138, FrameInterval=329981563138206, FrameStartTime=16666667, SyncQueued=329982222575437, SyncStart=329982223277975, IssueDrawCommandsStart=329982224153052, SwapBuffers=329982249586514, FrameCompleted=329982255179745, DequeueBufferDuration=58153, QueueBufferDuration=2050769, GpuCompleted=329982255179745, SwapBuffersCompleted=329982253267206, DisplayPresentTime=196216182258795191, 
2025-05-26 23:40:54.817 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:40:54.818 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:40:54.871 11795-11795 DynamicRangeResolver    com.chessvision.app                  D  Resolved dynamic range for use case androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b77082 to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@c021aaf{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@241d28e{encoding=SDR, bitDepth=8}
2025-05-26 23:40:54.898 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:40:54.902 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface created[total_surfaces=1, used_surfaces=0](androidx.camera.core.processing.SurfaceEdge$SettableSurface@ebb2aa8}
2025-05-26 23:40:54.912 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface created[total_surfaces=2, used_surfaces=0](androidx.camera.core.SurfaceRequest$2@aec4954}
2025-05-26 23:40:54.916 11795-11795 DeferrableSurface       com.chessvision.app                  D  New surface in use[total_surfaces=2, used_surfaces=1](androidx.camera.core.SurfaceRequest$2@aec4954}
2025-05-26 23:40:54.916 11795-11795 DeferrableSurface       com.chessvision.app                  D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@aec4954
2025-05-26 23:40:54.919 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:40:54.934 11795-11795 ImageCapture            com.chessvision.app                  D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=3264x2448, dynamicRange=DynamicRange@241d28e{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@66baff9})
2025-05-26 23:40:54.963 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [](id:2e1300000001,api:0,p:-1,c:11795) connect: controlledByApp=true
2025-05-26 23:40:54.965 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface created[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@f0dfb3e}
2025-05-26 23:40:54.982 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989 ACTIVE
2025-05-26 23:40:54.984 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:40:54.988 11795-11795 PreviewView             com.chessvision.app                  D  Surface requested by Preview.
2025-05-26 23:40:54.990 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972 ACTIVE
2025-05-26 23:40:54.994 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:40:55.000 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989 ACTIVE
2025-05-26 23:40:55.002 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:40:55.007 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:40:55.011 11795-11795 SurfaceFactory          com.chessvision.app                  I  [static] sSurfaceFactory = com.mediatek.view.impl.SurfaceFactoryImpl@3e6531
2025-05-26 23:40:55.013 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use cases [androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972, androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989] now ATTACHED
2025-05-26 23:40:55.016 11795-11795 PreviewView             com.chessvision.app                  D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-26 23:40:55.017 11795-11795 PreviewTransform        com.chessvision.app                  D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-26 23:40:55.018 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:40:55.025 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.026 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-26 23:40:55.030 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.039 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Resetting Capture Session
2025-05-26 23:40:55.040 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Releasing session in state INITIALIZED
2025-05-26 23:40:55.043 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Attempting to force open the camera.
2025-05-26 23:40:55.045 11795-11877 CameraStateRegistry     com.chessvision.app                  D  tryOpenCamera(Camera@e359927[id=0]) [Available Cameras: 1, Already Open: false (Previous state: null)] --> SUCCESS
2025-05-26 23:40:55.048 11795-11877 CameraStateRegistry     com.chessvision.app                  D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e359927[id=0]                         OPENING               
                                                                                                    Camera@522bd6c[id=1]                         UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-26 23:40:55.049 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Opening camera.
2025-05-26 23:40:55.051 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Transitioning camera internal state: INITIALIZED --> OPENING
2025-05-26 23:40:55.053 11795-11877 CameraStateMachine      com.chessvision.app                  D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-26 23:40:55.053 11795-11877 CameraStateMachine      com.chessvision.app                  D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-26 23:40:55.055 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Wait for new Surface creation.
2025-05-26 23:40:55.057 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.064 11795-11877 libc                    com.chessvision.app                  W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-26 23:40:55.061 11795-11795 CameraX-core_ca         com.chessvision.app                  W  type=1400 audit(0.0:3550207): avc: denied { read } for name="u:object_r:vendor_camera_mtk_prop:s0" dev="tmpfs" ino=13626 scontext=u:r:untrusted_app:s0:c206,c258,c512,c768 tcontext=u:object_r:vendor_camera_mtk_prop:s0 tclass=file permissive=0 app=com.chessvision.app
2025-05-26 23:40:55.083 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [](id:2e1300000002,api:0,p:-1,c:11795) connect: controlledByApp=false
2025-05-26 23:40:55.084 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1] constructor()
--------- beginning of system
2025-05-26 23:40:55.087 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface created.
2025-05-26 23:40:55.090 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface changed. Size: 1024x768
2025-05-26 23:40:55.090 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface set on Preview.
2025-05-26 23:40:55.153 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972 ACTIVE
2025-05-26 23:40:55.158 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.165 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989 ACTIVE
2025-05-26 23:40:55.168 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.175 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} CameraDevice.onOpened()
2025-05-26 23:40:55.176 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-26 23:40:55.178 11795-11877 CameraStateRegistry     com.chessvision.app                  D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e359927[id=0]                         OPEN                  
                                                                                                    Camera@522bd6c[id=1]                         UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-26 23:40:55.178 11795-11877 CameraStateMachine      com.chessvision.app                  D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-26 23:40:55.179 11795-11877 CameraStateMachine      com.chessvision.app                  D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-26 23:40:55.181 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.196 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989, androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972] for camera: 0
2025-05-26 23:40:55.205 11795-11877 SyncCaptureSessionBase  com.chessvision.app                  D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@fc48c23] getSurface...done
2025-05-26 23:40:55.206 11795-11877 CaptureSession          com.chessvision.app                  D  Opening capture session.
2025-05-26 23:40:55.224 11795-11877 DeferrableSurface       com.chessvision.app                  D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@aec4954
2025-05-26 23:40:55.224 11795-11877 DeferrableSurface       com.chessvision.app                  D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@f0dfb3e}
2025-05-26 23:40:55.224 11795-11877 DeferrableSurface       com.chessvision.app                  D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@f0dfb3e
2025-05-26 23:40:55.352 11795-11877 BufferQueueProducer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) connect: api=4 producerControlledByApp=true
2025-05-26 23:40:55.353 11795-11877 BufferQueueProducer     com.chessvision.app                  I  [ImageReader-3264x2448f100m4-11795-0](id:2e1300000001,api:4,p:985,c:11795) connect: api=4 producerControlledByApp=false
2025-05-26 23:40:55.361 11795-11877 CaptureSession          com.chessvision.app                  D  Attempting to send capture request onConfigured
2025-05-26 23:40:55.362 11795-11877 CaptureSession          com.chessvision.app                  D  Issuing request for session.
2025-05-26 23:40:55.363 11795-11877 Camera2Cap...estBuilder com.chessvision.app                  D  createCaptureRequest
2025-05-26 23:40:55.374 11795-11877 CaptureSession          com.chessvision.app                  D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-26 23:40:55.375 11795-11877 CaptureSession          com.chessvision.app                  D  CameraCaptureSession.onReady() OPENED
2025-05-26 23:40:55.787 11795-11877 StreamStateObserver     com.chessvision.app                  D  Update Preview stream state to STREAMING
2025-05-26 23:41:08.062 11795-11795 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-26 23:41:08.262 11795-11795 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-26 23:41:08.434 11795-11801 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 984KB AllocSpace bytes, 1(24KB) LOS objects, 25% free, 5912KB/7960KB, paused 396us,43us total 105.656ms
2025-05-26 23:41:08.448 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface destroyed.
2025-05-26 23:41:08.449 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface closed androidx.camera.core.SurfaceRequest@8b4b0a7
2025-05-26 23:41:08.450 11795-11795 DeferrableSurface       com.chessvision.app                  D  surface closed,  useCount=2 closed=true androidx.camera.core.SurfaceRequest$2@aec4954
2025-05-26 23:41:08.450 11795-11795 BufferQueueProducer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) disconnect: api -1
2025-05-26 23:41:08.451 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1] destructor()
2025-05-26 23:41:08.451 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) disconnect
2025-05-26 23:41:08.489 11795-11812 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:41:08.508 11795-11812 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) dequeueBuffer: BufferQueue has been abandoned
2025-05-26 23:41:08.558 11795-11812 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:41:08.622 11795-11812 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:41:08.659 11795-11812 BLASTBufferQueue        com.chessvision.app                  I  releaseBufferCallbackThunk bufferId:50659139256329 framenumber:191 blastBufferQueue is dead
2025-05-26 23:41:08.660 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 38 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:41:08.670 11795-11795 DeferrableSurface       com.chessvision.app                  D  surface closed,  useCount=0 closed=true androidx.camera.core.processing.SurfaceEdge$SettableSurface@ebb2aa8
2025-05-26 23:41:08.671 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface terminated[total_surfaces=2, used_surfaces=2](androidx.camera.core.processing.SurfaceEdge$SettableSurface@ebb2aa8}
2025-05-26 23:41:08.672 11795-11795 DeferrableSurface       com.chessvision.app                  D  use count-1,  useCount=1 closed=true androidx.camera.core.SurfaceRequest$2@aec4954
2025-05-26 23:41:08.674 11795-11795 ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-26 23:41:08.674 11795-11795 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-26 23:41:08.675 11795-11795 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-26 23:41:08.676 11795-11795 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-26 23:41:08.681 11795-11843 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=884ms; Flags=0, FrameTimelineVsyncId=18426854, IntendedVsync=329995268649156, Vsync=329995268649156, InputEventId=0, HandleInputStart=329995280210899, AnimationStart=329995280218207, PerformTraversalsStart=329995982010822, DrawStart=329996081814207, FrameDeadline=329995288649156, FrameInterval=329995280161899, FrameStartTime=16666667, SyncQueued=329996120851130, SyncStart=329996121184130, IssueDrawCommandsStart=329996124778284, SwapBuffers=329996144713361, FrameCompleted=329996153482592, DequeueBufferDuration=53769, QueueBufferDuration=2124308, GpuCompleted=329996153482592, SwapBuffersCompleted=329996149653053, DisplayPresentTime=1275945534518135671, 
2025-05-26 23:41:08.693 11795-11812 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:41:08.759 11795-11812 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:41:08.766 11795-11878 CaptureSession          com.chessvision.app                  D  CameraCaptureSession.onReady() OPENED
2025-05-26 23:41:10.776 11795-11795 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-26 23:41:10.782 11795-11859 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-26 23:41:10.785 11795-11859 ChessAI                 com.chessvision.app                  D  🔍 Processing chess board image with ONNX models (EXACT same as Python)...
2025-05-26 23:41:10.786 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔍 Processing with EXACT same models as Python...
2025-05-26 23:41:10.825 11795-11859 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-26 23:41:11.268 11795-11859 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-26 23:41:11.270 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation (EXACT same model as Python)...
2025-05-26 23:41:11.433 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-26 23:41:11.484 11795-11801 chessvision.ap          com.chessvision.app                  I  Background young concurrent copying GC freed 27KB AllocSpace bytes, 0(0B) LOS objects, 1% free, 12MB/12MB, paused 7.801ms,57us total 44.108ms
2025-05-26 23:41:15.743 11795-11801 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 69KB AllocSpace bytes, 1(1028KB) LOS objects, 24% free, 11MB/15MB, paused 145us,46us total 102.021ms
2025-05-26 23:41:29.703 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-34.606117, 14.612066]
2025-05-26 23:41:29.703 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔧 Applying sigmoid activation (raw logits detected)
2025-05-26 23:41:29.877 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-26 23:41:29.877 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed (using EXACT same weights as Python)
2025-05-26 23:41:29.896 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection (EXACT same model as Python)...
2025-05-26 23:41:30.043 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-26 23:41:30.377 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-26 23:41:30.377 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-26 23:41:30.378 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-26 23:41:30.378 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed (using EXACT same weights as Python)
2025-05-26 23:41:30.378 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-26 23:41:30.378 11795-11859 ONNXChessAI             com.chessvision.app                  D  ⏱️ Segmentation: 18626ms, Detection: 482ms
2025-05-26 23:41:30.378 11795-11859 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX processing completed - EXACT same results as Python!
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:41:30.380 11795-11859 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 19594ms
2025-05-26 23:41:30.381 11795-11859 ChessAI                 com.chessvision.app                  D  ✅ ONNX AI processing successful!
2025-05-26 23:41:30.381 11795-11859 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:41:30.381 11795-11859 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-26 23:41:30.381 11795-11859 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 19594ms
2025-05-26 23:41:30.386 11795-11795 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:41:30.387 11795-11795 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-26 23:41:30.387 11795-11795 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 19594ms
2025-05-26 23:41:32.967 11795-11801 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 506KB AllocSpace bytes, 6(4440KB) LOS objects, 24% free, 7472KB/9962KB, paused 284us,126us total 152.443ms
2025-05-26 23:41:33.332 11795-11795 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:41:33.458 11795-11843 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3068ms; Flags=0, FrameTimelineVsyncId=18427152, IntendedVsync=330017864687039, Vsync=330017881353706, InputEventId=0, HandleInputStart=330017881942516, AnimationStart=330017881953977, PerformTraversalsStart=330018081200054, DrawStart=330020619520362, FrameDeadline=330017884687039, FrameInterval=330017881887747, FrameStartTime=16666667, SyncQueued=330020804453978, SyncStart=330020804688055, IssueDrawCommandsStart=330020806912362, SwapBuffers=330020924352131, FrameCompleted=330020933787208, DequeueBufferDuration=93230, QueueBufferDuration=2210154, GpuCompleted=330020933787208, SwapBuffersCompleted=330020927798208, DisplayPresentTime=0, 
2025-05-26 23:41:33.572 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 182 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:41:34.711 11795-11843 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=4162ms; Flags=0, FrameTimelineVsyncId=18427154, IntendedVsync=330018014651843, Vsync=330021047985237, InputEventId=0, HandleInputStart=330021053274901, AnimationStart=330021053280362, PerformTraversalsStart=330022110425285, DrawStart=330022110745055, FrameDeadline=330018051318510, FrameInterval=330021053009362, FrameStartTime=16666667, SyncQueued=330022159052978, SyncStart=330022159399132, IssueDrawCommandsStart=330022160908670, SwapBuffers=330022167648670, FrameCompleted=330022177451516, DequeueBufferDuration=65154, QueueBufferDuration=1755692, GpuCompleted=330022177451516, SwapBuffersCompleted=330022171740978, DisplayPresentTime=0, 
2025-05-26 23:41:34.812 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 73 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:41:34.894 11795-11843 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1295ms; Flags=0, FrameTimelineVsyncId=18427179, IntendedVsync=330021064178665, Vsync=330022280845356, InputEventId=0, HandleInputStart=330022293150208, AnimationStart=330022293155824, PerformTraversalsStart=330022309609670, DrawStart=330022309830824, FrameDeadline=330021100845332, FrameInterval=330022292891132, FrameStartTime=16666667, SyncQueued=330022341828439, SyncStart=330022342395747, IssueDrawCommandsStart=330022343328362, SwapBuffers=330022350735824, FrameCompleted=330022360313593, DequeueBufferDuration=65154, QueueBufferDuration=2356000, GpuCompleted=330022360313593, SwapBuffersCompleted=330022355932055, DisplayPresentTime=0, 
2025-05-26 23:41:50.560 11795-11795 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600}, mServedView=DecorView@17fb6e3[MainActivity]
2025-05-26 23:41:50.562 11795-11795 Compose Focus           com.chessvision.app                  D  Owner FocusChanged(true)
2025-05-26 23:41:50.839 11795-11795 ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F....ID 0,0-720,1600} force=false package=com.chessvision.app
2025-05-26 23:41:50.873 11795-11795 InsetsController        com.chessvision.app                  D  show(ime(), fromIme=false)
2025-05-26 23:41:51.312 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [](id:2e1300000003,api:0,p:-1,c:11795) connect: controlledByApp=false
2025-05-26 23:41:51.313 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[Pop-Up Window]#2] constructor()
2025-05-26 23:41:51.316 11795-11831 BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[Pop-Up Window]#2(BLAST Consumer)2](id:2e1300000003,api:1,p:11795,c:11795) connect: api=1 producerControlledByApp=true
2025-05-26 23:41:53.020 11795-11795 InsetsController        com.chessvision.app                  D  show(ime(), fromIme=true)
2025-05-26 23:41:56.494 11795-11795 InsetsController        com.chessvision.app                  D  show(ime(), fromIme=false)
2025-05-26 23:41:56.801 11795-11795 InsetsController        com.chessvision.app                  D  show(ime(), fromIme=true)
2025-05-26 23:41:58.184 11795-11831 BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[Pop-Up Window]#2(BLAST Consumer)2](id:2e1300000003,api:1,p:11795,c:11795) disconnect: api 1
2025-05-26 23:41:58.187 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[Pop-Up Window]#2] destructor()
2025-05-26 23:41:58.187 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [ViewRootImpl[Pop-Up Window]#2(BLAST Consumer)2](id:2e1300000003,api:0,p:-1,c:11795) disconnect
2025-05-26 23:41:58.203 11795-11812 BLASTBufferQueue        com.chessvision.app                  I  releaseBufferCallbackThunk bufferId:50659139256341 framenumber:2 blastBufferQueue is dead
2025-05-26 23:41:58.431 11795-11795 IInputConnectionWrapper com.chessvision.app                  W  requestCursorAnchorInfo on inactive InputConnection
2025-05-26 23:42:04.973 11795-11801 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 2260KB AllocSpace bytes, 0(0B) LOS objects, 24% free, 7679KB/10239KB, paused 153us,58us total 171.159ms
2025-05-26 23:42:04.988 11795-11812 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=871ms; Flags=0, FrameTimelineVsyncId=18428240, IntendedVsync=330051575752051, Vsync=330051592418718, InputEventId=344263755, HandleInputStart=330051598691826, AnimationStart=330051598698287, PerformTraversalsStart=330051993791518, DrawStart=330052363450287, FrameDeadline=330051595752051, FrameInterval=330051598667749, FrameStartTime=16666667, SyncQueued=330052421258903, SyncStart=330052421578518, IssueDrawCommandsStart=330052422315210, SwapBuffers=330052437874210, FrameCompleted=330052447220672, DequeueBufferDuration=38077, QueueBufferDuration=1102539, GpuCompleted=330052447220672, SwapBuffersCompleted=330052439949287, DisplayPresentTime=21673899988484171, 
2025-05-26 23:42:05.085 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 38 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:42:07.390 11795-11795 ChessDrag               com.chessvision.app                  D  Started dragging KING from tray
2025-05-26 23:42:07.931 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 31 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:42:11.850 11795-11795 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/4K3 w KQkq - 0 1
2025-05-26 23:42:15.785 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 46 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:46:52.778 11795-11795 ImageCapture            com.chessvision.app                  D  clearPipeline
2025-05-26 23:46:52.779 11795-11795 DeferrableSurface       com.chessvision.app                  D  surface closed,  useCount=1 closed=true androidx.camera.core.impl.ImmediateSurface@f0dfb3e
2025-05-26 23:46:52.788 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use cases [androidx.camera.core.Preview-14a48d01-9865-4053-a066-834684b7708252743972, androidx.camera.core.ImageCapture-0a98e254-6525-4e4e-9be6-14b7464f49b8128014989] now DETACHED for camera
2025-05-26 23:46:52.789 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [] for camera: 0
2025-05-26 23:46:52.790 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Resetting Capture Session
2025-05-26 23:46:52.794 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Releasing session in state OPENED
2025-05-26 23:46:52.796 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:52.799 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:52.802 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Closing camera.
2025-05-26 23:46:52.802 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Transitioning camera internal state: OPENED --> CLOSING
2025-05-26 23:46:52.804 11795-11877 CameraStateRegistry     com.chessvision.app                  D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e359927[id=0]                         CLOSING               
                                                                                                    Camera@522bd6c[id=1]                         UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-26 23:46:52.804 11795-11877 CameraStateMachine      com.chessvision.app                  D  New public camera state CameraState{type=CLOSING, error=null} from CLOSING and null
2025-05-26 23:46:52.804 11795-11877 CameraStateMachine      com.chessvision.app                  D  Publishing new public camera state CameraState{type=CLOSING, error=null}
2025-05-26 23:46:52.805 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Resetting Capture Session
2025-05-26 23:46:52.806 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Releasing session in state CLOSING
2025-05-26 23:46:52.808 11795-11877 CaptureSession          com.chessvision.app                  D  onSessionFinished()
2025-05-26 23:46:52.809 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:46:52.810 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:46:52.843 11795-11795 DynamicRangeResolver    com.chessvision.app                  D  Resolved dynamic range for use case androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104 to no compatible HDR dynamic ranges.
                                                                                                    DynamicRange@c021aaf{encoding=UNSPECIFIED, bitDepth=0}
                                                                                                    ->
                                                                                                    DynamicRange@241d28e{encoding=SDR, bitDepth=8}
2025-05-26 23:46:52.855 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:46:52.856 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface created[total_surfaces=3, used_surfaces=2](androidx.camera.core.processing.SurfaceEdge$SettableSurface@32fd4a6}
2025-05-26 23:46:52.859 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface created[total_surfaces=4, used_surfaces=2](androidx.camera.core.SurfaceRequest$2@6f49532}
2025-05-26 23:46:52.861 11795-11795 DeferrableSurface       com.chessvision.app                  D  New surface in use[total_surfaces=4, used_surfaces=3](androidx.camera.core.SurfaceRequest$2@6f49532}
2025-05-26 23:46:52.861 11795-11795 DeferrableSurface       com.chessvision.app                  D  use count+1, useCount=1 androidx.camera.core.SurfaceRequest$2@6f49532
2025-05-26 23:46:52.863 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:46:52.871 11795-11795 ImageCapture            com.chessvision.app                  D  createPipeline(cameraId: 0, streamSpec: StreamSpec{resolution=3264x2448, dynamicRange=DynamicRange@241d28e{encoding=SDR, bitDepth=8}, expectedFrameRateRange=[0, 0], implementationOptions=androidx.camera.camera2.impl.Camera2ImplConfig@a5a33df})
2025-05-26 23:46:52.873 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [](id:2e1300000004,api:0,p:-1,c:11795) connect: controlledByApp=true
2025-05-26 23:46:52.876 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface created[total_surfaces=5, used_surfaces=3](androidx.camera.core.impl.ImmediateSurface@1b0382c}
2025-05-26 23:46:52.886 11795-11795 PreviewView             com.chessvision.app                  D  Surface requested by Preview.
2025-05-26 23:46:52.894 11795-11795 StreamStateObserver     com.chessvision.app                  D  Update Preview stream state to IDLE
2025-05-26 23:46:52.894 11795-11795 PreviewView             com.chessvision.app                  D  Preview transformation info updated. TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false}
2025-05-26 23:46:52.895 11795-11795 PreviewTransform        com.chessvision.app                  D  Transformation info set: TransformationInfo{getCropRect=Rect(0, 0 - 1024, 768), getRotationDegrees=90, getTargetRotation=-1, hasCameraTransform=true, getSensorToBufferTransform=Matrix{[1.0, 0.0, 0.0][0.0, 1.0, 0.0][0.0, 0.0, 1.0]}, getMirroring=false} 1024x768 false
2025-05-26 23:46:52.896 11795-11795 CameraOrientationUtil   com.chessvision.app                  D  getRelativeImageRotation: destRotationDegrees=0, sourceRotationDegrees=90, isOppositeFacing=true, result=90
2025-05-26 23:46:52.907 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Wait for new Surface creation.
2025-05-26 23:46:52.919 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [](id:2e1300000005,api:0,p:-1,c:11795) connect: controlledByApp=false
2025-05-26 23:46:52.920 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3] constructor()
2025-05-26 23:46:52.922 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface created.
2025-05-26 23:46:52.925 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface changed. Size: 1024x768
2025-05-26 23:46:52.925 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface set on Preview.
2025-05-26 23:46:53.605 11795-11877 BufferQueueProducer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#1(BLAST Consumer)1](id:2e1300000002,api:4,p:985,c:11795) disconnect: api 4
2025-05-26 23:46:53.606 11795-11877 BufferQueueProducer     com.chessvision.app                  I  [ImageReader-3264x2448f100m4-11795-0](id:2e1300000001,api:4,p:985,c:11795) disconnect: api 4
2025-05-26 23:46:53.620 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:53.622 11795-11877 DeferrableSurface       com.chessvision.app                  D  use count-1,  useCount=0 closed=true androidx.camera.core.SurfaceRequest$2@aec4954
2025-05-26 23:46:53.622 11795-11877 DeferrableSurface       com.chessvision.app                  D  Surface no longer in use[total_surfaces=5, used_surfaces=2](androidx.camera.core.SurfaceRequest$2@aec4954}
2025-05-26 23:46:53.622 11795-11877 DeferrableSurface       com.chessvision.app                  D  Surface terminated[total_surfaces=4, used_surfaces=2](androidx.camera.core.SurfaceRequest$2@aec4954}
2025-05-26 23:46:53.623 11795-11877 DeferrableSurface       com.chessvision.app                  D  use count-1,  useCount=0 closed=true androidx.camera.core.impl.ImmediateSurface@f0dfb3e
2025-05-26 23:46:53.623 11795-11877 DeferrableSurface       com.chessvision.app                  D  Surface no longer in use[total_surfaces=4, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@f0dfb3e}
2025-05-26 23:46:53.623 11795-11877 DeferrableSurface       com.chessvision.app                  D  Surface terminated[total_surfaces=3, used_surfaces=1](androidx.camera.core.impl.ImmediateSurface@f0dfb3e}
2025-05-26 23:46:53.624 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Safe to release surface.
2025-05-26 23:46:53.624 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348 ACTIVE
2025-05-26 23:46:53.624 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:53.625 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [ImageReader-3264x2448f100m4-11795-0](id:2e1300000001,api:0,p:-1,c:11795) disconnect
2025-05-26 23:46:53.627 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935 ACTIVE
2025-05-26 23:46:53.627 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:53.630 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348 ACTIVE
2025-05-26 23:46:53.631 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:53.635 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [] for camera: 0
2025-05-26 23:46:53.640 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use cases [androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935, androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348] now ATTACHED
2025-05-26 23:46:53.644 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.645 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  mMeteringRepeating is ATTACHED, SessionConfig Surfaces: 2, CaptureConfig Surfaces: 1
2025-05-26 23:46:53.649 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.659 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Resetting Capture Session
2025-05-26 23:46:53.660 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Releasing session in state CLOSING
2025-05-26 23:46:53.661 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Transitioning camera internal state: CLOSING --> REOPENING
2025-05-26 23:46:53.663 11795-11877 CameraStateRegistry     com.chessvision.app                  D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e359927[id=0]                         OPENING               
                                                                                                    Camera@522bd6c[id=1]                         UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-26 23:46:53.663 11795-11877 CameraStateMachine      com.chessvision.app                  D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-26 23:46:53.664 11795-11877 CameraStateMachine      com.chessvision.app                  D  Publishing new public camera state CameraState{type=OPENING, error=null}
2025-05-26 23:46:53.664 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935 ACTIVE
2025-05-26 23:46:53.666 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.673 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Use case androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348 ACTIVE
2025-05-26 23:46:53.676 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.683 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} CameraDevice.onClosed()
2025-05-26 23:46:53.684 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Attempting to open the camera.
2025-05-26 23:46:53.684 11795-11877 CameraStateRegistry     com.chessvision.app                  D  tryOpenCamera(Camera@e359927[id=0]) [Available Cameras: 0, Already Open: true (Previous state: OPENING)] --> SUCCESS
2025-05-26 23:46:53.685 11795-11877 CameraStateRegistry     com.chessvision.app                  D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e359927[id=0]                         OPENING               
                                                                                                    Camera@522bd6c[id=1]                         UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-26 23:46:53.686 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Opening camera.
2025-05-26 23:46:53.686 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Transitioning camera internal state: REOPENING --> OPENING
2025-05-26 23:46:53.686 11795-11877 CameraStateMachine      com.chessvision.app                  D  New public camera state CameraState{type=OPENING, error=null} from OPENING and null
2025-05-26 23:46:53.689 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.695 11795-11877 libc                    com.chessvision.app                  W  Access denied finding property "persist.vendor.camera.privapp.list"
2025-05-26 23:46:53.838 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} CameraDevice.onOpened()
2025-05-26 23:46:53.845 11795-11877 Camera2CameraImpl       com.chessvision.app                  D  {Camera@e359927[id=0]} Transitioning camera internal state: OPENING --> OPENED
2025-05-26 23:46:53.849 11795-11877 CameraStateRegistry     com.chessvision.app                  D  Recalculating open cameras:
                                                                                                    Camera                                       State                 
                                                                                                    -------------------------------------------------------------------
                                                                                                    Camera@e359927[id=0]                         OPEN                  
                                                                                                    Camera@522bd6c[id=1]                         UNKNOWN               
                                                                                                    -------------------------------------------------------------------
                                                                                                    Open count: 1 (Max allowed: 1)
2025-05-26 23:46:53.850 11795-11877 CameraStateMachine      com.chessvision.app                  D  New public camera state CameraState{type=OPEN, error=null} from OPEN and null
2025-05-26 23:46:53.851 11795-11877 CameraStateMachine      com.chessvision.app                  D  Publishing new public camera state CameraState{type=OPEN, error=null}
2025-05-26 23:46:53.859 11795-11877 UseCaseAttachState      com.chessvision.app                  D  All use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.873 11795-11877 UseCaseAttachState      com.chessvision.app                  D  Active and attached use case: [androidx.camera.core.ImageCapture-1b0e0ae1-7c53-4a92-92b0-db8088eb3960176740348, androidx.camera.core.Preview-f738f233-75df-4df8-b442-27e60fede104265629935] for camera: 0
2025-05-26 23:46:53.889 11795-11877 SyncCaptureSessionBase  com.chessvision.app                  D  [androidx.camera.camera2.internal.SynchronizedCaptureSessionBaseImpl@ebf0dbf] getSurface...done
2025-05-26 23:46:53.890 11795-11877 CaptureSession          com.chessvision.app                  D  Opening capture session.
2025-05-26 23:46:53.902 11795-11877 DeferrableSurface       com.chessvision.app                  D  use count+1, useCount=2 androidx.camera.core.SurfaceRequest$2@6f49532
2025-05-26 23:46:53.902 11795-11877 DeferrableSurface       com.chessvision.app                  D  New surface in use[total_surfaces=3, used_surfaces=2](androidx.camera.core.impl.ImmediateSurface@1b0382c}
2025-05-26 23:46:53.903 11795-11877 DeferrableSurface       com.chessvision.app                  D  use count+1, useCount=1 androidx.camera.core.impl.ImmediateSurface@1b0382c
2025-05-26 23:46:54.055 11795-11877 BufferQueueProducer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) connect: api=4 producerControlledByApp=true
2025-05-26 23:46:54.057 11795-11877 BufferQueueProducer     com.chessvision.app                  I  [ImageReader-3264x2448f100m4-11795-1](id:2e1300000004,api:4,p:985,c:11795) connect: api=4 producerControlledByApp=false
2025-05-26 23:46:54.065 11795-11877 CaptureSession          com.chessvision.app                  D  Attempting to send capture request onConfigured
2025-05-26 23:46:54.066 11795-11877 CaptureSession          com.chessvision.app                  D  Issuing request for session.
2025-05-26 23:46:54.074 11795-11877 Camera2Cap...estBuilder com.chessvision.app                  D  createCaptureRequest
2025-05-26 23:46:54.086 11795-11877 CaptureSession          com.chessvision.app                  D  CameraCaptureSession.onConfigured() mState=OPENED
2025-05-26 23:46:54.088 11795-11877 CaptureSession          com.chessvision.app                  D  CameraCaptureSession.onReady() OPENED
2025-05-26 23:46:54.473 11795-11878 StreamStateObserver     com.chessvision.app                  D  Update Preview stream state to STREAMING
2025-05-26 23:47:08.179 11795-11795 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-26 23:47:08.382 11795-11795 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-26 23:47:08.550 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface destroyed.
2025-05-26 23:47:08.552 11795-11795 SurfaceViewImpl         com.chessvision.app                  D  Surface closed androidx.camera.core.SurfaceRequest@d0d693d
2025-05-26 23:47:08.554 11795-11795 DeferrableSurface       com.chessvision.app                  D  surface closed,  useCount=2 closed=true androidx.camera.core.SurfaceRequest$2@6f49532
2025-05-26 23:47:08.555 11795-11795 BufferQueueProducer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) disconnect: api -1
2025-05-26 23:47:08.556 11795-11795 BLASTBufferQueue        com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3] destructor()
2025-05-26 23:47:08.556 11795-11795 BufferQueueConsumer     com.chessvision.app                  I  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) disconnect
2025-05-26 23:47:08.582 11795-11807 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:47:08.604 11795-11807 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) dequeueBuffer: BufferQueue has been abandoned
2025-05-26 23:47:08.640 11795-11801 chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 3160KB AllocSpace bytes, 2(188KB) LOS objects, 24% free, 6893KB/9190KB, paused 125us,79us total 169.599ms
2025-05-26 23:47:08.646 11795-11806 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:47:08.715 11795-11806 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:47:08.727 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 40 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:47:08.734 11795-11806 BLASTBufferQueue        com.chessvision.app                  I  releaseBufferCallbackThunk bufferId:50659139256343 framenumber:212 blastBufferQueue is dead
2025-05-26 23:47:08.737 11795-11795 DeferrableSurface       com.chessvision.app                  D  surface closed,  useCount=0 closed=true androidx.camera.core.processing.SurfaceEdge$SettableSurface@32fd4a6
2025-05-26 23:47:08.738 11795-11795 DeferrableSurface       com.chessvision.app                  D  Surface terminated[total_surfaces=2, used_surfaces=2](androidx.camera.core.processing.SurfaceEdge$SettableSurface@32fd4a6}
2025-05-26 23:47:08.738 11795-11795 DeferrableSurface       com.chessvision.app                  D  use count-1,  useCount=1 closed=true androidx.camera.core.SurfaceRequest$2@6f49532
2025-05-26 23:47:08.741 11795-11795 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824}, mServedView=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824}
2025-05-26 23:47:08.742 11795-11795 ImeFocusController      com.chessvision.app                  V  onWindowFocus: androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-26 23:47:08.742 11795-11795 ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-26 23:47:08.742 11795-11795 ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824}, mServedView=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824}
2025-05-26 23:47:08.742 11795-11795 ImeFocusController      com.chessvision.app                  V  checkFocus: view=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} next=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} force=true package=com.chessvision.app
2025-05-26 23:47:08.765 11795-11806 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=826ms; Flags=0, FrameTimelineVsyncId=18431784, IntendedVsync=330355392589981, Vsync=330355392589981, InputEventId=0, HandleInputStart=330355397256690, AnimationStart=330355397267536, PerformTraversalsStart=330356077939844, DrawStart=330356170811767, FrameDeadline=330355412589981, FrameInterval=330355397220998, FrameStartTime=16666667, SyncQueued=330356195478844, SyncStart=330356195619151, IssueDrawCommandsStart=330356198101305, SwapBuffers=330356211048690, FrameCompleted=330356219447536, DequeueBufferDuration=53923, QueueBufferDuration=3007923, GpuCompleted=330356219447536, SwapBuffersCompleted=330356216751151, DisplayPresentTime=837119350016709062, 
2025-05-26 23:47:08.781 11795-11806 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=700ms; Flags=0, FrameTimelineVsyncId=18431805, IntendedVsync=330355525894339, Vsync=330356192561019, InputEventId=0, HandleInputStart=330356208919459, AnimationStart=330356208923844, PerformTraversalsStart=330356208926228, DrawStart=330356213272459, FrameDeadline=330355562561006, FrameInterval=330356208695151, FrameStartTime=16666667, SyncQueued=330356213609998, SyncStart=330356217059228, IssueDrawCommandsStart=330356217631151, SwapBuffers=330356222045690, FrameCompleted=330356229723459, DequeueBufferDuration=61000, QueueBufferDuration=1918846, GpuCompleted=330356229723459, SwapBuffersCompleted=330356225463844, DisplayPresentTime=847534082924551121, 
2025-05-26 23:47:08.789 11795-11807 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:47:08.850 11795-11807 BufferQueueProducer     com.chessvision.app                  E  [SurfaceView[com.chessvision.app/com.chessvision.app.MainActivity]#3(BLAST Consumer)3](id:2e1300000005,api:4,p:985,c:11795) queueBuffer: BufferQueue has been abandoned
2025-05-26 23:47:08.854 11795-11878 CaptureSession          com.chessvision.app                  D  CameraCaptureSession.onReady() OPENED
2025-05-26 23:47:10.818 11795-11795 MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-26 23:47:10.820 11795-11859 ChessAI                 com.chessvision.app                  D  🔍 Processing captured image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-26 23:47:10.823 11795-11859 ChessAI                 com.chessvision.app                  D  🔍 Processing chess board image with ONNX models (EXACT same as Python)...
2025-05-26 23:47:10.826 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔍 Processing with EXACT same models as Python...
2025-05-26 23:47:10.878 11795-11859 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-26 23:47:11.371 11795-11859 skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-26 23:47:11.373 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation (EXACT same model as Python)...
2025-05-26 23:47:11.397 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-26 23:47:11.466 11795-11803 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-26 23:47:11.468 11795-11803 System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-26 23:47:25.255 11795-11795 ImeFocusController      com.chessvision.app                  V  checkFocus: view=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} next=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} force=true package=com.chessvision.app
2025-05-26 23:47:25.268 11795-11795 ImeFocusController      com.chessvision.app                  V  checkFocus: view=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} next=androidx.compose.ui.platform.AndroidComposeView{9994a35 VFED..... .F...... 0,0-720,1600 aid=1073741824} force=true package=com.chessvision.app
2025-05-26 23:47:27.273 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-31.683664, 15.319078]
2025-05-26 23:47:27.273 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔧 Applying sigmoid activation (raw logits detected)
2025-05-26 23:47:27.376 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-26 23:47:27.377 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed (using EXACT same weights as Python)
2025-05-26 23:47:27.415 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection (EXACT same model as Python)...
2025-05-26 23:47:27.431 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 519168 values
2025-05-26 23:47:27.553 11795-11801 chessvision.ap          com.chessvision.app                  I  Background young concurrent copying GC freed 57KB AllocSpace bytes, 2(1708KB) LOS objects, 1% free, 16MB/16MB, paused 257us,154us total 118.399ms
2025-05-26 23:47:28.144 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 YOLO output shape: [1, 16, 3549]
2025-05-26 23:47:28.144 11795-11859 ONNXChessAI             com.chessvision.app                  D  📊 Processing 16 detections with 3549 features each
2025-05-26 23:47:28.145 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Filtered to 4 high-confidence detections
2025-05-26 23:47:28.145 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ YOLO detection completed (using EXACT same weights as Python)
2025-05-26 23:47:28.146 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Detected 4 pieces
2025-05-26 23:47:28.146 11795-11859 ONNXChessAI             com.chessvision.app                  D  ⏱️ Segmentation: 16042ms, Detection: 731ms
2025-05-26 23:47:28.146 11795-11859 ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 4 detected pieces...
2025-05-26 23:47:28.148 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:47:28.149 11795-11859 ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-26 23:47:28.149 11795-11859 ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-26 23:47:28.149 11795-11859 ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-26 23:47:28.149 11795-11859 ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-26 23:47:28.150 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-26 23:47:28.150 11795-11859 ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-26 23:47:28.150 11795-11859 ONNXChessAI             com.chessvision.app                  D  ✅ ONNX processing completed - EXACT same results as Python!
2025-05-26 23:47:28.150 11795-11859 ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:47:28.150 11795-11859 ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 17323ms
2025-05-26 23:47:28.151 11795-11859 ChessAI                 com.chessvision.app                  D  ✅ ONNX AI processing successful!
2025-05-26 23:47:28.152 11795-11859 ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:47:28.152 11795-11859 ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-26 23:47:28.153 11795-11859 ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 17323ms
2025-05-26 23:47:28.157 11795-11795 MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:47:28.157 11795-11795 MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-26 23:47:28.157 11795-11795 MainActivity            com.chessvision.app                  D  ⚡ Processing time: 17323ms
2025-05-26 23:47:28.270 11795-11801 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 77KB AllocSpace bytes, 5(10MB) LOS objects, 24% free, 6790KB/9053KB, paused 170us,232us total 125.969ms
2025-05-26 23:47:29.908 11795-11795 ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-26 23:47:30.042 11795-11806 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1849ms; Flags=0, FrameTimelineVsyncId=18432139, IntendedVsync=330375655521145, Vsync=330375655521145, InputEventId=0, HandleInputStart=330375656614614, AnimationStart=330375656619845, PerformTraversalsStart=330375811369999, DrawStart=330377264782230, FrameDeadline=330375675521145, FrameInterval=330375656587460, FrameStartTime=16666667, SyncQueued=330377381161614, SyncStart=330377381432691, IssueDrawCommandsStart=330377382753999, SwapBuffers=330377496405383, FrameCompleted=330377505700383, DequeueBufferDuration=170846, QueueBufferDuration=1614154, GpuCompleted=330377505700383, SwapBuffersCompleted=330377499772768, DisplayPresentTime=0, 
2025-05-26 23:47:30.084 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 108 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:47:30.342 11795-11801 chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 345KB AllocSpace bytes, 0(0B) LOS objects, 24% free, 8676KB/11MB, paused 125us,46us total 142.617ms
2025-05-26 23:47:30.727 11795-11806 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2420ms; Flags=0, FrameTimelineVsyncId=18432141, IntendedVsync=330375755493697, Vsync=330377555493733, InputEventId=0, HandleInputStart=330377565980768, AnimationStart=330377565985384, PerformTraversalsStart=330378132925922, DrawStart=330378133147845, FrameDeadline=330375792160364, FrameInterval=330377565759230, FrameStartTime=16666667, SyncQueued=330378162293691, SyncStart=330378162661384, IssueDrawCommandsStart=330378163587153, SwapBuffers=330378167850537, FrameCompleted=330378176789922, DequeueBufferDuration=39693, QueueBufferDuration=1584307, GpuCompleted=330378176789922, SwapBuffersCompleted=330378170710845, DisplayPresentTime=0, 
2025-05-26 23:47:30.784 11795-11795 Choreographer           com.chessvision.app                  I  Skipped 41 frames!  The application may be doing too much work on its main thread.
2025-05-26 23:47:30.857 11795-11806 OpenGLRenderer          com.chessvision.app                  I  Davey! duration=740ms; Flags=0, FrameTimelineVsyncId=18432157, IntendedVsync=330377572194940, Vsync=330378255528287, InputEventId=0, HandleInputStart=330378265579768, AnimationStart=330378265584999, PerformTraversalsStart=330378276320768, DrawStart=330378276521076, FrameDeadline=330377608861607, FrameInterval=330378265313614, FrameStartTime=16666667, SyncQueued=330378300100999, SyncStart=330378300437999, IssueDrawCommandsStart=330378300862307, SwapBuffers=330378304169614, FrameCompleted=330378312967230, DequeueBufferDuration=32846, QueueBufferDuration=1564615, GpuCompleted=330378312967230, SwapBuffersCompleted=330378306868460, DisplayPresentTime=0, 
