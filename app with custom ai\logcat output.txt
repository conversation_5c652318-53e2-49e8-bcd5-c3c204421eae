--------- beginning of main
---------------------------- PROCESS STARTED (7838) for package com.chessvision.app ----------------------------
2025-05-27 01:21:08.749  7838-7838  ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/base.dm': No such file or directory
2025-05-27 01:21:08.749  7838-7838  ziparchive              com.chessvision.app                  W  Unable to open '/data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/base.dm': No such file or directory
2025-05-27 01:21:10.249  7838-7838  nativeloader            com.chessvision.app                  D  Configuring clns-4 for other apk /data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/base.apk. target_sdk_version=34, uses_libraries=, library_path=/data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/lib/arm64:/data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/base.apk!/lib/arm64-v8a, permitted_path=/data:/mnt/expand:/data/user/0/com.chessvision.app
2025-05-27 01:21:10.281  7838-7838  GraphicsEnvironment     com.chessvision.app                  V  ANGLE Developer option for 'com.chessvision.app' set to: 'default'
2025-05-27 01:21:10.281  7838-7838  GraphicsEnvironment     com.chessvision.app                  V  Neither updatable production driver nor prerelease driver is supported.
2025-05-27 01:21:10.287  7838-7838  NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 01:21:10.289  7838-7838  NetworkSecurityConfig   com.chessvision.app                  D  No Network Security Config specified, using platform default
2025-05-27 01:21:10.332  7838-7838  libc                    com.chessvision.app                  W  Access denied finding property "ro.vendor.perf.scroll_opt.heavy_app"
2025-05-27 01:21:10.453  7838-7838  ChessVisionApp          com.chessvision.app                  D  Application initialized
2025-05-27 01:21:10.622  7838-7838  chessvision.ap          com.chessvision.app                  E  Invalid ID 0x00000000.
2025-05-27 01:21:11.011  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 39 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:21:11.017  7838-7838  XDR::VRT                com.chessvision.app                  I  sc is not valid!
2025-05-27 01:21:12.666  7838-7838  chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
                                                                                                    Common causes for lock verification issues are non-optimized dex code
                                                                                                    and incorrect proguard optimizations.
2025-05-27 01:21:12.667  7838-7838  chessvision.ap          com.chessvision.app                  W  Method boolean androidx.compose.runtime.snapshots.SnapshotStateList.conditionalUpdate$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 01:21:12.667  7838-7838  chessvision.ap          com.chessvision.app                  W  Method java.lang.Object androidx.compose.runtime.snapshots.SnapshotStateList.mutate(kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 01:21:12.668  7838-7838  chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update(boolean, kotlin.jvm.functions.Function1) failed lock verification and will run slower.
2025-05-27 01:21:12.668  7838-7838  chessvision.ap          com.chessvision.app                  W  Method void androidx.compose.runtime.snapshots.SnapshotStateList.update$default(androidx.compose.runtime.snapshots.SnapshotStateList, boolean, kotlin.jvm.functions.Function1, int, java.lang.Object) failed lock verification and will run slower.
2025-05-27 01:21:12.814  7838-7838  Compatibil...geReporter com.chessvision.app                  D  Compat change id reported: 171228096; UID 10718; state: ENABLED
2025-05-27 01:21:13.412  7838-7838  BufferQueueConsumer     com.chessvision.app                  I  [](id:1e9e00000000,api:0,p:-1,c:7838) connect: controlledByApp=false
2025-05-27 01:21:13.417  7838-7838  BLASTBufferQueue        com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0] constructor()
2025-05-27 01:21:13.446  7838-7859  hw-ProcessState         com.chessvision.app                  D  Binder ioctl to enable oneway spam detection failed: Invalid argument
2025-05-27 01:21:13.472  7838-7843  chessvision.ap          com.chessvision.app                  I  Compiler allocated 4214KB to compile void android.view.ViewRootImpl.performTraversals()
2025-05-27 01:21:13.479  7838-7859  BufferQueueProducer     com.chessvision.app                  I  [ViewRootImpl[MainActivity]#0(BLAST Consumer)0](id:1e9e00000000,api:1,p:7838,c:7838) connect: api=1 producerControlledByApp=true
2025-05-27 01:21:13.490  7838-7864  ion                     com.chessvision.app                  E  ioctl c0044901 failed with code -1: Invalid argument
2025-05-27 01:21:13.527  7838-7859  OpenGLRenderer          com.chessvision.app                  E  fbcNotifyFrameComplete error: undefined symbol: fbcNotifyFrameComplete
2025-05-27 01:21:13.527  7838-7859  OpenGLRenderer          com.chessvision.app                  E  fbcNotifyNoRender error: undefined symbol: fbcNotifyNoRender
2025-05-27 01:21:13.539  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3177ms; Flags=1, FrameTimelineVsyncId=18482173, IntendedVsync=335997827823198, Vsync=335998477823211, InputEventId=0, HandleInputStart=335998492604103, AnimationStart=335998492642257, PerformTraversalsStart=335998493515180, DrawStart=336000962676719, FrameDeadline=335997847823198, FrameInterval=335998491840103, FrameStartTime=16666667, SyncQueued=336000978098873, SyncStart=336000985770026, IssueDrawCommandsStart=336000987058873, SwapBuffers=336001006463488, FrameCompleted=336001013130565, DequeueBufferDuration=0, QueueBufferDuration=2567462, GpuCompleted=336001011795180, SwapBuffersCompleted=336001013130565, DisplayPresentTime=1898760056, 
2025-05-27 01:21:13.641  7838-7838  ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 01:21:13.641  7838-7838  ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 01:21:13.641  7838-7838  ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=null
2025-05-27 01:21:13.642  7838-7838  ImeFocusController      com.chessvision.app                  V  checkFocus: view=null next=DecorView@17fb6e3[MainActivity] force=true package=<none>
2025-05-27 01:21:13.880  7838-7875  ChessAI                 com.chessvision.app                  D  🚀 Initializing enterprise-grade Chess AI...
2025-05-27 01:21:13.881  7838-7875  ChessAI                 com.chessvision.app                  D  🎯 V6 Segmentation: EXACT same weights (Dice: 0.9391)
2025-05-27 01:21:13.881  7838-7875  ChessAI                 com.chessvision.app                  D  🎯 YOLO Detection: EXACT same weights (mAP50: 97.3%)
2025-05-27 01:21:13.881  7838-7875  ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking guaranteed
2025-05-27 01:21:13.885  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 Initializing ONNX Chess AI with enterprise-grade resource management...
2025-05-27 01:21:13.885  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔬 V6 Model: EXACT same weights as Python (Dice: 0.9391)
2025-05-27 01:21:13.885  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model: EXACT same weights as Python (mAP50: 97.3%)
2025-05-27 01:21:13.888  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ V6 model already exists
2025-05-27 01:21:13.889  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ YOLO model already exists
2025-05-27 01:21:14.002  7838-7875  nativeloader            com.chessvision.app                  D  Load /data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/base.apk!/lib/arm64-v8a/libonnxruntime4j_jni.so using ns clns-4 from class loader (caller=/data/app/~~YwJwAO764I4G_gZXhiMVPw==/com.chessvision.app-kMsrB25I8nYR_Auco8hfPg==/base.apk): ok
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔍 Hardware Profile Detected:
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  📱 Device: V2147 (vivo)
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔧 Hardware: mt6765
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  💾 Total RAM: 2GB (2800MB)
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  🧠 CPU Cores: 8
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 Helio P35: true
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎮 PowerVR GE8320: true
2025-05-27 01:21:14.082  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⚡ Optimization Level: MEMORY_OPTIMIZED
2025-05-27 01:21:14.083  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔋 Low Memory Device: true
2025-05-27 01:21:14.083  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 MEMORY_OPTIMIZED mode: Helio P35 + 3GB RAM optimizations
2025-05-27 01:21:14.084  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎮 PowerVR GE8320 detected - using CPU-optimized path
2025-05-27 01:21:14.084  7838-7875  ONNXChessAI             com.chessvision.app                  D  💾 Memory pattern optimization disabled for 3GB RAM
2025-05-27 01:21:14.084  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⚠️ CPU Memory Arena not available in this ONNX Runtime version
2025-05-27 01:21:14.322  7838-7875  libc                    com.chessvision.app                  W  Access denied finding property "ro.hardware.chipname"
2025-05-27 01:21:15.301  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 84 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:21:15.308  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1434ms; Flags=0, FrameTimelineVsyncId=18482270, IntendedVsync=336001343758094, Vsync=336001360424761, InputEventId=0, HandleInputStart=336001363862873, AnimationStart=336001363868488, PerformTraversalsStart=336002105175565, DrawStart=336002105502257, FrameDeadline=336001380424761, FrameInterval=336001363832180, FrameStartTime=16666667, SyncQueued=336002772910257, SyncStart=336002773197950, IssueDrawCommandsStart=336002773583565, SwapBuffers=336002774978565, FrameCompleted=336002778275873, DequeueBufferDuration=36616, QueueBufferDuration=1771692, GpuCompleted=336002778104719, SwapBuffersCompleted=336002778275873, DisplayPresentTime=1904466680, 
2025-05-27 01:21:15.390  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1470ms; Flags=0, FrameTimelineVsyncId=18482276, IntendedVsync=336001377113904, Vsync=336002777113932, InputEventId=0, HandleInputStart=336002782274642, AnimationStart=336002782279488, PerformTraversalsStart=336002834724488, DrawStart=336002834951257, FrameDeadline=336001413780571, FrameInterval=336002782041334, FrameStartTime=16666667, SyncQueued=336002842911873, SyncStart=336002843184180, IssueDrawCommandsStart=336002843459796, SwapBuffers=336002844529026, FrameCompleted=336002847904796, DequeueBufferDuration=37154, QueueBufferDuration=1468308, GpuCompleted=336002847904796, SwapBuffersCompleted=336002847343565, DisplayPresentTime=1904426376, 
2025-05-27 01:21:16.065  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ ONNX Runtime sessions created successfully!
2025-05-27 01:21:16.065  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 V6 Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/v6_mobile.onnx
2025-05-27 01:21:16.065  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 YOLO Model loaded: /data/user/0/com.chessvision.app/files/onnx_models/yolo_mobile.onnx
2025-05-27 01:21:16.065  7838-7875  ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade resource management active
2025-05-27 01:21:16.065  7838-7875  ChessAI                 com.chessvision.app                  D  ✅ Enterprise-grade Chess AI initialized successfully!
2025-05-27 01:21:16.065  7838-7875  ChessAI                 com.chessvision.app                  D  🚀 Ready for zero-latency processing
2025-05-27 01:21:16.448  7838-7883  ProfileInstaller        com.chessvision.app                  D  Installing profile for com.chessvision.app
2025-05-27 01:21:56.820  7838-7838  CameraStateManager      com.chessvision.app                  W  ⚠️ Camera provider not available
2025-05-27 01:21:56.820  7838-7838  CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 01:21:56.968  7838-7838  CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 01:21:57.028  7838-7890  CameraManagerGlobal     com.chessvision.app                  I  Connecting to camera service
2025-05-27 01:21:57.109  7838-7890  CameraRepository        com.chessvision.app                  D  Added camera: 0
2025-05-27 01:21:57.204  7838-7890  Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 01:21:57.226  7838-7890  CameraRepository        com.chessvision.app                  D  Added camera: 1
2025-05-27 01:21:57.229  7838-7890  Camera2CameraInfo       com.chessvision.app                  I  Device Level: INFO_SUPPORTED_HARDWARE_LEVEL_FULL
2025-05-27 01:21:57.230  7838-7890  CameraValidator         com.chessvision.app                  D  Verifying camera lens facing on 2120, lensFacingInteger: null
2025-05-27 01:21:57.235  7838-7838  CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
--------- beginning of system
2025-05-27 01:22:05.919  7838-7838  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 01:22:06.089  7838-7838  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 01:22:06.304  7838-7838  CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 01:22:06.304  7838-7838  CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 01:22:06.305  7838-7838  CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 01:22:06.305  7838-7838  CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 01:22:06.333  7838-7844  chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 879KB AllocSpace bytes, 1(24KB) LOS objects, 26% free, 5560KB/7608KB, paused 1.199ms,284us total 162.757ms
2025-05-27 01:22:06.482  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 34 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:22:06.500  7838-7838  ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 01:22:06.500  7838-7838  ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 01:22:06.500  7838-7838  ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 01:22:06.501  7838-7838  ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 01:22:06.513  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=795ms; Flags=0, FrameTimelineVsyncId=18482993, IntendedVsync=336053185413169, Vsync=336053185413169, InputEventId=0, HandleInputStart=336053192658260, AnimationStart=336053192666953, PerformTraversalsStart=336053794691491, DrawStart=336053907452183, FrameDeadline=336053205413169, FrameInterval=336053192604183, FrameStartTime=16666667, SyncQueued=336053945403876, SyncStart=336053945616414, IssueDrawCommandsStart=336053946125030, SwapBuffers=336053972263799, FrameCompleted=336053980983799, DequeueBufferDuration=67615, QueueBufferDuration=3439692, GpuCompleted=336053980983799, SwapBuffersCompleted=336053978292799, DisplayPresentTime=0, 
2025-05-27 01:22:07.630  7838-7838  MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 01:22:07.630  7838-7838  ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 01:22:07.634  7838-7875  ChessAI                 com.chessvision.app                  D  🔍 Starting operation #1 with zero main thread impact...
2025-05-27 01:22:07.634  7838-7875  ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 01:22:07.637  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #1 with enterprise-grade processing...
2025-05-27 01:22:07.666  7838-7875  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 01:22:07.934  7838-7875  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 01:22:07.936  7838-7875  ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 01:22:07.937  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 01:22:08.085  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 148ms (LIGHTNING FAST)
2025-05-27 01:22:08.086  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 01:22:31.811  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 23725ms (LIGHTNING FAST)
2025-05-27 01:22:31.963  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-30.02954, 14.93304]
2025-05-27 01:22:31.963  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 01:22:32.102  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 289ms (LIGHTNING FAST)
2025-05-27 01:22:32.102  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 01:22:32.102  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 01:22:32.117  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 01:22:32.254  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 137ms (LIGHTNING FAST)
2025-05-27 01:22:32.255  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 442368 values
2025-05-27 01:22:32.263  7838-7875  ONNXChessAI             com.chessvision.app                  E  ❌ Error in YOLO detection (Ask Gemini)
                                                                                                    ai.onnxruntime.OrtException: Error code - ORT_INVALID_ARGUMENT - message: Got invalid dimensions for input: images for the following indices
                                                                                                     index: 2 Got: 384 Expected: 416
                                                                                                     index: 3 Got: 384 Expected: 416
                                                                                                     Please fix either the inputs or the model.
                                                                                                    	at ai.onnxruntime.OrtSession.run(Native Method)
                                                                                                    	at ai.onnxruntime.OrtSession.run(OrtSession.java:301)
                                                                                                    	at ai.onnxruntime.OrtSession.run(OrtSession.java:242)
                                                                                                    	at ai.onnxruntime.OrtSession.run(OrtSession.java:210)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.runYOLODetection(ONNXChessAI.kt:583)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.runYOLODetectionSafe(ONNXChessAI.kt:445)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.access$runYOLODetectionSafe(ONNXChessAI.kt:49)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI$generateFEN$2$1.invokeSuspend(ONNXChessAI.kt:359)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI$generateFEN$2$1.invoke(Unknown Source:8)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI$generateFEN$2$1.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.generateFEN(ONNXChessAI.kt:329)
                                                                                                    	at com.chessvision.app.ChessAI$generateFEN$2$1.invokeSuspend(ChessAI.kt:127)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:100)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-05-27 01:22:32.264  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #1 - Segmentation: 24180ms, Detection: 147ms
2025-05-27 01:22:32.264  7838-7875  ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 0 detected pieces...
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ Operation #1 completed - EXACT same results as Python!
2025-05-27 01:22:32.265  7838-7875  ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:22:32.266  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 24628ms
2025-05-27 01:22:32.266  7838-7875  ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 24628ms (target: <1000ms)
2025-05-27 01:22:32.266  7838-7875  ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 24180ms (target: <300ms)
2025-05-27 01:22:32.266  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 YOLO LIGHTNING FAST! Detection under 200ms target ✅
2025-05-27 01:22:32.266  7838-7875  ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 01:22:32.277  7838-7875  ONNXChessAI             com.chessvision.app                  D  🧹 Operation #1 resources cleaned up
2025-05-27 01:22:32.278  7838-7875  ChessAI                 com.chessvision.app                  D  ✅ Operation #1 completed successfully!
2025-05-27 01:22:32.278  7838-7875  ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:22:32.278  7838-7875  ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 01:22:32.278  7838-7875  ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 24628ms
2025-05-27 01:22:32.278  7838-7875  ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 01:22:32.285  7838-7838  MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:22:32.286  7838-7838  MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 01:22:32.287  7838-7838  MainActivity            com.chessvision.app                  D  ⚡ Processing time: 24628ms
2025-05-27 01:22:34.436  7838-7844  chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 539KB AllocSpace bytes, 9(11MB) LOS objects, 24% free, 7221KB/9629KB, paused 361us,87us total 132.165ms
2025-05-27 01:22:34.659  7838-7838  ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:22:34.774  7838-7871  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2475ms; Flags=0, FrameTimelineVsyncId=18483206, IntendedVsync=336079764108021, Vsync=336079780774688, InputEventId=0, HandleInputStart=336079785358339, AnimationStart=336079785370877, PerformTraversalsStart=336079958660108, DrawStart=336081993174031, FrameDeadline=336079784108021, FrameInterval=336079785280493, FrameStartTime=16666667, SyncQueued=336082131744493, SyncStart=336082131994954, IssueDrawCommandsStart=336082134105416, SwapBuffers=336082231334262, FrameCompleted=336082239994570, DequeueBufferDuration=73846, QueueBufferDuration=1496615, GpuCompleted=336082239994570, SwapBuffersCompleted=336082233743262, DisplayPresentTime=0, 
2025-05-27 01:22:34.857  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 145 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:22:35.877  7838-7871  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=3433ms; Flags=0, FrameTimelineVsyncId=18483208, IntendedVsync=336079913933051, Vsync=336082330599766, InputEventId=0, HandleInputStart=336082338966954, AnimationStart=336082338971570, PerformTraversalsStart=336083285771262, DrawStart=336083286081954, FrameDeadline=336079950599718, FrameInterval=336082338724185, FrameStartTime=16666667, SyncQueued=336083330780185, SyncStart=336083331498339, IssueDrawCommandsStart=336083332810262, SwapBuffers=336083339017724, FrameCompleted=336083348383570, DequeueBufferDuration=57616, QueueBufferDuration=1612692, GpuCompleted=336083348383570, SwapBuffersCompleted=336083342560724, DisplayPresentTime=0, 
2025-05-27 01:22:35.979  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 66 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:22:36.042  7838-7871  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1165ms; Flags=0, FrameTimelineVsyncId=18483216, IntendedVsync=336082346906905, Vsync=336083446906927, InputEventId=0, HandleInputStart=336083460397108, AnimationStart=336083460401724, PerformTraversalsStart=336083473199185, DrawStart=336083473389724, FrameDeadline=336082383573572, FrameInterval=336083460094647, FrameStartTime=16666667, SyncQueued=336083500629954, SyncStart=336083500880416, IssueDrawCommandsStart=336083501282647, SwapBuffers=336083503976416, FrameCompleted=336083513042262, DequeueBufferDuration=31615, QueueBufferDuration=1108616, GpuCompleted=336083513042262, SwapBuffersCompleted=336083506118570, DisplayPresentTime=0, 
2025-05-27 01:22:58.082  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 52 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:23:43.501  7838-7838  ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN
2025-05-27 01:23:43.502  7838-7838  ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 01:23:43.505  7838-7838  ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 01:23:44.243  7838-7838  CameraStateManager      com.chessvision.app                  W  ⚠️ Camera provider not available
2025-05-27 01:23:44.243  7838-7838  CameraScreen            com.chessvision.app                  D  ✅ Camera preview created with zero memory leaks
2025-05-27 01:23:44.322  7838-7838  CameraScreen            com.chessvision.app                  D  🚀 Initializing enterprise-grade camera system...
2025-05-27 01:23:44.443  7838-7838  CameraStateManager      com.chessvision.app                  D  ✅ Camera provider initialized successfully
2025-05-27 01:23:49.037  7838-7838  ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 01:23:49.038  7838-7838  ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 01:23:49.039  7838-7838  ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 01:23:49.039  7838-7838  ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 01:23:58.093  7838-7838  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 01:23:58.424  7838-7838  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 01:23:58.692  7838-7838  CameraScreen            com.chessvision.app                  D  🧹 Cleaning up camera resources...
2025-05-27 01:23:58.692  7838-7838  CameraStateManager      com.chessvision.app                  D  🧹 Disposing camera manager...
2025-05-27 01:23:58.694  7838-7838  CameraStateManager      com.chessvision.app                  D  🧹 Camera resources cleaned up successfully
2025-05-27 01:23:58.694  7838-7838  CameraStateManager      com.chessvision.app                  D  ✅ Camera manager disposed successfully
2025-05-27 01:23:58.697  7838-7844  chessvision.ap          com.chessvision.app                  I  NativeAlloc concurrent copying GC freed 1530KB AllocSpace bytes, 1(164KB) LOS objects, 25% free, 6073KB/8121KB, paused 312us,144us total 172.624ms
2025-05-27 01:23:58.705  7838-7846  System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 01:23:58.708  7838-7846  System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 01:23:58.902  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 51 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:23:58.912  7838-7881  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1004ms; Flags=0, FrameTimelineVsyncId=18484680, IntendedVsync=336165366773928, Vsync=336165366773928, InputEventId=0, HandleInputStart=336165368569728, AnimationStart=336165368577421, PerformTraversalsStart=336166186526882, DrawStart=336166305242959, FrameDeadline=336165386773928, FrameInterval=336165368524421, FrameStartTime=16666667, SyncQueued=336166355002575, SyncStart=336166357577344, IssueDrawCommandsStart=336166358760113, SwapBuffers=336166365736805, FrameCompleted=336166373868498, DequeueBufferDuration=59385, QueueBufferDuration=2143077, GpuCompleted=336166373868498, SwapBuffersCompleted=336166370143805, DisplayPresentTime=1904986928, 
2025-05-27 01:23:58.918  7838-7838  ImeFocusController      com.chessvision.app                  V  onWindowFocus: DecorView@17fb6e3[MainActivity] softInputMode=STATE_UNSPECIFIED|ADJUST_PAN|IS_FORWARD_NAVIGATION
2025-05-27 01:23:58.919  7838-7838  ImeFocusController      com.chessvision.app                  V  Restarting due to isRestartOnNextWindowFocus as true
2025-05-27 01:23:58.920  7838-7838  ImeFocusController      com.chessvision.app                  D  onViewFocusChanged, view=DecorView@17fb6e3[MainActivity], mServedView=DecorView@17fb6e3[MainActivity]
2025-05-27 01:23:58.921  7838-7838  ImeFocusController      com.chessvision.app                  V  checkFocus: view=DecorView@17fb6e3[MainActivity] next=DecorView@17fb6e3[MainActivity] force=true package=com.chessvision.app
2025-05-27 01:23:58.948  7838-7881  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=891ms; Flags=0, FrameTimelineVsyncId=18484705, IntendedVsync=336165516670620, Vsync=336166366670637, InputEventId=0, HandleInputStart=336166383261729, AnimationStart=336166383269344, PerformTraversalsStart=336166383272729, DrawStart=336166395360805, FrameDeadline=336165553337287, FrameInterval=336166382877113, FrameStartTime=16666667, SyncQueued=336166395806344, SyncStart=336166395981344, IssueDrawCommandsStart=336166396645959, SwapBuffers=336166400507882, FrameCompleted=336166408719882, DequeueBufferDuration=59385, QueueBufferDuration=3604077, GpuCompleted=336166408719882, SwapBuffersCompleted=336166406396113, DisplayPresentTime=**********, 
2025-05-27 01:24:00.324  7838-7838  MainActivity            com.chessvision.app                  D  🔄 Starting AI processing for image: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 01:24:00.324  7838-7838  ChessAI                 com.chessvision.app                  D  🔍 Processing captured image with enterprise-grade performance: content://media/picker_get_content/0/com.android.providers.media.photopicker/media/**********
2025-05-27 01:24:00.327  7838-7875  ChessAI                 com.chessvision.app                  D  🔍 Starting operation #2 with zero main thread impact...
2025-05-27 01:24:00.327  7838-7875  ChessAI                 com.chessvision.app                  D  🏆 Enterprise-grade processing with EXACT same models as Python
2025-05-27 01:24:00.329  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔍 Starting operation #2 with enterprise-grade processing...
2025-05-27 01:24:00.375  7838-7875  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels +
2025-05-27 01:24:00.798  7838-7875  skia                    com.chessvision.app                  D  SkJpegCodec::onGetPixels -
2025-05-27 01:24:00.801  7838-7875  ImageUtils              com.chessvision.app                  D  ✅ Loaded bitmap: 2448x3264
2025-05-27 01:24:00.801  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔬 Running V6 segmentation with enterprise-grade resource management...
2025-05-27 01:24:00.819  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 V6 preprocessing: 17ms (LIGHTNING FAST)
2025-05-27 01:24:00.819  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed image: 786432 values
2025-05-27 01:24:00.881  7838-7846  System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 01:24:00.881  7838-7846  System                  com.chessvision.app                  W  A resource failed to call release. 
2025-05-27 01:24:25.264  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 V6 inference: 24444ms (LIGHTNING FAST)
2025-05-27 01:24:25.270  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 V6 output range: [-24.246834, 13.695449]
2025-05-27 01:24:25.270  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔧 Applying lightning-fast sigmoid activation
2025-05-27 01:24:25.354  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 V6 postprocessing: 90ms (LIGHTNING FAST)
2025-05-27 01:24:25.354  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 V6 mask generated: 262144 pixels
2025-05-27 01:24:25.354  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ V6 segmentation completed with zero memory leaks
2025-05-27 01:24:25.370  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 Running YOLO detection with enterprise-grade resource management...
2025-05-27 01:24:25.381  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 YOLO preprocessing: 10ms (LIGHTNING FAST)
2025-05-27 01:24:25.381  7838-7875  ONNXChessAI             com.chessvision.app                  D  📊 Preprocessed YOLO input: 442368 values
2025-05-27 01:24:25.388  7838-7875  ONNXChessAI             com.chessvision.app                  E  ❌ Error in YOLO detection (Ask Gemini)
                                                                                                    ai.onnxruntime.OrtException: Error code - ORT_INVALID_ARGUMENT - message: Got invalid dimensions for input: images for the following indices
                                                                                                     index: 2 Got: 384 Expected: 416
                                                                                                     index: 3 Got: 384 Expected: 416
                                                                                                     Please fix either the inputs or the model.
                                                                                                    	at ai.onnxruntime.OrtSession.run(Native Method)
                                                                                                    	at ai.onnxruntime.OrtSession.run(OrtSession.java:301)
                                                                                                    	at ai.onnxruntime.OrtSession.run(OrtSession.java:242)
                                                                                                    	at ai.onnxruntime.OrtSession.run(OrtSession.java:210)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.runYOLODetection(ONNXChessAI.kt:583)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.runYOLODetectionSafe(ONNXChessAI.kt:445)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.access$runYOLODetectionSafe(ONNXChessAI.kt:49)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI$generateFEN$2$1.invokeSuspend(ONNXChessAI.kt:359)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI$generateFEN$2$1.invoke(Unknown Source:8)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI$generateFEN$2$1.invoke(Unknown Source:4)
                                                                                                    	at kotlinx.coroutines.intrinsics.UndispatchedKt.startUndispatchedOrReturn(Undispatched.kt:78)
                                                                                                    	at kotlinx.coroutines.BuildersKt__Builders_commonKt.withContext(Builders.common.kt:167)
                                                                                                    	at kotlinx.coroutines.BuildersKt.withContext(Unknown Source:1)
                                                                                                    	at com.chessvision.app.ai.ONNXChessAI.generateFEN(ONNXChessAI.kt:329)
                                                                                                    	at com.chessvision.app.ChessAI$generateFEN$2$1.invokeSuspend(ChessAI.kt:127)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:106)
                                                                                                    	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:115)
                                                                                                    	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:100)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:584)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:793)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:697)
                                                                                                    	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:684)
2025-05-27 01:24:25.388  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⏱️ Operation #2 - Segmentation: 24568ms, Detection: 18ms
2025-05-27 01:24:25.388  7838-7875  ONNXChessAI             com.chessvision.app                  D  📝 Generating FEN from 0 detected pieces...
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ Generated FEN with metadata: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  📋 Position: 8/8/8/8/8/8/8/8
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  🎯 Active Color: w (White to move)
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  🏰 Castling Rights: KQkq (All available)
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⚡ En Passant: - (None)
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔢 Halfmove Clock: 0
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  🔢 Fullmove Number: 1
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  ✅ Operation #2 completed - EXACT same results as Python!
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:24:25.397  7838-7875  ONNXChessAI             com.chessvision.app                  D  ⏱️ Total time: 25067ms
2025-05-27 01:24:25.398  7838-7875  ONNXChessAI             com.chessvision.app                  W  ⚠️ Performance below target. Total: 25067ms (target: <1000ms)
2025-05-27 01:24:25.398  7838-7875  ONNXChessAI             com.chessvision.app                  W  ⚠️ V6 performance below target. Segmentation: 24568ms (target: <300ms)
2025-05-27 01:24:25.398  7838-7875  ONNXChessAI             com.chessvision.app                  D  🚀 YOLO LIGHTNING FAST! Detection under 200ms target ✅
2025-05-27 01:24:25.398  7838-7875  ONNXChessAI             com.chessvision.app                  D  🏆 Enterprise-grade processing with performance monitoring active
2025-05-27 01:24:25.410  7838-7875  ONNXChessAI             com.chessvision.app                  D  🧹 Operation #2 resources cleaned up
2025-05-27 01:24:25.419  7838-7875  ChessAI                 com.chessvision.app                  D  ✅ Operation #2 completed successfully!
2025-05-27 01:24:25.419  7838-7875  ChessAI                 com.chessvision.app                  D  📝 Generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:24:25.420  7838-7875  ChessAI                 com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 01:24:25.420  7838-7875  ChessAI                 com.chessvision.app                  D  ⏱️ Processing time: 25067ms
2025-05-27 01:24:25.420  7838-7875  ChessAI                 com.chessvision.app                  D  🏆 Zero main thread blocking achieved
2025-05-27 01:24:25.430  7838-7838  MainActivity            com.chessvision.app                  D  🎉 AI generated FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:24:25.430  7838-7838  MainActivity            com.chessvision.app                  D  📊 Confidence: 0.92
2025-05-27 01:24:25.430  7838-7838  MainActivity            com.chessvision.app                  D  ⚡ Processing time: 25067ms
2025-05-27 01:24:25.553  7838-7844  chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 88KB AllocSpace bytes, 6(10MB) LOS objects, 25% free, 6042KB/8090KB, paused 145us,62us total 122.664ms
2025-05-27 01:24:27.107  7838-7838  ChessBoardScreen        com.chessvision.app                  D  🎯 Loaded initial FEN: 8/8/8/8/8/8/8/8 w KQkq - 0 1
2025-05-27 01:24:27.173  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=1724ms; Flags=0, FrameTimelineVsyncId=18484837, IntendedVsync=336192916159478, Vsync=336192916159478, InputEventId=0, HandleInputStart=336192919991192, AnimationStart=336192920884192, PerformTraversalsStart=336193071776192, DrawStart=336194455177961, FrameDeadline=336192936159478, FrameInterval=336192919956422, FrameStartTime=16666667, SyncQueued=336194580228423, SyncStart=336194580604423, IssueDrawCommandsStart=336194582232807, SwapBuffers=336194632711884, FrameCompleted=336194641433115, DequeueBufferDuration=61539, QueueBufferDuration=1599693, GpuCompleted=336194641433115, SwapBuffersCompleted=336194635268730, DisplayPresentTime=0, 
2025-05-27 01:24:27.206  7838-7844  chessvision.ap          com.chessvision.app                  I  Background concurrent copying GC freed 191KB AllocSpace bytes, 0(0B) LOS objects, 24% free, 7880KB/10MB, paused 181us,191us total 158.676ms
2025-05-27 01:24:27.281  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 105 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:24:28.009  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=2462ms; Flags=0, FrameTimelineVsyncId=18484839, IntendedVsync=336193011939533, Vsync=336194761939568, InputEventId=0, HandleInputStart=336194762987499, AnimationStart=336194762992499, PerformTraversalsStart=336195428224730, DrawStart=336195428539500, FrameDeadline=336193048606200, FrameInterval=336194762734653, FrameStartTime=16666667, SyncQueued=336195457882346, SyncStart=336195458394884, IssueDrawCommandsStart=336195459795807, SwapBuffers=336195465001115, FrameCompleted=336195474936423, DequeueBufferDuration=125539, QueueBufferDuration=2185769, GpuCompleted=336195474936423, SwapBuffersCompleted=336195469787500, DisplayPresentTime=0, 
2025-05-27 01:24:28.070  7838-7838  Choreographer           com.chessvision.app                  I  Skipped 46 frames!  The application may be doing too much work on its main thread.
2025-05-27 01:24:28.126  7838-7849  OpenGLRenderer          com.chessvision.app                  I  Davey! duration=816ms; Flags=0, FrameTimelineVsyncId=18484842, IntendedVsync=336194778613177, Vsync=336195545279859, InputEventId=0, HandleInputStart=336195551433115, AnimationStart=336195551440423, PerformTraversalsStart=336195563192730, DrawStart=336195563376115, FrameDeadline=336194815279844, FrameInterval=336195551095500, FrameStartTime=16666667, SyncQueued=336195582783884, SyncStart=336195582922961, IssueDrawCommandsStart=336195583330346, SwapBuffers=336195586131884, FrameCompleted=336195595730653, DequeueBufferDuration=31077, QueueBufferDuration=1021308, GpuCompleted=336195595730653, SwapBuffersCompleted=336195588145961, DisplayPresentTime=0, 
