"""
Export chess board detection model to ONNX format.

This script loads a trained model and exports it to ONNX format for deployment.
ONNX models can be deployed on various platforms and frameworks.
"""

import os
import argparse
import torch
import numpy as np
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from utils.model_optimization import export_to_onnx
from config import MODELS_DIR, INPUT_SIZE


def main():
    parser = argparse.ArgumentParser(description='Export chess board detection model to ONNX')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'deployment'),
                        help='Output directory for ONNX model')
    parser.add_argument('--batch_size', type=int, default=1,
                        help='Batch size for the exported model')
    parser.add_argument('--opset_version', type=int, default=12,
                        help='ONNX opset version')
    parser.add_argument('--dynamic_batch', action='store_true',
                        help='Export with dynamic batch dimension')
    parser.add_argument('--dynamic_size', action='store_true',
                        help='Export with dynamic height and width dimensions')
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Initialize model
    print("Initializing model...")
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    
    # Load checkpoint
    print(f"Loading checkpoint from {args.checkpoint}...")
    checkpoint = torch.load(args.checkpoint, map_location=device)
    model.load_state_dict(checkpoint)
    
    # Set model to evaluation mode
    model.eval()
    model = model.to(device)
    
    # Create sample input
    height, width = INPUT_SIZE
    dummy_input = torch.randn(args.batch_size, 3, height, width, device=device)
    
    # Define input and output names
    input_names = ['input']
    output_names = ['segmentation', 'heatmaps']
    
    # Define dynamic axes if needed
    dynamic_axes = None
    if args.dynamic_batch or args.dynamic_size:
        dynamic_axes = {}
        if args.dynamic_batch:
            dynamic_axes['input'] = {0: 'batch_size'}
            dynamic_axes['segmentation'] = {0: 'batch_size'}
            dynamic_axes['heatmaps'] = {0: 'batch_size'}
        
        if args.dynamic_size:
            if 'input' not in dynamic_axes:
                dynamic_axes['input'] = {}
                dynamic_axes['segmentation'] = {}
                dynamic_axes['heatmaps'] = {}
            
            dynamic_axes['input'].update({2: 'height', 3: 'width'})
            dynamic_axes['segmentation'].update({2: 'height', 3: 'width'})
            dynamic_axes['heatmaps'].update({2: 'height', 3: 'width'})
    
    # Define output path
    model_name = os.path.splitext(os.path.basename(args.checkpoint))[0]
    output_path = os.path.join(args.output_dir, f"{model_name}.onnx")
    
    # Export model to ONNX
    print(f"Exporting model to ONNX format...")
    export_to_onnx(
        model=model,
        sample_input=dummy_input,
        output_path=output_path,
        input_names=input_names,
        output_names=output_names,
        dynamic_axes=dynamic_axes
    )
    
    print(f"Model successfully exported to: {output_path}")
    
    # Verify the model
    try:
        import onnx
        onnx_model = onnx.load(output_path)
        onnx.checker.check_model(onnx_model)
        print("ONNX model verified successfully!")
    except ImportError:
        print("ONNX package not installed. Skipping verification.")
    except Exception as e:
        print(f"Error verifying ONNX model: {e}")


if __name__ == "__main__":
    main()
