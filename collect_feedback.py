import sys
import os
import cv2
import numpy as np
import json
import argparse
from ultralytics import YOLO
import matplotlib.pyplot as plt
from datetime import datetime

# Class names
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

# Define colors for each class (BGR format for OpenCV)
COLORS = {
    'white_pawn': (255, 255, 0),     # <PERSON>an
    'white_knight': (255, 0, 255),   # Magenta
    'white_bishop': (0, 255, 255),   # Yellow
    'white_rook': (0, 0, 255),       # Red
    'white_queen': (255, 0, 0),      # Blue
    'white_king': (0, 255, 0),       # Green
    'black_pawn': (128, 255, 0),     # <PERSON>an
    'black_knight': (255, 128, 255), # Light Magenta
    'black_bishop': (128, 255, 255), # Light Yellow
    'black_rook': (128, 128, 255),   # Light Red
    'black_queen': (255, 128, 128),  # Light Blue
    'black_king': (128, 255, 128),   # Light Green
}

def load_feedback_database(db_path):
    """Load the feedback database or create a new one if it doesn't exist"""
    if os.path.exists(db_path):
        with open(db_path, 'r') as f:
            return json.load(f)
    else:
        return {
            "feedback_items": [],
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_items": 0
            }
        }

def save_feedback_database(db, db_path):
    """Save the feedback database"""
    db["metadata"]["last_updated"] = datetime.now().isoformat()
    db["metadata"]["total_items"] = len(db["feedback_items"])
    
    with open(db_path, 'w') as f:
        json.dump(db, f, indent=2)

def run_inference(model_path, image_path):
    """Run inference on an image and return the results"""
    model = YOLO(model_path)
    results = model.predict(image_path, conf=0.25)[0]
    
    # Get the original image
    img = cv2.imread(image_path)
    
    # Get detection data
    boxes = results.boxes.xyxy.cpu().numpy()
    cls_ids = results.boxes.cls.cpu().numpy().astype(int)
    confs = results.boxes.conf.cpu().numpy()
    
    return img, boxes, cls_ids, confs

def draw_detections(img, boxes, cls_ids, confs):
    """Draw bounding boxes on the image"""
    img_copy = img.copy()
    
    for box, cls_id, conf in zip(boxes, cls_ids, confs):
        x1, y1, x2, y2 = box.astype(int)
        color = COLORS[CLASS_NAMES[cls_id]]
        
        # Draw box
        cv2.rectangle(img_copy, (x1, y1), (x2, y2), color, 2)
        
        # Add label
        label = f"{CLASS_NAMES[cls_id]} ({conf:.2f})"
        cv2.putText(img_copy, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    return img_copy

def collect_feedback(model_path, image_path, db_path, output_dir):
    """Collect feedback on model predictions"""
    # Load the feedback database
    db = load_feedback_database(db_path)
    
    # Run inference
    img, boxes, cls_ids, confs = run_inference(model_path, image_path)
    
    # Draw detections
    img_with_boxes = draw_detections(img, boxes, cls_ids, confs)
    
    # Save the image with detections
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.basename(image_path)
    output_path = os.path.join(output_dir, f"detection_{base_name}")
    cv2.imwrite(output_path, img_with_boxes)
    
    print(f"Detections saved to {output_path}")
    print("\nDetected pieces:")
    
    # Display detections and collect feedback
    for i, (box, cls_id, conf) in enumerate(zip(boxes, cls_ids, confs)):
        x1, y1, x2, y2 = box.astype(int)
        print(f"{i+1}. {CLASS_NAMES[cls_id]} (confidence: {conf:.2f}) at position ({x1}, {y1}, {x2}, {y2})")
    
    print("\nEnter feedback for incorrect detections (or press Enter to skip):")
    print("Format: <detection_number> <correct_class_name> (e.g., '3 black_bishop')")
    print("Type 'done' when finished")
    
    feedback_items = []
    while True:
        feedback = input("> ").strip()
        if feedback.lower() == 'done' or feedback == '':
            break
        
        try:
            parts = feedback.split(' ', 1)
            if len(parts) != 2:
                print("Invalid format. Please use: <detection_number> <correct_class_name>")
                continue
            
            idx = int(parts[0]) - 1
            correct_class = parts[1].strip()
            
            if idx < 0 or idx >= len(cls_ids):
                print(f"Invalid detection number. Please use 1-{len(cls_ids)}")
                continue
            
            if correct_class not in CLASS_NAMES:
                print(f"Invalid class name. Please use one of: {', '.join(CLASS_NAMES)}")
                continue
            
            # Get the detection details
            box = boxes[idx].tolist()
            predicted_class = CLASS_NAMES[cls_ids[idx]]
            confidence = float(confs[idx])
            
            # Create a feedback item
            feedback_item = {
                "image_path": image_path,
                "box": box,
                "predicted_class": predicted_class,
                "correct_class": correct_class,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat()
            }
            
            feedback_items.append(feedback_item)
            print(f"Feedback recorded: Detection {idx+1} should be {correct_class} (not {predicted_class})")
            
        except Exception as e:
            print(f"Error processing feedback: {e}")
    
    # Add feedback items to the database
    if feedback_items:
        db["feedback_items"].extend(feedback_items)
        save_feedback_database(db, db_path)
        print(f"\n{len(feedback_items)} feedback items saved to {db_path}")
    else:
        print("\nNo feedback collected.")
    
    return feedback_items

def main():
    parser = argparse.ArgumentParser(description="Collect feedback on chess piece detection")
    parser.add_argument("--model", required=True, help="Path to the YOLO model")
    parser.add_argument("--image", required=True, help="Path to the image to analyze")
    parser.add_argument("--db", default="feedback_database.json", help="Path to the feedback database")
    parser.add_argument("--output", default="feedback_output", help="Directory to save output images")
    
    args = parser.parse_args()
    
    collect_feedback(args.model, args.image, args.db, args.output)

if __name__ == "__main__":
    main()
