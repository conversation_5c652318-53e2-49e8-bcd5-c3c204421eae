package com.chessvision.app

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.painterResource
import androidx.compose.foundation.Image
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.geometry.Size

@Composable
fun rememberChessBoardState(): ChessBoardState {
    return remember { ChessBoardState() }
}

@Composable
fun InteractiveChessBoard(
    modifier: Modifier = Modifier,
    boardState: ChessBoardState = rememberChessBoardState(),
    onFENChanged: (String) -> Unit = {}
) {
    LaunchedEffect(boardState.currentFENString) {
        onFENChanged(boardState.currentFENString)
    }

    ChessBoardGrid(
        boardState = boardState,
        modifier = modifier
    )
}

@Composable
fun ChessBoardGrid(
    boardState: ChessBoardState,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .padding(horizontal = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .onGloballyPositioned { coordinates ->
                    val bounds = Rect(
                        offset = Offset(
                            coordinates.positionInRoot().x,
                            coordinates.positionInRoot().y
                        ),
                        size = Size(
                            coordinates.size.width.toFloat(),
                            coordinates.size.height.toFloat()
                        )
                    )
                    boardState.updateBoardBounds(bounds)
                }
        ) {
            // Chess board background PNG
            Image(
                painter = painterResource(id = R.drawable.chessboard_background),
                contentDescription = "Chess Board Background",
                modifier = Modifier.fillMaxSize(),
                contentScale = androidx.compose.ui.layout.ContentScale.FillBounds
            )

            // 8x8 Grid of squares that perfectly divide the board
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                val legalMoves = boardState.selectedSquare?.let { selected ->
                    if (!boardState.isEditMode) {
                        boardState.getLegalMoves(selected)
                    } else emptyList()
                } ?: emptyList()

                val rankRange = if (boardState.isBoardFlipped) (0..7) else (7 downTo 0)
                val fileRange = if (boardState.isBoardFlipped) (7 downTo 0) else (0..7)

                for (rank in rankRange) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        for (file in fileRange) {
                            val currentPosition = ChessPosition(file, rank)
                            ChessSquare(
                                position = currentPosition,
                                piece = boardState.board[rank][file],
                                isSelected = boardState.selectedSquare == currentPosition,
                                onClick = { boardState.onSquareClick(currentPosition) },
                                modifier = Modifier
                                    .weight(1f)
                                    .fillMaxHeight(),
                                boardState = boardState,
                                isLegalMove = legalMoves.contains(currentPosition)
                            )
                        }
                    }
                }
            }

            // Floating dragged piece overlay
            if (boardState.isDragging) {
                val draggedPiece = boardState.draggedPiece ?: boardState.draggedFromTrayPiece
                draggedPiece?.let { piece ->
                    Image(
                        painter = painterResource(id = piece.drawableRes),
                        contentDescription = "Dragged piece",
                        modifier = Modifier
                            .size(42.dp)
                            .offset {
                                IntOffset(
                                    (boardState.dragOffset.x - 21).toInt(),
                                    (boardState.dragOffset.y - 21).toInt()
                                )
                            }
                            .graphicsLayer {
                                alpha = 0.9f
                                scaleX = 1.25f
                                scaleY = 1.25f
                            }
                    )
                }
            }
        }
    }
}

@Composable
fun ChessSquare(
    position: ChessPosition,
    piece: ChessPiece?,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    boardState: ChessBoardState? = null,
    isLegalMove: Boolean = false
) {
    val backgroundColor by animateColorAsState(
        targetValue = when {
            isSelected -> Color(0xFF769656).copy(alpha = 0.8f)
            isLegalMove -> Color(0xFF90EE90).copy(alpha = 0.7f)
            boardState?.lastMovedSquare == position -> Color(0xFFFFD700).copy(alpha = 0.6f)
            else -> Color.Transparent
        },
        animationSpec = tween(300, easing = EaseOutCubic),
        label = "square_background"
    )

    val scale by animateFloatAsState(
        targetValue = if (boardState?.lastMovedSquare == position && boardState.isAnimatingPiece) 1.1f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "piece_placement_scale"
    )

    LaunchedEffect(boardState?.animationTrigger) {
        if (boardState?.isAnimatingPiece == true) {
            kotlinx.coroutines.delay(500)
            boardState.isAnimatingPiece = false
        }
    }

    Box(
        modifier = modifier
            .background(backgroundColor)
            .clickable(onClick = onClick)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .let { mod ->
                if (piece != null) {
                    mod.pointerInput(piece, position) {
                        detectDragGestures(
                            onDragStart = { _ ->
                                boardState?.startDrag(piece, position)
                                val squareSize = size.width / 8f
                                val initialOffset = Offset(
                                    (position.file + 0.5f) * squareSize,
                                    ((7 - position.rank) + 0.5f) * squareSize
                                )
                                boardState?.updateDragPosition(initialOffset)
                            },
                            onDragEnd = {
                                boardState?.endDrag()
                            },
                            onDrag = { _, dragAmount ->
                                boardState?.let {
                                    it.updateDragPosition(it.dragOffset + dragAmount)
                                }
                            }
                        )
                    }
                } else mod
            },
        contentAlignment = Alignment.Center
    ) {
        // Legal move indicator for empty squares
        AnimatedVisibility(
            visible = piece == null && isLegalMove,
            enter = scaleIn(),
            exit = scaleOut()
        ) {
            Box(
                modifier = Modifier
                    .size(14.dp)
                    .background(
                        Color(0xFF4a5c2a),
                        CircleShape
                    )
            )
        }

        // Piece display
        piece?.let { chessPiece ->
            val isDraggedPiece = boardState?.isDragging == true && boardState.draggedPiece == chessPiece

            AnimatedVisibility(
                visible = !isDraggedPiece,
                enter = fadeIn(animationSpec = tween(200)),
                exit = fadeOut(animationSpec = tween(200))
            ) {
                Image(
                    painter = painterResource(id = chessPiece.drawableRes),
                    contentDescription = "${chessPiece.color} ${chessPiece.type}",
                    modifier = Modifier
                        .size(36.dp)
                        .let { mod ->
                            if (boardState?.lastMovedSquare == position && boardState.isAnimatingPiece) {
                                mod.graphicsLayer {
                                    scaleX = 1.1f
                                    scaleY = 1.1f
                                }
                            } else mod
                        }
                )
            }
        }
    }
}

@Composable
fun FENDisplay(
    fen: String,
    onFenChanged: (String) -> Unit
) {
    var editableFen by remember { mutableStateOf(fen) }
    var isEditing by remember { mutableStateOf(false) }

    LaunchedEffect(fen) {
        if (!isEditing) {
            editableFen = fen
        }
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF262421).copy(alpha = 0.95f)
        ),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Current Position (FEN)",
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
                    color = Color.White
                )

                Card(
                    onClick = {
                        if (isEditing) {
                            onFenChanged(editableFen)
                            isEditing = false
                        } else {
                            isEditing = true
                        }
                    },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isEditing) Color(0xFF769656) else Color(0xFF3a3a3a)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Box(
                        modifier = Modifier.padding(10.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = if (isEditing) Icons.Default.Check else Icons.Default.Edit,
                            contentDescription = if (isEditing) "Save" else "Edit",
                            modifier = Modifier.size(18.dp),
                            tint = Color.White
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            if (isEditing) {
                OutlinedTextField(
                    value = editableFen,
                    onValueChange = { editableFen = it },
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                        color = Color.White
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color(0xFFE0E0E0),
                        focusedBorderColor = Color(0xFF769656),
                        unfocusedBorderColor = Color(0xFF3a3a3a),
                        cursorColor = Color(0xFF769656)
                    ),
                    shape = RoundedCornerShape(12.dp)
                )
            } else {
                Text(
                    text = editableFen,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFE0E0E0),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Color(0xFF1a1a1a).copy(alpha = 0.8f),
                            RoundedCornerShape(12.dp)
                        )
                        .padding(16.dp)
                        .clickable { isEditing = true }
                )
            }
        }
    }
}
