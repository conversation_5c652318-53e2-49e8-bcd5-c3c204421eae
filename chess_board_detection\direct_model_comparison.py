"""
Script to directly compare model predictions in their native 256x256 space.
This shows exactly how the models detect corners in the input space they were trained on.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Get original dimensions
    original_height, original_width = image.shape[:2]
    
    # Calculate aspect ratio
    aspect = original_width / original_height
    
    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)
    
    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]
    
    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)
    
    return image_final

def enhance_image(image):
    """
    Enhance image colors and contrast similar to training augmentations.
    """
    # 1. Adaptive contrast enhancement (similar to CLAHE in training)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    l, a, b = cv2.split(lab)
    l = clahe.apply(l)
    lab = cv2.merge([l, a, b])
    image_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)
    
    # 2. Gamma correction (similar to RandomGamma in training)
    # Use a gamma value that enhances details without over-brightening
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)]).astype("uint8")
    image_enhanced = cv2.LUT(image_enhanced, table)
    
    # 3. Saturation enhancement (similar to HueSaturationValue in training)
    hsv = cv2.cvtColor(image_enhanced, cv2.COLOR_RGB2HSV)
    h, s, v = cv2.split(hsv)
    s = np.clip(s * 1.2, 0, 255).astype(np.uint8)  # Increase saturation by 20%
    hsv = cv2.merge([h, s, v])
    image_enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)
    
    return image_enhanced

def normalize_for_model(image):
    """
    Normalize image for model input using same normalization as during training.
    """
    # Convert to PIL Image first
    image_pil = Image.fromarray(image)
    
    # Use torchvision transforms which handle the data type correctly
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transform and add batch dimension
    image_tensor = transform(image_pil).unsqueeze(0)
    
    return image_tensor

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']
    
    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)
    
    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []
    
    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))
    
    return segmentation, heatmaps, keypoints

def visualize_model_comparison(image, model_results, output_path):
    """
    Create a visualization comparing model predictions in the 256x256 space.
    """
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 10))
    fig.suptitle('Model Comparison in 256x256 Input Space', fontsize=16)
    
    # Display the preprocessed image
    ax.imshow(image)
    
    # Define colors and markers for different models
    styles = {
        "Phase2_Epoch16": {"color": "red", "marker": "x", "linestyle": "-", "linewidth": 2},
        "Phase3_Epoch8": {"color": "blue", "marker": "+", "linestyle": "--", "linewidth": 2}
    }
    
    # Corner names
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    
    # Plot keypoints and lines for each model
    legend_elements = []
    
    for model_name, results in model_results.items():
        style = styles[model_name]
        keypoints = results['keypoints']
        
        # Plot keypoints
        xs = []
        ys = []
        for i, (x, y, conf) in enumerate(keypoints):
            xs.append(x)
            ys.append(y)
            
            # Plot keypoint
            ax.scatter(x, y, c=style["color"], marker=style["marker"], 
                      s=100, linewidths=2)
            
            # Add label with coordinates and confidence
            ax.text(x+5, y+5, f"{corner_names[i]}\n({int(x)}, {int(y)})\nConf: {conf:.3f}", 
                   color=style["color"], fontsize=8,
                   bbox=dict(facecolor='black', alpha=0.7))
        
        # Add the first point again to close the polygon
        xs.append(xs[0])
        ys.append(ys[0])
        
        # Plot lines connecting the keypoints
        ax.plot(xs, ys, color=style["color"], 
               linestyle=style["linestyle"], 
               linewidth=style["linewidth"], alpha=0.7)
        
        # Add to legend
        legend_elements.append(plt.Line2D([0], [0], color=style["color"], 
                                         marker=style["marker"], 
                                         linestyle=style["linestyle"],
                                         linewidth=style["linewidth"],
                                         markersize=10,
                                         label=f"{model_name}"))
    
    # Add legend
    ax.legend(handles=legend_elements, loc='upper right')
    
    # Calculate distances between models
    if len(model_results) > 1:
        model_names = list(model_results.keys())
        model1_name = model_names[0]
        model2_name = model_names[1]
        
        model1_keypoints = model_results[model1_name]['keypoints']
        model2_keypoints = model_results[model2_name]['keypoints']
        
        distances = {}
        total_distance = 0
        
        for i, corner in enumerate(corner_names):
            x1, y1, _ = model1_keypoints[i]
            x2, y2, _ = model2_keypoints[i]
            
            # Calculate Euclidean distance
            distance = np.sqrt((x1 - x2)**2 + (y1 - y2)**2)
            distances[corner] = distance
            total_distance += distance
        
        distances['average'] = total_distance / 4
        
        # Add distance metrics as text
        metrics_text = f"Distance between {model1_name} and {model2_name}:\n\n"
        metrics_text += f"  Avg Distance: {distances['average']:.2f} pixels\n"
        
        for corner, distance in distances.items():
            if corner != 'average':
                metrics_text += f"  {corner}: {distance:.2f} pixels\n"
        
        plt.figtext(0.02, 0.02, metrics_text, fontsize=10, 
                   bbox=dict(facecolor='white', alpha=0.8))
    
    # Remove axes
    ax.axis('off')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\direct_comparison"
    
    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Preprocess image
    preprocessed_image = preprocess_image(image_path)
    enhanced_image = enhance_image(preprocessed_image)
    
    # Process each model
    model_results = {}
    
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name}...")
        
        # Load model
        model = load_model(model_path)
        
        # Normalize image for model
        input_tensor = normalize_for_model(enhanced_image)
        
        # Run inference
        segmentation, heatmaps, keypoints = detect_corners(model, input_tensor)
        
        # Store results
        model_results[model_name] = {
            'segmentation': segmentation.cpu().numpy(),
            'heatmaps': heatmaps.cpu().numpy(),
            'keypoints': keypoints
        }
        
        # Save individual heatmaps
        for i, corner_name in enumerate(['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']):
            heatmap = heatmaps[0, i].cpu().numpy()
            plt.figure(figsize=(8, 8))
            plt.imshow(heatmap, cmap='jet')
            plt.colorbar()
            plt.title(f"{model_name} - {corner_name} Heatmap")
            
            # Mark the detected peak
            x, y, conf = keypoints[i]
            plt.scatter(x, y, c='white', marker='x', s=100, linewidths=2)
            plt.text(x+5, y+5, f"({int(x)}, {int(y)})\nConf: {conf:.3f}", 
                    color='white', fontsize=10,
                    bbox=dict(facecolor='black', alpha=0.7))
            
            heatmap_path = os.path.join(output_dir, f"{model_name}_{corner_name}_heatmap.png")
            plt.tight_layout()
            plt.savefig(heatmap_path, dpi=150)
            plt.close()
            
            print(f"Saved heatmap to: {heatmap_path}")
    
    # Create comparison visualization
    output_path = os.path.join(output_dir, "model_comparison_256x256.png")
    visualize_model_comparison(enhanced_image, model_results, output_path)
    
    print(f"Model comparison visualization saved to: {output_path}")
    
    # Print keypoints
    print("\nKeypoints in 256x256 Space:")
    print("===========================")
    
    for model_name, results in model_results.items():
        print(f"\n{model_name}:")
        for i, (x, y, conf) in enumerate(results['keypoints']):
            corner_name = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left'][i]
            print(f"  {corner_name}: ({int(x)}, {int(y)}) - Conf: {conf:.4f}")
    
    # Save keypoints to JSON
    keypoints_data = {}
    for model_name, results in model_results.items():
        keypoints_data[model_name] = {}
        for i, (x, y, conf) in enumerate(results['keypoints']):
            corner_name = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left'][i]
            keypoints_data[model_name][corner_name] = {
                'x': int(float(x)),
                'y': int(float(y)),
                'confidence': float(conf)
            }
    
    json_path = os.path.join(output_dir, "keypoints_256x256.json")
    with open(json_path, 'w') as f:
        json.dump(keypoints_data, f, indent=4)
    
    print(f"Keypoints saved to: {json_path}")

if __name__ == "__main__":
    main()
