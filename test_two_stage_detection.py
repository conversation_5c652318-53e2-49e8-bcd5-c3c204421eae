"""
Two-Stage Chess Piece Detection Pipeline

This script implements a two-stage detection pipeline:
1. First, use the distilled segmentation model to detect and extract the chessboard region
2. Then, run the chess piece detection models only on the extracted chessboard region

The script compares the results before and after applying the chessboard region filtering.
"""

import os
import sys
import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
from ultralytics import YOLO
import argparse

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the TinySegmentationModel
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel

# Configuration
CONFIG = {
    # Models to test
    "models": [
        {"path": "runs/detect/train/weights/best.pt", "name": "Original Best Model (Epoch 86)", "coco_classes": False},
        {"path": "runs/detect/simple_continue/continue_epoch107/weights/best.pt", "name": "Epoch 107 (Best mAP)", "coco_classes": False},
        {"path": "runs/detect/simple_continue/continue_epoch111/weights/best.pt", "name": "Epoch 111 (Best Loss)", "coco_classes": False}
    ],

    # Segmentation model
    "segmentation_model": "chess_board_detection/models/segmentation_only/tiny_20250519_091307/best_model_dice.pth",

    # Test image
    "test_image": r"chess_board_detection\data\real\9.jpg",

    # Output directory for visualizations
    "output_dir": "two_stage_detection_comparison",

    # Class names
    "class_names": [
        "white_pawn", "white_knight", "white_bishop", "white_rook",
        "white_queen", "white_king", "black_pawn", "black_knight",
        "black_bishop", "black_rook", "black_queen", "black_king"
    ]
}

# Define colors for each class (white pieces in light colors, black pieces in dark colors)
COLORS = {
    'white_pawn': (200, 200, 200),    # Light gray
    'white_knight': (173, 216, 230),  # Light blue
    'white_bishop': (152, 251, 152),  # Light green
    'white_rook': (255, 182, 193),    # Light pink
    'white_queen': (255, 255, 224),   # Light yellow
    'white_king': (255, 228, 196),    # Light orange
    'black_pawn': (100, 100, 100),    # Dark gray
    'black_knight': (70, 130, 180),   # Dark blue
    'black_bishop': (34, 139, 34),    # Dark green
    'black_rook': (178, 34, 34),      # Dark red
    'black_queen': (218, 165, 32),    # Dark yellow/gold
    'black_king': (139, 69, 19)       # Dark brown
}

def load_segmentation_model(model_path):
    """Load the segmentation model."""
    model = TinySegmentationModel(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path):
    """
    Load the image at its original size for the segmentation model.
    No resizing is applied at this stage.
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Store original image and its size
    original_image = image.copy()
    original_size = image.shape[:2]  # (height, width)

    # Convert to RGB if needed
    if len(image.shape) == 3 and image.shape[2] == 3:
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    else:
        image_rgb = image

    # Store preprocessing info for later use
    preprocess_info = {
        'original_image': original_image,
        'original_size': original_size
    }

    return image_rgb, preprocess_info

def normalize_for_model(image):
    """
    Normalize image for model input.
    The image is already in RGB format from the preprocess_image function.
    """
    # Convert to float and normalize
    image_float = image.astype(np.float32) / 255.0

    # Transpose to (C, H, W) format
    image_transposed = np.transpose(image_float, (2, 0, 1))

    # Create tensor
    tensor = torch.from_numpy(image_transposed).unsqueeze(0)

    return tensor

def find_corners_from_segmentation(segmentation, threshold=0.5):
    """
    Find corners of the chess board using the segmentation mask with improved accuracy.
    Uses multiple thresholds and refinement steps to get more accurate corners.
    """
    # Create binary mask with the primary threshold
    binary_mask = (segmentation > threshold).astype(np.uint8)

    # Apply morphological operations to clean up the mask
    kernel = np.ones((5, 5), np.uint8)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)

    # Find contours
    contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        # Try with a lower threshold if no contours found
        lower_threshold = threshold * 0.8
        binary_mask = (segmentation > lower_threshold).astype(np.uint8)
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
        binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return None

    # Find the largest contour (the chess board)
    largest_contour = max(contours, key=cv2.contourArea)

    # Try multiple epsilon values to get the best approximation
    best_approx = None
    best_score = float('inf')  # Lower is better

    for epsilon_factor in [0.01, 0.02, 0.03, 0.04]:
        epsilon = epsilon_factor * cv2.arcLength(largest_contour, True)
        approx = cv2.approxPolyDP(largest_contour, epsilon, True)

        # Score based on how close to 4 corners we are
        score = abs(len(approx) - 4)

        if score < best_score:
            best_score = score
            best_approx = approx

    approx_polygon = best_approx

    # If we don't get exactly 4 corners, try to find the best 4 corners
    if len(approx_polygon) != 4:
        # If we have more than 4 corners, find the 4 corners that form the largest quadrilateral
        if len(approx_polygon) > 4:
            # Convert to a more convenient format
            points = [point[0] for point in approx_polygon]

            # Find the convex hull
            hull = cv2.convexHull(np.array(points))

            # Approximate the hull to get 4 corners
            epsilon = 0.02 * cv2.arcLength(hull, True)
            approx_polygon = cv2.approxPolyDP(hull, epsilon, True)

            # If we still don't have 4 corners, use the minimum area rectangle
            if len(approx_polygon) != 4:
                rect = cv2.minAreaRect(hull)
                box = cv2.boxPoints(rect)
                approx_polygon = np.int0(box)
        else:
            # If we have fewer than 4 corners, use the minimum area rectangle
            rect = cv2.minAreaRect(largest_contour)
            box = cv2.boxPoints(rect)
            approx_polygon = np.int0(box)

    # Extract corners
    corners = [(point[0][0], point[0][1]) for point in approx_polygon]

    # Refine corner positions using Harris corner detector for sub-pixel accuracy
    if binary_mask.shape[0] > 10 and binary_mask.shape[1] > 10:  # Ensure mask is large enough
        # Create a larger kernel for corner detection
        corner_kernel = np.ones((7, 7), np.uint8)
        # Dilate the mask slightly to ensure corners are included
        dilated_mask = cv2.dilate(binary_mask, corner_kernel, iterations=1)

        # Convert to float32 for corner detection
        gray = dilated_mask.astype(np.float32)

        # Detect corners
        harris_corners = cv2.cornerHarris(gray, blockSize=3, ksize=3, k=0.04)

        # Refine each corner
        refined_corners = []
        for x, y in corners:
            # Create a small region around the corner
            region_size = 10
            x_min = max(0, int(x) - region_size)
            y_min = max(0, int(y) - region_size)
            x_max = min(binary_mask.shape[1], int(x) + region_size)
            y_max = min(binary_mask.shape[0], int(y) + region_size)

            # Extract the region from the Harris response
            region = harris_corners[y_min:y_max, x_min:x_max]

            if region.size > 0:
                # Find the maximum response in the region
                y_local, x_local = np.unravel_index(np.argmax(region), region.shape)

                # Convert back to global coordinates
                refined_x = x_min + x_local
                refined_y = y_min + y_local

                refined_corners.append((refined_x, refined_y))
            else:
                # If region is empty, keep the original corner
                refined_corners.append((x, y))

        # Use refined corners if we have exactly 4
        if len(refined_corners) == 4:
            corners = refined_corners

    # Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left
    corners = sort_corners(corners)

    return corners

def sort_corners(corners):
    """Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left."""
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)

    # Sort corners based on their angle from the center
    return sorted(corners, key=lambda p: np.arctan2(p[1] - center_y, p[0] - center_x))

def extract_and_normalize_board(image, corners, output_size=(416, 416)):
    """
    Extract and normalize the chess board region with minimal distortion.
    Uses the highest quality interpolation method and preserves the original appearance as much as possible.
    """
    # Sort corners in clockwise order to ensure consistent mapping
    corners = sort_corners(corners)

    # Add a small margin to the corners to ensure pieces aren't cut off
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)

    # Expand corners slightly outward from center (3% expansion - reduced from 5%)
    # A smaller expansion helps preserve the original appearance better
    expanded_corners = []
    for x, y in corners:
        # Vector from center to corner
        dx = x - center_x
        dy = y - center_y

        # Expand by 3%
        expanded_x = center_x + dx * 1.03
        expanded_y = center_y + dy * 1.03

        expanded_corners.append((expanded_x, expanded_y))

    # Convert expanded corners to numpy array
    corners_np = np.array(expanded_corners, dtype=np.float32)

    # Define the destination points (normalized square)
    # Add a small margin inside the output image to avoid cutting off pieces at the edges
    margin = int(output_size[0] * 0.02)  # 2% margin
    dst_points = np.array([
        [margin, margin],  # Top-left with margin
        [output_size[0] - margin, margin],  # Top-right with margin
        [output_size[0] - margin, output_size[1] - margin],  # Bottom-right with margin
        [margin, output_size[1] - margin]  # Bottom-left with margin
    ], dtype=np.float32)

    # Get perspective transform
    M = cv2.getPerspectiveTransform(corners_np, dst_points)

    # Apply perspective transform with the highest quality interpolation
    normalized = cv2.warpPerspective(
        image,
        M,
        output_size,
        flags=cv2.INTER_LANCZOS4,  # Use Lanczos interpolation for highest quality
        borderMode=cv2.BORDER_REPLICATE  # Replicate border pixels to avoid artifacts
    )

    return normalized

def detect_chessboard(model, image_path):
    """
    Detect the chessboard in an image and extract it.
    The image is processed at its original size.
    """
    # Preprocess image (no resizing)
    original_image, preprocess_info = preprocess_image(image_path)

    # Normalize for model
    input_tensor = normalize_for_model(original_image)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]

    # Find corners from segmentation
    keypoints = find_corners_from_segmentation(segmentation)

    if keypoints is None:
        print(f"No chess board region detected in the image")
        return None

    # Extract and normalize board from original image for piece detection
    normalized_board = extract_and_normalize_board(
        preprocess_info['original_image'],
        keypoints,
        output_size=(416, 416)
    )

    return {
        'segmentation': segmentation,
        'keypoints': keypoints,
        'original_image': preprocess_info['original_image'],
        'normalized_board': normalized_board
    }

def box_iou(box1, box2):
    """Calculate IoU between two boxes."""
    # Box coordinates
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i < x1_i or y2_i < y1_i:
        return 0.0  # No intersection

    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate union area
    box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
    box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = box1_area + box2_area - intersection_area

    # Calculate IoU
    iou = intersection_area / union_area

    return iou

def remove_duplicate_detections(boxes, scores, class_ids, iou_threshold=0.5, area_overlap_threshold=0.8):
    """
    Remove duplicate detections by keeping only the highest confidence detection
    when bounding boxes overlap significantly.
    """
    if len(boxes) == 0:
        return boxes, scores, class_ids

    # Convert to numpy arrays if they're not already
    boxes = boxes.cpu().numpy() if hasattr(boxes, 'cpu') else boxes
    scores = scores.cpu().numpy() if hasattr(scores, 'cpu') else scores
    class_ids = class_ids.cpu().numpy() if hasattr(class_ids, 'cpu') else class_ids

    # Sort by confidence (highest first)
    indices = np.argsort(scores)[::-1]
    boxes = boxes[indices]
    scores = scores[indices]
    class_ids = class_ids[indices]

    # Initialize list of indices to keep
    keep_indices = []

    # Initialize list of indices to remove
    remove_indices = []

    # Calculate areas for all boxes
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

    for i in range(len(boxes)):
        # Skip if this box was already marked for removal
        if i in remove_indices:
            continue

        # Add current box to keep list
        keep_indices.append(i)

        # Compare with all remaining boxes
        for j in range(i + 1, len(boxes)):
            # Skip if this box was already marked for removal
            if j in remove_indices:
                continue

            # Calculate IoU
            iou = box_iou(boxes[i], boxes[j])

            # Calculate intersection area for containment check
            x1 = max(boxes[i][0], boxes[j][0])
            y1 = max(boxes[i][1], boxes[j][1])
            x2 = min(boxes[i][2], boxes[j][2])
            y2 = min(boxes[i][3], boxes[j][3])

            if x2 > x1 and y2 > y1:  # There is an intersection
                intersection_area = (x2 - x1) * (y2 - y1)

                # Check if one box is mostly contained within the other
                area_ratio_i_in_j = intersection_area / areas[i]
                area_ratio_j_in_i = intersection_area / areas[j]

                # Remove box j if:
                # 1. IoU is above threshold, OR
                # 2. Box j is mostly contained within box i, OR
                # 3. Box i is mostly contained within box j (but i has higher confidence)
                if (iou > iou_threshold or
                    area_ratio_i_in_j > area_overlap_threshold or
                    area_ratio_j_in_i > area_overlap_threshold):
                    remove_indices.append(j)

    # Keep only the selected boxes
    filtered_boxes = boxes[keep_indices]
    filtered_scores = scores[keep_indices]
    filtered_class_ids = class_ids[keep_indices]

    return filtered_boxes, filtered_scores, filtered_class_ids

def test_model_on_image(model_path, model_name, img_path, use_coco_classes=False):
    """Test a model on a single image and return the results."""
    print(f"\nTesting model: {model_name} on original image")
    print(f"Model path: {model_path}")

    # Load model
    model = YOLO(model_path)

    # Read and preprocess image to 416x416 (same as training)
    img = cv2.imread(img_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img_resized = cv2.resize(img, (416, 416))

    # Run inference with specific size and higher IoU threshold to reduce duplicates
    print(f"Running inference on {img_path} (resized to 416x416)...")
    results = model(img_resized, imgsz=416, iou=0.7)[0]

    # Get detections
    boxes = results.boxes.xyxy
    scores = results.boxes.conf
    class_ids = results.boxes.cls

    # Remove duplicate detections
    print("Removing duplicate detections...")
    filtered_boxes, filtered_scores, filtered_class_ids = remove_duplicate_detections(
        boxes, scores, class_ids, iou_threshold=0.45, area_overlap_threshold=0.7
    )

    # Process results
    classes = []
    confidences = []

    for i in range(len(filtered_boxes)):
        cls_id = int(filtered_class_ids[i])
        conf = filtered_scores[i]

        # Use appropriate class names based on model type
        if use_coco_classes:
            if cls_id < len(CONFIG["coco_class_names"]):
                class_name = CONFIG["coco_class_names"][cls_id]
            else:
                class_name = f"unknown_{cls_id}"
        else:
            if cls_id < len(CONFIG["class_names"]):
                class_name = CONFIG["class_names"][cls_id]
            else:
                class_name = f"unknown_{cls_id}"

        classes.append(class_name)
        confidences.append(conf)

    print(f"Detected {len(filtered_boxes)} objects after removing duplicates")

    # Create a simple dictionary to hold the filtered results
    filtered_boxes_tensor = torch.tensor(filtered_boxes) if len(filtered_boxes) > 0 else torch.zeros((0, 4))
    filtered_scores_tensor = torch.tensor(filtered_scores) if len(filtered_scores) > 0 else torch.zeros(0)
    filtered_class_ids_tensor = torch.tensor(filtered_class_ids) if len(filtered_class_ids) > 0 else torch.zeros(0)

    # Create a simple dictionary to hold the filtered results
    filtered_results = {
        'boxes': filtered_boxes_tensor,
        'scores': filtered_scores_tensor,
        'class_ids': filtered_class_ids_tensor
    }

    return filtered_results, classes, confidences

def test_model_on_chessboard(model_path, model_name, chessboard_img, use_coco_classes=False):
    """
    Test a model on the extracted chessboard image.
    This simplified version matches the single-stage approach more closely.
    """
    print(f"\nTesting model: {model_name} on extracted chessboard")
    print(f"Model path: {model_path}")

    # Load model
    model = YOLO(model_path)

    # Convert to RGB if needed
    chessboard_img_rgb = cv2.cvtColor(chessboard_img, cv2.COLOR_BGR2RGB) if len(chessboard_img.shape) == 3 else chessboard_img

    # Run inference with the same parameters as the single-stage approach
    print(f"Running inference on extracted chessboard (416x416)...")
    results = model(chessboard_img_rgb, imgsz=416, iou=0.7)[0]

    # Get detections
    boxes = results.boxes.xyxy
    scores = results.boxes.conf
    class_ids = results.boxes.cls

    # Remove duplicate detections with the same parameters as the single-stage approach
    print("Removing duplicate detections...")
    filtered_boxes, filtered_scores, filtered_class_ids = remove_duplicate_detections(
        boxes, scores, class_ids, iou_threshold=0.45, area_overlap_threshold=0.7
    )

    # Process results without any post-processing corrections
    classes = []
    confidences = []

    for i in range(len(filtered_boxes)):
        cls_id = int(filtered_class_ids[i])
        conf = filtered_scores[i]

        # Use appropriate class names based on model type
        if use_coco_classes:
            if cls_id < len(CONFIG["coco_class_names"]):
                class_name = CONFIG["coco_class_names"][cls_id]
            else:
                class_name = f"unknown_{cls_id}"
        else:
            if cls_id < len(CONFIG["class_names"]):
                class_name = CONFIG["class_names"][cls_id]
            else:
                class_name = f"unknown_{cls_id}"

        classes.append(class_name)
        confidences.append(conf)

    print(f"Detected {len(filtered_boxes)} objects after removing duplicates")

    # Create tensors for the filtered results
    filtered_boxes_tensor = torch.tensor(filtered_boxes) if len(filtered_boxes) > 0 else torch.zeros((0, 4))
    filtered_scores_tensor = torch.tensor(filtered_scores) if len(filtered_scores) > 0 else torch.zeros(0)
    filtered_class_ids_tensor = torch.tensor(filtered_class_ids) if len(filtered_class_ids) > 0 else torch.zeros(0)

    # Create a dictionary to hold the filtered results
    filtered_results = {
        'boxes': filtered_boxes_tensor,
        'scores': filtered_scores_tensor,
        'class_ids': filtered_class_ids_tensor
    }

    return filtered_results, classes, confidences

def create_visualization(img, chessboard_img, results_list, model_names, classes_list, confidences_list, approach_name):
    """Create a visualization of the detection results."""
    # Create figure with subplots
    fig, axes = plt.subplots(1, len(results_list), figsize=(7*len(results_list), 10))

    # Use the appropriate image based on the approach
    display_img = chessboard_img if approach_name == "Two-Stage" else cv2.resize(img, (416, 416))

    # Process each model's results
    for i, (results, model_name, classes, confidences) in enumerate(zip(results_list, model_names, classes_list, confidences_list)):
        # Create a copy of the image for this model
        img_copy = display_img.copy()

        # Get boxes from our filtered results dictionary
        boxes = results['boxes']

        # Draw boxes on the image
        for j in range(len(boxes)):
            if isinstance(boxes, torch.Tensor):
                x1, y1, x2, y2 = boxes[j].cpu().numpy().astype(int)
            else:
                x1, y1, x2, y2 = boxes[j].astype(int)

            class_name = classes[j]  # Use the class name from the classes list
            conf = confidences[j]

            # Get color for the class
            if class_name in COLORS:
                color = COLORS[class_name]
            else:
                # Use a default color for COCO classes or unknown classes
                color = (0, 255, 0)  # Green

            # Draw bounding box
            cv2.rectangle(img_copy, (x1, y1), (x2, y2), color, 2)

            # Draw only the number (no text label)
            cv2.putText(img_copy, f"{j+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)  # Black outline
            cv2.putText(img_copy, f"{j+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)  # White text

        # Display the image
        axes[i].imshow(img_copy)
        axes[i].set_title(f"{approach_name} - {model_name}: {len(boxes)} detections", fontsize=16)
        axes[i].axis('off')

    plt.tight_layout()
    return fig

def create_detection_text_file(output_path, model_names, classes_list, confidences_list, boxes_list, approach_name):
    """Create a text file with detection details for each model."""
    with open(output_path, 'w') as f:
        f.write(f"Chess Piece Detection - {approach_name} Approach\n")
        f.write("=" * 50 + "\n\n")

        for model_name, classes, confidences, boxes in zip(model_names, classes_list, confidences_list, boxes_list):
            f.write(f"{model_name} Detections:\n")
            f.write("-" * 50 + "\n")

            for i, (cls, conf) in enumerate(zip(classes, confidences)):
                if isinstance(boxes[i], torch.Tensor):
                    x1, y1, x2, y2 = boxes[i].cpu().numpy().astype(int)
                else:
                    x1, y1, x2, y2 = boxes[i].astype(int)

                f.write(f"Detection #{i+1}: {cls} (Confidence: {conf:.4f})\n")
                f.write(f"   Bounding Box: [{x1}, {y1}, {x2}, {y2}]\n")

            f.write(f"\nTotal detections: {len(classes)}\n")
            avg_conf = sum(confidences) / len(confidences) if confidences else 0
            f.write(f"Average confidence: {avg_conf:.4f}\n\n")
            f.write("=" * 50 + "\n\n")

def main():
    """Main function."""
    print("Two-Stage Chess Piece Detection Pipeline")
    print("=" * 50)

    # Create output directory
    os.makedirs(CONFIG["output_dir"], exist_ok=True)

    # Load segmentation model
    print(f"Loading segmentation model: {CONFIG['segmentation_model']}")
    segmentation_model = load_segmentation_model(CONFIG["segmentation_model"])

    # Detect chessboard
    print(f"Detecting chessboard in {CONFIG['test_image']}")
    chessboard_results = detect_chessboard(segmentation_model, CONFIG["test_image"])

    if chessboard_results is None:
        print("No chessboard detected in the image. Exiting.")
        return

    # Extract normalized chessboard
    normalized_board = chessboard_results['normalized_board']

    # Test each model on the original image
    original_results_list = []
    original_classes_list = []
    original_confidences_list = []
    original_model_names = []
    original_boxes_list = []

    for model_info in CONFIG["models"]:
        results, classes, confidences = test_model_on_image(
            model_info["path"],
            model_info["name"],
            CONFIG["test_image"],
            model_info.get("coco_classes", False)
        )
        original_results_list.append(results)
        original_classes_list.append(classes)
        original_confidences_list.append(confidences)
        original_model_names.append(model_info["name"])
        original_boxes_list.append(results['boxes'])

    # Test each model on the extracted chessboard
    chessboard_results_list = []
    chessboard_classes_list = []
    chessboard_confidences_list = []
    chessboard_model_names = []
    chessboard_boxes_list = []

    for model_info in CONFIG["models"]:
        results, classes, confidences = test_model_on_chessboard(
            model_info["path"],
            model_info["name"],
            normalized_board,
            model_info.get("coco_classes", False)
        )
        chessboard_results_list.append(results)
        chessboard_classes_list.append(classes)
        chessboard_confidences_list.append(confidences)
        chessboard_model_names.append(model_info["name"])
        chessboard_boxes_list.append(results['boxes'])

    # Create visualizations
    original_fig = create_visualization(
        chessboard_results['original_image'],
        normalized_board,
        original_results_list,
        original_model_names,
        original_classes_list,
        original_confidences_list,
        "Single-Stage"
    )

    chessboard_fig = create_visualization(
        chessboard_results['original_image'],
        normalized_board,
        chessboard_results_list,
        chessboard_model_names,
        chessboard_classes_list,
        chessboard_confidences_list,
        "Two-Stage"
    )

    # Save visualizations
    original_output_path = os.path.join(CONFIG["output_dir"], "single_stage_detection.png")
    chessboard_output_path = os.path.join(CONFIG["output_dir"], "two_stage_detection.png")

    original_fig.savefig(original_output_path, dpi=300, bbox_inches='tight')
    chessboard_fig.savefig(chessboard_output_path, dpi=300, bbox_inches='tight')

    plt.close(original_fig)
    plt.close(chessboard_fig)

    # Create and save text files with detection details
    original_text_path = os.path.join(CONFIG["output_dir"], "single_stage_detection_details.txt")
    chessboard_text_path = os.path.join(CONFIG["output_dir"], "two_stage_detection_details.txt")

    create_detection_text_file(
        original_text_path,
        original_model_names,
        original_classes_list,
        original_confidences_list,
        original_boxes_list,
        "Single-Stage"
    )

    create_detection_text_file(
        chessboard_text_path,
        chessboard_model_names,
        chessboard_classes_list,
        chessboard_confidences_list,
        chessboard_boxes_list,
        "Two-Stage"
    )

    # Print comparison summary
    print("\nDetection Comparison Summary:")
    print("=" * 50)
    print(f"{'Model':<25} {'Single-Stage':<15} {'Two-Stage':<15}")
    print("-" * 55)

    for i, model_name in enumerate(original_model_names):
        original_count = len(original_classes_list[i])
        chessboard_count = len(chessboard_classes_list[i])
        print(f"{model_name:<25} {original_count:<15} {chessboard_count:<15}")

    print("\nComparison completed!")
    print(f"Results saved to:")
    print(f"- Single-Stage Image: {original_output_path}")
    print(f"- Two-Stage Image: {chessboard_output_path}")
    print(f"- Single-Stage Details: {original_text_path}")
    print(f"- Two-Stage Details: {chessboard_text_path}")

if __name__ == "__main__":
    main()