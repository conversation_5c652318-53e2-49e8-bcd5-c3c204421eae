"""
Two-stage approach for chess piece detection:
1. First stage: Detect the chess board and extract the normalized board
2. Second stage: Classify each square individually

This approach should improve accuracy by:
- Focusing on one square at a time
- Eliminating false positives outside valid squares
- Using the board structure to constrain detections
"""

import os
import sys
import argparse
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw, ImageFont
import torchvision.transforms as transforms
from ultralytics import YOLO

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import board detection modules
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2 as ImprovedCornerDetectionModel
from chess_board_detection.utils.perspective_transform import get_perspective_transform
from chess_board_detection.utils.visualization import visualize_detection_results

class ChessPieceClassifier:
    """
    Classifier for individual chess squares.
    """
    def __init__(self, model_path):
        """
        Initialize the classifier.

        Args:
            model_path: Path to the YOLO model
        """
        self.model = YOLO(model_path)

        # Class names
        self.class_names = [
            'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
            'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
        ]

        # Class colors (for visualization)
        self.class_colors = {
            'white_pawn': (200, 200, 200),
            'white_knight': (220, 220, 220),
            'white_bishop': (240, 240, 240),
            'white_rook': (180, 180, 180),
            'white_queen': (255, 255, 255),
            'white_king': (160, 160, 160),
            'black_pawn': (50, 50, 50),
            'black_knight': (30, 30, 30),
            'black_bishop': (10, 10, 10),
            'black_rook': (70, 70, 70),
            'black_queen': (0, 0, 0),
            'black_king': (90, 90, 90),
            'empty': (128, 128, 128)
        }

    def classify_square(self, square_img, conf_threshold=0.25):
        """
        Classify a single chess square.

        Args:
            square_img: Image of a chess square
            conf_threshold: Confidence threshold

        Returns:
            class_name: Predicted class name
            confidence: Prediction confidence
        """
        # Run inference
        results = self.model(square_img, conf=conf_threshold)

        # Get highest confidence detection
        best_class = 'empty'
        best_conf = 0.0

        for result in results:
            boxes = result.boxes

            for i in range(len(boxes)):
                confidence = boxes.conf[i].item()
                class_id = int(boxes.cls[i].item())
                class_name = result.names[class_id]

                if confidence > best_conf:
                    best_class = class_name
                    best_conf = confidence

        return best_class, best_conf

class TwoStageChessDetection:
    """
    Two-stage approach for chess piece detection.
    """
    def __init__(self, board_model_path, piece_model_path):
        """
        Initialize the detector.

        Args:
            board_model_path: Path to the board detection model
            piece_model_path: Path to the piece classification model
        """
        # Load board detection model
        self.board_model = ImprovedCornerDetectionModel()
        self.board_model.load_state_dict(torch.load(board_model_path, map_location=torch.device('cpu')))
        self.board_model.eval()

        # Load piece classification model
        self.piece_classifier = ChessPieceClassifier(piece_model_path)

        # Set up transforms
        self.transform = transforms.Compose([
            transforms.Resize((256, 256)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    def detect_board(self, image):
        """
        Detect the chess board in an image.

        Args:
            image: Input image

        Returns:
            corners: Detected corners
            normalized_board: Normalized chess board image
            homography: Homography matrix
        """
        # Convert to PIL Image if needed
        if isinstance(image, np.ndarray):
            image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        else:
            image_pil = image

        # Prepare input for the model
        input_tensor = self.transform(image_pil).unsqueeze(0)

        # Run inference
        with torch.no_grad():
            outputs = self.board_model(input_tensor)

        # Extract corners
        heatmaps = outputs['heatmaps'][0].cpu().numpy()
        corners = []

        for i in range(4):
            heatmap = heatmaps[i]
            y, x = np.unravel_index(np.argmax(heatmap), heatmap.shape)

            # Scale to original image size
            orig_h, orig_w = image_pil.height, image_pil.width
            x_scaled = int(x * orig_w / heatmap.shape[1])
            y_scaled = int(y * orig_h / heatmap.shape[0])

            corners.append((x_scaled, y_scaled))

        # Sort corners (top-left, top-right, bottom-right, bottom-left)
        corners = self._sort_corners(corners)

        # Get perspective transform
        normalized_board, homography = get_perspective_transform(
            np.array(image_pil), corners, output_size=(512, 512)
        )

        return corners, normalized_board, homography

    def _sort_corners(self, corners):
        """
        Sort corners in the order: top-left, top-right, bottom-right, bottom-left.

        Args:
            corners: List of corner coordinates

        Returns:
            sorted_corners: Sorted corners
        """
        # Calculate center
        center_x = sum(x for x, y in corners) / 4
        center_y = sum(y for x, y in corners) / 4

        # Sort corners based on their position relative to the center
        def get_angle(point):
            return np.arctan2(point[1] - center_y, point[0] - center_x)

        return sorted(corners, key=get_angle, reverse=True)

    def detect_pieces(self, normalized_board, conf_threshold=0.25):
        """
        Detect chess pieces on the normalized board.

        Args:
            normalized_board: Normalized chess board image
            conf_threshold: Confidence threshold

        Returns:
            piece_map: Dictionary mapping chess coordinates to piece names and confidences
            square_images: Dictionary mapping chess coordinates to square images
        """
        # Convert to numpy if needed
        if isinstance(normalized_board, Image.Image):
            board_img = np.array(normalized_board)
        else:
            board_img = normalized_board

        # Get board dimensions
        h, w = board_img.shape[:2]
        square_size = h // 8

        # Extract and classify each square
        piece_map = {}
        square_images = {}

        for rank in range(8):
            for file in range(8):
                # Extract square
                y1 = rank * square_size
                y2 = (rank + 1) * square_size
                x1 = file * square_size
                x2 = (file + 1) * square_size

                square_img = board_img[y1:y2, x1:x2]

                # Convert to chess notation (A1-H8)
                file_letter = chr(ord('a') + file)
                rank_number = 8 - rank
                chess_coord = f"{file_letter}{rank_number}"

                # Store square image
                square_images[chess_coord] = square_img

                # Classify square
                class_name, confidence = self.piece_classifier.classify_square(square_img, conf_threshold)

                # Store result if not empty or confidence is high enough
                if class_name != 'empty' or confidence > conf_threshold:
                    piece_map[chess_coord] = {
                        'piece': class_name,
                        'confidence': confidence
                    }

        return piece_map, square_images

    def visualize_results(self, original_image, corners, normalized_board, piece_map, square_images, output_path):
        """
        Visualize detection results.

        Args:
            original_image: Original input image
            corners: Detected corners
            normalized_board: Normalized chess board image
            piece_map: Dictionary mapping chess coordinates to piece names and confidences
            square_images: Dictionary mapping chess coordinates to square images
            output_path: Path to save the visualization
        """
        # Convert to numpy if needed
        if isinstance(original_image, Image.Image):
            original_image = np.array(original_image)
            original_image = cv2.cvtColor(original_image, cv2.COLOR_RGB2BGR)

        if isinstance(normalized_board, Image.Image):
            normalized_board = np.array(normalized_board)
            normalized_board = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)

        # Create figure
        fig, axs = plt.subplots(2, 2, figsize=(15, 15))

        # Original image with detected corners
        axs[0, 0].imshow(cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB))
        axs[0, 0].set_title('Original Image')

        # Draw corners
        for i, (x, y) in enumerate(corners):
            axs[0, 0].plot(x, y, 'ro', markersize=10)
            axs[0, 0].text(x, y, f"{i+1}", color='white', fontsize=12)

        # Normalized chess board
        axs[0, 1].imshow(cv2.cvtColor(normalized_board, cv2.COLOR_BGR2RGB))
        axs[0, 1].set_title('Normalized Chess Board')

        # Detected pieces
        detected_board = normalized_board.copy()
        h, w = detected_board.shape[:2]
        square_size = h // 8

        for rank in range(8):
            for file in range(8):
                # Convert to chess notation
                file_letter = chr(ord('a') + file)
                rank_number = 8 - rank
                chess_coord = f"{file_letter}{rank_number}"

                # Draw square
                y1 = rank * square_size
                y2 = (rank + 1) * square_size
                x1 = file * square_size
                x2 = (file + 1) * square_size

                # Get piece info
                if chess_coord in piece_map:
                    piece_info = piece_map[chess_coord]
                    piece_name = piece_info['piece']
                    confidence = piece_info['confidence']

                    # Draw bounding box
                    color = self.piece_classifier.class_colors.get(piece_name, (0, 255, 0))
                    cv2.rectangle(detected_board, (x1, y1), (x2, y2), color, 2)

                    # Draw label
                    label = f"{piece_name}: {confidence:.2f}"
                    cv2.putText(detected_board, label, (x1, y1 + 15),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

        axs[1, 0].imshow(cv2.cvtColor(detected_board, cv2.COLOR_BGR2RGB))
        axs[1, 0].set_title(f'Detected Pieces ({len(piece_map)})')

        # Chess position
        position_board = np.zeros((8*50, 8*50, 3), dtype=np.uint8)

        for rank in range(8):
            for file in range(8):
                # Draw square
                y1 = rank * 50
                y2 = (rank + 1) * 50
                x1 = file * 50
                x2 = (file + 1) * 50

                # Alternate square colors
                if (rank + file) % 2 == 0:
                    color = (240, 217, 181)  # Light square
                else:
                    color = (181, 136, 99)   # Dark square

                cv2.rectangle(position_board, (x1, y1), (x2, y2), color, -1)

                # Convert to chess notation
                file_letter = chr(ord('a') + file)
                rank_number = 8 - rank
                chess_coord = f"{file_letter}{rank_number}"

                # Draw piece
                if chess_coord in piece_map:
                    piece_info = piece_map[chess_coord]
                    piece_name = piece_info['piece']

                    # Draw piece symbol
                    symbol = self._get_piece_symbol(piece_name)
                    cv2.putText(position_board, symbol, (x1 + 15, y1 + 35),
                                cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)

                # Draw coordinates
                if file == 0:
                    cv2.putText(position_board, str(rank_number), (x1 + 5, y1 + 15),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

                if rank == 7:
                    cv2.putText(position_board, file_letter, (x1 + 40, y1 + 45),
                                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 0), 1)

        axs[1, 1].imshow(cv2.cvtColor(position_board, cv2.COLOR_BGR2RGB))
        axs[1, 1].set_title('Chess Position')

        # Remove axis ticks
        for ax in axs.flat:
            ax.set_xticks([])
            ax.set_yticks([])

        # Save figure
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close()

    def _get_piece_symbol(self, piece_name):
        """
        Get Unicode symbol for a chess piece.

        Args:
            piece_name: Name of the piece

        Returns:
            symbol: Unicode symbol
        """
        symbols = {
            'white_pawn': '♙',
            'white_knight': '♘',
            'white_bishop': '♗',
            'white_rook': '♖',
            'white_queen': '♕',
            'white_king': '♔',
            'black_pawn': '♟',
            'black_knight': '♞',
            'black_bishop': '♝',
            'black_rook': '♜',
            'black_queen': '♛',
            'black_king': '♚',
            'empty': ' '
        }

        return symbols.get(piece_name, '?')

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Two-stage chess piece detection')
    parser.add_argument('--board_model', type=str, required=True,
                        help='Path to board detection model')
    parser.add_argument('--piece_model', type=str, required=True,
                        help='Path to piece classification model')
    parser.add_argument('--input_image', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/outputs',
                        help='Directory to save the output')
    parser.add_argument('--conf_threshold', type=float, default=0.25,
                        help='Confidence threshold for piece detection')
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load input image
    image = cv2.imread(args.input_image)
    if image is None:
        print(f"Could not load image from {args.input_image}")
        return

    # Initialize detector
    detector = TwoStageChessDetection(args.board_model, args.piece_model)

    # Detect board
    print("Detecting chess board...")
    corners, normalized_board, homography = detector.detect_board(image)

    # Detect pieces
    print("Detecting chess pieces...")
    piece_map, square_images = detector.detect_pieces(normalized_board, args.conf_threshold)

    # Visualize results
    output_path = os.path.join(args.output_dir, f"{os.path.splitext(os.path.basename(args.input_image))[0]}_detection.png")
    print(f"Saving results to {output_path}")
    detector.visualize_results(image, corners, normalized_board, piece_map, square_images, output_path)

    # Print detected pieces
    print("\nDetected pieces:")
    for coord, info in sorted(piece_map.items()):
        print(f"{coord}: {info['piece']} (confidence: {info['confidence']:.4f})")

if __name__ == "__main__":
    main()
