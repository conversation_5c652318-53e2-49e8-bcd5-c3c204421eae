@echo off
echo Continue Training for Chess Piece Detection
echo =========================================================
echo.

echo This script will continue training from epoch 90 with a focus on reducing classification loss to zero.
echo The best model from epoch 86 will be used as the starting point.
echo Using the targeted dataset that focuses on rare chess pieces to improve classification.
echo Models will be saved in a new directory to avoid overwriting existing models.
echo.

echo Memory optimization is enabled to prevent crashes.
echo Dynamic weight adjustment is enabled to focus on classification loss.
echo.

echo Press any key to start training...
pause > nul

python continue_training.py

echo.
echo Training completed.
pause
