"""
Train Efficient U-Net V3 with optimized architecture and reduced parameters.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.efficient_unet_v3 import get_efficient_model
from chess_board_detection.dataset.augmented_segmentation_dataset import create_augmented_dataloaders

class EfficientLoss(nn.Module):
    """Efficient loss function for V3 training."""
    
    def __init__(self, bce_weight=0.5, dice_weight=0.5, smooth=1e-6):
        super(EfficientLoss, self).__init__()
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.smooth = smooth
        self.bce_loss = nn.BCEWithLogitsLoss()
    
    def dice_loss(self, inputs, targets):
        """Efficient dice loss."""
        inputs = torch.sigmoid(inputs)
        
        # Flatten
        inputs = inputs.view(-1)
        targets = targets.view(-1)
        
        intersection = (inputs * targets).sum()
        dice = (2. * intersection + self.smooth) / (inputs.sum() + targets.sum() + self.smooth)
        
        return 1 - dice
    
    def forward(self, inputs, targets):
        bce = self.bce_loss(inputs, targets)
        dice = self.dice_loss(inputs, targets)
        total_loss = self.bce_weight * bce + self.dice_weight * dice
        
        return total_loss, {
            'bce': bce.item(),
            'dice': dice.item(),
            'total': total_loss.item()
        }

def calculate_metrics(predictions, targets, threshold=0.5):
    """Calculate metrics efficiently."""
    with torch.no_grad():
        predictions = torch.sigmoid(predictions)
        pred_binary = (predictions > threshold).float()
        targets_binary = (targets > threshold).float()
        
        # Flatten
        pred_flat = pred_binary.view(-1)
        target_flat = targets_binary.view(-1)
        
        # Calculate metrics
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum() - intersection
        
        iou = (intersection + 1e-6) / (union + 1e-6)
        dice = (2 * intersection + 1e-6) / (pred_flat.sum() + target_flat.sum() + 1e-6)
        
        return {
            'iou': torch.clamp(iou, 0, 1).item(),
            'dice': torch.clamp(dice, 0, 1).item()
        }

def train_epoch_v3(model, train_loader, criterion, optimizer, scaler, device):
    """Efficient training epoch for V3."""
    model.train()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(train_loader)
    
    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device, non_blocking=True)
        masks = masks.to(device, non_blocking=True)
        
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)
        
        optimizer.zero_grad()
        
        with autocast():
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
        
        # Backward pass
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        
        # Calculate metrics
        batch_metrics = calculate_metrics(outputs, masks)
        
        total_loss += loss.item()
        total_iou += batch_metrics['iou']
        total_dice += batch_metrics['dice']
        
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Dice': f'{batch_metrics["dice"]:.4f}',
            'IoU': f'{batch_metrics["iou"]:.4f}'
        })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def validate_epoch_v3(model, val_loader, criterion, device):
    """Efficient validation epoch for V3."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            
            if masks.dim() == 3:
                masks = masks.unsqueeze(1)
            
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            
            batch_metrics = calculate_metrics(outputs, masks)
            
            total_loss += loss.item()
            total_iou += batch_metrics['iou']
            total_dice += batch_metrics['dice']
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{batch_metrics["dice"]:.4f}',
                'IoU': f'{batch_metrics["iou"]:.4f}'
            })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def train_efficient_unet_v3():
    """Train Efficient U-Net V3."""
    
    # Configuration
    config = {
        'dataset_dir': r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326",
        'save_dir': "chess_board_detection/efficient_unet_v3_results",
        'epochs': 50,
        'batch_size': 8,  # Can use larger batch size due to efficiency
        'learning_rate': 1e-3,  # Higher LR for faster convergence
        'base_channels': 32,  # V3-32 configuration
        'num_workers': 0,
    }
    
    print("🚀 Starting Efficient U-Net V3 Training...")
    print(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Create save directory
    save_dir = Path(config['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Create dataloaders
    print("Creating dataloaders...")
    train_loader, val_loader = create_augmented_dataloaders(
        config['dataset_dir'],
        batch_size=config['batch_size'],
        train_split=0.8,
        num_workers=config['num_workers']
    )
    
    # Create efficient model
    print("Creating Efficient U-Net V3...")
    model = get_efficient_model(base_channels=config['base_channels'])
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,}")
    
    # Calculate efficiency
    v1_params = 17262977
    v2_params = 41036150
    efficiency_v1 = total_params / v1_params
    efficiency_v2 = total_params / v2_params
    print(f"Efficiency vs V1: {efficiency_v1:.3f}x ({(1-efficiency_v1)*100:.1f}% reduction)")
    print(f"Efficiency vs V2: {efficiency_v2:.3f}x ({(1-efficiency_v2)*100:.1f}% reduction)")
    
    # Loss function
    criterion = EfficientLoss(bce_weight=0.5, dice_weight=0.5)
    
    # Optimizer (higher LR for efficient model)
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=1e-4
    )
    
    # Scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config['epochs'], eta_min=1e-5
    )
    
    # Mixed precision scaler
    scaler = GradScaler()
    
    # Training history
    history = {
        'train_loss': [], 'val_loss': [],
        'train_dice': [], 'val_dice': [],
        'train_iou': [], 'val_iou': []
    }
    
    best_val_dice = 0
    patience_counter = 0
    patience = 15
    
    print(f"Starting training for {config['epochs']} epochs...")
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        print(f"\nEpoch {epoch+1}/{config['epochs']}")
        
        # Train
        train_loss, train_iou, train_dice = train_epoch_v3(
            model, train_loader, criterion, optimizer, scaler, device
        )
        
        # Validate
        val_loss, val_iou, val_dice = validate_epoch_v3(
            model, val_loader, criterion, device
        )
        
        # Update scheduler
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_dice'].append(train_dice)
        history['val_dice'].append(val_dice)
        history['train_iou'].append(train_iou)
        history['val_iou'].append(val_iou)
        
        # Print results
        print(f"Train - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}, IoU: {train_iou:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}, IoU: {val_iou:.4f}")
        print(f"LR: {current_lr:.6f}")
        
        # Save best model
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            torch.save(model.state_dict(), save_dir / "best_model.pth")
            print(f"✅ New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_dice': val_dice,
                'history': history
            }, save_dir / f"checkpoint_epoch_{epoch+1}.pth")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"Early stopping triggered after {patience} epochs without improvement")
            break
    
    # Save final results
    torch.save(model.state_dict(), save_dir / "final_model.pth")
    
    with open(save_dir / "training_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    training_time = time.time() - start_time
    print(f"\n🎉 Training completed in {training_time/3600:.2f} hours")
    print(f"🏆 Best validation Dice: {best_val_dice:.4f}")
    print(f"📁 Results saved to: {save_dir}")
    
    return model, history, best_val_dice

if __name__ == "__main__":
    try:
        model, history, best_dice = train_efficient_unet_v3()
        
        print(f"\n{'='*70}")
        print(f"🎉 EFFICIENT U-NET V3 TRAINING COMPLETED!")
        print(f"{'='*70}")
        print(f"🏆 Best Dice Score: {best_dice:.4f}")
        
        # Compare with previous versions
        v1_best = 0.9100
        v2_best = 0.8256
        
        print(f"\n📊 PERFORMANCE COMPARISON:")
        print(f"V1 Best Dice: {v1_best:.4f} (17.3M params)")
        print(f"V2 Best Dice: {v2_best:.4f} (41.0M params)")
        print(f"V3 Best Dice: {best_dice:.4f} (314K params)")
        
        # Calculate performance per parameter
        v1_efficiency = v1_best / 17262977 * 1e6
        v2_efficiency = v2_best / 41036150 * 1e6
        v3_efficiency = best_dice / 314195 * 1e6
        
        print(f"\n⚡ EFFICIENCY METRICS (Dice per Million Parameters):")
        print(f"V1: {v1_efficiency:.2f}")
        print(f"V2: {v2_efficiency:.2f}")
        print(f"V3: {v3_efficiency:.2f}")
        
        if v3_efficiency > v1_efficiency:
            print(f"🚀 V3 is {v3_efficiency/v1_efficiency:.1f}x more efficient than V1!")
        
        if best_dice >= 0.80:
            print(f"✅ EXCELLENT: V3 achieved production-ready performance!")
        elif best_dice >= 0.75:
            print(f"✅ GOOD: V3 achieved solid performance with massive efficiency!")
        else:
            print(f"⚠️ NEEDS IMPROVEMENT: Consider V3-48 or longer training")
        
    except Exception as e:
        print(f"❌ Training failed with error: {e}")
        import traceback
        traceback.print_exc()
