"""
Enhanced inference script for chess board detection with post-processing.
This script uses the improved model and applies post-processing to enhance results.
"""

import os
import argparse
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
from torchvision import transforms

from models.enhanced_unet import EnhancedChessBoardUNet
from post_processing import post_process_model_output, apply_non_maximum_suppression, enhance_detection_rate, enforce_geometric_constraints
from segmentation_guided_corner_detection import segmentation_guided_corner_detection


def load_image(image_path, target_size=(256, 256)):
    """
    Load and preprocess an image for inference.

    Args:
        image_path: Path to the image file
        target_size: Target size for resizing

    Returns:
        image_tensor: Preprocessed image tensor
        original_image: Original image as numpy array
        original_size: Original image size
    """
    # Load image
    image = Image.open(image_path).convert('RGB')
    original_size = image.size  # (width, height)
    original_image = np.array(image)

    # Preprocess image
    preprocess = transforms.Compose([
        transforms.Resize(target_size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    image_tensor = preprocess(image).unsqueeze(0)  # Add batch dimension

    return image_tensor, original_image, original_size


def predict(model, image_tensor, device, use_post_processing=False, use_segmentation_guidance=True):
    """
    Run inference with the model.

    Args:
        model: The model to use for inference
        image_tensor: Preprocessed image tensor
        device: Device to run inference on
        use_post_processing: Whether to apply post-processing
        use_segmentation_guidance: Whether to use segmentation to guide corner detection

    Returns:
        outputs: Model outputs
    """
    model.eval()
    image_tensor = image_tensor.to(device)

    with torch.no_grad():
        outputs = model(image_tensor)

        # Apply post-processing if requested
        if use_post_processing:
            outputs = post_process_model_output(outputs, use_segmentation_guidance=use_segmentation_guidance)

    return outputs


def extract_corners(heatmaps, threshold=0.3):
    """
    Extract corner coordinates from heatmaps.

    Args:
        heatmaps: Corner heatmaps (4, H, W)
        threshold: Detection threshold

    Returns:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    """
    # Apply sigmoid if not already applied
    if isinstance(heatmaps, torch.Tensor):
        if torch.max(heatmaps) > 1.0:
            heatmaps = torch.sigmoid(heatmaps)
        heatmaps = heatmaps.cpu().numpy()

    corners = []
    for i in range(4):
        heatmap = heatmaps[i]
        max_val = np.max(heatmap)

        if max_val >= threshold:
            # Find the location of the maximum value
            max_idx = np.argmax(heatmap)
            y, x = np.unravel_index(max_idx, heatmap.shape)
            corners.append((x, y))
        else:
            corners.append(None)

    return corners


def scale_corners_to_original(corners, original_size, model_size=(256, 256)):
    """
    Scale corner coordinates from model size to original image size.

    Args:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        original_size: Original image size (width, height)
        model_size: Model input size (width, height)

    Returns:
        scaled_corners: Scaled corner coordinates
    """
    if corners is None:
        return None

    scaled_corners = []
    for corner in corners:
        if corner is None:
            scaled_corners.append(None)
        else:
            x, y = corner
            scaled_x = int(x * original_size[0] / model_size[0])
            scaled_y = int(y * original_size[1] / model_size[1])
            scaled_corners.append((scaled_x, scaled_y))

    return scaled_corners


def visualize_results(image, segmentation, heatmaps, corners, output_path=None):
    """
    Visualize the results.

    Args:
        image: Original image
        segmentation: Segmentation mask
        heatmaps: Corner heatmaps
        corners: Corner coordinates
        output_path: Path to save the visualization
    """
    plt.figure(figsize=(15, 10))

    # Plot original image
    plt.subplot(2, 3, 1)
    plt.imshow(image)
    plt.title('Original Image')
    plt.axis('off')

    # Plot segmentation mask
    plt.subplot(2, 3, 2)
    plt.imshow(segmentation, cmap='gray')
    plt.title('Segmentation Mask')
    plt.axis('off')

    # Plot combined heatmap
    plt.subplot(2, 3, 3)
    combined_heatmap = np.max(heatmaps, axis=0)
    plt.imshow(combined_heatmap, cmap='hot')
    plt.title('Combined Heatmap')
    plt.axis('off')

    # Plot individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        plt.subplot(2, 4, 5 + i)
        plt.imshow(heatmaps[i], cmap='hot')
        plt.title(f'{corner_names[i]} Corner')
        plt.axis('off')

    # Plot image with corners and segmentation overlay
    plt.subplot(2, 3, 6)
    plt.imshow(image)

    # Create a segmentation boundary overlay
    if segmentation is not None:
        # Create a binary mask
        binary_mask = (segmentation > 0.5).astype(np.uint8)

        # Find contours in the binary mask
        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # Draw contours on the image
        if contours:
            # Convert to RGB for overlay if grayscale
            overlay = np.zeros_like(image)
            cv2.drawContours(overlay, contours, -1, (0, 255, 0), 2)

            # Overlay the contours on the image
            alpha = 0.5
            plt.imshow(overlay, alpha=alpha)

    # Draw corners and connections
    valid_corners = [c for c in corners if c is not None]
    if len(valid_corners) == 4:
        # Draw corners
        for i, corner in enumerate(corners):
            plt.plot(corner[0], corner[1], 'o', markersize=10,
                     color=['red', 'green', 'blue', 'purple'][i])

        # Draw connections
        for i in range(4):
            j = (i + 1) % 4
            plt.plot([corners[i][0], corners[j][0]],
                     [corners[i][1], corners[j][1]],
                     '-', linewidth=2, color='yellow')

    plt.title('Segmentation-Guided Corner Detection')
    plt.axis('off')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path)
        print(f"Visualization saved to {output_path}")

    plt.show()


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Run inference with enhanced chess board detection model')
    parser.add_argument('--image_path', type=str, required=True, help='Path to input image')
    parser.add_argument('--model_path', type=str, required=True, help='Path to model weights')
    parser.add_argument('--output_dir', type=str, default='outputs', help='Output directory')
    parser.add_argument('--threshold', type=float, default=0.3, help='Detection threshold')
    parser.add_argument('--use_post_processing', action='store_true', help='Apply post-processing')
    parser.add_argument('--use_segmentation_guidance', action='store_true', default=True,
                        help='Use segmentation to guide corner detection')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    args = parser.parse_args()

    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() and not args.cpu else 'cpu')
    print(f"Using device: {device}")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load model
    print(f"Loading model from {args.model_path}")
    model = EnhancedChessBoardUNet(n_channels=3, bilinear=True)
    model.load_state_dict(torch.load(args.model_path, map_location=device))
    model = model.to(device)
    model.eval()

    # Load and preprocess image
    print(f"Loading image from {args.image_path}")
    image_tensor, original_image, original_size = load_image(args.image_path)

    # Run inference
    print("Running inference...")
    outputs = predict(model, image_tensor, device,
                     args.use_post_processing,
                     args.use_segmentation_guidance)

    # Process outputs
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]
    heatmaps = torch.sigmoid(outputs['corner_heatmaps']).cpu().numpy()[0]

    # Use segmentation-guided corner detection
    corners = segmentation_guided_corner_detection(segmentation, heatmaps, threshold=args.threshold)

    # Apply post-processing to corners if requested
    if args.use_post_processing and None in corners:
        # Try to enhance detection
        enhanced_heatmaps = enhance_detection_rate(heatmaps, threshold=args.threshold)
        corners = segmentation_guided_corner_detection(segmentation, enhanced_heatmaps, threshold=args.threshold * 0.8)

    # Apply geometric constraints if all corners are detected
    if args.use_post_processing and all(corner is not None for corner in corners):
        corners = enforce_geometric_constraints(corners, img_shape=heatmaps.shape[1:])

    # Scale corners to original image size
    scaled_corners = scale_corners_to_original(corners, original_size)

    # Visualize results
    output_path = os.path.join(args.output_dir, os.path.basename(args.image_path).split('.')[0] + '_result.png')
    visualize_results(original_image, segmentation, heatmaps, corners, output_path)

    # Print results
    print("Inference completed!")
    print(f"Results saved to {output_path}")
    if all(corner is not None for corner in corners):
        print("All corners detected successfully!")
        for i, corner in enumerate(['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']):
            print(f"{corner} corner: {scaled_corners[i]}")
    else:
        print("Warning: Not all corners were detected.")
        for i, corner in enumerate(['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']):
            print(f"{corner} corner: {scaled_corners[i] if corners[i] is not None else 'Not detected'}")


if __name__ == "__main__":
    main()
