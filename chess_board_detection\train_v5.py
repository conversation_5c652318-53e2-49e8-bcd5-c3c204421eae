"""
Training script for the v5 chess board detection model.
This version implements a two-phase training approach:
1. Phase 1: Focus on peak-to-second ratio and detection rate
2. Phase 2: Fine-tune with balanced weights
"""

import os
import argparse
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, ConcatDataset
from tqdm import tqdm
from sklearn.model_selection import KFold
import albumentations as A
from albumentations.pytorch import ToTensorV2

from config import MODELS_DIR, DATA_DIR, DEVICE, INPUT_SIZE
from models.enhanced_unet_v5 import EnhancedChessBoardUNetV5
from enhanced_loss_v5 import EnhancedCornerFocusedHeatmapLossV5, RobustSegmentationGuidanceLoss
from enhanced_loss_v4 import EnhancedDiceLoss, ImprovedGeometricConsistencyLoss
from utils.real_dataset import RealChessBoardDataset
from utils.augmentation_v5 import get_training_augmentation, get_validation_augmentation
from utils.metrics import calculate_corner_confidence


# Define a custom collate function to handle tensors of different sizes
def custom_collate_fn(batch):
    # Filter out samples with empty tensors
    batch = [sample for sample in batch if all(v is not None and (not isinstance(v, torch.Tensor) or v.numel() > 0) for v in sample.values())]

    if len(batch) == 0:
        return {}

    # Get all keys from the first sample
    keys = batch[0].keys()

    # Initialize the result dictionary
    result = {}

    # Process each key
    for key in keys:
        # Get all values for this key
        values = [sample[key] for sample in batch]

        # Check if all values are tensors
        if all(isinstance(v, torch.Tensor) for v in values):
            # Check if all tensors have the same shape
            shapes = [v.shape for v in values]
            if len(set(str(s) for s in shapes)) == 1:
                # All tensors have the same shape, stack them
                result[key] = torch.stack(values, 0)
            else:
                # Tensors have different shapes, pad them to the same shape
                max_dims = [max(s[i] for s in shapes) for i in range(len(shapes[0]))]
                padded_values = []

                for v in values:
                    # Calculate padding
                    padding = []
                    for i in range(len(v.shape)):
                        padding.extend([0, max_dims[i] - v.shape[i]])

                    # Pad the tensor
                    padded_v = F.pad(v, padding)
                    padded_values.append(padded_v)

                # Stack the padded tensors
                result[key] = torch.stack(padded_values, 0)
        else:
            # Check if all values are the same type
            if all(isinstance(v, type(values[0])) for v in values):
                # Try to convert to tensor if possible
                try:
                    # Convert to numpy array first to avoid the warning
                    values_np = np.array(values)
                    result[key] = torch.tensor(values_np)
                except:
                    # If conversion fails, store as a list
                    result[key] = values
            else:
                # Different types, store as a list
                result[key] = values

    return result


def train_model(model, dataloaders, criterion_seg, criterion_heatmap, criterion_geometric,
               optimizer, output_dir, num_epochs=120, heatmap_weight=1.5, geometric_weight=0.8,
               save_interval=10, phase_name=''):
    """
    Train the model.

    Args:
        model: Model to train
        dataloaders: Dictionary of dataloaders ('train', 'val')
        criterion_seg: Loss function for segmentation
        criterion_heatmap: Loss function for heatmaps
        criterion_geometric: Loss function for geometric consistency
        optimizer: Optimizer
        output_dir: Output directory
        num_epochs: Number of epochs
        heatmap_weight: Weight for heatmap loss
        geometric_weight: Weight for geometric loss
        save_interval: Interval to save model checkpoints
        phase_name: Name of the training phase

    Returns:
        model: Trained model
        history: Training history
    """
    # Create output directories
    checkpoints_dir = os.path.join(output_dir, 'checkpoints', 'v5')
    logs_dir = os.path.join(output_dir, 'logs', 'v5')
    vis_dir = os.path.join(output_dir, 'visualizations', 'v5')

    os.makedirs(checkpoints_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)

    print(f"Saving outputs to v5 folders in {output_dir}")

    # Initialize history
    history = {
        'train_loss': [],
        'train_seg_loss': [],
        'train_heatmap_loss': [],
        'train_geometric_loss': [],
        'train_heatmap_components': [],
        'train_corner_confidence': [],
        'val_loss': [],
        'val_seg_loss': [],
        'val_heatmap_loss': [],
        'val_geometric_loss': [],
        'val_heatmap_components': [],
        'val_corner_confidence': []
    }

    # Initialize best loss
    best_loss = float('inf')

    # Start training
    since = time.time()

    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)

        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode

            running_loss = 0.0
            running_seg_loss = 0.0
            running_heatmap_loss = 0.0
            running_geometric_loss = 0.0
            running_heatmap_components = {
                'mse_loss': 0.0,
                'separation_loss': 0.0,
                'peak_separation_loss': 0.0,
                'edge_suppression_loss': 0.0,
                'peak_enhancement_loss': 0.0,
                'peak_to_second_ratio_loss': 0.0,
                'detection_rate_loss': 0.0,
                'segmentation_guidance_loss': 0.0
            }
            running_corner_confidence = {
                'avg_peak_value': 0.0,
                'avg_peak_to_mean_ratio': 0.0,
                'avg_peak_to_second_ratio': 0.0,
                'detection_rate': 0.0
            }

            # Iterate over data
            for batch_idx, batch in enumerate(tqdm(dataloaders[phase], desc=f'{phase}')):
                # Check if batch is empty or not a dictionary
                if not batch or not isinstance(batch, dict):
                    print(f"Skipping empty or invalid batch: {batch}")
                    continue

                # Check if required keys exist
                if 'image' not in batch or 'mask' not in batch:
                    print(f"Skipping batch missing required keys: {batch.keys()}")
                    continue

                # Convert to device
                inputs = batch['image'].to(DEVICE)
                masks = batch['mask'].to(DEVICE)

                # Handle different key names for heatmaps
                if 'heatmaps' in batch:
                    heatmaps = batch['heatmaps'].to(DEVICE)
                elif 'corner_heatmaps' in batch:
                    heatmaps = batch['corner_heatmaps'].to(DEVICE)
                else:
                    print(f"Neither 'heatmaps' nor 'corner_heatmaps' found in batch with keys: {batch.keys()}")
                    continue

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    heatmap_outputs = outputs['corner_heatmaps']

                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    # Pass segmentation to heatmap loss for segmentation-guided corner detection
                    heatmap_loss, heatmap_components = criterion_heatmap(heatmap_outputs, heatmaps, seg_outputs)
                    geometric_loss = criterion_geometric(heatmap_outputs)

                    # Combined loss with custom weights
                    loss = seg_loss + heatmap_weight * heatmap_loss + geometric_weight * geometric_loss

                    # Print loss values for debugging in first few batches
                    if batch_idx < 3 and epoch < 3:
                        print(f"Batch {batch_idx}, {phase} - Seg Loss: {seg_loss.item():.4f}, "
                              f"Heatmap Loss: {heatmap_loss.item():.4f}, "
                              f"Geometric Loss: {geometric_loss.item():.4f}, "
                              f"Total: {loss.item():.4f}")

                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()

                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_heatmap_loss += heatmap_loss.item() * inputs.size(0)
                running_geometric_loss += geometric_loss.item() * inputs.size(0)

                # Track heatmap loss components
                for key, value in heatmap_components.items():
                    running_heatmap_components[key] += value * inputs.size(0)

                # Calculate and track corner confidence metrics
                confidence_metrics = calculate_corner_confidence(heatmap_outputs)
                for key, value in confidence_metrics.items():
                    running_corner_confidence[key] += value * inputs.size(0)

            # Calculate epoch losses
            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_heatmap_loss = running_heatmap_loss / len(dataloaders[phase].dataset)
            epoch_geometric_loss = running_geometric_loss / len(dataloaders[phase].dataset)

            # Calculate average heatmap components
            epoch_heatmap_components = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_heatmap_components.items()
            }

            # Calculate average corner confidence metrics
            epoch_corner_confidence = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_corner_confidence.items()
            }

            # Print epoch losses
            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, '
                  f'Heatmap Loss: {epoch_heatmap_loss:.4f}, Geometric Loss: {epoch_geometric_loss:.4f}')

            # Print corner confidence metrics with more prominence
            print(f'=== {phase} Corner Confidence Metrics ===')
            for key, value in epoch_corner_confidence.items():
                print(f'  {key}: {value:.4f}')

            # Calculate overall confidence score (average of metrics)
            avg_confidence = sum(epoch_corner_confidence.values()) / len(epoch_corner_confidence)
            print(f'  Overall Confidence Score: {avg_confidence:.4f}')

            # Print heatmap components
            print(f'{phase} Heatmap Components:')
            for key, value in epoch_heatmap_components.items():
                print(f'  {key}: {value:.4f}')

            # Save history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_heatmap_loss'].append(epoch_heatmap_loss)
            history[f'{phase}_geometric_loss'].append(epoch_geometric_loss)
            history[f'{phase}_heatmap_components'].append(epoch_heatmap_components)
            history[f'{phase}_corner_confidence'].append(epoch_corner_confidence)

            # Save best model
            if phase == 'val' and epoch_loss < best_loss:
                best_loss = epoch_loss
                torch.save(model.state_dict(), os.path.join(checkpoints_dir, f'best_model_{phase_name}.pth'))
                print(f'New best model saved with loss: {best_loss:.4f}')

        # Save checkpoint at specified intervals
        if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': epoch_loss,
                'history': history
            }, os.path.join(checkpoints_dir, f'checkpoint_epoch_{epoch+1}_{phase_name}.pth'))

            # Save history to JSON
            with open(os.path.join(logs_dir, f'history_epoch_{epoch+1}_{phase_name}.json'), 'w') as f:
                # Convert numpy values to Python types for JSON serialization
                json_history = {}
                for key, value in history.items():
                    if isinstance(value[0], dict):
                        json_history[key] = [{k: float(v) for k, v in d.items()} for d in value]
                    else:
                        json_history[key] = [float(v) for v in value]

                json.dump(json_history, f, indent=4)

            # Plot losses
            plt.figure(figsize=(15, 5))

            # Plot total loss
            plt.subplot(1, 3, 1)
            plt.plot(history['train_loss'], label='Train')
            plt.plot(history['val_loss'], label='Validation')
            plt.title('Total Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot segmentation loss
            plt.subplot(1, 3, 2)
            plt.plot(history['train_seg_loss'], label='Train')
            plt.plot(history['val_seg_loss'], label='Validation')
            plt.title('Segmentation Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot heatmap loss
            plt.subplot(1, 3, 3)
            plt.plot(history['train_heatmap_loss'], label='Train')
            plt.plot(history['val_heatmap_loss'], label='Validation')
            plt.title('Heatmap Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'losses_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot heatmap components
            plt.figure(figsize=(15, 15))

            component_keys = list(history['train_heatmap_components'][0].keys())
            num_components = len(component_keys)

            # Calculate grid dimensions
            grid_cols = 3
            grid_rows = (num_components + grid_cols - 1) // grid_cols  # Ceiling division

            for i, key in enumerate(component_keys):
                plt.subplot(grid_rows, grid_cols, i+1)
                plt.plot(
                    [comp[key] for comp in history['train_heatmap_components']],
                    label='Train'
                )
                plt.plot(
                    [comp[key] for comp in history['val_heatmap_components']],
                    label='Validation'
                )
                plt.title(key)
                plt.xlabel('Epoch')
                plt.ylabel('Value')
                plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'heatmap_components_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot corner confidence metrics
            plt.figure(figsize=(15, 12))

            confidence_keys = list(history['train_corner_confidence'][0].keys())
            for i, key in enumerate(confidence_keys):
                plt.subplot(2, 3, i+1)
                plt.plot(
                    [conf[key] for conf in history['train_corner_confidence']],
                    label='Train'
                )
                plt.plot(
                    [conf[key] for conf in history['val_corner_confidence']],
                    label='Validation'
                )
                plt.title(key)
                plt.xlabel('Epoch')
                plt.ylabel('Value')
                plt.legend()

            # Add overall confidence score plot
            plt.subplot(2, 3, 5)
            train_overall = [sum(conf.values()) / len(conf) for conf in history['train_corner_confidence']]
            val_overall = [sum(conf.values()) / len(conf) for conf in history['val_corner_confidence']]
            plt.plot(train_overall, label='Train')
            plt.plot(val_overall, label='Validation')
            plt.title('Overall Confidence Score')
            plt.xlabel('Epoch')
            plt.ylabel('Value')
            plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'corner_confidence_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

        print()

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')

    # Load best model weights
    model.load_state_dict(torch.load(os.path.join(checkpoints_dir, f'best_model_{phase_name}.pth')))

    return model, history


def create_expanded_validation_set(data_dir, annotation_file, fold_idx=0, n_folds=5):
    """
    Create an expanded validation set using k-fold cross-validation.

    Args:
        data_dir: Directory containing the images
        annotation_file: Path to the annotation file
        fold_idx: Index of the fold to use as validation
        n_folds: Number of folds

    Returns:
        train_dataset: Training dataset
        val_dataset: Validation dataset
    """
    # Load dataset
    full_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_training_augmentation()
    )

    # Create indices for k-fold cross-validation
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

    # Get train and validation indices
    indices = list(range(len(full_dataset)))
    splits = list(kf.split(indices))
    train_indices, val_indices = splits[fold_idx]

    # Create train and validation datasets
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)

    # Create multiple validation datasets with different augmentations
    val_datasets = []

    # Original validation set
    val_dataset_orig = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_validation_augmentation()
    )
    val_dataset_orig = torch.utils.data.Subset(val_dataset_orig, val_indices)
    val_datasets.append(val_dataset_orig)

    # Create additional validation sets with different augmentations
    for i in range(3):  # Create 3 additional validation sets
        # Create custom augmentation for this validation set
        custom_aug = A.Compose([
            A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
            A.Affine(scale=(1.0-0.1*i, 1.0+0.1*i), translate_percent=(0.05*i, 0.05*i), rotate=(-15*i, 15*i), p=0.8),  # Replaced ShiftScaleRotate with Affine
            A.RandomBrightnessContrast(brightness_limit=0.1*i, contrast_limit=0.1*i, p=0.7),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))

        val_dataset_aug = RealChessBoardDataset(
            data_dir=data_dir,
            annotation_file=annotation_file,
            transform=custom_aug
        )
        val_dataset_aug = torch.utils.data.Subset(val_dataset_aug, val_indices)
        val_datasets.append(val_dataset_aug)

    # Combine validation datasets
    val_dataset = ConcatDataset(val_datasets)

    return train_dataset, val_dataset


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train v5 chess board detection model')
    parser.add_argument('--data_dir', type=str, default=os.path.join(DATA_DIR, 'real'),
                        help='Data directory')
    parser.add_argument('--annotation_file', type=str, default=os.path.join(DATA_DIR, 'real_annotations.json'),
                        help='Annotation file')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--lr_phase1', type=float, default=0.001, help='Learning rate for phase 1')
    parser.add_argument('--lr_phase2', type=float, default=0.0005, help='Learning rate for phase 2')
    parser.add_argument('--epochs_phase1', type=int, default=40, help='Number of epochs for phase 1')
    parser.add_argument('--epochs_phase2', type=int, default=80, help='Number of epochs for phase 2')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--dropout_rate', type=float, default=0.2, help='Dropout rate')
    parser.add_argument('--weight_decay', type=float, default=1e-5, help='Weight decay')
    parser.add_argument('--save_interval', type=int, default=10, help='Interval to save model checkpoints')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--continue_from_v4', action='store_true', help='Continue training from v4 checkpoint')
    parser.add_argument('--skip_phase1', action='store_true', help='Skip phase 1 and go directly to phase 2')
    parser.add_argument('--fold_idx', type=int, default=0, help='Index of the fold to use as validation')
    parser.add_argument('--n_folds', type=int, default=5, help='Number of folds for cross-validation')
    args = parser.parse_args()

    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")

    # Create expanded validation set
    print("Creating expanded validation set...")
    train_dataset, val_dataset = create_expanded_validation_set(
        data_dir=args.data_dir,
        annotation_file=args.annotation_file,
        fold_idx=args.fold_idx,
        n_folds=args.n_folds
    )

    # Use the custom collate function defined at the module level

    # Create data loaders with custom collate function
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        collate_fn=custom_collate_fn
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=0,  # Use 0 workers to avoid multiprocessing issues
        collate_fn=custom_collate_fn
    )

    dataloaders = {
        'train': train_loader,
        'val': val_loader
    }

    print(f"Train dataset size: {len(train_dataset)}")
    print(f"Validation dataset size: {len(val_dataset)}")

    # Initialize model
    print("Initializing v5 model...")
    model = EnhancedChessBoardUNetV5(n_channels=3, dropout_rate=args.dropout_rate)

    # Load from v4 checkpoint if requested
    if args.continue_from_v4:
        v4_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v4')
        best_model_path = os.path.join(v4_checkpoints_dir, 'best_model.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v4 best model: {best_model_path}")
            # Load state dict into base model
            model.base_model.load_state_dict(torch.load(best_model_path, map_location=device))
            print("Loaded v4 best model into base model")
        else:
            print(f"Warning: v4 best model not found at {best_model_path}")

    model = model.to(device)
    print(f"Model moved to {device}")

    # Define loss functions
    criterion_seg = EnhancedDiceLoss()
    criterion_geometric = ImprovedGeometricConsistencyLoss(weight=1.0)  # Weight applied in train_model

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Phase 1: Focus on peak-to-second ratio and detection rate
    if not args.skip_phase1:
        print("\n=== Phase 1: Focus on peak-to-second ratio and detection rate ===")

        # Phase 1 configuration
        criterion_heatmap_phase1 = EnhancedCornerFocusedHeatmapLossV5(
            separation_weight=0.6,
            peak_separation_weight=0.5,
            edge_suppression_weight=0.7,
            peak_enhancement_weight=0.5,
            peak_to_second_ratio_weight=10.0,  # Higher weight for peak-to-second ratio
            detection_rate_weight=8.0,         # Higher weight for detection rate
            segmentation_guidance_weight=1.0
        )

        optimizer_phase1 = optim.Adam(
            model.parameters(),
            lr=args.lr_phase1,
            weight_decay=args.weight_decay
        )

        # Train phase 1
        model, history_phase1 = train_model(
            model=model,
            dataloaders=dataloaders,
            criterion_seg=criterion_seg,
            criterion_heatmap=criterion_heatmap_phase1,
            criterion_geometric=criterion_geometric,
            optimizer=optimizer_phase1,
            output_dir=args.output_dir,
            num_epochs=args.epochs_phase1,
            heatmap_weight=1.5,
            geometric_weight=0.8,
            save_interval=args.save_interval,
            phase_name='phase1'
        )

    # Phase 2: Fine-tune with balanced weights
    print("\n=== Phase 2: Fine-tune with balanced weights ===")

    # Phase 2 configuration
    criterion_heatmap_phase2 = EnhancedCornerFocusedHeatmapLossV5(
        separation_weight=0.6,
        peak_separation_weight=0.5,
        edge_suppression_weight=0.7,
        peak_enhancement_weight=0.5,
        peak_to_second_ratio_weight=8.0,   # Slightly reduced from phase 1
        detection_rate_weight=5.0,         # Slightly reduced from phase 1
        segmentation_guidance_weight=1.0
    )

    optimizer_phase2 = optim.Adam(
        model.parameters(),
        lr=args.lr_phase2,  # Lower learning rate for fine-tuning
        weight_decay=args.weight_decay
    )

    # Train phase 2
    model, history_phase2 = train_model(
        model=model,
        dataloaders=dataloaders,
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap_phase2,
        criterion_geometric=criterion_geometric,
        optimizer=optimizer_phase2,
        output_dir=args.output_dir,
        num_epochs=args.epochs_phase2,
        heatmap_weight=1.5,
        geometric_weight=0.8,
        save_interval=args.save_interval,
        phase_name='phase2'
    )

    print("Training completed!")


if __name__ == "__main__":
    main()
