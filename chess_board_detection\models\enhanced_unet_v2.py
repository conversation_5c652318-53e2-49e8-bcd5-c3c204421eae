"""
Enhanced U-Net V2 with Attention, Multi-scale Features, and Deep Supervision.
Implements state-of-the-art segmentation architecture for chess board detection.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class ChannelAttention(nn.Module):
    """Channel Attention Module (CAM) from CBAM."""
    
    def __init__(self, in_channels, reduction=16):
        super(ChannelAttention, self).__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)
        
        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class SpatialAttention(nn.Module):
    """Spatial Attention Module (SAM) from CBAM."""
    
    def __init__(self, kernel_size=7):
        super(SpatialAttention, self).__init__()
        self.conv1 = nn.Conv2d(2, 1, kernel_size, padding=kernel_size//2, bias=False)
        self.sigmoid = nn.Sigmoid()
    
    def forward(self, x):
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        x = torch.cat([avg_out, max_out], dim=1)
        x = self.conv1(x)
        return self.sigmoid(x)

class CBAM(nn.Module):
    """Convolutional Block Attention Module."""
    
    def __init__(self, in_channels, reduction=16, kernel_size=7):
        super(CBAM, self).__init__()
        self.channel_attention = ChannelAttention(in_channels, reduction)
        self.spatial_attention = SpatialAttention(kernel_size)
    
    def forward(self, x):
        x = x * self.channel_attention(x)
        x = x * self.spatial_attention(x)
        return x

class SEBlock(nn.Module):
    """Squeeze-and-Excitation Block."""
    
    def __init__(self, in_channels, reduction=16):
        super(SEBlock, self).__init__()
        self.squeeze = nn.AdaptiveAvgPool2d(1)
        self.excitation = nn.Sequential(
            nn.Linear(in_channels, in_channels // reduction, bias=False),
            nn.ReLU(inplace=True),
            nn.Linear(in_channels // reduction, in_channels, bias=False),
            nn.Sigmoid()
        )
    
    def forward(self, x):
        b, c, _, _ = x.size()
        y = self.squeeze(x).view(b, c)
        y = self.excitation(y).view(b, c, 1, 1)
        return x * y.expand_as(x)

class EnhancedDoubleConv(nn.Module):
    """Enhanced double convolution with attention and residual connections."""
    
    def __init__(self, in_channels, out_channels, mid_channels=None, use_attention=True, use_se=True):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
        
        # Attention mechanisms
        self.use_attention = use_attention
        self.use_se = use_se
        
        if use_attention:
            self.cbam = CBAM(out_channels)
        
        if use_se:
            self.se = SEBlock(out_channels)
        
        # Residual connection
        self.residual = None
        if in_channels != out_channels:
            self.residual = nn.Conv2d(in_channels, out_channels, 1, bias=False)
    
    def forward(self, x):
        identity = x
        out = self.double_conv(x)
        
        if self.use_attention:
            out = self.cbam(out)
        
        if self.use_se:
            out = self.se(out)
        
        # Residual connection
        if self.residual is not None:
            identity = self.residual(identity)
        
        if identity.shape == out.shape:
            out = out + identity
        
        return out

class EnhancedDown(nn.Module):
    """Enhanced downscaling with attention."""
    
    def __init__(self, in_channels, out_channels, use_attention=True):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            EnhancedDoubleConv(in_channels, out_channels, use_attention=use_attention)
        )
    
    def forward(self, x):
        return self.maxpool_conv(x)

class EnhancedUp(nn.Module):
    """Enhanced upscaling with attention and residual connections."""
    
    def __init__(self, in_channels, out_channels, bilinear=True, use_attention=True):
        super().__init__()
        
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = EnhancedDoubleConv(in_channels, out_channels, in_channels // 2, use_attention=use_attention)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = EnhancedDoubleConv(in_channels, out_channels, use_attention=use_attention)
    
    def forward(self, x1, x2):
        x1 = self.up(x1)
        
        # Handle size mismatch
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]
        
        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class MultiScaleFeaturePyramid(nn.Module):
    """Multi-scale feature pyramid for enhanced feature extraction."""
    
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.conv1x1 = nn.Conv2d(in_channels, out_channels, 1)
        self.conv3x3 = nn.Conv2d(in_channels, out_channels, 3, padding=1)
        self.conv5x5 = nn.Conv2d(in_channels, out_channels, 5, padding=2)
        self.conv7x7 = nn.Conv2d(in_channels, out_channels, 7, padding=3)
        
        self.fusion = nn.Conv2d(out_channels * 4, out_channels, 1)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)
    
    def forward(self, x):
        feat1 = self.conv1x1(x)
        feat3 = self.conv3x3(x)
        feat5 = self.conv5x5(x)
        feat7 = self.conv7x7(x)
        
        fused = torch.cat([feat1, feat3, feat5, feat7], dim=1)
        out = self.fusion(fused)
        out = self.bn(out)
        out = self.relu(out)
        
        return out

class DeepSupervisionHead(nn.Module):
    """Deep supervision head for intermediate supervision."""
    
    def __init__(self, in_channels, num_classes=1):
        super().__init__()
        self.conv = nn.Conv2d(in_channels, num_classes, 1)
    
    def forward(self, x, target_size):
        x = self.conv(x)
        x = F.interpolate(x, size=target_size, mode='bilinear', align_corners=True)
        return x

class EnhancedUNetV2(nn.Module):
    """
    Enhanced U-Net V2 with:
    - CBAM attention mechanisms
    - Multi-scale feature pyramid
    - Deep supervision
    - Residual connections
    - Squeeze-and-Excitation blocks
    """
    
    def __init__(self, n_channels=3, n_classes=1, bilinear=False, deep_supervision=True):
        super(EnhancedUNetV2, self).__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        self.bilinear = bilinear
        self.deep_supervision = deep_supervision
        
        # Encoder
        self.inc = EnhancedDoubleConv(n_channels, 64)
        self.down1 = EnhancedDown(64, 128)
        self.down2 = EnhancedDown(128, 256)
        self.down3 = EnhancedDown(256, 512)
        
        factor = 2 if bilinear else 1
        self.down4 = EnhancedDown(512, 1024 // factor)
        
        # Multi-scale feature pyramid at bottleneck
        self.msfp = MultiScaleFeaturePyramid(1024 // factor, 1024 // factor)
        
        # Decoder
        self.up1 = EnhancedUp(1024, 512 // factor, bilinear)
        self.up2 = EnhancedUp(512, 256 // factor, bilinear)
        self.up3 = EnhancedUp(256, 128 // factor, bilinear)
        self.up4 = EnhancedUp(128, 64, bilinear)
        
        # Output head
        self.outc = nn.Conv2d(64, n_classes, kernel_size=1)
        
        # Deep supervision heads
        if deep_supervision:
            self.ds1 = DeepSupervisionHead(512 // factor, n_classes)
            self.ds2 = DeepSupervisionHead(256 // factor, n_classes)
            self.ds3 = DeepSupervisionHead(128 // factor, n_classes)
    
    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        
        # Multi-scale feature pyramid
        x5 = self.msfp(x5)
        
        # Decoder
        x = self.up1(x5, x4)
        if self.deep_supervision:
            ds1 = self.ds1(x, (x.size(2) * 8, x.size(3) * 8))
        
        x = self.up2(x, x3)
        if self.deep_supervision:
            ds2 = self.ds2(x, (x.size(2) * 4, x.size(3) * 4))
        
        x = self.up3(x, x2)
        if self.deep_supervision:
            ds3 = self.ds3(x, (x.size(2) * 2, x.size(3) * 2))
        
        x = self.up4(x, x1)
        logits = self.outc(x)
        
        if self.deep_supervision and self.training:
            return logits, ds1, ds2, ds3
        else:
            return logits

def get_enhanced_model(model_type="enhanced_v2", n_channels=3, n_classes=1, deep_supervision=True):
    """Get the enhanced U-Net V2 model."""
    return EnhancedUNetV2(
        n_channels=n_channels, 
        n_classes=n_classes, 
        bilinear=True,
        deep_supervision=deep_supervision
    )

if __name__ == "__main__":
    # Test the enhanced model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = get_enhanced_model()
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Enhanced U-Net V2 parameters: {total_params:,}")
    
    # Test forward pass
    x = torch.randn(2, 3, 512, 512).to(device)
    
    with torch.no_grad():
        model.eval()
        output = model(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {output.shape}")
        
        # Test training mode (with deep supervision)
        model.train()
        outputs = model(x)
        if isinstance(outputs, tuple):
            print(f"Deep supervision outputs: {len(outputs)} heads")
            for i, out in enumerate(outputs):
                print(f"  Head {i}: {out.shape}")
        else:
            print(f"Single output: {outputs.shape}")
