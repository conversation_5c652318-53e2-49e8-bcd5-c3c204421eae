"""
Improved Enhanced U-Net model for chess board detection (v5).
This version includes significant improvements to address the issues identified in Phase 1:
1. Enhanced peak sharpening module with stronger suppression of secondary peaks
2. Improved geometric refinement with better spatial constraints
3. More robust detection recovery mechanism
4. Increased regularization with batch normalization and spatial dropout
5. Segmentation-guided corner detection with soft boundary approach
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .enhanced_unet_v5 import EnhancedChessBoardUNetV5, PeakSharpeningModule, GeometricRefinementModule, DetectionRecoveryModule


class GradualPeakSharpening(nn.Module):
    """
    Gradual peak sharpening module that increases its effect over training epochs.
    This allows the model to adapt gradually to stronger peak sharpening.
    """
    def __init__(self, channels=4, start_epoch=1, full_effect_epoch=20):
        super(GradualPeakSharpening, self).__init__()
        self.channels = channels
        self.start_epoch = start_epoch
        self.full_effect_epoch = full_effect_epoch
        self.current_epoch = 0

        # Non-linear transformation for peak enhancement
        self.peak_enhancer = nn.Sequential(
            nn.Conv2d(channels, channels*2, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=1),
            nn.Sigmoid()
        )

    def set_epoch(self, epoch):
        """Update the current epoch for gradual activation."""
        self.current_epoch = epoch

    def forward(self, heatmaps):
        # Calculate strength factor (0.1 to 1.0)
        if self.current_epoch < self.start_epoch:
            strength = 0.1
        elif self.current_epoch >= self.full_effect_epoch:
            strength = 1.0
        else:
            strength = 0.1 + 0.9 * (self.current_epoch - self.start_epoch) / (self.full_effect_epoch - self.start_epoch)

        # Original heatmaps preserved
        original = heatmaps.clone()

        # Generate enhancement mask
        enhancement_mask = self.peak_enhancer(heatmaps)

        # Apply non-linear enhancement to create sharper peaks
        enhanced = torch.pow(heatmaps, 2) * enhancement_mask

        # Blend original and enhanced based on strength
        return original * (1 - strength) + enhanced * strength


class ImprovedPeakSharpeningModule(nn.Module):
    """
    Improved peak sharpening module with stronger suppression of secondary peaks
    and enhanced primary peak amplification.
    """
    def __init__(self, channels=4):
        super(ImprovedPeakSharpeningModule, self).__init__()

        # Peak detection with larger receptive field
        self.peak_detector = nn.Sequential(
            nn.Conv2d(channels, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=3, padding=1),
        )

        # Attention mechanism with stronger focus
        self.attention = nn.Sequential(
            nn.Conv2d(channels, channels*2, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels*2, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=1),
            nn.Sigmoid()  # Stronger bounding of attention values
        )

        # More aggressive suppression mechanism
        self.suppressor = nn.Sequential(
            nn.Conv2d(channels*2, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels*2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv2d(channels*2, channels, kernel_size=3, padding=1),
            nn.Tanh()  # Allow both enhancement and suppression
        )

        # Secondary peak identification
        self.secondary_peak_detector = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
        )

        # Gradual peak sharpening for controlled enhancement
        self.gradual_sharpener = GradualPeakSharpening(channels)

    def set_epoch(self, epoch):
        """Update the current epoch for gradual activation."""
        self.gradual_sharpener.set_epoch(epoch)

    def forward(self, heatmaps):
        # Detect peaks
        peak_map = self.peak_detector(heatmaps)

        # Generate attention weights (stronger focus on primary peaks)
        attention = self.attention(heatmaps) * 2.0  # Amplify attention effect

        # Apply attention to enhance primary peaks
        enhanced = heatmaps * (1 + attention)

        # Identify potential secondary peaks
        secondary_peaks = self.secondary_peak_detector(heatmaps)

        # Concatenate original, enhanced, and secondary peak info
        combined = torch.cat([heatmaps, enhanced], dim=1)

        # Apply suppression with awareness of secondary peaks
        suppression = self.suppressor(combined)

        # Apply suppression more aggressively to secondary peaks
        intermediate = heatmaps + suppression - (secondary_peaks * 0.5)

        # Apply gradual peak sharpening for final enhancement
        output = self.gradual_sharpener(intermediate)

        return output


class ImprovedGeometricRefinementModule(nn.Module):
    """
    Improved geometric refinement module with better spatial constraints
    and global context awareness.
    """
    def __init__(self, channels=4, features=64):
        super(ImprovedGeometricRefinementModule, self).__init__()

        # Feature extraction with larger receptive field
        self.features = nn.Sequential(
            nn.Conv2d(channels, features, kernel_size=5, padding=2),
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=5, padding=2),
            nn.BatchNorm2d(features),
            nn.ReLU()
        )

        # Global context module
        self.global_context = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(features, features // 4, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(features // 4, features, kernel_size=1),
            nn.Sigmoid()
        )

        # Geometric relationship modeling with spatial constraints
        self.geometric = nn.Sequential(
            nn.Conv2d(features, features, kernel_size=3, padding=1, dilation=1),
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=3, padding=2, dilation=2),
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=3, padding=4, dilation=4),
            nn.BatchNorm2d(features),
            nn.ReLU()
        )

        # Refinement with residual connection
        self.refine = nn.Sequential(
            nn.Conv2d(features * 2 + channels, features, kernel_size=3, padding=1),
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, channels, kernel_size=3, padding=1),
            nn.Tanh()  # Allow both positive and negative adjustments
        )

    def forward(self, heatmaps, segmentation=None):
        # Extract features
        feat = self.features(heatmaps)

        # Apply global context
        context = self.global_context(feat)
        feat_with_context = feat * context

        # Model geometric relationships
        geo = self.geometric(feat_with_context)

        # Concatenate with original features and heatmaps
        combined = torch.cat([geo, feat, heatmaps], dim=1)

        # Refine heatmaps
        refinement = self.refine(combined)

        # Apply refinement with controlled magnitude
        output = heatmaps + refinement * 0.2  # Scale to prevent large changes

        return output


class ImprovedDetectionRecoveryModule(nn.Module):
    """
    Improved detection recovery module with more robust recovery mechanism
    and multi-scale feature fusion.
    """
    def __init__(self, channels=4, features=64):
        super(ImprovedDetectionRecoveryModule, self).__init__()

        # Multi-scale detection with different kernel sizes
        self.multi_scale = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(channels, features, kernel_size=k, padding=k//2),
                nn.BatchNorm2d(features),
                nn.ReLU()
            ) for k in [3, 5, 7, 9]  # Added larger kernel for wider context
        ])

        # Feature fusion with attention
        self.fusion_attention = nn.Sequential(
            nn.Conv2d(features * 4, features, kernel_size=1),
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, 4, kernel_size=1),
            nn.Softmax(dim=1)
        )

        # Feature fusion
        self.fusion = nn.Sequential(
            nn.Conv2d(features * 4, features, kernel_size=1),
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, channels, kernel_size=3, padding=1),
            nn.Sigmoid()
        )

        # Recovery mechanism with segmentation guidance
        self.recovery = nn.Sequential(
            nn.Conv2d(channels * 2 + 1, features, kernel_size=3, padding=1),  # +1 for segmentation
            nn.BatchNorm2d(features),
            nn.ReLU(),
            nn.Conv2d(features, channels, kernel_size=3, padding=1),
            nn.Tanh()  # Allow both positive and negative adjustments
        )

    def forward(self, heatmaps, segmentation=None):
        # Multi-scale detection
        multi_scale_features = [module(heatmaps) for module in self.multi_scale]

        # Concatenate multi-scale features
        concat_features = torch.cat(multi_scale_features, dim=1)

        # Generate attention weights for each scale
        attention_weights = self.fusion_attention(concat_features)

        # Apply attention to each scale and sum
        attended_features = sum([attention_weights[:, i:i+1] * multi_scale_features[i]
                               for i in range(len(multi_scale_features))])

        # Fuse features
        recovery_weights = self.fusion(concat_features)

        # Apply recovery weights
        recovery_heatmaps = heatmaps * recovery_weights

        # Prepare inputs for recovery module
        if segmentation is not None:
            combined = torch.cat([heatmaps, recovery_heatmaps, segmentation], dim=1)
        else:
            combined = torch.cat([heatmaps, recovery_heatmaps, torch.zeros_like(heatmaps[:, :1])], dim=1)

        # Generate recovery adjustments
        recovery_adjustment = self.recovery(combined)

        # Apply recovery with controlled magnitude
        output = heatmaps + recovery_adjustment * 0.5  # Scale to prevent large changes

        return output


class ImprovedEnhancedChessBoardUNetV5(nn.Module):
    """
    Improved Enhanced U-Net for chess board detection with significant v5 improvements:
    - Enhanced peak sharpening module with stronger suppression
    - Improved geometric refinement with better spatial constraints
    - More robust detection recovery mechanism
    - Increased regularization with batch normalization and spatial dropout
    - Segmentation-guided corner detection with soft boundary approach
    - Curriculum learning for gradual enhancement of peak sharpening
    """
    def __init__(self, n_channels=3, dropout_rate=0.3, use_batch_norm=True, spatial_dropout=True):
        super(ImprovedEnhancedChessBoardUNetV5, self).__init__()

        # Base model from v5
        self.base_model = EnhancedChessBoardUNetV5(n_channels, dropout_rate=dropout_rate)

        # Replace with improved modules
        self.peak_sharpener = ImprovedPeakSharpeningModule()
        self.geometric_refiner = ImprovedGeometricRefinementModule()
        self.detection_recovery = ImprovedDetectionRecoveryModule()

        # Add batch normalization if requested
        self.use_batch_norm = use_batch_norm
        if use_batch_norm:
            self.bn_seg = nn.BatchNorm2d(1)  # For segmentation output
            self.bn_heatmap = nn.BatchNorm2d(4)  # For heatmap output

        # Use spatial dropout if requested
        self.spatial_dropout = spatial_dropout
        if spatial_dropout:
            self.spatial_dropout_layer = nn.Dropout2d(dropout_rate)

        # Track current epoch for curriculum learning
        self.current_epoch = 0

    def set_epoch(self, epoch):
        """Update the current epoch for curriculum learning in all modules."""
        self.current_epoch = epoch
        self.peak_sharpener.set_epoch(epoch)

    def forward(self, x):
        # Get base outputs
        outputs = self.base_model(x)

        # Apply batch normalization if enabled
        if self.use_batch_norm:
            outputs['segmentation'] = self.bn_seg(outputs['segmentation'])
            outputs['corner_heatmaps'] = self.bn_heatmap(outputs['corner_heatmaps'])

        # Apply improved peak sharpening
        sharpened_heatmaps = self.peak_sharpener(outputs['corner_heatmaps'])

        # Apply spatial dropout if enabled
        if self.spatial_dropout:
            sharpened_heatmaps = self.spatial_dropout_layer(sharpened_heatmaps)

        # Apply improved geometric refinement
        refined_heatmaps = self.geometric_refiner(sharpened_heatmaps, outputs['segmentation'])

        # Apply improved detection recovery
        recovered_heatmaps = self.detection_recovery(refined_heatmaps, outputs['segmentation'])

        # Update outputs
        outputs['corner_heatmaps'] = recovered_heatmaps

        return outputs
