PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_efficient_unet_v3.py
🚀 Starting Efficient U-Net V3 Training...
Configuration: {
  "dataset_dir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\data\\augmented\\v5.2\\augmented_20250518_153326",
  "save_dir": "chess_board_detection/efficient_unet_v3_results",
  "epochs": 50,
  "batch_size": 8,
  "learning_rate": 0.001,
  "base_channels": 32,
  "num_workers": 0
}
Using device: cuda
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.4 GB
Creating dataloaders...
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Creating Efficient U-Net V3...
Model parameters: 314,195
Efficiency vs V1: 0.018x (98.2% reduction)
Efficiency vs V2: 0.008x (99.2% reduction)
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_efficient_unet_v3.py:227: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
Starting training for 50 epochs...

Epoch 1/50
Training:   0%|                                                           | 0/11 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  image = torch.load(image_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  mask = torch.load(mask_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_efficient_unet_v3.py:98: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
Training: 100%|███████████| 11/11 [00:02<00:00,  4.18it/s, Loss=13.3617, Dice=0.1259, IoU=0.0672]
Validation: 100%|████████████| 3/3 [00:00<00:00,  9.26it/s, Loss=5.5992, Dice=0.1660, IoU=0.0905] 
E:\New folder\Lib\site-packages\torch\optim\lr_scheduler.py:224: UserWarning: Detected call of `lr_scheduler.step()` before `optimizer.step()`. In PyTorch 1.1.0 and later, you should call them in the opposite order: `optimizer.step()` before `lr_scheduler.step()`.  Failure to do this will result in PyTorch skipping the first value of the learning rate schedule. See more details at https://pytorch.org/docs/stable/optim.html#how-to-adjust-learning-rate
  warnings.warn(
Train - Loss: 13.6050, Dice: 0.5228, IoU: 0.3636
Val   - Loss: 5.9472, Dice: 0.2657, IoU: 0.1558
LR: 0.000999
✅ New best model saved! Val Dice: 0.2657

Epoch 2/50
Training: 100%|███████████| 11/11 [00:01<00:00,  5.72it/s, Loss=13.7245, Dice=0.8249, IoU=0.7019] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.21it/s, Loss=2.1667, Dice=0.3141, IoU=0.1863] 
Train - Loss: 34.7445, Dice: 0.5472, IoU: 0.4121
Val   - Loss: 4.1798, Dice: 0.3382, IoU: 0.2038
LR: 0.000996
✅ New best model saved! Val Dice: 0.3382

Epoch 3/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.82it/s, Loss=1.0311, Dice=0.9253, IoU=0.8609] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.20it/s, Loss=2.7681, Dice=0.6432, IoU=0.4740] 
Train - Loss: 14.2591, Dice: 0.6422, IoU: 0.5281
Val   - Loss: 4.3107, Dice: 0.5515, IoU: 0.3860
LR: 0.000991
✅ New best model saved! Val Dice: 0.5515

Epoch 4/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.86it/s, Loss=1.0641, Dice=0.9233, IoU=0.8576] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.79it/s, Loss=6.4239, Dice=0.6568, IoU=0.4890] 
Train - Loss: 3.3233, Dice: 0.7967, IoU: 0.6797
Val   - Loss: 7.8941, Dice: 0.5704, IoU: 0.4041
LR: 0.000984
✅ New best model saved! Val Dice: 0.5704

Epoch 5/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.80it/s, Loss=2.5190, Dice=0.8283, IoU=0.7069] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.43it/s, Loss=1.7452, Dice=0.7068, IoU=0.5466] 
Train - Loss: 1.6823, Dice: 0.8576, IoU: 0.7547
Val   - Loss: 2.7226, Dice: 0.6278, IoU: 0.4628
LR: 0.000976
✅ New best model saved! Val Dice: 0.6278

Epoch 6/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.97it/s, Loss=0.6840, Dice=0.8945, IoU=0.8092] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.54it/s, Loss=1.8485, Dice=0.7328, IoU=0.5782] 
Train - Loss: 1.3722, Dice: 0.8389, IoU: 0.7318
Val   - Loss: 2.7114, Dice: 0.6506, IoU: 0.4871
LR: 0.000965
✅ New best model saved! Val Dice: 0.6506

Epoch 7/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.98it/s, Loss=1.2587, Dice=0.8418, IoU=0.7268]
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.47it/s, Loss=0.6030, Dice=0.8030, IoU=0.6708] 
Train - Loss: 1.0187, Dice: 0.8647, IoU: 0.7666
Val   - Loss: 1.2057, Dice: 0.7114, IoU: 0.5574
LR: 0.000953
✅ New best model saved! Val Dice: 0.7114

Epoch 8/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.96it/s, Loss=2.3400, Dice=0.7370, IoU=0.5835] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.32it/s, Loss=3.0257, Dice=0.6926, IoU=0.5297] 
Train - Loss: 1.3575, Dice: 0.7176, IoU: 0.5947
Val   - Loss: 3.7547, Dice: 0.6153, IoU: 0.4494
LR: 0.000939

Epoch 9/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.96it/s, Loss=1.8098, Dice=0.7938, IoU=0.6580] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.88it/s, Loss=0.5864, Dice=0.7939, IoU=0.6583] 
Train - Loss: 1.2741, Dice: 0.8261, IoU: 0.7099
Val   - Loss: 1.2229, Dice: 0.7101, IoU: 0.5560
LR: 0.000923

Epoch 10/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.88it/s, Loss=0.8779, Dice=0.7549, IoU=0.6062] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.07it/s, Loss=0.7705, Dice=0.7859, IoU=0.6473] 
Train - Loss: 0.9459, Dice: 0.8593, IoU: 0.7589
Val   - Loss: 1.7053, Dice: 0.6976, IoU: 0.5411
LR: 0.000905

Epoch 11/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.99it/s, Loss=2.4014, Dice=0.5889, IoU=0.4173] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.68it/s, Loss=0.9212, Dice=0.7471, IoU=0.5962] 
Train - Loss: 1.0290, Dice: 0.8405, IoU: 0.7399
Val   - Loss: 2.1110, Dice: 0.6466, IoU: 0.4845
LR: 0.000886

Epoch 12/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.90it/s, Loss=0.9580, Dice=0.7501, IoU=0.6001] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.81it/s, Loss=1.7546, Dice=0.7251, IoU=0.5687] 
Train - Loss: 0.9872, Dice: 0.8079, IoU: 0.6949
Val   - Loss: 3.0393, Dice: 0.6398, IoU: 0.4758
LR: 0.000866

Epoch 13/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.87it/s, Loss=1.3195, Dice=0.6534, IoU=0.4853] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 10.60it/s, Loss=0.5041, Dice=0.8180, IoU=0.6920] 
Train - Loss: 1.2773, Dice: 0.8154, IoU: 0.6972
Val   - Loss: 1.7925, Dice: 0.7237, IoU: 0.5727
LR: 0.000844
✅ New best model saved! Val Dice: 0.7237

Epoch 14/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.90it/s, Loss=0.5862, Dice=0.8786, IoU=0.7836] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.73it/s, Loss=1.1923, Dice=0.7669, IoU=0.6219] 
Train - Loss: 0.8328, Dice: 0.8462, IoU: 0.7517
Val   - Loss: 2.0008, Dice: 0.6734, IoU: 0.5143
LR: 0.000821

Epoch 15/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.79it/s, Loss=0.2691, Dice=0.9476, IoU=0.9004] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.98it/s, Loss=0.7775, Dice=0.7807, IoU=0.6403] 
Train - Loss: 0.5962, Dice: 0.8750, IoU: 0.7856
Val   - Loss: 1.1789, Dice: 0.7016, IoU: 0.5461
LR: 0.000796

Epoch 16/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.84it/s, Loss=0.7956, Dice=0.5590, IoU=0.3879] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.24it/s, Loss=0.6599, Dice=0.7879, IoU=0.6501] 
Train - Loss: 0.4332, Dice: 0.8727, IoU: 0.7880
Val   - Loss: 1.2494, Dice: 0.7025, IoU: 0.5470
LR: 0.000770

Epoch 17/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.83it/s, Loss=0.3408, Dice=0.9281, IoU=0.8659] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.96it/s, Loss=1.8761, Dice=0.7047, IoU=0.5440] 
Train - Loss: 0.4139, Dice: 0.8941, IoU: 0.8135
Val   - Loss: 2.4751, Dice: 0.6304, IoU: 0.4653
LR: 0.000743

Epoch 18/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.76it/s, Loss=0.4273, Dice=0.9226, IoU=0.8563] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.09it/s, Loss=1.3252, Dice=0.7258, IoU=0.5696] 
Train - Loss: 0.4820, Dice: 0.9064, IoU: 0.8305
Val   - Loss: 1.8427, Dice: 0.6405, IoU: 0.4773
LR: 0.000716

Epoch 19/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.91it/s, Loss=0.6476, Dice=0.9118, IoU=0.8379] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.31it/s, Loss=1.2383, Dice=0.7380, IoU=0.5848] 
Train - Loss: 0.4075, Dice: 0.8941, IoU: 0.8155
Val   - Loss: 1.8087, Dice: 0.6587, IoU: 0.4972
LR: 0.000687

Epoch 20/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.83it/s, Loss=0.2564, Dice=0.8907, IoU=0.8030] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.05it/s, Loss=0.8553, Dice=0.7572, IoU=0.6092] 
Train - Loss: 0.3245, Dice: 0.9111, IoU: 0.8397
Val   - Loss: 1.2996, Dice: 0.6819, IoU: 0.5228
LR: 0.000658

Epoch 21/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.86it/s, Loss=0.3894, Dice=0.8818, IoU=0.7886] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.94it/s, Loss=0.8152, Dice=0.7511, IoU=0.6014] 
Train - Loss: 0.4018, Dice: 0.8844, IoU: 0.7952
Val   - Loss: 1.3328, Dice: 0.6713, IoU: 0.5113
LR: 0.000628

Epoch 22/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.73it/s, Loss=0.2762, Dice=0.8902, IoU=0.8022] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.96it/s, Loss=0.4679, Dice=0.8000, IoU=0.6666] 
Train - Loss: 0.3104, Dice: 0.9089, IoU: 0.8371
Val   - Loss: 0.8943, Dice: 0.7172, IoU: 0.5647
LR: 0.000598

Epoch 23/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.84it/s, Loss=0.8252, Dice=0.8724, IoU=0.7736] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.10it/s, Loss=0.6100, Dice=0.7637, IoU=0.6177] 
Train - Loss: 0.3964, Dice: 0.8976, IoU: 0.8209
Val   - Loss: 1.0576, Dice: 0.6867, IoU: 0.5291
LR: 0.000567

Epoch 24/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.90it/s, Loss=0.3117, Dice=0.9367, IoU=0.8809] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.44it/s, Loss=1.4005, Dice=0.7162, IoU=0.5579] 
Train - Loss: 0.4023, Dice: 0.8876, IoU: 0.8025
Val   - Loss: 2.0777, Dice: 0.6388, IoU: 0.4742
LR: 0.000536

Epoch 25/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.88it/s, Loss=0.4198, Dice=0.9004, IoU=0.8189] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.94it/s, Loss=0.4321, Dice=0.8039, IoU=0.6720] 
Train - Loss: 0.4041, Dice: 0.8982, IoU: 0.8228
Val   - Loss: 0.8985, Dice: 0.7204, IoU: 0.5687
LR: 0.000505

Epoch 26/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.82it/s, Loss=0.1591, Dice=0.9593, IoU=0.9219] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.36it/s, Loss=0.6917, Dice=0.7692, IoU=0.6250] 
Train - Loss: 0.3018, Dice: 0.9021, IoU: 0.8256
Val   - Loss: 1.2027, Dice: 0.6873, IoU: 0.5301
LR: 0.000474

Epoch 27/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.86it/s, Loss=0.5189, Dice=0.7310, IoU=0.5760] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.17it/s, Loss=1.0224, Dice=0.7437, IoU=0.5920] 
Train - Loss: 0.3386, Dice: 0.8912, IoU: 0.8095
Val   - Loss: 1.4558, Dice: 0.6661, IoU: 0.5053
LR: 0.000443

Epoch 28/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.86it/s, Loss=0.3456, Dice=0.8251, IoU=0.7023] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.41it/s, Loss=0.3942, Dice=0.8041, IoU=0.6724] 
Train - Loss: 0.2597, Dice: 0.9115, IoU: 0.8405
Val   - Loss: 0.6777, Dice: 0.7247, IoU: 0.5742
LR: 0.000412
✅ New best model saved! Val Dice: 0.7247

Epoch 29/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.88it/s, Loss=0.6274, Dice=0.8542, IoU=0.7455] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.97it/s, Loss=0.6539, Dice=0.7657, IoU=0.6204] 
Train - Loss: 0.2975, Dice: 0.8959, IoU: 0.8165
Val   - Loss: 1.0477, Dice: 0.6846, IoU: 0.5266
LR: 0.000382

Epoch 30/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.88it/s, Loss=0.2467, Dice=0.9563, IoU=0.9163] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.74it/s, Loss=1.1307, Dice=0.7246, IoU=0.5681] 
Train - Loss: 0.2693, Dice: 0.9184, IoU: 0.8516
Val   - Loss: 1.5830, Dice: 0.6506, IoU: 0.4878
LR: 0.000352

Epoch 31/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.84it/s, Loss=0.4484, Dice=0.9138, IoU=0.8412] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.93it/s, Loss=1.0185, Dice=0.7275, IoU=0.5718] 
Train - Loss: 0.2625, Dice: 0.9187, IoU: 0.8518
Val   - Loss: 1.5275, Dice: 0.6514, IoU: 0.4887
LR: 0.000323

Epoch 32/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.87it/s, Loss=0.6175, Dice=0.8231, IoU=0.6994] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.58it/s, Loss=0.6935, Dice=0.7518, IoU=0.6023] 
Train - Loss: 0.2741, Dice: 0.9136, IoU: 0.8441
Val   - Loss: 1.1831, Dice: 0.6692, IoU: 0.5090
LR: 0.000294

Epoch 33/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.90it/s, Loss=0.2098, Dice=0.9228, IoU=0.8567] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.10it/s, Loss=1.3489, Dice=0.7016, IoU=0.5404] 
Train - Loss: 0.2171, Dice: 0.9254, IoU: 0.8628
Val   - Loss: 1.7905, Dice: 0.6281, IoU: 0.4632
LR: 0.000267

Epoch 34/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.83it/s, Loss=0.4193, Dice=0.9105, IoU=0.8356] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.46it/s, Loss=1.4968, Dice=0.7007, IoU=0.5393] 
Train - Loss: 0.2523, Dice: 0.9113, IoU: 0.8400
Val   - Loss: 1.8357, Dice: 0.6321, IoU: 0.4670
LR: 0.000240

Epoch 35/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.84it/s, Loss=0.6305, Dice=0.9000, IoU=0.8182] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.93it/s, Loss=0.9048, Dice=0.7309, IoU=0.5759] 
Train - Loss: 0.2297, Dice: 0.9283, IoU: 0.8676
Val   - Loss: 1.3281, Dice: 0.6550, IoU: 0.4924
LR: 0.000214

Epoch 36/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.93it/s, Loss=0.4045, Dice=0.9136, IoU=0.8410] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.41it/s, Loss=0.9061, Dice=0.7324, IoU=0.5778] 
Train - Loss: 0.2302, Dice: 0.9268, IoU: 0.8653
Val   - Loss: 1.3183, Dice: 0.6575, IoU: 0.4953
LR: 0.000189

Epoch 37/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.88it/s, Loss=0.4110, Dice=0.9053, IoU=0.8270] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.43it/s, Loss=0.5831, Dice=0.7764, IoU=0.6345] 
Train - Loss: 0.2804, Dice: 0.9144, IoU: 0.8446
Val   - Loss: 0.9533, Dice: 0.6956, IoU: 0.5393
LR: 0.000166

Epoch 38/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.89it/s, Loss=0.1449, Dice=0.9593, IoU=0.9217] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.12it/s, Loss=0.7103, Dice=0.7549, IoU=0.6063] 
Train - Loss: 0.1902, Dice: 0.9236, IoU: 0.8613
Val   - Loss: 1.1070, Dice: 0.6774, IoU: 0.5185
LR: 0.000144

Epoch 39/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.96it/s, Loss=0.2570, Dice=0.8289, IoU=0.7078] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.40it/s, Loss=0.6046, Dice=0.7747, IoU=0.6323] 
Train - Loss: 0.1910, Dice: 0.9220, IoU: 0.8591
Val   - Loss: 0.9961, Dice: 0.6914, IoU: 0.5347
LR: 0.000124

Epoch 40/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.90it/s, Loss=0.5074, Dice=0.8224, IoU=0.6983] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 12.35it/s, Loss=0.5893, Dice=0.7761, IoU=0.6342] 
Train - Loss: 0.2483, Dice: 0.9092, IoU: 0.8370
Val   - Loss: 0.9729, Dice: 0.6950, IoU: 0.5383
LR: 0.000105

Epoch 41/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.88it/s, Loss=0.1936, Dice=0.9539, IoU=0.9119] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.71it/s, Loss=0.4587, Dice=0.7977, IoU=0.6635] 
Train - Loss: 0.2134, Dice: 0.9267, IoU: 0.8649
Val   - Loss: 0.9329, Dice: 0.7119, IoU: 0.5586
LR: 0.000087

Epoch 42/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.87it/s, Loss=0.1199, Dice=0.9665, IoU=0.9351] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.80it/s, Loss=0.6802, Dice=0.7531, IoU=0.6040] 
Train - Loss: 0.1617, Dice: 0.9432, IoU: 0.8943
Val   - Loss: 1.0990, Dice: 0.6748, IoU: 0.5150
LR: 0.000071

Epoch 43/50
Training: 100%|████████████| 11/11 [00:01<00:00,  5.92it/s, Loss=0.3550, Dice=0.8375, IoU=0.7205] 
Validation: 100%|████████████| 3/3 [00:00<00:00, 11.97it/s, Loss=0.7175, Dice=0.7510, IoU=0.6012] 
Train - Loss: 0.2046, Dice: 0.9260, IoU: 0.8654
Val   - Loss: 1.1163, Dice: 0.6745, IoU: 0.5148
LR: 0.000057
Early stopping triggered after 15 epochs without improvement

🎉 Training completed in 0.03 hours
🏆 Best validation Dice: 0.7247
📁 Results saved to: chess_board_detection\efficient_unet_v3_results

======================================================================
🎉 EFFICIENT U-NET V3 TRAINING COMPLETED!
======================================================================
🏆 Best Dice Score: 0.7247

📊 PERFORMANCE COMPARISON:
V1 Best Dice: 0.9100 (17.3M params)
V2 Best Dice: 0.8256 (41.0M params)
V3 Best Dice: 0.7247 (314K params)

⚡ EFFICIENCY METRICS (Dice per Million Parameters):
V1: 0.05
V2: 0.02
V3: 2.31
🚀 V3 is 43.8x more efficient than V1!
⚠️ NEEDS IMPROVEMENT: Consider V3-48 or longer training