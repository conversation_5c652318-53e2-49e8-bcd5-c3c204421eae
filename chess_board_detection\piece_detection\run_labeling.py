"""
Run the Chess Piece Labeling Tool

This script provides a simple interface to:
1. Run the labeling tool
2. Analyze the labeled dataset
3. Prepare the dataset for YOLO training
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def run_labeling_tool(image_dir, output_dir, corner_annotations=None):
    """Run the chess piece labeling tool."""
    print(f"Starting labeling tool with images from {image_dir}")
    print(f"Annotations will be saved to {output_dir}")

    cmd = [
        sys.executable,
        "chess_board_detection/piece_detection/label_tool.py",
        "--image_dir", image_dir,
        "--output_dir", output_dir
    ]

    if corner_annotations:
        print(f"Using corner annotations from {corner_annotations}")
        cmd.extend(["--corner_annotations", corner_annotations])

    subprocess.run(cmd)

def analyze_dataset(dataset_dir):
    """Analyze the labeled dataset."""
    print(f"Analyzing dataset in {dataset_dir}")

    cmd = [
        sys.executable,
        "chess_board_detection/piece_detection/prepare_labeled_dataset.py",
        "--input_dir", dataset_dir,
        "--analyze"
    ]

    subprocess.run(cmd)

def prepare_dataset(input_dir, output_dir, split_ratio):
    """Prepare the dataset for YOLO training."""
    print(f"Preparing dataset from {input_dir}")
    print(f"Output will be saved to {output_dir}")

    cmd = [
        sys.executable,
        "chess_board_detection/piece_detection/prepare_labeled_dataset.py",
        "--input_dir", input_dir,
        "--output_dir", output_dir,
        "--split_ratio", str(split_ratio)
    ]

    subprocess.run(cmd)

def train_yolo(data_yaml, model_size, epochs, batch_size):
    """Train a YOLO model on the prepared dataset."""
    print(f"Training YOLO model with data from {data_yaml}")

    cmd = [
        sys.executable,
        "chess_board_detection/piece_detection/train_yolo.py",
        "--data", data_yaml,
        "--model_size", model_size,
        "--epochs", str(epochs),
        "--batch_size", str(batch_size)
    ]

    subprocess.run(cmd)

def main():
    parser = argparse.ArgumentParser(description='Chess Piece Labeling and Training Pipeline')

    # Main operation mode
    parser.add_argument('--mode', type=str, choices=['label', 'analyze', 'prepare', 'train', 'all'],
                        default='label', help='Operation mode')

    # Common parameters
    parser.add_argument('--image_dir', type=str, default='chess_board_detection/data/real',
                        help='Directory containing images to label')
    parser.add_argument('--dataset_dir', type=str, default='chess_board_detection/piece_detection/dataset',
                        help='Directory to save/load labeled dataset')
    parser.add_argument('--yolo_dataset_dir', type=str, default='chess_board_detection/piece_detection/yolo_dataset',
                        help='Directory to save prepared YOLO dataset')
    parser.add_argument('--corner_annotations', type=str, default='chess_board_detection/data/real_annotations.json',
                        help='Path to corner annotations JSON file')

    # Training parameters
    parser.add_argument('--split_ratio', type=float, default=0.8,
                        help='Train/val split ratio')
    parser.add_argument('--model_size', type=str, default='n',
                        choices=['n', 's', 'm', 'l', 'x'],
                        help='YOLO model size')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=16,
                        help='Batch size for training')

    args = parser.parse_args()

    # Create directories if they don't exist
    os.makedirs(args.dataset_dir, exist_ok=True)
    os.makedirs(args.yolo_dataset_dir, exist_ok=True)

    # Run the selected mode
    if args.mode == 'label' or args.mode == 'all':
        run_labeling_tool(args.image_dir, args.dataset_dir, args.corner_annotations)

    if args.mode == 'analyze' or args.mode == 'all':
        analyze_dataset(args.dataset_dir)

    if args.mode == 'prepare' or args.mode == 'all':
        prepare_dataset(args.dataset_dir, args.yolo_dataset_dir, args.split_ratio)

    if args.mode == 'train' or args.mode == 'all':
        data_yaml = os.path.join(args.yolo_dataset_dir, 'data.yaml')
        if os.path.exists(data_yaml):
            train_yolo(data_yaml, args.model_size, args.epochs, args.batch_size)
        else:
            print(f"Error: data.yaml not found at {data_yaml}")
            print("Please run the 'prepare' mode first.")

if __name__ == "__main__":
    main()
