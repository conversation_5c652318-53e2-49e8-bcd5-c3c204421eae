import os
import json
import argparse
from datetime import datetime

# Class names
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

def load_feedback_database(db_path):
    """Load the feedback database or create a new one if it doesn't exist"""
    if os.path.exists(db_path):
        with open(db_path, 'r') as f:
            return json.load(f)
    else:
        return {
            "feedback_items": [],
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_items": 0
            }
        }

def save_feedback_database(db, db_path):
    """Save the feedback database"""
    db["metadata"]["last_updated"] = datetime.now().isoformat()
    db["metadata"]["total_items"] = len(db["feedback_items"])
    
    with open(db_path, 'w') as f:
        json.dump(db, f, indent=2)

def parse_detection_file(detection_file):
    """Parse the detection file to get detection information"""
    detections = []
    with open(detection_file, 'r') as f:
        lines = f.readlines()
        
    # Skip the first two lines (header)
    for line in lines[2:]:
        if not line.strip():
            continue
        
        # Parse the line
        # Format: "1. white_pawn (confidence: 0.95) at position (100, 200, 150, 250)"
        parts = line.strip().split('. ', 1)
        if len(parts) != 2:
            continue
        
        idx = int(parts[0])
        rest = parts[1]
        
        # Extract class name
        class_name_end = rest.find(' (confidence:')
        if class_name_end == -1:
            continue
        
        class_name = rest[:class_name_end]
        
        # Extract confidence
        conf_start = rest.find('confidence: ') + len('confidence: ')
        conf_end = rest.find(')', conf_start)
        if conf_start == -1 or conf_end == -1:
            continue
        
        confidence = float(rest[conf_start:conf_end])
        
        # Extract box coordinates
        box_start = rest.find('(', conf_end) + 1
        box_end = rest.find(')', box_start)
        if box_start == -1 or box_end == -1:
            continue
        
        box_str = rest[box_start:box_end]
        box = [int(x.strip()) for x in box_str.split(',')]
        
        detections.append({
            "idx": idx,
            "class_name": class_name,
            "confidence": confidence,
            "box": box
        })
    
    return detections

def collect_feedback(detection_file, image_path, db_path):
    """Collect feedback based on detection file"""
    # Load the feedback database
    db = load_feedback_database(db_path)
    
    # Parse the detection file
    detections = parse_detection_file(detection_file)
    
    if not detections:
        print(f"No detections found in {detection_file}")
        return
    
    print(f"\nDetections for {os.path.basename(image_path)}:")
    for det in detections:
        print(f"{det['idx']}. {det['class_name']} (confidence: {det['confidence']:.2f})")
    
    print("\nEnter feedback for incorrect detections (or press Enter to skip):")
    print("Format: <detection_number> <correct_class_name> (e.g., '3 black_bishop')")
    print("Type 'done' when finished")
    
    feedback_items = []
    while True:
        feedback = input("> ").strip()
        if feedback.lower() == 'done' or feedback == '':
            break
        
        try:
            parts = feedback.split(' ', 1)
            if len(parts) != 2:
                print("Invalid format. Please use: <detection_number> <correct_class_name>")
                continue
            
            idx = int(parts[0])
            correct_class = parts[1].strip()
            
            # Find the detection with this index
            detection = None
            for det in detections:
                if det['idx'] == idx:
                    detection = det
                    break
            
            if detection is None:
                print(f"Invalid detection number. Please use one of: {', '.join(str(d['idx']) for d in detections)}")
                continue
            
            if correct_class not in CLASS_NAMES:
                print(f"Invalid class name. Please use one of: {', '.join(CLASS_NAMES)}")
                continue
            
            # Create a feedback item
            feedback_item = {
                "image_path": image_path,
                "box": detection['box'],
                "predicted_class": detection['class_name'],
                "correct_class": correct_class,
                "confidence": detection['confidence'],
                "timestamp": datetime.now().isoformat()
            }
            
            feedback_items.append(feedback_item)
            print(f"Feedback recorded: Detection {idx} should be {correct_class} (not {detection['class_name']})")
            
        except Exception as e:
            print(f"Error processing feedback: {e}")
    
    # Add feedback items to the database
    if feedback_items:
        db["feedback_items"].extend(feedback_items)
        save_feedback_database(db, db_path)
        print(f"\n{len(feedback_items)} feedback items saved to {db_path}")
    else:
        print("\nNo feedback collected.")
    
    return feedback_items

def main():
    parser = argparse.ArgumentParser(description="Collect feedback on chess piece detection")
    parser.add_argument("--detection-file", required=True, help="Path to the detection text file")
    parser.add_argument("--image", required=True, help="Path to the original image")
    parser.add_argument("--db", default="feedback_database.json", help="Path to the feedback database")
    
    args = parser.parse_args()
    
    collect_feedback(args.detection_file, args.image, args.db)

if __name__ == "__main__":
    main()
