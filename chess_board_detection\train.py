"""
Training script for chess board detection model.
"""

import os
import time
import json
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import matplotlib.pyplot as plt

from models.unet import ChessBoardUNet
from utils.dataset import get_data_loaders
from config import (
    SYNTHETIC_DATA_DIR, MODELS_DIR, DEVICE, 
    LEARNING_RATE, NUM_EPOCHS
)


class DiceLoss(nn.Module):
    """
    Dice loss for segmentation tasks.
    """
    def __init__(self, smooth=1.0):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
        
    def forward(self, pred, target):
        pred = torch.sigmoid(pred)
        
        # Flatten
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)
        
        # Calculate Dice coefficient
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()
        
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        return 1.0 - dice


class KeypointLoss(nn.Module):
    """
    L1 loss for keypoint regression.
    """
    def __init__(self):
        super(KeypointLoss, self).__init__()
        self.loss = nn.L1Loss()
        
    def forward(self, pred, target):
        # Reshape predictions to match target shape
        batch_size = pred.size(0)
        pred = pred.view(batch_size, -1)
        
        return self.loss(pred, target)


def train_model(model, dataloaders, criterion_seg, criterion_kp, optimizer, num_epochs=25):
    """
    Train the model.
    
    Args:
        model: PyTorch model
        dataloaders: Dictionary with 'train' and 'val' dataloaders
        criterion_seg: Loss function for segmentation
        criterion_kp: Loss function for keypoint regression
        optimizer: PyTorch optimizer
        num_epochs: Number of epochs to train for
    
    Returns:
        model: Trained model
        history: Training history
    """
    since = time.time()
    
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_seg_loss': [],
        'val_seg_loss': [],
        'train_kp_loss': [],
        'val_kp_loss': []
    }
    
    best_model_wts = model.state_dict()
    best_loss = float('inf')
    
    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)
        
        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode
            
            running_loss = 0.0
            running_seg_loss = 0.0
            running_kp_loss = 0.0
            
            # Iterate over data
            for batch in tqdm(dataloaders[phase], desc=phase):
                inputs = batch['image'].to(DEVICE)
                masks = batch['mask'].to(DEVICE)
                keypoints = batch['keypoints'].to(DEVICE)
                
                # Zero the parameter gradients
                optimizer.zero_grad()
                
                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    kp_outputs = outputs['keypoints']
                    
                    # Reshape keypoint outputs to match target
                    batch_size = kp_outputs.size(0)
                    kp_outputs = kp_outputs.view(batch_size, -1)
                    
                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    kp_loss = criterion_kp(kp_outputs, keypoints)
                    
                    # Combined loss (you can adjust weights if needed)
                    loss = seg_loss + 0.1 * kp_loss
                    
                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()
                
                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_kp_loss += kp_loss.item() * inputs.size(0)
            
            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_kp_loss = running_kp_loss / len(dataloaders[phase].dataset)
            
            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, KP Loss: {epoch_kp_loss:.4f}')
            
            # Record history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_kp_loss'].append(epoch_kp_loss)
            
            # Deep copy the model if it's the best so far
            if phase == 'val' and epoch_loss < best_loss:
                best_loss = epoch_loss
                best_model_wts = model.state_dict().copy()
                
                # Save the best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': best_model_wts,
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': best_loss,
                }, os.path.join(MODELS_DIR, 'best_model.pth'))
        
        print()
    
    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')
    
    # Load best model weights
    model.load_state_dict(best_model_wts)
    
    # Save training history
    with open(os.path.join(MODELS_DIR, 'training_history.json'), 'w') as f:
        json.dump(history, f)
    
    # Plot training history
    plot_training_history(history)
    
    return model, history


def plot_training_history(history):
    """
    Plot training history.
    
    Args:
        history: Dictionary with training history
    """
    plt.figure(figsize=(12, 4))
    
    # Plot total loss
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='Train')
    plt.plot(history['val_loss'], label='Validation')
    plt.title('Total Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    # Plot segmentation loss
    plt.subplot(1, 3, 2)
    plt.plot(history['train_seg_loss'], label='Train')
    plt.plot(history['val_seg_loss'], label='Validation')
    plt.title('Segmentation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    # Plot keypoint loss
    plt.subplot(1, 3, 3)
    plt.plot(history['train_kp_loss'], label='Train')
    plt.plot(history['val_kp_loss'], label='Validation')
    plt.title('Keypoint Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(os.path.join(MODELS_DIR, 'training_history.png'))
    plt.close()


def main():
    """
    Main function.
    """
    # Create directories if they don't exist
    os.makedirs(MODELS_DIR, exist_ok=True)
    
    # Get data loaders
    dataloaders = get_data_loaders(SYNTHETIC_DATA_DIR)
    
    # Initialize model
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    model = model.to(DEVICE)
    
    # Define loss functions and optimizer
    criterion_seg = DiceLoss()
    criterion_kp = KeypointLoss()
    optimizer = optim.Adam(model.parameters(), lr=LEARNING_RATE)
    
    # Train model
    model, history = train_model(
        model=model,
        dataloaders={'train': dataloaders['train'], 'val': dataloaders['val']},
        criterion_seg=criterion_seg,
        criterion_kp=criterion_kp,
        optimizer=optimizer,
        num_epochs=NUM_EPOCHS
    )
    
    print("Training completed!")


if __name__ == "__main__":
    main()
