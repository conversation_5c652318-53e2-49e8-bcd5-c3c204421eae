"""
Identify misclassifications in chess piece detection.
This script helps identify pieces that are misclassified by color or type.
"""

import os
import sys
import argparse
import logging
import time
import json
from pathlib import Path
from ultralytics import YOLO
import cv2
import numpy as np

# Configure logging
os.makedirs("chess_board_detection/logs", exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("chess_board_detection/logs/misclassifications.log")
    ]
)
logger = logging.getLogger("misclassifications")

def identify_misclassifications(
    model_path,
    source,
    output_dir="chess_board_detection/outputs/misclassifications",
    conf_threshold=0.7,
    iou_threshold=0.9,
):
    """
    Identify potential misclassifications in chess piece detection.
    
    Args:
        model_path: Path to the trained YOLO model
        source: Path to input image or directory
        output_dir: Directory to save results
        conf_threshold: Confidence threshold for detections
        iou_threshold: IoU threshold for NMS
    """
    # Create output directory if needed
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"Output directory: {output_dir}")
    
    logger.info(f"Starting detection with model: {model_path}")
    logger.info(f"Source: {source}")
    logger.info(f"Confidence threshold: {conf_threshold}")
    
    # Load the model
    start_time = time.time()
    model = YOLO(model_path)
    logger.info(f"Model loaded in {time.time() - start_time:.2f} seconds")
    
    # Run inference
    logger.info(f"Running inference with IoU threshold: {iou_threshold}")
    inference_start = time.time()
    results = model.predict(
        source=source,
        conf=conf_threshold,
        save=False,
        verbose=True,
        iou=iou_threshold
    )
    inference_time = time.time() - inference_start
    logger.info(f"Inference completed in {inference_time:.2f} seconds")
    
    # Process results
    misclassifications = []
    
    for i, result in enumerate(results):
        # Get the original image
        img = result.orig_img
        source_path = Path(source) if isinstance(source, str) else Path(source[i])
        
        # Get detections
        boxes = result.boxes.xyxy.cpu().numpy()
        classes = result.boxes.cls.cpu().numpy().astype(int)
        confs = result.boxes.conf.cpu().numpy()
        
        # Create a copy of the image for visualization
        vis_img = img.copy()
        
        # Process each detection
        image_misclassifications = []
        
        for j, (box, cls, conf) in enumerate(zip(boxes, classes, confs)):
            x1, y1, x2, y2 = box.astype(int)
            
            # Get class name
            original_name = result.names[cls]
            
            # Extract the piece region
            piece_roi = img[y1:y2, x1:x2]
            
            if piece_roi.size > 0:
                # Convert to grayscale and calculate average brightness
                gray_roi = cv2.cvtColor(piece_roi, cv2.COLOR_BGR2GRAY)
                avg_brightness = np.mean(gray_roi)
                
                # Determine if piece is likely white or black based on brightness
                # This is just a heuristic for analysis, not for actual classification
                likely_white = avg_brightness > 120
                
                # Check if the model's color prediction matches the brightness heuristic
                model_color = "white" if original_name.startswith("white_") else "black"
                heuristic_color = "white" if likely_white else "black"
                
                color_mismatch = model_color != heuristic_color
                
                if color_mismatch:
                    # Record the misclassification
                    misclassification = {
                        "image": str(source_path),
                        "box": [int(x1), int(y1), int(x2), int(y2)],
                        "model_class": original_name,
                        "confidence": float(conf),
                        "avg_brightness": float(avg_brightness),
                        "likely_color": heuristic_color,
                        "issue": f"Color mismatch: model says {model_color}, brightness suggests {heuristic_color}"
                    }
                    image_misclassifications.append(misclassification)
                    
                    # Draw on the visualization image
                    color = (0, 0, 255)  # Red for misclassifications
                    cv2.rectangle(vis_img, (x1, y1), (x2, y2), color, 2)
                    
                    # Add label
                    label = f"{original_name} ({conf:.2f})"
                    cv2.putText(
                        vis_img,
                        label,
                        (x1, y1 - 5),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.5,
                        color,
                        2
                    )
                    
                    # Add brightness info
                    brightness_label = f"Brightness: {avg_brightness:.1f}"
                    cv2.putText(
                        vis_img,
                        brightness_label,
                        (x1, y2 + 15),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        0.4,
                        color,
                        1
                    )
        
        # Save the visualization if there are misclassifications
        if image_misclassifications:
            output_path = Path(output_dir) / f"misclass_{source_path.name}"
            cv2.imwrite(str(output_path), vis_img)
            logger.info(f"Saved misclassification visualization to {output_path}")
            
            # Add to overall list
            misclassifications.extend(image_misclassifications)
    
    # Save misclassifications to JSON
    if misclassifications:
        json_path = Path(output_dir) / "misclassifications.json"
        with open(json_path, 'w') as f:
            json.dump(misclassifications, f, indent=2)
        logger.info(f"Saved {len(misclassifications)} misclassifications to {json_path}")
    else:
        logger.info("No misclassifications found")
    
    return misclassifications

def main():
    parser = argparse.ArgumentParser(description="Identify misclassifications in chess piece detection")
    parser.add_argument("--model", type=str, required=True, help="Path to trained YOLO model")
    parser.add_argument("--input", type=str, required=True, help="Path to input image or directory")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/outputs/misclassifications", help="Directory to save results")
    parser.add_argument("--conf", type=float, default=0.7, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.9, help="IoU threshold for NMS")
    
    args = parser.parse_args()
    
    identify_misclassifications(
        args.model,
        args.input,
        args.output_dir,
        args.conf,
        args.iou
    )

if __name__ == "__main__":
    main()
