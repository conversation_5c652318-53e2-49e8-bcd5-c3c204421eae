"""
Dataset class for chess board segmentation training.
"""

import os
import json
import cv2
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from pathlib import Path
import albumentations as A
from albumentations.pytorch import ToTensorV2

class ChessboardSegmentationDataset(Dataset):
    """
    Dataset for chess board segmentation.
    """

    def __init__(self, image_dir, annotation_file, transform=None, image_size=(256, 256)):
        """
        Args:
            image_dir: Directory containing images
            annotation_file: JSON file with corner annotations
            transform: Albumentations transform pipeline
            image_size: Target image size (height, width)
        """
        self.image_dir = Path(image_dir)
        self.image_size = image_size
        self.transform = transform

        # Load annotations
        with open(annotation_file, 'r') as f:
            self.annotations = json.load(f)

        # Filter annotations to only include existing images
        self.valid_annotations = []
        for ann in self.annotations:
            image_path = self.image_dir / ann['image']
            if image_path.exists():
                self.valid_annotations.append(ann)

        print(f"Loaded {len(self.valid_annotations)} valid annotations")

    def __len__(self):
        return len(self.valid_annotations)

    def __getitem__(self, idx):
        annotation = self.valid_annotations[idx]

        # Load image
        image_path = self.image_dir / annotation['image']
        image = cv2.imread(str(image_path))
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Create mask from corners
        mask = self.create_mask_from_corners(
            annotation['corners'],
            annotation['image_size']
        )

        # Apply transforms
        if self.transform:
            transformed = self.transform(image=image, mask=mask)
            image = transformed['image']
            mask = transformed['mask']
        else:
            # Default resize
            image = cv2.resize(image, self.image_size)
            mask = cv2.resize(mask, self.image_size, interpolation=cv2.INTER_NEAREST)

            # Convert to tensor
            image = torch.from_numpy(image.transpose(2, 0, 1)).float() / 255.0
            mask = torch.from_numpy(mask).float()  # Don't add channel dimension here

        return image, mask

    def create_mask_from_corners(self, corners, image_size):
        """
        Create a binary mask from corner coordinates.

        Args:
            corners: List of 8 coordinates [x1, y1, x2, y2, x3, y3, x4, y4]
            image_size: [width, height] of the original image

        Returns:
            Binary mask where chessboard area is 1, background is 0
        """
        width, height = image_size

        # Reshape corners to [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        corner_points = []
        for i in range(0, len(corners), 2):
            x, y = corners[i], corners[i+1]
            corner_points.append([int(x), int(y)])

        # Create mask
        mask = np.zeros((height, width), dtype=np.uint8)

        # Fill the polygon defined by corners
        corner_array = np.array(corner_points, dtype=np.int32)
        cv2.fillPoly(mask, [corner_array], 1)

        return mask

def get_transforms(image_size=(256, 256), is_training=True):
    """
    Get augmentation transforms for training and validation.

    Args:
        image_size: Target image size (height, width)
        is_training: Whether to apply training augmentations
    """
    if is_training:
        transform = A.Compose([
            A.Resize(height=image_size[0], width=image_size[1]),
            A.HorizontalFlip(p=0.5),
            A.RandomBrightnessContrast(
                brightness_limit=0.2,
                contrast_limit=0.2,
                p=0.5
            ),
            A.HueSaturationValue(
                hue_shift_limit=10,
                sat_shift_limit=20,
                val_shift_limit=20,
                p=0.3
            ),
            A.GaussNoise(noise_scale_factor=0.1, p=0.3),
            A.Blur(blur_limit=3, p=0.2),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])
    else:
        transform = A.Compose([
            A.Resize(height=image_size[0], width=image_size[1]),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])

    return transform

def create_dataloaders(
    image_dir,
    annotation_file,
    batch_size=8,
    image_size=(256, 256),
    train_split=0.8,
    num_workers=4
):
    """
    Create training and validation dataloaders.

    Args:
        image_dir: Directory containing images
        annotation_file: JSON file with annotations
        batch_size: Batch size for training
        image_size: Target image size
        train_split: Fraction of data for training
        num_workers: Number of worker processes
    """
    # Load all annotations
    with open(annotation_file, 'r') as f:
        all_annotations = json.load(f)

    # Split into train/val
    split_idx = int(len(all_annotations) * train_split)
    train_annotations = all_annotations[:split_idx]
    val_annotations = all_annotations[split_idx:]

    # Save split annotations
    train_file = 'train_annotations.json'
    val_file = 'val_annotations.json'

    with open(train_file, 'w') as f:
        json.dump(train_annotations, f)

    with open(val_file, 'w') as f:
        json.dump(val_annotations, f)

    # Create datasets
    train_transform = get_transforms(image_size, is_training=True)
    val_transform = get_transforms(image_size, is_training=False)

    train_dataset = ChessboardSegmentationDataset(
        image_dir, train_file, train_transform, image_size
    )

    val_dataset = ChessboardSegmentationDataset(
        image_dir, val_file, val_transform, image_size
    )

    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True
    )

    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")

    return train_loader, val_loader

def visualize_sample(dataset, idx=0, save_path=None):
    """Visualize a sample from the dataset."""
    import matplotlib.pyplot as plt

    image, mask = dataset[idx]

    # Convert tensor to numpy for visualization
    if isinstance(image, torch.Tensor):
        # Denormalize
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        image = image * std + mean
        image = torch.clamp(image, 0, 1)
        image = image.permute(1, 2, 0).numpy()

    if isinstance(mask, torch.Tensor):
        mask = mask.squeeze().numpy()

    # Create visualization
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))

    axes[0].imshow(image)
    axes[0].set_title('Original Image')
    axes[0].axis('off')

    axes[1].imshow(mask, cmap='gray')
    axes[1].set_title('Segmentation Mask')
    axes[1].axis('off')

    # Overlay
    overlay = image.copy()
    overlay[:, :, 0] = np.where(mask > 0.5, 1.0, overlay[:, :, 0])
    axes[2].imshow(overlay)
    axes[2].set_title('Overlay')
    axes[2].axis('off')

    plt.tight_layout()

    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to {save_path}")
    else:
        plt.show()

    plt.close()

if __name__ == "__main__":
    # Test the dataset
    IMAGE_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real"
    ANNOTATION_FILE = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json"

    # Create dataset
    transform = get_transforms(image_size=(256, 256), is_training=True)
    dataset = ChessboardSegmentationDataset(IMAGE_DIR, ANNOTATION_FILE, transform)

    print(f"Dataset size: {len(dataset)}")

    # Visualize a sample
    os.makedirs("chess_board_detection/dataset_visualizations", exist_ok=True)
    visualize_sample(dataset, idx=0, save_path="chess_board_detection/dataset_visualizations/dataset_sample.png")

    # Test dataloader
    train_loader, val_loader = create_dataloaders(
        IMAGE_DIR, ANNOTATION_FILE, batch_size=4, image_size=(256, 256)
    )

    # Test batch
    for batch_idx, (images, masks) in enumerate(train_loader):
        print(f"Batch {batch_idx}: Images shape: {images.shape}, Masks shape: {masks.shape}")
        if batch_idx == 0:
            break
