"""
Prepare an existing dataset of chess piece images for YOLO training.
This script:
1. Creates the proper directory structure for YOLO training
2. Generates annotations in YOLO format
3. Creates a data.yaml file
"""

import os
import sys
import argparse
import cv2
import numpy as np
import shutil
from tqdm import tqdm
import random

def parse_filename(filename):
    """
    Parse the filename to extract piece information.
    
    Args:
        filename: Filename of the chess piece image
        
    Returns:
        piece_info: Dictionary containing piece information
    """
    # Remove extension
    basename = os.path.splitext(filename)[0]
    
    if basename.startswith('e'):
        # Empty square
        return {
            'is_empty': True,
            'square_color': 'black' if basename[1] == 'b' else 'white'
        }
    
    # Parse piece information
    piece_color = 'white' if basename[0] == 'w' else 'black'
    
    piece_type_map = {
        'p': 'pawn',
        'n': 'knight',
        'b': 'bishop',
        'r': 'rook',
        'q': 'queen',
        'k': 'king'
    }
    
    piece_type = piece_type_map.get(basename[1], 'unknown')
    square_color = 'black' if basename[2] == 'b' else 'white'
    
    return {
        'is_empty': False,
        'piece_color': piece_color,
        'piece_type': piece_type,
        'square_color': square_color,
        'class_name': f"{piece_color}_{piece_type}"
    }

def create_synthetic_board_image(piece_images, output_path, num_pieces=10, board_size=(640, 640)):
    """
    Create a synthetic chess board image by placing pieces randomly on the board.
    
    Args:
        piece_images: Dictionary mapping class names to lists of image paths
        output_path: Path to save the synthetic image
        num_pieces: Number of pieces to place on the board
        board_size: Size of the chess board image
    
    Returns:
        board_image: Synthetic chess board image
        annotations: List of piece annotations in YOLO format
    """
    # Create empty board image
    board_image = np.zeros((board_size[0], board_size[1], 3), dtype=np.uint8)
    
    # Calculate square size
    square_size = board_size[0] // 8
    
    # Fill board with alternating colors
    for i in range(8):
        for j in range(8):
            square_color = (240, 240, 240) if (i + j) % 2 == 0 else (120, 120, 120)
            board_image[i*square_size:(i+1)*square_size, j*square_size:(j+1)*square_size] = square_color
    
    # Create a set to track occupied squares
    occupied_squares = set()
    
    # List to store annotations
    annotations = []
    
    # Place pieces randomly
    all_classes = list(piece_images.keys())
    
    # Filter out empty squares
    piece_classes = [cls for cls in all_classes if cls != 'empty']
    
    # Randomly select number of pieces
    num_pieces = min(num_pieces, 32)  # Maximum 32 pieces on a chess board
    
    for _ in range(num_pieces):
        # Randomly select piece class
        class_name = random.choice(piece_classes)
        
        # Skip if no images for this class
        if not piece_images[class_name]:
            continue
        
        # Randomly select piece image
        piece_path = random.choice(piece_images[class_name])
        
        # Randomly select square
        while True:
            rank = random.randint(0, 7)
            file = random.randint(0, 7)
            
            if (rank, file) not in occupied_squares:
                occupied_squares.add((rank, file))
                break
        
        # Load piece image
        piece_img = cv2.imread(piece_path)
        
        # Resize piece image to fit square
        piece_img = cv2.resize(piece_img, (square_size, square_size))
        
        # Place piece on board
        board_image[rank*square_size:(rank+1)*square_size, file*square_size:(file+1)*square_size] = piece_img
        
        # Calculate YOLO format annotation
        # YOLO format: class_id center_x center_y width height
        # All values are normalized to [0, 1]
        class_id = all_classes.index(class_name)
        center_x = (file * square_size + square_size / 2) / board_size[0]
        center_y = (rank * square_size + square_size / 2) / board_size[1]
        width = square_size / board_size[0]
        height = square_size / board_size[1]
        
        annotations.append(f"{class_id} {center_x} {center_y} {width} {height}")
    
    # Save board image
    cv2.imwrite(output_path, board_image)
    
    return board_image, annotations

def create_yolo_dataset(input_dir, output_dir, num_images=1000, split_ratio=0.8):
    """
    Create a YOLO dataset from individual chess piece images.
    
    Args:
        input_dir: Directory containing chess piece images
        output_dir: Directory to save the YOLO dataset
        num_images: Number of synthetic images to generate
        split_ratio: Train/val split ratio
    """
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    
    train_dir = os.path.join(output_dir, 'train')
    val_dir = os.path.join(output_dir, 'val')
    
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    
    train_images_dir = os.path.join(train_dir, 'images')
    train_labels_dir = os.path.join(train_dir, 'labels')
    val_images_dir = os.path.join(val_dir, 'images')
    val_labels_dir = os.path.join(val_dir, 'labels')
    
    os.makedirs(train_images_dir, exist_ok=True)
    os.makedirs(train_labels_dir, exist_ok=True)
    os.makedirs(val_images_dir, exist_ok=True)
    os.makedirs(val_labels_dir, exist_ok=True)
    
    # Find all images in the input directory
    image_paths = []
    for file in os.listdir(input_dir):
        if file.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_paths.append(os.path.join(input_dir, file))
    
    if not image_paths:
        print(f"No images found in {input_dir}")
        return
    
    print(f"Found {len(image_paths)} piece images")
    
    # Group images by class
    piece_images = {
        'empty': [],
        'white_pawn': [],
        'white_knight': [],
        'white_bishop': [],
        'white_rook': [],
        'white_queen': [],
        'white_king': [],
        'black_pawn': [],
        'black_knight': [],
        'black_bishop': [],
        'black_rook': [],
        'black_queen': [],
        'black_king': []
    }
    
    for image_path in image_paths:
        filename = os.path.basename(image_path)
        piece_info = parse_filename(filename)
        
        if piece_info['is_empty']:
            piece_images['empty'].append(image_path)
        else:
            class_name = piece_info['class_name']
            piece_images[class_name].append(image_path)
    
    # Print class distribution
    print("\nClass distribution:")
    for class_name, paths in piece_images.items():
        print(f"{class_name}: {len(paths)} images")
    
    # Define class names (excluding empty)
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]
    
    # Calculate split
    num_train = int(num_images * split_ratio)
    num_val = num_images - num_train
    
    print(f"\nGenerating {num_train} training images and {num_val} validation images")
    
    # Generate training images
    print("\nGenerating training images:")
    for i in tqdm(range(num_train)):
        # Create synthetic board image
        image_path = os.path.join(train_images_dir, f"board_{i:06d}.jpg")
        _, annotations = create_synthetic_board_image(piece_images, image_path)
        
        # Save annotations
        label_path = os.path.join(train_labels_dir, f"board_{i:06d}.txt")
        with open(label_path, 'w') as f:
            f.write('\n'.join(annotations))
    
    # Generate validation images
    print("\nGenerating validation images:")
    for i in tqdm(range(num_val)):
        # Create synthetic board image
        image_path = os.path.join(val_images_dir, f"board_{i:06d}.jpg")
        _, annotations = create_synthetic_board_image(piece_images, image_path)
        
        # Save annotations
        label_path = os.path.join(val_labels_dir, f"board_{i:06d}.txt")
        with open(label_path, 'w') as f:
            f.write('\n'.join(annotations))
    
    # Create data.yaml file
    data_yaml = os.path.join(output_dir, 'data.yaml')
    with open(data_yaml, 'w') as f:
        f.write(f"train: {os.path.abspath(train_images_dir)}\n")
        f.write(f"val: {os.path.abspath(val_images_dir)}\n")
        f.write(f"nc: {len(class_names)}\n")
        f.write(f"names: {class_names}\n")
    
    print(f"\nDataset prepared at {output_dir}")
    print(f"Next steps:")
    print(f"1. Train YOLO using the data.yaml file:")
    print(f"   python chess_board_detection/piece_detection/train_yolo.py --data {data_yaml} --model_size n --epochs 100")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Prepare chess piece dataset for YOLO training')
    parser.add_argument('--input_dir', type=str, required=True,
                        help='Directory containing chess piece images')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/piece_detection/dataset',
                        help='Directory to save the YOLO dataset')
    parser.add_argument('--num_images', type=int, default=1000,
                        help='Number of synthetic images to generate')
    parser.add_argument('--split_ratio', type=float, default=0.8,
                        help='Train/val split ratio')
    args = parser.parse_args()
    
    # Create YOLO dataset
    create_yolo_dataset(
        args.input_dir,
        args.output_dir,
        num_images=args.num_images,
        split_ratio=args.split_ratio
    )

if __name__ == "__main__":
    main()
