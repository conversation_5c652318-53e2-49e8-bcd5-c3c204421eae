import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms as transforms
import cv2
import argparse
import sys

# Add the parent directory to the path to import the model
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from chess_board_detection.models.unet import ChessBoardUNet

def load_model(model_path):
    # Initialize model
    model = ChessBoardUNet(n_channels=3, bilinear=True)

    # Load model weights
    checkpoint = torch.load(model_path, map_location=torch.device('cpu'))

    # Handle different checkpoint formats
    if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.eval()

    return model

def preprocess_image(image_path, size=(256, 256)):
    # Load image
    image = Image.open(image_path).convert('RGB')

    # Original dimensions for later use
    original_width, original_height = image.size

    # Resize and convert to tensor
    transform = transforms.Compose([
        transforms.Resize(size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    input_tensor = transform(image).unsqueeze(0)  # Add batch dimension

    return input_tensor, (original_width, original_height), np.array(image)

def find_corners_from_heatmaps(heatmaps, threshold=0.5):
    """Extract corner coordinates from heatmaps"""
    corners = []

    for i in range(4):  # 4 corners
        heatmap = heatmaps[0, i].detach().numpy()

        # Find the maximum value location
        max_idx = np.argmax(heatmap)
        y, x = np.unravel_index(max_idx, heatmap.shape)

        # Only consider it a valid corner if the confidence is above threshold
        if heatmap[y, x] > threshold:
            corners.append((x, y))
        else:
            corners.append(None)

    return corners

def visualize_results(original_image, mask, heatmaps, corners, output_path=None):
    """Visualize the segmentation mask, heatmaps, and detected corners"""
    # Create two separate figures

    # Figure 1: Original, Mask, and Combined Heatmap
    plt.figure(figsize=(15, 5))

    # Original image
    plt.subplot(1, 3, 1)
    plt.imshow(original_image)
    plt.title('Original Image')
    plt.axis('off')

    # Segmentation mask
    plt.subplot(1, 3, 2)
    plt.imshow(mask, cmap='gray')
    plt.title('Segmentation Mask')
    plt.axis('off')

    # Combined heatmap
    plt.subplot(1, 3, 3)
    combined_heatmap = np.max(heatmaps[0].detach().numpy(), axis=0)
    plt.imshow(original_image)
    plt.imshow(combined_heatmap, alpha=0.5, cmap='hot')
    plt.title('Combined Heatmap')
    plt.axis('off')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path.replace('.png', '_part1.png'))

    # Figure 2: Individual corner heatmaps
    plt.figure(figsize=(15, 10))

    # Individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        plt.subplot(2, 2, i+1)
        plt.imshow(original_image)
        plt.imshow(heatmaps[0, i].detach().numpy(), alpha=0.5, cmap='hot')

        # Mark the detected corner if available
        if corners[i]:
            x, y = corners[i]
            plt.scatter(x, y, c='blue', s=50, marker='x')

        plt.title(f'{corner_names[i]} Corner')
        plt.axis('off')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path.replace('.png', '_part2.png'))

    plt.show()

def main(args):
    # Load model
    model = load_model(args.model_path)

    # Preprocess image
    input_tensor, original_dims, original_image = preprocess_image(args.image_path)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Process outputs
    mask_pred = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    mask = torch.sigmoid(mask_pred).squeeze().detach().numpy() > 0.5

    # Find corners
    corners = find_corners_from_heatmaps(heatmaps, threshold=args.threshold)

    # Visualize
    visualize_results(original_image, mask, heatmaps, corners, args.output_path)

    # Print corner coordinates
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    print("Detected corners:")
    for i, corner in enumerate(corners):
        if corner:
            print(f"{corner_names[i]}: {corner}")
        else:
            print(f"{corner_names[i]}: Not detected")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Test chess board detection model')
    parser.add_argument('--model_path', type=str, required=True, help='Path to the model weights')
    parser.add_argument('--image_path', type=str, required=True, help='Path to the test image')
    parser.add_argument('--output_path', type=str, default=None, help='Path to save visualization')
    parser.add_argument('--threshold', type=float, default=0.5, help='Confidence threshold for corner detection')

    args = parser.parse_args()
    main(args)
