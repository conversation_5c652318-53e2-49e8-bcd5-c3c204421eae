# Simple YOLO Training for Chess Piece Detection

This README explains the simplified training approach for the chess piece detection model.

## Overview

This training script provides a simplified approach to training a YOLO model for chess piece detection with:

1. **No augmentation** during processing
2. **Default weights and parameters** for the YOLO11n model
3. **Single training phase** with max 100 epochs (no multi-phase system)
4. **Early stopping mechanism** to prevent overfitting
5. **Detailed logging** of best epoch achievements
6. **Target metric comparison** after 100 epochs with option to continue for 10 more epochs

## How to Run

Simply execute the batch file:

```
chess_board_detection/train_simple_model.bat
```

Or run the Python script directly with custom parameters:

```
python chess_board_detection/train_simple_yolo.py --model yolo11n.pt --epochs 100 --patience 20
```

## Parameters

- `--model`: The YOLO model to use (default: yolo11n.pt)
- `--epochs`: Maximum number of epochs to train (default: 100)
- `--patience`: Number of epochs to wait for improvement before early stopping (default: 20)
- `--dataset`: Path to the dataset YAML file (default: chess_board_detection/piece_detection/enhanced_dataset_99plus/dataset.yaml)
- `--target-map50`: Target mAP50 value to achieve (default: 0.99)
- `--target-precision`: Target precision value to achieve (default: 0.99)
- `--target-recall`: Target recall value to achieve (default: 0.99)

## Features

### 1. Simplified Training

- No complex augmentations during training
- Uses default model weights and parameters
- Focuses on clean, reproducible results

### 2. Early Stopping

The training will automatically stop if no improvement is seen for the specified number of epochs (default: 20).

### 3. Best Epoch Logging

The training process includes a custom callback that logs detailed information whenever a new best model is found:

- Prints a clear notification in the console
- Saves detailed metrics to a log file
- Records the history of best epochs in a JSON file for later analysis

### 4. Target Metric Comparison and Continuation

After completing 100 epochs, the script:

1. Compares the best achieved metrics with target values (mAP50=0.99, Precision=0.99, Recall=0.99)
2. Shows which targets have been met and which have not
3. Asks the user if they want to continue training for 10 more epochs
4. If the user chooses to continue:
   - Loads the best model from the previous training
   - Trains for 10 more epochs
   - Reports the final metrics after continuation

### 5. Metrics Tracked

For each best epoch, the following metrics are recorded:

- mAP50 (Mean Average Precision at 50% IoU)
- mAP50-95 (Mean Average Precision across IoU thresholds)
- Precision
- Recall
- Box Loss
- Classification Loss
- DFL Loss (Distribution Focal Loss)

## Output Files

Training results are saved in:
```
chess_board_detection/piece_detection/models/simple_yolo/simple_[timestamp]/
```

Key files include:
- `best.pt`: The best model weights
- `best_epochs_[timestamp].txt`: Log of best epoch achievements
- `best_epochs_history.json`: JSON record of best epoch metrics
- Standard YOLO training outputs (confusion matrix, results.csv, etc.)

## Notes

- This approach prioritizes simplicity and reproducibility over complex optimizations
- The model is trained with a fixed image size of 416x416 pixels
- AdamW optimizer is used with a learning rate of 0.001
- Deterministic training is enabled for reproducibility (seed=42)
- **Single-phase training**: Unlike some approaches that use multiple phases with different learning rates, this uses a single training phase for all 100 epochs
- No learning rate scheduling or complex training strategies are used
