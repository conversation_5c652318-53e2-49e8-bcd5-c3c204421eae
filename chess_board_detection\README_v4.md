# Chess Board Detection v4

This is an improved version of the chess board detection model with enhanced corner detection capabilities. The v4 model addresses specific issues identified in the v3 model:

1. **Peak-to-Second Ratio**: Improved to better distinguish primary peaks from secondary peaks
2. **Detection Rate**: Enhanced to improve corner detection reliability
3. **Geometric Consistency**: Stabilized to reduce volatility in geometric loss
4. **Peak Values**: Improved to increase peak values for more confident corner detection

## Key Improvements

### 1. Enhanced Loss Functions

- **ImprovedCornerFocusedHeatmapLoss**: Added specific penalties for peak-to-second ratio and detection rate
- **ImprovedGeometricConsistencyLoss**: Added convexity check and angle constraints for more stable geometric consistency

### 2. Post-Processing

- **Non-Maximum Suppression**: Enhances primary peaks and suppresses secondary peaks
- **Detection Rate Enhancement**: Improves detection of weak corners
- **Geometric Constraint Enforcement**: Ensures detected corners form a valid quadrilateral

## Usage

### Training the v4 Model

To train the v4 model starting from the best v3 checkpoint (around epoch 94):

```bash
python train_v4.py --data_dir path/to/data --output_dir path/to/output --epochs 30 --lr 0.0005 --heatmap_weight 1.5 --geometric_weight 0.4 --separation_weight 0.6 --peak_separation_weight 0.5 --edge_suppression_weight 0.7 --peak_enhancement_weight 0.5 --peak_to_second_ratio_weight 1.0 --detection_rate_weight 1.0 --start_from_epoch 94
```

### Running Inference with Post-Processing

To run inference with the v4 model and apply post-processing:

```bash
python inference_enhanced_v4.py --image_path path/to/image.jpg --model_path path/to/model.pth --use_post_processing
```

## Expected Metrics

The v4 model aims to achieve the following target metrics:

| Metric | Target | v3 Best | v4 Expected |
|--------|--------|---------|-------------|
| **Peak-to-Second Ratio** | > 2.0 | ~1.03 | > 1.5 |
| **Detection Rate** | > 0.95 | ~0.75 | > 0.9 |
| **Geometric Loss** | < 0.5 | ~0.32-1.16 | < 0.5 |
| **Peak Value** | > 0.9 | ~0.77 | > 0.85 |

## Model Architecture

The v4 model uses the same EnhancedChessBoardUNet architecture as v3, but with improved loss functions and post-processing:

```
EnhancedChessBoardUNet
├── Encoder (ResNet-like)
├── Decoder (U-Net style)
├── Segmentation Head
└── Corner Heatmap Head
```

## Loss Function Components

### ImprovedCornerFocusedHeatmapLoss

- **MSE Loss**: Basic heatmap prediction loss
- **Separation Loss**: Penalizes overlap between different corner heatmaps
- **Peak Separation Loss**: Ensures peaks are well separated
- **Edge Suppression Loss**: Penalizes activations along edges
- **Peak Enhancement Loss**: Encourages sharper peaks at corner locations
- **Peak-to-Second Ratio Loss**: Enhances the ratio between primary and secondary peaks
- **Detection Rate Loss**: Penalizes low peak values to improve detection rate

### ImprovedGeometricConsistencyLoss

- **Aspect Ratio Consistency**: Ensures width/height ratio is consistent
- **Parallelogram Check**: Ensures opposite sides are parallel
- **Convexity Check**: Ensures the quadrilateral is convex
- **Angle Constraints**: Encourages right angles for rectangular boards

## Post-Processing Functions

### apply_non_maximum_suppression

Enhances primary peaks and suppresses secondary peaks using max pooling.

### enhance_detection_rate

Improves detection of weak corners by enhancing peaks that are below the detection threshold.

### enforce_geometric_constraints

Ensures detected corners form a valid quadrilateral by enforcing convexity and reasonable area constraints.

## Training Process

The v4 model is trained in two stages:

1. **Pre-training**: The model is first trained with the v3 configuration for 94 epochs
2. **Fine-tuning**: The model is then fine-tuned with the v4 configuration for 30 epochs

This approach allows the model to learn basic corner detection first, then refine its ability to distinguish between primary and secondary peaks and improve detection reliability.

## Evaluation

The model is evaluated on the following metrics:

- **Total Loss**: Combined loss from all components
- **Segmentation Loss**: Dice loss for board segmentation
- **Heatmap Loss**: Loss for corner heatmap prediction
- **Geometric Loss**: Loss for geometric consistency
- **Peak Value**: Average value of detected peaks
- **Peak-to-Mean Ratio**: Ratio of peak value to mean value
- **Peak-to-Second Ratio**: Ratio of primary peak to secondary peak
- **Detection Rate**: Percentage of corners detected

## Troubleshooting

If you encounter issues with corner detection:

1. **Low Detection Rate**: Try lowering the detection threshold or increasing the detection_rate_weight
2. **Poor Geometric Consistency**: Try increasing the geometric_weight
3. **Low Peak-to-Second Ratio**: Try increasing the peak_to_second_ratio_weight
4. **Edge Detections**: Try increasing the edge_suppression_weight

## References

- Original U-Net paper: [U-Net: Convolutional Networks for Biomedical Image Segmentation](https://arxiv.org/abs/1505.04597)
- Heatmap-based keypoint detection: [Simple Baselines for Human Pose Estimation and Tracking](https://arxiv.org/abs/1804.06208)
