# Continue Training for Chess Piece Detection

This script continues training from the best model (epoch 86) with a focus on reducing classification loss to zero while maintaining high detection performance. It uses the targeted dataset that focuses on rare chess pieces to improve classification accuracy.

## Features

- **Continues from Epoch 86**: Uses the best model from the previous training run
- **Targeted Dataset**: Uses a dataset focused on rare chess pieces to improve classification
- **Classification Loss Focus**: Specifically designed to drive classification loss to zero
- **Memory Optimization**: Implements techniques to prevent memory-related crashes
- **Dynamic Weight Adjustment**: Automatically adjusts loss weights based on classification loss
- **Performance Preservation**: Monitors precision and recall to ensure they remain high
- **Comprehensive Visualization**: Generates detailed charts tracking classification loss, weights, and performance metrics

## How It Works

1. **Loading the Best Model**: The script loads the best model from epoch 86, which had excellent metrics:
   - Precision: 0.98
   - Recall: 0.936
   - mAP50: 0.973
   - mAP50-95: 0.852
   - Classification loss: 0.305

2. **Dynamic Weight Adjustment**:
   - The script starts with high classification weights (10.0) and reduced box/DFL weights (0.5)
   - Every 3 epochs, it adjusts the weights based on the classification loss:
     - High loss (>0.1): Classification weight is doubled, other weights maintained
     - Medium loss (>0.05): Classification weight is increased by 50%, other weights reduced by 10%
     - Low loss (>0.01): Classification weight is increased by 20%, other weights reduced by 5%
     - Very low loss (<0.01): Classification weight maintained, other weights slightly increased
   - If precision or recall degrades by more than 2%, it compensates by increasing box/DFL weights

3. **Memory Optimization**:
   - Disables cache to reduce memory usage
   - Uses mixed precision training
   - Reduces the number of worker threads
   - Runs garbage collection between epochs
   - Monitors GPU memory usage

4. **Visualization**:
   - Generates loss charts showing classification, box, and DFL losses
   - Creates performance charts showing precision, recall, mAP50, and mAP50-95
   - Plots weight adjustments over time

## Usage

Simply run the batch file:

```
continue_training.bat
```

The script will:
1. Load the best model from epoch 86
2. Continue training for 15 more epochs
3. Dynamically adjust weights to focus on classification loss
4. Save models in a new directory to avoid overwriting existing models
5. Generate visualizations of the training process

## Configuration

The script uses the following configuration:

```python
CONFIG = {
    # Paths
    "base_model_path": "runs/detect/train/weights/best.pt",  # Best model from epoch 86
    "dataset_yaml": "chess_board_detection/piece_detection/targeted_dataset/dataset.yaml",
    "output_dir": "runs/detect/continue_training",

    # Training parameters
    "start_epoch": 91,  # Continue from epoch 91 (after 90)
    "epochs": 15,       # Train for 15 more epochs
    "batch_size": 16,   # Same batch size as original training
    "img_size": 416,    # Same image size as original training
    "patience": 100,    # High patience to allow training to continue

    # Loss weights
    "initial_cls_weight": 10.0,  # Start with high classification weight
    "initial_box_weight": 0.5,   # Reduced box weight to prioritize classification
    "initial_dfl_weight": 0.5,   # Reduced DFL weight to prioritize classification

    # Targets
    "target_cls_loss": 0.01,     # Target classification loss near zero
    "target_precision": 0.99,    # Target precision
    "target_recall": 0.99,       # Target recall

    # Memory optimization
    "cache": False,              # Disable cache to reduce memory usage
    "workers": 4,                # Reduce number of workers
    "amp": True,                 # Use mixed precision to reduce memory usage

    # Dynamic weight adjustment
    "dynamic_weights": True,     # Enable dynamic weight adjustment
    "weight_adjustment_epochs": 3, # Adjust weights every 3 epochs
    "max_cls_weight": 50.0,      # Maximum classification weight
    "min_box_weight": 0.1,       # Minimum box weight
    "min_dfl_weight": 0.1,       # Minimum DFL weight
}
```

You can modify these parameters in the `continue_training.py` file if needed.

## Output

The script saves the following outputs in the `runs/detect/continue_training` directory:

- **Trained Models**: Best model from each epoch
- **Training History**: JSON file with loss values, metrics, and weights for each epoch
- **Visualizations**: Charts showing losses, metrics, and weight adjustments
- **Configuration**: YAML file with the configuration used for training

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Ultralytics YOLO v8+
- Matplotlib
- NumPy

## Troubleshooting

If you encounter memory issues:
- Reduce the batch size
- Reduce the number of workers
- Increase the swap file size
- Close other applications to free up memory
