import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import time

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model and utilities
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
import matplotlib.pyplot as plt

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for model input."""
    # Load and resize the image
    image = Image.open(image_path).convert('RGB')
    original_image = np.array(image)

    # Resize while maintaining aspect ratio
    w, h = image.size
    ratio = min(target_size[0] / w, target_size[1] / h)
    new_size = (int(w * ratio), int(h * ratio))
    image = image.resize(new_size, Image.LANCZOS)

    # Create a black canvas of target size
    canvas = Image.new('RGB', target_size, (0, 0, 0))
    # Paste the resized image in the center
    offset = ((target_size[0] - new_size[0]) // 2, (target_size[1] - new_size[1]) // 2)
    canvas.paste(image, offset)

    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    input_tensor = transform(canvas).unsqueeze(0)

    return input_tensor, original_image, offset, ratio

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)

    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []

    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))

    return segmentation, heatmaps, keypoints

def visualize_results(original_image, segmentation, heatmaps, keypoints, offset, ratio, title):
    """Visualize the detection results."""
    # Create a figure
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    # Display the original image
    ax.imshow(original_image)

    # Convert keypoints back to original image coordinates
    original_keypoints = []
    for x, y, conf in keypoints:
        orig_x = (x - offset[0]) / ratio
        orig_y = (y - offset[1]) / ratio
        original_keypoints.append((orig_x, orig_y, conf))

    # Plot keypoints
    colors = ['r', 'g', 'b', 'y']
    labels = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    for i, (x, y, conf) in enumerate(original_keypoints):
        ax.scatter(x, y, c=colors[i], s=100, marker='x', linewidths=3)
        ax.text(x+10, y+10, f"{labels[i]}\nConf: {conf:.3f}", color=colors[i], fontsize=12,
                bbox=dict(facecolor='white', alpha=0.7))

    # Draw lines connecting the corners
    points = [(x, y) for x, y, _ in original_keypoints]
    if len(points) == 4:
        # Draw the quadrilateral
        points.append(points[0])  # Close the shape
        xs, ys = zip(*points)
        ax.plot(xs, ys, 'g-', linewidth=2)

    ax.set_title(title)
    ax.axis('off')

    return fig

def test_models(image_path, output_dir, model_paths):
    """Test multiple models on an image and save the results."""
    os.makedirs(output_dir, exist_ok=True)

    # Load and preprocess the image
    input_tensor, original_image, offset, ratio = preprocess_image(image_path)

    results = []

    for model_name, model_path in model_paths.items():
        print(f"Testing model: {model_name}")

        # Load the model
        model = load_model(model_path)

        # Run inference
        start_time = time.time()
        segmentation, heatmaps, keypoints = detect_corners(model, input_tensor)
        inference_time = time.time() - start_time

        # Visualize results
        fig = visualize_results(
            original_image, segmentation, heatmaps, keypoints, offset, ratio,
            f"{model_name} - Inference Time: {inference_time:.3f}s"
        )

        # Save the figure
        output_path = os.path.join(output_dir, f"{model_name}_result.png")
        fig.savefig(output_path, bbox_inches='tight')
        plt.close(fig)

        print(f"Results saved to: {output_path}")

        # Save detailed metrics
        results.append({
            'model_name': model_name,
            'inference_time': inference_time,
            'keypoints': keypoints,
            'confidences': [conf for _, _, conf in keypoints]
        })

        # Create heatmap visualization
        heatmap_fig = plt.figure(figsize=(15, 5))
        for i in range(4):
            plt.subplot(1, 4, i+1)
            plt.imshow(heatmaps[0, i].cpu().numpy())
            plt.title(f"Corner {i+1} Heatmap")
            plt.colorbar()

        # Save heatmap visualization
        heatmap_path = os.path.join(output_dir, f"{model_name}_heatmaps.png")
        heatmap_fig.savefig(heatmap_path, bbox_inches='tight')
        plt.close(heatmap_fig)

        # Create segmentation visualization
        seg_fig = plt.figure(figsize=(10, 5))
        plt.subplot(1, 2, 1)
        plt.imshow(original_image)
        plt.title("Original Image")

        plt.subplot(1, 2, 2)
        plt.imshow(segmentation[0, 0].cpu().numpy(), cmap='gray')
        plt.title("Segmentation Mask")
        plt.colorbar()

        # Save segmentation visualization
        seg_path = os.path.join(output_dir, f"{model_name}_segmentation.png")
        seg_fig.savefig(seg_path, bbox_inches='tight')
        plt.close(seg_fig)

    # Create comparison summary
    compare_metrics(results, output_dir)

    return results

def compare_metrics(results, output_dir):
    """Create a comparison of metrics between models."""
    fig, axs = plt.subplots(1, 2, figsize=(15, 6))

    # Compare inference times
    model_names = [r['model_name'] for r in results]
    inference_times = [r['inference_time'] for r in results]

    axs[0].bar(model_names, inference_times)
    axs[0].set_title('Inference Time (seconds)')
    axs[0].set_ylabel('Time (s)')

    # Compare average confidence
    avg_confidences = [np.mean(r['confidences']) for r in results]

    axs[1].bar(model_names, avg_confidences)
    axs[1].set_title('Average Corner Confidence')
    axs[1].set_ylabel('Confidence')

    plt.tight_layout()
    comparison_path = os.path.join(output_dir, "model_comparison.png")
    fig.savefig(comparison_path, bbox_inches='tight')
    plt.close(fig)

    # Create a detailed text report
    report_path = os.path.join(output_dir, "comparison_report.txt")
    with open(report_path, 'w') as f:
        f.write("Model Comparison Report\n")
        f.write("======================\n\n")

        for r in results:
            f.write(f"Model: {r['model_name']}\n")
            f.write(f"Inference Time: {r['inference_time']:.3f} seconds\n")
            f.write("Corner Confidences:\n")

            corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
            for i, conf in enumerate(r['confidences']):
                f.write(f"  {corner_names[i]}: {conf:.3f}\n")

            f.write(f"Average Confidence: {np.mean(r['confidences']):.3f}\n")
            f.write("\n")

        # Compare models
        f.write("Comparison:\n")
        faster_model = model_names[np.argmin(inference_times)]
        more_confident_model = model_names[np.argmax(avg_confidences)]

        f.write(f"Faster Model: {faster_model} ({min(inference_times):.3f}s)\n")
        f.write(f"More Confident Model: {more_confident_model} (Avg: {max(avg_confidences):.3f})\n")

if __name__ == "__main__":
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    # Test the models
    results = test_models(image_path, output_dir, model_paths)
