"""
Manual annotation tool for chess board corners.
This tool allows you to manually annotate the corners of a chess board in an image,
without seeing the model predictions first.
"""

import os
import sys
import numpy as np
import cv2
import json
import matplotlib.pyplot as plt
from matplotlib.widgets import Button
import datetime

class ChessBoardAnnotator:
    def __init__(self, image_path, output_dir):
        # Load image
        self.image_path = image_path
        self.output_dir = output_dir
        self.image = cv2.imread(image_path)
        if self.image is None:
            raise ValueError(f"Could not load image from {image_path}")
        
        self.image = cv2.cvtColor(self.image, cv2.COLOR_BGR2RGB)
        
        # Get image dimensions
        self.height, self.width = self.image.shape[:2]
        
        # Initialize corner points
        self.corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        self.corners = []
        self.current_corner_idx = 0
        
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        
        # Setup the figure
        self.setup_figure()
    
    def setup_figure(self):
        """Setup the matplotlib figure and axes for annotation."""
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.fig.suptitle('Chess Board Corner Annotation Tool', fontsize=16)
        
        # Display the image
        self.ax.imshow(self.image)
        self.ax.set_title(f'Click to annotate: {self.corner_names[self.current_corner_idx]}', fontsize=14)
        
        # Add instructions
        self.fig.text(0.5, 0.01, 
                     'Instructions:\n'
                     '1. Click on each corner in the order: Top-Left, Top-Right, Bottom-Right, Bottom-Left\n'
                     '2. Press "Reset" to clear all annotations and start over\n'
                     '3. Press "Save" when all corners are annotated to save the annotations',
                     ha='center', fontsize=12, bbox=dict(facecolor='white', alpha=0.8))
        
        # Add buttons
        self.reset_button_ax = plt.axes([0.7, 0.01, 0.1, 0.05])
        self.save_button_ax = plt.axes([0.85, 0.01, 0.1, 0.05])
        
        self.reset_button = Button(self.reset_button_ax, 'Reset')
        self.save_button = Button(self.save_button_ax, 'Save')
        
        self.reset_button.on_clicked(self.reset)
        self.save_button.on_clicked(self.save)
        
        # Connect the click event
        self.cid = self.fig.canvas.mpl_connect('button_press_event', self.on_click)
        
        # Initialize the corner markers and lines
        self.corner_markers = []
        self.corner_labels = []
        self.lines = []
    
    def on_click(self, event):
        """Handle mouse click events."""
        if event.inaxes != self.ax:
            return
        
        if self.current_corner_idx < len(self.corner_names):
            # Get the coordinates
            x, y = event.xdata, event.ydata
            
            # Add the corner
            self.corners.append((x, y))
            
            # Update the plot
            self.update_plot()
            
            # Move to the next corner
            self.current_corner_idx += 1
            
            if self.current_corner_idx < len(self.corner_names):
                self.ax.set_title(f'Click to annotate: {self.corner_names[self.current_corner_idx]}', fontsize=14)
            else:
                self.ax.set_title('All corners annotated. Press "Save" to save the annotations.', fontsize=14)
            
            self.fig.canvas.draw()
    
    def update_plot(self):
        """Update the plot with the current corners."""
        # Clear previous markers and lines
        for marker in self.corner_markers:
            marker.remove()
        for label in self.corner_labels:
            label.remove()
        for line in self.lines:
            line.remove()
        
        self.corner_markers = []
        self.corner_labels = []
        self.lines = []
        
        # Plot the corners
        colors = ['red', 'green', 'blue', 'yellow']
        for i, (x, y) in enumerate(self.corners):
            if i < len(self.corner_names):
                marker = self.ax.scatter(x, y, c=colors[i], s=100, marker='x', linewidths=2)
                label = self.ax.text(x+10, y+10, f"{self.corner_names[i]}\n({int(x)}, {int(y)})", 
                                    color=colors[i], fontsize=10,
                                    bbox=dict(facecolor='white', alpha=0.7))
                
                self.corner_markers.append(marker)
                self.corner_labels.append(label)
        
        # Draw lines connecting the corners
        if len(self.corners) > 1:
            for i in range(len(self.corners)):
                if i < len(self.corner_names) - 1:
                    line = self.ax.plot([self.corners[i][0], self.corners[i+1][0]], 
                                       [self.corners[i][1], self.corners[i+1][1]], 
                                       color='white', linestyle='-', linewidth=1, alpha=0.7)[0]
                    self.lines.append(line)
            
            # Close the polygon if all corners are annotated
            if len(self.corners) == len(self.corner_names):
                line = self.ax.plot([self.corners[-1][0], self.corners[0][0]], 
                                   [self.corners[-1][1], self.corners[0][1]], 
                                   color='white', linestyle='-', linewidth=1, alpha=0.7)[0]
                self.lines.append(line)
    
    def reset(self, event):
        """Reset all annotations."""
        self.corners = []
        self.current_corner_idx = 0
        
        # Clear previous markers and lines
        for marker in self.corner_markers:
            marker.remove()
        for label in self.corner_labels:
            label.remove()
        for line in self.lines:
            line.remove()
        
        self.corner_markers = []
        self.corner_labels = []
        self.lines = []
        
        self.ax.set_title(f'Click to annotate: {self.corner_names[self.current_corner_idx]}', fontsize=14)
        self.fig.canvas.draw()
    
    def save(self, event):
        """Save the annotations."""
        if len(self.corners) != len(self.corner_names):
            plt.figtext(0.5, 0.5, 'Please annotate all corners before saving.', 
                       ha='center', fontsize=14, color='red',
                       bbox=dict(facecolor='white', alpha=0.8))
            self.fig.canvas.draw()
            return
        
        # Create the annotation data
        annotation_data = {
            'image_path': self.image_path,
            'image_size': {'width': self.width, 'height': self.height},
            'corners': {}
        }
        
        for i, (x, y) in enumerate(self.corners):
            annotation_data['corners'][self.corner_names[i]] = {
                'x': int(x),
                'y': int(y)
            }
        
        # Generate a timestamp for the filename
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # Save as JSON
        json_path = os.path.join(self.output_dir, f'manual_annotation_{timestamp}.json')
        with open(json_path, 'w') as f:
            json.dump(annotation_data, f, indent=4)
        
        # Save visualization
        vis_path = os.path.join(self.output_dir, f'manual_annotation_{timestamp}.png')
        self.fig.savefig(vis_path, dpi=150, bbox_inches='tight')
        
        # Show confirmation
        plt.figtext(0.5, 0.5, f'Annotations saved to:\n{json_path}\n{vis_path}', 
                   ha='center', fontsize=14, color='green',
                   bbox=dict(facecolor='white', alpha=0.8))
        self.fig.canvas.draw()
        
        print(f"Annotations saved to: {json_path}")
        print(f"Visualization saved to: {vis_path}")
        
        # Create a clean visualization without the UI elements
        self.save_clean_visualization(vis_path.replace('.png', '_clean.png'))
    
    def save_clean_visualization(self, output_path):
        """Save a clean visualization without UI elements."""
        # Create a new figure
        fig, ax = plt.subplots(figsize=(12, 10))
        fig.suptitle('Manual Chess Board Corner Annotation', fontsize=16)
        
        # Display the image
        ax.imshow(self.image)
        
        # Plot the corners
        colors = ['red', 'green', 'blue', 'yellow']
        for i, (x, y) in enumerate(self.corners):
            ax.scatter(x, y, c=colors[i], s=100, marker='x', linewidths=2)
            ax.text(x+10, y+10, f"{self.corner_names[i]}\n({int(x)}, {int(y)})", 
                   color=colors[i], fontsize=10,
                   bbox=dict(facecolor='white', alpha=0.7))
        
        # Draw lines connecting the corners
        points = self.corners + [self.corners[0]]  # Close the polygon
        xs, ys = zip(*points)
        ax.plot(xs, ys, 'g-', linewidth=2, alpha=0.7)
        
        # Save the figure
        plt.tight_layout()
        plt.savefig(output_path, dpi=150, bbox_inches='tight')
        plt.close(fig)
        
        print(f"Clean visualization saved to: {output_path}")
    
    def run(self):
        """Run the annotation tool."""
        plt.tight_layout(rect=[0, 0.07, 1, 0.97])
        plt.show()

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\manual_annotations"
    
    # Create the annotator
    annotator = ChessBoardAnnotator(image_path, output_dir)
    
    # Run the annotation tool
    annotator.run()

if __name__ == "__main__":
    main()
