"""
<PERSON><PERSON><PERSON> to analyze the quality of corner heatmaps.
This helps diagnose issues with corner detection.
"""

import os
import argparse
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter
from scipy.ndimage.measurements import maximum_position
from tqdm import tqdm

from models.unet import ChessBoardUNet
from utils.real_dataset import ChessBoardDataset
from inference_real import find_peaks, validate_corners
from config import DATA_DIR, MODELS_DIR, DEVICE, INPUT_SIZE


def analyze_heatmap_quality(model, dataset, output_dir, num_samples=None):
    """
    Analyze the quality of corner heatmaps.
    
    Args:
        model: PyTorch model.
        dataset: Dataset to analyze.
        output_dir (str): Directory to save visualizations.
        num_samples (int, optional): Number of samples to analyze.
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Limit number of samples if specified
    if num_samples is not None:
        indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    else:
        indices = range(len(dataset))
    
    # Metrics to track
    peak_distances = []
    heatmap_overlaps = []
    peak_confidences = []
    
    for i in tqdm(indices, desc="Analyzing heatmaps"):
        sample = dataset[i]
        image = sample['image'].unsqueeze(0).to(DEVICE)
        gt_corners = sample['corners'].numpy().reshape(-1, 2)
        
        # Run inference
        with torch.no_grad():
            outputs = model(image)
        
        # Get corner heatmaps
        corner_heatmaps = outputs['corner_heatmaps'][0].cpu().numpy()
        
        # Calculate metrics
        metrics = calculate_heatmap_metrics(corner_heatmaps)
        peak_distances.extend(metrics['peak_distances'])
        heatmap_overlaps.extend(metrics['heatmap_overlaps'])
        peak_confidences.extend(metrics['peak_confidences'])
        
        # Visualize
        if i < 10:  # Only visualize first 10 samples
            visualize_heatmaps(
                sample['image'].permute(1, 2, 0).numpy(),
                corner_heatmaps,
                gt_corners,
                metrics,
                os.path.join(output_dir, f"heatmap_analysis_{i}.png")
            )
    
    # Plot overall metrics
    plot_metrics(peak_distances, heatmap_overlaps, peak_confidences, output_dir)
    
    print("Analysis completed!")


def calculate_heatmap_metrics(corner_heatmaps):
    """
    Calculate metrics for heatmap quality.
    
    Args:
        corner_heatmaps (numpy.ndarray): Corner heatmaps.
    
    Returns:
        dict: Metrics.
    """
    # Find peaks
    peaks = []
    peak_confidences = []
    for i in range(4):
        heatmap = corner_heatmaps[i]
        y, x = maximum_position(heatmap)
        peaks.append((x, y))
        peak_confidences.append(heatmap[y, x])
    
    # Calculate distances between peaks
    peak_distances = []
    for i in range(4):
        for j in range(i+1, 4):
            p1 = np.array(peaks[i])
            p2 = np.array(peaks[j])
            dist = np.sqrt(np.sum((p1 - p2)**2))
            peak_distances.append(dist)
    
    # Calculate overlap between heatmaps
    heatmap_overlaps = []
    for i in range(4):
        for j in range(i+1, 4):
            h1 = corner_heatmaps[i]
            h2 = corner_heatmaps[j]
            # Normalize heatmaps
            h1 = h1 / np.max(h1) if np.max(h1) > 0 else h1
            h2 = h2 / np.max(h2) if np.max(h2) > 0 else h2
            # Calculate overlap
            overlap = np.sum(h1 * h2) / (np.sum(h1) + np.sum(h2) - np.sum(h1 * h2))
            heatmap_overlaps.append(overlap)
    
    return {
        'peaks': peaks,
        'peak_confidences': peak_confidences,
        'peak_distances': peak_distances,
        'heatmap_overlaps': heatmap_overlaps
    }


def visualize_heatmaps(image, corner_heatmaps, gt_corners, metrics, output_path):
    """
    Visualize heatmaps and metrics.
    
    Args:
        image (numpy.ndarray): Input image.
        corner_heatmaps (numpy.ndarray): Corner heatmaps.
        gt_corners (numpy.ndarray): Ground truth corners.
        metrics (dict): Heatmap metrics.
        output_path (str): Path to save visualization.
    """
    plt.figure(figsize=(15, 10))
    
    # Original image with ground truth corners
    plt.subplot(2, 3, 1)
    plt.imshow(image)
    for i, (x, y) in enumerate(gt_corners):
        plt.plot(x, y, 'go', markersize=8)
        plt.text(x, y, str(i+1), color='white', fontsize=12,
                bbox=dict(facecolor='green', alpha=0.7))
    plt.title('Ground Truth Corners')
    plt.axis('off')
    
    # Heatmap overlays
    plt.subplot(2, 3, 2)
    plt.imshow(image)
    
    # Create a combined heatmap with different colors for each corner
    combined_heatmap = np.zeros((*corner_heatmaps[0].shape, 3))
    colors = [(1, 0, 0), (0, 1, 0), (0, 0, 1), (1, 1, 0)]  # R, G, B, Y
    
    for i in range(4):
        heatmap = corner_heatmaps[i]
        heatmap = heatmap / np.max(heatmap) if np.max(heatmap) > 0 else heatmap
        for c in range(3):
            combined_heatmap[:, :, c] += heatmap * colors[i][c]
    
    # Normalize and overlay
    combined_heatmap = np.clip(combined_heatmap, 0, 1)
    plt.imshow(combined_heatmap, alpha=0.6)
    
    # Plot detected peaks
    for i, (x, y) in enumerate(metrics['peaks']):
        plt.plot(x, y, 'ro', markersize=8)
        plt.text(x, y, str(i+1), color='white', fontsize=12,
                bbox=dict(facecolor='red', alpha=0.7))
    
    plt.title('Heatmap Overlay with Peaks')
    plt.axis('off')
    
    # Individual heatmaps
    for i in range(4):
        plt.subplot(2, 3, i+3)
        plt.imshow(corner_heatmaps[i], cmap='jet')
        plt.title(f'Corner {i+1} Heatmap (Conf: {metrics["peak_confidences"][i]:.2f})')
        plt.axis('off')
        
        # Plot peak
        x, y = metrics['peaks'][i]
        plt.plot(x, y, 'ro', markersize=6)
    
    # Add metrics text
    plt.figtext(0.5, 0.01, 
                f"Avg Peak Distance: {np.mean(metrics['peak_distances']):.2f}, "
                f"Avg Heatmap Overlap: {np.mean(metrics['heatmap_overlaps']):.2f}, "
                f"Avg Peak Confidence: {np.mean(metrics['peak_confidences']):.2f}",
                ha='center', fontsize=12, bbox=dict(facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def plot_metrics(peak_distances, heatmap_overlaps, peak_confidences, output_dir):
    """
    Plot overall metrics.
    
    Args:
        peak_distances (list): List of peak distances.
        heatmap_overlaps (list): List of heatmap overlaps.
        peak_confidences (list): List of peak confidences.
        output_dir (str): Directory to save plots.
    """
    plt.figure(figsize=(15, 5))
    
    # Peak distances
    plt.subplot(1, 3, 1)
    plt.hist(peak_distances, bins=20)
    plt.title('Peak Distances')
    plt.xlabel('Distance')
    plt.ylabel('Frequency')
    
    # Heatmap overlaps
    plt.subplot(1, 3, 2)
    plt.hist(heatmap_overlaps, bins=20)
    plt.title('Heatmap Overlaps')
    plt.xlabel('Overlap')
    plt.ylabel('Frequency')
    
    # Peak confidences
    plt.subplot(1, 3, 3)
    plt.hist(peak_confidences, bins=20)
    plt.title('Peak Confidences')
    plt.xlabel('Confidence')
    plt.ylabel('Frequency')
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, "heatmap_metrics.png"))
    plt.close()


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Analyze Heatmap Quality')
    parser.add_argument('--model', type=str, default=os.path.join(MODELS_DIR, 'best_model_real.pth'),
                        help='Path to model checkpoint')
    parser.add_argument('--data_dir', type=str, default=os.path.join(DATA_DIR, 'real'),
                        help='Path to data directory')
    parser.add_argument('--annotation_file', type=str, default=os.path.join(DATA_DIR, 'real_annotations.json'),
                        help='Path to annotation file')
    parser.add_argument('--output_dir', type=str, default='heatmap_analysis',
                        help='Directory to save analysis results')
    parser.add_argument('--num_samples', type=int, default=None,
                        help='Number of samples to analyze')
    args = parser.parse_args()
    
    # Load model
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    model.load_state_dict(torch.load(args.model, map_location=DEVICE))
    model = model.to(DEVICE)
    model.eval()
    
    # Create dataset
    dataset = ChessBoardDataset(
        data_dir=args.data_dir,
        annotation_file=args.annotation_file,
        split='test'
    )
    
    # Analyze heatmaps
    analyze_heatmap_quality(model, dataset, args.output_dir, args.num_samples)


if __name__ == "__main__":
    main()
