"""
<PERSON><PERSON><PERSON> to implement optimal preprocessing for real-world images based on training augmentations.
This applies sophisticated preprocessing to showcase the models' achievements on real images.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import time
import pandas as pd

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_real_image(image_path, target_size=(256, 256)):
    """
    Preprocess a real-world image for optimal model performance.
    Preserves aspect ratio and applies appropriate resizing.
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """
    Enhance image colors and contrast similar to training augmentations.
    """
    # 1. Adaptive contrast enhancement (similar to CLAHE in training)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    l, a, b = cv2.split(lab)
    l = clahe.apply(l)
    lab = cv2.merge([l, a, b])
    image_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)

    # 2. Gamma correction (similar to RandomGamma in training)
    # Use a gamma value that enhances details without over-brightening
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)]).astype("uint8")
    image_enhanced = cv2.LUT(image_enhanced, table)

    # 3. Saturation enhancement (similar to HueSaturationValue in training)
    hsv = cv2.cvtColor(image_enhanced, cv2.COLOR_RGB2HSV)
    h, s, v = cv2.split(hsv)
    s = np.clip(s * 1.2, 0, 255).astype(np.uint8)  # Increase saturation by 20%
    hsv = cv2.merge([h, s, v])
    image_enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)

    return image_enhanced

def denoise_and_enhance_details(image):
    """
    Apply denoising and detail enhancement similar to training conditions.
    """
    # Apply slight denoising (similar to what model was trained to handle)
    image_denoised = cv2.fastNlMeansDenoisingColored(image, None, 5, 5, 7, 21)

    # Enhance edges and details
    kernel_sharpening = np.array([[-1,-1,-1],
                                  [-1, 9,-1],
                                  [-1,-1,-1]])
    image_enhanced = cv2.filter2D(image_denoised, -1, kernel_sharpening)

    return image_enhanced

def normalize_for_model(image):
    """
    Normalize image for model input using same normalization as during training.
    """
    # Convert to PIL Image first
    image_pil = Image.fromarray(image)

    # Use torchvision transforms which handle the data type correctly
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Apply transform and add batch dimension
    image_tensor = transform(image_pil).unsqueeze(0)

    return image_tensor

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)

    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []

    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))

    return segmentation, heatmaps, keypoints

def map_to_original_coordinates(keypoints, preprocess_info):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    Accounts for all preprocessing steps: resize, crop, and final resize.
    """
    mapped_keypoints = []

    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']

    for x, y, conf in keypoints:
        # Step 1: Map from target size to crop size
        crop_scale_x = crop_size / target_w
        crop_scale_y = crop_size / target_h

        x_in_crop = x * crop_scale_x
        y_in_crop = y * crop_scale_y

        # Step 2: Add crop offset to get coordinates in resized image
        x_in_resized = x_in_crop + crop_start_x
        y_in_resized = y_in_crop + crop_start_y

        # Step 3: Scale to original image size
        resized_w, resized_h = preprocess_info['resized_size']

        orig_scale_x = original_w / resized_w
        orig_scale_y = original_h / resized_h

        x_in_original = x_in_resized * orig_scale_x
        y_in_original = y_in_resized * orig_scale_y

        # Ensure coordinates are within image bounds
        x_in_original = max(0, min(x_in_original, original_w - 1))
        y_in_original = max(0, min(y_in_original, original_h - 1))

        mapped_keypoints.append((x_in_original, y_in_original, conf))

    return mapped_keypoints

def visualize_preprocessing_steps(preprocessing_results, output_path):
    """
    Create a visualization showing all preprocessing steps and detection results.
    """
    # Create figure with 3 rows and 3 columns
    fig, axs = plt.subplots(3, 3, figsize=(18, 15))
    fig.suptitle(f'Optimal Preprocessing Pipeline for Chess Board Detection - {preprocessing_results["model_name"]}', fontsize=16)

    # Row 1: Original image and preprocessing steps
    axs[0, 0].imshow(preprocessing_results['original_image'])
    axs[0, 0].set_title('Original Image', fontsize=12)
    axs[0, 0].axis('off')

    axs[0, 1].imshow(preprocessing_results['basic_preprocessed'])
    axs[0, 1].set_title('Basic Preprocessing\n(Resize & Crop)', fontsize=12)
    axs[0, 1].axis('off')

    axs[0, 2].imshow(preprocessing_results['enhanced_image'])
    axs[0, 2].set_title('Color Enhancement\n(CLAHE, Gamma, Saturation)', fontsize=12)
    axs[0, 2].axis('off')

    # Row 2: Final preprocessing and model inputs
    axs[1, 0].imshow(preprocessing_results['detail_enhanced'])
    axs[1, 0].set_title('Detail Enhancement\n(Denoise & Sharpen)', fontsize=12)
    axs[1, 0].axis('off')

    axs[1, 1].imshow(preprocessing_results['segmentation'][0, 0], cmap='gray')
    axs[1, 1].set_title('Segmentation Mask', fontsize=12)
    axs[1, 1].axis('off')

    # Create combined heatmap visualization
    combined_hm = np.zeros((preprocessing_results['heatmaps'].shape[2],
                           preprocessing_results['heatmaps'].shape[3], 3))

    for i in range(4):
        hm = preprocessing_results['heatmaps'][0, i]

        # Normalize heatmap
        if np.max(hm) > 0:
            hm = hm / np.max(hm)

        # Add to combined heatmap with color
        color_idx = i / 3.0
        color = plt.cm.jet(color_idx)[:3]

        for ch in range(3):
            combined_hm[:, :, ch] += hm * color[ch]

    # Normalize combined heatmap
    if np.max(combined_hm) > 0:
        combined_hm = combined_hm / np.max(combined_hm)

    axs[1, 2].imshow(combined_hm)
    axs[1, 2].set_title('Combined Heatmaps', fontsize=12)
    axs[1, 2].axis('off')

    # Row 3: Individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        if i < 3:
            ax = axs[2, i]
        else:
            # Create a special subplot for the 4th heatmap
            ax = plt.subplot2grid((3, 3), (2, 2), fig=fig)

        hm = preprocessing_results['heatmaps'][0, i]
        ax.imshow(hm, cmap='jet')
        ax.set_title(f'{corner_names[i]} Heatmap\nConfidence: {preprocessing_results["model_keypoints"][i][2]:.3f}', fontsize=12)
        ax.axis('off')

    # Add original image with mapped keypoints
    orig_img = preprocessing_results['original_image'].copy()

    # Draw keypoints and lines on original image
    keypoints = preprocessing_results['original_keypoints']
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # BGR format for OpenCV

    # Create a copy for drawing
    vis_img = orig_img.copy()

    # Draw keypoints
    for i, (x, y, conf) in enumerate(keypoints):
        cv2.circle(vis_img, (int(x), int(y)), 15, colors[i], -1)
        cv2.putText(vis_img, f"{corner_names[i]} ({int(x)}, {int(y)})",
                   (int(x) + 20, int(y) + 20), cv2.FONT_HERSHEY_SIMPLEX,
                   1.0, colors[i], 3)

    # Draw lines connecting keypoints
    points = [(int(x), int(y)) for x, y, _ in keypoints]
    if len(points) == 4:
        for i in range(4):
            cv2.line(vis_img, points[i], points[(i + 1) % 4], (0, 255, 0), 5)

    # Create a new figure for the final result
    plt.figure(figsize=(12, 10))
    plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    plt.title(f'Final Detection Result - {preprocessing_results["model_name"]}', fontsize=16)
    plt.axis('off')

    # Save the final result
    final_output_path = output_path.replace('.png', '_final_result.png')
    plt.tight_layout()
    plt.savefig(final_output_path, dpi=150, bbox_inches='tight')
    plt.close()

    # Save the preprocessing steps
    plt.tight_layout(rect=[0, 0, 1, 0.97])
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    return output_path, final_output_path

def process_real_world_image(image_path, model, model_name):
    """
    Apply the complete optimal preprocessing pipeline to a real-world image.
    """
    # Step 1: Basic preprocessing (resize & crop)
    basic_preprocessed, preprocess_info = preprocess_real_image(image_path)

    # Step 2: Color/lighting enhancement
    enhanced_image = enhance_image(basic_preprocessed)

    # Step 3: Denoise and enhance details
    detail_enhanced = denoise_and_enhance_details(enhanced_image)

    # Step 4: Normalize for model
    input_tensor = normalize_for_model(detail_enhanced)

    # Step 5: Run inference
    segmentation, heatmaps, model_keypoints = detect_corners(model, input_tensor)

    # Step 6: Map keypoints back to original image
    original_keypoints = map_to_original_coordinates(model_keypoints, preprocess_info)

    # Return all results
    return {
        'model_name': model_name,
        'original_image': preprocess_info['original_image'],
        'basic_preprocessed': basic_preprocessed,
        'enhanced_image': enhanced_image,
        'detail_enhanced': detail_enhanced,
        'segmentation': segmentation.cpu().numpy(),
        'heatmaps': heatmaps.cpu().numpy(),
        'model_keypoints': model_keypoints,
        'original_keypoints': original_keypoints
    }

def create_corner_coordinates_table(results_dict, output_path):
    """Create a table with corner coordinates and save it as CSV."""
    # Corner names
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Create data for table
    table_data = []

    for model_name, results in results_dict.items():
        for i, (x, y, conf) in enumerate(results['original_keypoints']):
            table_data.append({
                'Model': model_name,
                'Corner': corner_names[i],
                'X': int(x),
                'Y': int(y),
                'Confidence': round(conf, 4)
            })

    # Create DataFrame
    df = pd.DataFrame(table_data)

    # Save as CSV
    csv_path = output_path.replace('.png', '_coordinates.csv')
    df.to_csv(csv_path, index=False)

    # Print table
    print("\nChess Board Corner Detection - Pixel Coordinates")
    print("==============================================")
    print(df.to_string(index=False))

    return csv_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    os.makedirs(output_dir, exist_ok=True)

    # Process each model
    results_dict = {}

    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} with optimal preprocessing...")

        # Load model
        model = load_model(model_path)

        # Process image with optimal preprocessing
        results = process_real_world_image(image_path, model, model_name)

        # Store results
        results_dict[model_name] = results

        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_optimal_preprocessing.png")
        preprocessing_path, final_path = visualize_preprocessing_steps(results, output_path)

        print(f"Preprocessing visualization saved to: {preprocessing_path}")
        print(f"Final detection result saved to: {final_path}")

    # Create coordinates table
    csv_path = create_corner_coordinates_table(results_dict, os.path.join(output_dir, "optimal_preprocessing"))
    print(f"Corner coordinates saved to: {csv_path}")

    print("All visualizations completed!")

if __name__ == "__main__":
    main()
