package com.chessvision.app.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import java.io.InputStream

/**
 * 🏆 WORLD-CLASS: Image utilities for chess board processing
 */
object ImageUtils {
    private const val TAG = "ImageUtils"

    /**
     * Load bitmap from URI with proper error handling
     */
    fun loadBitmapFromUri(context: Context, uri: Uri): Bitmap? {
        return try {
            val inputStream: InputStream? = context.contentResolver.openInputStream(uri)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream?.close()
            
            if (bitmap != null) {
                Log.d(TAG, "✅ Loaded bitmap: ${bitmap.width}x${bitmap.height}")
            } else {
                Log.e(TAG, "❌ Failed to decode bitmap from URI: $uri")
            }
            
            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error loading bitmap from URI: $uri", e)
            null
        }
    }

    /**
     * Load bitmap from assets with proper error handling
     */
    fun loadBitmapFromAssets(context: Context, fileName: String): Bitmap? {
        return try {
            val inputStream = context.assets.open(fileName)
            val bitmap = BitmapFactory.decodeStream(inputStream)
            inputStream.close()
            
            if (bitmap != null) {
                Log.d(TAG, "✅ Loaded asset bitmap: ${bitmap.width}x${bitmap.height} from $fileName")
            } else {
                Log.e(TAG, "❌ Failed to decode bitmap from asset: $fileName")
            }
            
            bitmap
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error loading bitmap from assets: $fileName", e)
            null
        }
    }

    /**
     * Create a test chessboard bitmap if asset loading fails
     */
    fun createTestChessboard(): Bitmap {
        val size = 512
        val bitmap = Bitmap.createBitmap(size, size, Bitmap.Config.RGB_565)
        
        // Create a simple 8x8 checkerboard pattern
        val squareSize = size / 8
        val pixels = IntArray(size * size)
        
        for (y in 0 until size) {
            for (x in 0 until size) {
                val squareX = x / squareSize
                val squareY = y / squareSize
                val isLight = (squareX + squareY) % 2 == 0
                
                pixels[y * size + x] = if (isLight) {
                    0xFFE0E0E0.toInt() // Light gray
                } else {
                    0xFF404040.toInt() // Dark gray
                }
            }
        }
        
        bitmap.setPixels(pixels, 0, size, 0, 0, size, size)
        Log.d(TAG, "✅ Created test chessboard: ${size}x${size}")
        
        return bitmap
    }
}
