"""
Enhanced Dataset V2 with advanced augmentations, higher resolution, and improved data pipeline.
"""

import os
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import glob
import cv2
import albumentations as A
from albumentations.pytorch import ToTensorV2
from sklearn.model_selection import StratifiedKFold
import random

class AdvancedAugmentations:
    """Advanced augmentation strategies for chess board segmentation."""

    @staticmethod
    def get_training_transforms(image_size=(512, 512), p=0.8):
        """Get advanced training augmentations."""
        return <PERSON><PERSON><PERSON>([
            # Geometric augmentations
            A.Resize(height=image_size[0], width=image_size[1]),
            A<PERSON>(p=0.5),
            <PERSON><PERSON>(p=0.2),
            A.<PERSON>(p=0.5),
            A.<PERSON>ota<PERSON>(limit=15, p=0.7, border_mode=cv2.BORDER_CONSTANT),
            <PERSON><PERSON>ft<PERSON>(
                shift_limit=0.1,
                scale_limit=0.2,
                rotate_limit=15,
                p=0.7,
                border_mode=cv2.BORDER_CONSTANT
            ),

            # Perspective and distortion
            A.Perspective(scale=(0.05, 0.15), p=0.5),
            <PERSON><PERSON>asticTransform(alpha=1, sigma=50, p=0.3),
            A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.3),
            A.OpticalDistortion(distort_limit=0.2, p=0.3),

            # Color and lighting augmentations
            A.RandomBrightnessContrast(
                brightness_limit=0.3,
                contrast_limit=0.3,
                p=0.7
            ),
            A.HueSaturationValue(
                hue_shift_limit=20,
                sat_shift_limit=30,
                val_shift_limit=30,
                p=0.6
            ),
            A.CLAHE(clip_limit=4.0, tile_grid_size=(8, 8), p=0.5),
            A.RandomGamma(gamma_limit=(80, 120), p=0.5),
            A.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1, p=0.5),

            # Noise and blur
            A.OneOf([
                A.GaussNoise(noise_scale_factor=0.1, p=1.0),
                A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=1.0),
                A.MultiplicativeNoise(multiplier=(0.9, 1.1), p=1.0),
            ], p=0.4),

            A.OneOf([
                A.Blur(blur_limit=3, p=1.0),
                A.GaussianBlur(blur_limit=3, p=1.0),
                A.MotionBlur(blur_limit=3, p=1.0),
            ], p=0.3),

            # Weather and environmental effects
            A.OneOf([
                A.RandomShadow(shadow_roi=(0, 0.5, 1, 1), p=1.0),
                A.RandomSunFlare(flare_roi=(0, 0, 1, 0.5), p=1.0),
                A.RandomRain(p=1.0),
            ], p=0.2),

            # Cutout and erasing
            A.CoarseDropout(
                max_holes=8,
                max_height=32,
                max_width=32,
                p=0.3
            ),

            # Normalization and tensor conversion
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])

    @staticmethod
    def get_validation_transforms(image_size=(512, 512)):
        """Get validation transforms."""
        return A.Compose([
            A.Resize(height=image_size[0], width=image_size[1]),
            A.Normalize(
                mean=[0.485, 0.456, 0.406],
                std=[0.229, 0.224, 0.225]
            ),
            ToTensorV2()
        ])

    @staticmethod
    def get_test_time_augmentations(image_size=(512, 512)):
        """Get test-time augmentations for ensemble prediction."""
        return [
            # Original
            A.Compose([
                A.Resize(height=image_size[0], width=image_size[1]),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ]),
            # Horizontal flip
            A.Compose([
                A.Resize(height=image_size[0], width=image_size[1]),
                A.HorizontalFlip(p=1.0),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ]),
            # Vertical flip
            A.Compose([
                A.Resize(height=image_size[0], width=image_size[1]),
                A.VerticalFlip(p=1.0),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ]),
            # Rotate 90
            A.Compose([
                A.Resize(height=image_size[0], width=image_size[1]),
                A.RandomRotate90(p=1.0),
                A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
                ToTensorV2()
            ]),
        ]

class MixupCutmixDataset(Dataset):
    """Dataset wrapper for Mixup and CutMix augmentations."""

    def __init__(self, dataset, mixup_alpha=0.2, cutmix_alpha=1.0, mixup_prob=0.5, cutmix_prob=0.5):
        self.dataset = dataset
        self.mixup_alpha = mixup_alpha
        self.cutmix_alpha = cutmix_alpha
        self.mixup_prob = mixup_prob
        self.cutmix_prob = cutmix_prob

    def __len__(self):
        return len(self.dataset)

    def __getitem__(self, idx):
        image, mask = self.dataset[idx]

        # Apply mixup or cutmix randomly
        if random.random() < self.mixup_prob:
            return self._mixup(image, mask, idx)
        elif random.random() < self.cutmix_prob:
            return self._cutmix(image, mask, idx)
        else:
            return image, mask

    def _mixup(self, image1, mask1, idx1):
        """Apply mixup augmentation."""
        idx2 = random.randint(0, len(self.dataset) - 1)
        image2, mask2 = self.dataset[idx2]

        lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)

        mixed_image = lam * image1 + (1 - lam) * image2
        mixed_mask = lam * mask1 + (1 - lam) * mask2

        return mixed_image, mixed_mask

    def _cutmix(self, image1, mask1, idx1):
        """Apply cutmix augmentation."""
        idx2 = random.randint(0, len(self.dataset) - 1)
        image2, mask2 = self.dataset[idx2]

        lam = np.random.beta(self.cutmix_alpha, self.cutmix_alpha)

        H, W = image1.shape[1], image1.shape[2]
        cut_rat = np.sqrt(1. - lam)
        cut_w = int(W * cut_rat)
        cut_h = int(H * cut_rat)

        # Uniform sampling
        cx = np.random.randint(W)
        cy = np.random.randint(H)

        bbx1 = np.clip(cx - cut_w // 2, 0, W)
        bby1 = np.clip(cy - cut_h // 2, 0, H)
        bbx2 = np.clip(cx + cut_w // 2, 0, W)
        bby2 = np.clip(cy + cut_h // 2, 0, H)

        mixed_image = image1.clone()
        mixed_mask = mask1.clone()

        mixed_image[:, bby1:bby2, bbx1:bbx2] = image2[:, bby1:bby2, bbx1:bbx2]
        mixed_mask[bby1:bby2, bbx1:bbx2] = mask2[bby1:bby2, bbx1:bbx2]

        return mixed_image, mixed_mask

class EnhancedSegmentationDatasetV2(Dataset):
    """Enhanced segmentation dataset with progressive resizing and advanced features."""

    def __init__(self, dataset_dir, sample_folders=None, transform=None, image_size=(512, 512),
                 progressive_resize=False, current_epoch=0, max_epochs=100):
        self.dataset_dir = Path(dataset_dir)
        self.transform = transform
        self.image_size = image_size
        self.progressive_resize = progressive_resize
        self.current_epoch = current_epoch
        self.max_epochs = max_epochs

        # Find all sample folders
        if sample_folders is None:
            self.sample_folders = sorted([
                d for d in self.dataset_dir.iterdir()
                if d.is_dir() and d.name.startswith('sample_')
            ])
        else:
            self.sample_folders = [self.dataset_dir / folder for folder in sample_folders]

        # Filter valid folders
        valid_folders = []
        for folder in self.sample_folders:
            image_path = folder / "image.pt"
            mask_path = folder / "mask.pt"
            if image_path.exists() and mask_path.exists():
                valid_folders.append(folder)

        self.sample_folders = valid_folders
        print(f"Found {len(self.sample_folders)} valid samples")

    def update_epoch(self, epoch):
        """Update current epoch for progressive resizing."""
        self.current_epoch = epoch
        if self.progressive_resize:
            self._update_image_size()

    def _update_image_size(self):
        """Update image size based on progressive resizing schedule."""
        if self.current_epoch < self.max_epochs * 0.3:
            self.image_size = (256, 256)
        elif self.current_epoch < self.max_epochs * 0.6:
            self.image_size = (384, 384)
        else:
            self.image_size = (512, 512)

    def __len__(self):
        return len(self.sample_folders)

    def __getitem__(self, idx):
        folder = self.sample_folders[idx]

        # Load image and mask tensors
        image_path = folder / "image.pt"
        mask_path = folder / "mask.pt"

        try:
            # Load tensors
            image = torch.load(image_path, map_location='cpu')
            mask = torch.load(mask_path, map_location='cpu')

            # Ensure correct data types and shapes
            image = image.float()
            mask = mask.float()

            # Ensure image is in CHW format
            if image.dim() == 3 and image.shape[0] != 3:
                if image.shape[2] == 3:
                    image = image.permute(2, 0, 1)

            # Ensure mask is 2D (HW)
            if mask.dim() == 3:
                mask = mask.squeeze()

            # Normalize to [0, 1]
            if image.max() > 1.0:
                image = image / 255.0
            if mask.max() > 1.0:
                mask = mask / 255.0

            # Apply transforms
            if self.transform:
                # Convert to numpy for albumentations
                image_np = image.permute(1, 2, 0).numpy()
                mask_np = mask.numpy()

                # Resize to current progressive size if needed
                if hasattr(self.transform, 'transforms'):
                    # Update resize transform
                    for t in self.transform.transforms:
                        if isinstance(t, A.Resize):
                            t.height = self.image_size[0]
                            t.width = self.image_size[1]

                transformed = self.transform(image=image_np, mask=mask_np)
                image = transformed['image']
                mask = transformed['mask']

            return image, mask

        except Exception as e:
            print(f"Error loading sample {folder.name}: {e}")
            # Return a dummy sample
            return torch.zeros(3, self.image_size[0], self.image_size[1]), torch.zeros(self.image_size[0], self.image_size[1])

def create_stratified_folds(dataset_dir, n_folds=5, random_state=42):
    """Create stratified K-fold splits for cross-validation."""
    dataset_path = Path(dataset_dir)

    # Get all sample folders
    all_folders = sorted([
        d.name for d in dataset_path.iterdir()
        if d.is_dir() and d.name.startswith('sample_')
    ])

    # Create stratification labels (based on original image source)
    labels = []
    for folder in all_folders:
        # Extract original image identifier for stratification
        # Assuming folder names like 'sample_0001_aug_0', 'sample_0001_aug_1', etc.
        base_id = folder.split('_')[1] if '_' in folder else '0'
        labels.append(int(base_id) % 5)  # Create 5 strata

    # Create stratified folds
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=random_state)
    folds = []

    for train_idx, val_idx in skf.split(all_folders, labels):
        train_folders = [all_folders[i] for i in train_idx]
        val_folders = [all_folders[i] for i in val_idx]
        folds.append((train_folders, val_folders))

    return folds

def create_enhanced_dataloaders_v2(
    dataset_dir,
    fold_idx=0,
    n_folds=5,
    batch_size=8,
    image_size=(512, 512),
    num_workers=0,
    use_mixup_cutmix=True,
    progressive_resize=False,
    current_epoch=0,
    max_epochs=100
):
    """Create enhanced dataloaders with cross-validation and advanced augmentations."""

    # Create stratified folds
    folds = create_stratified_folds(dataset_dir, n_folds)
    train_folders, val_folders = folds[fold_idx]

    print(f"Fold {fold_idx + 1}/{n_folds}")
    print(f"Train samples: {len(train_folders)}")
    print(f"Val samples: {len(val_folders)}")

    # Create transforms
    train_transform = AdvancedAugmentations.get_training_transforms(image_size)
    val_transform = AdvancedAugmentations.get_validation_transforms(image_size)

    # Create datasets
    train_dataset = EnhancedSegmentationDatasetV2(
        dataset_dir, train_folders, train_transform, image_size,
        progressive_resize, current_epoch, max_epochs
    )

    val_dataset = EnhancedSegmentationDatasetV2(
        dataset_dir, val_folders, val_transform, image_size
    )

    # Apply mixup/cutmix to training dataset
    if use_mixup_cutmix:
        train_dataset = MixupCutmixDataset(train_dataset)

    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False,
        drop_last=True
    )

    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )

    return train_loader, val_loader, train_dataset, val_dataset

if __name__ == "__main__":
    # Test the enhanced dataset
    DATASET_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326"

    print("Testing enhanced dataset V2...")

    # Test stratified folds
    folds = create_stratified_folds(DATASET_DIR, n_folds=5)
    print(f"Created {len(folds)} folds")

    # Test dataloader creation
    train_loader, val_loader, train_dataset, val_dataset = create_enhanced_dataloaders_v2(
        DATASET_DIR,
        fold_idx=0,
        batch_size=4,
        image_size=(512, 512),
        use_mixup_cutmix=True
    )

    # Test batch loading
    for batch_idx, (images, masks) in enumerate(train_loader):
        print(f"Batch {batch_idx}: Images {images.shape}, Masks {masks.shape}")
        print(f"Image range: [{images.min():.3f}, {images.max():.3f}]")
        print(f"Mask range: [{masks.min():.3f}, {masks.max():.3f}]")
        if batch_idx == 0:
            break

    print("Enhanced dataset V2 test successful!")
