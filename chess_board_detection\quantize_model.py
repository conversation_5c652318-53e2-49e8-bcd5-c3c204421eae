"""
Quantize chess board detection model for faster inference.

This script loads a trained model and quantizes it to INT8 format
for faster inference and smaller size.
"""

import os
import argparse
import torch
import torch.quantization as quantization
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from utils.model_optimization import quantize_model, get_model_size_mb
from utils.augmented_dataset import create_augmented_dataloaders
from config import MODELS_DIR, INPUT_SIZE, DATA_DIR


def main():
    parser = argparse.ArgumentParser(description='Quantize chess board detection model')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'deployment'),
                        help='Output directory for quantized model')
    parser.add_argument('--augmented_data_dir', type=str, 
                        default=os.path.join(DATA_DIR, 'augmented', 'v5.2'),
                        help='Directory containing augmented data for calibration')
    parser.add_argument('--backend', type=str, default='fbgemm', choices=['fbgemm', 'qnnpack'],
                        help='Quantization backend (fbgemm for x86, qnnpack for ARM)')
    parser.add_argument('--batch_size', type=int, default=4,
                        help='Batch size for calibration')
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device (quantization is done on CPU)
    device = torch.device('cpu')
    print(f"Using device: {device}")

    # Initialize model
    print("Initializing model...")
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    
    # Load checkpoint
    print(f"Loading checkpoint from {args.checkpoint}...")
    checkpoint = torch.load(args.checkpoint, map_location=device)
    model.load_state_dict(checkpoint)
    
    # Set model to evaluation mode
    model.eval()
    
    # Create calibration data loader if augmented data directory exists
    calibration_loader = None
    if os.path.exists(args.augmented_data_dir):
        print(f"Creating calibration data loader from {args.augmented_data_dir}...")
        try:
            # Find the most recent augmented dataset
            subdirs = [d for d in os.listdir(args.augmented_data_dir) if os.path.isdir(os.path.join(args.augmented_data_dir, d))]
            if subdirs:
                latest_dir = sorted(subdirs)[-1]
                augmented_data_path = os.path.join(args.augmented_data_dir, latest_dir)
                print(f"Using augmented data from: {augmented_data_path}")
                
                # Create data loaders
                train_loader, val_loader = create_augmented_dataloaders(
                    augmented_data_dir=augmented_data_path,
                    batch_size=args.batch_size,
                    val_split=0.2,
                    filter_valid=True,
                    shuffle=False  # No need to shuffle for calibration
                )
                
                # Use validation loader for calibration (smaller and more diverse)
                calibration_loader = val_loader
                print(f"Calibration loader created with {len(calibration_loader.dataset)} samples")
            else:
                print("No augmented data subdirectories found")
        except Exception as e:
            print(f"Error creating calibration loader: {e}")
            print("Continuing without calibration data...")
    else:
        print(f"Augmented data directory {args.augmented_data_dir} not found")
        print("Continuing without calibration data...")
    
    # Quantize model
    print(f"Quantizing model with backend: {args.backend}...")
    original_size = get_model_size_mb(model)
    
    quantized_model = quantize_model(
        model=model,
        calibration_data_loader=calibration_loader,
        backend=args.backend
    )
    
    quantized_size = get_model_size_mb(quantized_model)
    
    # Define output path
    model_name = os.path.splitext(os.path.basename(args.checkpoint))[0]
    output_path = os.path.join(args.output_dir, f"{model_name}_quantized.pth")
    
    # Save quantized model
    print(f"Saving quantized model to {output_path}...")
    torch.save(quantized_model.state_dict(), output_path)
    
    # Print size comparison
    print("\nModel Size Comparison:")
    print(f"Original model: {original_size:.2f} MB")
    print(f"Quantized model: {quantized_size:.2f} MB")
    print(f"Size reduction: {(1 - quantized_size/original_size) * 100:.2f}%")
    
    print("\nQuantization complete!")
    print(f"Quantized model saved to: {output_path}")
    
    # Additional information
    print("\nUsage Information:")
    print("1. Load the quantized model with:")
    print("   model = EnhancedChessBoardUNetV5_2(n_channels=3)")
    print("   model.load_state_dict(torch.load('path_to_quantized_model.pth'))")
    print("2. Make sure to set the correct quantization backend:")
    print(f"   torch.backends.quantized.engine = '{args.backend}'")
    print("3. For deployment on ARM devices (mobile), use 'qnnpack' backend")
    print("4. For deployment on x86 devices (desktop/server), use 'fbgemm' backend")


if __name__ == "__main__":
    main()
