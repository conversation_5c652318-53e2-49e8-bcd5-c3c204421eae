"""
Mobile-Optimized FEN Generation Script
Lightweight version for mobile deployment with optimized models:
- Uses FP16 V6 segmentation model
- Uses ONNX YOLO piece detection model
- Reduced memory footprint
- Faster inference on mobile devices
"""

import os
import cv2
import numpy as np
import torch
import onnxruntime as ort
import argparse
import time

# Import the V6 model architecture
from models.breakthrough_unet_v6_simple import get_breakthrough_v6_model

# Mobile-optimized configuration
MOBILE_CONFIG = {
    "v6_model": "breakthrough_v6_results/best_model_mobile_fp16.pth",
    "piece_model": "piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best_mobile.onnx",
    "fen_symbols": {
        "white_pawn": "P", "white_knight": "N", "white_bishop": "B", "white_rook": "R", "white_queen": "Q", "white_king": "K",
        "black_pawn": "p", "black_knight": "n", "black_bishop": "b", "black_rook": "r", "black_queen": "q", "black_king": "k"
    },
    "input_size": 512,
    "piece_input_size": 416
}

class MobileChessBoardDetector:
    """Mobile-optimized chess board detector using FP16 V6 model."""
    
    def __init__(self, model_path, device='cpu'):
        self.device = torch.device(device)
        self.model = self._load_model(model_path)
        
    def _load_model(self, model_path):
        """Load FP16 V6 model."""
        print(f"📱 Loading mobile V6 model: {model_path}")
        
        model = get_breakthrough_v6_model(base_channels=32)
        state_dict = torch.load(model_path, map_location=self.device, weights_only=True)
        model.load_state_dict(state_dict)
        model = model.to(self.device).half().eval()  # Use FP16
        
        print("✅ Mobile V6 model loaded successfully")
        return model
    
    def detect_board(self, image):
        """Detect chess board using mobile V6 model."""
        # Preprocess
        input_tensor = self._preprocess_image(image)
        
        # Inference
        with torch.no_grad():
            output = self.model(input_tensor)
            mask = torch.sigmoid(output).cpu().numpy()[0, 0]
        
        return mask
    
    def _preprocess_image(self, image):
        """Preprocess image for V6 model."""
        # Resize to model input size
        resized = cv2.resize(image, (MOBILE_CONFIG["input_size"], MOBILE_CONFIG["input_size"]))
        
        # Normalize
        normalized = resized.astype(np.float32) / 255.0
        
        # Convert to tensor
        tensor = torch.from_numpy(normalized.transpose(2, 0, 1)).unsqueeze(0)
        return tensor.to(self.device).half()  # Use FP16

class MobilePieceDetector:
    """Mobile-optimized piece detector using ONNX model."""
    
    def __init__(self, model_path):
        self.session = self._load_onnx_model(model_path)
        self.input_name = self.session.get_inputs()[0].name
        self.output_names = [output.name for output in self.session.get_outputs()]
        
    def _load_onnx_model(self, model_path):
        """Load ONNX model for mobile inference."""
        print(f"📱 Loading mobile ONNX model: {model_path}")
        
        # Configure ONNX Runtime for mobile
        providers = ['CPUExecutionProvider']  # Mobile-friendly provider
        session = ort.InferenceSession(model_path, providers=providers)
        
        print("✅ Mobile ONNX model loaded successfully")
        return session
    
    def detect_pieces(self, image):
        """Detect chess pieces using ONNX model."""
        # Preprocess
        input_tensor = self._preprocess_image(image)
        
        # Inference
        outputs = self.session.run(self.output_names, {self.input_name: input_tensor})
        
        # Parse YOLO outputs
        detections = self._parse_yolo_outputs(outputs, image.shape)
        return detections
    
    def _preprocess_image(self, image):
        """Preprocess image for YOLO ONNX model."""
        # Resize
        size = MOBILE_CONFIG["piece_input_size"]
        resized = cv2.resize(image, (size, size))
        
        # Normalize and transpose
        normalized = resized.astype(np.float32) / 255.0
        transposed = normalized.transpose(2, 0, 1)
        
        # Add batch dimension
        return np.expand_dims(transposed, axis=0)
    
    def _parse_yolo_outputs(self, outputs, original_shape):
        """Parse YOLO ONNX outputs to detections."""
        # This is a simplified parser - adjust based on your YOLO model output format
        detections = []
        
        # Extract predictions (adjust indices based on your model)
        predictions = outputs[0]  # Assuming first output contains predictions
        
        # Process predictions (simplified)
        for detection in predictions[0]:  # Batch dimension
            confidence = detection[4]
            if confidence > 0.5:  # Confidence threshold
                # Extract bounding box and class
                x, y, w, h = detection[:4]
                class_scores = detection[5:]
                class_id = np.argmax(class_scores)
                class_conf = class_scores[class_id]
                
                if class_conf > 0.4:  # Class confidence threshold
                    detections.append({
                        'bbox': np.array([x-w/2, y-h/2, x+w/2, y+h/2]),
                        'confidence': float(confidence * class_conf),
                        'class_id': int(class_id),
                        'class_name': self._get_class_name(class_id)
                    })
        
        return detections
    
    def _get_class_name(self, class_id):
        """Get class name from class ID."""
        class_names = [
            "white_pawn", "white_knight", "white_bishop", "white_rook", "white_queen", "white_king",
            "black_pawn", "black_knight", "black_bishop", "black_rook", "black_queen", "black_king"
        ]
        return class_names[class_id] if class_id < len(class_names) else "unknown"

def generate_fen_mobile(image_path, output_path=None):
    """Mobile-optimized FEN generation."""
    print("📱 MOBILE FEN GENERATION")
    print("=" * 40)
    
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"❌ Failed to load image: {image_path}")
        return None
    
    print(f"📸 Processing: {image_path}")
    
    # Initialize mobile detectors
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    board_detector = MobileChessBoardDetector(MOBILE_CONFIG["v6_model"], device)
    piece_detector = MobilePieceDetector(MOBILE_CONFIG["piece_model"])
    
    # Detect board
    start_time = time.time()
    board_mask = board_detector.detect_board(image)
    board_time = time.time() - start_time
    print(f"⏱️ Board detection: {board_time:.3f}s")
    
    # Extract board region (simplified)
    # In a full implementation, you'd use the mask to extract the board
    # For now, we'll use the full image
    board_image = image
    
    # Detect pieces
    start_time = time.time()
    pieces = piece_detector.detect_pieces(board_image)
    piece_time = time.time() - start_time
    print(f"⏱️ Piece detection: {piece_time:.3f}s")
    print(f"🎯 Detected {len(pieces)} pieces")
    
    # Generate FEN (simplified - you'd implement full grid mapping)
    fen = generate_simple_fen(pieces)
    
    print(f"📝 FEN: {fen}")
    print(f"⏱️ Total time: {board_time + piece_time:.3f}s")
    
    return fen

def generate_simple_fen(pieces):
    """Generate simplified FEN from detected pieces."""
    # This is a placeholder - implement full FEN generation logic
    # based on piece positions and grid mapping
    
    if not pieces:
        return "8/8/8/8/8/8/8/8"  # Empty board
    
    # For demo purposes, return a sample FEN
    return "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR"

def main():
    """Main mobile FEN generation function."""
    parser = argparse.ArgumentParser(description="Mobile Chess FEN Generation")
    parser.add_argument("image", help="Input chess board image")
    parser.add_argument("--output", help="Output file path")
    
    args = parser.parse_args()
    
    # Generate FEN
    fen = generate_fen_mobile(args.image, args.output)
    
    if fen:
        print("✅ Mobile FEN generation completed!")
        if args.output:
            with open(args.output, 'w') as f:
                f.write(fen)
            print(f"💾 FEN saved to: {args.output}")
    else:
        print("❌ FEN generation failed")

if __name__ == "__main__":
    main()
