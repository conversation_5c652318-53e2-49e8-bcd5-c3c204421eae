restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CameraScreen(
  stable onBackPressed: Function0<Unit>
  stable onImageCaptured: Function1<String, Unit>
  stable isProcessing: Boolean = @static false
)
fun rememberCameraStateManager()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CameraViewScreen(
  stable onBackPressed: Function0<Unit>
  stable onImageCaptured: Function1<String, Unit>
  stable isProcessing: Boolean = @static false
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoardGuide()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CornerGuide(
  stable modifier: Modifier? = @static Companion
  stable cornerSize: Dp
  stable cornerThickness: Dp
  stable cornerColor: Color
  stable isTopLeft: Boolean = @static false
  stable isTopRight: Boolean = @static false
  stable isBottomLeft: Boolean = @static false
  stable isBottomRight: Boolean = @static false
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun PhotoPreviewScreen(
  stable imagePath: String
  stable onBackToCamera: Function0<Unit>
  stable onConfirmImage: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CapturedImagePreview(
  stable imagePath: String
  stable modifier: Modifier? = @static Companion
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoard(
  stable modifier: Modifier? = @static Companion
  stable onFENChanged: Function1<String, Unit>? = @static { it: String ->

}

)
restartable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoardControls(
  unstable boardState: ChessBoardState
  stable modifier: Modifier? = @static Companion
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ControlButton(
  stable icon: ImageVector
  stable label: String
  stable isActive: Boolean
  stable onClick: Function0<Unit>
  stable color: Color = @static Color(4285961814L)
)
fun rememberChessBoardState()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun InteractiveChessBoard(
  stable modifier: Modifier? = @static Companion
  unstable boardState: ChessBoardState? = @dynamic rememberChessBoardState($composer, 0)
  stable onFENChanged: Function1<String, Unit>? = @static { it: String ->

}

)
restartable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoardGrid(
  unstable boardState: ChessBoardState
  stable modifier: Modifier? = @static Companion
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessSquare(
  stable position: ChessPosition
  stable piece: ChessPiece?
  stable isSelected: Boolean
  stable onClick: Function0<Unit>
  stable modifier: Modifier? = @static Companion
  unstable boardState: ChessBoardState? = @static null
  stable isLegalMove: Boolean = @static false
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun FENDisplay(
  stable fen: String
  stable onFenChanged: Function1<String, Unit>
)
restartable scheme("[androidx.compose.ui.UiComposable]") fun MainScreen(
  unstable chessAI: ChessAI
)
restartable scheme("[androidx.compose.ui.UiComposable]") fun MainHomeScreen(
  stable isVisible: Boolean
  stable isAIInitialized: Boolean
  unstable chessAI: ChessAI
  stable onNavigateToCamera: Function0<Unit>
  stable onNavigateToBoard: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessAppHeader()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun QuickActionsSection(
  stable onNavigateToCamera: Function0<Unit>
  stable onNavigateToBoard: Function0<Unit>
  stable isAIInitialized: Boolean
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessActionCard(
  stable title: String
  stable icon: ImageVector
  stable modifier: Modifier? = @static Companion
  stable onClick: Function0<Unit>
)
restartable scheme("[androidx.compose.ui.UiComposable]") fun FeaturesSection(
  unstable chessAI: ChessAI
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun FeatureItem(
  stable icon: String
  stable title: String
  stable description: String
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoardScreen(
  stable onBackPressed: Function0<Unit>
  stable initialFEN: String? = @static null
  stable onFENChanged: Function1<String, Unit>? = @static { it: String ->

}

)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ModernControlButton(
  stable icon: ImageVector
  stable label: String
  stable isActive: Boolean
  stable onClick: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun MainScreenPreview()
restartable scheme("[androidx.compose.ui.UiComposable]") fun PieceTray(
  unstable boardState: ChessBoardState
  stable modifier: Modifier? = @static Companion
)
restartable scheme("[androidx.compose.ui.UiComposable]") fun PieceTrayItem(
  stable piece: ChessPiece
  stable isSelected: Boolean
  stable onClick: Function0<Unit>
  unstable boardState: ChessBoardState
)
fun rememberAdaptiveLayout()
fun expressiveClickable(
  stable <this>: Modifier
  stable enabled: Boolean = @static true
  stable onClick: Function0<Unit>
): Modifier
fun breathingAnimation(
  stable <this>: Modifier
  stable enabled: Boolean = @static true
  stable minAlpha: Float = @static 0.6f
  stable maxAlpha: Float = @static 1.0f
  stable duration: Int = @static 2000
): Modifier
fun shimmerEffect(
  stable <this>: Modifier
  stable enabled: Boolean = @static true
  stable duration: Int = @static 1500
): Modifier
fun floatingAnimation(
  stable <this>: Modifier
  stable enabled: Boolean = @static true
  stable amplitude: Float = @static 2.0f
  stable duration: Int = @static 3000
): Modifier
restartable skippable scheme("[0, [0]]") fun ChessVisionAppTheme(
  stable darkTheme: Boolean = @dynamic isSystemInDarkTheme($composer, 0)
  stable dynamicColor: Boolean = @static true
  stable content: Function2<Composer, Int, Unit>
)
