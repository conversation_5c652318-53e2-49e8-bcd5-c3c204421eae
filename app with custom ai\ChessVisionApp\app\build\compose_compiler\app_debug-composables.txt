restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun MainScreen(
  chessAI: ChessAI
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun MainHomeScreen(
  stable isVisible: Boolean
  stable isAIInitialized: Boolean
  chessAI: ChessAI
  stable onNavigateToCamera: Function0<Unit>
  stable onNavigateToBoard: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessAppHeader()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun QuickActionsSection(
  stable onNavigateToCamera: Function0<Unit>
  stable onNavigateToBoard: Function0<Unit>
  stable isAIInitialized: Boolean
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessActionCard(
  stable title: String
  stable icon: ImageVector
  stable modifier: Modifier? = @static Companion
  stable onClick: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun FeaturesSection(
  chessAI: ChessAI
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun FeatureItem(
  stable icon: String
  stable title: String
  stable description: String
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoardScreen(
  stable onBackPressed: Function0<Unit>
  stable initialFEN: String? = @static null
  stable onFENChanged: Function1<String, Unit>? = @static { it: String ->

}

)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ModernControlButton(
  stable icon: ImageVector
  stable label: String
  stable isActive: Boolean
  stable onClick: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun MainScreenPreview()
