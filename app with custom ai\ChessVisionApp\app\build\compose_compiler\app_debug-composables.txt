restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CameraScreen(
  stable onBackPressed: Function0<Unit>
  stable onImageCaptured: Function1<String, Unit>
  stable isProcessing: Boolean = @static false
)
fun rememberCameraStateManager()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CameraViewScreen(
  stable onBackPressed: Function0<Unit>
  stable onImageCaptured: Function1<String, Unit>
  stable isProcessing: Boolean = @static false
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun ChessBoardGuide()
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CornerGuide(
  stable modifier: Modifier? = @static Companion
  stable cornerSize: Dp
  stable cornerThickness: Dp
  stable cornerColor: Color
  stable isTopLeft: Boolean = @static false
  stable isTopRight: Boolean = @static false
  stable isBottomLeft: Boolean = @static false
  stable isBottomRight: Boolean = @static false
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun PhotoPreviewScreen(
  stable imagePath: String
  stable onBackToCamera: Function0<Unit>
  stable onConfirmImage: Function0<Unit>
)
restartable skippable scheme("[androidx.compose.ui.UiComposable]") fun CapturedImagePreview(
  stable imagePath: String
  stable modifier: Modifier? = @static Companion
)
