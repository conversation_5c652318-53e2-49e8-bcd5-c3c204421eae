import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms as transforms
import cv2
import argparse
from matplotlib.widgets import Slider

# Add the parent directory to the path to import the model
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from chess_board_detection.models.unet import ChessBoardUNet

def load_model(model_path):
    """Load the chess board detection model"""
    # Initialize model
    model = ChessBoardUNet(n_channels=3, bilinear=True)

    # Load model weights
    checkpoint = torch.load(model_path, map_location=torch.device('cpu'))

    # Handle different checkpoint formats
    if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    else:
        model.load_state_dict(checkpoint)

    model.eval()

    return model

def preprocess_image(image_path, size=(256, 256)):
    """Preprocess the input image for the model"""
    # Load image
    image = Image.open(image_path).convert('RGB')

    # Original dimensions for later use
    original_width, original_height = image.size

    # Resize and convert to tensor
    transform = transforms.Compose([
        transforms.Resize(size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    input_tensor = transform(image).unsqueeze(0)  # Add batch dimension

    return input_tensor, (original_width, original_height), np.array(image)

def find_corners_from_heatmaps(heatmaps, threshold=0.5):
    """Extract corner coordinates and confidence values from heatmaps"""
    corners = []
    confidences = []

    for i in range(4):  # 4 corners
        heatmap = heatmaps[0, i].detach().numpy()

        # Find the maximum value location
        max_idx = np.argmax(heatmap)
        y, x = np.unravel_index(max_idx, heatmap.shape)
        confidence = heatmap[y, x]

        # Only consider it a valid corner if the confidence is above threshold
        if confidence > threshold:
            corners.append((x, y))
        else:
            corners.append(None)

        confidences.append(confidence)

    return corners, confidences

def visualize_results(original_image, mask, heatmaps, corners, confidences, output_path=None):
    """Visualize the segmentation mask, heatmaps, and detected corners with confidence values"""
    # Create two separate figures

    # Figure 1: Original, Mask, and Combined Heatmap
    plt.figure(figsize=(15, 5))

    # Original image
    plt.subplot(1, 3, 1)
    plt.imshow(original_image)
    plt.title('Original Image')
    plt.axis('off')

    # Segmentation mask
    plt.subplot(1, 3, 2)
    plt.imshow(mask, cmap='gray')
    plt.title('Segmentation Mask')
    plt.axis('off')

    # Combined heatmap
    plt.subplot(1, 3, 3)
    combined_heatmap = np.max(heatmaps[0].detach().numpy(), axis=0)
    plt.imshow(original_image)
    plt.imshow(combined_heatmap, alpha=0.5, cmap='hot')
    plt.title('Combined Heatmap')
    plt.axis('off')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path.replace('.png', '_part1.png'))

    # Figure 2: Individual corner heatmaps
    plt.figure(figsize=(15, 10))

    # Individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        plt.subplot(2, 2, i+1)
        plt.imshow(original_image)
        plt.imshow(heatmaps[0, i].detach().numpy(), alpha=0.5, cmap='hot')

        # Mark the detected corner if available
        if corners[i]:
            x, y = corners[i]
            plt.scatter(x, y, c='blue', s=50, marker='x')

        plt.title(f'{corner_names[i]} Corner (Conf: {confidences[i]:.4f})')
        plt.axis('off')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path.replace('.png', '_part2.png'))

    plt.show()

def interactive_threshold(heatmaps, original_image):
    """Interactive visualization with adjustable threshold"""
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))
    plt.subplots_adjust(bottom=0.15)  # Make room for the slider

    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    scatter_plots = []

    # Initial threshold
    initial_threshold = 0.3

    # Plot the heatmaps
    for i in range(4):
        row, col = i // 2, i % 2
        axs[row, col].imshow(original_image)
        axs[row, col].imshow(heatmaps[0, i].detach().numpy(), alpha=0.5, cmap='hot')
        axs[row, col].set_title(f'{corner_names[i]} Corner')
        axs[row, col].axis('off')

        # Initial corner detection
        heatmap = heatmaps[0, i].detach().numpy()
        max_idx = np.argmax(heatmap)
        y, x = np.unravel_index(max_idx, heatmap.shape)
        confidence = heatmap[y, x]

        # Only plot if above threshold
        if confidence > initial_threshold:
            scatter = axs[row, col].scatter(x, y, c='blue', s=50, marker='x')
            axs[row, col].set_title(f'{corner_names[i]} Corner (Conf: {confidence:.4f})')
        else:
            scatter = axs[row, col].scatter([], [], c='blue', s=50, marker='x')
            axs[row, col].set_title(f'{corner_names[i]} Corner (Below threshold)')

        scatter_plots.append(scatter)

    # Add a slider for threshold
    ax_threshold = plt.axes([0.25, 0.05, 0.65, 0.03])
    threshold_slider = Slider(
        ax=ax_threshold,
        label='Confidence Threshold',
        valmin=0.0,
        valmax=1.0,
        valinit=initial_threshold,
    )

    def update(val):
        threshold = threshold_slider.val

        for i in range(4):
            row, col = i // 2, i % 2
            heatmap = heatmaps[0, i].detach().numpy()
            max_idx = np.argmax(heatmap)
            y, x = np.unravel_index(max_idx, heatmap.shape)
            confidence = heatmap[y, x]

            if confidence > threshold:
                scatter_plots[i].set_offsets(np.array([[x, y]]))
                axs[row, col].set_title(f'{corner_names[i]} Corner (Conf: {confidence:.4f})')
            else:
                scatter_plots[i].set_offsets(np.array([[0, 0]]))  # Set to empty position
                scatter_plots[i].set_visible(False)  # Hide the scatter point
                axs[row, col].set_title(f'{corner_names[i]} Corner (Below threshold)')

            # Make sure it's visible if it should be
            scatter_plots[i].set_visible(confidence > threshold)

        fig.canvas.draw_idle()

    threshold_slider.on_changed(update)
    plt.show()

def main(args):
    # Load model
    model = load_model(args.model_path)

    # Preprocess image
    input_tensor, original_dims, original_image = preprocess_image(args.image_path)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Process outputs
    mask_pred = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    mask = torch.sigmoid(mask_pred).squeeze().detach().numpy() > 0.5

    # Find corners
    corners, confidences = find_corners_from_heatmaps(heatmaps, threshold=args.threshold)

    # Print corner coordinates and confidences
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    print("Detected corners:")
    for i, (corner, conf) in enumerate(zip(corners, confidences)):
        if corner:
            print(f"{corner_names[i]}: {corner} (Confidence: {conf:.4f})")
        else:
            print(f"{corner_names[i]}: Not detected (Confidence: {conf:.4f})")

    # Visualize
    if args.interactive:
        interactive_threshold(heatmaps, original_image)
    else:
        visualize_results(original_image, mask, heatmaps, corners, confidences, args.output_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Test chess board detection model')
    parser.add_argument('--model_path', type=str, required=True, help='Path to the model weights')
    parser.add_argument('--image_path', type=str, required=True, help='Path to the test image')
    parser.add_argument('--output_path', type=str, default=None, help='Path to save visualization')
    parser.add_argument('--threshold', type=float, default=0.3, help='Confidence threshold for corner detection')
    parser.add_argument('--interactive', action='store_true', help='Enable interactive threshold adjustment')

    args = parser.parse_args()
    main(args)
