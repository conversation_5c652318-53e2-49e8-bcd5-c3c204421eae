"""
Fine-tune a YOLOv11n model for chess piece detection with a focus on classification accuracy.
This script emphasizes classification loss to achieve perfect classification accuracy.
"""

import os
import sys
import argparse
import yaml
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from ultralytics import YOLO
from tqdm import tqdm

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def finetune_classification_focused(
    model_path,
    data_yaml,
    output_dir=None,
    epochs=15,
    batch_size=16,
    img_size=416,
    device='0',
    workers=4,
    patience=15,
    save_period=5,
    conf_threshold=0.001,
    iou_threshold=0.7,
    cls_loss_weight=5.0,  # Increased weight for classification loss
    box_loss_weight=1.0,  # Reduced weight for box loss
    dfl_loss_weight=1.0,  # Reduced weight for DFL loss
    seed=42
):
    """
    Fine-tune a YOLO model with a focus on classification accuracy.
    
    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        output_dir: Directory to save results
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or GPU device id)
        workers: Number of worker threads
        patience: Stop training if no improvement for this many epochs
        save_period: Save checkpoints every N epochs
        conf_threshold: Confidence threshold for training
        iou_threshold: IoU threshold for NMS
        cls_loss_weight: Weight for classification loss (higher to focus on classification)
        box_loss_weight: Weight for box loss
        dfl_loss_weight: Weight for DFL loss
        seed: Random seed for reproducibility
    """
    # Set random seed
    set_seed(seed)

    # Print system information
    print_system_info()
    print(f"Training on: {device}")

    if torch.cuda.is_available():
        device_info = torch.cuda.get_device_properties(0)
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {device_info.total_memory / 1e9:.2f} GB")

    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f'cls_focused_{timestamp}'

    # Create output directory
    if output_dir is None:
        output_dir = f"runs/cls_focused"

    run_dir = os.path.join(output_dir, run_name)
    os.makedirs(run_dir, exist_ok=True)

    # Save training configuration
    config = {
        "model_path": model_path,
        "data_yaml": data_yaml,
        "epochs": epochs,
        "batch_size": batch_size,
        "img_size": img_size,
        "device": device,
        "workers": workers,
        "patience": patience,
        "save_period": save_period,
        "conf_threshold": conf_threshold,
        "iou_threshold": iou_threshold,
        "cls_loss_weight": cls_loss_weight,
        "box_loss_weight": box_loss_weight,
        "dfl_loss_weight": dfl_loss_weight,
        "seed": seed,
        "timestamp": timestamp
    }

    with open(os.path.join(run_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)

    # Load model
    model = YOLO(model_path)

    # Train with focus on classification loss
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        imgsz=img_size,
        batch=batch_size,
        patience=patience,
        device=device,
        workers=workers,
        project=output_dir,
        name=run_name,
        exist_ok=True,
        pretrained=False,  # We're using a pre-trained model
        verbose=True,
        seed=seed,
        cache=True,
        close_mosaic=0,  # Disable mosaic completely for fine-tuning
        amp=True,  # Enable mixed precision
        # Minimal augmentation for fine-tuning
        augment=True,
        mosaic=0.0,  # Disable mosaic
        mixup=0.0,  # Disable mixup
        degrees=0.0,  # Disable rotation
        translate=0.05,  # Minimal translation
        scale=0.1,  # Minimal scaling
        shear=0.0,  # No shear
        fliplr=0.5,  # Keep horizontal flip
        perspective=0.0,  # No perspective
        # Learning rate settings - much lower for fine-tuning
        lr0=0.0005,  # Very low initial learning rate for fine-tuning
        lrf=0.0001,  # Very low final learning rate
        # Save checkpoints
        save_period=save_period,
        # Loss weights - emphasize classification much more
        box=box_loss_weight,
        cls=cls_loss_weight,  # Significantly increased class loss gain
        dfl=dfl_loss_weight,
        # Validation settings
        val=True,
        # NMS settings
        conf=conf_threshold,
        iou=iou_threshold
    )

    # Get the best model
    best_model_path = os.path.join(run_dir, 'weights', 'best.pt')
    final_model = YOLO(best_model_path)

    # Validate the final model
    print("\nValidating final model...")
    metrics = final_model.val(data=data_yaml, conf=0.7, iou=0.7)

    # Get validation results
    val_results = metrics.box

    # Extract metrics
    precision = val_results.precision
    recall = val_results.recall
    map50 = val_results.map50
    map = val_results.map

    # Print final metrics
    print("\nFinal Model Performance:")
    print(f"mAP50: {map50:.4f}")
    print(f"mAP50-95: {map:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")

    # Check if we achieved perfect classification
    perfect_classification = precision >= 0.99 and recall >= 0.99
    if perfect_classification:
        print("\n🎉 Perfect classification achieved!")
    else:
        print("\nClassification still needs improvement.")

    # Save metrics to file
    metrics_dict = {
        "map50": float(map50),
        "map": float(map),
        "precision": float(precision),
        "recall": float(recall),
        "perfect_classification": perfect_classification,
        "training_completed": True
    }

    # Save metrics to the appropriate directory
    metrics_path = os.path.join(run_dir, 'final_metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics_dict, f, indent=2)

    print(f"Training complete. Final model saved to {best_model_path}")
    return best_model_path, metrics_dict

def main():
    parser = argparse.ArgumentParser(description="Fine-tune chess piece detection model with focus on classification")
    parser.add_argument("--model", type=str, default="runs/detect/train/weights/best.pt", help="Path to pre-trained model")
    parser.add_argument("--data_yaml", type=str, default="chess_board_detection/piece_detection/targeted_dataset/dataset.yaml", help="Path to dataset YAML file")
    parser.add_argument("--output_dir", type=str, default=None, help="Directory to save results")
    parser.add_argument("--epochs", type=int, default=15, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16, help="Batch size")
    parser.add_argument("--img-size", type=int, default=416, help="Image size for training")
    parser.add_argument("--device", type=str, default="0", help="Device to train on ('cpu' or GPU device id)")
    parser.add_argument("--workers", type=int, default=4, help="Number of worker threads")
    parser.add_argument("--patience", type=int, default=15, help="Stop if no improvement for this many epochs")
    parser.add_argument("--save_period", type=int, default=5, help="Save checkpoints every N epochs")
    parser.add_argument("--cls_weight", type=float, default=5.0, help="Weight for classification loss")
    parser.add_argument("--box_weight", type=float, default=1.0, help="Weight for box loss")
    parser.add_argument("--dfl_weight", type=float, default=1.0, help="Weight for DFL loss")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")
    
    args = parser.parse_args()
    
    # Fine-tune model with focus on classification
    finetune_classification_focused(
        args.model,
        args.data_yaml,
        args.output_dir,
        args.epochs,
        args.batch,
        args.img_size,
        args.device,
        args.workers,
        args.patience,
        args.save_period,
        cls_loss_weight=args.cls_weight,
        box_loss_weight=args.box_weight,
        dfl_loss_weight=args.dfl_weight,
        seed=args.seed
    )

if __name__ == "__main__":
    main()
