"""
Test Improved Chess Piece Detection

This script:
1. Loads a trained YOLO model
2. Detects chess pieces in an image
3. Visualizes the results with improved mapping and visualization
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import time
from ultralytics import YOLO
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import models and utilities
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel
from chess_board_detection.utils.perspective_transform import get_perspective_transform

def load_segmentation_model(model_path):
    """Load the distilled segmentation model."""
    model = TinySegmentationModel(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for the segmentation model."""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    # Convert to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Store original size
    original_size = image.shape[:2]
    
    # Resize while maintaining aspect ratio
    h, w = original_size
    scale = min(target_size[0] / w, target_size[1] / h)
    
    new_w = int(w * scale)
    new_h = int(h * scale)
    
    resized = cv2.resize(image_rgb, (new_w, new_h), interpolation=cv2.INTER_AREA)
    
    # Create a black canvas of target size
    preprocessed = np.zeros((target_size[1], target_size[0], 3), dtype=np.uint8)
    
    # Paste the resized image
    offset_x = (target_size[0] - new_w) // 2
    offset_y = (target_size[1] - new_h) // 2
    
    preprocessed[offset_y:offset_y+new_h, offset_x:offset_x+new_w] = resized
    
    # Convert to tensor
    preprocessed_tensor = torch.from_numpy(preprocessed).permute(2, 0, 1).float() / 255.0
    preprocessed_tensor = preprocessed_tensor.unsqueeze(0)
    
    # Store preprocessing info for mapping back to original coordinates
    preprocess_info = {
        'original_size': (w, h),
        'resized_size': (new_w, new_h),
        'offset': (offset_x, offset_y),
        'scale': scale
    }
    
    return preprocessed_tensor, preprocessed, preprocess_info

def find_corners_from_segmentation(segmentation, threshold=0.5):
    """Find chess board corners from segmentation mask."""
    # Threshold segmentation
    binary = (segmentation > threshold).astype(np.uint8) * 255
    
    # Find contours
    contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # Find the largest contour
    largest_contour = max(contours, key=cv2.contourArea)
    
    # Approximate the contour to get corners
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx = cv2.approxPolyDP(largest_contour, epsilon, True)
    
    # We need exactly 4 corners
    if len(approx) != 4:
        # Try to find the 4 corners using a different approach
        rect = cv2.minAreaRect(largest_contour)
        box = cv2.boxPoints(rect)
        approx = np.int0(box)
    
    # Extract corners
    corners = [(point[0][0], point[0][1]) for point in approx]
    
    # Sort corners (top-left, top-right, bottom-right, bottom-left)
    corners = sort_corners(corners)
    
    return corners

def sort_corners(corners):
    """Sort corners in order: top-left, top-right, bottom-right, bottom-left."""
    # Calculate center
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)
    
    # Sort corners based on their position relative to center
    def get_angle(point):
        return np.arctan2(point[1] - center_y, point[0] - center_x)
    
    # Sort by angle
    sorted_corners = sorted(corners, key=get_angle)
    
    # Rotate so that top-left is first
    while get_angle(sorted_corners[0]) > -3*np.pi/4:
        sorted_corners = sorted_corners[1:] + [sorted_corners[0]]
    
    return sorted_corners

def extract_and_normalize_board(image, corners, output_size=(640, 640)):
    """Extract and normalize the chess board region."""
    # Convert corners to numpy array
    corners_np = np.array(corners, dtype=np.float32)
    
    # Define the destination points (normalized square)
    dst_points = np.array([
        [0, 0],
        [output_size[0], 0],
        [output_size[0], output_size[1]],
        [0, output_size[1]]
    ], dtype=np.float32)
    
    # Get perspective transform
    M = cv2.getPerspectiveTransform(corners_np, dst_points)
    
    # Apply perspective transform
    normalized = cv2.warpPerspective(image, M, output_size)
    
    return normalized

def detect_board(segmentation_model, image_path, output_size=(640, 640)):
    """
    Detect chess board in an image.
    
    Args:
        segmentation_model: The segmentation model
        image_path: Path to the input image
        output_size: Size of the normalized board image
        
    Returns:
        normalized_board: Normalized chess board image
        keypoints: Detected corner keypoints
        preprocess_info: Preprocessing information
    """
    # Preprocess image
    input_tensor, preprocessed_image, preprocess_info = preprocess_image(image_path)
    
    # Run inference
    with torch.no_grad():
        outputs = segmentation_model(input_tensor)
    
    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]
    
    # Find corners from segmentation
    keypoints = find_corners_from_segmentation(segmentation)
    
    if keypoints is None:
        print(f"No chess board detected in {image_path}")
        return None, None, None
    
    # Extract and normalize board
    normalized_board = extract_and_normalize_board(preprocessed_image, keypoints, output_size)
    
    return normalized_board, keypoints, preprocess_info

def detect_pieces(yolo_model, normalized_board, confidence=0.25):
    """
    Detect chess pieces in the normalized board image with improved handling.
    
    Args:
        yolo_model: The YOLO model
        normalized_board: Normalized chess board image
        confidence: Detection confidence threshold
        
    Returns:
        detections: List of detected pieces with coordinates and classes
    """
    # Convert to BGR for YOLO
    normalized_board_bgr = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)
    
    # Run inference
    results = yolo_model(normalized_board_bgr, conf=confidence)
    
    # Extract detections
    detections = []
    
    for result in results:
        boxes = result.boxes
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes.xyxy[i].tolist()
            confidence = boxes.conf[i].item()
            class_id = int(boxes.cls[i].item())
            class_name = result.names[class_id]
            
            # Calculate center point
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            detections.append({
                'class_id': class_id,
                'class_name': class_name,
                'confidence': confidence,
                'bbox': (x1, y1, x2, y2),
                'center': (center_x, center_y)
            })
    
    return detections

def map_to_chess_coordinates(detections, board_size=(640, 640)):
    """
    Map detected pieces to chess board coordinates (A1-H8) with improved handling.
    
    Args:
        detections: List of detected pieces with coordinates
        board_size: Size of the normalized board image
        
    Returns:
        chess_pieces: Dictionary mapping chess coordinates to piece names
    """
    chess_pieces = {}
    
    # Calculate square size
    square_width = board_size[0] / 8
    square_height = board_size[1] / 8
    
    # Create a grid to track multiple detections per square
    grid = {}
    
    for detection in detections:
        center_x, center_y = detection['center']
        
        # Calculate chess coordinates
        file_idx = int(center_x / square_width)
        rank_idx = int(center_y / square_height)
        
        # Ensure coordinates are within bounds
        file_idx = max(0, min(file_idx, 7))
        rank_idx = max(0, min(rank_idx, 7))
        
        # Convert to chess notation (A1-H8)
        file_letter = chr(ord('a') + file_idx)
        rank_number = 8 - rank_idx
        
        # Create chess coordinate
        chess_coord = f"{file_letter}{rank_number}"
        
        # Add to grid
        if chess_coord not in grid:
            grid[chess_coord] = []
        
        grid[chess_coord].append(detection)
    
    # For each square, select the detection with highest confidence
    for chess_coord, detections_in_square in grid.items():
        if not detections_in_square:
            continue
        
        # Sort by confidence (descending)
        detections_in_square.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Select the highest confidence detection
        best_detection = detections_in_square[0]
        
        chess_pieces[chess_coord] = {
            'piece': best_detection['class_name'],
            'confidence': best_detection['confidence'],
            'bbox': best_detection['bbox']
        }
    
    return chess_pieces

def visualize_detection(image_path, normalized_board, detections, chess_pieces, output_path):
    """
    Visualize the chess board detection and piece detection results with improved visualization.
    
    Args:
        image_path: Path to the input image
        normalized_board: Normalized chess board image
        detections: List of detected pieces
        chess_pieces: Dictionary mapping chess coordinates to piece names
        output_path: Path to save the visualization
    """
    # Create figure with 2x2 subplots
    fig, axs = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot original image
    original_image = cv2.imread(image_path)
    original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
    axs[0, 0].imshow(original_image)
    axs[0, 0].set_title('Original Image')
    axs[0, 0].axis('off')
    
    # Plot normalized board
    axs[0, 1].imshow(normalized_board)
    axs[0, 1].set_title('Normalized Chess Board')
    axs[0, 1].axis('off')
    
    # Plot detected pieces
    axs[1, 0].imshow(normalized_board)
    
    # Draw bounding boxes
    for detection in detections:
        x1, y1, x2, y2 = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Determine color based on piece color
        color = 'r' if 'white' in class_name else 'b'
        
        # Draw bounding box
        rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, linewidth=2, edgecolor=color, facecolor='none')
        axs[1, 0].add_patch(rect)
        
        # Add label
        axs[1, 0].text(x1, y1-5, f"{class_name.split('_')[1]}: {confidence:.2f}", color=color, fontsize=8)
    
    axs[1, 0].set_title(f'Detected Pieces ({len(detections)})')
    axs[1, 0].axis('off')
    
    # Plot chess position
    axs[1, 1].imshow(normalized_board)
    
    # Draw grid
    board_size = normalized_board.shape[:2]
    square_size = (board_size[1] / 8, board_size[0] / 8)
    
    for i in range(9):
        # Vertical lines
        axs[1, 1].axvline(x=i*square_size[0], color='w', linewidth=1)
        # Horizontal lines
        axs[1, 1].axhline(y=i*square_size[1], color='w', linewidth=1)
    
    # Add coordinates
    for i in range(8):
        # File labels (a-h)
        axs[1, 1].text((i+0.5)*square_size[0], board_size[0]-10, chr(ord('a')+i), 
                      ha='center', va='bottom', color='w', fontsize=10)
        # Rank labels (1-8)
        axs[1, 1].text(10, (i+0.5)*square_size[1], str(8-i), 
                      ha='left', va='center', color='w', fontsize=10)
    
    # Add piece labels
    for coord, piece_info in chess_pieces.items():
        file_letter = coord[0]
        rank_number = int(coord[1])
        
        file_idx = ord(file_letter) - ord('a')
        rank_idx = 8 - rank_number
        
        center_x = (file_idx + 0.5) * square_size[0]
        center_y = (rank_idx + 0.5) * square_size[1]
        
        piece_name = piece_info['piece']
        piece_color = 'white' if 'white' in piece_name else 'black'
        piece_type = piece_name.split('_')[1]
        
        # Add piece label
        axs[1, 1].text(center_x, center_y, piece_type[0].upper() if piece_color == 'white' else piece_type[0].lower(), 
                      ha='center', va='center', color='w' if piece_color == 'black' else 'k', 
                      fontsize=16, fontweight='bold', 
                      bbox=dict(facecolor='k' if piece_color == 'white' else 'w', alpha=0.7, boxstyle='round'))
    
    axs[1, 1].set_title('Chess Position')
    axs[1, 1].axis('off')
    
    # Add title
    plt.suptitle('Chess Piece Detection Results', fontsize=16)
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Test Improved Chess Piece Detection')
    parser.add_argument('--segmentation_model', type=str, 
                        default='chess_board_detection/models/segmentation_only/tiny_20250519_091307/best_model_dice.pth',
                        help='Path to segmentation model')
    parser.add_argument('--yolo_model', type=str, required=True,
                        help='Path to YOLO model')
    parser.add_argument('--input', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/outputs/improved_piece_detection',
                        help='Directory to save the output')
    parser.add_argument('--confidence', type=float, default=0.25,
                        help='Detection confidence threshold')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load models
    print(f"Loading segmentation model from {args.segmentation_model}")
    segmentation_model = load_segmentation_model(args.segmentation_model)
    
    print(f"Loading YOLO model from {args.yolo_model}")
    yolo_model = YOLO(args.yolo_model)
    
    # Start timing
    start_time = time.time()
    
    # Detect board
    normalized_board, corners, preprocess_info = detect_board(segmentation_model, args.input)
    
    if normalized_board is None:
        print(f"No chess board detected in {args.input}")
        return
    
    # Detect pieces
    detections = detect_pieces(yolo_model, normalized_board, args.confidence)
    
    # Map to chess coordinates
    chess_pieces = map_to_chess_coordinates(detections)
    
    # Calculate total time
    total_time = time.time() - start_time
    
    # Create output path
    base_name = os.path.splitext(os.path.basename(args.input))[0]
    output_path = os.path.join(args.output_dir, f"{base_name}_detection.png")
    
    # Visualize detection
    visualize_detection(args.input, normalized_board, detections, chess_pieces, output_path)
    
    # Print results
    print(f"Chess board and pieces detected in {total_time:.3f}s")
    print(f"Detected {len(detections)} pieces")
    print(f"Visualization saved to {output_path}")
    
    # Print detected pieces
    print("\nDetected pieces:")
    for coord, info in sorted(chess_pieces.items()):
        print(f"{coord}: {info['piece']} (confidence: {info['confidence']:.4f})")

if __name__ == "__main__":
    main()
