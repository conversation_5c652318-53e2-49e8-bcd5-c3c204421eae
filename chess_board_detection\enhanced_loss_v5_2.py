"""
Enhanced loss functions for chess board detection (v5.2).
This version includes specialized loss components to address the challenges identified in v5.1:
1. Balanced Peak-to-Second Ratio Loss with adaptive targets
2. Balanced Metric Weight Manager for harmonized optimization
3. Multi-Loss Convergence Scheduler for smooth convergence
4. Metric-Specific Learning Rate Adjustment for targeted improvement
5. Loss Component Balancer for preventing any single loss from dominating
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from enhanced_loss_v5 import EnhancedCornerFocusedHeatmapLossV5, RobustSegmentationGuidanceLoss, StabilizedGeometricLoss
from enhanced_loss_v4 import EnhancedDiceLoss


class MultiLossConvergenceScheduler:
    """
    Learning rate scheduler that ensures all loss components converge smoothly to near-zero.

    This scheduler:
    1. Tracks the convergence of multiple loss components
    2. Adjusts learning rate to ensure smooth convergence
    3. Prevents oscillations and divergence
    4. Provides detailed convergence analysis
    """
    def __init__(self, optimizer, total_epochs=80, initial_lr=0.001, min_lr=0.00005,
                 warmup_epochs=5, target_losses=None, loss_weights=None,
                 smoothing_factor=0.8, convergence_shape='exponential'):
        """
        Initialize the scheduler.

        Args:
            optimizer: PyTorch optimizer
            total_epochs: Total number of training epochs
            initial_lr: Initial learning rate
            min_lr: Minimum learning rate
            warmup_epochs: Number of warmup epochs
            target_losses: Dictionary of target values for each loss component
            loss_weights: Dictionary of weights for each loss component
            smoothing_factor: EMA smoothing factor (0-1)
            convergence_shape: Shape of convergence curve ('exponential', 'linear', or 'sigmoid')
        """
        self.optimizer = optimizer
        self.total_epochs = total_epochs
        self.initial_lr = initial_lr
        self.min_lr = min_lr
        self.warmup_epochs = warmup_epochs
        self.smoothing_factor = smoothing_factor
        self.convergence_shape = convergence_shape

        # Default target losses if not provided
        self.target_losses = target_losses or {
            'total_loss': 10.0,
            'segmentation_loss': 0.05,
            'heatmap_loss': 5.0,
            'geometric_loss': 0.1,
            'mse_loss': 0.1
        }

        # Default loss weights if not provided
        self.loss_weights = loss_weights or {
            'total_loss': 1.0,
            'segmentation_loss': 0.8,
            'heatmap_loss': 1.0,
            'geometric_loss': 0.5,
            'mse_loss': 0.7
        }

        # State variables
        self.current_epoch = 0
        self.loss_histories = {k: [] for k in self.target_losses}
        self.smoothed_losses = {k: None for k in self.target_losses}
        self.lr_history = []
        self.loss_velocities = {k: [] for k in self.target_losses}

        # Precompute ideal loss curves for each component
        self.ideal_loss_curves = {}
        for loss_name, target in self.target_losses.items():
            # Estimate initial loss based on v5.1 results
            if loss_name == 'total_loss':
                initial_estimate = 1500.0
            elif loss_name == 'segmentation_loss':
                initial_estimate = 0.3
            elif loss_name == 'heatmap_loss':
                initial_estimate = 1000.0
            elif loss_name == 'geometric_loss':
                initial_estimate = 1.0
            elif loss_name == 'mse_loss':
                initial_estimate = 0.7
            else:
                initial_estimate = 100.0  # Default for unknown loss types

            self.ideal_loss_curves[loss_name] = self._compute_ideal_curve(
                initial_estimate, target, self.convergence_shape)

    def _compute_ideal_curve(self, initial_value, target_value, shape='exponential'):
        """Compute ideal convergence curve from initial to target value"""
        curve = []

        # Warmup phase - slight decrease
        for i in range(self.warmup_epochs):
            factor = i / max(1, self.warmup_epochs)
            curve.append(initial_value * (1.0 - 0.1 * factor))

        # Main training phase
        remaining_epochs = self.total_epochs - self.warmup_epochs
        for i in range(remaining_epochs):
            progress = i / max(1, remaining_epochs)

            if shape == 'exponential':
                # Exponential decay (faster early, slower later)
                decay_factor = math.exp(-5 * progress)
                value = target_value + (initial_value - target_value) * decay_factor

            elif shape == 'linear':
                # Linear decay (constant rate)
                value = initial_value + progress * (target_value - initial_value)

            elif shape == 'sigmoid':
                # Sigmoid decay (slower early and late, faster in middle)
                sigmoid = 1.0 / (1.0 + math.exp(-10 * (progress - 0.5)))
                value = initial_value + sigmoid * (target_value - initial_value)

            else:  # Default to exponential
                decay_factor = math.exp(-5 * progress)
                value = target_value + (initial_value - target_value) * decay_factor

            curve.append(value)

        return curve

    def step(self, losses, metrics=None):
        """
        Update learning rate based on multiple loss components

        Args:
            losses: Dictionary of current loss values
            metrics: Optional dictionary of other metrics
        """
        # Record loss histories
        for loss_name, loss_value in losses.items():
            if loss_name in self.loss_histories:
                self.loss_histories[loss_name].append(loss_value)

                # Calculate smoothed loss using exponential moving average
                if self.smoothed_losses[loss_name] is None:
                    self.smoothed_losses[loss_name] = loss_value
                else:
                    self.smoothed_losses[loss_name] = (
                        self.smoothing_factor * self.smoothed_losses[loss_name] +
                        (1 - self.smoothing_factor) * loss_value
                    )

                # Calculate loss velocity (rate of change)
                if len(self.loss_histories[loss_name]) > 1:
                    velocity = (self.loss_histories[loss_name][-1] -
                               self.loss_histories[loss_name][-2])
                    self.loss_velocities[loss_name].append(velocity)

        # Get current learning rate
        current_lr = self.optimizer.param_groups[0]['lr']
        self.lr_history.append(current_lr)

        # Determine new learning rate for smooth convergence of all losses
        new_lr = self._calculate_multi_loss_lr()

        # Apply learning rate
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = new_lr

        self.current_epoch += 1
        return new_lr

    def _calculate_multi_loss_lr(self):
        """Calculate learning rate to ensure all loss components converge smoothly"""
        # During warmup phase
        if self.current_epoch < self.warmup_epochs:
            warmup_progress = (self.current_epoch + 1) / self.warmup_epochs
            return self.min_lr + (self.initial_lr - self.min_lr) * warmup_progress

        # After warmup phase
        progress = (self.current_epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)

        # Calculate convergence gap for each loss component
        convergence_gaps = {}
        weighted_gaps = []

        for loss_name, history in self.loss_histories.items():
            if not history:
                continue

            # Get ideal loss for current epoch
            if self.current_epoch < len(self.ideal_loss_curves[loss_name]):
                ideal_loss = self.ideal_loss_curves[loss_name][self.current_epoch]
                actual_loss = self.smoothed_losses[loss_name]

                # Calculate gap ratio (>1 means we're above ideal curve, <1 means below)
                gap_ratio = actual_loss / (ideal_loss + 1e-8)
                convergence_gaps[loss_name] = gap_ratio

                # Apply weight to this loss component
                weight = self.loss_weights.get(loss_name, 1.0)
                weighted_gaps.append((gap_ratio, weight))

        # Calculate weighted average gap
        if weighted_gaps:
            total_weight = sum(w for _, w in weighted_gaps)
            avg_gap = sum(gap * w for gap, w in weighted_gaps) / (total_weight + 1e-8)
        else:
            avg_gap = 1.0  # Default to no adjustment

        # Base learning rate follows a smooth decay curve
        # Cosine annealing with warm restarts
        cycle_progress = progress % 0.2  # 20% of training is one cycle
        normalized_cycle_progress = cycle_progress / 0.2
        cosine_factor = 0.5 * (1 + math.cos(math.pi * normalized_cycle_progress))

        # Decay the max learning rate over time
        cycle_number = int(progress / 0.2)
        max_lr_decay = 0.8 ** cycle_number

        # Calculate base learning rate
        base_lr = self.min_lr + (self.initial_lr - self.min_lr) * max_lr_decay * cosine_factor

        # Adjust learning rate based on convergence gap
        if avg_gap > 1.1:  # We're above ideal curve
            # Increase learning rate to catch up, but with a cap
            adjustment = min(1.3, 1.0 + 0.3 * (avg_gap - 1.0))
            new_lr = base_lr * adjustment
        elif avg_gap < 0.9:  # We're below ideal curve
            # Decrease learning rate to avoid overshooting
            adjustment = max(0.7, 1.0 - 0.3 * (1.0 - avg_gap))
            new_lr = base_lr * adjustment
        else:
            # We're close to ideal curve, use base learning rate
            new_lr = base_lr

        # Special handling for final 20% of training
        if progress > 0.8:
            # Ensure smooth final approach to target losses
            final_phase_progress = (progress - 0.8) / 0.2  # 0 to 1 in final phase
            final_lr = self.min_lr + (base_lr - self.min_lr) * (1 - final_phase_progress)
            new_lr = min(new_lr, final_lr)

            # If any loss component is far from target, slow down the decay
            max_gap = max(convergence_gaps.values()) if convergence_gaps else 1.0
            if max_gap > 2.0:
                # Increase learning rate for components that are far from target
                new_lr = max(new_lr, self.min_lr * 5)

        # Smooth learning rate changes to avoid sudden jumps
        if self.lr_history:
            prev_lr = self.lr_history[-1]
            max_change = prev_lr * 0.2  # Limit change to 20% of previous LR
            new_lr = max(prev_lr - max_change, min(prev_lr + max_change, new_lr))

        # Ensure learning rate stays within bounds
        new_lr = max(self.min_lr, min(self.initial_lr, new_lr))

        return new_lr

    def get_convergence_status(self):
        """Get detailed convergence status for all loss components"""
        status = {}

        for loss_name in self.loss_histories:
            if not self.loss_histories[loss_name]:
                continue

            current_loss = self.smoothed_losses[loss_name]
            target_loss = self.target_losses[loss_name]

            if self.current_epoch < len(self.ideal_loss_curves[loss_name]):
                ideal_loss = self.ideal_loss_curves[loss_name][self.current_epoch]
                # How close we are to ideal curve (1.0 is perfect)
                on_track_ratio = ideal_loss / (current_loss + 1e-8)
                # Clamp to reasonable range for display
                on_track_ratio = max(0.1, min(10.0, on_track_ratio))
            else:
                ideal_loss = target_loss
                on_track_ratio = 1.0

            # Calculate progress toward target
            progress = 1.0 - (current_loss - target_loss) / (self.loss_histories[loss_name][0] - target_loss + 1e-8)
            progress = max(0.0, min(1.0, progress))

            # Estimate epochs to target
            if len(self.loss_velocities[loss_name]) > 5:
                recent_velocities = self.loss_velocities[loss_name][-5:]
                avg_velocity = sum(recent_velocities) / len(recent_velocities)

                if avg_velocity < 0:  # Loss is decreasing
                    epochs_to_target = (current_loss - target_loss) / (-avg_velocity + 1e-8)
                else:
                    epochs_to_target = float('inf')
            else:
                epochs_to_target = None

            status[loss_name] = {
                'current': current_loss,
                'ideal': ideal_loss,
                'target': target_loss,
                'on_track_ratio': on_track_ratio,
                'progress': progress,
                'epochs_to_target': epochs_to_target
            }

        return status


class MetricSpecificLROptimizer:
    """
    Optimizer wrapper that applies different learning rates to model components
    based on their influence on specific metrics.

    This allows targeted optimization of components that affect underperforming metrics.

    Improved version includes:
    1. Per-layer learning rates for more granular control
    2. Cyclical learning rate with adaptive amplitude
    3. Layer-specific learning rate schedules based on convergence patterns
    """
    def __init__(self, model, base_optimizer, base_lr=0.001,
                 cycle_length=10, cycle_mult=1.0, min_lr_factor=0.1, max_lr_factor=2.0):
        self.model = model
        self.base_optimizer = base_optimizer
        self.base_lr = base_lr
        self.param_groups = base_optimizer.param_groups

        # Cyclical learning rate parameters
        self.cycle_length = cycle_length  # Length of one cycle in epochs
        self.cycle_mult = cycle_mult      # Multiplier for cycle length after each cycle
        self.min_lr_factor = min_lr_factor  # Minimum LR as factor of base_lr
        self.max_lr_factor = max_lr_factor  # Maximum LR as factor of base_lr
        self.current_epoch = 0
        self.current_cycle = 0
        self.cycle_position = 0

        # Adaptive amplitude parameters
        self.amplitude_factors = {}  # Layer-specific amplitude factors
        self.convergence_history = {}  # Track convergence for each layer
        self.oscillation_detection = {}  # Track oscillations for each layer

        # Map model components to metrics they influence
        self.component_to_metric_map = {
            'encoder': ['segmentation_loss', 'mse_loss'],
            'decoder': ['detection_rate'],
            'segmentation': ['segmentation_loss'],
            'heatmap': ['mse_loss', 'peak_to_second_ratio'],
            'peak_competition': ['peak_to_second_ratio'],
            'cross_attention': ['peak_to_second_ratio', 'segmentation_loss']
        }

        # More detailed layer mapping for finer control
        self.layer_to_metric_map = {
            'encoder.down1': ['segmentation_loss'],
            'encoder.down2': ['segmentation_loss', 'mse_loss'],
            'encoder.down3': ['segmentation_loss', 'mse_loss', 'peak_to_second_ratio'],
            'encoder.down4': ['segmentation_loss', 'mse_loss', 'peak_to_second_ratio'],
            'decoder.up1': ['detection_rate', 'peak_to_second_ratio'],
            'decoder.up2': ['detection_rate', 'peak_to_second_ratio'],
            'decoder.up3': ['detection_rate'],
            'decoder.up4': ['detection_rate'],
            'segmentation.conv': ['segmentation_loss'],
            'heatmap.conv': ['mse_loss', 'peak_to_second_ratio'],
            'peak_competition': ['peak_to_second_ratio'],
            'cross_attention': ['peak_to_second_ratio', 'segmentation_loss']
        }

        # Initialize parameter-specific learning rates
        self.param_lrs = {}
        self.param_history = {}  # Track parameter changes
        self.param_velocities = {}  # Track parameter update velocities

        for name, param in model.named_parameters():
            if param.requires_grad:
                self.param_lrs[name] = base_lr
                self.param_history[name] = []
                self.param_velocities[name] = 0.0
                self.amplitude_factors[name] = 1.0
                self.convergence_history[name] = []
                self.oscillation_detection[name] = []

    def update_metric_priorities(self, metrics, targets):
        """Update learning rates based on metric performance vs targets"""
        # Calculate metric priorities based on gap to target
        metric_priorities = {}
        for metric, value in metrics.items():
            if metric in targets:
                target = targets[metric]
                # Calculate normalized gap (0-1 range)
                if target > value:  # Higher is better
                    gap = (target - value) / target
                else:  # Lower is better
                    gap = (value - target) / target

                # Convert gap to priority (0-2 range, where 1 is neutral)
                metric_priorities[metric] = 1.0 + min(gap, 1.0)

        # Update parameter learning rates based on metric priorities and layer mapping
        for name, param in self.model.named_parameters():
            if not param.requires_grad:
                continue

            # Find which layer this parameter belongs to (more specific than component)
            layer = None
            for layer_name in self.layer_to_metric_map:
                if layer_name in name:
                    layer = layer_name
                    break

            # If no specific layer match, fall back to component mapping
            if not layer:
                for comp_name in self.component_to_metric_map:
                    if comp_name in name:
                        layer = comp_name
                        break

            if layer:
                # Get metrics influenced by this layer
                influenced_metrics = self.layer_to_metric_map.get(
                    layer, self.component_to_metric_map.get(layer, []))

                # Calculate average priority for this parameter
                priorities = [metric_priorities.get(metric, 1.0) for metric in influenced_metrics
                             if metric in metric_priorities]

                if priorities:
                    # Emphasize peak-to-second ratio
                    if 'peak_to_second_ratio' in influenced_metrics and 'peak_to_second_ratio' in metric_priorities:
                        p2s_priority = metric_priorities['peak_to_second_ratio']
                        priorities.append(p2s_priority)  # Add it twice for more weight

                    avg_priority = sum(priorities) / len(priorities)

                    # Adjust amplitude factor based on priority
                    self.amplitude_factors[name] = max(0.5, min(2.0, avg_priority))

                    # Calculate cyclical learning rate with adaptive amplitude
                    cycle_lr = self._calculate_cyclical_lr(name)

                    # Combine with priority-based adjustment
                    self.param_lrs[name] = cycle_lr * avg_priority

                    # Track parameter history for convergence analysis
                    if len(self.param_history[name]) > 10:
                        self.param_history[name].pop(0)

                    # Store a copy of the parameter data
                    self.param_history[name].append(param.data.mean().item())

                    # Detect oscillations and adjust amplitude if needed
                    self._detect_and_handle_oscillations(name)

        # Apply updated learning rates to optimizer
        self._apply_param_lrs()

        # Update cycle position for next epoch
        self._update_cycle_position()

    def _calculate_cyclical_lr(self, param_name):
        """Calculate cyclical learning rate with adaptive amplitude for a parameter"""
        # Calculate position in cycle (0 to 1)
        cycle_pos = self.cycle_position / self.cycle_length

        # Cosine annealing formula
        cosine_factor = 0.5 * (1 + np.cos(np.pi * cycle_pos))

        # Adaptive amplitude based on parameter-specific factor
        amplitude = self.base_lr * (self.max_lr_factor - self.min_lr_factor) * self.amplitude_factors[param_name]

        # Calculate learning rate
        lr = self.base_lr * self.min_lr_factor + amplitude * cosine_factor

        return lr

    def _detect_and_handle_oscillations(self, param_name):
        """Detect oscillations in parameter updates and adjust amplitude accordingly"""
        if len(self.param_history[param_name]) < 5:
            return

        # Calculate recent changes
        recent_changes = []
        for i in range(1, min(5, len(self.param_history[param_name]))):
            change = self.param_history[param_name][i] - self.param_history[param_name][i-1]
            recent_changes.append(change)

        # Check for sign changes (oscillations)
        sign_changes = 0
        for i in range(1, len(recent_changes)):
            if recent_changes[i] * recent_changes[i-1] < 0:
                sign_changes += 1

        # Record oscillation detection
        self.oscillation_detection[param_name].append(sign_changes)
        if len(self.oscillation_detection[param_name]) > 3:
            self.oscillation_detection[param_name].pop(0)

        # If consistent oscillations detected, reduce amplitude
        if len(self.oscillation_detection[param_name]) == 3 and sum(self.oscillation_detection[param_name]) >= 4:
            self.amplitude_factors[param_name] *= 0.8
            # Reset oscillation detection after adjustment
            self.oscillation_detection[param_name] = [0]

    def _update_cycle_position(self):
        """Update cycle position and handle cycle transitions"""
        self.cycle_position += 1
        self.current_epoch += 1

        # Check if we've completed a cycle
        if self.cycle_position >= self.cycle_length:
            self.current_cycle += 1
            self.cycle_position = 0

            # Increase cycle length if needed
            self.cycle_length = int(self.cycle_length * self.cycle_mult)

    def _apply_param_lrs(self):
        """Apply parameter-specific learning rates to optimizer"""
        # Group parameters by learning rate
        lr_groups = {}
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                lr = self.param_lrs.get(name, self.base_lr)
                if lr not in lr_groups:
                    lr_groups[lr] = []
                lr_groups[lr].append(param)

        # Create new param groups
        new_param_groups = [{'params': params, 'lr': lr} for lr, params in lr_groups.items()]

        # Replace optimizer's param_groups
        self.base_optimizer.param_groups = new_param_groups
        self.param_groups = self.base_optimizer.param_groups

    def get_lr_stats(self):
        """Get statistics about current learning rates"""
        lrs = list(self.param_lrs.values())
        return {
            'min_lr': min(lrs),
            'max_lr': max(lrs),
            'avg_lr': sum(lrs) / len(lrs),
            'cycle': self.current_cycle,
            'cycle_position': self.cycle_position,
            'cycle_length': self.cycle_length
        }

    def step(self):
        """Perform optimization step"""
        return self.base_optimizer.step()

    def zero_grad(self):
        """Zero gradients"""
        return self.base_optimizer.zero_grad()

    def state_dict(self):
        """Return the optimizer's state dict"""
        # Include our custom parameters in the state dict
        state_dict = self.base_optimizer.state_dict()
        state_dict['param_lrs'] = self.param_lrs
        state_dict['amplitude_factors'] = self.amplitude_factors
        state_dict['current_epoch'] = self.current_epoch
        state_dict['current_cycle'] = self.current_cycle
        state_dict['cycle_position'] = self.cycle_position
        state_dict['cycle_length'] = self.cycle_length
        state_dict['convergence_history'] = self.convergence_history
        state_dict['oscillation_detection'] = self.oscillation_detection
        state_dict['param_history'] = self.param_history
        state_dict['param_velocities'] = self.param_velocities
        return state_dict

    def load_state_dict(self, state_dict):
        """Load the optimizer's state dict"""
        # Extract our custom parameters
        param_lrs = state_dict.pop('param_lrs', None)
        if param_lrs is not None:
            self.param_lrs = param_lrs

        amplitude_factors = state_dict.pop('amplitude_factors', None)
        if amplitude_factors is not None:
            self.amplitude_factors = amplitude_factors

        current_epoch = state_dict.pop('current_epoch', None)
        if current_epoch is not None:
            self.current_epoch = current_epoch

        current_cycle = state_dict.pop('current_cycle', None)
        if current_cycle is not None:
            self.current_cycle = current_cycle

        cycle_position = state_dict.pop('cycle_position', None)
        if cycle_position is not None:
            self.cycle_position = cycle_position

        cycle_length = state_dict.pop('cycle_length', None)
        if cycle_length is not None:
            self.cycle_length = cycle_length

        convergence_history = state_dict.pop('convergence_history', None)
        if convergence_history is not None:
            self.convergence_history = convergence_history

        oscillation_detection = state_dict.pop('oscillation_detection', None)
        if oscillation_detection is not None:
            self.oscillation_detection = oscillation_detection

        param_history = state_dict.pop('param_history', None)
        if param_history is not None:
            self.param_history = param_history

        param_velocities = state_dict.pop('param_velocities', None)
        if param_velocities is not None:
            self.param_velocities = param_velocities

        # Load the base optimizer's state dict
        self.base_optimizer.load_state_dict(state_dict)


class LossComponentBalancer:
    """
    Dynamically balances loss components to ensure all converge smoothly.

    This helps prevent any single loss component from dominating the optimization.

    Improved version includes:
    1. Momentum-based weight adjustment to prevent oscillations
    2. Adaptive step sizes based on loss component sensitivity
    3. Pareto optimization for finding optimal weight balance
    """
    def __init__(self, loss_components=None, target_ratios=None, initial_weights=None,
                 momentum=0.9, min_step_size=0.01, max_step_size=0.2):
        """
        Initialize the balancer.

        Args:
            loss_components: List of loss component names
            target_ratios: Dictionary of target ratios between components
            initial_weights: Dictionary of initial weights for each component
            momentum: Momentum factor for weight updates (0-1)
            min_step_size: Minimum step size for weight adjustments
            max_step_size: Maximum step size for weight adjustments
        """
        # Default loss components if not provided
        self.loss_components = loss_components or [
            'total_loss', 'segmentation_loss', 'heatmap_loss',
            'geometric_loss', 'mse_loss'
        ]

        # Default target ratios if not provided (relative importance)
        self.target_ratios = target_ratios or {
            'segmentation_loss/total_loss': 0.05,  # Segmentation should be 5% of total
            'heatmap_loss/total_loss': 0.5,        # Heatmap should be 50% of total
            'geometric_loss/total_loss': 0.1,      # Geometric should be 10% of total
            'mse_loss/total_loss': 0.1            # MSE should be 10% of total
        }

        # Default initial weights if not provided
        self.weights = initial_weights or {
            'total_loss': 1.0,
            'segmentation_loss': 0.8,
            'heatmap_loss': 1.0,
            'geometric_loss': 0.5,
            'mse_loss': 0.7
        }

        # State variables
        self.loss_histories = {k: [] for k in self.loss_components}
        self.convergence_rates = {k: [] for k in self.loss_components}
        self.current_epoch = 0
        self.weight_history = {k: [] for k in self.weights}

        # Momentum parameters
        self.momentum = momentum
        self.weight_velocities = {k: 0.0 for k in self.weights}

        # Adaptive step size parameters
        self.min_step_size = min_step_size
        self.max_step_size = max_step_size
        self.step_sizes = {k: 0.05 for k in self.weights}  # Initial step size
        self.sensitivity = {k: [] for k in self.weights}  # Track sensitivity of each component

        # Pareto optimization parameters
        self.pareto_front = []  # Store Pareto-optimal weight configurations
        self.pareto_metrics = []  # Store corresponding metrics
        self.exploration_rate = 0.2  # Rate of exploration vs exploitation

    def update(self, current_losses):
        """
        Update component weights based on convergence rates with momentum and adaptive step sizes

        Args:
            current_losses: Dictionary of current loss values

        Returns:
            Dictionary of updated weights
        """
        # Record loss histories
        for component, value in current_losses.items():
            if component in self.loss_histories:
                self.loss_histories[component].append(value)

        # Calculate convergence rates for each component
        for component, history in self.loss_histories.items():
            if len(history) >= 5:  # Need at least 5 epochs to calculate rate
                # Calculate average rate of change over last 5 epochs
                recent = history[-5:]
                if recent[0] > 0:  # Avoid division by zero
                    # Normalized rate: negative means decreasing (good)
                    rate = (recent[-1] - recent[0]) / (recent[0] * 5)
                    self.convergence_rates[component].append(rate)

        # Check if we have enough data to balance components
        if all(len(rates) >= 3 for rates in self.convergence_rates.values()):
            self._balance_components_with_momentum()

        # Record weight history
        for component, weight in self.weights.items():
            self.weight_history[component].append(weight)

        # Update Pareto front every 5 epochs
        if self.current_epoch % 5 == 0 and self.current_epoch > 0:
            self._update_pareto_front(current_losses)

        self.current_epoch += 1
        return self.weights

    def _get_adaptive_step_size(self, component):
        """Calculate adaptive step size based on component sensitivity"""
        if component in self.sensitivity and len(self.sensitivity[component]) > 0:
            # Use recent sensitivity values
            recent_sensitivity = self.sensitivity[component][-min(5, len(self.sensitivity[component])):]
            avg_sensitivity = sum(recent_sensitivity) / len(recent_sensitivity)

            # Inverse relationship: higher sensitivity -> smaller step size
            step_size = 1.0 / (1.0 + avg_sensitivity)

            # Clamp to range
            step_size = max(self.min_step_size, min(self.max_step_size, step_size))

            # Update component step size
            self.step_sizes[component] = step_size

            return step_size
        else:
            # Return default step size if no sensitivity data available
            return self.step_sizes[component]

    def _update_pareto_front(self, current_losses):
        """Update the Pareto front of weight configurations"""
        # Create metric vector from current losses
        metric_vector = []
        for c in self.loss_components:
            if c in current_losses:
                metric_vector.append(current_losses[c])

        if not metric_vector:
            return

        metric_vector = np.array(metric_vector)

        # Create weight vector from current weights
        weight_vector = np.array([self.weights[c] for c in self.loss_components if c in self.weights])

        # Check if current configuration is Pareto-optimal
        is_pareto_optimal = True

        # Compare with existing Pareto front
        i = 0
        while i < len(self.pareto_metrics):
            existing_metrics = self.pareto_metrics[i]

            # Check if vectors are comparable (same length)
            if len(existing_metrics) == len(metric_vector):
                if np.all(existing_metrics <= metric_vector):
                    # Existing configuration dominates current one
                    is_pareto_optimal = False
                    break

                if np.all(metric_vector <= existing_metrics):
                    # Current configuration dominates existing one
                    # Remove dominated configuration
                    self.pareto_front.pop(i)
                    self.pareto_metrics.pop(i)
                    continue  # Don't increment i as we removed an element

            i += 1

        # Add current configuration to Pareto front if optimal
        if is_pareto_optimal:
            self.pareto_front.append(weight_vector.copy())
            self.pareto_metrics.append(metric_vector.copy())

            # Limit size of Pareto front
            if len(self.pareto_front) > 10:
                # Remove oldest entry
                self.pareto_front.pop(0)
                self.pareto_metrics.pop(0)

        # Occasionally use a Pareto-optimal configuration for exploration
        if np.random.random() < self.exploration_rate and self.pareto_front:
            # Select a random Pareto-optimal configuration
            idx = np.random.randint(0, len(self.pareto_front))
            pareto_weights = self.pareto_front[idx]

            # Apply these weights with some noise for exploration
            for i, component in enumerate(self.loss_components):
                if i < len(pareto_weights) and component in self.weights:
                    # Add some noise for exploration
                    noise = np.random.normal(0, 0.1)
                    self.weights[component] = max(0.1, pareto_weights[i] + noise)

            # Normalize weights
            total_weight = sum(self.weights.values())
            self.weights = {k: v / total_weight * len(self.weights)
                           for k, v in self.weights.items()}

    def _balance_components_with_momentum(self):
        """Balance component weights using momentum and adaptive step sizes"""
        # Calculate average convergence rate for each component
        avg_rates = {}
        for component, rates in self.convergence_rates.items():
            if rates:
                # Use recent rates (last 3)
                avg_rates[component] = sum(rates[-3:]) / 3

        if not avg_rates:
            return

        # Find the component with the worst (least negative) convergence rate
        worst_component = max(avg_rates.items(), key=lambda x: x[1])[0]
        worst_rate = avg_rates[worst_component]

        # Find the component with the best (most negative) convergence rate
        best_component = min(avg_rates.items(), key=lambda x: x[1])[0]
        best_rate = avg_rates[best_component]

        # Calculate weight adjustments with adaptive step sizes
        weight_adjustments = {k: 0.0 for k in self.weights}

        # Check if we need to balance (significant difference in rates)
        if worst_rate > best_rate * 0.5:  # If worst is converging less than half as fast
            # Calculate adaptive step size based on sensitivity
            step_size_worst = self._get_adaptive_step_size(worst_component)
            step_size_best = self._get_adaptive_step_size(best_component)

            # Increase weight for worst component
            weight_adjustments[worst_component] = step_size_worst * 1.2

            # Slightly decrease weight for best component to maintain balance
            weight_adjustments[best_component] = -step_size_best * 0.5

        # Check component ratios against target ratios
        for ratio_key, target in self.target_ratios.items():
            num, denom = ratio_key.split('/')
            if num in self.loss_histories and denom in self.loss_histories:
                if len(self.loss_histories[num]) > 0 and len(self.loss_histories[denom]) > 0:
                    # Calculate current ratio
                    current_ratio = (self.loss_histories[num][-1] /
                                    (self.loss_histories[denom][-1] + 1e-8))

                    # Get adaptive step sizes
                    step_size_num = self._get_adaptive_step_size(num)

                    # Adjust weights if ratio is far from target
                    if current_ratio > target * 1.5:  # Too high
                        weight_adjustments[num] -= step_size_num * 0.9  # Decrease numerator weight
                    elif current_ratio < target * 0.5:  # Too low
                        weight_adjustments[num] += step_size_num * 1.1  # Increase numerator weight

        # Apply momentum to weight adjustments
        for component in self.weights:
            # Update velocity with momentum
            self.weight_velocities[component] = (
                self.momentum * self.weight_velocities[component] +
                (1 - self.momentum) * weight_adjustments[component]
            )

            # Apply velocity to weights
            self.weights[component] += self.weight_velocities[component]

            # Ensure weights stay positive
            self.weights[component] = max(0.1, self.weights[component])

            # Update sensitivity based on weight change and effect on loss
            if len(self.loss_histories.get(component, [])) >= 2:
                weight_change = self.weight_history[component][-1] - self.weight_history[component][-2]
                loss_change = self.loss_histories[component][-1] - self.loss_histories[component][-2]

                if abs(weight_change) > 1e-6:  # Avoid division by zero
                    sensitivity = abs(loss_change / weight_change)
                    self.sensitivity[component].append(sensitivity)

        # Normalize weights to prevent overall scaling issues
        total_weight = sum(self.weights.values())
        self.weights = {k: v / total_weight * len(self.weights)
                       for k, v in self.weights.items()}

    def state_dict(self):
        """Return the balancer's state dict"""
        return {
            'weights': self.weights,
            'loss_histories': self.loss_histories,
            'convergence_rates': self.convergence_rates,
            'current_epoch': self.current_epoch,
            'weight_history': self.weight_history,
            'weight_velocities': self.weight_velocities,
            'step_sizes': self.step_sizes,
            'sensitivity': self.sensitivity,
            'pareto_front': self.pareto_front,
            'pareto_metrics': self.pareto_metrics
        }

    def load_state_dict(self, state_dict):
        """Load the balancer's state dict"""
        if 'weights' in state_dict:
            self.weights = state_dict['weights']
        if 'loss_histories' in state_dict:
            self.loss_histories = state_dict['loss_histories']
        if 'convergence_rates' in state_dict:
            self.convergence_rates = state_dict['convergence_rates']
        if 'current_epoch' in state_dict:
            self.current_epoch = state_dict['current_epoch']
        if 'weight_history' in state_dict:
            self.weight_history = state_dict['weight_history']
        if 'weight_velocities' in state_dict:
            self.weight_velocities = state_dict['weight_velocities']
        if 'step_sizes' in state_dict:
            self.step_sizes = state_dict['step_sizes']
        if 'sensitivity' in state_dict:
            self.sensitivity = state_dict['sensitivity']
        if 'pareto_front' in state_dict:
            self.pareto_front = state_dict['pareto_front']
        if 'pareto_metrics' in state_dict:
            self.pareto_metrics = state_dict['pareto_metrics']


class BalancedPeakToSecondRatioLoss(nn.Module):
    """
    Balanced peak-to-second ratio loss with adaptive targets and balanced penalties.
    Designed to improve peak-to-second ratio without sacrificing other metrics.
    """
    def __init__(self, target_ratio=1.8, min_ratio=1.2, max_ratio=2.2, weight=10.0):
        super().__init__()
        self.target_ratio = target_ratio
        self.min_ratio = min_ratio
        self.max_ratio = max_ratio
        self.weight = weight
        self.current_epoch = 0
        self.other_metrics = {}  # Will store other metrics for balance calculations

    def set_epoch(self, epoch):
        self.current_epoch = epoch

    def update_other_metrics(self, metrics_dict):
        """Update other metrics to enable balanced optimization"""
        self.other_metrics = metrics_dict

    def forward(self, heatmaps):
        batch_size, channels, h, w = heatmaps.shape
        loss = 0.0
        ratios = []

        for b in range(batch_size):
            for c in range(channels):
                hm = heatmaps[b, c].view(-1)

                # Find top two values
                values, _ = torch.topk(hm, k=2)
                if values.shape[0] < 2:
                    continue

                peak1, peak2 = values[0], values[1]
                current_ratio = peak1 / (peak2 + 1e-6)
                ratios.append(current_ratio.item())

                # Progressive target based on current performance and balance
                adaptive_target = self.calculate_adaptive_target(current_ratio)

                # Calculate loss with balanced penalty
                if current_ratio < adaptive_target:
                    # Need improvement - apply progressive penalty
                    ratio_gap = adaptive_target - current_ratio
                    # Use smoother penalty for better gradient flow
                    loss += torch.log1p(ratio_gap * 5) * 2.0
                elif current_ratio > self.max_ratio:
                    # Too high - apply penalty to prevent excessive values
                    excess = current_ratio - self.max_ratio
                    loss += torch.log1p(excess * 3) * 1.5

        avg_ratio = sum(ratios) / len(ratios) if ratios else 1.0

        # Apply dynamic weight based on balance with other metrics
        effective_weight = self.calculate_effective_weight(avg_ratio)

        return effective_weight * loss / (batch_size * channels), avg_ratio

    def calculate_adaptive_target(self, current_ratio):
        """Calculate adaptive target based on current performance and balance"""
        # Start with minimum target and gradually increase
        base_target = self.min_ratio + (self.target_ratio - self.min_ratio) * min(1.0, self.current_epoch / 25)

        # If other metrics are performing well, we can be more aggressive
        if self.other_metrics and 'peak_to_mean_ratio' in self.other_metrics:
            ptm_ratio = self.other_metrics['peak_to_mean_ratio']
            if ptm_ratio > 20.0:  # If peak-to-mean is very high
                # We can afford to focus more on peak-to-second
                base_target = min(base_target * 1.1, self.target_ratio)

        return base_target

    def calculate_effective_weight(self, current_ratio):
        """Calculate effective weight based on balance with other metrics"""
        # Start with base weight
        effective_weight = self.weight

        # If we're far from target, increase weight
        target_gap_percent = abs(current_ratio - self.target_ratio) / self.target_ratio
        if target_gap_percent > 0.3:  # More than 30% from target
            effective_weight *= 1.2

        # If other metrics are suffering, reduce weight
        if self.other_metrics:
            metrics_below_target = sum(1 for k, v in self.other_metrics.items()
                                     if k != 'peak_to_second_ratio' and v < 0.9)  # 90% of target
            if metrics_below_target > 1:  # If multiple metrics are below target
                effective_weight *= 0.9

        return effective_weight


class BalancedMetricWeightManager:
    """
    Manages weights for different metrics to ensure balanced improvement.
    Adjusts weights based on current performance and improvement velocity.
    """
    def __init__(self, target_metrics, initial_weights, min_weights=None, max_weights=None):
        self.target_metrics = target_metrics  # Dictionary of metric names to target values
        self.weights = initial_weights        # Dictionary of metric names to weights
        self.min_weights = min_weights or {k: 0.1 * v for k, v in initial_weights.items()}
        self.max_weights = max_weights or {k: 10.0 * v for k, v in initial_weights.items()}
        self.current_values = {k: 0.0 for k in target_metrics}
        self.improvement_velocity = {k: 0.0 for k in target_metrics}
        self.history = {k: [] for k in target_metrics}

    def update_metrics(self, current_values):
        # Calculate improvement velocity
        for k, v in current_values.items():
            if len(self.history[k]) > 0:
                prev_value = self.history[k][-1]
                target = self.target_metrics[k]
                # Normalize improvement relative to gap
                if target > prev_value:  # Higher is better
                    gap = max(0.001, target - prev_value)
                    self.improvement_velocity[k] = (v - prev_value) / gap
                else:  # Lower is better
                    gap = max(0.001, prev_value - target)
                    self.improvement_velocity[k] = (prev_value - v) / gap

            self.current_values[k] = v
            self.history[k].append(v)

    def adjust_weights(self):
        # Calculate gap to target for each metric
        gaps = {}
        for k, v in self.current_values.items():
            target = self.target_metrics[k]
            if target > v:  # Higher is better
                gaps[k] = (target - v) / target
            else:  # Lower is better
                gaps[k] = (v - target) / target

        # Normalize gaps
        total_gap = sum(gaps.values())
        if total_gap > 0:
            normalized_gaps = {k: v / total_gap for k, v in gaps.items()}
        else:
            normalized_gaps = {k: 1.0 / len(gaps) for k in gaps}

        # Adjust weights based on gaps and improvement velocity
        for k in self.weights:
            # If improving quickly, reduce weight; if improving slowly, increase weight
            velocity_factor = 1.0
            if self.improvement_velocity[k] > 0.1:
                velocity_factor = 0.9  # Reducing weight if improving well
            elif self.improvement_velocity[k] < 0.01:
                velocity_factor = 1.2  # Increasing weight if improving slowly

            # Adjust weight based on normalized gap and velocity
            self.weights[k] = self.weights[k] * (1.0 + normalized_gaps[k] * 0.5) * velocity_factor

            # Ensure weight stays within bounds
            self.weights[k] = max(self.min_weights[k], min(self.max_weights[k], self.weights[k]))

        # Normalize weights to prevent overall loss magnitude changes
        total_weight = sum(self.weights.values())
        self.weights = {k: v / total_weight * len(self.weights) for k, v in self.weights.items()}

        return self.weights

    def state_dict(self):
        """Return the weight manager's state dict"""
        return {
            'weights': self.weights,
            'current_values': self.current_values,
            'improvement_velocity': self.improvement_velocity,
            'history': self.history
        }

    def load_state_dict(self, state_dict):
        """Load the weight manager's state dict"""
        if 'weights' in state_dict:
            self.weights = state_dict['weights']
        if 'current_values' in state_dict:
            self.current_values = state_dict['current_values']
        if 'improvement_velocity' in state_dict:
            self.improvement_velocity = state_dict['improvement_velocity']
        if 'history' in state_dict:
            self.history = state_dict['history']


class EnhancedCornerFocusedHeatmapLossV5_2(nn.Module):
    """
    Enhanced loss function for corner heatmap prediction (v5.2).
    This improved version includes:
    1. Balanced Peak-to-Second Ratio Loss with adaptive targets
    2. Dynamic weight adjustment based on metric performance
    3. Enhanced integration with segmentation features
    4. Balanced component weights for harmonized optimization
    """
    def __init__(self,
                 separation_weight=1.0,
                 peak_separation_weight=0.8,
                 edge_suppression_weight=1.0,
                 peak_enhancement_weight=1.0,
                 peak_to_second_ratio_weight=10.0,
                 detection_rate_weight=5.0,
                 segmentation_guidance_weight=1.5):
        super(EnhancedCornerFocusedHeatmapLossV5_2, self).__init__()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight
        self.edge_suppression_weight = edge_suppression_weight
        self.peak_enhancement_weight = peak_enhancement_weight
        self.peak_to_second_ratio_weight = peak_to_second_ratio_weight
        self.detection_rate_weight = detection_rate_weight
        self.segmentation_guidance_weight = segmentation_guidance_weight
        self.current_epoch = 0

        # Enhanced components with improved parameters
        self.peak_to_second_ratio_loss = BalancedPeakToSecondRatioLoss(
            weight=1.0,
            target_ratio=1.8,
            min_ratio=1.2,
            max_ratio=2.2
        )

        self.segmentation_guidance_loss = RobustSegmentationGuidanceLoss(weight=1.0, boundary_width=5)
        self.eps = 1e-6

        # Initialize weight manager
        self.weight_manager = None

    def initialize_weight_manager(self, target_metrics):
        """Initialize the weight manager with target metrics"""
        initial_weights = {
            'separation_loss': self.separation_weight,
            'peak_separation_loss': self.peak_separation_weight,
            'edge_suppression_loss': self.edge_suppression_weight,
            'peak_enhancement_loss': self.peak_enhancement_weight,
            'peak_to_second_ratio_loss': self.peak_to_second_ratio_weight,
            'detection_rate_loss': self.detection_rate_weight,
            'segmentation_guidance_loss': self.segmentation_guidance_weight
        }

        # Make sure all target metrics are included in the history
        metrics_to_track = set(initial_weights.keys()) | set(target_metrics.keys())

        # Create a combined target metrics dictionary
        combined_metrics = {}
        for k in metrics_to_track:
            if k in target_metrics:
                combined_metrics[k] = target_metrics[k]
            else:
                # Default target values for loss components
                combined_metrics[k] = 0.1  # Default target for losses is low

        self.weight_manager = BalancedMetricWeightManager(
            target_metrics=combined_metrics,
            initial_weights=initial_weights
        )

    def set_epoch(self, epoch):
        """Update the current epoch for curriculum learning."""
        self.current_epoch = epoch
        self.peak_to_second_ratio_loss.set_epoch(epoch)

    def update_metrics(self, metrics_dict):
        """Update metrics for balanced optimization"""
        self.peak_to_second_ratio_loss.update_other_metrics(metrics_dict)
        if self.weight_manager:
            self.weight_manager.update_metrics(metrics_dict)

    def forward(self, pred_heatmaps, target_heatmaps, segmentation=None):
        """
        Calculate the loss between predicted and target heatmaps.

        Args:
            pred_heatmaps: Predicted heatmaps (B, 4, H, W)
            target_heatmaps: Target heatmaps (B, 4, H, W)
            segmentation: Optional segmentation mask (B, 1, H, W)
        """
        # Basic MSE loss
        mse_loss = F.mse_loss(pred_heatmaps, target_heatmaps)

        # Separation loss
        separation_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            # Find peak locations for all corners
            peak_locations = []
            for c in range(pred_heatmaps.size(1)):
                hm = pred_heatmaps[i, c]
                max_val, max_idx = torch.max(hm.view(-1), dim=0)
                y, x = max_idx // hm.size(1), max_idx % hm.size(1)
                peak_locations.append((y, x))

            # Calculate pairwise distances between peaks
            for c1 in range(pred_heatmaps.size(1)):
                for c2 in range(c1+1, pred_heatmaps.size(1)):
                    y1, x1 = peak_locations[c1]
                    y2, x2 = peak_locations[c2]

                    # Euclidean distance
                    distance = torch.sqrt(torch.pow(y1 - y2, 2) + torch.pow(x1 - x2, 2) + self.eps)

                    # Penalize if distance is too small (corners too close)
                    target_separation = torch.tensor(30.0, device=pred_heatmaps.device)
                    if distance < target_separation:
                        penalty = torch.pow(target_separation - distance, 2) / target_separation
                        separation_loss += penalty

        # Normalize separation loss
        separation_loss = separation_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) * (pred_heatmaps.size(1) - 1) / 2 + self.eps)

        # Peak separation loss
        peak_separation_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]
                max_val, _ = torch.max(heatmap.view(-1), dim=0)

                # Skip if max_val is too small
                if max_val < 0.1:
                    continue

                # Create a mask of values that are close to the maximum
                peak_mask = (heatmap > 0.5 * max_val).float()

                # Penalize if the peak area is too large
                peak_size = peak_mask.sum()
                ideal_peak_size = torch.tensor(9.0, device=pred_heatmaps.device)  # 3x3 peak
                peak_separation_loss += torch.abs(peak_size - ideal_peak_size) / (ideal_peak_size + self.eps)

        # Normalize peak separation loss
        peak_separation_loss = peak_separation_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Edge suppression loss
        edge_suppression_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]

                # Create edge mask (border of width 5)
                edge_mask = torch.zeros_like(heatmap)
                h, w = heatmap.shape
                edge_width = 5

                edge_mask[:edge_width, :] = 1.0  # Top
                edge_mask[-edge_width:, :] = 1.0  # Bottom
                edge_mask[:, :edge_width] = 1.0  # Left
                edge_mask[:, -edge_width:] = 1.0  # Right

                # Penalize activations along edges
                edge_activations = (heatmap * edge_mask).sum()
                edge_suppression_loss += edge_activations / (h * w + self.eps)

        # Normalize edge suppression loss
        edge_suppression_loss = edge_suppression_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Peak enhancement loss
        peak_enhancement_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]
                target = target_heatmaps[i, c]

                # Find the location of the maximum value in the target
                _, max_idx = torch.max(target.view(-1), dim=0)
                y, x = max_idx // target.size(1), max_idx % target.size(1)

                # Get the predicted value at the target peak location
                pred_at_peak = heatmap[y, x]

                # Penalize if the predicted value at the peak is too low
                target_peak_value = torch.tensor(1.0, device=pred_heatmaps.device)
                if pred_at_peak < target_peak_value:
                    peak_enhancement_loss += (target_peak_value - pred_at_peak) ** 2

        # Normalize peak enhancement loss
        peak_enhancement_loss = peak_enhancement_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Peak-to-second ratio loss
        peak_to_second_ratio_loss, avg_ratio = self.peak_to_second_ratio_loss(pred_heatmaps)

        # Detection rate loss
        detection_rate_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(pred_heatmaps.size(0)):
            for c in range(pred_heatmaps.size(1)):
                heatmap = pred_heatmaps[i, c]
                max_val = torch.max(heatmap)

                # Penalize if max_val is below detection threshold
                detection_threshold = torch.tensor(0.5, device=pred_heatmaps.device)
                if max_val < detection_threshold:
                    # Exponential penalty for more aggressive optimization
                    detection_rate_loss += torch.exp(detection_threshold - max_val) - 1.0

        # Normalize detection rate loss
        detection_rate_loss = detection_rate_loss / (pred_heatmaps.size(0) * pred_heatmaps.size(1) + self.eps)

        # Segmentation guidance loss
        segmentation_guidance_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        if segmentation is not None:
            segmentation_guidance_loss = self.segmentation_guidance_loss(pred_heatmaps, segmentation)

        # Adjust weights if weight manager is initialized
        if self.weight_manager:
            current_metrics = {
                'separation_loss': separation_loss.item(),
                'peak_separation_loss': peak_separation_loss.item(),
                'edge_suppression_loss': edge_suppression_loss.item(),
                'peak_enhancement_loss': peak_enhancement_loss.item(),
                'peak_to_second_ratio_loss': peak_to_second_ratio_loss if isinstance(peak_to_second_ratio_loss, float) else peak_to_second_ratio_loss.item(),
                'detection_rate_loss': detection_rate_loss.item(),
                'segmentation_guidance_loss': segmentation_guidance_loss.item() if segmentation is not None else 0.0
            }
            self.weight_manager.update_metrics(current_metrics)
            weights = self.weight_manager.adjust_weights()

            # Update weights
            self.separation_weight = weights['separation_loss']
            self.peak_separation_weight = weights['peak_separation_loss']
            self.edge_suppression_weight = weights['edge_suppression_loss']
            self.peak_enhancement_weight = weights['peak_enhancement_loss']
            self.peak_to_second_ratio_weight = weights['peak_to_second_ratio_loss']
            self.detection_rate_weight = weights['detection_rate_loss']
            self.segmentation_guidance_weight = weights['segmentation_guidance_loss']

        # Combine all losses with weights
        total_loss = (mse_loss +
                     self.separation_weight * separation_loss +
                     self.peak_separation_weight * peak_separation_loss +
                     self.edge_suppression_weight * edge_suppression_loss +
                     self.peak_enhancement_weight * peak_enhancement_loss +
                     self.peak_to_second_ratio_weight * peak_to_second_ratio_loss +
                     self.detection_rate_weight * detection_rate_loss +
                     self.segmentation_guidance_weight * segmentation_guidance_loss)

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("WARNING: NaN or Inf detected in loss calculation. Using fallback loss.")
            total_loss = mse_loss

        return total_loss, {
            'mse_loss': mse_loss.item(),
            'separation_loss': separation_loss.item(),
            'peak_separation_loss': peak_separation_loss.item(),
            'edge_suppression_loss': edge_suppression_loss.item(),
            'peak_enhancement_loss': peak_enhancement_loss.item(),
            'peak_to_second_ratio_loss': peak_to_second_ratio_loss if isinstance(peak_to_second_ratio_loss, float) else peak_to_second_ratio_loss.item(),
            'avg_peak_to_second_ratio': avg_ratio,
            'detection_rate_loss': detection_rate_loss.item(),
            'segmentation_guidance_loss': segmentation_guidance_loss.item() if segmentation is not None else 0.0
        }
