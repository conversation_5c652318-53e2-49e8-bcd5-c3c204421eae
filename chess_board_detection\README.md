# Chess Board Detection with U-Net for Custom Chessboards

This project implements a chess board detection system using a U-Net architecture, inspired by <PERSON><PERSON><PERSON>'s "Teaching AI to See" series. The system can detect chess boards in images, segment them, and identify their four corners using heatmaps.

## Project Structure

```
chess_board_detection/
├── config.py                  # Configuration settings
├── annotate_real_images.py    # Tool for annotating real chess board images
├── train_real.py              # Training script for real images
├── train_enhanced.py          # Enhanced training script with improved corner detection
├── inference_real.py          # Inference script for real images
├── inference_enhanced.py      # Enhanced inference script
├── run_enhanced_model.py      # Script to run the enhanced model
├── enhanced_loss.py           # Enhanced loss functions for corner detection
├── models/
│   ├── unet.py                # U-Net model architecture
│   └── enhanced_unet.py       # Enhanced U-Net with improved corner detection
├── utils/
│   ├── real_dataset.py        # Dataset handling utilities for real images
│   └── augmentation.py        # Data augmentation utilities
├── data/
│   ├── real/                  # Real chess board images
│   └── real_annotations.json  # Annotations for real images
└── models/                    # Saved model checkpoints
```

## Setup

1. Create a virtual environment and activate it:
   ```
   python -m venv venv
   venv\Scripts\activate  # Windows
   source venv/bin/activate  # Linux/Mac
   ```

2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```

## Working with Real Images

This project is designed to work with a small set of real images (10-20) of a specific chess board. The approach uses heatmaps to detect the four corners of the chess board, similar to Samobot's method in his videos.

### Step 1: Prepare Your Images

Place your real chess board images in the `data/real` directory. The images should show the chess board from various angles and under different lighting conditions.

### Step 2: Annotate Your Images

Run the annotation tool to mark the four corners of the chess board in each image:

```
python annotate_real_images.py
```

This will open a GUI where you can click on the four corners of the chess board in each image. The annotations will be saved to `data/real_annotations.json`.

### Step 3: Train the Model

#### Standard Training

Train the U-Net model on your annotated images:

```
python train_real.py
```

#### Enhanced Training (Recommended)

Train the enhanced U-Net model with improved corner detection:

```
python train_enhanced.py
```

Or use the simplified command:

```
python run_enhanced_model.py --mode train
```

This will:
1. Load your annotated real images
2. Train the enhanced U-Net model with corner-focused loss functions
3. Save the best model checkpoint to the `models/enhanced_model` directory
4. Generate training history plots

### Step 4: Run Inference

#### Standard Inference

To detect a chess board in a new image:

```
python inference_real.py --image path/to/image.jpg --output path/to/output.png
```

#### Enhanced Inference (Recommended)

To detect a chess board with the enhanced model:

```
python inference_enhanced.py --image_path path/to/image.jpg
```

Or use the simplified command:

```
python run_enhanced_model.py --mode test --test_image path/to/image.jpg
```

This will:
1. Load the trained enhanced model
2. Process the input image
3. Generate a segmentation mask and corner heatmaps
4. Find the peaks in the heatmaps to locate the corners
5. Save a visualization of the results

## Enhanced Model Features

The enhanced model includes several improvements over the standard model:

### 1. Improved Architecture

- **Attention Mechanism**: Helps the model focus on corner regions
- **Separate Corner Pathways**: Dedicated convolutional paths for each corner
- **Edge Suppression**: Explicitly suppresses activations along edges that aren't near corners

### 2. Enhanced Loss Functions

- **Corner-Focused Heatmap Loss**: Specifically designed to emphasize corner points rather than edges
- **Edge Suppression Loss**: Penalizes activations along edges that aren't near corners
- **Peak Enhancement Loss**: Encourages sharper peaks at corner locations
- **Geometric Consistency Loss**: Ensures the four corners form a proper quadrilateral

### 3. Advanced Data Augmentation

- **Corner-Focused Augmentations**: Perspective and distortion transforms that help the model generalize better
- **Enhanced Heatmap Generation**: Improved heatmap generation with edge suppression

## Model Architecture

The model is based on the U-Net architecture with two output heads:
1. A segmentation head that produces a binary mask of the chess board
2. A corner heatmap head that produces four heatmaps, one for each corner

The corner detection approach uses heatmaps instead of direct coordinate regression, which tends to work better for precise localization tasks, especially with limited training data.

## Tips for Best Results

- Use images of the same chess board you want to detect
- Include various angles and lighting conditions in your training set
- Make sure the corners are clearly visible in the training images
- If the model struggles with certain conditions, add more training examples of those conditions
- Use the enhanced model for better corner detection, especially when corners are near edges

## Troubleshooting

If the model is detecting the edges of the board rather than the corners:
1. Use the enhanced model with the corner-focused loss functions
2. Increase the `edge_suppression_weight` parameter in `train_enhanced.py`
3. Add more training examples with clear corners
4. Lower the detection threshold in `inference_enhanced.py` (e.g., `--threshold 0.05`)

## Acknowledgements

This project is inspired by Samobot's "Teaching AI to See" YouTube series, which demonstrates computer vision techniques for chess board detection. The heatmap-based corner detection approach is based on his implementation in Part 4 of the series.
