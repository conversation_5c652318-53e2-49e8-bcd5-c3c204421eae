<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="Theme.ChessVisionApp" parent="android:Theme.Material.NoActionBar">
        <!-- Primary brand color. -->
        <item name="android:colorPrimary">#1A1A2E</item>

        <!-- System bars - transparent for immersive experience -->
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>

        <!-- Window flags for immersive mode -->
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        <!-- Dark content for system bars -->
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>

        <!-- Enable immersive layout -->
        <item name="android:windowFullscreen">false</item>
        <item name="android:fitsSystemWindows">false</item>

        <!-- Hide navigation bar by default -->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
