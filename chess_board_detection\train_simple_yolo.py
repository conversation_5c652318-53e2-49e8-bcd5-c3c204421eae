import os
import sys
import torch
import argparse
from datetime import datetime
from ultralytics import YOL<PERSON>
from pathlib import Path

# Import the callback directly
from best_epoch_callback import Best<PERSON><PERSON>ch<PERSON>ogger

def print_system_info():
    """Print system information for debugging and record keeping."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        if hasattr(torch, 'backends') and hasattr(torch.backends, 'cudnn'):
            print(f"cuDNN Version: {torch.backends.cudnn.version()}")

    if torch.cuda.is_available():
        device = torch.cuda.current_device()
        print(f"Training on: {device}")
        print(f"GPU: {torch.cuda.get_device_name(device)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(device).total_memory / 1e9:.2f} GB")
    print()

def train_simple_model(dataset_path, model_name="yolo11n.pt", epochs=100, patience=20,
                  target_map50=0.99, target_precision=0.99, target_recall=0.99):
    """
    Train a simple YOLO model without complex augmentation.

    Args:
        dataset_path: Path to the dataset YAML file
        model_name: Name of the model to use (default: yolo11n.pt)
        epochs: Maximum number of epochs to train
        patience: Number of epochs to wait for improvement before early stopping
        target_map50: Target mAP50 value to achieve
        target_precision: Target precision value to achieve
        target_recall: Target recall value to achieve
    """
    # Create timestamp for unique run name
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f"simple_{timestamp}"

    # Set up project directory
    project_dir = os.path.join("piece_detection", "models", "simple_yolo")
    os.makedirs(project_dir, exist_ok=True)

    # Print system information
    print_system_info()

    print(f"=== Training Simple Model (max {epochs} epochs, no phase system) ===")

    # Load the model
    model = YOLO(model_name)

    # Create our custom logger for best epoch tracking
    best_epoch_logger = BestEpochLogger()

    # Define a custom callback function that will be registered with the YOLO model
    def custom_on_fit_epoch_end(trainer):
        best_epoch_logger.on_train_epoch_end(trainer)

    def custom_on_train_start(trainer):
        best_epoch_logger.on_train_start(trainer)

    def custom_on_train_end(trainer):
        best_epoch_logger.on_train_end(trainer)

    # Register the callbacks with the YOLO model
    try:
        model.add_callback("on_train_start", custom_on_train_start)
        model.add_callback("on_fit_epoch_end", custom_on_fit_epoch_end)  # This is the correct event name in newer versions
        model.add_callback("on_train_end", custom_on_train_end)
        print("Custom callbacks registered successfully")
    except Exception as e:
        print(f"Warning: Could not register callbacks: {e}")
        print("Training will continue without detailed best epoch logging")

    # Initial training parameters
    train_params = {
        "data": dataset_path,
        "epochs": epochs,
        "patience": patience,
        "imgsz": 416,
        "batch": 16,
        "device": 0,
        "project": project_dir,
        "name": run_name,
        "exist_ok": True,
        "pretrained": True,
        "verbose": True,
        "deterministic": True,
        "seed": 42,

        # Disable most augmentations
        "augment": False,
        "mosaic": 0,
        "mixup": 0,
        "copy_paste": 0,
        "degrees": 0,
        "translate": 0,
        "scale": 1.0,
        "shear": 0,
        "perspective": 0,
        "flipud": 0,
        "fliplr": 0,
        "hsv_h": 0,
        "hsv_s": 0,
        "hsv_v": 0,

        # Early stopping and callbacks
        "save_period": 5,

        # Optimizer settings
        "optimizer": "AdamW",
        "lr0": 0.001,
        "lrf": 0.01,
        "momentum": 0.9,
        "weight_decay": 0.0005,
        "warmup_epochs": 3.0,
        "warmup_momentum": 0.8,
        "warmup_bias_lr": 0.1,

        # Detection settings
        "conf": 0.001,
        "iou": 0.7,
        "max_det": 300,

        # Validation settings
        "val": True,
        "save": True,
    }

    # Configure training parameters - minimal augmentation
    results = model.train(**train_params)

    # Get metrics after initial training
    metrics = results.results_dict
    best_map50 = metrics.get('metrics/mAP50(B)', 0)
    best_precision = metrics.get('metrics/precision(B)', 0)
    best_recall = metrics.get('metrics/recall(B)', 0)
    best_epoch = metrics.get('best/epoch', 0)

    # Print results and compare with targets
    print("\n" + "="*50)
    print("=== Training Results After 100 Epochs ===")
    print(f"Best mAP50: {best_map50:.4f} (Target: {target_map50:.4f})")
    print(f"Best Precision: {best_precision:.4f} (Target: {target_precision:.4f})")
    print(f"Best Recall: {best_recall:.4f} (Target: {target_recall:.4f})")
    print(f"Best achieved at epoch: {best_epoch}")

    # Check if targets are met
    targets_met = (
        best_map50 >= target_map50 and
        best_precision >= target_precision and
        best_recall >= target_recall
    )

    if targets_met:
        print("\nAll targets have been met! 🎉")
    else:
        print("\nSome targets have not been met yet:")
        if best_map50 < target_map50:
            print(f"- mAP50: {best_map50:.4f} < {target_map50:.4f}")
        if best_precision < target_precision:
            print(f"- Precision: {best_precision:.4f} < {target_precision:.4f}")
        if best_recall < target_recall:
            print(f"- Recall: {best_recall:.4f} < {target_recall:.4f}")

    # Ask user if they want to continue training
    user_input = input("\nWould you like to continue training for 10 more epochs? (y/n): ").strip().lower()

    # If user wants to continue, train for 10 more epochs
    if user_input == 'y' or user_input == 'yes':
        print("\n=== Continuing Training for 10 More Epochs ===")

        # Load the best model from the previous training
        best_model_path = os.path.join(project_dir, run_name, "weights", "best.pt")
        print(f"Loading best model from: {best_model_path}")
        continuation_model = YOLO(best_model_path)

        # Create a new run name for continuation
        continuation_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        continuation_run_name = f"{run_name}_continued_{continuation_timestamp}"

        # Create our custom logger for best epoch tracking
        continuation_logger = BestEpochLogger()

        # Define a custom callback function that will be registered with the YOLO model
        def custom_on_fit_epoch_end(trainer):
            continuation_logger.on_train_epoch_end(trainer)

        def custom_on_train_start(trainer):
            continuation_logger.on_train_start(trainer)

        def custom_on_train_end(trainer):
            continuation_logger.on_train_end(trainer)

        # Register the callbacks with the YOLO model
        try:
            continuation_model.add_callback("on_train_start", custom_on_train_start)
            continuation_model.add_callback("on_fit_epoch_end", custom_on_fit_epoch_end)
            continuation_model.add_callback("on_train_end", custom_on_train_end)
            print("Custom callbacks registered successfully for continuation")
        except Exception as e:
            print(f"Warning: Could not register callbacks for continuation: {e}")
            print("Continuation training will proceed without detailed best epoch logging")

        # Update training parameters for continuation
        continuation_params = train_params.copy()
        continuation_params.update({
            "epochs": 10,  # Train for 10 more epochs
            "name": continuation_run_name,
            "resume": False,  # Don't resume training, start fresh with the best model
        })

        # Train for 10 more epochs
        continuation_results = continuation_model.train(**continuation_params)

        # Get final metrics after continuation
        continuation_metrics = continuation_results.results_dict
        final_map50 = continuation_metrics.get('metrics/mAP50(B)', 0)
        final_precision = continuation_metrics.get('metrics/precision(B)', 0)
        final_recall = continuation_metrics.get('metrics/recall(B)', 0)
        final_best_epoch = continuation_metrics.get('best/epoch', 0)

        print("\n=== Final Training Results After Continuation ===")
        print(f"Best mAP50: {final_map50:.4f} (Target: {target_map50:.4f})")
        print(f"Best Precision: {final_precision:.4f} (Target: {target_precision:.4f})")
        print(f"Best Recall: {final_recall:.4f} (Target: {target_recall:.4f})")
        print(f"Best achieved at epoch: {final_best_epoch}")

        # Return the continuation results
        return continuation_results
    else:
        print("\nTraining completed without continuation.")

        # Print final metrics
        print("\n=== Final Training Results ===")
        print(f"Best mAP50: {best_map50:.4f}")
        print(f"Best mAP50-95: {metrics.get('metrics/mAP50-95(B)', 'N/A'):.4f}")
        print(f"Best Precision: {best_precision:.4f}")
        print(f"Best Recall: {best_recall:.4f}")
        print(f"Best achieved at epoch: {best_epoch}")

        return results

def main():
    parser = argparse.ArgumentParser(description="Train a simple YOLO model for chess piece detection")
    parser.add_argument("--dataset", type=str, default="../chess_board_detection/piece_detection/enhanced_dataset_99plus/dataset.yaml",
                        help="Path to dataset YAML file")
    parser.add_argument("--model", type=str, default="yolo11n.pt",
                        help="Model to use for training")
    parser.add_argument("--epochs", type=int, default=100,
                        help="Maximum number of epochs to train")
    parser.add_argument("--patience", type=int, default=20,
                        help="Number of epochs to wait for improvement before early stopping")
    parser.add_argument("--target-map50", type=float, default=0.99,
                        help="Target mAP50 value to achieve")
    parser.add_argument("--target-precision", type=float, default=0.99,
                        help="Target precision value to achieve")
    parser.add_argument("--target-recall", type=float, default=0.99,
                        help="Target recall value to achieve")

    args = parser.parse_args()

    train_simple_model(
        dataset_path=args.dataset,
        model_name=args.model,
        epochs=args.epochs,
        patience=args.patience,
        target_map50=args.target_map50,
        target_precision=args.target_precision,
        target_recall=args.target_recall
    )

if __name__ == "__main__":
    main()
