# GPU Training for Chess Piece Detection

This guide explains how to train the chess piece detection model using your RTX 3050 laptop GPU for significantly faster training and better results.

## Prerequisites

- NVIDIA RTX 3050 laptop GPU
- CUDA 12.1 and cuDNN
- PyTorch with CUDA support
- Ultralytics package

## Setup Instructions

### 1. Install PyTorch with CUDA Support

```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
```

This installs PyTorch with CUDA 12.1 support, which is compatible with your RTX 3050.

### 2. Install Ultralytics

```bash
pip install ultralytics
```

### 3. Verify GPU Setup

Run the verification script to ensure your GPU is properly configured:

```bash
python chess_board_detection/piece_detection/verify_gpu.py
```

This will check:
- If CUDA is available
- GPU specifications
- PyTorch and Ultralytics installation
- Estimated performance for YOLO training

## Training with Extended Epochs

The GPU training script is designed to use the speed advantage of your GPU to train for more epochs, resulting in a better model.

### Basic Training Command

```bash
python chess_board_detection/piece_detection/train_gpu.py \
    --model yolo11n.pt \
    --input_dir "chess_board_detection/piece_detection/dataset/images" \
    --output_dir "chess_board_detection/piece_detection/dataset_split" \
    --epochs 800 \
    --batch 16 \
    --device 0
```

### Key Parameters

- `--model`: Path to pre-trained model (default: "yolo11n.pt")
- `--input_dir`: Directory containing labeled images
- `--output_dir`: Directory to save split dataset
- `--epochs`: Number of training epochs (default: 800)
- `--batch`: Batch size (default: 16)
- `--img-size`: Image size for training (default: 640)
- `--device`: Device to train on ('0' for first GPU, 'cpu' for CPU)
- `--workers`: Number of worker threads (default: 4)
- `--patience`: Early stopping patience (default: 100)
- `--save_period`: Save checkpoints every N epochs (default: 100)

## Advanced Features

The GPU training script includes several advanced features:

### 1. Extended Training

Training for 800 epochs instead of 100 (typical for CPU) to achieve better model quality in the same timeframe.

### 2. Mixed Precision Training

Uses FP16 computation to accelerate training without sacrificing accuracy.

```python
model.train(amp=True)  # Enable mixed precision
```

### 3. Strong Data Augmentation

Implements extensive data augmentation to prevent overfitting with limited data:

```python
model.train(
    augment=True,
    mosaic=1.0,
    mixup=0.5,
    degrees=15.0,
    translate=0.2,
    scale=0.5,
    shear=2.0,
    fliplr=0.5,
    perspective=0.0005
)
```

### 4. Checkpoint Ensemble

Creates an ensemble model from checkpoints saved during training for better performance:

```python
create_ensemble(checkpoints_dir, ensemble_path)
```

### 5. ONNX Export

Automatically exports the trained model to ONNX format for deployment:

```python
model.export(format='onnx', dynamic=True, simplify=True)
```

## Performance Expectations

With your RTX 3050 laptop GPU:

| Metric | Value |
|--------|-------|
| Training Time (800 epochs) | ~5-8 hours |
| Batch Size | 16 |
| Image Size | 640×640 |
| GPU Memory Usage | ~3-4 GB |
| Expected mAP | 90-95% |

## Monitoring Training

During training, you can monitor progress through:

1. **Console Output**: Shows loss, mAP, and other metrics for each epoch
2. **TensorBoard**: Visualize training metrics in real-time

To view TensorBoard:

```bash
pip install tensorboard
tensorboard --logdir chess_board_detection/piece_detection/models
```

## Troubleshooting

### Out of Memory Errors

If you encounter CUDA out of memory errors:

1. Reduce batch size: `--batch 8` or `--batch 4`
2. Reduce image size: `--img-size 416`
3. Reduce workers: `--workers 2`

### Training Too Slow

If training is slower than expected:

1. Check GPU utilization with Task Manager
2. Ensure power settings are set to "High Performance"
3. Close other GPU-intensive applications
4. Check for thermal throttling

### Model Not Improving

If the model's performance plateaus:

1. Increase patience: `--patience 200`
2. Adjust learning rate schedule
3. Try different augmentation settings
4. Consider adding more labeled data

## Next Steps After Training

After training completes:

1. Evaluate the model on test images
2. Compare performance with CPU-trained model
3. Deploy the ONNX model for inference
4. Integrate with the chess board detection pipeline

## Example Workflow

```bash
# 1. Verify GPU setup
python chess_board_detection/piece_detection/verify_gpu.py

# 2. Train model with extended epochs
python chess_board_detection/piece_detection/train_gpu.py --model yolo11n.pt --epochs 800

# 3. Test the trained model
python chess_board_detection/piece_detection/detect_pieces.py --yolo_model "path/to/trained/model.pt" --input "path/to/test/image.jpg"
```

## References

- [Ultralytics YOLO Documentation](https://docs.ultralytics.com/)
- [PyTorch CUDA Documentation](https://pytorch.org/docs/stable/notes/cuda.html)
- [NVIDIA RTX 3050 Specifications](https://www.nvidia.com/en-us/geforce/graphics-cards/30-series/rtx-3050/)
