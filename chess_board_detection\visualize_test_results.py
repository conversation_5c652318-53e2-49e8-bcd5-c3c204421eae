"""
<PERSON><PERSON><PERSON> to visualize test results in a format similar to training visualizations.
This will show the detected corners overlaid on the original image, along with
heatmaps and segmentation mask.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import time

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for model input."""
    # Load and resize the image
    image = Image.open(image_path).convert('RGB')
    original_image = np.array(image)
    
    # Resize while maintaining aspect ratio
    w, h = image.size
    ratio = min(target_size[0] / w, target_size[1] / h)
    new_size = (int(w * ratio), int(h * ratio))
    image = image.resize(new_size, Image.LANCZOS)
    
    # Create a black canvas of target size
    canvas = Image.new('RGB', target_size, (0, 0, 0))
    # Paste the resized image in the center
    offset = ((target_size[0] - new_size[0]) // 2, (target_size[1] - new_size[1]) // 2)
    canvas.paste(image, offset)
    
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    input_tensor = transform(canvas).unsqueeze(0)
    
    return input_tensor, original_image, offset, ratio, canvas

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']
    
    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)
    
    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []
    
    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))
    
    return segmentation, heatmaps, keypoints

def visualize_training_style(original_image, processed_image, segmentation, heatmaps, keypoints, model_name, output_path):
    """Create a visualization similar to training visualizations."""
    # Convert processed image back to numpy array
    processed_np = np.array(processed_image)
    
    # Create figure with 3 rows and 3 columns
    fig, axs = plt.subplots(3, 3, figsize=(15, 15))
    fig.suptitle(f'Chess Board Detection Results - {model_name}', fontsize=16)
    
    # Row 1: Original image, processed image, segmentation mask
    axs[0, 0].imshow(original_image)
    axs[0, 0].set_title('Original Image')
    axs[0, 0].axis('off')
    
    axs[0, 1].imshow(processed_np)
    axs[0, 1].set_title('Processed Input')
    axs[0, 1].axis('off')
    
    axs[0, 2].imshow(segmentation[0, 0].cpu().numpy(), cmap='gray')
    axs[0, 2].set_title('Segmentation Mask')
    axs[0, 2].axis('off')
    
    # Row 2: Individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        if i < 3:
            ax = axs[1, i]
        else:
            ax = axs[2, 0]  # Bottom-Left goes to first position in row 3
        
        hm = heatmaps[0, i].cpu().numpy()
        ax.imshow(hm, cmap='jet')
        ax.set_title(f'{corner_names[i]} Heatmap\nConfidence: {keypoints[i][2]:.3f}')
        ax.axis('off')
    
    # Row 3: Combined visualization
    # Create a copy of processed image for drawing
    vis_img = processed_np.copy()
    
    # Draw detected corners on the processed image
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # RGB format
    
    for i, (x, y, conf) in enumerate(keypoints):
        # Draw circle at keypoint location
        cv2.circle(vis_img, (int(x), int(y)), 5, colors[i], -1)
        
        # Add label
        cv2.putText(vis_img, f"{corner_names[i]}", (int(x) + 5, int(y) - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, colors[i], 2)
    
    # Draw lines connecting the corners
    points = [(int(x), int(y)) for x, y, _ in keypoints]
    if len(points) == 4:
        # Draw the quadrilateral
        for i in range(4):
            cv2.line(vis_img, points[i], points[(i + 1) % 4], (0, 255, 0), 2)
    
    axs[2, 1].imshow(vis_img)
    axs[2, 1].set_title('Detected Corners')
    axs[2, 1].axis('off')
    
    # Create combined heatmap visualization
    combined_hm = np.zeros((heatmaps.shape[2], heatmaps.shape[3], 3))
    
    for i in range(4):
        hm = heatmaps[0, i].cpu().numpy()
        
        # Normalize heatmap
        if np.max(hm) > 0:
            hm = hm / np.max(hm)
        
        # Add to combined heatmap with color
        color_idx = i / 3.0
        color = plt.cm.jet(color_idx)[:3]
        
        for ch in range(3):
            combined_hm[:, :, ch] += hm * color[ch]
    
    # Normalize combined heatmap
    if np.max(combined_hm) > 0:
        combined_hm = combined_hm / np.max(combined_hm)
    
    axs[2, 2].imshow(combined_hm)
    axs[2, 2].set_title('Combined Heatmaps')
    axs[2, 2].axis('off')
    
    # Add metrics text
    avg_confidence = np.mean([kp[2] for kp in keypoints])
    metrics_text = f"Average Confidence: {avg_confidence:.3f}\n"
    for i, (_, _, conf) in enumerate(keypoints):
        metrics_text += f"{corner_names[i]}: {conf:.3f}\n"
    
    plt.figtext(0.5, 0.01, metrics_text, ha='center', fontsize=12, 
                bbox=dict(facecolor='white', alpha=0.8))
    
    # Adjust layout and save
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs"
    
    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each model
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name}...")
        
        # Load model
        model = load_model(model_path)
        
        # Preprocess image
        input_tensor, original_image, offset, ratio, processed_image = preprocess_image(image_path)
        
        # Detect corners
        segmentation, heatmaps, keypoints = detect_corners(model, input_tensor)
        
        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_training_style_vis.png")
        visualize_training_style(
            original_image, 
            processed_image, 
            segmentation, 
            heatmaps, 
            keypoints, 
            model_name, 
            output_path
        )
        
        print(f"Visualization saved to: {output_path}")
    
    print("All visualizations completed!")

if __name__ == "__main__":
    main()
