"""
Simplified Test Script for Chess Piece Detection
"""

import os
import sys
import cv2
import numpy as np
import torch
from ultralytics import YOLO
from ultralytics.utils.plotting import Annotator, colors
import time

def remove_duplicate_detections(boxes, scores, class_ids, iou_threshold=0.5):
    """
    Remove duplicate detections by keeping only the highest confidence detection
    for each location based on IoU.
    """
    if len(boxes) == 0:
        return boxes, scores, class_ids

    # Sort by confidence
    indices = np.argsort(scores)[::-1]
    boxes = boxes[indices]
    scores = scores[indices]
    class_ids = class_ids[indices]

    # Initialize list of indices to keep
    keep_indices = []

    # Initialize list of indices to remove
    remove_indices = []

    # First pass: Remove same-class duplicates
    # Group boxes by class
    class_groups = {}
    for i in range(len(boxes)):
        cls = int(class_ids[i])
        if cls not in class_groups:
            class_groups[cls] = []
        class_groups[cls].append(i)

    # For each class, keep only the highest confidence box
    for cls, indices in class_groups.items():
        if len(indices) > 1:
            # Keep the first (highest confidence) box
            keep_indices.append(indices[0])
            # Mark the rest for removal
            for idx in indices[1:]:
                remove_indices.append(idx)
        else:
            # Only one box for this class, keep it
            keep_indices.append(indices[0])

    # Second pass: Remove overlapping boxes of different classes
    final_keep = []
    final_remove = list(remove_indices)  # Start with already removed indices

    for i in sorted(keep_indices):
        # Skip if this box was marked for removal in the second pass
        if i in final_remove:
            continue

        # Keep this box
        final_keep.append(i)

        # Compare with other kept boxes
        for j in sorted(keep_indices):
            if i == j or j in final_remove:
                continue

            # Calculate IoU between boxes
            iou = box_iou(boxes[i], boxes[j])

            # If IoU is above threshold, remove the lower confidence box
            if iou > iou_threshold:
                final_remove.append(j)

    # Get the filtered boxes, scores, and class_ids
    filtered_boxes = boxes[final_keep]
    filtered_scores = scores[final_keep]
    filtered_class_ids = class_ids[final_keep]

    return filtered_boxes, filtered_scores, filtered_class_ids

def box_iou(box1, box2):
    """
    Calculate IoU between two boxes.
    """
    # Box coordinates
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i < x1_i or y2_i < y1_i:
        return 0.0  # No intersection

    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate union area
    box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
    box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = box1_area + box2_area - intersection_area

    # Calculate IoU
    iou = intersection_area / union_area

    return iou

# Configuration
CONFIG = {
    # Best model path
    "model_path": "runs/detect/simple_continue/continue_epoch107/weights/best.pt",

    # Test images directory
    "test_dir": r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)",

    # Output directory for visualizations
    "output_dir": "test_results_simple",

    # Detection parameters
    "conf_threshold": 0.25,  # Confidence threshold for detections
    "iou_threshold": 0.45,   # IoU threshold for NMS
    "img_size": 416,         # Image size for inference

    # Class names
    "class_names": [
        "white_pawn", "white_knight", "white_bishop", "white_rook",
        "white_queen", "white_king", "black_pawn", "black_knight",
        "black_bishop", "black_rook", "black_queen", "black_king"
    ]
}

def main():
    """Main function."""
    print("Chess Piece Detection - Simple Test")
    print("=" * 50)

    # Create output directory
    os.makedirs(CONFIG["output_dir"], exist_ok=True)

    # Load model
    print(f"Loading model: {CONFIG['model_path']}")
    start_time = time.time()
    model = YOLO(CONFIG["model_path"])
    print(f"Model loaded in {time.time() - start_time:.2f} seconds")

    # Get test images
    print(f"Looking for test images in: {CONFIG['test_dir']}")
    if not os.path.exists(CONFIG["test_dir"]):
        print(f"ERROR: Test directory not found: {CONFIG['test_dir']}")
        return

    test_images = [os.path.join(CONFIG["test_dir"], f) for f in os.listdir(CONFIG["test_dir"])
                  if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

    print(f"Found {len(test_images)} test images")
    if len(test_images) == 0:
        print("No test images found!")
        return

    # Process each image
    for i, img_path in enumerate(test_images):
        print(f"\nProcessing image {i+1}/{len(test_images)}: {os.path.basename(img_path)}")

        # Check if image exists
        if not os.path.exists(img_path):
            print(f"ERROR: Image not found: {img_path}")
            continue

        # Read image
        print(f"Reading image: {img_path}")
        start_time = time.time()
        img = cv2.imread(img_path)
        if img is None:
            print(f"ERROR: Failed to read image: {img_path}")
            continue

        print(f"Image shape: {img.shape}")
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        print(f"Image read in {time.time() - start_time:.2f} seconds")

        # Run inference
        print("Running inference...")
        start_time = time.time()
        results = model.predict(
            img_rgb,
            conf=CONFIG["conf_threshold"],
            iou=CONFIG["iou_threshold"],
            imgsz=CONFIG["img_size"],
            verbose=False
        )[0]
        print(f"Inference completed in {time.time() - start_time:.2f} seconds")

        # Get detections
        boxes = results.boxes.xyxy.cpu().numpy()
        scores = results.boxes.conf.cpu().numpy()
        class_ids = results.boxes.cls.cpu().numpy().astype(int)

        print(f"Raw detections: {len(boxes)}")

        # Remove duplicate detections
        if len(boxes) > 0:
            print("Removing duplicate detections...")
            filtered_boxes, filtered_scores, filtered_class_ids = remove_duplicate_detections(
                boxes, scores, class_ids, CONFIG["iou_threshold"]
            )
            print(f"After removing duplicates: {len(filtered_boxes)} detections")

            boxes = filtered_boxes
            scores = filtered_scores
            class_ids = filtered_class_ids

        # Create output image
        output_img = img.copy()
        annotator = Annotator(output_img)

        # Draw detections
        for j, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
            # Get class name
            class_name = CONFIG["class_names"][class_id]

            # Draw box with class color
            color = colors(class_id)
            annotator.box_label(box, f"{j+1}", color=color)

        # Save output image
        output_path = os.path.join(CONFIG["output_dir"], f"{os.path.splitext(os.path.basename(img_path))[0]}_result.jpg")
        print(f"Saving result to: {output_path}")
        cv2.imwrite(output_path, output_img)

        # Print detection details
        print(f"Detections: {len(boxes)}")
        for j, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
            class_name = CONFIG["class_names"][class_id]
            print(f"  {j+1}. {class_name}: {score:.4f}")

    print("\nTesting completed!")
    print(f"Results saved to {CONFIG['output_dir']}")

if __name__ == "__main__":
    main()
