"""
Evaluate the high-accuracy chess piece detection model.
This script performs detailed evaluation to verify that the model meets
the 99%+ accuracy and precision targets.
"""

import os
import sys
import argparse
import json
import numpy as np
import matplotlib.pyplot as plt
import cv2
from pathlib import Path
from ultralytics import YOL<PERSON>
from tqdm import tqdm
from sklearn.metrics import confusion_matrix, classification_report, precision_recall_curve
import seaborn as sns

def evaluate_model(
    model_path,
    data_yaml,
    output_dir,
    conf_threshold=0.7,
    iou_threshold=0.7,
    per_class=True,
    plot_confusion=True,
    plot_pr_curve=True,
    calculate_color_metrics=True,
    calculate_f_beta=True,
    calculate_iou_metrics=True
):
    """
    Evaluate a trained YOLO model with detailed metrics.

    Args:
        model_path: Path to the trained model
        data_yaml: Path to the dataset YAML file
        output_dir: Directory to save evaluation results
        conf_threshold: Confidence threshold for detections
        iou_threshold: IoU threshold for NMS
        per_class: Whether to compute per-class metrics
        plot_confusion: Whether to plot confusion matrix
        plot_pr_curve: Whether to plot precision-recall curve
        calculate_color_metrics: Whether to calculate color-specific metrics (white vs black accuracy)
        calculate_f_beta: Whether to calculate F-beta scores (F1, F2, F0.5)
        calculate_iou_metrics: Whether to calculate IoU-based metrics
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Load model
    model = YOLO(model_path)

    # Run validation
    print(f"Validating {model_path}...")
    metrics = model.val(
        data=data_yaml,
        conf=conf_threshold,
        iou=iou_threshold,
        verbose=True
    )

    # Extract metrics
    map50 = metrics.box.map50
    map = metrics.box.map
    precision = metrics.box.precision
    recall = metrics.box.recall

    # Print overall metrics
    print("\nOverall Metrics:")
    print(f"mAP50: {map50:.4f}")
    print(f"mAP50-95: {map:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall: {recall:.4f}")

    # Calculate additional metrics
    additional_metrics = {}

    # Calculate F-beta scores if requested
    if calculate_f_beta:
        # F1 score (harmonic mean of precision and recall)
        f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0

        # F2 score (weighs recall higher than precision)
        beta = 2
        f2_score = (1 + beta**2) * precision * recall / (beta**2 * precision + recall) if (beta**2 * precision + recall) > 0 else 0

        # F0.5 score (weighs precision higher than recall)
        beta = 0.5
        f05_score = (1 + beta**2) * precision * recall / (beta**2 * precision + recall) if (beta**2 * precision + recall) > 0 else 0

        additional_metrics.update({
            "f1_score": float(f1_score),
            "f2_score": float(f2_score),
            "f05_score": float(f05_score)
        })

        print("\nF-beta Scores:")
        print(f"F1 Score (balanced): {f1_score:.4f}")
        print(f"F2 Score (recall-focused): {f2_score:.4f}")
        print(f"F0.5 Score (precision-focused): {f05_score:.4f}")

    # Calculate color-specific metrics if requested
    if calculate_color_metrics and hasattr(metrics, 'class_result'):
        # Group classes by color
        white_classes = [i for i, name in enumerate(metrics.names) if name.startswith('white_')]
        black_classes = [i for i, name in enumerate(metrics.names) if name.startswith('black_')]

        # Calculate metrics for white pieces
        white_precision = np.mean([metrics.class_result[i]['precision'] for i in white_classes])
        white_recall = np.mean([metrics.class_result[i]['recall'] for i in white_classes])
        white_map50 = np.mean([metrics.class_result[i]['map50'] for i in white_classes])
        white_f1 = 2 * white_precision * white_recall / (white_precision + white_recall) if (white_precision + white_recall) > 0 else 0

        # Calculate metrics for black pieces
        black_precision = np.mean([metrics.class_result[i]['precision'] for i in black_classes])
        black_recall = np.mean([metrics.class_result[i]['recall'] for i in black_classes])
        black_map50 = np.mean([metrics.class_result[i]['map50'] for i in black_classes])
        black_f1 = 2 * black_precision * black_recall / (black_precision + black_recall) if (black_precision + black_recall) > 0 else 0

        # Calculate color accuracy (how well the model distinguishes white from black)
        # This requires analyzing the confusion matrix
        if hasattr(metrics, 'confusion_matrix'):
            cm = metrics.confusion_matrix

            # Calculate color confusion
            white_to_black_confusion = 0
            black_to_white_confusion = 0
            total_white = 0
            total_black = 0

            for i in white_classes:
                for j in black_classes:
                    white_to_black_confusion += cm[i, j]
                total_white += np.sum(cm[i, :])

            for i in black_classes:
                for j in white_classes:
                    black_to_white_confusion += cm[i, j]
                total_black += np.sum(cm[i, :])

            color_accuracy = 1.0 - (white_to_black_confusion + black_to_white_confusion) / (total_white + total_black) if (total_white + total_black) > 0 else 0
        else:
            color_accuracy = None

        additional_metrics.update({
            "white_precision": float(white_precision),
            "white_recall": float(white_recall),
            "white_map50": float(white_map50),
            "white_f1": float(white_f1),
            "black_precision": float(black_precision),
            "black_recall": float(black_recall),
            "black_map50": float(black_map50),
            "black_f1": float(black_f1),
            "color_accuracy": float(color_accuracy) if color_accuracy is not None else None
        })

        print("\nColor-Specific Metrics:")
        print("White Pieces:")
        print(f"  Precision: {white_precision:.4f}")
        print(f"  Recall: {white_recall:.4f}")
        print(f"  mAP50: {white_map50:.4f}")
        print(f"  F1 Score: {white_f1:.4f}")
        print("Black Pieces:")
        print(f"  Precision: {black_precision:.4f}")
        print(f"  Recall: {black_recall:.4f}")
        print(f"  mAP50: {black_map50:.4f}")
        print(f"  F1 Score: {black_f1:.4f}")
        if color_accuracy is not None:
            print(f"Color Classification Accuracy: {color_accuracy:.4f}")

    # Calculate IoU-based metrics if requested
    if calculate_iou_metrics and hasattr(metrics, 'iou_metrics'):
        iou_metrics = metrics.iou_metrics
        additional_metrics.update({
            "mean_iou": float(iou_metrics.get('mean_iou', 0)),
            "iou_50": float(iou_metrics.get('iou_50', 0)),
            "iou_75": float(iou_metrics.get('iou_75', 0))
        })

        print("\nIoU Metrics:")
        print(f"Mean IoU: {iou_metrics.get('mean_iou', 0):.4f}")
        print(f"IoU@50: {iou_metrics.get('iou_50', 0):.4f}")
        print(f"IoU@75: {iou_metrics.get('iou_75', 0):.4f}")

    # Check if targets are met
    precision_target = 0.99
    recall_target = 0.99
    map50_target = 0.99
    color_accuracy_target = 0.99 if 'color_accuracy' in additional_metrics else None

    targets_met = {
        "precision": precision >= precision_target,
        "recall": recall >= recall_target,
        "map50": map50 >= map50_target
    }

    if color_accuracy_target is not None and 'color_accuracy' in additional_metrics:
        targets_met["color_accuracy"] = additional_metrics['color_accuracy'] >= color_accuracy_target

    print("\nTargets Met:")
    for metric, met in targets_met.items():
        status = "✅" if met else "❌"
        print(f"{metric}: {status}")

    # Save metrics to file
    metrics_dict = {
        "map50": float(map50),
        "map": float(map),
        "precision": float(precision),
        "recall": float(recall),
        "targets_met": targets_met,
        "all_targets_met": all(targets_met.values()),
        **additional_metrics  # Include all additional metrics
    }

    with open(os.path.join(output_dir, 'evaluation_metrics.json'), 'w') as f:
        json.dump(metrics_dict, f, indent=2)

    # Get per-class metrics if available
    if per_class and hasattr(metrics, 'class_result'):
        class_metrics = {}
        for i, class_name in enumerate(metrics.names):
            class_metrics[class_name] = {
                "precision": float(metrics.class_result[i]['precision']),
                "recall": float(metrics.class_result[i]['recall']),
                "map50": float(metrics.class_result[i]['map50'])
            }

        # Save per-class metrics
        with open(os.path.join(output_dir, 'per_class_metrics.json'), 'w') as f:
            json.dump(class_metrics, f, indent=2)

        # Print per-class metrics
        print("\nPer-Class Metrics:")
        for class_name, class_metric in class_metrics.items():
            print(f"{class_name}:")
            print(f"  Precision: {class_metric['precision']:.4f}")
            print(f"  Recall: {class_metric['recall']:.4f}")
            print(f"  mAP50: {class_metric['map50']:.4f}")

    # Plot confusion matrix if requested
    if plot_confusion and hasattr(metrics, 'confusion_matrix'):
        cm = metrics.confusion_matrix
        class_names = metrics.names

        plt.figure(figsize=(12, 10))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                   xticklabels=class_names, yticklabels=class_names)
        plt.xlabel('Predicted')
        plt.ylabel('True')
        plt.title('Confusion Matrix')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'confusion_matrix.png'), dpi=150)
        plt.close()

    # Plot precision-recall curve if requested
    if plot_pr_curve and hasattr(metrics, 'pr_curve'):
        pr_data = metrics.pr_curve

        plt.figure(figsize=(10, 8))
        for i, class_name in enumerate(metrics.names):
            if i < len(pr_data):
                precision = pr_data[i]['precision']
                recall = pr_data[i]['recall']
                plt.plot(recall, precision, label=f'{class_name} (AP={pr_data[i]["ap"]:.4f})')

        plt.xlabel('Recall')
        plt.ylabel('Precision')
        plt.title('Precision-Recall Curve')
        plt.legend(loc='lower left')
        plt.grid(True)
        plt.savefig(os.path.join(output_dir, 'precision_recall_curve.png'), dpi=150)
        plt.close()

    # Test on specific problem cases
    test_problem_cases(model, output_dir, conf_threshold, iou_threshold)

    return metrics_dict

def test_problem_cases(model, output_dir, conf_threshold=0.7, iou_threshold=0.7):
    """
    Test the model on specific problem cases, particularly focusing on
    color differentiation between black and white pieces.

    Args:
        model: Loaded YOLO model
        output_dir: Directory to save results
        conf_threshold: Confidence threshold for detections
        iou_threshold: IoU threshold for NMS
    """
    # Create directory for problem case results
    problem_dir = os.path.join(output_dir, 'problem_cases')
    os.makedirs(problem_dir, exist_ok=True)

    # Define test images (these should be images with known issues)
    test_images = [
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (4)\\24.jpg",
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (4)\\25.jpg",
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (4)\\26.jpg",
        "C:\\Users\\<USER>\\OneDrive\\Desktop\\New folder (4)\\27.jpg"
    ]

    print("\nTesting on problem cases:")
    for img_path in test_images:
        if not os.path.exists(img_path):
            print(f"Warning: Image not found: {img_path}")
            continue

        print(f"Processing {img_path}")

        # Run inference
        results = model.predict(
            source=img_path,
            conf=conf_threshold,
            iou=iou_threshold,
            verbose=False
        )

        # Save results
        for i, result in enumerate(results):
            # Get the original image
            img = result.orig_img

            # Get detections
            boxes = result.boxes.xyxy.cpu().numpy()
            classes = result.boxes.cls.cpu().numpy().astype(int)
            confs = result.boxes.conf.cpu().numpy()

            # Create a clean image without annotations
            clean_img = img.copy()

            # Draw custom annotations
            for box, cls, conf in zip(boxes, classes, confs):
                x1, y1, x2, y2 = box.astype(int)

                # Get class name
                class_name = result.names[cls]

                # Determine color based on class name
                if class_name.startswith("white_"):
                    color = (0, 255, 0)  # Green for white pieces
                elif class_name.startswith("black_"):
                    color = (255, 0, 255)  # Magenta for black pieces
                else:
                    color = (0, 255, 255)  # Yellow for unknown

                # Draw bounding box
                cv2.rectangle(clean_img, (x1, y1), (x2, y2), color, 2)

                # Draw label
                label = f"{class_name} {conf:.2f}"
                cv2.putText(
                    clean_img,
                    label,
                    (x1, y1 - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    color,
                    2
                )

            # Save the annotated image
            output_path = os.path.join(problem_dir, f"detection_{Path(img_path).name}")
            cv2.imwrite(output_path, clean_img)
            print(f"Saved result to {output_path}")

            # Log detections
            detections = []
            for box, cls, conf in zip(boxes, classes, confs):
                detections.append({
                    "class": result.names[cls],
                    "confidence": float(conf),
                    "box": box.tolist()
                })

            # Save detections to JSON
            json_path = os.path.join(problem_dir, f"detection_{Path(img_path).stem}.json")
            with open(json_path, 'w') as f:
                json.dump(detections, f, indent=2)

def main():
    parser = argparse.ArgumentParser(description="Evaluate high-accuracy chess piece detection model")
    parser.add_argument("--model", type=str, required=True, help="Path to trained model")
    parser.add_argument("--data_yaml", type=str, required=True, help="Path to dataset YAML file")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/piece_detection/evaluation",
                        help="Directory to save evaluation results")
    parser.add_argument("--conf", type=float, default=0.7, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.7, help="IoU threshold")
    parser.add_argument("--no-color-metrics", action="store_false", dest="color_metrics",
                        help="Disable color-specific metrics")
    parser.add_argument("--no-f-beta", action="store_false", dest="f_beta",
                        help="Disable F-beta score calculation")
    parser.add_argument("--no-iou-metrics", action="store_false", dest="iou_metrics",
                        help="Disable IoU-based metrics")
    parser.add_argument("--no-confusion", action="store_false", dest="confusion",
                        help="Disable confusion matrix plotting")
    parser.add_argument("--no-pr-curve", action="store_false", dest="pr_curve",
                        help="Disable precision-recall curve plotting")

    args = parser.parse_args()

    evaluate_model(
        args.model,
        args.data_yaml,
        args.output_dir,
        args.conf,
        args.iou,
        per_class=True,
        plot_confusion=args.confusion,
        plot_pr_curve=args.pr_curve,
        calculate_color_metrics=args.color_metrics,
        calculate_f_beta=args.f_beta,
        calculate_iou_metrics=args.iou_metrics
    )

if __name__ == "__main__":
    main()
