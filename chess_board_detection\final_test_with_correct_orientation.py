"""
<PERSON><PERSON><PERSON> to test chess board detection models on real-world images with correct orientation.
This script ensures no flipping or rotation of images during preprocessing.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import pandas as pd
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image_preserve_orientation(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """
    Enhance image colors and contrast similar to training augmentations.
    """
    # 1. Adaptive contrast enhancement (similar to CLAHE in training)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
    lab = cv2.cvtColor(image, cv2.COLOR_RGB2LAB)
    l, a, b = cv2.split(lab)
    l = clahe.apply(l)
    lab = cv2.merge([l, a, b])
    image_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2RGB)

    # 2. Gamma correction (similar to RandomGamma in training)
    # Use a gamma value that enhances details without over-brightening
    gamma = 1.1
    inv_gamma = 1.0 / gamma
    table = np.array([((i / 255.0) ** inv_gamma) * 255 for i in range(256)]).astype("uint8")
    image_enhanced = cv2.LUT(image_enhanced, table)

    # 3. Saturation enhancement (similar to HueSaturationValue in training)
    hsv = cv2.cvtColor(image_enhanced, cv2.COLOR_RGB2HSV)
    h, s, v = cv2.split(hsv)
    s = np.clip(s * 1.2, 0, 255).astype(np.uint8)  # Increase saturation by 20%
    hsv = cv2.merge([h, s, v])
    image_enhanced = cv2.cvtColor(hsv, cv2.COLOR_HSV2RGB)

    return image_enhanced

def normalize_for_model(image):
    """
    Normalize image for model input using same normalization as during training.
    """
    # Convert to PIL Image first
    image_pil = Image.fromarray(image)

    # Use torchvision transforms which handle the data type correctly
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Apply transform and add batch dimension
    image_tensor = transform(image_pil).unsqueeze(0)

    return image_tensor

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)

    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []

    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))

    return segmentation, heatmaps, keypoints

def map_to_original_coordinates(keypoints, preprocess_info):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    Accounts for all preprocessing steps: resize, crop, and final resize.
    """
    mapped_keypoints = []

    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']
    resized_w, resized_h = preprocess_info['resized_size']

    for x, y, conf in keypoints:
        # Step 1: Map from target size to crop size
        crop_scale_x = crop_size / target_w
        crop_scale_y = crop_size / target_h

        x_in_crop = x * crop_scale_x
        y_in_crop = y * crop_scale_y

        # Step 2: Add crop offset to get coordinates in resized image
        x_in_resized = x_in_crop + crop_start_x
        y_in_resized = y_in_crop + crop_start_y

        # Step 3: Scale to original image size
        orig_scale_x = original_w / resized_w
        orig_scale_y = original_h / resized_h

        x_in_original = x_in_resized * orig_scale_x
        y_in_original = y_in_resized * orig_scale_y

        # Ensure coordinates are within image bounds
        x_in_original = max(0, min(x_in_original, original_w - 1))
        y_in_original = max(0, min(y_in_original, original_h - 1))

        mapped_keypoints.append((x_in_original, y_in_original, conf))

    return mapped_keypoints

def visualize_results(original_image, preprocessing_results, output_path):
    """
    Create a visualization showing the detection results on the original image.
    """
    # Create a copy of the original image for drawing
    vis_img = original_image.copy()

    # Define corner names and colors
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = [(255, 0, 0), (0, 255, 0), (0, 0, 255), (255, 255, 0)]  # BGR format for OpenCV

    # Draw keypoints and lines
    keypoints = preprocessing_results['original_keypoints']

    # Draw keypoints
    for i, (x, y, conf) in enumerate(keypoints):
        cv2.circle(vis_img, (int(x), int(y)), 15, colors[i], -1)
        cv2.putText(vis_img, f"{corner_names[i]} ({int(x)}, {int(y)})",
                   (int(x) + 20, int(y) + 20), cv2.FONT_HERSHEY_SIMPLEX,
                   1.0, colors[i], 3)

    # Draw lines connecting keypoints
    points = [(int(x), int(y)) for x, y, _ in keypoints]
    if len(points) == 4:
        for i in range(4):
            cv2.line(vis_img, points[i], points[(i + 1) % 4], (0, 255, 0), 5)

    # Create figure
    plt.figure(figsize=(15, 12))
    plt.imshow(cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB))
    plt.title(f'Chess Board Detection - {preprocessing_results["model_name"]}', fontsize=16)
    plt.axis('off')

    # Add confidence values as text
    confidence_text = "Corner Confidence Values:\n"
    for i, (_, _, conf) in enumerate(keypoints):
        confidence_text += f"{corner_names[i]}: {conf:.4f}\n"

    avg_confidence = np.mean([conf for _, _, conf in keypoints])
    confidence_text += f"\nAverage Confidence: {avg_confidence:.4f}"

    plt.figtext(0.02, 0.02, confidence_text, fontsize=12,
               bbox=dict(facecolor='white', alpha=0.8))

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

    return output_path

def save_corner_coordinates(results_dict, output_path):
    """Save corner coordinates to CSV and JSON files."""
    # Create data for CSV
    table_data = []

    for model_name, results in results_dict.items():
        corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        for i, (x, y, conf) in enumerate(results['original_keypoints']):
            # Convert numpy types to Python native types
            x_val = int(float(x))
            y_val = int(float(y))
            conf_val = float(conf)

            table_data.append({
                'Model': model_name,
                'Corner': corner_names[i],
                'X': x_val,
                'Y': y_val,
                'Confidence': round(conf_val, 4)
            })

    # Create DataFrame
    df = pd.DataFrame(table_data)

    # Save as CSV
    csv_path = output_path.replace('.json', '.csv')
    df.to_csv(csv_path, index=False)

    # Create JSON data
    json_data = {}
    for model_name, results in results_dict.items():
        json_data[model_name] = {}
        corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        for i, (x, y, conf) in enumerate(results['original_keypoints']):
            # Convert numpy types to Python native types
            x_val = int(float(x))
            y_val = int(float(y))
            conf_val = float(conf)

            json_data[model_name][corner_names[i]] = {
                'x': x_val,
                'y': y_val,
                'confidence': round(conf_val, 4)
            }

    # Save as JSON
    with open(output_path, 'w') as f:
        json.dump(json_data, f, indent=4)

    # Print table
    print("\nChess Board Corner Detection - Pixel Coordinates")
    print("==============================================")
    print(df.to_string(index=False))

    return csv_path, output_path

def process_real_world_image(image_path, model, model_name):
    """
    Apply the complete optimal preprocessing pipeline to a real-world image.
    """
    # Step 1: Basic preprocessing (resize & crop) with orientation preservation
    basic_preprocessed, preprocess_info = preprocess_image_preserve_orientation(image_path)

    # Step 2: Color/lighting enhancement
    enhanced_image = enhance_image(basic_preprocessed)

    # Step 3: Normalize for model
    input_tensor = normalize_for_model(enhanced_image)

    # Step 4: Run inference
    segmentation, heatmaps, model_keypoints = detect_corners(model, input_tensor)

    # Step 5: Map keypoints back to original image
    original_keypoints = map_to_original_coordinates(model_keypoints, preprocess_info)

    # Return all results
    return {
        'model_name': model_name,
        'original_image': preprocess_info['original_image'],
        'basic_preprocessed': basic_preprocessed,
        'enhanced_image': enhanced_image,
        'segmentation': segmentation.cpu().numpy(),
        'heatmaps': heatmaps.cpu().numpy(),
        'model_keypoints': model_keypoints,
        'original_keypoints': original_keypoints
    }

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\final_test"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    os.makedirs(output_dir, exist_ok=True)

    # Process each model
    results_dict = {}

    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} with orientation-preserving preprocessing...")

        # Load model
        model = load_model(model_path)

        # Process image with optimal preprocessing
        results = process_real_world_image(image_path, model, model_name)

        # Store results
        results_dict[model_name] = results

        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_final_result.png")
        visualize_results(results['original_image'], results, output_path)

        print(f"Final detection result saved to: {output_path}")

    # Save corner coordinates
    coords_path = os.path.join(output_dir, "corner_coordinates.json")
    csv_path, json_path = save_corner_coordinates(results_dict, coords_path)

    print(f"Corner coordinates saved to: {csv_path} and {json_path}")
    print("All processing completed!")

if __name__ == "__main__":
    main()
