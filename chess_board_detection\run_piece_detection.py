"""
Run chess piece detection with improved visualization.
"""

import os
import sys
import argparse

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Now we can import from the module
from piece_detection.visualize_detections import visualize_detections

def main():
    parser = argparse.ArgumentParser(description="Run chess piece detection with improved visualization")
    parser.add_argument("--model", type=str, required=True, help="Path to trained YOLO model")
    parser.add_argument("--input", type=str, required=True, help="Path to input image or directory")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/outputs/piece_detection",
                        help="Directory to save results")
    parser.add_argument("--conf", type=float, default=0.25, help="Confidence threshold")
    parser.add_argument("--font_size", type=float, default=0.4, help="Size of font for labels")
    parser.add_argument("--line_width", type=int, default=1, help="Width of bounding box lines")
    parser.add_argument("--show_conf", action="store_true", help="Show confidence scores")

    args = parser.parse_args()

    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)

    # Run detection with improved visualization
    visualize_detections(
        model_path=args.model,
        source=args.input,
        output_dir=args.output_dir,
        conf_threshold=args.conf,
        show_labels=True,
        show_conf=args.show_conf,
        line_width=args.line_width,
        font_size=args.font_size,
        font_thickness=1
    )

    print(f"Detection completed. Results saved to {args.output_dir}")

if __name__ == "__main__":
    main()
