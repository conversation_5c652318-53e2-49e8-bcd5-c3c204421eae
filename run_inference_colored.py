import sys
import os
import cv2
import numpy as np
from ultralytics import YOLO
import matplotlib.pyplot as plt
from matplotlib.patches import Patch

# Define colors for each class (BGR format for OpenCV)
COLORS = {
    'white_pawn': (255, 255, 0),     # <PERSON>an
    'white_knight': (255, 0, 255),   # <PERSON><PERSON>a
    'white_bishop': (0, 255, 255),   # Yellow
    'white_rook': (0, 0, 255),       # Red
    'white_queen': (255, 0, 0),      # <PERSON>
    'white_king': (0, 255, 0),       # Green
    'black_pawn': (128, 255, 0),     # <PERSON> Cyan
    'black_knight': (255, 128, 255), # Light Magenta
    'black_bishop': (128, 255, 255), # Light Yellow
    'black_rook': (128, 128, 255),   # Light Red
    'black_queen': (255, 128, 128),  # Light Blue
    'black_king': (128, 255, 128),   # Light Green
}

# Class names
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

def create_legend_image():
    """Create a legend image showing colors for each class"""
    fig, ax = plt.figure(figsize=(10, 6)), plt.gca()
    ax.axis('off')
    
    # Create legend elements
    legend_elements = [
        Patch(facecolor=[c[2]/255, c[1]/255, c[0]/255], label=name) 
        for name, c in COLORS.items()
    ]
    
    # Add legend to plot
    ax.legend(handles=legend_elements, loc='center', fontsize=12)
    plt.title('Chess Piece Color Legend', fontsize=14)
    
    # Save legend
    plt.tight_layout()
    plt.savefig('chess_piece_legend.png', dpi=200, bbox_inches='tight')
    plt.close()

def process_image(model_path, image_path, output_dir):
    """Process an image with the model and save with colored boxes"""
    # Load model
    model = YOLO(model_path)
    
    # Run inference
    results = model.predict(image_path, conf=0.25)
    
    # Process each image result
    for i, result in enumerate(results):
        # Get the original image
        img = cv2.imread(image_path[i] if isinstance(image_path, list) else image_path)
        
        # Get detection data
        boxes = result.boxes.xyxy.cpu().numpy()
        cls_ids = result.boxes.cls.cpu().numpy().astype(int)
        confs = result.boxes.conf.cpu().numpy()
        
        # Draw colored boxes without labels
        for box, cls_id, conf in zip(boxes, cls_ids, confs):
            x1, y1, x2, y2 = box.astype(int)
            color = COLORS[CLASS_NAMES[cls_id]]
            
            # Draw thicker box for better visibility
            cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
        
        # Save the image
        base_name = os.path.basename(image_path[i] if isinstance(image_path, list) else image_path)
        output_path = os.path.join(output_dir, base_name)
        os.makedirs(output_dir, exist_ok=True)
        cv2.imwrite(output_path, img)
        
        print(f"Processed {base_name} and saved to {output_path}")

if __name__ == "__main__":
    if len(sys.argv) < 4:
        print("Usage: python run_inference_colored.py <model_path> <image_dir> <output_dir>")
        sys.exit(1)
    
    model_path = sys.argv[1]
    image_dir = sys.argv[2]
    output_dir = sys.argv[3]
    
    # Get all images in the directory
    image_paths = [os.path.join(image_dir, f) for f in os.listdir(image_dir) 
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    # Create legend
    create_legend_image()
    
    # Process images
    process_image(model_path, image_paths, output_dir)
    
    print(f"Legend saved to chess_piece_legend.png")
