import os
import cv2
import numpy as np
import albumentations as A
from sklearn.model_selection import train_test_split
import shutil
import glob
import random
import yaml
from tqdm import tqdm

# Define piece class mappings
PIECE_CLASS_MAPPING = {
    'white_pawn': 0,
    'white_knight': 1,
    'white_bishop': 2,
    'white_rook': 3,
    'white_queen': 4,
    'white_king': 5,
    'black_pawn': 6,
    'black_knight': 7,
    'black_bishop': 8,
    'black_rook': 9,
    'black_queen': 10,
    'black_king': 11
}

# Define target pieces that need improvement
TARGET_PIECES = ['white_bishop', 'white_knight', 'white_queen', 'black_queen', 'black_bishop']

# Define augmentation transforms
def get_general_transform():
    return A.<PERSON>([
        A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.7),
        A.<PERSON>SaturationValue(hue_shift_limit=10, sat_shift_limit=15, val_shift_limit=10, p=0.5),
        <PERSON><PERSON>(p=0.3),
        <PERSON><PERSON>([
            <PERSON><PERSON>(blur_limit=7, p=0.2),
            <PERSON><PERSON>(blur_limit=7, p=0.2),
            A.GaussianBlur(blur_limit=7, p=0.2),
        ], p=0.2),
        A.OneOf([
            A.RandomRotate90(p=0.5),
            A.Perspective(scale=(0.05, 0.1), p=0.5),
        ], p=0.7),
    ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))

def get_bishop_transform():
    return A.Compose([
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
        A.HueSaturationValue(hue_shift_limit=15, sat_shift_limit=25, val_shift_limit=15, p=0.7),
        A.CLAHE(clip_limit=4.0, tile_grid_size=(8, 8), p=0.5),
        A.Perspective(scale=(0.05, 0.15), p=0.7),  # More perspective variation for bishops
        A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=20, p=0.8),
    ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))

def get_queen_transform():
    return A.Compose([
        A.RandomBrightnessContrast(brightness_limit=0.25, contrast_limit=0.25, p=0.8),
        A.HueSaturationValue(hue_shift_limit=10, sat_shift_limit=20, val_shift_limit=10, p=0.6),
        A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=0.5),
        A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.15, rotate_limit=15, p=0.8),
        A.RandomShadow(shadow_roi=(0, 0, 1, 1), num_shadows_lower=1, num_shadows_upper=2, p=0.3),
    ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))

def get_knight_transform():
    return A.Compose([
        A.RandomBrightnessContrast(brightness_limit=0.25, contrast_limit=0.25, p=0.7),
        A.HueSaturationValue(hue_shift_limit=15, sat_shift_limit=20, val_shift_limit=15, p=0.6),
        A.GaussNoise(p=0.4),
        A.ShiftScaleRotate(shift_limit=0.15, scale_limit=0.2, rotate_limit=25, p=0.8),  # More rotation for knights
        A.RandomShadow(shadow_roi=(0, 0, 1, 1), num_shadows_lower=1, num_shadows_upper=2, p=0.4),
    ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))

def find_images_with_pieces(source_dir, piece_types):
    """Find all images containing the specified piece types"""
    result = []

    # Get all label files
    label_files = glob.glob(os.path.join(source_dir, 'labels', '**', '*.txt'), recursive=True)

    for label_file in tqdm(label_files, desc="Scanning labels"):
        contains_target_piece = False

        # Read the label file
        with open(label_file, 'r') as f:
            annotations = f.readlines()

        # Check if any target piece is in this image
        for ann in annotations:
            parts = ann.strip().split()
            if not parts:
                continue

            # Handle both integer and float class IDs
            class_id = int(float(parts[0]))
            # Check if this class ID corresponds to any target piece
            for piece in piece_types:
                if class_id == PIECE_CLASS_MAPPING.get(piece, -1):
                    contains_target_piece = True
                    break

            if contains_target_piece:
                break

        # If image contains target piece, add to result
        if contains_target_piece:
            # Convert label path to image path
            img_file = label_file.replace('labels', 'images').replace('.txt', '.jpg')
            if os.path.exists(img_file):
                result.append(img_file)

    print(f"Found {len(result)} images containing target pieces")
    return result

def augment_and_save(image_path, output_img_dir, output_label_dir, piece_types, num_augmentations):
    """Apply piece-specific augmentations and save results"""
    try:
        image = cv2.imread(image_path)
        if image is None:
            print(f"Warning: Could not read image {image_path}")
            return

        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Get corresponding annotation
        annotation_path = image_path.replace('images', 'labels').replace('.jpg', '.txt')
        if not os.path.exists(annotation_path):
            print(f"Warning: No annotation file found for {image_path}")
            return

        with open(annotation_path, 'r') as f:
            annotations = f.readlines()

        # Parse annotations to get bounding boxes and class labels
        bboxes = []
        class_labels = []
        for ann in annotations:
            parts = ann.strip().split()
            if not parts:
                continue

            # Handle both integer and float class IDs
            class_id = int(float(parts[0]))
            x_center, y_center, width, height = map(float, parts[1:5])
            bboxes.append([x_center, y_center, width, height])
            class_labels.append(class_id)

        # Determine which target pieces are in this image
        contained_pieces = []
        for piece in piece_types:
            piece_class_id = PIECE_CLASS_MAPPING.get(piece, -1)
            if piece_class_id in class_labels:
                contained_pieces.append(piece)

        # Select appropriate transform based on pieces in the image
        if 'white_bishop' in contained_pieces or 'black_bishop' in contained_pieces:
            transform = get_bishop_transform()
        elif 'white_queen' in contained_pieces or 'black_queen' in contained_pieces:
            transform = get_queen_transform()
        elif 'white_knight' in contained_pieces or 'black_knight' in contained_pieces:
            transform = get_knight_transform()
        else:
            transform = get_general_transform()

        # Generate augmented samples
        for i in range(num_augmentations):
            try:
                augmented = transform(image=image, bboxes=bboxes, class_labels=class_labels)

                # Save augmented image
                img_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_aug{i}.jpg"
                cv2.imwrite(os.path.join(output_img_dir, img_filename),
                           cv2.cvtColor(augmented['image'], cv2.COLOR_RGB2BGR))

                # Save augmented annotation
                label_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_aug{i}.txt"
                with open(os.path.join(output_label_dir, label_filename), 'w') as f:
                    for bbox, class_id in zip(augmented['bboxes'], augmented['class_labels']):
                        f.write(f"{class_id} {bbox[0]} {bbox[1]} {bbox[2]} {bbox[3]}\n")
            except Exception as e:
                print(f"Error augmenting {image_path} (iteration {i}): {e}")
    except Exception as e:
        print(f"Error processing {image_path}: {e}")

def create_dataset_yaml(output_dir, num_classes=12):
    """Create dataset.yaml file for the augmented dataset"""
    yaml_content = {
        'path': output_dir,
        'train': 'images/train',
        'val': 'images/val',
        'nc': num_classes,
        'names': {
            0: 'white_pawn',
            1: 'white_knight',
            2: 'white_bishop',
            3: 'white_rook',
            4: 'white_queen',
            5: 'white_king',
            6: 'black_pawn',
            7: 'black_knight',
            8: 'black_bishop',
            9: 'black_rook',
            10: 'black_queen',
            11: 'black_king'
        }
    }

    with open(os.path.join(output_dir, 'dataset.yaml'), 'w') as f:
        yaml.dump(yaml_content, f, default_flow_style=False)

def create_targeted_dataset(source_dir, output_dir, piece_types=TARGET_PIECES, augmentations_per_image=10):
    """
    Create a targeted dataset with augmented images of specific chess pieces.

    Args:
        source_dir: Directory containing original images and annotations
        output_dir: Directory to save augmented dataset
        piece_types: List of piece types to focus on (e.g., ['white_bishop', 'black_queen'])
        augmentations_per_image: Number of augmented versions to create per original image
    """
    # Create output directories
    os.makedirs(os.path.join(output_dir, 'images', 'train'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'images', 'val'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'labels', 'train'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'labels', 'val'), exist_ok=True)

    # Get all images containing target pieces
    target_images = find_images_with_pieces(source_dir, piece_types)

    if not target_images:
        print("No images found with target pieces. Check your source directory and piece types.")
        return

    # Split into train/val sets (80/20 split)
    train_images, val_images = train_test_split(target_images, test_size=0.2, random_state=42)

    print(f"Processing {len(train_images)} training images...")
    # Process training images
    for img_path in tqdm(train_images, desc="Augmenting training images"):
        augment_and_save(img_path, os.path.join(output_dir, 'images', 'train'),
                       os.path.join(output_dir, 'labels', 'train'),
                       piece_types, augmentations_per_image)

    print(f"Processing {len(val_images)} validation images...")
    # Process validation images (fewer augmentations)
    for img_path in tqdm(val_images, desc="Augmenting validation images"):
        augment_and_save(img_path, os.path.join(output_dir, 'images', 'val'),
                       os.path.join(output_dir, 'labels', 'val'),
                       piece_types, max(2, augmentations_per_image // 5))

    # Create dataset.yaml file
    create_dataset_yaml(output_dir)

    # Count files in the dataset
    train_images_count = len(glob.glob(os.path.join(output_dir, 'images', 'train', '*.jpg')))
    val_images_count = len(glob.glob(os.path.join(output_dir, 'images', 'val', '*.jpg')))

    print(f"Dataset created successfully!")
    print(f"Training images: {train_images_count}")
    print(f"Validation images: {val_images_count}")

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Create targeted augmented dataset for chess piece detection")
    parser.add_argument("--source", type=str, required=True, help="Source dataset directory")
    parser.add_argument("--output", type=str, required=True, help="Output directory for augmented dataset")
    parser.add_argument("--augmentations", type=int, default=10, help="Number of augmentations per image")
    parser.add_argument("--pieces", nargs='+', default=TARGET_PIECES,
                        help="Piece types to focus on (e.g., white_bishop black_queen)")

    args = parser.parse_args()

    create_targeted_dataset(args.source, args.output, args.pieces, args.augmentations)
