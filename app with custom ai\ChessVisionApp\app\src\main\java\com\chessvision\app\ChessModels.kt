package com.chessvision.app

/**
 * Core chess data models and enums
 */

data class <PERSON><PERSON><PERSON>ce(
    val type: PieceType,
    val color: PieceColor,
    val drawableRes: Int
)

enum class PieceType {
    KING, QUEEN, ROOK, <PERSON>ISHOP, KNIGHT, PAWN
}

enum class PieceColor {
    WHITE, BLACK
}

data class ChessPosition(
    val file: Int, // 0-7 (a-h)
    val rank: Int  // 0-7 (1-8)
) {
    fun isValid(): <PERSON><PERSON>an = file in 0..7 && rank in 0..7
}
