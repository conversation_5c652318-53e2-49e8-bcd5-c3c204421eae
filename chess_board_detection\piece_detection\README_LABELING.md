# Chess Piece Labeling and Detection Improvement

This set of tools helps improve chess piece detection by:
1. Manually labeling chess pieces in real images
2. Preparing the labeled dataset for YOLO training
3. Training a new YOLO model on the labeled data
4. Testing the improved model on new images

## The Problem

The current chess piece detection system has several issues:
- Misclassification of pieces (especially empty squares being detected as pieces)
- Incorrect mapping of pieces to chess coordinates
- Inconsistent confidence scores

## Solution Approach

We'll create a better training dataset by manually labeling chess pieces in real images, then train a new YOLO model on this data.

## Tools Included

### 1. Chess Piece Labeling Tool (`label_tool.py`)

A graphical tool for manually labeling chess pieces in images:
- Draw bounding boxes around chess pieces
- Assign piece types to each bounding box
- Save annotations in YOLO format
- Load existing annotations for review/editing

### 2. Dataset Preparation (`prepare_labeled_dataset.py`)

Prepares the labeled dataset for YOLO training:
- Organizes images and annotations into train/val splits
- Creates data.yaml file for YOLO training
- Analyzes class distribution in the dataset

### 3. Pipeline Runner (`run_labeling.py`)

A script to run the entire pipeline:
- Run the labeling tool
- Analyze the labeled dataset
- Prepare the dataset for YOLO training
- Train a YOLO model on the prepared dataset

### 4. Improved Detection Tester (`test_improved_detection.py`)

Tests the improved model on new images:
- Detects the chess board using the segmentation model
- Detects chess pieces using the improved YOLO model
- Maps pieces to chess coordinates with improved handling
- Visualizes the results

## How to Use

### Step 1: Label Chess Pieces

```bash
python chess_board_detection/piece_detection/run_labeling.py --mode label --image_dir "chess_board_detection/data/real"
```

This will open the labeling tool. For each image:
1. Draw a box around a chess piece
2. Select the piece type from the right panel
3. Repeat for all pieces in the image
4. Click "Save & Next" to move to the next image

### Step 2: Analyze the Labeled Dataset

```bash
python chess_board_detection/piece_detection/run_labeling.py --mode analyze
```

This will show statistics about the labeled dataset, including the number of annotations per class.

### Step 3: Prepare the Dataset for YOLO Training

```bash
python chess_board_detection/piece_detection/run_labeling.py --mode prepare
```

This will organize the labeled data into train/val splits and create a data.yaml file for YOLO training.

### Step 4: Train a YOLO Model

```bash
python chess_board_detection/piece_detection/run_labeling.py --mode train --epochs 100 --batch_size 16
```

This will train a YOLO model on the prepared dataset.

### Step 5: Test the Improved Model

```bash
python chess_board_detection/piece_detection/test_improved_detection.py --yolo_model "chess_board_detection/piece_detection/models/yolov8n_latest/train/weights/best.pt" --input "path/to/test/image.jpg"
```

This will test the improved model on a new image and visualize the results.

### Run the Entire Pipeline

To run all steps in sequence:

```bash
python chess_board_detection/piece_detection/run_labeling.py --mode all --epochs 100
```

## Labeling Tips

1. **Be Consistent**: Label all pieces of the same type consistently
2. **Tight Bounding Boxes**: Draw boxes tightly around each piece
3. **Label All Pieces**: Make sure to label all pieces in each image
4. **Check Annotations**: Review your annotations before saving
5. **Empty Squares**: Don't label empty squares (the model will learn to ignore them)

## Improvements Made

The improved detection system addresses the following issues:

1. **Better Training Data**: Using real images with manual annotations
2. **Improved Mapping**: More robust mapping of pieces to chess coordinates
3. **Confidence Handling**: Better handling of confidence scores
4. **Visualization**: Enhanced visualization of detection results

## Troubleshooting

- **No Chess Board Detected**: Make sure the chess board is clearly visible in the image
- **Missing Pieces**: Try lowering the confidence threshold
- **Misclassified Pieces**: Add more training examples of the problematic piece types
- **Incorrect Mapping**: Check if the chess board is properly normalized

## Next Steps

After improving the piece detection, consider:
1. Integrating with the chess board detection system
2. Implementing a full chess position analyzer
3. Adding support for non-standard chess pieces
4. Improving performance on low-light or blurry images
