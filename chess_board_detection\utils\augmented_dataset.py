"""
Dataset class for loading pre-augmented data.
"""

import os
import json
import torch
import numpy as np
from torch.utils.data import Dataset
import glob

class AugmentedDataset(Dataset):
    """
    Dataset for loading pre-augmented data that was generated and saved to disk.
    """
    def __init__(self, augmented_data_dir, filter_valid=True):
        """
        Initialize the dataset.
        
        Args:
            augmented_data_dir: Directory containing the augmented data
            filter_valid: Whether to filter out samples with invalid corners
        """
        self.augmented_data_dir = augmented_data_dir
        self.filter_valid = filter_valid
        
        # Load annotations
        annotation_path = os.path.join(augmented_data_dir, "augmented_annotations.json")
        with open(annotation_path, 'r') as f:
            self.annotations = json.load(f)
        
        # Get all sample directories
        self.sample_dirs = []
        for sample_id, annotation in self.annotations.items():
            # Skip invalid samples if filtering is enabled
            if filter_valid and not annotation.get('valid', True):
                continue
            
            # Find the corresponding directory
            sample_dir = os.path.join(augmented_data_dir, sample_id)
            if os.path.exists(sample_dir):
                self.sample_dirs.append(sample_dir)
        
        print(f"Loaded {len(self.sample_dirs)} augmented samples from {augmented_data_dir}")
    
    def __len__(self):
        return len(self.sample_dirs)
    
    def __getitem__(self, idx):
        """
        Get a sample from the dataset.
        
        Args:
            idx: Index of the sample
        
        Returns:
            Dictionary containing 'image', 'mask', 'corner_heatmaps', and 'corners'
        """
        sample_dir = self.sample_dirs[idx]
        
        # Load image
        image_path = os.path.join(sample_dir, "image.pt")
        image = torch.load(image_path)
        
        # Load mask
        mask_path = os.path.join(sample_dir, "mask.pt")
        mask = torch.load(mask_path)
        
        # Load corners
        corners_path = os.path.join(sample_dir, "corners.pt")
        corners = torch.load(corners_path)
        
        # Load corner heatmaps
        heatmaps_path = os.path.join(sample_dir, "corner_heatmaps.pt")
        corner_heatmaps = torch.load(heatmaps_path)
        
        return {
            'image': image,
            'mask': mask,
            'corners': corners,
            'corner_heatmaps': corner_heatmaps
        }

def create_augmented_dataloaders(augmented_data_dir, batch_size=4, val_split=0.2, filter_valid=True, shuffle=True):
    """
    Create training and validation dataloaders from pre-augmented data.
    
    Args:
        augmented_data_dir: Directory containing the augmented data
        batch_size: Batch size for the dataloaders
        val_split: Fraction of data to use for validation
        filter_valid: Whether to filter out samples with invalid corners
        shuffle: Whether to shuffle the data
    
    Returns:
        train_loader, val_loader
    """
    # Create dataset
    dataset = AugmentedDataset(augmented_data_dir, filter_valid=filter_valid)
    
    # Split into training and validation
    dataset_size = len(dataset)
    val_size = int(dataset_size * val_split)
    train_size = dataset_size - val_size
    
    train_dataset, val_dataset = torch.utils.data.random_split(
        dataset, [train_size, val_size]
    )
    
    # Create dataloaders
    train_loader = torch.utils.data.DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=0
    )
    
    val_loader = torch.utils.data.DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=0
    )
    
    print(f"Created dataloaders with {train_size} training samples and {val_size} validation samples")
    
    return train_loader, val_loader
