"""
Enhanced data augmentation for chess board detection (v5).
This version includes more aggressive augmentation to improve model generalization.
"""

import albumentations as A
from albumentations.pytorch import ToTensorV2
import numpy as np
import cv2


def get_training_augmentation():
    """
    Returns augmentation pipeline for training.

    This version includes more aggressive augmentation to improve model generalization:
    - More aggressive perspective changes
    - More diverse lighting conditions
    - Noise and blur to simulate different image qualities
    - Grid distortion to simulate different camera angles
    """
    return <PERSON><PERSON>([
        # Spatial augmentations
        A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
        A.Affine(scale=(0.8, 1.2), translate_percent=(0.1, 0.1), rotate=(-30, 30), p=0.8),  # Replaced ShiftScaleRotate with Affine
        A.Perspective(scale=(0.05, 0.15), p=0.7),  # More aggressive perspective changes
        A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.5),  # Simulate different camera angles

        # Color augmentations
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
        <PERSON><PERSON>Value(hue_shift_limit=15, sat_shift_limit=25, val_shift_limit=15, p=0.6),
        A.CLAHE(clip_limit=4.0, p=0.4),
        A.RandomGamma(gamma_limit=(80, 120), p=0.5),

        # Noise and blur
        A.GaussNoise(p=0.6),
        A.GaussianBlur(blur_limit=7, p=0.4),
        A.MotionBlur(blur_limit=7, p=0.3),

        # Normalization
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def get_validation_augmentation():
    """
    Returns augmentation pipeline for validation.

    This version includes light augmentation to better evaluate model robustness:
    - Resize to ensure consistent input size
    - Light color adjustments to simulate different lighting conditions
    """
    return A.Compose([
        A.Resize(height=256, width=256),
        A.RandomBrightnessContrast(brightness_limit=0.1, contrast_limit=0.1, p=0.5),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def get_test_augmentation():
    """
    Returns augmentation pipeline for testing.

    This version only includes resizing and normalization:
    - Resize to ensure consistent input size
    - Normalization to match training data
    """
    return A.Compose([
        A.Resize(height=256, width=256),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def visualize_augmentation(image, mask, keypoints, augmentation):
    """
    Visualize the effect of augmentation on an image, mask, and keypoints.

    Args:
        image: Input image (H, W, C)
        mask: Segmentation mask (H, W)
        keypoints: List of keypoints [(x, y), ...]
        augmentation: Albumentations augmentation pipeline

    Returns:
        augmented_image: Augmented image
        augmented_mask: Augmented mask
        augmented_keypoints: Augmented keypoints
    """
    # Convert keypoints to Albumentations format
    keypoints_albu = [{'x': kp[0], 'y': kp[1]} for kp in keypoints]

    # Apply augmentation
    augmented = augmentation(image=image, mask=mask, keypoints=keypoints_albu)

    # Extract augmented data
    augmented_image = augmented['image']
    augmented_mask = augmented['mask']
    augmented_keypoints = [(kp['x'], kp['y']) for kp in augmented['keypoints']]

    return augmented_image, augmented_mask, augmented_keypoints


def create_heatmaps_from_keypoints(keypoints, height, width, sigma=5):
    """
    Create heatmaps from keypoints.

    Args:
        keypoints: List of keypoints [(x, y), ...]
        height: Height of the heatmap
        width: Width of the heatmap
        sigma: Standard deviation of the Gaussian kernel

    Returns:
        heatmaps: Heatmaps (4, H, W)
    """
    heatmaps = np.zeros((4, height, width), dtype=np.float32)

    for i, (x, y) in enumerate(keypoints):
        if i >= 4:  # Only use the first 4 keypoints
            break

        # Skip invalid keypoints
        if x < 0 or y < 0 or x >= width or y >= height:
            continue

        # Create a Gaussian heatmap
        x, y = int(x), int(y)

        # Gaussian range
        x_min = max(0, x - 3*sigma)
        x_max = min(width, x + 3*sigma + 1)
        y_min = max(0, y - 3*sigma)
        y_max = min(height, y + 3*sigma + 1)

        # X and Y coordinates
        x_grid, y_grid = np.meshgrid(np.arange(x_min, x_max), np.arange(y_min, y_max))

        # Gaussian function
        gaussian = np.exp(-((x_grid - x)**2 + (y_grid - y)**2) / (2 * sigma**2))

        # Add to heatmap
        heatmaps[i, y_min:y_max, x_min:x_max] = gaussian

    return heatmaps
