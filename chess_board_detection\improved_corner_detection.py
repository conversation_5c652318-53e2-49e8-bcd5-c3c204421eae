"""
Improved corner detection script that enforces corners to be detected at the edges
of the segmented chess board region.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import json
from scipy import ndimage
from skimage.measure import regionprops

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """Apply basic image enhancement."""
    # Convert to float32 for processing
    image_float = image.astype(np.float32) / 255.0
    
    # Apply contrast stretching
    p2, p98 = np.percentile(image_float, (2, 98))
    enhanced = np.clip((image_float - p2) / (p98 - p2), 0, 1)
    
    # Convert back to uint8
    enhanced = (enhanced * 255).astype(np.uint8)
    
    return enhanced

def normalize_for_model(image):
    """Normalize image for model input."""
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)
    
    return input_tensor

def process_segmentation_mask(segmentation_mask, threshold=0.5):
    """
    Process the segmentation mask to get the chess board region.
    Returns the bounding box of the largest connected component.
    """
    # Threshold the segmentation mask
    binary_mask = (segmentation_mask > threshold).astype(np.uint8)

    # Find connected components
    labeled_mask, num_features = ndimage.label(binary_mask)

    if num_features == 0:
        return None

    # Get properties of the labeled regions
    regions = regionprops(labeled_mask)

    # Find the largest region (by area)
    if not regions:
        return None
    
    largest_region = max(regions, key=lambda r: r.area)
    
    # Get bounding box (min_row, min_col, max_row, max_col)
    bbox = largest_region.bbox
    
    # Convert to (x, y, width, height) format
    x = bbox[1]
    y = bbox[0]
    width = bbox[3] - bbox[1]
    height = bbox[2] - bbox[0]
    
    return (x, y, width, height)

def detect_corners_at_region_edges(heatmaps, region_bbox, threshold=0.3, edge_margin=10):
    """
    Detect corners specifically at the edges of the segmented region.
    
    Args:
        heatmaps: Corner heatmaps tensor [1, 4, H, W]
        region_bbox: Region bounding box (x, y, width, height)
        threshold: Detection threshold
        edge_margin: Margin from the edge to consider (in pixels)
        
    Returns:
        keypoints: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    """
    # Extract region coordinates
    x, y, width, height = region_bbox
    
    # Define the four corner regions (top-left, top-right, bottom-right, bottom-left)
    corner_regions = [
        # Top-left: (x, y, margin, margin)
        (x, y, edge_margin, edge_margin),
        # Top-right: (x+width-margin, y, margin, margin)
        (x+width-edge_margin, y, edge_margin, edge_margin),
        # Bottom-right: (x+width-margin, y+height-margin, margin, margin)
        (x+width-edge_margin, y+height-edge_margin, edge_margin, edge_margin),
        # Bottom-left: (x, y+height-margin, margin, margin)
        (x, y+height-edge_margin, edge_margin, edge_margin)
    ]
    
    # Process each heatmap
    keypoints = []
    
    # Handle batch dimension - heatmaps shape is [1, 4, H, W]
    for i in range(heatmaps.shape[1]):
        heatmap = heatmaps[0, i].cpu().numpy()
        
        # Create a mask for the entire region
        region_mask = np.zeros_like(heatmap)
        region_mask[y:y+height, x:x+width] = 1
        
        # Apply mask to heatmap
        masked_heatmap = heatmap * region_mask
        
        # Find maximum point within the region
        max_val = np.max(masked_heatmap)
        
        if max_val > threshold:
            # Get coordinates of maximum
            max_idx = np.argmax(masked_heatmap)
            max_y, max_x = np.unravel_index(max_idx, masked_heatmap.shape)
            
            # Find the closest corner of the region
            distances = []
            corner_points = [
                (x, y),  # Top-left
                (x+width, y),  # Top-right
                (x+width, y+height),  # Bottom-right
                (x, y+height)  # Bottom-left
            ]
            
            for cx, cy in corner_points:
                dist = np.sqrt((max_x - cx)**2 + (max_y - cy)**2)
                distances.append(dist)
            
            # Use the corner of the region that's closest to the detected point
            closest_corner_idx = np.argmin(distances)
            keypoints.append(corner_points[closest_corner_idx])
        else:
            keypoints.append(None)
    
    return keypoints

def map_to_original_coordinates(keypoints, preprocess_info):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    Accounts for all preprocessing steps: resize, crop, and final resize.
    """
    mapped_keypoints = []
    
    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']
    resized_w, resized_h = preprocess_info['resized_size']
    
    for kp in keypoints:
        if kp is None:
            mapped_keypoints.append(None)
            continue
            
        x, y = kp
        
        # Map from target size to crop size
        x_crop = x * crop_size / target_w
        y_crop = y * crop_size / target_h
        
        # Map from crop to resized image
        x_resized = x_crop + crop_start_x
        y_resized = y_crop + crop_start_y
        
        # Map from resized to original image
        x_original = x_resized * original_w / resized_w
        y_original = y_resized * original_h / resized_h
        
        mapped_keypoints.append((int(x_original), int(y_original)))
    
    return mapped_keypoints

def improved_corner_detection(image_path, model, model_name):
    """
    Apply improved corner detection that enforces corners to be at the edges
    of the segmented chess board region.
    """
    # Preprocess image
    preprocessed_image, preprocess_info = preprocess_image(image_path)
    enhanced_image = enhance_image(preprocessed_image)
    
    # Normalize for model
    input_tensor = normalize_for_model(enhanced_image)
    
    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation and heatmaps
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()
    heatmaps = torch.sigmoid(outputs['corner_heatmaps'])
    
    # Process segmentation to get chess board region
    region_bbox = process_segmentation_mask(segmentation[0, 0])
    
    if region_bbox is None:
        print(f"No chess board region detected in the image for {model_name}")
        return None
    
    # Detect corners at the edges of the region
    keypoints = detect_corners_at_region_edges(heatmaps, region_bbox)
    
    # Map keypoints back to original image
    original_keypoints = map_to_original_coordinates(keypoints, preprocess_info)
    
    return {
        'model_name': model_name,
        'preprocessed_image': enhanced_image,
        'segmentation': segmentation,
        'heatmaps': heatmaps.cpu().numpy(),
        'region_bbox': region_bbox,
        'keypoints': keypoints,
        'original_keypoints': original_keypoints,
        'original_image': preprocess_info['original_image']
    }

def visualize_improved_detection(image, segmentation, heatmaps, region_bbox, keypoints, output_path):
    """
    Visualize the results of improved corner detection.
    """
    # Create figure with 2x3 subplots
    fig, axs = plt.subplots(2, 3, figsize=(15, 10))
    
    # Plot original image
    axs[0, 0].imshow(image)
    axs[0, 0].set_title('Preprocessed Image')
    axs[0, 0].axis('off')
    
    # Plot segmentation mask
    axs[0, 1].imshow(segmentation[0, 0], cmap='viridis')
    axs[0, 1].set_title('Segmentation Mask')
    axs[0, 1].axis('off')
    
    # Plot region and keypoints
    axs[0, 2].imshow(image)
    x, y, w, h = region_bbox
    rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='r', facecolor='none')
    axs[0, 2].add_patch(rect)
    axs[0, 2].set_title('Detected Region')
    axs[0, 2].axis('off')
    
    # Plot individual heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        if i < 3:
            ax = axs[1, i]
        else:
            ax = axs[1, 2]  # Use the last subplot for the 4th heatmap
            
        ax.imshow(heatmaps[0, i], cmap='hot')
        ax.set_title(f'{corner_names[i]} Heatmap')
        
        # Add region rectangle to heatmap
        rect = plt.Rectangle((x, y), w, h, linewidth=1, edgecolor='g', facecolor='none')
        ax.add_patch(rect)
        
        # Mark the detected keypoint
        if keypoints[i] is not None:
            kx, ky = keypoints[i]
            ax.scatter(kx, ky, c='b', s=30)
        
        ax.axis('off')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

def visualize_original_with_keypoints(original_image, keypoints, output_path, model_name):
    """
    Visualize the original image with detected keypoints.
    """
    plt.figure(figsize=(10, 8))
    plt.imshow(original_image)
    
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['r', 'g', 'b', 'y']
    
    for i, (kp, name, color) in enumerate(zip(keypoints, corner_names, colors)):
        if kp is not None:
            x, y = kp
            plt.scatter(x, y, c=color, s=100, marker='o')
            plt.text(x+15, y+15, name, color=color, fontsize=12, weight='bold')
    
    plt.title(f'Improved Chess Board Corner Detection - {model_name}', fontsize=16)
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\improved_corner_detection"
    
    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each model
    results = {}
    
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} with improved corner detection...")
        
        # Load model
        model = load_model(model_path)
        
        # Apply improved corner detection
        detection_results = improved_corner_detection(image_path, model, model_name)
        
        if detection_results is None:
            continue
        
        # Store results
        results[model_name] = detection_results
        
        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_improved_detection.png")
        visualize_improved_detection(
            detection_results['preprocessed_image'],
            detection_results['segmentation'],
            detection_results['heatmaps'],
            detection_results['region_bbox'],
            detection_results['keypoints'],
            output_path
        )
        
        # Save original image with keypoints
        original_output_path = os.path.join(output_dir, f"{model_name}_original_with_keypoints.png")
        visualize_original_with_keypoints(
            detection_results['original_image'],
            detection_results['original_keypoints'],
            original_output_path,
            model_name
        )
        
        print(f"Improved detection visualization saved to: {output_path}")
        print(f"Original image with keypoints saved to: {original_output_path}")
        
        # Print corner coordinates
        print(f"\nCorner coordinates for {model_name}:")
        corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        for i, (name, kp) in enumerate(zip(corner_names, detection_results['original_keypoints'])):
            if kp is not None:
                print(f"{name}: {kp}")
            else:
                print(f"{name}: Not detected")
    
    print("\nAll processing completed!")

if __name__ == "__main__":
    main()
