@echo off
echo Running v5.1 improved training (continuing from v4 best model)...
echo This version includes significant improvements to address the issues identified in Phase 1:
echo 1. Enhanced peak sharpening module with stronger suppression of secondary peaks
echo 2. Improved geometric refinement with better spatial constraints
echo 3. More robust detection recovery mechanism
echo 4. Increased regularization with batch normalization and spatial dropout
echo 5. More aggressive loss functions targeting the peak-to-second ratio

python "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_v5_improved.py" ^
--continue_from_v4 ^
--data_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real" ^
--annotation_file "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json" ^
--output_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection" ^
--epochs_phase1 40 ^
--epochs_phase2 80 ^
--lr_phase1 0.001 ^
--lr_phase2 0.0005 ^
--dropout_rate 0.3 ^
--weight_decay 1e-4 ^
--batch_size 4 ^
--save_interval 5 ^
--cpu ^
--use_scheduler

echo Training completed!
pause
