# Chess Piece Detection Feedback Loop

This system implements a feedback loop for continuously improving chess piece detection through user feedback and model fine-tuning.

## Overview

The feedback loop consists of three main components:

1. **Feedback Collection**: Collects user feedback on incorrect detections
2. **Model Fine-Tuning**: Uses the feedback to fine-tune the model
3. **Feedback Loop Automation**: Orchestrates the entire process

## Getting Started

### Prerequisites

- Python 3.8+
- PyTorch with CUDA support
- Ultralytics YOLO package
- OpenCV
- NumPy

### Installation

```bash
pip install ultralytics opencv-python numpy
```

### Directory Structure

```
├── collect_feedback.py      # Tool for collecting feedback
├── finetune_model.py        # Script for fine-tuning the model
├── feedback_loop.py         # Main script for running the feedback loop
├── feedback_database.json   # Database of collected feedback
├── model_versions/          # Directory for storing model versions
│   └── version_history.json # Record of model versions
├── feedback_training_data/  # Directory for training data
│   ├── images/              # Images for fine-tuning
│   └── labels/              # Labels for fine-tuning
└── runs/                    # Training outputs
    └── finetune/            # Fine-tuning results
```

## Usage

### 1. Collecting Feedback Manually

To collect feedback on a single image:

```bash
python collect_feedback.py --model runs/detect/train/weights/best.pt --image path/to/image.jpg
```

This will:
- Run inference on the image
- Display the detections
- Allow you to provide feedback on incorrect detections
- Save the feedback to the database

### 2. Fine-Tuning the Model Manually

To fine-tune the model using collected feedback:

```bash
python finetune_model.py --model runs/detect/train/weights/best.pt --feedback feedback_database.json --epochs 10
```

This will:
- Prepare training data from the feedback
- Fine-tune the model
- Save the fine-tuned model
- Evaluate the model if a test directory is provided

### 3. Running the Complete Feedback Loop

To run the complete feedback loop:

```bash
python feedback_loop.py --base-model runs/detect/train/weights/best.pt --image-dir path/to/images --test-dir path/to/test_images
```

This will:
- Process each image in the directory
- Collect feedback on incorrect detections
- Fine-tune the model when enough feedback is collected
- Create a new model version
- Clear the feedback database for the next iteration

## Feedback Format

When providing feedback, use the following format:

```
<detection_number> <correct_class_name>
```

For example:
```
3 black_bishop
```

This indicates that detection #3 should be a black bishop.

Available class names:
- white_pawn
- white_knight
- white_bishop
- white_rook
- white_queen
- white_king
- black_pawn
- black_knight
- black_bishop
- black_rook
- black_queen
- black_king

## Model Versioning

The system maintains a history of model versions in `model_versions/version_history.json`. Each version includes:

- Version number
- Model path
- Base model
- Creation timestamp
- Feedback count used for training
- Performance metrics

## Best Practices

1. **Collect Diverse Feedback**: Include a variety of chess pieces, positions, and lighting conditions
2. **Focus on Difficult Cases**: Prioritize feedback on pieces that are frequently misclassified
3. **Verify Fine-Tuned Models**: Always test the fine-tuned model on new images
4. **Maintain Version History**: Keep track of model improvements over time
5. **Iterate Gradually**: Fine-tune in small increments rather than large batches

## Troubleshooting

- **Model Not Improving**: Try increasing the number of epochs or collecting more diverse feedback
- **Overfitting**: If the model performs well on training data but poorly on new images, try reducing epochs or using more diverse feedback
- **Training Errors**: Check that the feedback format is correct and that all referenced images exist

## Advanced Configuration

You can customize the feedback loop by adjusting these parameters:

- `--epochs`: Number of training epochs (default: 10)
- `--batch`: Batch size for training (default: 16)
- `--min-feedback`: Minimum feedback items required for fine-tuning (default: 5)

## Example Workflow

1. Start with a base model
2. Run the feedback loop on a set of images
3. Provide feedback on misclassifications
4. Let the system fine-tune the model
5. Use the new model version for future detections
6. Repeat the process to continuously improve the model
