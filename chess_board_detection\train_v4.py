"""
<PERSON><PERSON><PERSON> to train the enhanced chess board detection model with v4 configuration.
This script will load the best checkpoint from v3 and continue training with improved loss functions.
"""

import os
import argparse
import subprocess
import sys
import torch
import json
import matplotlib.pyplot as plt
import numpy as np

from config import MODELS_DIR, DATA_DIR, DEVICE
from models.enhanced_unet import EnhancedChessBoardUNet
from enhanced_loss_v4 import EnhancedDiceLoss, ImprovedCornerFocusedHeatmapLoss, ImprovedGeometricConsistencyLoss
from utils.real_dataset import get_data_loaders as get_real_data_loaders
from utils.dataset import get_data_loaders as get_synthetic_data_loaders


def load_best_v3_checkpoint(checkpoints_dir):
    """
    Load the best checkpoint from v3 training.

    Args:
        checkpoints_dir: Directory containing v3 checkpoints

    Returns:
        model: Loaded model
        optimizer: Loaded optimizer
        epoch: Last epoch
        history: Training history
    """
    # Check if best model exists
    best_model_path = os.path.join(checkpoints_dir, 'best_model.pth')
    if not os.path.exists(best_model_path):
        print(f"Error: Best model not found at {best_model_path}")
        return None, None, 0, None

    # Find the latest checkpoint
    checkpoints = [f for f in os.listdir(checkpoints_dir) if f.startswith('checkpoint_epoch_')]
    if not checkpoints:
        print("No checkpoints found, using best model only")
        model = EnhancedChessBoardUNet(n_channels=3, bilinear=True)
        model.load_state_dict(torch.load(best_model_path))
        return model, None, 0, None

    # Sort checkpoints by epoch number
    checkpoints.sort(key=lambda x: int(x.split('_')[-1].split('.')[0]))
    latest_checkpoint = checkpoints[-1]
    checkpoint_path = os.path.join(checkpoints_dir, latest_checkpoint)

    # Load checkpoint
    print(f"Loading checkpoint: {checkpoint_path}")
    checkpoint = torch.load(checkpoint_path)

    # Create model and load state dict
    model = EnhancedChessBoardUNet(n_channels=3, bilinear=True)
    model.load_state_dict(checkpoint['model_state_dict'])

    # Get optimizer, epoch, and history
    optimizer_state_dict = checkpoint.get('optimizer_state_dict')
    epoch = checkpoint.get('epoch', 0)
    history = checkpoint.get('history', None)

    return model, optimizer_state_dict, epoch, history


def main():
    """
    Main function to run the enhanced model training with v4 configuration.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train enhanced chess board detection model with v4 configuration')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR, help='Data directory')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--epochs', type=int, default=30, help='Number of epochs for training')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size for training')
    parser.add_argument('--lr', type=float, default=0.0005, help='Learning rate for training')
    parser.add_argument('--heatmap_weight', type=float, default=1.5, help='Weight for heatmap loss')
    parser.add_argument('--geometric_weight', type=float, default=0.4, help='Weight for geometric loss')
    parser.add_argument('--separation_weight', type=float, default=0.6,
                        help='Weight for separation loss')
    parser.add_argument('--peak_separation_weight', type=float, default=0.5,
                        help='Weight for peak separation loss')
    parser.add_argument('--edge_suppression_weight', type=float, default=0.7,
                        help='Weight for edge suppression loss')
    parser.add_argument('--peak_enhancement_weight', type=float, default=0.5,
                        help='Weight for peak enhancement loss')
    parser.add_argument('--peak_to_second_ratio_weight', type=float, default=1.0,
                        help='Weight for peak-to-second ratio loss')
    parser.add_argument('--detection_rate_weight', type=float, default=1.0,
                        help='Weight for detection rate loss')
    parser.add_argument('--save_interval', type=int, default=5,
                        help='Interval to save model checkpoints')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--start_from_epoch', type=int, default=94,
                        help='Epoch to start from (use checkpoint closest to this)')
    args = parser.parse_args()

    # Print configuration
    print("=== Training Enhanced Model with v4 Configuration ===")
    print(f"Output directory: {args.output_dir}")
    print(f"Data directory: {args.data_dir}")
    print(f"Epochs: {args.epochs}")
    print(f"Batch size: {args.batch_size}")
    print(f"Learning rate: {args.lr}")
    print(f"Heatmap weight: {args.heatmap_weight}")
    print(f"Geometric weight: {args.geometric_weight}")
    print(f"Separation weight: {args.separation_weight}")
    print(f"Peak separation weight: {args.peak_separation_weight}")
    print(f"Edge suppression weight: {args.edge_suppression_weight}")
    print(f"Peak enhancement weight: {args.peak_enhancement_weight}")
    print(f"Peak-to-second ratio weight: {args.peak_to_second_ratio_weight}")
    print(f"Detection rate weight: {args.detection_rate_weight}")
    print(f"Save interval: {args.save_interval}")
    print(f"Using CPU: {args.cpu}")
    print(f"Starting from epoch: {args.start_from_epoch}")

    # Ensure the v4 directories exist
    checkpoints_dir_v3 = os.path.join(args.output_dir, 'checkpoints', 'v3')
    checkpoints_dir_v4 = os.path.join(args.output_dir, 'checkpoints', 'v4')
    logs_dir_v4 = os.path.join(args.output_dir, 'logs', 'v4')
    vis_dir_v4 = os.path.join(args.output_dir, 'visualizations', 'v4')

    os.makedirs(checkpoints_dir_v4, exist_ok=True)
    os.makedirs(logs_dir_v4, exist_ok=True)
    os.makedirs(vis_dir_v4, exist_ok=True)

    print(f"Created v4 directories in {args.output_dir}")

    # Load the best v3 checkpoint
    model, optimizer_state_dict, last_epoch, history = load_best_v3_checkpoint(checkpoints_dir_v3)
    if model is None:
        print("Failed to load v3 checkpoint. Exiting.")
        return

    # Move model to device
    device = torch.device('cpu') if args.cpu else DEVICE
    model = model.to(device)
    print(f"Model loaded and moved to {device}")

    # Create optimizer and load state if available
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)
    if optimizer_state_dict is not None:
        optimizer.load_state_dict(optimizer_state_dict)
        # Update learning rate
        for param_group in optimizer.param_groups:
            param_group['lr'] = args.lr
        print(f"Optimizer loaded with updated learning rate: {args.lr}")

    # Create loss functions
    criterion_seg = EnhancedDiceLoss()
    criterion_heatmap = ImprovedCornerFocusedHeatmapLoss(
        separation_weight=args.separation_weight,
        peak_separation_weight=args.peak_separation_weight,
        edge_suppression_weight=args.edge_suppression_weight,
        peak_enhancement_weight=args.peak_enhancement_weight,
        peak_to_second_ratio_weight=args.peak_to_second_ratio_weight,
        detection_rate_weight=args.detection_rate_weight
    )
    criterion_geometric = ImprovedGeometricConsistencyLoss(weight=1.0)  # Weight applied in train_enhanced.py

    # Get data loaders
    print("Loading data...")
    real_data_dir = os.path.join(args.data_dir, 'real')
    annotation_file = os.path.join(args.data_dir, 'real_annotations.json')

    if os.path.exists(annotation_file) and os.path.exists(real_data_dir):
        print("Using real dataset...")
        dataloaders = get_real_data_loaders(
            data_dir=real_data_dir,
            annotation_file=annotation_file,
            batch_size=args.batch_size
        )
    else:
        # Fall back to synthetic data
        synthetic_data_dir = os.path.join(args.data_dir, 'synthetic')
        if os.path.exists(synthetic_data_dir):
            print("Real dataset not found. Using synthetic dataset...")
            dataloaders = get_synthetic_data_loaders(
                data_dir=synthetic_data_dir,
                batch_size=args.batch_size
            )
        else:
            print("No dataset found. Exiting.")
            return

    # Create command for training
    train_cmd = [
        sys.executable, 'train_enhanced.py',
        '--data_dir', args.data_dir,
        '--output_dir', args.output_dir,
        '--epochs', str(args.epochs),
        '--batch_size', str(args.batch_size),
        '--lr', str(args.lr),
        '--heatmap_weight', str(args.heatmap_weight),
        '--geometric_weight', str(args.geometric_weight),
        '--separation_weight', str(args.separation_weight),
        '--peak_separation_weight', str(args.peak_separation_weight),
        '--edge_suppression_weight', str(args.edge_suppression_weight),
        '--peak_enhancement_weight', str(args.peak_enhancement_weight),
        '--peak_to_second_ratio_weight', str(args.peak_to_second_ratio_weight),
        '--detection_rate_weight', str(args.detection_rate_weight),
        '--save_interval', str(args.save_interval),
        '--v4',  # Flag to use v4 loss functions
        '--continue_from_v3',  # Flag to continue from v3 checkpoint
        '--start_from_epoch', str(args.start_from_epoch)
    ]

    if args.cpu:
        train_cmd.append('--cpu')

    # Run the training script
    print(f"Running command: {' '.join(train_cmd)}")
    try:
        subprocess.run(train_cmd)
    except Exception as e:
        print(f"Error during training: {e}")
        print("Training failed, but we'll still create the necessary directories for future use.")

    print("Training completed!")
    print(f"Model data saved to v4 folders in {args.output_dir}:")
    print(f"  - Checkpoints: {checkpoints_dir_v4}")
    print(f"  - Logs: {logs_dir_v4}")
    print(f"  - Visualizations: {vis_dir_v4}")
    print("\nTo test the model, run:")
    print(f"python inference_enhanced_v4.py --image_path <path_to_image> --model_path {os.path.join(checkpoints_dir_v4, 'best_model.pth')} --use_post_processing")


if __name__ == "__main__":
    main()
