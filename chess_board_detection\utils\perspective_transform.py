"""
Utility functions for perspective transformation of chess boards.
"""

import numpy as np
import cv2
from PIL import Image

def get_perspective_transform(image, corners, output_size=(512, 512)):
    """
    Apply perspective transform to normalize a chess board.
    
    Args:
        image: Input image (numpy array)
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
                 in order: top-left, top-right, bottom-right, bottom-left
        output_size: Size of the output image (width, height)
        
    Returns:
        normalized_board: Normalized chess board image
        homography: Homography matrix
    """
    # Convert to numpy array if needed
    if isinstance(image, Image.Image):
        image = np.array(image)
    
    # Convert corners to numpy array
    src_points = np.array(corners, dtype=np.float32)
    
    # Define destination points (normalized square)
    dst_points = np.array([
        [0, 0],                          # top-left
        [output_size[0], 0],             # top-right
        [output_size[0], output_size[1]], # bottom-right
        [0, output_size[1]]              # bottom-left
    ], dtype=np.float32)
    
    # Calculate homography matrix
    homography, _ = cv2.findHomography(src_points, dst_points)
    
    # Apply perspective transform
    normalized_board = cv2.warpPerspective(image, homography, output_size)
    
    return normalized_board, homography

def map_points_to_normalized_board(points, homography, output_size=(512, 512)):
    """
    Map points from the original image to the normalized board.
    
    Args:
        points: List of points [(x1, y1), (x2, y2), ...] in the original image
        homography: Homography matrix
        output_size: Size of the normalized board
        
    Returns:
        normalized_points: List of points in the normalized board
    """
    # Convert points to numpy array
    points = np.array(points, dtype=np.float32).reshape(-1, 1, 2)
    
    # Apply perspective transform
    normalized_points = cv2.perspectiveTransform(points, homography)
    
    # Reshape to list of tuples
    normalized_points = normalized_points.reshape(-1, 2).tolist()
    
    return normalized_points

def map_points_from_normalized_board(points, homography, original_size):
    """
    Map points from the normalized board to the original image.
    
    Args:
        points: List of points [(x1, y1), (x2, y2), ...] in the normalized board
        homography: Homography matrix
        original_size: Size of the original image (width, height)
        
    Returns:
        original_points: List of points in the original image
    """
    # Convert points to numpy array
    points = np.array(points, dtype=np.float32).reshape(-1, 1, 2)
    
    # Calculate inverse homography
    inv_homography = np.linalg.inv(homography)
    
    # Apply inverse perspective transform
    original_points = cv2.perspectiveTransform(points, inv_homography)
    
    # Reshape to list of tuples
    original_points = original_points.reshape(-1, 2).tolist()
    
    return original_points
