# Segmentation-Guided Corner Detection for Chess Board Detection

This document explains the segmentation-guided corner detection approach implemented in the v4 model.

## Problem Statement

In the previous versions of the chess board detection model, the segmentation and corner detection components operated independently, with limited information sharing between them. This led to situations where:

1. The segmentation correctly identified the chess board area, but the corner detection failed to find corners at the boundaries of this area.
2. The corner detection focused on edges rather than corners, leading to poor detection results.
3. The model detected corners in areas that were not part of the chess board.

## Solution: Segmentation-Guided Corner Detection

The segmentation-guided corner detection approach integrates the segmentation and corner detection components, ensuring that:

1. Corner detection is constrained to the boundaries of the segmented chess board area.
2. The model focuses on detecting corners at the intersections of the segmentation boundary.
3. The segmentation information is used to guide the corner detection process.

## Implementation Details

### 1. Segmentation-Guided Corner Detection Loss

The `SegmentationGuidedCornerDetectionLoss` class enforces consistency between segmentation and corner heatmaps:

```python
class SegmentationGuidedCornerDetectionLoss(nn.Module):
    """
    Loss function that enforces consistency between segmentation and corner heatmaps.
    """
    def __init__(self, weight=1.0, boundary_width=5):
        super(SegmentationGuidedCornerDetectionLoss, self).__init__()
        self.weight = weight
        self.boundary_width = boundary_width
    
    def forward(self, segmentation, heatmaps):
        """
        Calculate the segmentation-guided corner detection loss.
        """
        # Create a boundary mask from segmentation
        # Use gradient magnitude to find edges
        grad_x = torch.abs(seg[:, 1:] - seg[:, :-1])
        grad_y = torch.abs(seg[1:, :] - seg[:-1, :])
        boundary = torch.max(grad_x, grad_y)
        
        # Dilate the boundary to create a boundary region
        boundary = F.max_pool2d(boundary.unsqueeze(0).unsqueeze(0), 
                                kernel_size=self.boundary_width, 
                                stride=1, 
                                padding=self.boundary_width//2)[0, 0]
        
        # Penalize heatmap activations that are not on the boundary
        penalty = hm * (1.0 - boundary)
        loss += torch.sum(penalty) / (torch.sum(hm) + 1e-6)
```

This loss function:
- Creates a boundary mask from the segmentation output
- Penalizes heatmap activations that are not on the boundary
- Encourages the model to detect corners at the boundaries of the segmented area

### 2. Post-Processing with Segmentation Guidance

The `segmentation_guided_corner_detection` function uses the segmentation mask to guide corner detection:

```python
def segmentation_guided_corner_detection(segmentation, heatmaps, threshold=0.3):
    """
    Use segmentation mask to guide corner detection.
    """
    # Create a binary mask from segmentation
    binary_mask = (segmentation > 0.5).astype(np.float32)
    
    # Find contours in the binary mask
    contours = cv2.findContours(binary_mask.astype(np.uint8), 
                               cv2.RETR_EXTERNAL, 
                               cv2.CHAIN_APPROX_SIMPLE)[0]
    
    # If we get a quadrilateral, use its corners
    if len(approx_polygon) == 4:
        corners = [(pt[0][0], pt[0][1]) for pt in approx_polygon]
        return corners
    
    # Otherwise, use the heatmaps but constrain to the segmentation boundary
    # Create a distance transform from the boundary
    dist_transform = cv2.distanceTransform(binary_mask.astype(np.uint8), cv2.DIST_L2, 3)
    boundary_mask = (dist_transform < boundary_width).astype(np.float32)
    
    # Apply boundary mask to heatmap
    masked_heatmap = heatmap * boundary_mask
```

This function:
- Creates a binary mask from the segmentation output
- Finds contours in the binary mask
- If the contours form a quadrilateral, uses its corners
- Otherwise, constrains corner detection to the boundary of the segmentation mask

### 3. Integration in the Training Process

The segmentation-guided corner detection is integrated into the training process:

```python
# Calculate losses
seg_loss = criterion_seg(seg_outputs, masks)
# Pass segmentation to heatmap loss for segmentation-guided corner detection
heatmap_loss, heatmap_components = criterion_heatmap(heatmap_outputs, heatmaps, seg_outputs)
geometric_loss = criterion_geometric(heatmap_outputs)
```

This ensures that the segmentation information is used during training to guide corner detection.

## Benefits

1. **Improved Corner Detection**: By constraining corner detection to the boundaries of the segmented area, the model is more likely to detect corners at the correct locations.

2. **Reduced False Positives**: The model is less likely to detect corners in areas that are not part of the chess board.

3. **Better Integration**: The segmentation and corner detection components work together, sharing information and constraints.

4. **Robustness to Challenging Images**: The approach is more robust to challenging images where the chess board is difficult to detect.

## Usage

To train the v4 model with segmentation-guided corner detection:

```bash
python chess_board_detection/train_v4_segmentation_guided.py --data_dir /path/to/data --output_dir /path/to/output
```

To run inference with segmentation-guided corner detection:

```bash
python chess_board_detection/inference_enhanced_v4.py --image_path /path/to/image --model_path /path/to/model --use_segmentation_guidance
```

## Conclusion

The segmentation-guided corner detection approach addresses the limitations of the previous versions by integrating the segmentation and corner detection components. This leads to improved corner detection, reduced false positives, and better overall performance on challenging images.
