"""
Create a clean visualization of chess piece detection with no text labels on bounding boxes,
avoiding duplicate classifications, and using the classification with highest confidence.
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
from pathlib import Path
from ultralytics import YOLO
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, Patch

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def filter_duplicates(boxes, classes, confidences, coords, iou_threshold=0.5):
    """
    Filter out duplicate detections by keeping only the one with highest confidence.
    
    Args:
        boxes: List of bounding boxes
        classes: List of class names
        confidences: List of confidence scores
        coords: List of coordinates (x1, y1, x2, y2)
        iou_threshold: IoU threshold for considering boxes as duplicates
        
    Returns:
        Filtered lists of classes, confidences, and coordinates
    """
    if len(boxes) == 0:
        return [], [], []
    
    # Sort by confidence (highest first)
    indices = np.argsort([-conf for conf in confidences])
    
    filtered_classes = []
    filtered_confidences = []
    filtered_coords = []
    
    for i in indices:
        keep = True
        box_i = coords[i]
        
        for j, box_j in enumerate(filtered_coords):
            # Calculate IoU
            x1_i, y1_i, x2_i, y2_i = box_i
            x1_j, y1_j, x2_j, y2_j = box_j
            
            # Calculate intersection area
            x1_inter = max(x1_i, x1_j)
            y1_inter = max(y1_i, y1_j)
            x2_inter = min(x2_i, x2_j)
            y2_inter = min(y2_i, y2_j)
            
            if x2_inter < x1_inter or y2_inter < y1_inter:
                # No intersection
                continue
                
            inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
            
            # Calculate union area
            box_i_area = (x2_i - x1_i) * (y2_i - y1_i)
            box_j_area = (x2_j - x1_j) * (y2_j - y1_j)
            union_area = box_i_area + box_j_area - inter_area
            
            iou = inter_area / union_area
            
            if iou > iou_threshold:
                keep = False
                break
        
        if keep:
            filtered_classes.append(classes[i])
            filtered_confidences.append(confidences[i])
            filtered_coords.append(coords[i])
    
    return filtered_classes, filtered_confidences, filtered_coords

def visualize_chess_pieces(base_model_path, finetuned_model_path, image_path, output_dir=None, conf_threshold=0.7, iou_threshold=0.85):
    """
    Create a clean visualization of chess piece detection with no text labels on bounding boxes.
    
    Args:
        base_model_path: Path to the base model
        finetuned_model_path: Path to the fine-tuned model
        image_path: Path to the test image
        output_dir: Directory to save visualization results
        conf_threshold: Confidence threshold for predictions
        iou_threshold: IoU threshold for NMS and duplicate filtering
    """
    # Set up output directory
    if output_dir is None:
        output_dir = "clean_visualization"
    os.makedirs(output_dir, exist_ok=True)
    
    # Load models
    print(f"Loading base model from {base_model_path}")
    base_model = YOLO(base_model_path)
    
    print(f"Loading fine-tuned model from {finetuned_model_path}")
    finetuned_model = YOLO(finetuned_model_path)
    
    # Class names and colors
    class_names = ['white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king', 
                   'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king']
    
    # Define colors for each class (white pieces in light colors, black pieces in dark colors)
    colors = {
        'white_pawn': (200, 200, 200),    # Light gray
        'white_knight': (173, 216, 230),  # Light blue
        'white_bishop': (152, 251, 152),  # Light green
        'white_rook': (255, 182, 193),    # Light pink
        'white_queen': (255, 255, 224),   # Light yellow
        'white_king': (255, 228, 196),    # Light orange
        'black_pawn': (100, 100, 100),    # Dark gray
        'black_knight': (70, 130, 180),   # Dark blue
        'black_bishop': (34, 139, 34),    # Dark green
        'black_rook': (178, 34, 34),      # Dark red
        'black_queen': (218, 165, 32),    # Dark yellow/gold
        'black_king': (139, 69, 19)       # Dark brown
    }
    
    # Convert colors from BGR to RGB for matplotlib
    colors_rgb = {}
    for piece, color in colors.items():
        colors_rgb[piece] = (color[2]/255, color[1]/255, color[0]/255)  # Convert to RGB and normalize to 0-1
    
    print(f"\nProcessing {image_path}")
    
    # Run predictions with both models with higher IoU threshold to avoid duplicates
    base_results = base_model(image_path, conf=conf_threshold, iou=iou_threshold)[0]
    finetuned_results = finetuned_model(image_path, conf=conf_threshold, iou=iou_threshold)[0]
    
    # Get the original image
    img = cv2.imread(image_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # Create copies for drawing
    base_img = img.copy()
    finetuned_img = img.copy()
    
    # Process base model results
    base_boxes = base_results.boxes
    base_classes = []
    base_confidences = []
    base_coords = []
    
    for box in base_boxes:
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        cls_id = int(box.cls[0].item())
        conf = box.conf[0].item()
        
        class_name = class_names[cls_id]
        
        base_classes.append(class_name)
        base_confidences.append(conf)
        base_coords.append((x1, y1, x2, y2))
    
    # Filter out duplicates in base model results
    base_classes, base_confidences, base_coords = filter_duplicates(
        base_boxes, base_classes, base_confidences, base_coords, iou_threshold
    )
    
    # Draw base model results
    for i, ((x1, y1, x2, y2), class_name) in enumerate(zip(base_coords, base_classes)):
        color = colors[class_name]
        # Draw bounding box
        cv2.rectangle(base_img, (x1, y1), (x2, y2), color, 2)
        # Draw only the number (no text label)
        cv2.putText(base_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)  # Black outline
        cv2.putText(base_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)  # White text
    
    # Process fine-tuned model results
    finetuned_boxes = finetuned_results.boxes
    finetuned_classes = []
    finetuned_confidences = []
    finetuned_coords = []
    
    for box in finetuned_boxes:
        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
        cls_id = int(box.cls[0].item())
        conf = box.conf[0].item()
        
        class_name = class_names[cls_id]
        
        finetuned_classes.append(class_name)
        finetuned_confidences.append(conf)
        finetuned_coords.append((x1, y1, x2, y2))
    
    # Filter out duplicates in fine-tuned model results
    finetuned_classes, finetuned_confidences, finetuned_coords = filter_duplicates(
        finetuned_boxes, finetuned_classes, finetuned_confidences, finetuned_coords, iou_threshold
    )
    
    # Draw fine-tuned model results
    for i, ((x1, y1, x2, y2), class_name) in enumerate(zip(finetuned_coords, finetuned_classes)):
        color = colors[class_name]
        # Draw bounding box
        cv2.rectangle(finetuned_img, (x1, y1), (x2, y2), color, 2)
        # Draw only the number (no text label)
        cv2.putText(finetuned_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)  # Black outline
        cv2.putText(finetuned_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)  # White text
    
    # Create side-by-side comparison with legend
    plt.figure(figsize=(24, 12))
    
    # Base model subplot
    plt.subplot(1, 2, 1)
    plt.imshow(base_img)
    plt.title(f"Base Model: {len(base_classes)} detections", fontsize=16)
    plt.axis('off')
    
    # Add base model legend
    base_legend_elements = []
    for i, (cls, conf) in enumerate(zip(base_classes, base_confidences)):
        base_legend_elements.append(
            Patch(facecolor=colors_rgb[cls], edgecolor='black', 
                  label=f"{i+1}: {cls} ({conf:.2f})")
        )
    plt.legend(handles=base_legend_elements, loc='upper right', 
               bbox_to_anchor=(1.0, 1.0), fontsize=10, title="Base Model Detections")
    
    # Fine-tuned model subplot
    plt.subplot(1, 2, 2)
    plt.imshow(finetuned_img)
    plt.title(f"Fine-tuned Model: {len(finetuned_classes)} detections", fontsize=16)
    plt.axis('off')
    
    # Add fine-tuned model legend
    finetuned_legend_elements = []
    for i, (cls, conf) in enumerate(zip(finetuned_classes, finetuned_confidences)):
        finetuned_legend_elements.append(
            Patch(facecolor=colors_rgb[cls], edgecolor='black', 
                  label=f"{i+1}: {cls} ({conf:.2f})")
        )
    plt.legend(handles=finetuned_legend_elements, loc='upper right', 
               bbox_to_anchor=(1.0, 1.0), fontsize=10, title="Fine-tuned Model Detections")
    
    plt.tight_layout()
    
    # Save comparison image
    img_name = os.path.basename(image_path)
    output_path = os.path.join(output_dir, f"clean_{img_name}")
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    # Print detection counts
    print(f"\nDetection counts for {img_name}:")
    print(f"Base model: {len(base_classes)} detections")
    print(f"Fine-tuned model: {len(finetuned_classes)} detections")
    
    # Print class counts
    base_class_counts = {}
    for cls in base_classes:
        base_class_counts[cls] = base_class_counts.get(cls, 0) + 1
    
    finetuned_class_counts = {}
    for cls in finetuned_classes:
        finetuned_class_counts[cls] = finetuned_class_counts.get(cls, 0) + 1
    
    print("\nBase model class counts:")
    for cls, count in sorted(base_class_counts.items()):
        print(f"  {cls}: {count}")
    
    print("\nFine-tuned model class counts:")
    for cls, count in sorted(finetuned_class_counts.items()):
        print(f"  {cls}: {count}")
    
    print(f"\nVisualization saved to {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Create clean visualization of chess piece detection")
    parser.add_argument("--base_model", type=str, default="runs/detect/train/weights/best.pt", help="Path to base model")
    parser.add_argument("--finetuned_model", type=str, default="runs/cls_focused/cls_focused_20250522_133104/weights/best.pt", help="Path to fine-tuned model")
    parser.add_argument("--image", type=str, default="24.jpg", help="Path to test image")
    parser.add_argument("--output_dir", type=str, default="clean_visualization", help="Directory to save visualization results")
    parser.add_argument("--conf", type=float, default=0.7, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.85, help="IoU threshold")
    
    args = parser.parse_args()
    
    # Print system info
    print_system_info()
    
    # Create visualization
    visualize_chess_pieces(
        args.base_model,
        args.finetuned_model,
        args.image,
        args.output_dir,
        args.conf,
        args.iou
    )

if __name__ == "__main__":
    main()
