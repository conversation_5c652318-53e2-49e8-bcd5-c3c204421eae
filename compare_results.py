import cv2
import numpy as np
import matplotlib.pyplot as plt
import os
import sys

def create_comparison_image(original_path, fixed_path, output_path):
    """Create a side-by-side comparison of original and fixed detection images"""
    # Read images
    original_img = cv2.imread(original_path)
    fixed_img = cv2.imread(fixed_path)
    
    # Ensure both images have the same height
    h1, w1 = original_img.shape[:2]
    h2, w2 = fixed_img.shape[:2]
    
    # Resize if needed to match heights
    if h1 != h2:
        scale = h1 / h2
        fixed_img = cv2.resize(fixed_img, (int(w2 * scale), h1))
    
    # Create a combined image with a separator
    separator = np.ones((h1, 10, 3), dtype=np.uint8) * 255  # White separator
    combined_img = np.hstack([original_img, separator, fixed_img])
    
    # Add labels
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(combined_img, "Original", (10, 30), font, 1, (0, 0, 255), 2)
    cv2.putText(combined_img, "Fixed", (w1 + 20, 30), font, 1, (0, 255, 0), 2)
    
    # Save the comparison image
    cv2.imwrite(output_path, combined_img)
    print(f"Saved comparison to {output_path}")
    
    return combined_img

def main():
    if len(sys.argv) < 4:
        print("Usage: python compare_results.py <original_dir> <fixed_dir> <output_dir>")
        sys.exit(1)
    
    original_dir = sys.argv[1]
    fixed_dir = sys.argv[2]
    output_dir = sys.argv[3]
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all images in the original directory
    image_files = [f for f in os.listdir(original_dir) 
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    for img_file in image_files:
        original_path = os.path.join(original_dir, img_file)
        fixed_path = os.path.join(fixed_dir, img_file)
        
        # Skip if fixed version doesn't exist
        if not os.path.exists(fixed_path):
            print(f"Warning: Fixed version of {img_file} not found. Skipping.")
            continue
        
        output_path = os.path.join(output_dir, f"comparison_{img_file}")
        create_comparison_image(original_path, fixed_path, output_path)

if __name__ == "__main__":
    main()
