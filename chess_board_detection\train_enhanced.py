"""
Enhanced training script for chess board detection with improved corner detection.
This script uses the enhanced U-Net model and corner-focused loss functions.
"""

import os
import argparse
import json
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import matplotlib.pyplot as plt
from scipy.ndimage.measurements import maximum_position

from models.enhanced_unet import EnhancedChessBoardUNet
from utils.real_dataset import get_data_loaders as get_real_data_loaders
from utils.dataset import get_data_loaders as get_synthetic_data_loaders
from enhanced_loss import EnhancedDiceLoss, CornerFocusedHeatmapLoss, GeometricConsistencyLoss
try:
    from enhanced_loss_v4 import EnhancedDiceLoss as EnhancedDiceLossV4
    from enhanced_loss_v4 import ImprovedCornerFocusedHeatmapLoss, ImprovedGeometricConsistencyLoss
    v4_available = True
except ImportError:
    v4_available = False
from config import (
    DATA_DIR, MODELS_DIR, DEVICE,
    LEARNING_RATE, NUM_EPOCHS, INPUT_SIZE
)


def calculate_corner_confidence(heatmaps, threshold=0.3):
    """
    Calculate confidence metrics for corner detection.

    Args:
        heatmaps (torch.Tensor): Corner heatmaps (B, 4, H, W)
        threshold (float): Confidence threshold

    Returns:
        dict: Dictionary of confidence metrics
    """
    batch_size = heatmaps.size(0)
    metrics = {
        'avg_peak_value': 0.0,
        'avg_peak_to_mean_ratio': 0.0,
        'avg_peak_to_second_ratio': 0.0,
        'detection_rate': 0.0
    }

    # Apply sigmoid to ensure values are in [0, 1]
    heatmaps = torch.sigmoid(heatmaps)

    # Convert to numpy for easier processing
    heatmaps_np = heatmaps.detach().cpu().numpy()

    detected_corners = 0
    valid_corners = 0
    eps = 1e-6  # Small epsilon to prevent division by zero

    for i in range(batch_size):
        for c in range(4):
            heatmap = heatmaps_np[i, c]

            # Find peak value and location
            max_val = np.max(heatmap)

            # Skip if max value is too small (no clear peak)
            if max_val < 0.05:
                continue

            valid_corners += 1

            # Calculate mean value (excluding the peak area)
            flat_heatmap = heatmap.flatten()
            sorted_indices = np.argsort(flat_heatmap)
            # Exclude top 1% values to avoid including the peak
            mean_val = np.mean(flat_heatmap[sorted_indices[:-int(len(sorted_indices)*0.01)]])

            # Ensure mean_val is positive
            mean_val = max(mean_val, eps)

            # Find second highest peak
            # Create a mask to exclude the main peak and its immediate surroundings
            peak_y, peak_x = np.unravel_index(np.argmax(heatmap), heatmap.shape)
            mask = np.ones_like(heatmap)
            y_size, x_size = heatmap.shape
            for y in range(max(0, peak_y-3), min(y_size, peak_y+4)):
                for x in range(max(0, peak_x-3), min(x_size, peak_x+4)):
                    mask[y, x] = 0

            # Find second peak
            masked_heatmap = heatmap * mask
            second_max_val = np.max(masked_heatmap)

            # Ensure second_max_val is positive
            second_max_val = max(second_max_val, eps)

            # Update metrics
            metrics['avg_peak_value'] += max_val

            # Calculate ratios with clamping to avoid extreme values
            peak_to_mean_ratio = min(max_val / mean_val, 100.0)  # Clamp to reasonable range
            peak_to_second_ratio = min(max_val / second_max_val, 100.0)  # Clamp to reasonable range

            metrics['avg_peak_to_mean_ratio'] += peak_to_mean_ratio
            metrics['avg_peak_to_second_ratio'] += peak_to_second_ratio

            # Count detected corners
            if max_val > threshold:
                detected_corners += 1

    # Calculate averages
    total_corners = batch_size * 4
    valid_corners = max(valid_corners, 1)  # Avoid division by zero

    metrics['avg_peak_value'] /= valid_corners
    metrics['avg_peak_to_mean_ratio'] /= valid_corners
    metrics['avg_peak_to_second_ratio'] /= valid_corners
    metrics['detection_rate'] = detected_corners / total_corners

    return metrics


def train_model(
    model,
    dataloaders,
    criterion_seg,
    criterion_heatmap,
    criterion_geometric,
    optimizer,
    output_dir,
    num_epochs=25,
    heatmap_weight=1.0,
    geometric_weight=0.2,
    save_interval=5,
    version='v3'
):
    """
    Train the model with enhanced loss functions.

    Args:
        model: The model to train
        dataloaders: Dictionary with 'train' and 'val' dataloaders
        criterion_seg: Loss function for segmentation
        criterion_heatmap: Loss function for corner heatmaps
        criterion_geometric: Loss function for geometric consistency
        optimizer: Optimizer to use
        output_dir: Directory to save outputs
        num_epochs: Number of epochs to train for
        heatmap_weight: Weight for heatmap loss
        geometric_weight: Weight for geometric consistency loss
        save_interval: Interval to save model checkpoints
        version: Version string (v3 or v4) for folder naming

    Returns:
        model: Trained model
        history: Training history
    """
    since = time.time()

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Create subdirectories with version folders
    checkpoints_dir = os.path.join(output_dir, 'checkpoints', version)
    logs_dir = os.path.join(output_dir, 'logs', version)
    vis_dir = os.path.join(output_dir, 'visualizations', version)

    os.makedirs(checkpoints_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)

    print(f"Saving outputs to {version} folders in {output_dir}")

    # Initialize history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_seg_loss': [],
        'val_seg_loss': [],
        'train_heatmap_loss': [],
        'val_heatmap_loss': [],
        'train_geometric_loss': [],
        'val_geometric_loss': [],
        'train_heatmap_components': [],
        'val_heatmap_components': [],
        'train_corner_confidence': [],
        'val_corner_confidence': []
    }

    best_loss = float('inf')

    # Training loop
    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)

        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode

            running_loss = 0.0
            running_seg_loss = 0.0
            running_heatmap_loss = 0.0
            running_geometric_loss = 0.0
            running_heatmap_components = {
                'mse_loss': 0.0,
                'separation_loss': 0.0,
                'peak_separation_loss': 0.0,
                'edge_suppression_loss': 0.0,
                'peak_enhancement_loss': 0.0,
                'peak_to_second_ratio_loss': 0.0,
                'detection_rate_loss': 0.0,
                'segmentation_guidance_loss': 0.0
            }
            running_corner_confidence = {
                'avg_peak_value': 0.0,
                'avg_peak_to_mean_ratio': 0.0,
                'avg_peak_to_second_ratio': 0.0,
                'detection_rate': 0.0
            }

            # Iterate over data
            for batch_idx, batch in enumerate(tqdm(dataloaders[phase], desc=f'{phase}')):
                inputs = batch['image'].to(DEVICE)
                masks = batch['mask'].to(DEVICE)

                # Handle different key names for heatmaps
                if 'heatmaps' in batch:
                    heatmaps = batch['heatmaps'].to(DEVICE)
                elif 'corner_heatmaps' in batch:
                    heatmaps = batch['corner_heatmaps'].to(DEVICE)
                else:
                    raise KeyError("Neither 'heatmaps' nor 'corner_heatmaps' found in batch")

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    heatmap_outputs = outputs['corner_heatmaps']

                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    # Pass segmentation to heatmap loss for segmentation-guided corner detection
                    heatmap_loss, heatmap_components = criterion_heatmap(heatmap_outputs, heatmaps, seg_outputs)
                    geometric_loss = criterion_geometric(heatmap_outputs)

                    # Combined loss with custom weights
                    loss = seg_loss + heatmap_weight * heatmap_loss + geometric_weight * geometric_loss

                    # Print loss values for debugging in first few batches
                    if batch_idx < 3 and epoch < 3:
                        print(f"Batch {batch_idx}, {phase} - Seg Loss: {seg_loss.item():.4f}, "
                              f"Heatmap Loss: {heatmap_loss.item():.4f}, "
                              f"Geometric Loss: {geometric_loss.item():.4f}, "
                              f"Total: {loss.item():.4f}")

                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        loss.backward()
                        optimizer.step()

                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_heatmap_loss += heatmap_loss.item() * inputs.size(0)
                running_geometric_loss += geometric_loss.item() * inputs.size(0)

                # Track heatmap loss components
                for key, value in heatmap_components.items():
                    running_heatmap_components[key] += value * inputs.size(0)

                # Calculate and track corner confidence metrics
                confidence_metrics = calculate_corner_confidence(heatmap_outputs)
                for key, value in confidence_metrics.items():
                    running_corner_confidence[key] += value * inputs.size(0)

            # Calculate epoch losses
            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_heatmap_loss = running_heatmap_loss / len(dataloaders[phase].dataset)
            epoch_geometric_loss = running_geometric_loss / len(dataloaders[phase].dataset)

            # Calculate average heatmap components
            epoch_heatmap_components = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_heatmap_components.items()
            }

            # Calculate average corner confidence metrics
            epoch_corner_confidence = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_corner_confidence.items()
            }

            # Print epoch losses
            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, '
                  f'Heatmap Loss: {epoch_heatmap_loss:.4f}, Geometric Loss: {epoch_geometric_loss:.4f}')

            # Print corner confidence metrics with more prominence
            print(f'=== {phase} Corner Confidence Metrics ===')
            for key, value in epoch_corner_confidence.items():
                print(f'  {key}: {value:.4f}')

            # Calculate overall confidence score (average of metrics)
            avg_confidence = sum(epoch_corner_confidence.values()) / len(epoch_corner_confidence)
            print(f'  Overall Confidence Score: {avg_confidence:.4f}')

            # Print heatmap components
            print(f'{phase} Heatmap Components:')
            for key, value in epoch_heatmap_components.items():
                print(f'  {key}: {value:.4f}')

            # Save history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_heatmap_loss'].append(epoch_heatmap_loss)
            history[f'{phase}_geometric_loss'].append(epoch_geometric_loss)
            history[f'{phase}_heatmap_components'].append(epoch_heatmap_components)
            history[f'{phase}_corner_confidence'].append(epoch_corner_confidence)

            # Save best model
            if phase == 'val' and epoch_loss < best_loss:
                best_loss = epoch_loss
                torch.save(model.state_dict(), os.path.join(checkpoints_dir, 'best_model.pth'))
                print(f'New best model saved with loss: {best_loss:.4f}')

        # Save checkpoint at specified intervals
        if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': epoch_loss,
                'history': history
            }, os.path.join(checkpoints_dir, f'checkpoint_epoch_{epoch+1}.pth'))

            # Save history to JSON
            with open(os.path.join(logs_dir, f'history_epoch_{epoch+1}.json'), 'w') as f:
                # Convert numpy values to Python types for JSON serialization
                json_history = {}
                for key, value in history.items():
                    if isinstance(value[0], dict):
                        json_history[key] = [{k: float(v) for k, v in d.items()} for d in value]
                    else:
                        json_history[key] = [float(v) for v in value]

                json.dump(json_history, f, indent=4)

            # Plot losses
            plt.figure(figsize=(15, 5))

            # Plot total loss
            plt.subplot(1, 3, 1)
            plt.plot(history['train_loss'], label='Train')
            plt.plot(history['val_loss'], label='Validation')
            plt.title('Total Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot segmentation loss
            plt.subplot(1, 3, 2)
            plt.plot(history['train_seg_loss'], label='Train')
            plt.plot(history['val_seg_loss'], label='Validation')
            plt.title('Segmentation Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot heatmap loss
            plt.subplot(1, 3, 3)
            plt.plot(history['train_heatmap_loss'], label='Train')
            plt.plot(history['val_heatmap_loss'], label='Validation')
            plt.title('Heatmap Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'losses_epoch_{epoch+1}.png'))
            plt.close()

            # Plot heatmap components
            plt.figure(figsize=(15, 15))

            component_keys = list(history['train_heatmap_components'][0].keys())
            num_components = len(component_keys)

            # Calculate grid dimensions
            grid_cols = 3
            grid_rows = (num_components + grid_cols - 1) // grid_cols  # Ceiling division

            for i, key in enumerate(component_keys):
                plt.subplot(grid_rows, grid_cols, i+1)
                plt.plot(
                    [comp[key] for comp in history['train_heatmap_components']],
                    label='Train'
                )
                plt.plot(
                    [comp[key] for comp in history['val_heatmap_components']],
                    label='Validation'
                )
                plt.title(key)
                plt.xlabel('Epoch')
                plt.ylabel('Value')
                plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'heatmap_components_epoch_{epoch+1}.png'))
            plt.close()

            # Plot corner confidence metrics
            plt.figure(figsize=(15, 12))

            confidence_keys = list(history['train_corner_confidence'][0].keys())
            for i, key in enumerate(confidence_keys):
                plt.subplot(2, 3, i+1)
                plt.plot(
                    [conf[key] for conf in history['train_corner_confidence']],
                    label='Train'
                )
                plt.plot(
                    [conf[key] for conf in history['val_corner_confidence']],
                    label='Validation'
                )
                plt.title(key)
                plt.xlabel('Epoch')
                plt.ylabel('Value')
                plt.legend()

            # Add overall confidence score plot
            plt.subplot(2, 3, 5)
            train_overall = [sum(conf.values()) / len(conf) for conf in history['train_corner_confidence']]
            val_overall = [sum(conf.values()) / len(conf) for conf in history['val_corner_confidence']]
            plt.plot(train_overall, label='Train')
            plt.plot(val_overall, label='Validation')
            plt.title('Overall Confidence Score')
            plt.xlabel('Epoch')
            plt.ylabel('Value')
            plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_dir, f'corner_confidence_epoch_{epoch+1}.png'))
            plt.close()

        print()

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')

    # Load best model weights
    model.load_state_dict(torch.load(os.path.join(checkpoints_dir, 'best_model.pth')))

    return model, history


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train enhanced chess board detection model')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR, help='Data directory')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--lr', type=float, default=LEARNING_RATE, help='Learning rate')
    parser.add_argument('--epochs', type=int, default=NUM_EPOCHS, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--heatmap_weight', type=float, default=1.0, help='Weight for heatmap loss')
    parser.add_argument('--geometric_weight', type=float, default=0.2, help='Weight for geometric loss')
    parser.add_argument('--separation_weight', type=float, default=0.4,
                        help='Weight for separation loss')
    parser.add_argument('--peak_separation_weight', type=float, default=0.3,
                        help='Weight for peak separation loss')
    parser.add_argument('--edge_suppression_weight', type=float, default=0.5,
                        help='Weight for edge suppression loss')
    parser.add_argument('--peak_enhancement_weight', type=float, default=0.3,
                        help='Weight for peak enhancement loss')
    parser.add_argument('--peak_to_second_ratio_weight', type=float, default=1.0,
                        help='Weight for peak-to-second ratio loss (v4 only)')
    parser.add_argument('--detection_rate_weight', type=float, default=1.0,
                        help='Weight for detection rate loss (v4 only)')
    parser.add_argument('--save_interval', type=int, default=5,
                        help='Interval to save model checkpoints')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--v4', action='store_true', help='Use v4 loss functions')
    parser.add_argument('--continue_from_v3', action='store_true', help='Continue training from v3 checkpoint')
    parser.add_argument('--start_from_epoch', type=int, default=0, help='Epoch to start from (for v3 continuation)')
    args = parser.parse_args()

    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")

    # Get data loaders
    print("Loading data...")

    # Try to load real data first
    real_data_dir = os.path.join(args.data_dir, 'real')
    annotation_file = os.path.join(args.data_dir, 'real_annotations.json')

    if os.path.exists(annotation_file) and os.path.exists(real_data_dir):
        print("Using real dataset...")
        dataloaders = get_real_data_loaders(
            data_dir=real_data_dir,
            annotation_file=annotation_file
        )
    else:
        # Fall back to synthetic data
        synthetic_data_dir = os.path.join(args.data_dir, 'synthetic')
        if os.path.exists(synthetic_data_dir):
            print("Real dataset not found. Using synthetic dataset...")
            dataloaders = get_synthetic_data_loaders(
                data_dir=synthetic_data_dir
            )
        else:
            # Create dummy data for testing
            print("No dataset found. Creating dummy data for testing...")
            from torch.utils.data import TensorDataset, DataLoader

            # Create dummy data (2 samples, 3 channels, 256x256)
            dummy_images = torch.randn(2, 3, INPUT_SIZE[0], INPUT_SIZE[1])
            dummy_masks = torch.zeros(2, 1, INPUT_SIZE[0], INPUT_SIZE[1])
            dummy_masks[:, :, 50:200, 50:200] = 1  # Create a simple square mask
            dummy_heatmaps = torch.zeros(2, 4, INPUT_SIZE[0], INPUT_SIZE[1])

            # Create Gaussian peaks at the corners of the square
            for i in range(2):  # For each sample
                # Top-left corner
                dummy_heatmaps[i, 0, 50, 50] = 1
                # Top-right corner
                dummy_heatmaps[i, 1, 50, 200] = 1
                # Bottom-right corner
                dummy_heatmaps[i, 2, 200, 200] = 1
                # Bottom-left corner
                dummy_heatmaps[i, 3, 200, 50] = 1

            # Apply Gaussian blur to create heatmaps
            import torch.nn.functional as F
            dummy_heatmaps = F.conv2d(
                dummy_heatmaps,
                torch.ones(1, 1, 15, 15) / 225,
                padding=7,
                groups=4
            )

            # Create a dataset that returns a dictionary with the required keys
            class DummyDataset(TensorDataset):
                def __getitem__(self, index):
                    return {
                        'image': dummy_images[index],
                        'mask': dummy_masks[index],
                        'heatmaps': dummy_heatmaps[index],
                        'corners': torch.tensor([50, 50, 200, 50, 200, 200, 50, 200], dtype=torch.float32),
                        'filename': f'dummy_{index}.png'
                    }

            dummy_dataset = DummyDataset(dummy_images, dummy_masks)
            dataloaders = {
                'train': DataLoader(dummy_dataset, batch_size=1, shuffle=True),
                'val': DataLoader(dummy_dataset, batch_size=1, shuffle=False)
            }
    print(f"Train dataset size: {len(dataloaders['train'].dataset)}")
    print(f"Validation dataset size: {len(dataloaders['val'].dataset)}")

    # Initialize model
    print("Initializing enhanced model...")
    model = EnhancedChessBoardUNet(n_channels=3, bilinear=True)

    # Load from v3 checkpoint if requested
    start_epoch = 0
    history = None
    if args.continue_from_v3:
        v3_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v3')
        if not os.path.exists(v3_checkpoints_dir):
            print(f"Warning: v3 checkpoints directory not found at {v3_checkpoints_dir}")
        else:
            # Find checkpoint closest to requested start epoch
            checkpoints = [f for f in os.listdir(v3_checkpoints_dir) if f.startswith('checkpoint_epoch_')]
            if checkpoints:
                # Extract epoch numbers
                checkpoint_epochs = [int(f.split('_')[-1].split('.')[0]) for f in checkpoints]
                # Find closest epoch
                closest_epoch = min(checkpoint_epochs, key=lambda x: abs(x - args.start_from_epoch))
                checkpoint_file = f"checkpoint_epoch_{closest_epoch}.pth"
                checkpoint_path = os.path.join(v3_checkpoints_dir, checkpoint_file)

                if os.path.exists(checkpoint_path):
                    print(f"Loading checkpoint from epoch {closest_epoch}: {checkpoint_path}")
                    checkpoint = torch.load(checkpoint_path)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    start_epoch = checkpoint.get('epoch', 0)
                    history = checkpoint.get('history', None)
                    print(f"Loaded checkpoint from epoch {start_epoch}")
                else:
                    print(f"Warning: Checkpoint file not found at {checkpoint_path}")
            else:
                # Try to load best model
                best_model_path = os.path.join(v3_checkpoints_dir, 'best_model.pth')
                if os.path.exists(best_model_path):
                    print(f"No checkpoints found, loading best model: {best_model_path}")
                    model.load_state_dict(torch.load(best_model_path))
                else:
                    print(f"Warning: No checkpoints or best model found in {v3_checkpoints_dir}")

    model = model.to(device)
    print(f"Model moved to {device}")

    # Define loss functions and optimizer
    if args.v4 and v4_available:
        print("Using v4 loss functions...")
        criterion_seg = EnhancedDiceLossV4()
        criterion_heatmap = ImprovedCornerFocusedHeatmapLoss(
            separation_weight=args.separation_weight,
            peak_separation_weight=args.peak_separation_weight,
            edge_suppression_weight=args.edge_suppression_weight,
            peak_enhancement_weight=args.peak_enhancement_weight,
            peak_to_second_ratio_weight=args.peak_to_second_ratio_weight,
            detection_rate_weight=args.detection_rate_weight
        )
        criterion_geometric = ImprovedGeometricConsistencyLoss(weight=1.0)  # Weight applied in train_model
    else:
        print("Using standard loss functions...")
        criterion_seg = EnhancedDiceLoss()
        criterion_heatmap = CornerFocusedHeatmapLoss(
            separation_weight=args.separation_weight,
            peak_separation_weight=args.peak_separation_weight,
            edge_suppression_weight=args.edge_suppression_weight,
            peak_enhancement_weight=args.peak_enhancement_weight
        )
        criterion_geometric = GeometricConsistencyLoss(weight=1.0)  # Weight applied in train_model

    optimizer = optim.Adam(model.parameters(), lr=args.lr)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Set output directory based on version
    version = 'v4' if args.v4 else 'v3'
    output_dir = args.output_dir

    # Train model
    print(f"Starting training for {args.epochs} epochs with {version} configuration...")
    model, history = train_model(
        model=model,
        dataloaders={'train': dataloaders['train'], 'val': dataloaders['val']},
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap,
        criterion_geometric=criterion_geometric,
        optimizer=optimizer,
        output_dir=output_dir,
        num_epochs=args.epochs,
        heatmap_weight=args.heatmap_weight,
        geometric_weight=args.geometric_weight,
        save_interval=args.save_interval,
        version=version
    )

    print("Training completed!")


if __name__ == "__main__":
    main()
