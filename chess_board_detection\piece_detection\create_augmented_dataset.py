"""
Create an augmented dataset for chess piece detection.
This script:
1. Loads labeled images and annotations
2. Applies augmentations while preserving bounding box coordinates
3. Resizes all images to 416x416 for mobile deployment
4. Saves the augmented dataset in YOLO format
"""

import os
import cv2
import numpy as np
import shutil
import random
import argparse
from pathlib import Path
from datetime import datetime
import albumentations as A
from tqdm import tqdm

def create_augmentation_pipeline(augmentation_type='standard'):
    """
    Create an augmentation pipeline that preserves bounding boxes.

    Args:
        augmentation_type: Type of augmentation to apply ('standard', 'color', 'noise', 'lighting', 'geometric')
    """
    # Base transform (always applied)
    base_transform = [
        # Resize to target size for mobile deployment
        A.Resize(416, 416)
    ]

    # Color augmentations
    color_transforms = [
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.7),
        A.<PERSON>eSaturationValue(hue_shift_limit=15, sat_shift_limit=40, val_shift_limit=30, p=0.7),
        <PERSON><PERSON>hift(r_shift_limit=20, g_shift_limit=20, b_shift_limit=20, p=0.7),
        A<PERSON>(clip_limit=4.0, tile_grid_size=(8, 8), p=0.5),
        A.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1, p=0.7)
    ]

    # Noise and blur augmentations
    noise_transforms = [
        A.GaussNoise(p=0.6),
        A.GaussianBlur(blur_limit=(3, 7), p=0.6),
        A.MotionBlur(blur_limit=(3, 7), p=0.6),
        A.MedianBlur(blur_limit=5, p=0.4),
        A.ISONoise(p=0.5)
    ]

    # Lighting augmentations
    lighting_transforms = [
        A.RandomShadow(p=0.6),
        A.RandomFog(p=0.4),
        A.RandomSunFlare(p=0.3),
        A.RandomToneCurve(p=0.5),
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.7)
    ]

    # Geometric augmentations (no rotation to keep pieces upright)
    geometric_transforms = [
        A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.15, rotate_limit=0, border_mode=cv2.BORDER_CONSTANT, p=0.7),
        A.Perspective(scale=(0.01, 0.05), p=0.5),
        A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=0.3),
        A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.3),
        A.OpticalDistortion(distort_limit=0.3, shift_limit=0.3, p=0.3)
    ]

    # Select transforms based on augmentation type
    if augmentation_type == 'color':
        transforms = base_transform + [A.OneOf(color_transforms, p=0.9)]
    elif augmentation_type == 'noise':
        transforms = base_transform + [A.OneOf(noise_transforms, p=0.9)]
    elif augmentation_type == 'lighting':
        transforms = base_transform + [A.OneOf(lighting_transforms, p=0.9)]
    elif augmentation_type == 'geometric':
        transforms = base_transform + [A.OneOf(geometric_transforms, p=0.9)]
    else:  # standard - mix of all
        transforms = base_transform + [
            A.OneOf(color_transforms, p=0.5),
            A.OneOf(noise_transforms, p=0.3),
            A.OneOf(lighting_transforms, p=0.3),
            A.OneOf(geometric_transforms, p=0.3)
        ]

    return A.Compose(transforms, bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))

def parse_yolo_annotation(annotation_path):
    """Parse YOLO format annotation file."""
    bboxes = []
    class_labels = []

    with open(annotation_path, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 5:  # class_id, x_center, y_center, width, height
                class_id = int(parts[0])
                x_center = float(parts[1])
                y_center = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])

                bboxes.append([x_center, y_center, width, height])
                class_labels.append(class_id)

    return bboxes, class_labels

def save_yolo_annotation(annotation_path, bboxes, class_labels):
    """Save annotations in YOLO format."""
    with open(annotation_path, 'w') as f:
        for bbox, class_id in zip(bboxes, class_labels):
            x_center, y_center, width, height = bbox
            f.write(f"{class_id} {x_center:.6f} {y_center:.6f} {width:.6f} {height:.6f}\n")

def create_augmented_dataset(
    input_images_dir,
    input_labels_dir,
    output_dir,
    num_augmentations=10,
    train_ratio=0.8,
    seed=42
):
    """
    Create an augmented dataset for chess piece detection.

    Args:
        input_images_dir: Directory containing original images
        input_labels_dir: Directory containing original labels
        output_dir: Directory to save augmented dataset
        num_augmentations: Number of augmentations per image
        train_ratio: Ratio of images to use for training
        seed: Random seed for reproducibility
    """
    random.seed(seed)
    np.random.seed(seed)

    # Create output directories
    train_img_dir = os.path.join(output_dir, 'images', 'train')
    val_img_dir = os.path.join(output_dir, 'images', 'val')
    train_label_dir = os.path.join(output_dir, 'labels', 'train')
    val_label_dir = os.path.join(output_dir, 'labels', 'val')

    os.makedirs(train_img_dir, exist_ok=True)
    os.makedirs(val_img_dir, exist_ok=True)
    os.makedirs(train_label_dir, exist_ok=True)
    os.makedirs(val_label_dir, exist_ok=True)

    # Create augmentation pipelines for different types
    standard_transform = create_augmentation_pipeline('standard')
    color_transform = create_augmentation_pipeline('color')
    noise_transform = create_augmentation_pipeline('noise')
    lighting_transform = create_augmentation_pipeline('lighting')
    geometric_transform = create_augmentation_pipeline('geometric')

    # Get all image files
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png']:
        image_files.extend(list(Path(input_images_dir).glob(f'*{ext}')))

    # Shuffle and split
    random.shuffle(image_files)
    split_idx = int(len(image_files) * train_ratio)
    train_files = image_files[:split_idx]
    val_files = image_files[split_idx:]

    print(f"Total original images: {len(image_files)}")
    print(f"Training images: {len(train_files)}")
    print(f"Validation images: {len(val_files)}")

    # Process training images with augmentation
    print("Processing training images...")
    for idx, img_path in enumerate(tqdm(train_files)):
        # Get corresponding label path
        label_path = os.path.join(input_labels_dir, f"{img_path.stem}.txt")

        if not os.path.exists(label_path):
            print(f"Warning: No label file found for {img_path.name}")
            continue

        # Read image and annotations
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"Warning: Could not read image {img_path}")
            continue

        # Parse YOLO annotations
        bboxes, class_labels = parse_yolo_annotation(label_path)

        # Save original image (resized to 416x416)
        original_transformed = standard_transform(image=image, bboxes=bboxes, class_labels=class_labels)
        original_img = original_transformed['image']
        original_bboxes = original_transformed['bboxes']

        # Save original (resized)
        original_img_path = os.path.join(train_img_dir, f"{img_path.stem}.jpg")
        cv2.imwrite(original_img_path, original_img)

        # Save original annotations
        original_label_path = os.path.join(train_label_dir, f"{img_path.stem}.txt")
        save_yolo_annotation(original_label_path, original_bboxes, class_labels)

        # Create augmentations with different transforms
        aug_count = 0

        # Standard augmentations (mix of all)
        for i in range(4):
            augmented = standard_transform(image=image, bboxes=bboxes, class_labels=class_labels)
            aug_image = augmented['image']
            aug_bboxes = augmented['bboxes']

            aug_img_path = os.path.join(train_img_dir, f"{img_path.stem}_aug{aug_count+1}.jpg")
            cv2.imwrite(aug_img_path, aug_image)

            aug_label_path = os.path.join(train_label_dir, f"{img_path.stem}_aug{aug_count+1}.txt")
            save_yolo_annotation(aug_label_path, aug_bboxes, class_labels)
            aug_count += 1

        # Color augmentations
        for i in range(2):
            augmented = color_transform(image=image, bboxes=bboxes, class_labels=class_labels)
            aug_image = augmented['image']
            aug_bboxes = augmented['bboxes']

            aug_img_path = os.path.join(train_img_dir, f"{img_path.stem}_aug{aug_count+1}.jpg")
            cv2.imwrite(aug_img_path, aug_image)

            aug_label_path = os.path.join(train_label_dir, f"{img_path.stem}_aug{aug_count+1}.txt")
            save_yolo_annotation(aug_label_path, aug_bboxes, class_labels)
            aug_count += 1

        # Noise augmentations
        for i in range(1):
            augmented = noise_transform(image=image, bboxes=bboxes, class_labels=class_labels)
            aug_image = augmented['image']
            aug_bboxes = augmented['bboxes']

            aug_img_path = os.path.join(train_img_dir, f"{img_path.stem}_aug{aug_count+1}.jpg")
            cv2.imwrite(aug_img_path, aug_image)

            aug_label_path = os.path.join(train_label_dir, f"{img_path.stem}_aug{aug_count+1}.txt")
            save_yolo_annotation(aug_label_path, aug_bboxes, class_labels)
            aug_count += 1

        # Lighting augmentations
        for i in range(2):
            augmented = lighting_transform(image=image, bboxes=bboxes, class_labels=class_labels)
            aug_image = augmented['image']
            aug_bboxes = augmented['bboxes']

            aug_img_path = os.path.join(train_img_dir, f"{img_path.stem}_aug{aug_count+1}.jpg")
            cv2.imwrite(aug_img_path, aug_image)

            aug_label_path = os.path.join(train_label_dir, f"{img_path.stem}_aug{aug_count+1}.txt")
            save_yolo_annotation(aug_label_path, aug_bboxes, class_labels)
            aug_count += 1

        # Geometric augmentations
        for i in range(1):
            augmented = geometric_transform(image=image, bboxes=bboxes, class_labels=class_labels)
            aug_image = augmented['image']
            aug_bboxes = augmented['bboxes']

            aug_img_path = os.path.join(train_img_dir, f"{img_path.stem}_aug{aug_count+1}.jpg")
            cv2.imwrite(aug_img_path, aug_image)

            aug_label_path = os.path.join(train_label_dir, f"{img_path.stem}_aug{aug_count+1}.txt")
            save_yolo_annotation(aug_label_path, aug_bboxes, class_labels)
            aug_count += 1

    # Process validation images (only resize, no augmentation)
    print("Processing validation images...")
    for img_path in tqdm(val_files):
        # Get corresponding label path
        label_path = os.path.join(input_labels_dir, f"{img_path.stem}.txt")

        if not os.path.exists(label_path):
            print(f"Warning: No label file found for {img_path.name}")
            continue

        # Read image and annotations
        image = cv2.imread(str(img_path))
        if image is None:
            print(f"Warning: Could not read image {img_path}")
            continue

        # Parse YOLO annotations
        bboxes, class_labels = parse_yolo_annotation(label_path)

        # Resize to 416x416
        resized = A.Compose(
            [A.Resize(416, 416, always_apply=True)],
            bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels'])
        )(image=image, bboxes=bboxes, class_labels=class_labels)

        resized_img = resized['image']
        resized_bboxes = resized['bboxes']

        # Save resized image
        resized_img_path = os.path.join(val_img_dir, f"{img_path.stem}.jpg")
        cv2.imwrite(resized_img_path, resized_img)

        # Save resized annotations
        resized_label_path = os.path.join(val_label_dir, f"{img_path.stem}.txt")
        save_yolo_annotation(resized_label_path, resized_bboxes, class_labels)

    # Create dataset.yaml file
    yaml_path = os.path.join(output_dir, 'dataset.yaml')
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]

    with open(yaml_path, 'w') as f:
        f.write(f"path: {output_dir}\n")
        f.write("train: images/train\n")
        f.write("val: images/val\n")
        f.write("nc: 12\n")
        f.write("names:\n")
        for i, name in enumerate(class_names):
            f.write(f"  {i}: {name}\n")

    print(f"Augmented dataset created at {output_dir}")
    print(f"Training images: {len(list(Path(train_img_dir).glob('*.jpg')))}")
    print(f"Validation images: {len(list(Path(val_img_dir).glob('*.jpg')))}")
    print(f"Dataset configuration saved to {yaml_path}")

    return output_dir

def main():
    parser = argparse.ArgumentParser(description="Create augmented dataset for chess piece detection")
    parser.add_argument("--input_images", type=str, default="chess_board_detection/piece_detection/dataset/images",
                        help="Directory containing original images")
    parser.add_argument("--input_labels", type=str, default="chess_board_detection/piece_detection/dataset/labels",
                        help="Directory containing original labels")
    parser.add_argument("--output_dir", type=str, default=None,
                        help="Directory to save augmented dataset")
    parser.add_argument("--num_augmentations", type=int, default=10,
                        help="Number of augmentations per image")
    parser.add_argument("--train_ratio", type=float, default=0.8,
                        help="Ratio of images to use for training")
    parser.add_argument("--seed", type=int, default=42,
                        help="Random seed for reproducibility")

    args = parser.parse_args()

    # Set default output directory if not provided
    if args.output_dir is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output_dir = f"chess_board_detection/piece_detection/augmented_dataset_{timestamp}"

    create_augmented_dataset(
        args.input_images,
        args.input_labels,
        args.output_dir,
        args.num_augmentations,
        args.train_ratio,
        args.seed
    )

if __name__ == "__main__":
    main()
