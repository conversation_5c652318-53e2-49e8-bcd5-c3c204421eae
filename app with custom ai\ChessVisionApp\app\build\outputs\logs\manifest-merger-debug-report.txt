-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:49:9-57:20
	android:grantUriPermissions
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:53:13-47
	android:authorities
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:51:13-64
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:52:13-37
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:50:13-62
manifest
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:2:1-61:12
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:2:1-61:12
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\8dde0d52a5967059f98f91e9f39c02eb\transformed\viewbinding-8.2.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a6d39f7d2b3cd64e5f154e99ea8b7a6\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2559031d7e9fe48b3590293cf5c6448f\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c9a778efb97434c628456537ce440f6\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\18caba07eccbe3fbbf2187ab7ee20228\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\48ae0f3814377200f627ddcfa28e7cdd\transformed\material-icons-extended-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0183ad98e4fcce82b5bce74e74cb306d\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\171906c4790eadf8b101b80a980ec89b\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f4742a87403bd5b0dd54647db063225\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb385fd8e991124dddc99f0b8ff2d8a0\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ead5545371d24235bc023f33a0e98494\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\67d9b8adf15bd6e4345a13922f59f7ff\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\99d1b9f542d8014336567412ba165359\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a183211418b77f78634304c515a13dd8\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fd9a7a132438146e1034b9cdd7d9660\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe80bf9892616fb612c556e8ad1bff21\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\338ceb409d7ecf99e336f65a5c41c13f\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\88551070551ada598948d3a48621d7b5\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4713635adcc2bac18369e016a04ea54e\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ceb550784afadb35d7f99d8c6d7571\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b6f91d6ca1bc5fcbf005898d2d6bb4e\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\460cd7bf85acb2de05de5e63bf256cd0\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca7edd7dea9a9293306bd31b1a5bbbe\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3c9569c12d12ee6bef96519efefd727\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b68849d71562fcbe2f5dc61ad8364bd7\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\4761891a00e04f90f15e1d3423ddd7ef\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\fba87ce246ccd05582e8dcc6e570965d\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b46d61e5c41c267a16ee81b65178c624\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e483c98f4668a3e994f85fa42e07be86\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d0945550d6b3404aab379337c0e8055\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a0214ca42e1a5ee1583e331235710ff\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\61157df515ef0c7e277bf766bd9ab98f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\39850584ec2c63dfe3ab909b2301a2f7\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f730594f7316821ef466e389eba523ca\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cb0f06ef6b767d925a284333d566c08\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8293e1d2c86054515277ca3a49ddd68\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc6ef576da25e3b07fbdf4cd8dbb83ff\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9a6c82564db2d0e70cc0471d838149c\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f9e2408b4519dc2b043569ff95761de\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b637f534b28270887d4661755f749cdd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47e8f640a3b72507e915165e172e30d3\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f52cd528f9ef7f36f833bd69565f4ad4\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\734e6e14280520b6576807a538c3eb88\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5160d069d5389f296c7123d21f1eabf7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a853a3bdd82a352651f96484ac59081e\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cdd27c7332ff07aa654c6c65661598f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\52379206e96007b93825c871a803cb9b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac1f0be00862d92439477595fd06bcc2\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7a931fa447fd312ebb20535a178d7a0\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.microsoft.onnxruntime:onnxruntime-android:1.16.3] C:\Users\<USER>\.gradle\caches\transforms-3\efe06c0974d9f98f01deea806077da22\transformed\onnxruntime-android-1.16.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\66c08ce8b5f34192d36ac101ea221d93\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57a20820f4b1db9c1fb9324972f26046\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\19708a749d6dff7eb36ff09ac5f05682\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d2e4bda91f51a96317c870c0b7cb8c5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\586f00dcd4af9979479231247b62947a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66dc2fe4d41730e9b260a9c2fcf27d99\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0a2fbb92a7fa2fa05766a4be785791db\transformed\napier-debug\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\85ed454bd0b5d8143814be8088c66e2d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:6:5-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:6:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:7:5-8:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:8:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:9:5-10:38
	android:maxSdkVersion
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:13:5-67
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:13:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:14:5-79
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:14:22-76
uses-feature#android.hardware.camera
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:17:5-19:35
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:19:9-32
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:18:9-47
uses-feature#android.hardware.camera.autofocus
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:20:5-22:36
	android:required
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:22:9-33
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:21:9-57
application
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:24:5-59:19
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:24:5-59:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\19708a749d6dff7eb36ff09ac5f05682\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\19708a749d6dff7eb36ff09ac5f05682\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:32:9-35
	android:label
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:30:9-41
	android:hardwareAccelerated
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:34:9-43
	android:fullBackupContent
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:28:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:31:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:35:9-29
	android:icon
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:29:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:26:9-35
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:33:9-52
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:27:9-65
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:25:9-47
activity#com.chessvision.app.MainActivity
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:37:9-46:20
	android:screenOrientation
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:41:13-49
	android:exported
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:39:13-36
	android:theme
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:40:13-56
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:38:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:42:13-45:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:43:17-69
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:43:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:44:17-77
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:44:27-74
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:54:13-56:54
	android:resource
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:56:17-51
	android:name
		ADDED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:55:17-67
uses-sdk
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\8dde0d52a5967059f98f91e9f39c02eb\transformed\viewbinding-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.2.2] C:\Users\<USER>\.gradle\caches\transforms-3\8dde0d52a5967059f98f91e9f39c02eb\transformed\viewbinding-8.2.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a6d39f7d2b3cd64e5f154e99ea8b7a6\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.accompanist:accompanist-permissions:0.32.0] C:\Users\<USER>\.gradle\caches\transforms-3\6a6d39f7d2b3cd64e5f154e99ea8b7a6\transformed\accompanist-permissions-0.32.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2559031d7e9fe48b3590293cf5c6448f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\2559031d7e9fe48b3590293cf5c6448f\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c9a778efb97434c628456537ce440f6\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0c9a778efb97434c628456537ce440f6\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\18caba07eccbe3fbbf2187ab7ee20228\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\18caba07eccbe3fbbf2187ab7ee20228\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\48ae0f3814377200f627ddcfa28e7cdd\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-extended-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\48ae0f3814377200f627ddcfa28e7cdd\transformed\material-icons-extended-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0183ad98e4fcce82b5bce74e74cb306d\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\0183ad98e4fcce82b5bce74e74cb306d\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\171906c4790eadf8b101b80a980ec89b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\171906c4790eadf8b101b80a980ec89b\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f4742a87403bd5b0dd54647db063225\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\5f4742a87403bd5b0dd54647db063225\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb385fd8e991124dddc99f0b8ff2d8a0\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\eb385fd8e991124dddc99f0b8ff2d8a0\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ead5545371d24235bc023f33a0e98494\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ead5545371d24235bc023f33a0e98494\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\67d9b8adf15bd6e4345a13922f59f7ff\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\67d9b8adf15bd6e4345a13922f59f7ff\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\99d1b9f542d8014336567412ba165359\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\99d1b9f542d8014336567412ba165359\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a183211418b77f78634304c515a13dd8\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\a183211418b77f78634304c515a13dd8\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fd9a7a132438146e1034b9cdd7d9660\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\9fd9a7a132438146e1034b9cdd7d9660\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe80bf9892616fb612c556e8ad1bff21\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\fe80bf9892616fb612c556e8ad1bff21\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\338ceb409d7ecf99e336f65a5c41c13f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\338ceb409d7ecf99e336f65a5c41c13f\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\88551070551ada598948d3a48621d7b5\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\88551070551ada598948d3a48621d7b5\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4713635adcc2bac18369e016a04ea54e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\4713635adcc2bac18369e016a04ea54e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ceb550784afadb35d7f99d8c6d7571\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\72ceb550784afadb35d7f99d8c6d7571\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b6f91d6ca1bc5fcbf005898d2d6bb4e\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1b6f91d6ca1bc5fcbf005898d2d6bb4e\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\460cd7bf85acb2de05de5e63bf256cd0\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\460cd7bf85acb2de05de5e63bf256cd0\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca7edd7dea9a9293306bd31b1a5bbbe\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\bca7edd7dea9a9293306bd31b1a5bbbe\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3c9569c12d12ee6bef96519efefd727\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\c3c9569c12d12ee6bef96519efefd727\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b68849d71562fcbe2f5dc61ad8364bd7\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b68849d71562fcbe2f5dc61ad8364bd7\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\4761891a00e04f90f15e1d3423ddd7ef\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\4761891a00e04f90f15e1d3423ddd7ef\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\fba87ce246ccd05582e8dcc6e570965d\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\fba87ce246ccd05582e8dcc6e570965d\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b46d61e5c41c267a16ee81b65178c624\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b46d61e5c41c267a16ee81b65178c624\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e483c98f4668a3e994f85fa42e07be86\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e483c98f4668a3e994f85fa42e07be86\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d0945550d6b3404aab379337c0e8055\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d0945550d6b3404aab379337c0e8055\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a0214ca42e1a5ee1583e331235710ff\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9a0214ca42e1a5ee1583e331235710ff\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\61157df515ef0c7e277bf766bd9ab98f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\61157df515ef0c7e277bf766bd9ab98f\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\39850584ec2c63dfe3ab909b2301a2f7\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\39850584ec2c63dfe3ab909b2301a2f7\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f730594f7316821ef466e389eba523ca\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f730594f7316821ef466e389eba523ca\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cb0f06ef6b767d925a284333d566c08\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8cb0f06ef6b767d925a284333d566c08\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8293e1d2c86054515277ca3a49ddd68\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c8293e1d2c86054515277ca3a49ddd68\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc6ef576da25e3b07fbdf4cd8dbb83ff\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\cc6ef576da25e3b07fbdf4cd8dbb83ff\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9a6c82564db2d0e70cc0471d838149c\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e9a6c82564db2d0e70cc0471d838149c\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f9e2408b4519dc2b043569ff95761de\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f9e2408b4519dc2b043569ff95761de\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b637f534b28270887d4661755f749cdd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\b637f534b28270887d4661755f749cdd\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47e8f640a3b72507e915165e172e30d3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\47e8f640a3b72507e915165e172e30d3\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f52cd528f9ef7f36f833bd69565f4ad4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f52cd528f9ef7f36f833bd69565f4ad4\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\734e6e14280520b6576807a538c3eb88\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\734e6e14280520b6576807a538c3eb88\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5160d069d5389f296c7123d21f1eabf7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\5160d069d5389f296c7123d21f1eabf7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a853a3bdd82a352651f96484ac59081e\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a853a3bdd82a352651f96484ac59081e\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cdd27c7332ff07aa654c6c65661598f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\0cdd27c7332ff07aa654c6c65661598f\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\52379206e96007b93825c871a803cb9b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\52379206e96007b93825c871a803cb9b\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac1f0be00862d92439477595fd06bcc2\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\ac1f0be00862d92439477595fd06bcc2\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7a931fa447fd312ebb20535a178d7a0\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\f7a931fa447fd312ebb20535a178d7a0\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.microsoft.onnxruntime:onnxruntime-android:1.16.3] C:\Users\<USER>\.gradle\caches\transforms-3\efe06c0974d9f98f01deea806077da22\transformed\onnxruntime-android-1.16.3\AndroidManifest.xml:5:5-44
MERGED from [com.microsoft.onnxruntime:onnxruntime-android:1.16.3] C:\Users\<USER>\.gradle\caches\transforms-3\efe06c0974d9f98f01deea806077da22\transformed\onnxruntime-android-1.16.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\66c08ce8b5f34192d36ac101ea221d93\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\66c08ce8b5f34192d36ac101ea221d93\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57a20820f4b1db9c1fb9324972f26046\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\57a20820f4b1db9c1fb9324972f26046\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\19708a749d6dff7eb36ff09ac5f05682\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\19708a749d6dff7eb36ff09ac5f05682\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d2e4bda91f51a96317c870c0b7cb8c5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\9d2e4bda91f51a96317c870c0b7cb8c5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\586f00dcd4af9979479231247b62947a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\586f00dcd4af9979479231247b62947a\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66dc2fe4d41730e9b260a9c2fcf27d99\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\66dc2fe4d41730e9b260a9c2fcf27d99\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0a2fbb92a7fa2fa05766a4be785791db\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [io.github.aakira:napier-android-debug:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\0a2fbb92a7fa2fa05766a4be785791db\transformed\napier-debug\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\85ed454bd0b5d8143814be8088c66e2d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\85ed454bd0b5d8143814be8088c66e2d\transformed\annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1a10fd9ba45929f9d51d807a99a8ea70\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\61baf198e7b4f8aa28c1bc6e34bcfaed\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.chessvision.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.chessvision.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
