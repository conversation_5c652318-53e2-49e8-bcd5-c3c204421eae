"""
Stable Enhanced U-Net V2 Training Script.
Focuses on proven improvements while maintaining stability.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.enhanced_unet_v2 import get_enhanced_model
from chess_board_detection.dataset.augmented_segmentation_dataset import create_augmented_dataloaders

class StableLoss(nn.Module):
    """Stable loss function combining BCE and Dice with numerical stability."""
    
    def __init__(self, bce_weight=0.5, dice_weight=0.5, smooth=1e-6):
        super(StableLoss, self).__init__()
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.smooth = smooth
        self.bce_loss = nn.BCEWithLogitsLoss()
    
    def dice_loss(self, inputs, targets):
        """Stable dice loss."""
        inputs = torch.sigmoid(inputs)
        
        # Flatten
        inputs = inputs.view(-1)
        targets = targets.view(-1)
        
        intersection = (inputs * targets).sum()
        dice = (2. * intersection + self.smooth) / (inputs.sum() + targets.sum() + self.smooth)
        
        return 1 - dice
    
    def forward(self, inputs, targets):
        # Handle deep supervision
        if isinstance(inputs, tuple):
            main_output = inputs[0]
            ds_outputs = inputs[1:]
            
            # Main loss
            bce = self.bce_loss(main_output, targets)
            dice = self.dice_loss(main_output, targets)
            main_loss = self.bce_weight * bce + self.dice_weight * dice
            
            # Deep supervision losses (reduced weight)
            total_loss = main_loss
            for ds_output in ds_outputs:
                ds_bce = self.bce_loss(ds_output, targets)
                ds_dice = self.dice_loss(ds_output, targets)
                ds_loss = self.bce_weight * ds_bce + self.dice_weight * ds_dice
                total_loss += 0.3 * ds_loss  # Reduced weight for deep supervision
            
            return total_loss, {
                'bce': bce.item(),
                'dice': dice.item(),
                'total': total_loss.item()
            }
        else:
            bce = self.bce_loss(inputs, targets)
            dice = self.dice_loss(inputs, targets)
            total_loss = self.bce_weight * bce + self.dice_weight * dice
            
            return total_loss, {
                'bce': bce.item(),
                'dice': dice.item(),
                'total': total_loss.item()
            }

def calculate_metrics(predictions, targets, threshold=0.5):
    """Calculate metrics with numerical stability."""
    with torch.no_grad():
        predictions = torch.sigmoid(predictions)
        
        # Apply threshold
        pred_binary = (predictions > threshold).float()
        targets_binary = (targets > threshold).float()
        
        # Flatten
        pred_flat = pred_binary.view(-1)
        target_flat = targets_binary.view(-1)
        
        # Calculate metrics with stability
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum() - intersection
        
        # IoU with stability
        iou = (intersection + 1e-6) / (union + 1e-6)
        
        # Dice with stability
        dice = (2 * intersection + 1e-6) / (pred_flat.sum() + target_flat.sum() + 1e-6)
        
        # Ensure valid ranges
        iou = torch.clamp(iou, 0, 1)
        dice = torch.clamp(dice, 0, 1)
        
        return {
            'iou': iou.item(),
            'dice': dice.item()
        }

def train_epoch_stable(model, train_loader, criterion, optimizer, scaler, device):
    """Stable training epoch with gradient clipping."""
    model.train()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(train_loader)
    
    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device, non_blocking=True)
        masks = masks.to(device, non_blocking=True)
        
        # Add channel dimension if needed
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)
        
        optimizer.zero_grad()
        
        with autocast():
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            
            # Check for NaN
            if torch.isnan(loss):
                print(f"NaN loss detected at batch {batch_idx}, skipping...")
                continue
        
        # Backward pass with gradient clipping
        scaler.scale(loss).backward()
        
        # Gradient clipping for stability
        scaler.unscale_(optimizer)
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
        
        scaler.step(optimizer)
        scaler.update()
        
        # Calculate metrics
        main_output = outputs[0] if isinstance(outputs, tuple) else outputs
        batch_metrics = calculate_metrics(main_output, masks)
        
        total_loss += loss.item()
        total_iou += batch_metrics['iou']
        total_dice += batch_metrics['dice']
        
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Dice': f'{batch_metrics["dice"]:.4f}',
            'IoU': f'{batch_metrics["iou"]:.4f}'
        })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def validate_epoch_stable(model, val_loader, criterion, device):
    """Stable validation epoch."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            
            if masks.dim() == 3:
                masks = masks.unsqueeze(1)
            
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            
            # Check for NaN
            if torch.isnan(loss):
                print(f"NaN loss in validation at batch {batch_idx}")
                continue
            
            # Calculate metrics
            main_output = outputs[0] if isinstance(outputs, tuple) else outputs
            batch_metrics = calculate_metrics(main_output, masks)
            
            total_loss += loss.item()
            total_iou += batch_metrics['iou']
            total_dice += batch_metrics['dice']
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{batch_metrics["dice"]:.4f}',
                'IoU': f'{batch_metrics["iou"]:.4f}'
            })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def train_enhanced_unet_v2_stable():
    """Stable Enhanced U-Net V2 training."""
    
    # Configuration
    config = {
        'dataset_dir': r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326",
        'save_dir': "chess_board_detection/enhanced_unet_v2_stable_results",
        'epochs': 50,
        'batch_size': 4,  # Increased batch size
        'learning_rate': 5e-5,  # Lower learning rate for stability
        'image_size': (384, 384),  # Smaller image size for stability
        'num_workers': 0,
    }
    
    print("🚀 Starting Enhanced U-Net V2 Stable Training...")
    print(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Create save directory
    save_dir = Path(config['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Create dataloaders (using stable augmented dataset)
    print("Creating dataloaders...")
    train_loader, val_loader = create_augmented_dataloaders(
        config['dataset_dir'],
        batch_size=config['batch_size'],
        train_split=0.8,
        num_workers=config['num_workers']
    )
    
    # Create model (without deep supervision for stability)
    print("Creating Enhanced U-Net V2...")
    model = get_enhanced_model(
        model_type="enhanced_v2",
        n_channels=3,
        n_classes=1,
        deep_supervision=False  # Disable for stability
    )
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,}")
    
    # Stable loss function
    criterion = StableLoss(bce_weight=0.5, dice_weight=0.5)
    
    # Optimizer with lower learning rate
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=1e-5,  # Lower weight decay
        eps=1e-8  # Numerical stability
    )
    
    # Scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(
        optimizer, T_max=config['epochs'], eta_min=1e-6
    )
    
    # Mixed precision scaler
    scaler = GradScaler()
    
    # Training history
    history = {
        'train_loss': [], 'val_loss': [],
        'train_dice': [], 'val_dice': [],
        'train_iou': [], 'val_iou': []
    }
    
    best_val_dice = 0
    patience_counter = 0
    patience = 15
    
    print(f"Starting training for {config['epochs']} epochs...")
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        print(f"\nEpoch {epoch+1}/{config['epochs']}")
        
        # Train
        train_loss, train_iou, train_dice = train_epoch_stable(
            model, train_loader, criterion, optimizer, scaler, device
        )
        
        # Validate
        val_loss, val_iou, val_dice = validate_epoch_stable(
            model, val_loader, criterion, device
        )
        
        # Update scheduler
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_dice'].append(train_dice)
        history['val_dice'].append(val_dice)
        history['train_iou'].append(train_iou)
        history['val_iou'].append(val_iou)
        
        # Print results
        print(f"Train - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}, IoU: {train_iou:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}, IoU: {val_iou:.4f}")
        print(f"LR: {current_lr:.6f}")
        
        # Save best model
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            torch.save(model.state_dict(), save_dir / "best_model.pth")
            print(f"✅ New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_dice': val_dice,
                'history': history
            }, save_dir / f"checkpoint_epoch_{epoch+1}.pth")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"Early stopping triggered after {patience} epochs without improvement")
            break
    
    # Save final results
    torch.save(model.state_dict(), save_dir / "final_model.pth")
    
    with open(save_dir / "training_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    training_time = time.time() - start_time
    print(f"\n🎉 Training completed in {training_time/3600:.2f} hours")
    print(f"🏆 Best validation Dice: {best_val_dice:.4f}")
    print(f"📁 Results saved to: {save_dir}")
    
    return model, history, best_val_dice

if __name__ == "__main__":
    try:
        model, history, best_dice = train_enhanced_unet_v2_stable()
        
        print(f"\n{'='*60}")
        print(f"🎉 ENHANCED U-NET V2 TRAINING COMPLETED!")
        print(f"{'='*60}")
        print(f"🏆 Best Dice Score: {best_dice:.4f}")
        
        # Compare with V1
        v1_best = 0.9100
        print(f"\n📊 COMPARISON WITH V1:")
        print(f"V1 Best Dice: {v1_best:.4f}")
        print(f"V2 Best Dice: {best_dice:.4f}")
        
        if best_dice > v1_best:
            improvement = ((best_dice - v1_best) / v1_best) * 100
            print(f"🚀 Improvement: +{improvement:.2f}%")
            print(f"✅ V2 is BETTER than V1!")
        else:
            decline = ((v1_best - best_dice) / v1_best) * 100
            print(f"📉 Decline: -{decline:.2f}%")
            print(f"⚠️ V2 needs further optimization")
        
        print(f"\n🎯 NEXT STEPS:")
        if best_dice > 0.92:
            print("✅ Excellent performance! Ready for deployment")
        elif best_dice > 0.90:
            print("✅ Good performance! Consider fine-tuning")
        else:
            print("⚠️ Needs improvement - try different hyperparameters")
        
    except Exception as e:
        print(f"❌ Training failed with error: {e}")
        import traceback
        traceback.print_exc()
