#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Generate a comprehensive analysis report for chess board detection model v5.2.
This script analyzes training history, model performance, and generates visualizations.
"""

import os
import sys
import argparse
import json
import numpy as np
import matplotlib.pyplot as plt
import torch
import pandas as pd
from datetime import datetime
import cv2
from PIL import Image
import torchvision
from matplotlib.colors import LinearSegmentedColormap

from config import MODELS_DIR, DATA_DIR, DEVICE, INPUT_SIZE
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from utils.visualization import visualize_heatmaps_and_keypoints, visualize_peak_to_second_ratio
from utils.metrics import calculate_corner_confidence_metrics, calculate_corner_confusion_matrix

def load_training_history(history_path):
    """
    Load training history from JSON file.

    Args:
        history_path: Path to the history JSON file

    Returns:
        Dictionary containing training history
    """
    with open(history_path, 'r') as f:
        history = json.load(f)
    return history

def analyze_loss_trends(history):
    """
    Analyze loss trends from training history.

    Args:
        history: Training history dictionary

    Returns:
        Dictionary of analysis results
    """
    results = {}

    # Calculate final losses
    results['final_train_loss'] = history['train_loss'][-1]
    results['final_val_loss'] = history['val_loss'][-1]
    results['final_train_seg_loss'] = history['train_seg_loss'][-1]
    results['final_val_seg_loss'] = history['val_seg_loss'][-1]
    results['final_train_heatmap_loss'] = history['train_heatmap_loss'][-1]
    results['final_val_heatmap_loss'] = history['val_heatmap_loss'][-1]

    # Calculate loss reduction (first epoch to last)
    results['train_loss_reduction'] = (history['train_loss'][0] - history['train_loss'][-1]) / history['train_loss'][0]
    results['val_loss_reduction'] = (history['val_loss'][0] - history['val_loss'][-1]) / history['val_loss'][0]

    # Calculate convergence speed (epochs to reach 90% of total reduction)
    train_loss_target = history['train_loss'][0] - 0.9 * (history['train_loss'][0] - min(history['train_loss']))
    val_loss_target = history['val_loss'][0] - 0.9 * (history['val_loss'][0] - min(history['val_loss']))

    for i, loss in enumerate(history['train_loss']):
        if loss <= train_loss_target:
            results['train_convergence_epoch'] = i
            break
    else:
        results['train_convergence_epoch'] = len(history['train_loss'])

    for i, loss in enumerate(history['val_loss']):
        if loss <= val_loss_target:
            results['val_convergence_epoch'] = i
            break
    else:
        results['val_convergence_epoch'] = len(history['val_loss'])

    # Check for overfitting (validation loss increasing while training loss decreasing)
    overfitting_epochs = []
    for i in range(1, len(history['train_loss'])):
        if (history['train_loss'][i] < history['train_loss'][i-1] and
            history['val_loss'][i] > history['val_loss'][i-1]):
            overfitting_epochs.append(i)

    results['overfitting_epochs'] = overfitting_epochs
    results['overfitting_detected'] = len(overfitting_epochs) > 0

    return results

def analyze_metric_trends(history, target_metrics):
    """
    Analyze metric trends from training history.

    Args:
        history: Training history dictionary
        target_metrics: Dictionary of target metrics

    Returns:
        Dictionary of analysis results
    """
    results = {}

    # Get final metrics
    final_metrics = history['val_corner_confidence'][-1]

    # Calculate distance to targets
    for key, target in target_metrics.items():
        if key in final_metrics:
            results[f'{key}_final'] = final_metrics[key]
            results[f'{key}_target'] = target
            results[f'{key}_distance_to_target'] = (final_metrics[key] - target) / target

    # Analyze peak-to-second ratio trend
    p2s_values = [conf['avg_peak_to_second_ratio'] for conf in history['val_corner_confidence']]

    # Calculate improvement rate
    if len(p2s_values) > 1:
        p2s_improvement_rate = (p2s_values[-1] - p2s_values[0]) / (len(p2s_values) - 1)
        results['p2s_improvement_rate'] = p2s_improvement_rate

        # Estimate epochs needed to reach target
        if p2s_improvement_rate > 0:
            target_p2s = target_metrics.get('peak_to_second_ratio', 2.0)
            epochs_to_target = (target_p2s - p2s_values[-1]) / p2s_improvement_rate
            results['estimated_epochs_to_target_p2s'] = max(0, epochs_to_target)
        else:
            results['estimated_epochs_to_target_p2s'] = float('inf')

    # Analyze per-corner performance
    if 'per_corner_p2s_ratio' in final_metrics:
        corner_names = ["Top-Left", "Top-Right", "Bottom-Right", "Bottom-Left"]
        per_corner_p2s = final_metrics['per_corner_p2s_ratio']

        for i, corner in enumerate(corner_names):
            results[f'{corner}_p2s_ratio'] = per_corner_p2s[i]

        # Identify weakest corner
        weakest_corner_idx = np.argmin(per_corner_p2s)
        results['weakest_corner'] = corner_names[weakest_corner_idx]
        results['weakest_corner_p2s'] = per_corner_p2s[weakest_corner_idx]

        # Identify strongest corner
        strongest_corner_idx = np.argmax(per_corner_p2s)
        results['strongest_corner'] = corner_names[strongest_corner_idx]
        results['strongest_corner_p2s'] = per_corner_p2s[strongest_corner_idx]

    return results

def analyze_hyperparameter_tuning(history):
    """
    Analyze hyperparameter tuning from training history.

    Args:
        history: Training history dictionary

    Returns:
        Dictionary of analysis results
    """
    results = {}

    # Analyze learning rate
    if 'learning_rates' in history and len(history['learning_rates']) > 0:
        results['initial_lr'] = history['learning_rates'][0]
        results['final_lr'] = history['learning_rates'][-1]
        results['lr_reduction'] = 1 - (results['final_lr'] / results['initial_lr'])

        # Find epochs with significant LR changes
        lr_change_epochs = []
        for i in range(1, len(history['learning_rates'])):
            change = abs(history['learning_rates'][i] - history['learning_rates'][i-1]) / history['learning_rates'][i-1]
            if change > 0.1:  # More than 10% change
                lr_change_epochs.append((i, change))

        results['significant_lr_changes'] = lr_change_epochs

    # Analyze weight tuning
    if 'heatmap_weights' in history and 'geometric_weights' in history:
        results['initial_heatmap_weight'] = history['heatmap_weights'][0]
        results['final_heatmap_weight'] = history['heatmap_weights'][-1]
        results['initial_geometric_weight'] = history['geometric_weights'][0]
        results['final_geometric_weight'] = history['geometric_weights'][-1]

        # Find epochs with weight changes
        weight_change_epochs = []
        for i in range(1, len(history['heatmap_weights'])):
            heatmap_change = history['heatmap_weights'][i] != history['heatmap_weights'][i-1]
            geometric_change = history['geometric_weights'][i] != history['geometric_weights'][i-1]
            if heatmap_change or geometric_change:
                weight_change_epochs.append(i)

        results['weight_change_epochs'] = weight_change_epochs

    return results

def generate_report(history_path, output_dir, phase_name='phase1', target_metrics=None):
    """
    Generate a comprehensive analysis report.

    Args:
        history_path: Path to the history JSON file
        output_dir: Output directory for the report
        phase_name: Name of the training phase
        target_metrics: Dictionary of target metrics
    """
    # Create output directory
    report_dir = os.path.join(output_dir, 'reports', 'v5.2', phase_name)
    os.makedirs(report_dir, exist_ok=True)

    # Load history
    history = load_training_history(history_path)

    # Set default target metrics if not provided
    if target_metrics is None:
        target_metrics = {
            'peak_to_second_ratio': 2.0,
            'detection_rate': 0.95,
            'avg_peak_value': 0.8
        }

    # Analyze trends
    loss_analysis = analyze_loss_trends(history)
    metric_analysis = analyze_metric_trends(history, target_metrics)
    hyperparameter_analysis = analyze_hyperparameter_tuning(history)

    # Generate report
    report_path = os.path.join(report_dir, f'analysis_report_{phase_name}.md')

    with open(report_path, 'w') as f:
        # Header
        f.write(f"# Chess Board Detection Model v5.2 Analysis Report\n\n")
        f.write(f"## Training Phase: {phase_name}\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # Summary
        f.write("## Summary\n\n")
        f.write(f"- **Total Epochs**: {len(history['train_loss'])}\n")
        f.write(f"- **Final Validation Loss**: {loss_analysis['final_val_loss']:.4f}\n")
        f.write(f"- **Final Peak-to-Second Ratio**: {metric_analysis.get('peak_to_second_ratio_final', 'N/A')}\n")
        f.write(f"- **Final Detection Rate**: {metric_analysis.get('detection_rate_final', 'N/A')}\n")
        f.write(f"- **Overfitting Detected**: {'Yes' if loss_analysis['overfitting_detected'] else 'No'}\n\n")

        # Loss Analysis
        f.write("## Loss Analysis\n\n")
        f.write("### Final Loss Values\n\n")
        f.write("| Metric | Training | Validation |\n")
        f.write("|--------|----------|------------|\n")
        f.write(f"| Total Loss | {loss_analysis['final_train_loss']:.4f} | {loss_analysis['final_val_loss']:.4f} |\n")
        f.write(f"| Segmentation Loss | {loss_analysis['final_train_seg_loss']:.4f} | {loss_analysis['final_val_seg_loss']:.4f} |\n")
        f.write(f"| Heatmap Loss | {loss_analysis['final_train_heatmap_loss']:.4f} | {loss_analysis['final_val_heatmap_loss']:.4f} |\n\n")

        f.write("### Convergence Analysis\n\n")
        f.write(f"- **Training Loss Reduction**: {loss_analysis['train_loss_reduction']*100:.2f}%\n")
        f.write(f"- **Validation Loss Reduction**: {loss_analysis['val_loss_reduction']*100:.2f}%\n")
        f.write(f"- **Training Convergence Epoch**: {loss_analysis['train_convergence_epoch']}\n")
        f.write(f"- **Validation Convergence Epoch**: {loss_analysis['val_convergence_epoch']}\n\n")

        if loss_analysis['overfitting_detected']:
            f.write("### Overfitting Analysis\n\n")
            f.write(f"Overfitting detected at epochs: {', '.join(map(str, loss_analysis['overfitting_epochs']))}\n\n")

        # Metric Analysis
        f.write("## Metric Analysis\n\n")
        f.write("### Target Metrics Comparison\n\n")
        f.write("| Metric | Final Value | Target | Distance to Target |\n")
        f.write("|--------|-------------|--------|--------------------|\n")

        for key in target_metrics.keys():
            final_value = metric_analysis.get(f'{key}_final', 'N/A')
            target_value = metric_analysis.get(f'{key}_target', 'N/A')
            distance = metric_analysis.get(f'{key}_distance_to_target', 'N/A')

            if isinstance(final_value, (int, float)):
                final_str = f"{final_value:.4f}"
            else:
                final_str = str(final_value)

            if isinstance(target_value, (int, float)):
                target_str = f"{target_value:.4f}"
            else:
                target_str = str(target_value)

            if isinstance(distance, (int, float)):
                distance_str = f"{distance*100:.2f}%"
                emoji = "🟢" if distance >= 0 else "🔴"
                distance_str = f"{emoji} {distance_str}"
            else:
                distance_str = str(distance)

            f.write(f"| {key} | {final_str} | {target_str} | {distance_str} |\n")

        f.write("\n")

        # Per-corner analysis
        if 'weakest_corner' in metric_analysis:
            f.write("### Per-Corner Analysis\n\n")
            f.write("| Corner | Peak-to-Second Ratio |\n")
            f.write("|--------|----------------------|\n")
            corner_names = ["Top-Left", "Top-Right", "Bottom-Right", "Bottom-Left"]
            for corner in corner_names:
                p2s = metric_analysis.get(f'{corner}_p2s_ratio', 'N/A')
                if corner == metric_analysis['weakest_corner']:
                    f.write(f"| {corner} (Weakest) | {p2s:.4f} |\n")
                elif corner == metric_analysis['strongest_corner']:
                    f.write(f"| {corner} (Strongest) | {p2s:.4f} |\n")
                else:
                    f.write(f"| {corner} | {p2s:.4f} |\n")
            f.write("\n")

        # Hyperparameter Analysis
        f.write("## Hyperparameter Analysis\n\n")

        if 'initial_lr' in hyperparameter_analysis:
            f.write("### Learning Rate Analysis\n\n")
            f.write(f"- **Initial Learning Rate**: {hyperparameter_analysis['initial_lr']:.6f}\n")
            f.write(f"- **Final Learning Rate**: {hyperparameter_analysis['final_lr']:.6f}\n")
            f.write(f"- **Learning Rate Reduction**: {hyperparameter_analysis['lr_reduction']*100:.2f}%\n")

            if hyperparameter_analysis['significant_lr_changes']:
                f.write("\nSignificant learning rate changes:\n\n")
                for epoch, change in hyperparameter_analysis['significant_lr_changes']:
                    f.write(f"- Epoch {epoch}: {change*100:.2f}% change\n")
            f.write("\n")

        if 'initial_heatmap_weight' in hyperparameter_analysis:
            f.write("### Weight Tuning Analysis\n\n")
            f.write(f"- **Initial Heatmap Weight**: {hyperparameter_analysis['initial_heatmap_weight']:.2f}\n")
            f.write(f"- **Final Heatmap Weight**: {hyperparameter_analysis['final_heatmap_weight']:.2f}\n")
            f.write(f"- **Initial Geometric Weight**: {hyperparameter_analysis['initial_geometric_weight']:.2f}\n")
            f.write(f"- **Final Geometric Weight**: {hyperparameter_analysis['final_geometric_weight']:.2f}\n")

            if hyperparameter_analysis['weight_change_epochs']:
                f.write("\nWeight changes occurred at epochs: ")
                f.write(f"{', '.join(map(str, hyperparameter_analysis['weight_change_epochs']))}\n")
            f.write("\n")

        # Recommendations
        f.write("## Recommendations\n\n")

        # P2S ratio recommendations
        if 'peak_to_second_ratio_distance_to_target' in metric_analysis:
            p2s_distance = metric_analysis['peak_to_second_ratio_distance_to_target']
            if p2s_distance < -0.3:  # More than 30% below target
                f.write("### Peak-to-Second Ratio Improvement\n\n")
                f.write("The peak-to-second ratio is significantly below the target. Consider:\n\n")
                f.write("1. Increasing the heatmap weight to focus more on corner detection\n")
                f.write("2. Adding more targeted augmentations for corner detection\n")
                f.write("3. Implementing a focal loss component that emphasizes corners with low p2s ratio\n")
                f.write("4. Increasing the peak enhancement weight in the loss function\n\n")
            elif p2s_distance < 0:  # Below target but close
                f.write("### Peak-to-Second Ratio Fine-Tuning\n\n")
                f.write("The peak-to-second ratio is close to the target but still below. Consider:\n\n")
                f.write("1. Fine-tuning with a lower learning rate\n")
                f.write("2. Slightly increasing the peak enhancement weight\n")
                f.write("3. Adding more training examples with challenging corner cases\n\n")

        # Overfitting recommendations
        if loss_analysis['overfitting_detected']:
            f.write("### Addressing Overfitting\n\n")
            f.write("Overfitting was detected. Consider:\n\n")
            f.write("1. Increasing dropout rate\n")
            f.write("2. Adding more data augmentation\n")
            f.write("3. Implementing early stopping with a shorter patience\n")
            f.write("4. Adding L2 regularization\n\n")

        # Per-corner recommendations
        if 'weakest_corner' in metric_analysis:
            f.write("### Per-Corner Improvements\n\n")
            f.write(f"The {metric_analysis['weakest_corner']} corner has the lowest peak-to-second ratio. Consider:\n\n")
            f.write("1. Adding more training examples that highlight this corner\n")
            f.write("2. Implementing a corner-specific loss weighting\n")
            f.write("3. Creating augmentations that specifically challenge this corner\n\n")

        # Conclusion
        f.write("## Conclusion\n\n")

        # Overall assessment
        overall_success = True
        for key in target_metrics.keys():
            if key + '_distance_to_target' in metric_analysis:
                if metric_analysis[key + '_distance_to_target'] < -0.1:  # More than 10% below target
                    overall_success = False

        if overall_success:
            f.write("The model has successfully achieved or is very close to all target metrics. ")
            f.write("It is ready for deployment or can be further fine-tuned for optimal performance.\n\n")
        else:
            f.write("The model has not yet achieved all target metrics. ")
            f.write("Further training or architectural improvements are recommended before deployment.\n\n")

        # Estimated additional training
        if 'estimated_epochs_to_target_p2s' in metric_analysis:
            epochs_to_target = metric_analysis['estimated_epochs_to_target_p2s']
            if epochs_to_target < float('inf'):
                f.write(f"Based on the current improvement rate, approximately {int(epochs_to_target)} ")
                f.write("additional epochs may be needed to reach the target peak-to-second ratio.\n")

    print(f"Analysis report generated at {report_path}")
    return report_path

def main():
    """
    Main function for generating analysis report.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Generate analysis report for chess board detection model v5.2')
    parser.add_argument('--history_path', type=str, required=True, help='Path to training history JSON file')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--phase_name', type=str, default='phase1', help='Name of the training phase')
    args = parser.parse_args()

    # Generate report
    report_path = generate_report(args.history_path, args.output_dir, args.phase_name)

    print(f"Report generated at {report_path}")

if __name__ == '__main__':
    main()
