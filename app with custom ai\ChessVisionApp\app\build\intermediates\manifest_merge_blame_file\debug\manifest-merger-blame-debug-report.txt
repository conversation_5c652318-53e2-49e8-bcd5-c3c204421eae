1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.chessvision.app"
4    android:versionCode="2"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <!-- Camera permissions -->
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:6:5-65
12-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:6:22-62
13    <uses-permission
13-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:7:5-8:38
14        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:7:22-78
15        android:maxSdkVersion="28" />
15-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:8:9-35
16    <uses-permission
16-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:9:5-10:38
17        android:name="android.permission.READ_EXTERNAL_STORAGE"
17-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:9:22-77
18        android:maxSdkVersion="32" />
18-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:10:9-35
19
20    <!-- Network permissions for Stockfish updates -->
21    <uses-permission android:name="android.permission.INTERNET" />
21-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:13:5-67
21-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:13:22-64
22    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
22-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:14:5-79
22-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:14:22-76
23
24    <!-- Camera features -->
25    <uses-feature
25-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:17:5-19:35
26        android:name="android.hardware.camera"
26-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:18:9-47
27        android:required="true" />
27-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:19:9-32
28    <uses-feature
28-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:20:5-22:36
29        android:name="android.hardware.camera.autofocus"
29-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:21:9-57
30        android:required="false" />
30-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:22:9-33
31
32    <permission
32-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.chessvision.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.chessvision.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
37
38    <application
38-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:24:5-59:19
39        android:name="com.chessvision.app.ChessVisionApplication"
39-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:25:9-47
40        android:allowBackup="true"
40-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:26:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\d5b68b955b795828701719ac184f2bfb\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:27:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:28:9-54
46        android:hardwareAccelerated="true"
46-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:34:9-43
47        android:icon="@mipmap/ic_launcher"
47-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:29:9-43
48        android:label="@string/app_name"
48-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:30:9-41
49        android:roundIcon="@mipmap/ic_launcher_round"
49-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:31:9-54
50        android:supportsRtl="true"
50-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:32:9-35
51        android:theme="@style/Theme.ChessVisionApp" >
51-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:33:9-52
52        <activity
52-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:37:9-46:20
53            android:name="com.chessvision.app.MainActivity"
53-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:38:13-41
54            android:exported="true"
54-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:39:13-36
55            android:screenOrientation="portrait"
55-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:41:13-49
56            android:theme="@style/Theme.ChessVisionApp" >
56-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:40:13-56
57            <intent-filter>
57-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:42:13-45:29
58                <action android:name="android.intent.action.MAIN" />
58-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:43:17-69
58-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:43:25-66
59
60                <category android:name="android.intent.category.LAUNCHER" />
60-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:44:17-77
60-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:44:27-74
61            </intent-filter>
62        </activity>
63
64        <!-- File provider for camera images -->
65        <provider
66            android:name="androidx.core.content.FileProvider"
66-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:50:13-62
67            android:authorities="com.chessvision.app.fileprovider"
67-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:51:13-64
68            android:exported="false"
68-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:52:13-37
69            android:grantUriPermissions="true" >
69-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:53:13-47
70            <meta-data
70-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:54:13-56:54
71                android:name="android.support.FILE_PROVIDER_PATHS"
71-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:55:17-67
72                android:resource="@xml/file_paths" />
72-->C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\AndroidManifest.xml:56:17-51
73        </provider>
74
75        <activity
75-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
76            android:name="androidx.compose.ui.tooling.PreviewActivity"
76-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
77            android:exported="true" />
77-->[androidx.compose.ui:ui-tooling-android:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\da0ac75a82617b23975ccbc9e3e37eaa\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
78
79        <service
79-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
80            android:name="androidx.camera.core.impl.MetadataHolderService"
80-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
81            android:enabled="false"
81-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
82            android:exported="false" >
82-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
83            <meta-data
83-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
84                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
84-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
85                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
85-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\f357fb61c747a7a368293f2efe1595d3\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
86        </service>
87
88        <provider
88-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
89            android:name="androidx.startup.InitializationProvider"
89-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
90            android:authorities="com.chessvision.app.androidx-startup"
90-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
91            android:exported="false" >
91-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
92            <meta-data
92-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
93                android:name="androidx.emoji2.text.EmojiCompatInitializer"
93-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
94                android:value="androidx.startup" />
94-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab96d4fdb576d58b6663dd729e38475a\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
95            <meta-data
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
97                android:value="androidx.startup" />
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\97c1b9cc3812a0f1b51d4498724f2512\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
100                android:value="androidx.startup" />
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
101        </provider>
102
103        <receiver
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
104            android:name="androidx.profileinstaller.ProfileInstallReceiver"
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
105            android:directBootAware="false"
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
106            android:enabled="true"
106-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
107            android:exported="true"
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
108            android:permission="android.permission.DUMP" >
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
110                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
113                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
113-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
116                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
116-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
119                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
119-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f2160ac109a05b5cf16e9db6fa447d5\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
120            </intent-filter>
121        </receiver>
122    </application>
123
124</manifest>
