"""
Configuration settings for the chess board detection project.
"""

import os
import torch

# Paths
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, 'data')
SYNTHETIC_DATA_DIR = os.path.join(DATA_DIR, 'synthetic')
REAL_DATA_DIR = os.path.join(DATA_DIR, 'real')
MODELS_DIR = os.path.join(BASE_DIR, 'models')

# Model parameters
INPUT_SIZE = (256, 256)  # Height, Width
BATCH_SIZE = 4  # Smaller batch size for real images
LEARNING_RATE = 1e-4
NUM_EPOCHS = 100  # More epochs for real images
# Check if CUDA is available and set the device accordingly
if torch.cuda.is_available():
    DEVICE = 'cuda:0'  # Use the first GPU (RTX 3050)
    print(f"Using GPU: {torch.cuda.get_device_name(0)}")
else:
    DEVICE = 'cpu'
    print("CUDA not available. Using CPU.")

# Data generation parameters
NUM_SYNTHETIC_IMAGES = 1000
VALIDATION_SPLIT = 0.2
TEST_SPLIT = 0.1

# Augmentation parameters
AUGMENTATION_PROBABILITY = 0.5

# Heatmap parameters
HEATMAP_SIGMA = 5  # Standard deviation for Gaussian heatmaps - smaller value for more focused corners
