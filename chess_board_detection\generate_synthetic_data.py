"""
Generate synthetic chess board images for training.

Since we don't have <PERSON><PERSON><PERSON> integrated directly, this script will generate
simplified synthetic chess board images using OpenCV.
"""

import os
import json
import random
import numpy as np
import cv2
from tqdm import tqdm
import matplotlib.pyplot as plt

from config import (
    SYNTHETIC_DATA_DIR, NUM_SYNTHETIC_IMAGES,
    VALIDATION_SPLIT, TEST_SPLIT
)


def create_chessboard(img_size=(512, 512), board_size=8, perspective_distortion=0.2):
    """
    Create a synthetic chess board image.
    
    Args:
        img_size (tuple): Image size (width, height).
        board_size (int): Number of squares in each dimension.
        perspective_distortion (float): Amount of perspective distortion.
    
    Returns:
        tuple: (image, mask, corners)
    """
    width, height = img_size
    
    # Create a blank image
    image = np.ones((height, width, 3), dtype=np.uint8) * 255
    mask = np.zeros((height, width), dtype=np.uint8)
    
    # Define the corners of the chess board (with some randomization)
    margin = int(min(width, height) * 0.1)
    
    # Base corners (top-left, top-right, bottom-right, bottom-left)
    base_corners = np.array([
        [margin, margin],
        [width - margin, margin],
        [width - margin, height - margin],
        [margin, height - margin]
    ], dtype=np.float32)
    
    # Apply random perspective distortion
    distortion = perspective_distortion * min(width, height)
    corners = base_corners + np.random.uniform(-distortion, distortion, base_corners.shape)
    corners = corners.astype(np.float32)
    
    # Create a mask for the chess board
    cv2.fillPoly(mask, [corners.astype(np.int32)], 255)
    
    # Create a perspective transform to map the distorted board to a square
    square_size = min(width, height) - 2 * margin
    square_corners = np.array([
        [margin, margin],
        [margin + square_size, margin],
        [margin + square_size, margin + square_size],
        [margin, margin + square_size]
    ], dtype=np.float32)
    
    M = cv2.getPerspectiveTransform(corners, square_corners)
    
    # Create a chess board pattern
    board = np.zeros((board_size * 50, board_size * 50), dtype=np.uint8)
    for i in range(board_size):
        for j in range(board_size):
            if (i + j) % 2 == 0:
                board[i*50:(i+1)*50, j*50:(j+1)*50] = 255
    
    # Apply the inverse perspective transform to map the chess board to the distorted shape
    M_inv = cv2.getPerspectiveTransform(square_corners, corners)
    warped_board = cv2.warpPerspective(board, M_inv, img_size)
    
    # Create the final image
    for c in range(3):
        # Add some color variation to the squares
        color_variation = np.random.uniform(0.8, 1.2)
        image_c = image[:, :, c].copy()
        image_c[mask > 0] = warped_board[mask > 0] * color_variation
        image[:, :, c] = image_c
    
    # Add some noise and blur
    noise = np.random.normal(0, 5, image.shape).astype(np.int32)
    image = np.clip(image.astype(np.int32) + noise, 0, 255).astype(np.uint8)
    image = cv2.GaussianBlur(image, (3, 3), 0)
    
    # Add random background
    background = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    background = cv2.GaussianBlur(background, (21, 21), 0)
    
    # Combine board with background
    mask_3d = np.stack([mask, mask, mask], axis=2) / 255.0
    image = (image * mask_3d + background * (1 - mask_3d)).astype(np.uint8)
    
    # Flatten corners for output
    corners_flat = corners.flatten().tolist()
    
    return image, mask, corners_flat


def generate_dataset(num_images, output_dir):
    """
    Generate a synthetic dataset of chess board images.
    
    Args:
        num_images (int): Number of images to generate.
        output_dir (str): Output directory.
    """
    # Create output directories
    os.makedirs(os.path.join(output_dir, 'images'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'masks'), exist_ok=True)
    os.makedirs(os.path.join(output_dir, 'keypoints'), exist_ok=True)
    
    # Generate images
    for i in tqdm(range(num_images), desc="Generating synthetic images"):
        # Randomize image size
        img_size = (random.randint(512, 768), random.randint(512, 768))
        
        # Randomize board size (usually 8x8, but we can add some variation)
        board_size = 8  # Standard chess board
        
        # Randomize perspective distortion
        perspective_distortion = random.uniform(0.05, 0.3)
        
        # Generate synthetic image
        image, mask, corners = create_chessboard(
            img_size=img_size,
            board_size=board_size,
            perspective_distortion=perspective_distortion
        )
        
        # Save image
        image_path = os.path.join(output_dir, 'images', f'board_{i:05d}.jpg')
        cv2.imwrite(image_path, cv2.cvtColor(image, cv2.COLOR_RGB2BGR))
        
        # Save mask
        mask_path = os.path.join(output_dir, 'masks', f'board_{i:05d}.png')
        cv2.imwrite(mask_path, mask)
        
        # Save keypoints
        keypoints_path = os.path.join(output_dir, 'keypoints', f'board_{i:05d}.json')
        with open(keypoints_path, 'w') as f:
            json.dump({
                'corners': corners,
                'image_size': img_size
            }, f)
        
        # Visualize a few examples
        if i < 5:
            plt.figure(figsize=(12, 4))
            
            plt.subplot(1, 3, 1)
            plt.imshow(image)
            plt.title(f'Image {i}')
            plt.axis('off')
            
            plt.subplot(1, 3, 2)
            plt.imshow(mask, cmap='gray')
            plt.title('Mask')
            plt.axis('off')
            
            plt.subplot(1, 3, 3)
            plt.imshow(image)
            corners_np = np.array(corners).reshape(-1, 2)
            plt.scatter(corners_np[:, 0], corners_np[:, 1], c='r', s=50)
            plt.title('Corners')
            plt.axis('off')
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, f'example_{i}.png'))
            plt.close()


def split_dataset(data_dir, val_split=0.2, test_split=0.1):
    """
    Split the dataset into train, validation, and test sets.
    
    Args:
        data_dir (str): Data directory.
        val_split (float): Validation split ratio.
        test_split (float): Test split ratio.
    """
    # Get all image filenames
    image_dir = os.path.join(data_dir, 'images')
    filenames = [f for f in os.listdir(image_dir) if f.endswith('.jpg') or f.endswith('.png')]
    
    # Shuffle filenames
    random.shuffle(filenames)
    
    # Calculate split indices
    n_total = len(filenames)
    n_test = int(n_total * test_split)
    n_val = int(n_total * val_split)
    n_train = n_total - n_test - n_val
    
    # Split filenames
    train_filenames = filenames[:n_train]
    val_filenames = filenames[n_train:n_train+n_val]
    test_filenames = filenames[n_train+n_val:]
    
    # Create index files
    for split, split_filenames in [
        ('train', train_filenames),
        ('val', val_filenames),
        ('test', test_filenames)
    ]:
        index = []
        for filename in split_filenames:
            image_path = os.path.join(data_dir, 'images', filename)
            mask_path = os.path.join(data_dir, 'masks', filename.replace('.jpg', '.png').replace('.png', '.png'))
            keypoints_path = os.path.join(data_dir, 'keypoints', filename.replace('.jpg', '.json').replace('.png', '.json'))
            
            index.append({
                'image': image_path,
                'mask': mask_path,
                'keypoints': keypoints_path
            })
        
        # Save index
        with open(os.path.join(data_dir, f'{split}_index.json'), 'w') as f:
            json.dump(index, f)
    
    print(f"Dataset split: {n_train} train, {n_val} validation, {n_test} test")


def main():
    """
    Main function.
    """
    # Create output directory
    os.makedirs(SYNTHETIC_DATA_DIR, exist_ok=True)
    
    # Generate dataset
    generate_dataset(NUM_SYNTHETIC_IMAGES, SYNTHETIC_DATA_DIR)
    
    # Split dataset
    split_dataset(SYNTHETIC_DATA_DIR, VALIDATION_SPLIT, TEST_SPLIT)
    
    print(f"Generated {NUM_SYNTHETIC_IMAGES} synthetic images in {SYNTHETIC_DATA_DIR}")


if __name__ == "__main__":
    main()
