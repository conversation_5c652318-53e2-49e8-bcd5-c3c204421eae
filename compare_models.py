"""
Compare the base model and fine-tuned model on test images.
This script runs both models on the same test images and displays the results side by side
with numbered predictions and a legend.
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
from pathlib import Path
from ultralytics import YOLO
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, Patch

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def compare_models(base_model_path, finetuned_model_path, test_images, output_dir=None, conf_threshold=0.7, iou_threshold=0.85):
    """
    Compare base model and fine-tuned model on test images.

    Args:
        base_model_path: Path to the base model
        finetuned_model_path: Path to the fine-tuned model
        test_images: List of paths to test images
        output_dir: Directory to save comparison results
        conf_threshold: Confidence threshold for predictions
        iou_threshold: IoU threshold for NMS (higher to avoid duplicates)
    """
    # Set up output directory
    if output_dir is None:
        output_dir = "model_comparison"
    os.makedirs(output_dir, exist_ok=True)

    # Load models
    print(f"Loading base model from {base_model_path}")
    base_model = YOLO(base_model_path)

    print(f"Loading fine-tuned model from {finetuned_model_path}")
    finetuned_model = YOLO(finetuned_model_path)

    # Class names and colors
    class_names = ['white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
                   'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king']

    # Define colors for each class (white pieces in light colors, black pieces in dark colors)
    colors = {
        'white_pawn': (200, 200, 200),    # Light gray
        'white_knight': (173, 216, 230),  # Light blue
        'white_bishop': (152, 251, 152),  # Light green
        'white_rook': (255, 182, 193),    # Light pink
        'white_queen': (255, 255, 224),   # Light yellow
        'white_king': (255, 228, 196),    # Light orange
        'black_pawn': (100, 100, 100),    # Dark gray
        'black_knight': (70, 130, 180),   # Dark blue
        'black_bishop': (34, 139, 34),    # Dark green
        'black_rook': (178, 34, 34),      # Dark red
        'black_queen': (218, 165, 32),    # Dark yellow/gold
        'black_king': (139, 69, 19)       # Dark brown
    }

    # Convert colors from BGR to RGB for matplotlib
    colors_rgb = {}
    for piece, color in colors.items():
        colors_rgb[piece] = (color[2]/255, color[1]/255, color[0]/255)  # Convert to RGB and normalize to 0-1

    # Process each test image
    for img_path in test_images:
        print(f"Processing {img_path}")

        # Run predictions with both models with higher IoU threshold to avoid duplicates
        base_results = base_model(img_path, conf=conf_threshold, iou=iou_threshold)[0]
        finetuned_results = finetuned_model(img_path, conf=conf_threshold, iou=iou_threshold)[0]

        # Get the original image
        img = cv2.imread(img_path)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Create copies for drawing
        base_img = img.copy()
        finetuned_img = img.copy()

        # Process base model results
        base_boxes = base_results.boxes
        base_classes = []
        base_confidences = []

        for i, box in enumerate(base_boxes):
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
            cls_id = int(box.cls[0].item())
            conf = box.conf[0].item()

            class_name = class_names[cls_id]
            color = colors[class_name]

            # Draw bounding box
            cv2.rectangle(base_img, (x1, y1), (x2, y2), color, 2)

            # Draw only the number (no text label)
            cv2.putText(base_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)  # Black outline
            cv2.putText(base_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)  # White text

            base_classes.append(class_name)
            base_confidences.append(conf)

        # Process fine-tuned model results
        finetuned_boxes = finetuned_results.boxes
        finetuned_classes = []
        finetuned_confidences = []

        for i, box in enumerate(finetuned_boxes):
            x1, y1, x2, y2 = box.xyxy[0].cpu().numpy().astype(int)
            cls_id = int(box.cls[0].item())
            conf = box.conf[0].item()

            class_name = class_names[cls_id]
            color = colors[class_name]

            # Draw bounding box
            cv2.rectangle(finetuned_img, (x1, y1), (x2, y2), color, 2)

            # Draw only the number (no text label)
            cv2.putText(finetuned_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)  # Black outline
            cv2.putText(finetuned_img, f"{i+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)  # White text

            finetuned_classes.append(class_name)
            finetuned_confidences.append(conf)

        # Create side-by-side comparison with legend
        plt.figure(figsize=(24, 12))

        # Base model subplot
        plt.subplot(1, 2, 1)
        plt.imshow(base_img)
        plt.title(f"Base Model: {len(base_boxes)} detections", fontsize=16)
        plt.axis('off')

        # Add base model legend
        base_legend_elements = []
        for i, (cls, conf) in enumerate(zip(base_classes, base_confidences)):
            base_legend_elements.append(
                Patch(facecolor=colors_rgb[cls], edgecolor='black',
                      label=f"{i+1}: {cls} ({conf:.2f})")
            )
        plt.legend(handles=base_legend_elements, loc='upper right',
                   bbox_to_anchor=(1.0, 1.0), fontsize=10, title="Base Model Detections")

        # Fine-tuned model subplot
        plt.subplot(1, 2, 2)
        plt.imshow(finetuned_img)
        plt.title(f"Fine-tuned Model: {len(finetuned_boxes)} detections", fontsize=16)
        plt.axis('off')

        # Add fine-tuned model legend
        finetuned_legend_elements = []
        for i, (cls, conf) in enumerate(zip(finetuned_classes, finetuned_confidences)):
            finetuned_legend_elements.append(
                Patch(facecolor=colors_rgb[cls], edgecolor='black',
                      label=f"{i+1}: {cls} ({conf:.2f})")
            )
        plt.legend(handles=finetuned_legend_elements, loc='upper right',
                   bbox_to_anchor=(1.0, 1.0), fontsize=10, title="Fine-tuned Model Detections")

        plt.tight_layout()

        # Save comparison image
        img_name = os.path.basename(img_path)
        output_path = os.path.join(output_dir, f"comparison_{img_name}")
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        # Print comparison statistics
        print(f"\nComparison for {img_name}:")
        print(f"Base model: {len(base_boxes)} detections")
        print(f"Fine-tuned model: {len(finetuned_boxes)} detections")

        # Check for differences in classifications
        if len(base_classes) == len(finetuned_classes):
            differences = 0
            for i in range(len(base_classes)):
                if base_classes[i] != finetuned_classes[i]:
                    differences += 1
                    print(f"  Difference: Base model #{i+1}: {base_classes[i]} ({base_confidences[i]:.2f}), Fine-tuned model #{i+1}: {finetuned_classes[i]} ({finetuned_confidences[i]:.2f})")

            if differences == 0:
                print("  No classification differences found")
            else:
                print(f"  Found {differences} classification differences")
        else:
            print("  Different number of detections, cannot directly compare classifications")

        print(f"Comparison saved to {output_path}")
        print("-" * 50)

    print(f"All comparisons completed and saved to {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Compare base model and fine-tuned model on test images")
    parser.add_argument("--base_model", type=str, default="runs/detect/train/weights/best.pt", help="Path to base model")
    parser.add_argument("--finetuned_model", type=str, default="runs/cls_focused/cls_focused_20250522_133104/weights/best.pt", help="Path to fine-tuned model")
    parser.add_argument("--test_images", type=str, nargs='+', default=["24.jpg", "25.jpg", "26.jpg", "27.jpg"], help="Paths to test images")
    parser.add_argument("--output_dir", type=str, default="model_comparison_clean", help="Directory to save comparison results")
    parser.add_argument("--conf", type=float, default=0.7, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.85, help="IoU threshold")

    args = parser.parse_args()

    # Print system info
    print_system_info()

    # Compare models
    compare_models(
        args.base_model,
        args.finetuned_model,
        args.test_images,
        args.output_dir,
        args.conf,
        args.iou
    )

if __name__ == "__main__":
    main()
