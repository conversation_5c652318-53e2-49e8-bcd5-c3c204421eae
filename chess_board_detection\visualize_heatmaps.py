import argparse
import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import torchvision.transforms as transforms
from models.enhanced_unet_v5_2 import ChessBoardDetector

def load_model(model_path):
    """Load the trained model"""
    model = ChessBoardDetector()
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path):
    """Preprocess the image for model input"""
    # Load image
    image = Image.open(image_path).convert('RGB')
    original_image = np.array(image)

    # Get original dimensions
    original_dims = original_image.shape[:2]

    # Preprocess
    transform = transforms.Compose([
        transforms.Resize((256, 256)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    input_tensor = transform(image).unsqueeze(0)
    return input_tensor, original_dims, original_image

def find_corners_from_heatmaps(heatmaps, threshold=0.3):
    """Find corners from heatmaps"""
    corners = []
    confidences = []

    for i in range(4):
        heatmap = heatmaps[0, i].detach().numpy()
        max_idx = np.argmax(heatmap)
        y, x = np.unravel_index(max_idx, heatmap.shape)
        confidence = heatmap[y, x]

        if confidence > threshold:
            corners.append((x, y))
            confidences.append(confidence)
        else:
            corners.append(None)
            confidences.append(confidence)

    return corners, confidences

def visualize_heatmaps(original_image, heatmaps, corners, confidences, output_path=None):
    """Visualize heatmaps with both blue background and overlaid on original image"""
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Create a figure with 3 rows: original image, blue background heatmaps, overlaid heatmaps
    fig, axs = plt.subplots(3, 4, figsize=(20, 15))

    # Row 0: Original image with corners
    for i in range(4):
        axs[0, i].imshow(original_image)
        axs[0, i].set_title(f'Original Image with {corner_names[i]} Corner')

        # Mark the detected corner if available
        if corners[i]:
            x, y = corners[i]
            axs[0, i].scatter(x, y, c='red', s=50, marker='x')
            axs[0, i].text(x+5, y+5, f'Conf: {confidences[i]:.4f}', color='white',
                          backgroundcolor='black', fontsize=8)

        axs[0, i].axis('off')

    # Row 1: Heatmaps with blue background
    for i in range(4):
        # Create blue background
        blue_bg = np.zeros((heatmaps.shape[2], heatmaps.shape[3], 3))
        blue_bg[:, :, 2] = 1.0  # Set blue channel to 1

        axs[1, i].imshow(blue_bg)
        axs[1, i].imshow(heatmaps[0, i].detach().numpy(), alpha=0.7, cmap='hot')
        axs[1, i].set_title(f'{corner_names[i]} Heatmap (Blue Background)')

        # Mark the detected corner if available
        if corners[i]:
            x, y = corners[i]
            axs[1, i].scatter(x, y, c='cyan', s=50, marker='x')

        axs[1, i].axis('off')

    # Row 2: Heatmaps overlaid on original image
    for i in range(4):
        axs[2, i].imshow(original_image)
        axs[2, i].imshow(heatmaps[0, i].detach().numpy(), alpha=0.5, cmap='hot')
        axs[2, i].set_title(f'{corner_names[i]} Heatmap (Overlaid)')

        # Mark the detected corner if available
        if corners[i]:
            x, y = corners[i]
            axs[2, i].scatter(x, y, c='blue', s=50, marker='x')

        axs[2, i].axis('off')

    plt.tight_layout()

    if output_path:
        plt.savefig(output_path)
        print(f"Visualization saved to {output_path}")

    plt.show()

def main(args):
    # Load model
    model = load_model(args.model_path)

    # Preprocess image
    input_tensor, original_dims, original_image = preprocess_image(args.image_path)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Process outputs
    mask_pred = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    mask = torch.sigmoid(mask_pred).squeeze().detach().numpy() > 0.5

    # Find corners
    corners, confidences = find_corners_from_heatmaps(heatmaps, threshold=args.threshold)

    # Print corner coordinates and confidences
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    print("Detected corners:")
    for i, (corner, conf) in enumerate(zip(corners, confidences)):
        if corner:
            print(f"{corner_names[i]}: {corner} (Confidence: {conf:.4f})")
        else:
            print(f"{corner_names[i]}: Not detected (Confidence: {conf:.4f})")

    # Visualize
    visualize_heatmaps(original_image, heatmaps, corners, confidences, args.output_path)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Visualize heatmaps from chess board detection model')
    parser.add_argument('--model_path', type=str, required=True, help='Path to the model weights')
    parser.add_argument('--image_path', type=str, required=True, help='Path to the test image')
    parser.add_argument('--output_path', type=str, default=None, help='Path to save visualization')
    parser.add_argument('--threshold', type=float, default=0.3, help='Confidence threshold for corner detection')

    args = parser.parse_args()
    main(args)
