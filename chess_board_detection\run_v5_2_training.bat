@echo off
echo Starting v5.2 chess board detection model training...

python "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_v5_2.py" ^
--data_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real" ^
--annotation_file "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json" ^
--output_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection" ^
--epochs_phase1 20 ^
--epochs_phase2 40 ^
--epochs_phase3 20 ^
--lr_phase1 0.0008 ^
--lr_phase2 0.0005 ^
--lr_phase3 0.0002 ^
--dropout_rate 0.3 ^
--weight_decay 1e-4 ^
--batch_size 4 ^
--save_interval 5 ^
--continue_from_v5_1 ^
--cpu

echo Training completed!
pause
