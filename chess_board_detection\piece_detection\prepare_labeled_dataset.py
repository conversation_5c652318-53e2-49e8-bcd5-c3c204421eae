"""
Prepare Labeled Dataset for YOLO Training

This script:
1. Takes manually labeled chess piece images
2. Organizes them into a YOLO-compatible dataset structure
3. Creates train/val splits
4. Generates data.yaml file for training
"""

import os
import sys
import argparse
import shutil
import random
import yaml
from pathlib import Path
import json

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Chess piece classes
PIECE_CLASSES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

def prepare_dataset(input_dir, output_dir, split_ratio=0.8, seed=42):
    """
    Prepare a YOLO dataset from manually labeled images.
    
    Args:
        input_dir: Directory containing labeled images and annotations
        output_dir: Directory to save the YOLO dataset
        split_ratio: Train/val split ratio
        seed: Random seed for reproducibility
    """
    # Set random seed
    random.seed(seed)
    
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    
    train_dir = os.path.join(output_dir, "train")
    val_dir = os.path.join(output_dir, "val")
    
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    
    os.makedirs(os.path.join(train_dir, "images"), exist_ok=True)
    os.makedirs(os.path.join(train_dir, "labels"), exist_ok=True)
    os.makedirs(os.path.join(val_dir, "images"), exist_ok=True)
    os.makedirs(os.path.join(val_dir, "labels"), exist_ok=True)
    
    # Get all labeled images
    image_dir = os.path.join(input_dir, "images")
    label_dir = os.path.join(input_dir, "labels")
    
    image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
    
    # Check which images have corresponding labels
    valid_images = []
    for img_file in image_files:
        base_name = os.path.splitext(img_file)[0]
        label_file = f"{base_name}.txt"
        
        if os.path.exists(os.path.join(label_dir, label_file)):
            valid_images.append(img_file)
    
    print(f"Found {len(valid_images)} labeled images")
    
    if not valid_images:
        print("No labeled images found. Please label some images first.")
        return
    
    # Split into train/val
    random.shuffle(valid_images)
    split_idx = int(len(valid_images) * split_ratio)
    
    train_images = valid_images[:split_idx]
    val_images = valid_images[split_idx:]
    
    print(f"Train set: {len(train_images)} images")
    print(f"Validation set: {len(val_images)} images")
    
    # Copy files to train/val directories
    for img_file in train_images:
        base_name = os.path.splitext(img_file)[0]
        label_file = f"{base_name}.txt"
        
        # Copy image
        shutil.copy2(
            os.path.join(image_dir, img_file),
            os.path.join(train_dir, "images", img_file)
        )
        
        # Copy label
        shutil.copy2(
            os.path.join(label_dir, label_file),
            os.path.join(train_dir, "labels", label_file)
        )
    
    for img_file in val_images:
        base_name = os.path.splitext(img_file)[0]
        label_file = f"{base_name}.txt"
        
        # Copy image
        shutil.copy2(
            os.path.join(image_dir, img_file),
            os.path.join(val_dir, "images", img_file)
        )
        
        # Copy label
        shutil.copy2(
            os.path.join(label_dir, label_file),
            os.path.join(val_dir, "labels", label_file)
        )
    
    # Create data.yaml file
    data_yaml = {
        'train': os.path.abspath(os.path.join(train_dir, "images")),
        'val': os.path.abspath(os.path.join(val_dir, "images")),
        'nc': len(PIECE_CLASSES),
        'names': PIECE_CLASSES
    }
    
    with open(os.path.join(output_dir, "data.yaml"), 'w') as f:
        yaml.dump(data_yaml, f, default_flow_style=False)
    
    print(f"Dataset prepared at {output_dir}")
    print(f"data.yaml created at {os.path.join(output_dir, 'data.yaml')}")
    print("\nNext steps:")
    print("1. Train YOLO model using:")
    print(f"   python chess_board_detection/piece_detection/train_yolo.py --data {os.path.join(output_dir, 'data.yaml')}")

def analyze_dataset(input_dir):
    """
    Analyze the labeled dataset.
    
    Args:
        input_dir: Directory containing labeled images and annotations
    """
    label_dir = os.path.join(input_dir, "labels")
    
    # Count classes
    class_counts = {class_name: 0 for class_name in PIECE_CLASSES}
    total_annotations = 0
    
    # Get all label files
    label_files = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
    
    for label_file in label_files:
        label_path = os.path.join(label_dir, label_file)
        
        with open(label_path, 'r') as f:
            lines = f.read().strip().split('\n')
        
        for line in lines:
            if not line.strip():
                continue
            
            parts = line.strip().split()
            if len(parts) != 5:
                continue
            
            class_id = int(parts[0])
            if 0 <= class_id < len(PIECE_CLASSES):
                class_name = PIECE_CLASSES[class_id]
                class_counts[class_name] += 1
                total_annotations += 1
    
    print(f"Dataset Analysis ({len(label_files)} images, {total_annotations} annotations):")
    print("-" * 50)
    
    for class_name, count in class_counts.items():
        print(f"{class_name}: {count} ({count/total_annotations*100:.1f}%)")
    
    print("-" * 50)
    print(f"Total: {total_annotations}")

def main():
    parser = argparse.ArgumentParser(description='Prepare Labeled Dataset for YOLO Training')
    parser.add_argument('--input_dir', type=str, default='chess_board_detection/piece_detection/dataset',
                        help='Directory containing labeled images and annotations')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/piece_detection/yolo_dataset',
                        help='Directory to save the YOLO dataset')
    parser.add_argument('--split_ratio', type=float, default=0.8,
                        help='Train/val split ratio')
    parser.add_argument('--analyze', action='store_true',
                        help='Analyze the dataset without preparing it')
    args = parser.parse_args()
    
    if args.analyze:
        analyze_dataset(args.input_dir)
    else:
        prepare_dataset(args.input_dir, args.output_dir, args.split_ratio)

if __name__ == "__main__":
    main()
