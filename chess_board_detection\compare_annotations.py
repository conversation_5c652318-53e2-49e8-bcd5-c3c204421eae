"""
<PERSON><PERSON><PERSON> to compare manual annotations with model predictions.
This provides a fair assessment of model performance against human annotation.
"""

import os
import sys
import numpy as np
import cv2
import json
import matplotlib.pyplot as plt
import pandas as pd
import glob
from matplotlib.patches import Polygon
from matplotlib.collections import PatchCollection

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_manual_annotation(annotation_dir):
    """Load the most recent manual annotation."""
    # Find the most recent JSON file
    json_files = glob.glob(os.path.join(annotation_dir, 'manual_annotation_*.json'))
    if not json_files:
        raise ValueError(f"No manual annotations found in {annotation_dir}")

    # Sort by modification time (most recent first)
    json_files.sort(key=os.path.getmtime, reverse=True)
    latest_json = json_files[0]

    # Load the annotation
    with open(latest_json, 'r') as f:
        annotation = json.load(f)

    return annotation, latest_json

def load_model_predictions(predictions_path):
    """Load model predictions from CSV or JSON file."""
    if not os.path.exists(predictions_path):
        raise ValueError(f"Model predictions file not found: {predictions_path}")

    # Check file extension
    if predictions_path.endswith('.csv'):
        # Load from CSV
        predictions_df = pd.read_csv(predictions_path)

        # Organize by model
        models = predictions_df['Model'].unique()
        predictions = {}

        for model in models:
            model_df = predictions_df[predictions_df['Model'] == model]
            predictions[model] = {}

            for _, row in model_df.iterrows():
                corner = row['Corner']
                x = row['X']
                y = row['Y']
                confidence = row['Confidence']

                predictions[model][corner] = {
                    'x': x,
                    'y': y,
                    'confidence': confidence
                }

    elif predictions_path.endswith('.json'):
        # Load from JSON
        with open(predictions_path, 'r') as f:
            json_data = json.load(f)

        predictions = {}
        for model_name, corners in json_data.items():
            predictions[model_name] = {}

            for corner_name, data in corners.items():
                predictions[model_name][corner_name] = {
                    'x': data['x'],
                    'y': data['y'],
                    'confidence': data['confidence']
                }

    else:
        raise ValueError(f"Unsupported file format: {predictions_path}")

    return predictions

def calculate_metrics(manual_corners, model_corners):
    """Calculate metrics comparing manual and model annotations."""
    metrics = {}

    # Calculate Euclidean distance for each corner
    distances = {}
    total_distance = 0

    for corner in manual_corners:
        manual_x = manual_corners[corner]['x']
        manual_y = manual_corners[corner]['y']

        model_x = model_corners[corner]['x']
        model_y = model_corners[corner]['y']

        # Calculate Euclidean distance
        distance = np.sqrt((manual_x - model_x)**2 + (manual_y - model_y)**2)
        distances[corner] = distance
        total_distance += distance

    # Calculate average distance
    avg_distance = total_distance / len(manual_corners)

    # Calculate IoU (Intersection over Union) of the quadrilaterals
    manual_quad = np.array([(manual_corners[c]['x'], manual_corners[c]['y'])
                           for c in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']])

    model_quad = np.array([(model_corners[c]['x'], model_corners[c]['y'])
                          for c in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']])

    # Calculate IoU using the Shoelace formula for area and polygon intersection
    iou = calculate_polygon_iou(manual_quad, model_quad)

    # Store metrics
    metrics['distances'] = distances
    metrics['average_distance'] = avg_distance
    metrics['iou'] = iou

    return metrics

def calculate_polygon_iou(poly1, poly2):
    """Calculate IoU between two polygons using the Shoelace formula."""
    # Calculate areas using the Shoelace formula
    def polygon_area(vertices):
        x = vertices[:, 0]
        y = vertices[:, 1]
        return 0.5 * np.abs(np.dot(x, np.roll(y, 1)) - np.dot(y, np.roll(x, 1)))

    area1 = polygon_area(poly1)
    area2 = polygon_area(poly2)

    # For simplicity, we'll approximate the intersection area
    # by creating a fine grid and counting points inside both polygons

    # Get bounds
    min_x = min(np.min(poly1[:, 0]), np.min(poly2[:, 0]))
    max_x = max(np.max(poly1[:, 0]), np.max(poly2[:, 0]))
    min_y = min(np.min(poly1[:, 1]), np.min(poly2[:, 1]))
    max_y = max(np.max(poly1[:, 1]), np.max(poly2[:, 1]))

    # Create a grid
    grid_size = 100
    x_grid = np.linspace(min_x, max_x, grid_size)
    y_grid = np.linspace(min_y, max_y, grid_size)
    xx, yy = np.meshgrid(x_grid, y_grid)
    points = np.vstack((xx.flatten(), yy.flatten())).T

    # Check which points are inside each polygon
    from matplotlib.path import Path
    path1 = Path(poly1)
    path2 = Path(poly2)

    mask1 = path1.contains_points(points)
    mask2 = path2.contains_points(points)

    # Calculate intersection and union
    intersection = np.sum(mask1 & mask2)
    union = np.sum(mask1 | mask2)

    # Calculate IoU
    if union == 0:
        return 0.0

    # Scale by the ratio of the grid area to the bounding box area
    grid_area = (max_x - min_x) * (max_y - min_y)
    point_area = grid_area / (grid_size * grid_size)

    intersection_area = intersection * point_area
    union_area = union * point_area

    return intersection_area / union_area

def visualize_comparison(image_path, manual_annotation, model_predictions, metrics, output_path):
    """Create a visualization comparing manual and model annotations."""
    # Load the image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # Create figure
    fig, ax = plt.subplots(figsize=(15, 12))
    fig.suptitle('Comparison of Manual Annotation vs. Model Predictions', fontsize=16)

    # Display the image
    ax.imshow(image)

    # Get manual corners
    manual_corners = manual_annotation['corners']

    # Define colors and markers
    styles = {
        'Manual': {'color': 'white', 'marker': 'o', 'linestyle': '-', 'linewidth': 3, 'markersize': 10},
        'Phase2_Epoch16': {'color': 'red', 'marker': 'x', 'linestyle': '--', 'linewidth': 2, 'markersize': 10},
        'Phase3_Epoch8': {'color': 'blue', 'marker': '+', 'linestyle': ':', 'linewidth': 2, 'markersize': 10}
    }

    # Plot manual annotation
    manual_xs = []
    manual_ys = []

    for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
        x = manual_corners[corner]['x']
        y = manual_corners[corner]['y']
        manual_xs.append(x)
        manual_ys.append(y)

        ax.scatter(x, y, c=styles['Manual']['color'], marker=styles['Manual']['marker'],
                  s=styles['Manual']['markersize']**2, linewidths=2)
        ax.text(x+10, y+10, f"Manual {corner}\n({int(x)}, {int(y)})",
               color=styles['Manual']['color'], fontsize=10,
               bbox=dict(facecolor='black', alpha=0.7))

    # Close the polygon
    manual_xs.append(manual_xs[0])
    manual_ys.append(manual_ys[0])

    # Plot manual polygon
    ax.plot(manual_xs, manual_ys, color=styles['Manual']['color'],
           linestyle=styles['Manual']['linestyle'],
           linewidth=styles['Manual']['linewidth'], alpha=0.7)

    # Plot model predictions
    legend_elements = [plt.Line2D([0], [0], color=styles['Manual']['color'],
                                 marker=styles['Manual']['marker'],
                                 linestyle=styles['Manual']['linestyle'],
                                 linewidth=styles['Manual']['linewidth'],
                                 markersize=styles['Manual']['markersize'],
                                 label='Manual Annotation')]

    for model_name, corners in model_predictions.items():
        if model_name not in styles:
            continue

        style = styles[model_name]
        xs = []
        ys = []

        for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
            x = corners[corner]['x']
            y = corners[corner]['y']
            xs.append(x)
            ys.append(y)

            ax.scatter(x, y, c=style['color'], marker=style['marker'],
                      s=style['markersize']**2, linewidths=2)
            ax.text(x-10, y-10, f"{model_name} {corner}\n({int(x)}, {int(y)})",
                   color=style['color'], fontsize=10,
                   bbox=dict(facecolor='black', alpha=0.7))

        # Close the polygon
        xs.append(xs[0])
        ys.append(ys[0])

        # Plot model polygon
        ax.plot(xs, ys, color=style['color'],
               linestyle=style['linestyle'],
               linewidth=style['linewidth'], alpha=0.7)

        # Add to legend
        legend_elements.append(plt.Line2D([0], [0], color=style['color'],
                                         marker=style['marker'],
                                         linestyle=style['linestyle'],
                                         linewidth=style['linewidth'],
                                         markersize=style['markersize'],
                                         label=f'{model_name}'))

    # Add legend
    ax.legend(handles=legend_elements, loc='upper right', fontsize=12)

    # Add metrics as text
    metrics_text = "Performance Metrics:\n\n"

    for model_name, model_metrics in metrics.items():
        metrics_text += f"{model_name}:\n"
        metrics_text += f"  IoU: {model_metrics['iou']:.4f}\n"
        metrics_text += f"  Avg Distance: {model_metrics['average_distance']:.2f} pixels\n"
        metrics_text += "  Corner Distances (pixels):\n"

        for corner, distance in model_metrics['distances'].items():
            metrics_text += f"    {corner}: {distance:.2f}\n"

        metrics_text += "\n"

    # Add metrics text to figure
    plt.figtext(0.02, 0.02, metrics_text, fontsize=12,
               bbox=dict(facecolor='white', alpha=0.8))

    # Remove axes
    ax.axis('off')

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    # Create a zoomed view of each corner
    create_corner_zoom_comparison(image, manual_corners, model_predictions,
                                 output_path.replace('.png', '_zoomed.png'))

    return output_path

def create_corner_zoom_comparison(image, manual_corners, model_predictions, output_path):
    """Create a zoomed view of each corner for detailed comparison."""
    # Create figure with 2x2 grid
    fig, axs = plt.subplots(2, 2, figsize=(15, 15))
    fig.suptitle('Zoomed Corner Comparison', fontsize=16)

    # Define colors and markers
    styles = {
        'Manual': {'color': 'white', 'marker': 'o', 'markersize': 15},
        'Phase2_Epoch16': {'color': 'red', 'marker': 'x', 'markersize': 15},
        'Phase3_Epoch8': {'color': 'blue', 'marker': '+', 'markersize': 15}
    }

    # Corner names and positions in the grid
    corners = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    positions = [(0, 0), (0, 1), (1, 1), (1, 0)]

    # For each corner
    for corner, pos in zip(corners, positions):
        ax = axs[pos]

        # Display the image
        ax.imshow(image)
        ax.set_title(f'{corner} Corner', fontsize=14)

        # Get manual corner
        manual_x = manual_corners[corner]['x']
        manual_y = manual_corners[corner]['y']

        # Plot manual corner
        ax.scatter(manual_x, manual_y, c=styles['Manual']['color'],
                  marker=styles['Manual']['marker'],
                  s=styles['Manual']['markersize']**2, linewidths=2)

        # Plot model corners
        for model_name, corners_dict in model_predictions.items():
            if model_name not in styles:
                continue

            style = styles[model_name]
            model_x = corners_dict[corner]['x']
            model_y = corners_dict[corner]['y']

            ax.scatter(model_x, model_y, c=style['color'],
                      marker=style['marker'],
                      s=style['markersize']**2, linewidths=2)

        # Calculate zoom region
        all_x = [manual_x] + [corners_dict[corner]['x'] for model_name, corners_dict in model_predictions.items()]
        all_y = [manual_y] + [corners_dict[corner]['y'] for model_name, corners_dict in model_predictions.items()]

        center_x = np.mean(all_x)
        center_y = np.mean(all_y)

        # Calculate max distance from center
        max_dist = max([np.sqrt((x - center_x)**2 + (y - center_y)**2) for x, y in zip(all_x, all_y)])
        zoom_radius = max(max_dist * 2, 100)  # At least 100 pixels radius

        # Set zoom limits
        ax.set_xlim(center_x - zoom_radius, center_x + zoom_radius)
        ax.set_ylim(center_y - zoom_radius, center_y + zoom_radius)

        # Add corner labels
        for model_name, corners_dict in model_predictions.items():
            if model_name not in styles:
                continue

            style = styles[model_name]
            model_x = corners_dict[corner]['x']
            model_y = corners_dict[corner]['y']

            # Calculate distance from manual
            distance = np.sqrt((model_x - manual_x)**2 + (model_y - manual_y)**2)

            ax.text(model_x + 5, model_y + 5,
                   f"{model_name}\n({int(model_x)}, {int(model_y)})\nDist: {distance:.2f}px",
                   color=style['color'], fontsize=10,
                   bbox=dict(facecolor='black', alpha=0.7))

        # Add manual label
        ax.text(manual_x - 5, manual_y - 5,
               f"Manual\n({int(manual_x)}, {int(manual_y)})",
               color=styles['Manual']['color'], fontsize=10,
               bbox=dict(facecolor='black', alpha=0.7))

    # Add legend
    legend_elements = []
    for name, style in styles.items():
        legend_elements.append(plt.Line2D([0], [0], color=style['color'],
                                         marker=style['marker'],
                                         markersize=style['markersize'] // 2,
                                         label=name))

    fig.legend(handles=legend_elements, loc='lower center', fontsize=12, ncol=3)

    # Save figure
    plt.tight_layout(rect=[0, 0.03, 1, 0.97])
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    manual_annotation_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\manual_annotations"

    # Use the two-stage detection results
    model_predictions_json = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\two_stage\\two_stage_keypoints.json"

    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\comparison"

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Load manual annotation
    manual_annotation, annotation_path = load_manual_annotation(manual_annotation_dir)
    print(f"Loaded manual annotation from: {annotation_path}")

    # Load model predictions
    model_predictions = load_model_predictions(model_predictions_json)
    print(f"Loaded model predictions from: {model_predictions_json}")

    # Calculate metrics
    metrics = {}
    for model_name, corners in model_predictions.items():
        metrics[model_name] = calculate_metrics(manual_annotation['corners'], corners)

    # Create comparison visualization
    output_path = os.path.join(output_dir, "two_stage_comparison.png")
    visualize_comparison(image_path, manual_annotation, model_predictions, metrics, output_path)

    print(f"Comparison visualization saved to: {output_path}")
    print(f"Zoomed comparison saved to: {output_path.replace('.png', '_zoomed.png')}")

    # Print metrics
    print("\nPerformance Metrics:")
    for model_name, model_metrics in metrics.items():
        print(f"\n{model_name}:")
        print(f"  IoU: {model_metrics['iou']:.4f}")
        print(f"  Average Distance: {model_metrics['average_distance']:.2f} pixels")
        print("  Corner Distances (pixels):")
        for corner, distance in model_metrics['distances'].items():
            print(f"    {corner}: {distance:.2f}")

if __name__ == "__main__":
    main()
