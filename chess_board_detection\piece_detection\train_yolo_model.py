"""
Train a YOLO model for chess piece detection.
This script:
1. Loads a pre-trained YOLO model (e.g., YOLO11n)
2. Fine-tunes it on the chess piece dataset
3. Exports the trained model to ONNX format for deployment
"""

import os
import argparse
import torch
import platform
from ultralytics import YOLO
from datetime import datetime
import shutil
import yaml

def split_dataset(input_dir, output_dir, train_ratio=0.8, seed=42):
    """
    Split the labeled dataset into training and validation sets.

    Args:
        input_dir: Directory containing the labeled images and annotations
        output_dir: Directory to save the split dataset
        train_ratio: Ratio of images to use for training (default: 0.8)
        seed: Random seed for reproducibility
    """
    import random
    from pathlib import Path

    random.seed(seed)

    # Create output directories
    train_img_dir = os.path.join(output_dir, 'images', 'train')
    val_img_dir = os.path.join(output_dir, 'images', 'val')
    train_label_dir = os.path.join(output_dir, 'labels', 'train')
    val_label_dir = os.path.join(output_dir, 'labels', 'val')

    os.makedirs(train_img_dir, exist_ok=True)
    os.makedirs(val_img_dir, exist_ok=True)
    os.makedirs(train_label_dir, exist_ok=True)
    os.makedirs(val_label_dir, exist_ok=True)

    # Get all image files
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png']:
        image_files.extend(list(Path(input_dir).glob(f'*{ext}')))

    # Shuffle and split
    random.shuffle(image_files)
    split_idx = int(len(image_files) * train_ratio)
    train_files = image_files[:split_idx]
    val_files = image_files[split_idx:]

    print(f"Total images: {len(image_files)}")
    print(f"Training images: {len(train_files)}")
    print(f"Validation images: {len(val_files)}")

    # Copy files to their respective directories
    for img_path in train_files:
        # Copy image
        shutil.copy(img_path, os.path.join(train_img_dir, img_path.name))

        # Copy corresponding label if it exists
        label_path = os.path.join(os.path.dirname(input_dir), 'labels', f"{img_path.stem}.txt")
        if os.path.exists(label_path):
            shutil.copy(label_path, os.path.join(train_label_dir, f"{img_path.stem}.txt"))
        else:
            print(f"Warning: No label file found for {img_path.name}")

    for img_path in val_files:
        # Copy image
        shutil.copy(img_path, os.path.join(val_img_dir, img_path.name))

        # Copy corresponding label if it exists
        label_path = os.path.join(os.path.dirname(input_dir), 'labels', f"{img_path.stem}.txt")
        if os.path.exists(label_path):
            shutil.copy(label_path, os.path.join(val_label_dir, f"{img_path.stem}.txt"))
        else:
            print(f"Warning: No label file found for {img_path.name}")

    print(f"Dataset split complete. Files saved to {output_dir}")
    return output_dir

def create_dataset_yaml(output_dir, class_names):
    """
    Create a YAML configuration file for the dataset.

    Args:
        output_dir: Directory to save the YAML file
        class_names: List of class names

    Returns:
        Path to the created YAML file
    """
    yaml_path = os.path.join(output_dir, 'dataset.yaml')

    # Create dataset configuration
    dataset_config = {
        'path': output_dir,
        'train': 'images/train',
        'val': 'images/val',
        'names': {i: name for i, name in enumerate(class_names)},
        'nc': len(class_names)
    }

    # Write to YAML file
    with open(yaml_path, 'w') as f:
        yaml.dump(dataset_config, f, default_flow_style=False)

    print(f"Dataset configuration saved to {yaml_path}")
    return yaml_path

def train_model(
    model_path,
    data_yaml,
    epochs=100,
    batch_size=4,
    img_size=416,
    device='cpu',
    workers=1,
    patience=20,
    output_dir=None
):
    """
    Train a YOLO model on the chess pieces dataset.

    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or 'cuda')
        workers: Number of worker threads
        patience: Early stopping patience
        output_dir: Directory to save results
    """
    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join('chess_board_detection/piece_detection/models', f'run_{timestamp}')

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Print system info
    print(f"Python version: {platform.python_version()}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    print(f"Training on: {device}")

    # Load the model
    model = YOLO(model_path)

    # Train the model
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        imgsz=img_size,
        batch=batch_size,
        patience=patience,
        device=device,
        workers=workers,
        project=output_dir,
        name=f'chess_pieces_{timestamp}',
        exist_ok=True,
        pretrained=True,
        verbose=True,
        seed=42,
        cache=True,  # Cache images for faster training
        close_mosaic=10,  # Disable mosaic augmentation for final epochs
        amp=False,  # Disable mixed precision (more stable on CPU)
    )

    # Export the model to ONNX format
    model.export(format='onnx', dynamic=True, simplify=True)

    print(f"Training complete. Model saved to {output_dir}")
    return results

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description="Train YOLO model on chess pieces dataset")
    parser.add_argument("--model", type=str, default="yolo11n.pt", help="Path to pre-trained model")
    parser.add_argument("--input_dir", type=str, default="chess_board_detection/piece_detection/dataset/images",
                        help="Directory containing labeled images")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/piece_detection/dataset_split",
                        help="Directory to save split dataset")
    parser.add_argument("--train_ratio", type=float, default=0.8, help="Ratio of images for training (default: 0.8)")
    parser.add_argument("--epochs", type=int, default=100, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16 if torch.cuda.is_available() else 8,
                        help="Batch size (default: 16 for GPU, 8 for CPU)")
    parser.add_argument("--img-size", type=int, default=640, help="Image size for training (default: 640)")
    parser.add_argument("--device", type=str, default="0" if torch.cuda.is_available() else "cpu",
                        help="Device to train on ('cpu' or GPU device id)")
    parser.add_argument("--workers", type=int, default=4 if torch.cuda.is_available() else 8,
                        help="Number of worker threads (default: 4 for GPU, 8 for CPU)")
    parser.add_argument("--patience", type=int, default=20, help="Early stopping patience")
    parser.add_argument("--model_dir", type=str, default=None, help="Directory to save trained model")
    parser.add_argument("--use-gpu", action="store_true", help="Force GPU usage if available")

    args = parser.parse_args()

    # Check for GPU availability and user preference
    if args.use_gpu and not torch.cuda.is_available():
        print("Warning: GPU requested but not available. Falling back to CPU.")
        args.device = "cpu"
    elif args.use_gpu and torch.cuda.is_available():
        args.device = "0"  # Use first GPU
        print(f"Using GPU: {torch.cuda.get_device_name(0)}")

    # Adjust batch size based on device
    if args.device != "cpu" and args.batch < 16:
        print(f"Increasing batch size to 16 for GPU training (was {args.batch})")
        args.batch = 16

    args = parser.parse_args()

    # Chess piece class names
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]

    # Split dataset
    print(f"Splitting dataset from {args.input_dir} to {args.output_dir}")
    split_dataset(args.input_dir, args.output_dir, args.train_ratio)

    # Create dataset YAML
    data_yaml = create_dataset_yaml(args.output_dir, class_names)

    # Train model
    print(f"Training model {args.model} on dataset {data_yaml}")
    train_model(
        args.model,
        data_yaml,
        args.epochs,
        args.batch,
        args.img_size,
        args.device,
        args.workers,
        args.patience,
        args.model_dir
    )

if __name__ == "__main__":
    main()
