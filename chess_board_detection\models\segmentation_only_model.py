"""
Segmentation-only model for chess board detection.
This is a smaller, more efficient model that focuses solely on segmentation.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F

class DoubleConv(nn.Module):
    """Double convolution block for U-Net."""
    def __init__(self, in_channels, out_channels, mid_channels=None, use_batch_norm=True):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels

        if use_batch_norm:
            self.double_conv = nn.Sequential(
                nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(mid_channels),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
                nn.BatchNorm2d(out_channels),
                nn.ReLU(inplace=True)
            )
        else:
            self.double_conv = nn.Sequential(
                nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
                nn.ReLU(inplace=True)
            )

    def forward(self, x):
        return self.double_conv(x)

class Down(nn.Module):
    """Downscaling with maxpool then double conv."""
    def __init__(self, in_channels, out_channels, use_batch_norm=True):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels, use_batch_norm=use_batch_norm)
        )

    def forward(self, x):
        return self.maxpool_conv(x)

class Up(nn.Module):
    """Upscaling then double conv."""
    def __init__(self, in_channels, out_channels, bilinear=True, use_batch_norm=True):
        super().__init__()

        # if bilinear, use the normal convolutions to reduce the number of channels
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2, use_batch_norm=use_batch_norm)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels, use_batch_norm=use_batch_norm)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        # input is CHW
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]

        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        # if you have padding issues, see
        # https://github.com/HaiyongJiang/U-Net-Pytorch-Unstructured-Buggy/commit/0e854509c2cea854e247a9c615f175f76fbb2e3a
        # https://github.com/xiaopeng-liao/Pytorch-UNet/commit/8ebac70e633bac59fc22bb5195e513d5832fb3bd
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class OutConv(nn.Module):
    """Output convolution."""
    def __init__(self, in_channels, out_channels):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)

    def forward(self, x):
        return self.conv(x)

class SegmentationOnlyModel(nn.Module):
    """
    A smaller, more efficient model that focuses solely on segmentation.
    Based on U-Net architecture but with fewer parameters.
    """
    def __init__(self, n_channels=3, bilinear=True, use_batch_norm=True, dropout_rate=0.2):
        super(SegmentationOnlyModel, self).__init__()
        self.n_channels = n_channels
        self.bilinear = bilinear

        # Reduced number of filters compared to the full model
        factor = 2 if bilinear else 1

        # Encoder
        self.inc = DoubleConv(n_channels, 32, use_batch_norm=use_batch_norm)
        self.down1 = Down(32, 64, use_batch_norm=use_batch_norm)
        self.down2 = Down(64, 128, use_batch_norm=use_batch_norm)
        self.down3 = Down(128, 256, use_batch_norm=use_batch_norm)
        self.down4 = Down(256, 512 // factor, use_batch_norm=use_batch_norm)

        # Decoder
        self.up1 = Up(512, 256 // factor, bilinear, use_batch_norm=use_batch_norm)
        self.up2 = Up(256, 128 // factor, bilinear, use_batch_norm=use_batch_norm)
        self.up3 = Up(128, 64 // factor, bilinear, use_batch_norm=use_batch_norm)
        self.up4 = Up(64, 32, bilinear, use_batch_norm=use_batch_norm)

        # Output layer for segmentation
        self.outc = OutConv(32, 1)

        # Dropout for regularization
        self.dropout = nn.Dropout2d(dropout_rate)

    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x2 = self.dropout(x2)
        x3 = self.down2(x2)
        x3 = self.dropout(x3)
        x4 = self.down3(x3)
        x4 = self.dropout(x4)
        x5 = self.down4(x4)
        x5 = self.dropout(x5)

        # Decoder
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)

        # Output segmentation
        segmentation = self.outc(x)

        return {'segmentation': segmentation}

class TinySegmentationModel(nn.Module):
    """
    An even smaller model for segmentation, designed for efficiency.
    """
    def __init__(self, n_channels=3, use_batch_norm=True, dropout_rate=0.2):
        super(TinySegmentationModel, self).__init__()
        self.n_channels = n_channels

        # Simplified architecture with fewer parameters
        self.inc = DoubleConv(n_channels, 16, use_batch_norm=use_batch_norm)
        self.pool1 = nn.MaxPool2d(2)
        self.conv1 = DoubleConv(16, 32, use_batch_norm=use_batch_norm)
        self.pool2 = nn.MaxPool2d(2)
        self.conv2 = DoubleConv(32, 64, use_batch_norm=use_batch_norm)

        # Upsampling path
        self.up1 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.conv3 = DoubleConv(64, 32, use_batch_norm=use_batch_norm)
        self.up2 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.conv4 = DoubleConv(32, 16, use_batch_norm=use_batch_norm)

        # Output layer
        self.outc = OutConv(16, 1)

        # Dropout for regularization
        self.dropout = nn.Dropout2d(dropout_rate)

    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x = self.pool1(x1)
        x = self.dropout(x)

        x2 = self.conv1(x)
        x = self.pool2(x2)
        x = self.dropout(x)

        x = self.conv2(x)
        x = self.dropout(x)

        # Decoder
        x = self.up1(x)
        x = self.conv3(x)

        x = self.up2(x)
        x = self.conv4(x)

        # Output
        segmentation = self.outc(x)

        return {'segmentation': segmentation}

def count_parameters(model):
    """Count the number of trainable parameters in a model."""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

if __name__ == "__main__":
    # Create models
    full_model = SegmentationOnlyModel(n_channels=3)
    tiny_model = TinySegmentationModel(n_channels=3)

    # Count parameters
    full_params = count_parameters(full_model)
    tiny_params = count_parameters(tiny_model)

    print(f"SegmentationOnlyModel parameters: {full_params:,}")
    print(f"TinySegmentationModel parameters: {tiny_params:,}")

    # Test forward pass
    x = torch.randn(1, 3, 256, 256)
    full_output = full_model(x)
    tiny_output = tiny_model(x)

    print(f"SegmentationOnlyModel output shape: {full_output['segmentation'].shape}")
    print(f"TinySegmentationModel output shape: {tiny_output['segmentation'].shape}")
