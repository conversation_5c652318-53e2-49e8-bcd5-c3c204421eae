"""
Chess Piece Labeling Tool

This tool allows manual labeling of chess pieces in images for training a YOLO model.
Features:
- Load images from a directory
- Draw bounding boxes around chess pieces
- Assign piece types to each bounding box
- Save annotations in YOLO format
- Load existing annotations for review/editing
"""

import os
import sys
import cv2
import numpy as np
import json
import argparse
from pathlib import Path
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from PIL import Image, ImageTk
import math

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Chess piece classes
PIECE_CLASSES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

# Class to numeric ID mapping
CLASS_TO_ID = {class_name: i for i, class_name in enumerate(PIECE_CLASSES)}

class ChessPieceLabelTool:
    def __init__(self, root, image_dir, output_dir, corner_annotations_path=None):
        self.root = root
        self.root.title("Chess Piece Labeling Tool")
        self.root.geometry("1200x800")

        self.image_dir = image_dir
        self.output_dir = output_dir

        # Create output directories
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, "labels"), exist_ok=True)
        os.makedirs(os.path.join(output_dir, "images"), exist_ok=True)

        # Load image list
        self.image_files = [f for f in os.listdir(image_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        if not self.image_files:
            messagebox.showerror("Error", f"No images found in {image_dir}")
            self.root.quit()
            return

        # Load corner annotations if provided
        self.corner_annotations = {}
        if corner_annotations_path and os.path.exists(corner_annotations_path):
            try:
                with open(corner_annotations_path, 'r') as f:
                    annotations = json.load(f)
                    for ann in annotations:
                        self.corner_annotations[ann['image']] = {
                            'corners': ann['corners'],
                            'image_size': ann['image_size']
                        }
                print(f"Loaded {len(self.corner_annotations)} corner annotations")
            except Exception as e:
                print(f"Error loading corner annotations: {e}")

        self.current_image_index = 0
        self.current_image = None
        self.current_image_display = None
        self.current_annotations = []

        # Drawing and editing variables
        self.drawing = False
        self.moving = False
        self.resizing = False
        self.resize_edge = None  # Can be 'tl', 'tr', 'bl', 'br', 'top', 'right', 'bottom', 'left'
        self.selected_annotation_index = None
        self.start_x, self.start_y = 0, 0
        self.rect_id = None
        self.scale_factor = 1.0
        self.move_start_x, self.move_start_y = 0, 0

        # Zoom variables
        self.zoom_level = 1.0
        self.zoom_factor = 1.2  # How much to zoom in/out per step
        self.pan_start_x = 0
        self.pan_start_y = 0
        self.is_panning = False
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0

        # Chess board variables
        self.quick_label_mode = False
        self.square_size = (0, 0)  # Will be calculated when image is loaded
        self.board_corners = None  # Will be set if corner annotations are available
        self.perspective_grid = None  # Will be calculated from board corners
        self.shrink_boxes = True  # Whether to make boxes 5% smaller than grid squares
        self.shrink_factor = 0.05  # How much to shrink boxes (5%)

        # Create UI
        self.create_ui()

        # Load first image
        self.load_image(0)

    def create_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Left panel (image display)
        self.left_panel = ttk.Frame(main_frame)
        self.left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Canvas for image display with scrollbars
        canvas_frame = ttk.Frame(self.left_panel)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # Add scrollbars
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Canvas for image display
        self.canvas = tk.Canvas(canvas_frame, bg="gray",
                               xscrollcommand=h_scrollbar.set,
                               yscrollcommand=v_scrollbar.set)
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # Configure scrollbars
        h_scrollbar.config(command=self.canvas.xview)
        v_scrollbar.config(command=self.canvas.yview)

        # Canvas events
        self.canvas.bind("<ButtonPress-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_move)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
        self.canvas.bind("<ButtonPress-2>", self.on_pan_start)  # Middle button for panning
        self.canvas.bind("<B2-Motion>", self.on_pan_move)
        self.canvas.bind("<ButtonRelease-2>", self.on_pan_end)
        self.canvas.bind("<MouseWheel>", self.on_mouse_wheel)  # Mouse wheel for zooming
        self.canvas.bind("<Motion>", self.on_mouse_motion)  # Mouse motion for cursor changes

        # Right panel (controls)
        right_panel = ttk.Frame(main_frame, width=300)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=10)

        # Image navigation
        nav_frame = ttk.LabelFrame(right_panel, text="Navigation")
        nav_frame.pack(fill=tk.X, pady=5)

        ttk.Button(nav_frame, text="Previous", command=self.prev_image).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(nav_frame, text="Next", command=self.next_image).pack(side=tk.LEFT, padx=5, pady=5)

        self.image_label = ttk.Label(nav_frame, text="Image: 0/0")
        self.image_label.pack(side=tk.LEFT, padx=5, pady=5)

        # Zoom controls
        zoom_frame = ttk.LabelFrame(right_panel, text="Zoom")
        zoom_frame.pack(fill=tk.X, pady=5)

        ttk.Button(zoom_frame, text="Zoom In (+)", command=self.zoom_in).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(zoom_frame, text="Zoom Out (-)", command=self.zoom_out).pack(side=tk.LEFT, padx=5, pady=5)
        ttk.Button(zoom_frame, text="Reset Zoom", command=self.reset_zoom).pack(side=tk.LEFT, padx=5, pady=5)

        self.zoom_label = ttk.Label(zoom_frame, text="Zoom: 100%")
        self.zoom_label.pack(side=tk.LEFT, padx=5, pady=5)

        # Tool selection
        tool_frame = ttk.LabelFrame(right_panel, text="Tools")
        tool_frame.pack(fill=tk.X, pady=5)

        self.tool_var = tk.StringVar(value="draw")
        ttk.Radiobutton(
            tool_frame,
            text="Draw Box",
            value="draw",
            variable=self.tool_var
        ).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Radiobutton(
            tool_frame,
            text="Adjust Box",
            value="move",
            variable=self.tool_var
        ).pack(side=tk.LEFT, padx=5, pady=5)

        # Adjustment sensitivity
        sensitivity_frame = ttk.Frame(tool_frame)
        sensitivity_frame.pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Label(sensitivity_frame, text="Adjustment Sensitivity:").pack(side=tk.LEFT)

        self.sensitivity_var = tk.DoubleVar(value=1.0)
        sensitivity_scale = ttk.Scale(
            sensitivity_frame,
            from_=0.1,
            to=2.0,
            orient=tk.HORIZONTAL,
            variable=self.sensitivity_var,
            length=100
        )
        sensitivity_scale.pack(side=tk.LEFT, padx=5)

        # Quick Label mode
        quick_label_frame = ttk.LabelFrame(right_panel, text="Labeling Mode")
        quick_label_frame.pack(fill=tk.X, pady=5)

        self.quick_label_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(
            quick_label_frame,
            text="Quick Label Mode (Click on Square)",
            variable=self.quick_label_var,
            command=self.toggle_quick_label_mode
        ).pack(side=tk.LEFT, padx=5, pady=5)

        # Box shrinking controls
        shrink_frame = ttk.Frame(quick_label_frame)
        shrink_frame.pack(side=tk.LEFT, padx=5, pady=5)

        self.shrink_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(
            shrink_frame,
            text="Make boxes smaller than squares by",
            variable=self.shrink_var,
            command=self.toggle_shrink_boxes
        ).pack(side=tk.LEFT)

        # Percentage input
        self.shrink_percent_var = tk.StringVar(value="5")
        percent_entry = ttk.Entry(
            shrink_frame,
            textvariable=self.shrink_percent_var,
            width=3
        )
        percent_entry.pack(side=tk.LEFT, padx=2)
        percent_entry.bind("<FocusOut>", self.update_shrink_factor)
        percent_entry.bind("<Return>", self.update_shrink_factor)

        ttk.Label(shrink_frame, text="%").pack(side=tk.LEFT)

        # Chess board controls
        chess_board_frame = ttk.LabelFrame(right_panel, text="Chess Board")
        chess_board_frame.pack(fill=tk.X, pady=5)

        ttk.Button(
            chess_board_frame,
            text="Use Corner Annotations",
            command=self.use_corner_annotations
        ).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(
            chess_board_frame,
            text="Clear Board Grid",
            command=self.clear_board_grid
        ).pack(side=tk.LEFT, padx=5, pady=5)

        # Square size controls (for manual grid)
        self.square_size_frame = ttk.LabelFrame(right_panel, text="Manual Grid Size")
        self.square_size_frame.pack(fill=tk.X, pady=5)

        ttk.Label(self.square_size_frame, text="Rows:").pack(side=tk.LEFT, padx=5, pady=5)
        self.rows_var = tk.StringVar(value="8")
        ttk.Spinbox(self.square_size_frame, from_=1, to=20, width=3, textvariable=self.rows_var).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Label(self.square_size_frame, text="Columns:").pack(side=tk.LEFT, padx=5, pady=5)
        self.cols_var = tk.StringVar(value="8")
        ttk.Spinbox(self.square_size_frame, from_=1, to=20, width=3, textvariable=self.cols_var).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(self.square_size_frame, text="Apply Manual Grid", command=self.calculate_square_size).pack(side=tk.LEFT, padx=5, pady=5)

        # Class selection
        class_frame = ttk.LabelFrame(right_panel, text="Piece Class")
        class_frame.pack(fill=tk.X, pady=5)

        self.class_var = tk.StringVar()
        self.class_var.set(PIECE_CLASSES[0])

        for i, class_name in enumerate(PIECE_CLASSES):
            if i % 2 == 0:
                row_frame = ttk.Frame(class_frame)
                row_frame.pack(fill=tk.X)

            ttk.Radiobutton(
                row_frame,
                text=class_name.replace('_', ' ').title(),
                value=class_name,
                variable=self.class_var
            ).pack(side=tk.LEFT, padx=5, pady=2)

        # Annotations list
        annotations_frame = ttk.LabelFrame(right_panel, text="Annotations")
        annotations_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        self.annotations_listbox = tk.Listbox(annotations_frame)
        self.annotations_listbox.pack(fill=tk.BOTH, expand=True)
        self.annotations_listbox.bind("<<ListboxSelect>>", self.on_annotation_select)

        # Buttons
        buttons_frame = ttk.Frame(right_panel)
        buttons_frame.pack(fill=tk.X, pady=5)

        ttk.Button(buttons_frame, text="Delete Selected", command=self.delete_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Save", command=self.save_annotations).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="Save & Next", command=self.save_and_next).pack(side=tk.LEFT, padx=5)

    def load_image(self, index):
        if 0 <= index < len(self.image_files):
            self.current_image_index = index
            image_file = self.image_files[index]
            image_path = os.path.join(self.image_dir, image_file)

            # Load image
            self.current_image = cv2.imread(image_path)
            self.current_image = cv2.cvtColor(self.current_image, cv2.COLOR_BGR2RGB)

            # Reset board corners and grid
            self.board_corners = None
            self.perspective_grid = None

            # Check if we have corner annotations for this image
            if image_file in self.corner_annotations:
                # Get corner annotations
                corners = self.corner_annotations[image_file]['corners']
                image_size = self.corner_annotations[image_file]['image_size']

                # Check if image size matches
                if self.current_image.shape[1] == image_size[0] and self.current_image.shape[0] == image_size[1]:
                    # Convert corners to the format we need
                    self.board_corners = [
                        (corners[0], corners[1]),  # Top-left
                        (corners[2], corners[3]),  # Top-right
                        (corners[4], corners[5]),  # Bottom-right
                        (corners[6], corners[7])   # Bottom-left
                    ]
                    print(f"Loaded corner annotations for {image_file}")
                else:
                    print(f"Image size mismatch for {image_file}: {self.current_image.shape[:2]} vs {image_size}")

            # Scale image to fit canvas
            self.scale_image_to_fit()

            # Update image label
            self.image_label.config(text=f"Image: {index+1}/{len(self.image_files)}")

            # Load annotations if they exist
            self.load_annotations()

            # Update display
            self.update_display()

    def scale_image_to_fit(self):
        if self.current_image is None:
            return

        # Get canvas size
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:
            # Canvas not yet realized, use default size
            canvas_width = 800
            canvas_height = 600

        # Get image size
        img_height, img_width = self.current_image.shape[:2]

        # Calculate scale factor
        width_scale = canvas_width / img_width
        height_scale = canvas_height / img_height
        self.scale_factor = min(width_scale, height_scale)

        # Resize image
        new_width = int(img_width * self.scale_factor)
        new_height = int(img_height * self.scale_factor)

        # Use PIL for better quality resizing
        pil_img = Image.fromarray(self.current_image)
        pil_img = pil_img.resize((new_width, new_height), Image.LANCZOS)
        self.current_image_display = ImageTk.PhotoImage(pil_img)

    def update_display(self, preserve_zoom=False):
        # Store current zoom level if preserving zoom
        old_zoom_level = self.zoom_level if preserve_zoom else 1.0
        old_offset_x = self.canvas_offset_x if preserve_zoom else 0
        old_offset_y = self.canvas_offset_y if preserve_zoom else 0

        # Reset zoom if not preserving
        if not preserve_zoom:
            self.zoom_level = 1.0
            self.canvas_offset_x = 0
            self.canvas_offset_y = 0

        # Clear canvas
        self.canvas.delete("all")

        if self.current_image_display:
            # Get image size
            img_height, img_width = self.current_image.shape[:2]

            # If not preserving zoom, recreate the image display
            if not preserve_zoom:
                # Calculate new size
                new_width = int(img_width * self.scale_factor)
                new_height = int(img_height * self.scale_factor)

                # Configure canvas scrollregion
                self.canvas.config(scrollregion=(0, 0, new_width, new_height))

                # Display image
                self.canvas.create_image(0, 0, anchor=tk.NW, image=self.current_image_display)
            else:
                # Calculate new size with current zoom
                new_width = int(img_width * self.scale_factor * old_zoom_level)
                new_height = int(img_height * self.scale_factor * old_zoom_level)

                # Configure canvas scrollregion
                self.canvas.config(scrollregion=(0, 0, new_width, new_height))

                # Display image
                self.canvas.create_image(old_offset_x, old_offset_y, anchor=tk.NW, image=self.current_image_display)

            # Redraw annotations
            self.redraw_annotations()

            # Redraw grid if in quick label mode
            if self.quick_label_mode:
                self.show_grid_overlay()

        # Update annotations listbox
        self.update_annotations_listbox()

        # Update zoom label
        self.zoom_label.config(text=f"Zoom: {int(self.zoom_level * 100)}%")

    def use_corner_annotations(self):
        """Use corner annotations to create a perspective-corrected chess board grid."""
        if self.current_image is None:
            messagebox.showerror("Error", "No image loaded")
            return

        if self.board_corners is None:
            messagebox.showerror("Error", "No corner annotations available for this image")
            return

        # Enable quick label mode
        self.quick_label_var.set(True)
        self.quick_label_mode = True

        # Create perspective grid
        self.create_perspective_grid()

        # Show grid overlay
        self.show_grid_overlay()

        messagebox.showinfo("Success", "Using corner annotations for chess board grid")

    def clear_board_grid(self):
        """Clear the chess board grid."""
        self.canvas.delete("grid")
        self.perspective_grid = None
        self.quick_label_mode = False
        self.quick_label_var.set(False)

    def create_perspective_grid(self):
        """Create a perspective-corrected grid based on board corners."""
        if self.board_corners is None or self.current_image is None:
            return

        # Define the number of rows and columns
        rows = 8
        cols = 8

        # Create a grid of points in the perspective-corrected space
        self.perspective_grid = []

        # Get corners in the right order (top-left, top-right, bottom-right, bottom-left)
        corners = np.array(self.board_corners, dtype=np.float32)

        # Define the destination points for a perfect square grid
        # We'll use a normalized 8x8 grid (0-8 x 0-8)
        dst_size = 8
        dst_points = np.array([
            [0, 0],
            [dst_size, 0],
            [dst_size, dst_size],
            [0, dst_size]
        ], dtype=np.float32)

        # Calculate the perspective transform matrix
        M = cv2.getPerspectiveTransform(corners, dst_points)
        M_inv = cv2.getPerspectiveTransform(dst_points, corners)

        # Create grid points in the normalized space
        for row in range(rows + 1):
            grid_row = []
            for col in range(cols + 1):
                # Normalized coordinates (0-8 x 0-8)
                norm_x = col
                norm_y = row

                # Apply inverse perspective transform to get original image coordinates
                pt = np.array([[norm_x, norm_y, 1]], dtype=np.float32).reshape(1, 3)
                transformed_pt = np.matmul(M_inv, pt.T).T

                # Convert to homogeneous coordinates
                x = transformed_pt[0, 0] / transformed_pt[0, 2]
                y = transformed_pt[0, 1] / transformed_pt[0, 2]

                grid_row.append((x, y))
            self.perspective_grid.append(grid_row)

    def toggle_quick_label_mode(self):
        """Toggle between quick label mode and normal mode."""
        self.quick_label_mode = self.quick_label_var.get()
        if self.quick_label_mode:
            if self.board_corners is not None:
                self.create_perspective_grid()
            else:
                self.calculate_square_size()
            self.show_grid_overlay()
        else:
            self.canvas.delete("grid")

    def calculate_square_size(self):
        """Calculate the size of each square for quick label mode."""
        if self.current_image is None:
            return

        try:
            rows = int(self.rows_var.get())
            cols = int(self.cols_var.get())

            if rows <= 0 or cols <= 0:
                messagebox.showerror("Error", "Rows and columns must be positive integers")
                return

            # Calculate square size based on image dimensions
            img_height, img_width = self.current_image.shape[:2]
            square_width = img_width / cols
            square_height = img_height / rows

            self.square_size = (square_width, square_height)

            # Reset perspective grid
            self.perspective_grid = None

            # Show grid overlay
            self.show_grid_overlay()

            messagebox.showinfo("Success", f"Square size set to {square_width:.1f}x{square_height:.1f} pixels")
        except ValueError:
            messagebox.showerror("Error", "Invalid row or column value")

    def show_grid_overlay(self):
        """Show a grid overlay on the image."""
        # Remove existing grid
        self.canvas.delete("grid")

        if not self.quick_label_mode or self.current_image is None:
            return

        if self.perspective_grid is not None:
            # Draw perspective-corrected grid
            self.draw_perspective_grid()
        else:
            # Draw regular grid
            self.draw_regular_grid()

    def draw_regular_grid(self):
        """Draw a regular grid overlay."""
        # Get image dimensions
        img_height, img_width = self.current_image.shape[:2]

        # Get number of rows and columns
        rows = int(self.rows_var.get())
        cols = int(self.cols_var.get())

        # Calculate scaled dimensions
        scaled_width = img_width * self.scale_factor * self.zoom_level
        scaled_height = img_height * self.scale_factor * self.zoom_level

        # Draw grid
        for i in range(rows + 1):
            y = i * (scaled_height / rows) + self.canvas_offset_y
            self.canvas.create_line(
                self.canvas_offset_x, y,
                self.canvas_offset_x + scaled_width, y,
                fill="cyan", width=1, tags="grid"
            )

        for i in range(cols + 1):
            x = i * (scaled_width / cols) + self.canvas_offset_x
            self.canvas.create_line(
                x, self.canvas_offset_y,
                x, self.canvas_offset_y + scaled_height,
                fill="cyan", width=1, tags="grid"
            )

    def draw_perspective_grid(self):
        """Draw a perspective-corrected grid overlay."""
        if self.perspective_grid is None:
            return

        # Draw horizontal lines
        for row in range(len(self.perspective_grid)):
            points = []
            for col in range(len(self.perspective_grid[0])):
                x, y = self.perspective_grid[row][col]
                # Scale to canvas coordinates
                canvas_x = x * self.scale_factor * self.zoom_level + self.canvas_offset_x
                canvas_y = y * self.scale_factor * self.zoom_level + self.canvas_offset_y
                points.append(canvas_x)
                points.append(canvas_y)

            self.canvas.create_line(points, fill="cyan", width=1, tags="grid")

        # Draw vertical lines
        for col in range(len(self.perspective_grid[0])):
            points = []
            for row in range(len(self.perspective_grid)):
                x, y = self.perspective_grid[row][col]
                # Scale to canvas coordinates
                canvas_x = x * self.scale_factor * self.zoom_level + self.canvas_offset_x
                canvas_y = y * self.scale_factor * self.zoom_level + self.canvas_offset_y
                points.append(canvas_x)
                points.append(canvas_y)

            self.canvas.create_line(points, fill="cyan", width=1, tags="grid")

    def on_mouse_down(self, event):
        # Get current tool
        current_tool = self.tool_var.get()

        if self.quick_label_mode:
            # In quick label mode, we'll create a box on mouse click
            self.handle_quick_label_click(event)
        elif current_tool == "move":
            # Move tool - check if we clicked on an annotation
            self.try_select_annotation(event)
        else:
            # Draw tool - normal drawing mode
            self.drawing = True
            self.start_x, self.start_y = event.x, event.y

            # Create initial rectangle
            self.rect_id = self.canvas.create_rectangle(
                self.start_x, self.start_y, self.start_x, self.start_y,
                outline="yellow", width=2
            )

    def on_mouse_motion(self, event):
        """Handle mouse motion events."""
        # Only change cursor in move mode
        if self.tool_var.get() == "move" and not self.moving and not self.resizing:
            # Check if mouse is over a resize handle
            resize_edge = self.get_resize_edge(event)
            if resize_edge:
                # Set appropriate cursor based on which edge/corner
                if resize_edge in ['tl', 'br']:
                    self.canvas.config(cursor="size_nw_se")
                elif resize_edge in ['tr', 'bl']:
                    self.canvas.config(cursor="size_ne_sw")
                elif resize_edge in ['top', 'bottom']:
                    self.canvas.config(cursor="size_ns")
                elif resize_edge in ['left', 'right']:
                    self.canvas.config(cursor="size_we")
            # Check if mouse is over any annotation (for moving)
            elif self.is_over_annotation(event):
                self.canvas.config(cursor="fleur")  # Move cursor
            else:
                self.canvas.config(cursor="")  # Default cursor

    def get_resize_edge(self, event):
        """Check if the mouse is over a resize handle of any annotation."""
        # Only in move mode
        if self.tool_var.get() != "move":
            return None

        # Get canvas coordinates accounting for scrolling
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Handle size (in canvas coordinates)
        handle_size = 6
        edge_tolerance = 8  # Slightly larger than handle for easier targeting

        # Check each annotation
        for i, ann in enumerate(self.current_annotations):
            x1, y1, x2, y2 = ann['bbox']

            # Convert to canvas coordinates
            scaled_x1 = int(x1 * self.scale_factor * self.zoom_level)
            scaled_y1 = int(y1 * self.scale_factor * self.zoom_level)
            scaled_x2 = int(x2 * self.scale_factor * self.zoom_level)
            scaled_y2 = int(y2 * self.scale_factor * self.zoom_level)

            # Check corners first (they take precedence)
            # Top-left corner
            if abs(canvas_x - scaled_x1) <= edge_tolerance and abs(canvas_y - scaled_y1) <= edge_tolerance:
                return 'tl'
            # Top-right corner
            if abs(canvas_x - scaled_x2) <= edge_tolerance and abs(canvas_y - scaled_y1) <= edge_tolerance:
                return 'tr'
            # Bottom-left corner
            if abs(canvas_x - scaled_x1) <= edge_tolerance and abs(canvas_y - scaled_y2) <= edge_tolerance:
                return 'bl'
            # Bottom-right corner
            if abs(canvas_x - scaled_x2) <= edge_tolerance and abs(canvas_y - scaled_y2) <= edge_tolerance:
                return 'br'

            # Then check edges
            # Top edge
            if abs(canvas_y - scaled_y1) <= edge_tolerance and scaled_x1 <= canvas_x <= scaled_x2:
                return 'top'
            # Bottom edge
            if abs(canvas_y - scaled_y2) <= edge_tolerance and scaled_x1 <= canvas_x <= scaled_x2:
                return 'bottom'
            # Left edge
            if abs(canvas_x - scaled_x1) <= edge_tolerance and scaled_y1 <= canvas_y <= scaled_y2:
                return 'left'
            # Right edge
            if abs(canvas_x - scaled_x2) <= edge_tolerance and scaled_y1 <= canvas_y <= scaled_y2:
                return 'right'

        return None

    def is_over_annotation(self, event):
        """Check if the mouse is over any annotation."""
        # Get canvas coordinates accounting for scrolling
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Convert to original image coordinates
        orig_x = canvas_x / (self.scale_factor * self.zoom_level)
        orig_y = canvas_y / (self.scale_factor * self.zoom_level)

        # Check if mouse is inside any annotation
        for ann in self.current_annotations:
            x1, y1, x2, y2 = ann['bbox']

            if x1 <= orig_x <= x2 and y1 <= orig_y <= y2:
                return True

        return False

    def try_select_annotation(self, event):
        """Try to select an annotation for moving or resizing."""
        # Get canvas coordinates accounting for scrolling
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Convert to original image coordinates
        orig_x = canvas_x / (self.scale_factor * self.zoom_level)
        orig_y = canvas_y / (self.scale_factor * self.zoom_level)

        # First check if we're clicking on a resize handle
        resize_edge = self.get_resize_edge(event)
        if resize_edge:
            # Find which annotation this handle belongs to
            for i, ann in enumerate(self.current_annotations):
                x1, y1, x2, y2 = ann['bbox']

                # Convert to canvas coordinates
                scaled_x1 = int(x1 * self.scale_factor * self.zoom_level)
                scaled_y1 = int(y1 * self.scale_factor * self.zoom_level)
                scaled_x2 = int(x2 * self.scale_factor * self.zoom_level)
                scaled_y2 = int(y2 * self.scale_factor * self.zoom_level)

                # Check if this is the annotation we're resizing
                if ((resize_edge == 'tl' and abs(canvas_x - scaled_x1) <= 8 and abs(canvas_y - scaled_y1) <= 8) or
                    (resize_edge == 'tr' and abs(canvas_x - scaled_x2) <= 8 and abs(canvas_y - scaled_y1) <= 8) or
                    (resize_edge == 'bl' and abs(canvas_x - scaled_x1) <= 8 and abs(canvas_y - scaled_y2) <= 8) or
                    (resize_edge == 'br' and abs(canvas_x - scaled_x2) <= 8 and abs(canvas_y - scaled_y2) <= 8) or
                    (resize_edge == 'top' and abs(canvas_y - scaled_y1) <= 8 and scaled_x1 <= canvas_x <= scaled_x2) or
                    (resize_edge == 'bottom' and abs(canvas_y - scaled_y2) <= 8 and scaled_x1 <= canvas_x <= scaled_x2) or
                    (resize_edge == 'left' and abs(canvas_x - scaled_x1) <= 8 and scaled_y1 <= canvas_y <= scaled_y2) or
                    (resize_edge == 'right' and abs(canvas_x - scaled_x2) <= 8 and scaled_y1 <= canvas_y <= scaled_y2)):

                    # Start resizing
                    self.resizing = True
                    self.resize_edge = resize_edge
                    self.selected_annotation_index = i
                    self.move_start_x = orig_x
                    self.move_start_y = orig_y

                    # Highlight the selected annotation
                    self.canvas.delete("highlight")

                    # Scale coordinates
                    scaled_x1 = int(x1 * self.scale_factor * self.zoom_level)
                    scaled_y1 = int(y1 * self.scale_factor * self.zoom_level)
                    scaled_x2 = int(x2 * self.scale_factor * self.zoom_level)
                    scaled_y2 = int(y2 * self.scale_factor * self.zoom_level)

                    return

        # If not resizing, check if we're moving
        # Check if we clicked inside any annotation
        for i, ann in enumerate(self.current_annotations):
            x1, y1, x2, y2 = ann['bbox']

            if x1 <= orig_x <= x2 and y1 <= orig_y <= y2:
                # Found an annotation to move
                self.moving = True
                self.selected_annotation_index = i
                self.move_start_x = orig_x
                self.move_start_y = orig_y

                # Change cursor to indicate moving
                self.canvas.config(cursor="fleur")

                # Highlight the selected annotation
                self.canvas.delete("highlight")

                # Scale coordinates
                scaled_x1 = int(x1 * self.scale_factor * self.zoom_level)
                scaled_y1 = int(y1 * self.scale_factor * self.zoom_level)
                scaled_x2 = int(x2 * self.scale_factor * self.zoom_level)
                scaled_y2 = int(y2 * self.scale_factor * self.zoom_level)

                # Draw highlight with handles
                self.canvas.create_rectangle(
                    scaled_x1, scaled_y1, scaled_x2, scaled_y2,
                    outline="yellow", width=3,
                    tags="highlight"
                )

                # Add adjustment handles
                handle_size = 6
                # Top-left handle
                self.canvas.create_rectangle(
                    scaled_x1 - handle_size, scaled_y1 - handle_size,
                    scaled_x1 + handle_size, scaled_y1 + handle_size,
                    fill="yellow", outline="black", tags="highlight"
                )
                # Top-right handle
                self.canvas.create_rectangle(
                    scaled_x2 - handle_size, scaled_y1 - handle_size,
                    scaled_x2 + handle_size, scaled_y1 + handle_size,
                    fill="yellow", outline="black", tags="highlight"
                )
                # Bottom-left handle
                self.canvas.create_rectangle(
                    scaled_x1 - handle_size, scaled_y2 - handle_size,
                    scaled_x1 + handle_size, scaled_y2 + handle_size,
                    fill="yellow", outline="black", tags="highlight"
                )
                # Bottom-right handle
                self.canvas.create_rectangle(
                    scaled_x2 - handle_size, scaled_y2 - handle_size,
                    scaled_x2 + handle_size, scaled_y2 + handle_size,
                    fill="yellow", outline="black", tags="highlight"
                )

                # Select in listbox
                self.annotations_listbox.selection_clear(0, tk.END)
                self.annotations_listbox.selection_set(i)
                self.annotations_listbox.see(i)

                return

    def handle_quick_label_click(self, event):
        """Handle click in quick label mode."""
        if self.current_image is None:
            return

        # Get canvas coordinates accounting for scrolling
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Convert to original image coordinates
        orig_x = int(canvas_x / (self.scale_factor * self.zoom_level))
        orig_y = int(canvas_y / (self.scale_factor * self.zoom_level))

        # Get image dimensions
        img_height, img_width = self.current_image.shape[:2]

        # Ensure coordinates are within image bounds
        if orig_x < 0 or orig_x >= img_width or orig_y < 0 or orig_y >= img_height:
            return

        if self.perspective_grid is not None:
            # Find which square was clicked in the perspective grid
            self.handle_perspective_grid_click(orig_x, orig_y)
        else:
            # Use regular grid
            self.handle_regular_grid_click(orig_x, orig_y)

    def toggle_shrink_boxes(self):
        """Toggle whether to make boxes smaller than grid squares."""
        self.shrink_boxes = self.shrink_var.get()
        if self.shrink_boxes:
            self.update_shrink_factor()

    def update_shrink_factor(self, event=None):
        """Update the shrink factor based on the percentage input."""
        try:
            percent = float(self.shrink_percent_var.get())
            if percent < 0:
                percent = 0
            elif percent > 50:
                percent = 50
                self.shrink_percent_var.set("50")

            self.shrink_factor = percent / 100.0

            # Show confirmation if called from event
            if event:
                messagebox.showinfo("Shrink Factor Updated", f"Boxes will be {percent}% smaller than grid squares.")
        except ValueError:
            # Reset to default if invalid input
            self.shrink_percent_var.set("5")
            self.shrink_factor = 0.05
            if event:
                messagebox.showerror("Invalid Input", "Please enter a valid percentage (0-50).")

        # Update any existing annotations if in quick label mode
        if self.quick_label_mode and self.shrink_boxes:
            self.redraw_annotations()

    def handle_perspective_grid_click(self, orig_x, orig_y):
        """Handle click in perspective grid mode."""
        # Find which square was clicked
        rows = len(self.perspective_grid) - 1
        cols = len(self.perspective_grid[0]) - 1

        # Find the square that contains the click point
        for row in range(rows):
            for col in range(cols):
                # Get the four corners of this square
                tl = self.perspective_grid[row][col]
                tr = self.perspective_grid[row][col+1]
                br = self.perspective_grid[row+1][col+1]
                bl = self.perspective_grid[row+1][col]

                # Check if point is inside this quadrilateral
                if self.point_in_quadrilateral(orig_x, orig_y, tl, tr, br, bl):
                    # Create a bounding box for this square
                    x_coords = [tl[0], tr[0], br[0], bl[0]]
                    y_coords = [tl[1], tr[1], br[1], bl[1]]

                    x1 = min(x_coords)
                    y1 = min(y_coords)
                    x2 = max(x_coords)
                    y2 = max(y_coords)

                    # Shrink the box if enabled
                    if self.shrink_boxes:
                        width = x2 - x1
                        height = y2 - y1
                        shrink_x = width * self.shrink_factor
                        shrink_y = height * self.shrink_factor

                        x1 += shrink_x
                        y1 += shrink_y
                        x2 -= shrink_x
                        y2 -= shrink_y

                    # Add annotation
                    class_name = self.class_var.get()
                    self.current_annotations.append({
                        'class': class_name,
                        'bbox': (int(x1), int(y1), int(x2), int(y2))
                    })

                    # Redraw annotations
                    self.redraw_annotations()

                    # Update annotations listbox
                    self.update_annotations_listbox()
                    return

    def point_in_quadrilateral(self, x, y, tl, tr, br, bl):
        """Check if a point is inside a quadrilateral."""
        # Convert to numpy arrays
        point = np.array([x, y])
        vertices = np.array([tl, tr, br, bl])

        # Check if point is inside using the ray casting algorithm
        return self.point_in_polygon(point, vertices)

    def point_in_polygon(self, point, vertices):
        """Check if a point is inside a polygon using ray casting algorithm."""
        x, y = point
        n = len(vertices)
        inside = False

        p1x, p1y = vertices[0]
        for i in range(1, n + 1):
            p2x, p2y = vertices[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y

        return inside

    def handle_regular_grid_click(self, orig_x, orig_y):
        """Handle click in regular grid mode."""
        if self.square_size[0] <= 0 or self.square_size[1] <= 0:
            return

        # Get image dimensions
        img_height, img_width = self.current_image.shape[:2]

        # Calculate which square was clicked
        square_width, square_height = self.square_size
        col = int(orig_x / square_width)
        row = int(orig_y / square_height)

        # Calculate square boundaries
        x1 = col * square_width
        y1 = row * square_height
        x2 = (col + 1) * square_width
        y2 = (row + 1) * square_height

        # Shrink the box if enabled
        if self.shrink_boxes:
            width = x2 - x1
            height = y2 - y1
            shrink_x = width * self.shrink_factor
            shrink_y = height * self.shrink_factor

            x1 += shrink_x
            y1 += shrink_y
            x2 -= shrink_x
            y2 -= shrink_y

        # Ensure coordinates are within image bounds
        x1 = max(0, min(x1, img_width - 1))
        y1 = max(0, min(y1, img_height - 1))
        x2 = max(0, min(x2, img_width - 1))
        y2 = max(0, min(y2, img_height - 1))

        # Add annotation
        class_name = self.class_var.get()
        self.current_annotations.append({
            'class': class_name,
            'bbox': (int(x1), int(y1), int(x2), int(y2))
        })

        # Redraw annotations
        self.redraw_annotations()

        # Update annotations listbox
        self.update_annotations_listbox()

    def update_annotations_listbox(self):
        """Update the annotations listbox."""
        self.annotations_listbox.delete(0, tk.END)
        for i, ann in enumerate(self.current_annotations):
            class_name = ann['class']
            piece_type = class_name.split('_')[1]
            color = class_name.split('_')[0]
            self.annotations_listbox.insert(tk.END, f"{i+1}: {color} {piece_type}")

    def on_mouse_move(self, event):
        if self.drawing:
            # Update rectangle during drawing
            self.canvas.coords(self.rect_id, self.start_x, self.start_y, event.x, event.y)
        elif self.resizing and self.selected_annotation_index is not None:
            # Resize the selected annotation
            # Get canvas coordinates accounting for scrolling
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)

            # Convert to original image coordinates
            orig_x = canvas_x / (self.scale_factor * self.zoom_level)
            orig_y = canvas_y / (self.scale_factor * self.zoom_level)

            # Calculate the movement delta in original image coordinates
            dx = (orig_x - self.move_start_x) * self.sensitivity_var.get()
            dy = (orig_y - self.move_start_y) * self.sensitivity_var.get()

            # Update the start position for the next move
            self.move_start_x = orig_x
            self.move_start_y = orig_y

            # Get the current annotation
            ann = self.current_annotations[self.selected_annotation_index]
            x1, y1, x2, y2 = ann['bbox']

            # Apply the resize based on which edge/corner is being dragged
            if self.resize_edge == 'tl':  # Top-left corner
                x1 += dx
                y1 += dy
            elif self.resize_edge == 'tr':  # Top-right corner
                x2 += dx
                y1 += dy
            elif self.resize_edge == 'bl':  # Bottom-left corner
                x1 += dx
                y2 += dy
            elif self.resize_edge == 'br':  # Bottom-right corner
                x2 += dx
                y2 += dy
            elif self.resize_edge == 'top':  # Top edge
                y1 += dy
            elif self.resize_edge == 'bottom':  # Bottom edge
                y2 += dy
            elif self.resize_edge == 'left':  # Left edge
                x1 += dx
            elif self.resize_edge == 'right':  # Right edge
                x2 += dx

            # Ensure x1 < x2 and y1 < y2
            if x1 > x2:
                x1, x2 = x2, x1
                # Switch resize edge if needed
                if self.resize_edge == 'tl':
                    self.resize_edge = 'tr'
                elif self.resize_edge == 'tr':
                    self.resize_edge = 'tl'
                elif self.resize_edge == 'bl':
                    self.resize_edge = 'br'
                elif self.resize_edge == 'br':
                    self.resize_edge = 'bl'
                elif self.resize_edge == 'left':
                    self.resize_edge = 'right'
                elif self.resize_edge == 'right':
                    self.resize_edge = 'left'

            if y1 > y2:
                y1, y2 = y2, y1
                # Switch resize edge if needed
                if self.resize_edge == 'tl':
                    self.resize_edge = 'bl'
                elif self.resize_edge == 'tr':
                    self.resize_edge = 'br'
                elif self.resize_edge == 'bl':
                    self.resize_edge = 'tl'
                elif self.resize_edge == 'br':
                    self.resize_edge = 'tr'
                elif self.resize_edge == 'top':
                    self.resize_edge = 'bottom'
                elif self.resize_edge == 'bottom':
                    self.resize_edge = 'top'

            # Ensure coordinates are within image bounds
            img_height, img_width = self.current_image.shape[:2]
            x1 = max(0, min(x1, img_width - 1))
            y1 = max(0, min(y1, img_height - 1))
            x2 = max(0, min(x2, img_width - 1))
            y2 = max(0, min(y2, img_height - 1))

            # Update the annotation with integer coordinates
            self.current_annotations[self.selected_annotation_index]['bbox'] = (int(x1), int(y1), int(x2), int(y2))

            # Clear all canvas elements and redraw everything
            self.canvas.delete("all")

            # Redisplay the image
            if self.current_image_display:
                self.canvas.create_image(self.canvas_offset_x, self.canvas_offset_y,
                                        anchor=tk.NW, image=self.current_image_display)

            # Redraw grid if in quick label mode
            if self.quick_label_mode:
                if self.perspective_grid is not None:
                    self.draw_perspective_grid()
                else:
                    self.draw_regular_grid()

            # Redraw all annotations
            self.redraw_annotations()

            # Clear highlight before redrawing
            self.canvas.delete("highlight")

            # Draw highlight with handles for the selected annotation
            ann = self.current_annotations[self.selected_annotation_index]
            x1, y1, x2, y2 = ann['bbox']

            # Scale coordinates
            scaled_x1 = int(x1 * self.scale_factor * self.zoom_level)
            scaled_y1 = int(y1 * self.scale_factor * self.zoom_level)
            scaled_x2 = int(x2 * self.scale_factor * self.zoom_level)
            scaled_y2 = int(y2 * self.scale_factor * self.zoom_level)

            # Draw highlight with handles
            self.canvas.create_rectangle(
                scaled_x1, scaled_y1, scaled_x2, scaled_y2,
                outline="yellow", width=3,
                tags="highlight"
            )

            # Add adjustment handles
            handle_size = 6
            # Top-left handle
            self.canvas.create_rectangle(
                scaled_x1 - handle_size, scaled_y1 - handle_size,
                scaled_x1 + handle_size, scaled_y1 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )
            # Top-right handle
            self.canvas.create_rectangle(
                scaled_x2 - handle_size, scaled_y1 - handle_size,
                scaled_x2 + handle_size, scaled_y1 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )
            # Bottom-left handle
            self.canvas.create_rectangle(
                scaled_x1 - handle_size, scaled_y2 - handle_size,
                scaled_x1 + handle_size, scaled_y2 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )
            # Bottom-right handle
            self.canvas.create_rectangle(
                scaled_x2 - handle_size, scaled_y2 - handle_size,
                scaled_x2 + handle_size, scaled_y2 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )

        elif self.moving and self.selected_annotation_index is not None:
            # Move the selected annotation
            # Get canvas coordinates accounting for scrolling
            canvas_x = self.canvas.canvasx(event.x)
            canvas_y = self.canvas.canvasy(event.y)

            # Convert to original image coordinates
            orig_x = canvas_x / (self.scale_factor * self.zoom_level)
            orig_y = canvas_y / (self.scale_factor * self.zoom_level)

            # Calculate the movement delta in original image coordinates
            dx = (orig_x - self.move_start_x) * self.sensitivity_var.get()
            dy = (orig_y - self.move_start_y) * self.sensitivity_var.get()

            # Update the start position for the next move
            self.move_start_x = orig_x
            self.move_start_y = orig_y

            # Get the current annotation
            ann = self.current_annotations[self.selected_annotation_index]
            x1, y1, x2, y2 = ann['bbox']

            # Apply the movement
            x1 += dx
            y1 += dy
            x2 += dx
            y2 += dy

            # Ensure coordinates are within image bounds
            img_height, img_width = self.current_image.shape[:2]
            x1 = max(0, min(x1, img_width - 1))
            y1 = max(0, min(y1, img_height - 1))
            x2 = max(0, min(x2, img_width - 1))
            y2 = max(0, min(y2, img_height - 1))

            # Update the annotation with integer coordinates
            self.current_annotations[self.selected_annotation_index]['bbox'] = (int(x1), int(y1), int(x2), int(y2))

            # Clear all canvas elements and redraw everything
            self.canvas.delete("all")

            # Redisplay the image
            if self.current_image_display:
                self.canvas.create_image(self.canvas_offset_x, self.canvas_offset_y,
                                        anchor=tk.NW, image=self.current_image_display)

            # Redraw grid if in quick label mode
            if self.quick_label_mode:
                if self.perspective_grid is not None:
                    self.draw_perspective_grid()
                else:
                    self.draw_regular_grid()

            # Redraw all annotations
            self.redraw_annotations()

            # Clear highlight before redrawing
            self.canvas.delete("highlight")

            # Scale coordinates for display
            scaled_x1 = int(x1 * self.scale_factor * self.zoom_level) + self.canvas_offset_x
            scaled_y1 = int(y1 * self.scale_factor * self.zoom_level) + self.canvas_offset_y
            scaled_x2 = int(x2 * self.scale_factor * self.zoom_level) + self.canvas_offset_x
            scaled_y2 = int(y2 * self.scale_factor * self.zoom_level) + self.canvas_offset_y

            # Draw highlight with handles
            self.canvas.create_rectangle(
                scaled_x1, scaled_y1, scaled_x2, scaled_y2,
                outline="yellow", width=3,
                tags="highlight"
            )

            # Add adjustment handles
            handle_size = 6
            # Top-left handle
            self.canvas.create_rectangle(
                scaled_x1 - handle_size, scaled_y1 - handle_size,
                scaled_x1 + handle_size, scaled_y1 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )
            # Top-right handle
            self.canvas.create_rectangle(
                scaled_x2 - handle_size, scaled_y1 - handle_size,
                scaled_x2 + handle_size, scaled_y1 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )
            # Bottom-left handle
            self.canvas.create_rectangle(
                scaled_x1 - handle_size, scaled_y2 - handle_size,
                scaled_x1 + handle_size, scaled_y2 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )
            # Bottom-right handle
            self.canvas.create_rectangle(
                scaled_x2 - handle_size, scaled_y2 - handle_size,
                scaled_x2 + handle_size, scaled_y2 + handle_size,
                fill="yellow", outline="black", tags="highlight"
            )

    def on_mouse_up(self, event):
        if self.drawing:
            self.drawing = False

            # Get final coordinates
            x1, y1 = self.start_x, self.start_y
            x2, y2 = event.x, event.y

            # Ensure x1,y1 is top-left and x2,y2 is bottom-right
            if x1 > x2:
                x1, x2 = x2, x1
            if y1 > y2:
                y1, y2 = y2, y1

            # Check if rectangle is too small
            if abs(x2 - x1) < 5 or abs(y2 - y1) < 5:
                self.canvas.delete(self.rect_id)
                return

            # Get canvas coordinates accounting for scrolling
            canvas_x1 = self.canvas.canvasx(x1)
            canvas_y1 = self.canvas.canvasy(y1)
            canvas_x2 = self.canvas.canvasx(x2)
            canvas_y2 = self.canvas.canvasy(y2)

            # Convert to original image coordinates
            orig_x1 = int(canvas_x1 / (self.scale_factor * self.zoom_level))
            orig_y1 = int(canvas_y1 / (self.scale_factor * self.zoom_level))
            orig_x2 = int(canvas_x2 / (self.scale_factor * self.zoom_level))
            orig_y2 = int(canvas_y2 / (self.scale_factor * self.zoom_level))

            # Ensure coordinates are within image bounds
            img_height, img_width = self.current_image.shape[:2]
            orig_x1 = max(0, min(orig_x1, img_width - 1))
            orig_y1 = max(0, min(orig_y1, img_height - 1))
            orig_x2 = max(0, min(orig_x2, img_width - 1))
            orig_y2 = max(0, min(orig_y2, img_height - 1))

            # Add annotation
            class_name = self.class_var.get()
            self.current_annotations.append({
                'class': class_name,
                'bbox': (orig_x1, orig_y1, orig_x2, orig_y2)
            })

            # Remove temporary rectangle
            self.canvas.delete(self.rect_id)

            # Redraw annotations
            self.redraw_annotations()

            # Update annotations listbox
            self.update_annotations_listbox()

        elif self.moving:
            # End moving
            self.moving = False
            self.selected_annotation_index = None

            # Reset cursor
            self.canvas.config(cursor="")

        elif self.resizing:
            # End resizing
            self.resizing = False
            self.resize_edge = None
            self.selected_annotation_index = None

            # Reset cursor
            self.canvas.config(cursor="")

    def on_annotation_select(self, event):
        # Highlight selected annotation
        selection = self.annotations_listbox.curselection()
        if selection:
            index = selection[0]
            self.canvas.delete("highlight")

            if 0 <= index < len(self.current_annotations):
                ann = self.current_annotations[index]
                x1, y1, x2, y2 = ann['bbox']

                # Scale coordinates with zoom
                x1 = int(x1 * self.scale_factor * self.zoom_level) + self.canvas_offset_x
                y1 = int(y1 * self.scale_factor * self.zoom_level) + self.canvas_offset_y
                x2 = int(x2 * self.scale_factor * self.zoom_level) + self.canvas_offset_x
                y2 = int(y2 * self.scale_factor * self.zoom_level) + self.canvas_offset_y

                # Draw highlight with handles
                self.canvas.create_rectangle(
                    x1, y1, x2, y2,
                    outline="yellow", width=3,
                    tags="highlight"
                )

                # Add adjustment handles if in move mode
                if self.tool_var.get() == "move":
                    handle_size = 6
                    # Top-left handle
                    self.canvas.create_rectangle(
                        x1 - handle_size, y1 - handle_size,
                        x1 + handle_size, y1 + handle_size,
                        fill="yellow", outline="black", tags="highlight"
                    )
                    # Top-right handle
                    self.canvas.create_rectangle(
                        x2 - handle_size, y1 - handle_size,
                        x2 + handle_size, y1 + handle_size,
                        fill="yellow", outline="black", tags="highlight"
                    )
                    # Bottom-left handle
                    self.canvas.create_rectangle(
                        x1 - handle_size, y2 - handle_size,
                        x1 + handle_size, y2 + handle_size,
                        fill="yellow", outline="black", tags="highlight"
                    )
                    # Bottom-right handle
                    self.canvas.create_rectangle(
                        x2 - handle_size, y2 - handle_size,
                        x2 + handle_size, y2 + handle_size,
                        fill="yellow", outline="black", tags="highlight"
                    )

                # Ensure the highlighted area is visible by scrolling to it
                # Calculate the fraction of the canvas width/height
                canvas_width = self.canvas.winfo_width()
                canvas_height = self.canvas.winfo_height()

                # Get the scrollregion
                bbox = self.canvas.bbox("all")
                if bbox:
                    scroll_width = bbox[2] - bbox[0]
                    scroll_height = bbox[3] - bbox[1]

                    # Calculate fractions for scrolling
                    if scroll_width > 0:
                        x_fraction = max(0, min(1, (x1 / scroll_width)))
                        self.canvas.xview_moveto(x_fraction)

                    if scroll_height > 0:
                        y_fraction = max(0, min(1, (y1 / scroll_height)))
                        self.canvas.yview_moveto(y_fraction)

    def delete_selected(self):
        selection = self.annotations_listbox.curselection()
        if selection:
            index = selection[0]
            if 0 <= index < len(self.current_annotations):
                del self.current_annotations[index]

                # Update display while preserving zoom
                self.update_display(preserve_zoom=True)

    def save_annotations(self):
        if not self.current_annotations:
            messagebox.showinfo("Info", "No annotations to save")
            return

        image_file = self.image_files[self.current_image_index]
        image_name = os.path.splitext(image_file)[0]

        # Save image copy
        img_path = os.path.join(self.output_dir, "images", image_file)
        cv2.imwrite(img_path, cv2.cvtColor(self.current_image, cv2.COLOR_RGB2BGR))

        # Save annotations in YOLO format
        img_height, img_width = self.current_image.shape[:2]

        # YOLO format: <class> <x_center> <y_center> <width> <height>
        # All values are normalized to [0, 1]
        yolo_annotations = []

        for ann in self.current_annotations:
            x1, y1, x2, y2 = ann['bbox']
            class_id = CLASS_TO_ID[ann['class']]

            # Convert to YOLO format
            x_center = (x1 + x2) / 2 / img_width
            y_center = (y1 + y2) / 2 / img_height
            width = (x2 - x1) / img_width
            height = (y2 - y1) / img_height

            yolo_annotations.append(f"{class_id} {x_center} {y_center} {width} {height}")

        # Save to file
        label_path = os.path.join(self.output_dir, "labels", f"{image_name}.txt")
        with open(label_path, 'w') as f:
            f.write('\n'.join(yolo_annotations))

        # Also save in JSON format for easier review
        json_annotations = []
        for ann in self.current_annotations:
            x1, y1, x2, y2 = ann['bbox']
            json_annotations.append({
                'class': ann['class'],
                'bbox': [x1, y1, x2, y2]
            })

        json_path = os.path.join(self.output_dir, "labels", f"{image_name}.json")
        with open(json_path, 'w') as f:
            json.dump(json_annotations, f, indent=2)

        messagebox.showinfo("Success", f"Annotations saved for {image_file}")

    def save_and_next(self):
        self.save_annotations()
        self.next_image()

    def load_annotations(self):
        image_file = self.image_files[self.current_image_index]
        image_name = os.path.splitext(image_file)[0]

        # Clear current annotations
        self.current_annotations = []

        # Try to load JSON annotations first
        json_path = os.path.join(self.output_dir, "labels", f"{image_name}.json")
        if os.path.exists(json_path):
            try:
                with open(json_path, 'r') as f:
                    self.current_annotations = json.load(f)
                return
            except:
                pass

        # Try to load YOLO format annotations
        yolo_path = os.path.join(self.output_dir, "labels", f"{image_name}.txt")
        if os.path.exists(yolo_path):
            try:
                img_height, img_width = self.current_image.shape[:2]

                with open(yolo_path, 'r') as f:
                    lines = f.read().strip().split('\n')

                for line in lines:
                    if not line.strip():
                        continue

                    parts = line.strip().split()
                    if len(parts) != 5:
                        continue

                    class_id, x_center, y_center, width, height = parts
                    class_id = int(class_id)
                    x_center = float(x_center)
                    y_center = float(y_center)
                    width = float(width)
                    height = float(height)

                    # Convert from YOLO format to pixel coordinates
                    x1 = int((x_center - width/2) * img_width)
                    y1 = int((y_center - height/2) * img_height)
                    x2 = int((x_center + width/2) * img_width)
                    y2 = int((y_center + height/2) * img_height)

                    # Get class name
                    class_name = PIECE_CLASSES[class_id] if 0 <= class_id < len(PIECE_CLASSES) else "unknown"

                    self.current_annotations.append({
                        'class': class_name,
                        'bbox': (x1, y1, x2, y2)
                    })
            except:
                pass

    def next_image(self):
        self.load_image(self.current_image_index + 1)

    def prev_image(self):
        self.load_image(self.current_image_index - 1)

    def zoom_in(self):
        """Zoom in on the image."""
        self.zoom_level *= self.zoom_factor
        self.apply_zoom()

    def zoom_out(self):
        """Zoom out of the image."""
        self.zoom_level /= self.zoom_factor
        # Limit minimum zoom level
        self.zoom_level = max(0.1, self.zoom_level)
        self.apply_zoom()

    def reset_zoom(self):
        """Reset zoom to original level."""
        self.zoom_level = 1.0
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0
        self.apply_zoom()

    def apply_zoom(self):
        """Apply the current zoom level to the image."""
        if self.current_image is None:
            return

        # Update zoom label
        self.zoom_label.config(text=f"Zoom: {int(self.zoom_level * 100)}%")

        # Get image size
        img_height, img_width = self.current_image.shape[:2]

        # Calculate new size
        new_width = int(img_width * self.scale_factor * self.zoom_level)
        new_height = int(img_height * self.scale_factor * self.zoom_level)

        # Save current view center
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        view_center_x = self.canvas.canvasx(canvas_width/2)
        view_center_y = self.canvas.canvasy(canvas_height/2)

        # Calculate relative position (0-1) within the image
        if self.canvas.bbox("all"):
            old_width = self.canvas.bbox("all")[2] - self.canvas.bbox("all")[0]
            old_height = self.canvas.bbox("all")[3] - self.canvas.bbox("all")[1]
            rel_x = view_center_x / old_width if old_width > 0 else 0.5
            rel_y = view_center_y / old_height if old_height > 0 else 0.5
        else:
            rel_x, rel_y = 0.5, 0.5

        # Use PIL for better quality resizing
        pil_img = Image.fromarray(self.current_image)
        pil_img = pil_img.resize((new_width, new_height), Image.LANCZOS)
        self.current_image_display = ImageTk.PhotoImage(pil_img)

        # Update canvas
        self.canvas.delete("all")
        self.canvas.config(scrollregion=(0, 0, new_width, new_height))
        self.canvas.create_image(0, 0, anchor=tk.NW, image=self.current_image_display)

        # Reset canvas offset
        self.canvas_offset_x = 0
        self.canvas_offset_y = 0

        # Redraw annotations
        self.redraw_annotations()

        # Redraw grid if in quick label mode
        if self.quick_label_mode:
            self.show_grid_overlay()

        # Center view on the same relative position
        self.canvas.xview_moveto(rel_x - (canvas_width/2)/new_width)
        self.canvas.yview_moveto(rel_y - (canvas_height/2)/new_height)

    def redraw_annotations(self):
        """Redraw all annotations with current zoom level."""
        if not self.current_annotations:
            return

        for i, ann in enumerate(self.current_annotations):
            x1, y1, x2, y2 = ann['bbox']
            class_name = ann['class']

            # Scale coordinates with zoom
            x1 = int(x1 * self.scale_factor * self.zoom_level) + self.canvas_offset_x
            y1 = int(y1 * self.scale_factor * self.zoom_level) + self.canvas_offset_y
            x2 = int(x2 * self.scale_factor * self.zoom_level) + self.canvas_offset_x
            y2 = int(y2 * self.scale_factor * self.zoom_level) + self.canvas_offset_y

            # Determine color based on piece color
            color = "red" if class_name.startswith("white") else "blue"

            # Draw rectangle
            self.canvas.create_rectangle(x1, y1, x2, y2, outline=color, width=2, tags=f"ann_{i}")

            # Draw label
            piece_type = class_name.split('_')[1]
            self.canvas.create_text(x1, y1-5, text=piece_type, fill=color, anchor=tk.SW, tags=f"ann_{i}")

    def on_mouse_wheel(self, event):
        """Handle mouse wheel events for zooming."""
        # Determine zoom direction
        if event.delta > 0:
            # Zoom in
            self.zoom_level *= self.zoom_factor
        else:
            # Zoom out
            self.zoom_level /= self.zoom_factor
            # Limit minimum zoom level
            self.zoom_level = max(0.1, self.zoom_level)

        # Apply zoom
        self.apply_zoom()

    def on_pan_start(self, event):
        """Start panning the image."""
        self.is_panning = True
        self.pan_start_x = event.x
        self.pan_start_y = event.y

    def on_pan_move(self, event):
        """Pan the image as the mouse moves."""
        if not self.is_panning:
            return

        # Calculate pan distance
        dx = event.x - self.pan_start_x
        dy = event.y - self.pan_start_y

        # Update canvas offset
        self.canvas_offset_x += dx
        self.canvas_offset_y += dy

        # Update pan start position
        self.pan_start_x = event.x
        self.pan_start_y = event.y

        # Redraw with new offset
        self.apply_zoom()

    def on_pan_end(self, event):
        """End panning."""
        self.is_panning = False

def main():
    parser = argparse.ArgumentParser(description='Chess Piece Labeling Tool')
    parser.add_argument('--image_dir', type=str, default='chess_board_detection/data/real',
                        help='Directory containing images to label')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/piece_detection/dataset',
                        help='Directory to save annotations and processed images')
    parser.add_argument('--corner_annotations', type=str, default='chess_board_detection/data/real_annotations.json',
                        help='Path to corner annotations JSON file')
    args = parser.parse_args()

    # Create Tkinter root
    root = tk.Tk()
    app = ChessPieceLabelTool(root, args.image_dir, args.output_dir, args.corner_annotations)
    root.mainloop()

if __name__ == "__main__":
    main()
