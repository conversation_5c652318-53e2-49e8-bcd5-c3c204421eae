PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> .\run_training.bat
Training simple YOLO model for chess piece detection...
Python version: 3.11.4 (tags/v3.11.4:d2340ef, Jun  7 2023, 05:45:37) [MSC v.1934 64 bit (AMD64)]
PyTorch version: 2.5.1+cu121
CUDA available: True
CUDA device: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB
CUDA Version: 12.1
cuDNN Version: 90100
Training on: 0
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB

=== Training Simple Model (max 100 epochs, no phase system) ===
Dataset: piece_detection/enhanced_dataset_99plus/dataset.yaml
Model: ../yolo11n.pt
Project directory: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo

Starting training...
New https://pypi.org/project/ultralytics/8.3.141 available  Update with 'pip install -U ultralytics'
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=0.001, copy_paste=0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=piece_detection/enhanced_dataset_99plus/dataset.yaml, degrees=0, deterministic=True, device=0, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=True, fliplr=0, flipud=0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0, hsv_s=0, hsv_v=0, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.001, lrf=0.01, mask_ratio=4, max_det=300, mixup=0, mode=train, model=../yolo11n.pt, momentum=0.9, mosaic=0, multi_scale=False, name=simple_basic_20250521_182136, nbs=64, nms=False, opset=None, optimize=False, optimizer=AdamW, overlap_mask=True, patience=20, perspective=0, plots=True, pose=12.0, pretrained=True, profile=False, project=C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136, save_frames=False, save_json=False, save_period=5, save_txt=False, scale=1.0, seed=42, shear=0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments             

  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]         

  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]        

  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]        

  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]      

  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]   

  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]      

  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]   

  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]         

 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]         

 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']  

 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]  

 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']  

 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]   

 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]        

 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]  

 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]      

 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]   

 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]  

YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed
train: Fast image access  (ping: 0.10.0 ms, read: 677.3175.2 MB/s, size: 580.4 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\enha
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.20.0 ms, read: 251.185.5 MB/s, size: 1759.5 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\enhanc
Plotting labels to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136\labels.jpg... 
optimizer: AdamW(lr=0.001, momentum=0.9) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136
Starting training for 100 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/100      1.09G      1.043      3.666     0.9249        125        416: 100%|██████████| 24/24 [00:05<00:00,  4.21it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588    0.00118     0.0706     0.0104    0.00422
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/100      1.24G     0.8839      2.382     0.8787        178        416: 100%|██████████| 24/24 [00:04<00:00,  5.42it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588     0.0536      0.152     0.0702     0.0505

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/100      1.25G     0.7974      1.769     0.8647        183        416: 100%|██████████| 24/24 [00:04<00:00,  5.78it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588     0.0849      0.473      0.182      0.136

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/100      1.25G     0.7391      1.365     0.8536        161        416: 100%|██████████| 24/24 [00:04<00:00,  5.71it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588     0.0676      0.845       0.34      0.251

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      5/100      1.25G     0.7376      1.109     0.8493        144        416: 100%|██████████| 24/24 [00:03<00:00,  6.52it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.773      0.169      0.396       0.29

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      6/100      1.25G     0.7093     0.9804      0.848        159        416: 100%|██████████| 24/24 [00:03<00:00,  7.24it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.555      0.364      0.416      0.306

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      7/100      1.25G      0.714     0.8904     0.8428        154        416: 100%|██████████| 24/24 [00:03<00:00,  7.18it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.493      0.491      0.467      0.363

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      8/100      1.25G     0.6582     0.7783     0.8434        162        416: 100%|██████████| 24/24 [00:03<00:00,  7.31it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.517      0.557      0.554      0.423

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      9/100      1.25G     0.7517     0.7785     0.8506        174        416: 100%|██████████| 24/24 [00:03<00:00,  6.05it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.675      0.512      0.601      0.477
  Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     10/100      1.25G     0.6562     0.7007     0.8341        177        416: 100%|██████████| 24/24 [00:03<00:00,  6.11it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.698      0.489      0.571      0.436

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     11/100      1.25G     0.6055     0.6515     0.8324        182        416: 100%|██████████| 24/24 [00:03<00:00,  6.06it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.595      0.617      0.614      0.488

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     12/100      1.25G     0.6338      0.648      0.833        176        416: 100%|██████████| 24/24 [00:03<00:00,  6.48it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.634      0.634      0.682      0.539

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     13/100      1.25G     0.5678        0.6     0.8273        188        416: 100%|██████████| 24/24 [00:03<00:00,  6.67it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.692      0.551      0.636      0.508

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     14/100      1.25G     0.5598     0.5794     0.8248        171        416: 100%|██████████| 24/24 [00:03<00:00,  7.47it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588       0.65      0.575      0.641      0.517

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     15/100      1.25G     0.5563     0.5667     0.8276        170        416: 100%|██████████| 24/24 [00:03<00:00,  7.14it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.679      0.601      0.652      0.536

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     16/100      1.25G     0.5725     0.5669     0.8168        164        416: 100%|██████████| 24/24 [00:04<00:00,  5.86it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.623      0.586      0.632      0.515

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     17/100      1.25G       0.54     0.5277     0.8174        165        416: 100%|██████████| 24/24 [00:04<00:00,  5.88it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.664      0.542      0.623      0.486

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     18/100      1.25G      0.551     0.5285     0.8182        134        416: 100%|██████████| 24/24 [00:03<00:00,  6.22it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588        0.7      0.585      0.676      0.541

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     19/100      1.25G     0.5385     0.5093     0.8173        179        416: 100%|██████████| 24/24 [00:03<00:00,  6.91it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.749      0.565      0.677      0.543

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     20/100      1.25G     0.5246     0.5049     0.8168        188        416: 100%|██████████| 24/24 [00:03<00:00,  7.87it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.706      0.593      0.678      0.535

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     21/100      1.25G     0.5691     0.5144     0.8187        163        416: 100%|██████████| 24/24 [00:03<00:00,  7.25it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.685      0.574      0.678      0.541

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     22/100      1.25G     0.5456     0.5028     0.8166        190        416: 100%|██████████| 24/24 [00:03<00:00,  7.03it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.664      0.597      0.669      0.526

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     23/100      1.25G     0.4945     0.4798     0.8142        130        416: 100%|██████████| 24/24 [00:04<00:00,  5.98it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.747      0.569      0.663      0.545

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     24/100      1.25G     0.5061     0.4825     0.8052        184        416: 100%|██████████| 24/24 [00:04<00:00,  5.87it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.693      0.573      0.682      0.558

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     25/100      1.25G     0.5092     0.4719     0.8107        178        416: 100%|██████████| 24/24 [00:03<00:00,  6.05it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.742       0.63      0.714      0.578

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     26/100      1.25G      0.499      0.461     0.8055        175        416: 100%|██████████| 24/24 [00:03<00:00,  6.68it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.721      0.634      0.715      0.583

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     27/100      1.25G      0.508     0.4605     0.8051        179        416: 100%|██████████| 24/24 [00:03<00:00,  7.67it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.733      0.635      0.726      0.593

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     28/100      1.25G     0.4948      0.453     0.8084        173        416: 100%|██████████| 24/24 [00:03<00:00,  7.02it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.677      0.611      0.694       0.57

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     29/100      1.25G     0.4888     0.4535     0.8155        114        416: 100%|██████████| 24/24 [00:04<00:00,  5.88it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.683       0.67      0.712      0.587
 30/100      1.25G     0.5023     0.4536      0.808        167        416: 100%|██████████| 24/24 [00:04<00:00,  5.85it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.729      0.634      0.734      0.596

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     31/100      1.25G     0.4572      0.419     0.8057        143        416: 100%|██████████| 24/24 [00:04<00:00,  5.89it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.815      0.575      0.707      0.586

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     32/100      1.25G     0.4543     0.4183     0.8023        142        416: 100%|██████████| 24/24 [00:03<00:00,  6.42it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.684      0.667      0.725      0.598

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     33/100      1.25G     0.4794     0.4178     0.8056        178        416: 100%|██████████| 24/24 [00:03<00:00,  7.50it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.695      0.661      0.741      0.617

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     34/100      1.25G     0.4646     0.4065     0.7982        184        416: 100%|██████████| 24/24 [00:03<00:00,  7.63it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.746      0.645      0.738      0.615

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     35/100      1.25G     0.4482     0.3978     0.7985        165        416: 100%|██████████| 24/24 [00:04<00:00,  5.93it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.715      0.647      0.731      0.605

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     36/100      1.25G     0.4515       0.41     0.8028        170        416: 100%|██████████| 24/24 [00:04<00:00,  5.71it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.756      0.625      0.739      0.615
 30/100      1.25G     0.5023     0.4536      0.808        167        416: 100%|██████████| 24/24 [00:04<00:00,  5.85it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.729      0.634      0.734      0.596

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     31/100      1.25G     0.4572      0.419     0.8057        143        416: 100%|██████████| 24/24 [00:04<00:00,  5.89it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.815      0.575      0.707      0.586

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     32/100      1.25G     0.4543     0.4183     0.8023        142        416: 100%|██████████| 24/24 [00:03<00:00,  6.42it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.684      0.667      0.725      0.598

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     33/100      1.25G     0.4794     0.4178     0.8056        178        416: 100%|██████████| 24/24 [00:03<00:00,  7.50it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.695      0.661      0.741      0.617

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     34/100      1.25G     0.4646     0.4065     0.7982        184        416: 100%|██████████| 24/24 [00:03<00:00,  7.63it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.746      0.645      0.738      0.615

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     35/100      1.25G     0.4482     0.3978     0.7985        165        416: 100%|██████████| 24/24 [00:04<00:00,  5.93it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.715      0.647      0.731      0.605

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     36/100      1.25G     0.4515       0.41     0.8028        170        416: 100%|██████████| 24/24 [00:04<00:00,  5.71it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.756      0.625      0.739      0.615

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     37/100      1.25G     0.4319     0.3886     0.8003        162        416: 100%|██████████| 24/24 [00:04<00:00,  5.66it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.761      0.629      0.724      0.603

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     38/100      1.25G     0.4417     0.3971     0.8015        149        416: 100%|██████████| 24/24 [00:04<00:00,  5.53it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.716      0.666      0.749      0.621

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     39/100      1.25G     0.4426     0.3857      0.799        176        416: 100%|██████████| 24/24 [00:03<00:00,  6.34it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.785      0.576      0.737      0.612

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     40/100      1.25G     0.4516     0.3936     0.8051        176        416: 100%|██████████| 24/24 [00:03<00:00,  6.74it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.734      0.669      0.741      0.612

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     41/100      1.25G     0.4397     0.3815     0.7999        158        416: 100%|██████████| 24/24 [00:03<00:00,  7.80it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.739      0.586      0.698      0.566

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     42/100      1.25G     0.4411     0.3753     0.8022        177        416: 100%|██████████| 24/24 [00:03<00:00,  6.84it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.786      0.572      0.694      0.574

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     43/100      1.25G     0.4638     0.3827     0.7983        181        416: 100%|██████████| 24/24 [00:03<00:00,  6.07it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.698      0.619      0.693      0.583

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     44/100      1.25G     0.4422      0.375     0.8006        148        416: 100%|██████████| 24/24 [00:03<00:00,  6.05it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.694      0.624      0.686      0.554

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     45/100      1.25G     0.4307     0.3742      0.801        191        416: 100%|██████████| 24/24 [00:04<00:00,  5.89it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.686      0.648      0.712      0.585

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     46/100      1.25G     0.4239     0.3641     0.7986        177        416: 100%|██████████| 24/24 [00:03<00:00,  6.52it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.653      0.676      0.716      0.584

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     47/100      1.25G     0.4166     0.3656     0.7954        138        416: 100%|██████████| 24/24 [00:03<00:00,  7.62it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.757      0.561      0.696      0.568

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     48/100      1.25G       0.41     0.3593     0.7975        181        416: 100%|██████████| 24/24 [00:03<00:00,  6.99it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.763      0.603      0.724      0.598

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     49/100      1.25G     0.3914     0.3466     0.7921        183        416: 100%|██████████| 24/24 [00:04<00:00,  5.22it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.709      0.658      0.732      0.605

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     50/100      1.25G     0.4117     0.3584     0.7924        178        416: 100%|██████████| 24/24 [00:04<00:00,  5.41it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.708      0.633       0.72      0.597

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     51/100      1.25G     0.3974     0.3495      0.794        133        416: 100%|██████████| 24/24 [00:04<00:00,  5.57it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.724      0.614      0.703      0.589

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     52/100      1.25G     0.3913      0.348     0.7953        184        416: 100%|██████████| 24/24 [00:04<00:00,  5.60it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.765      0.615       0.73      0.612

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     53/100      1.25G     0.4041     0.3478     0.7949        180        416: 100%|██████████| 24/24 [00:04<00:00,  5.80it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.855      0.581       0.75      0.628

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     54/100      1.25G     0.3921      0.346     0.7938        162        416: 100%|██████████| 24/24 [00:03<00:00,  6.55it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.778       0.63      0.745      0.616

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     55/100      1.25G     0.3958     0.3431     0.7956        176        416: 100%|██████████| 24/24 [00:03<00:00,  6.69it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588       0.72      0.639      0.731      0.609

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     56/100      1.25G     0.3862     0.3371     0.7919        173        416: 100%|██████████| 24/24 [00:03<00:00,  7.48it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.714      0.675      0.738      0.607

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     57/100      1.25G     0.4053      0.352      0.798        155        416: 100%|██████████| 24/24 [00:03<00:00,  6.53it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.664       0.67      0.724      0.597

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     58/100      1.25G     0.3897     0.3325     0.7949        181        416: 100%|██████████| 24/24 [00:04<00:00,  5.87it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588       0.71      0.606      0.703      0.583

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     59/100      1.25G     0.3851      0.329     0.7978        140        416: 100%|██████████| 24/24 [00:04<00:00,  5.87it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.611      0.698      0.716      0.581

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     60/100      1.25G     0.3991     0.3338     0.7956        177        416: 100%|██████████| 24/24 [00:04<00:00,  5.83it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588       0.64       0.69      0.721      0.598

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     61/100      1.25G      0.378     0.3273     0.7917        170        416: 100%|██████████| 24/24 [00:04<00:00,  5.96it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588        0.7       0.64      0.725      0.598

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     62/100      1.25G     0.3743     0.3222     0.7946        152        416: 100%|██████████| 24/24 [00:03<00:00,  6.66it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.728       0.65      0.719      0.591

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     63/100      1.25G     0.3774     0.3274     0.7912        171        416: 100%|██████████| 24/24 [00:03<00:00,  7.96it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588       0.79      0.633      0.749       0.63

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     64/100      1.25G     0.3668     0.3214     0.7909        172        416: 100%|██████████| 24/24 [00:03<00:00,  6.73it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.724      0.576      0.707      0.593

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     65/100      1.25G     0.3952     0.3339     0.7856        155        416: 100%|██████████| 24/24 [00:04<00:00,  5.75it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.653      0.627      0.693      0.585

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     66/100      1.25G      0.361     0.3174     0.7942        172        416: 100%|██████████| 24/24 [00:04<00:00,  5.91it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.696      0.626       0.71      0.594

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     67/100      1.25G     0.3719     0.3205     0.7894        166        416: 100%|██████████| 24/24 [00:04<00:00,  5.91it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588        0.7      0.638      0.728      0.612

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     68/100      1.25G     0.3527     0.3125     0.7915        159        416: 100%|██████████| 24/24 [00:03<00:00,  6.15it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.703      0.667      0.742       0.61

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     69/100      1.25G     0.3879      0.328     0.7952        185        416: 100%|██████████| 24/24 [00:03<00:00,  6.64it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.686      0.694      0.763       0.63

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     70/100      1.25G     0.3559     0.3075     0.7905        181        416: 100%|██████████| 24/24 [00:03<00:00,  7.35it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.713      0.683      0.757      0.627
  Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     71/100      1.25G     0.3657     0.3108     0.7944        161        416: 100%|██████████| 24/24 [00:03<00:00,  6.94it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.697      0.682      0.743      0.623

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     72/100      1.25G     0.3572     0.3117     0.7872        170        416: 100%|██████████| 24/24 [00:04<00:00,  5.95it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.675      0.679      0.724      0.612

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     73/100      1.25G     0.3555     0.3066     0.7888        173        416: 100%|██████████| 24/24 [00:04<00:00,  5.74it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.703      0.668      0.724        0.6

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     74/100      1.25G      0.363     0.3074     0.7911        174        416: 100%|██████████| 24/24 [00:04<00:00,  5.81it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588        0.7      0.684      0.748       0.63

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     75/100      1.25G     0.3491     0.3007      0.791        179        416: 100%|██████████| 24/24 [00:03<00:00,  6.36it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.709      0.665      0.732      0.623

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     76/100      1.25G     0.3312     0.2896     0.7862        147        416: 100%|██████████| 24/24 [00:03<00:00,  6.86it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.763      0.644      0.748      0.638

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     77/100      1.25G     0.3459     0.2964     0.7875        117        416: 100%|██████████| 24/24 [00:03<00:00,  7.50it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.723      0.661      0.756      0.643

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     78/100      1.25G     0.3384     0.2913     0.7883        178        416: 100%|██████████| 24/24 [00:03<00:00,  6.96it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.766       0.64      0.754      0.636

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     79/100      1.25G     0.3413     0.2939     0.7861        190        416: 100%|██████████| 24/24 [00:03<00:00,  6.33it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.734      0.653      0.757       0.64

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     80/100      1.25G     0.3369     0.2921     0.7876        183        416: 100%|██████████| 24/24 [00:04<00:00,  5.84it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.833      0.581      0.751      0.628

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     81/100      1.25G     0.3334     0.2903     0.7915        163        416: 100%|██████████| 24/24 [00:04<00:00,  5.97it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.717      0.639      0.742      0.618

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     82/100      1.25G     0.3489      0.303     0.7878        157        416: 100%|██████████| 24/24 [00:04<00:00,  5.91it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.706      0.662      0.745      0.625

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     83/100      1.25G     0.3317     0.2891     0.7882        148        416: 100%|██████████| 24/24 [00:03<00:00,  6.70it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.673      0.694      0.754      0.638

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     84/100      1.25G     0.3449     0.2995     0.7851        170        416: 100%|██████████| 24/24 [00:03<00:00,  6.95it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.816      0.581      0.747      0.637

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     85/100      1.25G     0.3226     0.2847     0.7816        174        416: 100%|██████████| 24/24 [00:03<00:00,  6.98it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.673      0.661      0.744      0.629

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     86/100      1.25G     0.3241      0.285     0.7844        137        416: 100%|██████████| 24/24 [00:03<00:00,  7.34it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.736      0.632       0.74      0.628

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     87/100      1.25G     0.3099     0.2765     0.7873        160        416: 100%|██████████| 24/24 [00:03<00:00,  6.31it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.649      0.677      0.733      0.625

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     88/100      1.25G     0.3226     0.2856     0.7866        157        416: 100%|██████████| 24/24 [00:04<00:00,  5.93it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588       0.65      0.723      0.741       0.63

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     89/100      1.25G     0.3167     0.2789     0.7855        147        416: 100%|██████████| 24/24 [00:03<00:00,  6.03it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.698      0.669      0.746       0.63

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     90/100      1.25G     0.3186     0.2812     0.7868        158        416: 100%|██████████| 24/24 [00:04<00:00,  5.84it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.664       0.68      0.746      0.629
Closing dataloader mosaic
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     91/100      1.25G      0.327      0.286     0.7853        196        416: 100%|██████████| 24/24 [00:04<00:00,  5.40it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00 
                   all         30        588      0.836       0.57      0.744      0.629

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     92/100      1.25G     0.3037     0.2713      0.785        161        416: 100%|██████████| 24/24 [00:03<00:00,  6.05it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.825       0.57      0.741      0.628

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     93/100      1.25G     0.3056     0.2752     0.7845        191        416: 100%|██████████| 24/24 [00:03<00:00,  6.37it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.658      0.672      0.739      0.628

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     94/100      1.25G     0.3115     0.2748     0.7858        150        416: 100%|██████████| 24/24 [00:03<00:00,  6.98it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588       0.74      0.616      0.738      0.627

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     95/100      1.25G     0.3134     0.2754     0.7868        167        416: 100%|██████████| 24/24 [00:03<00:00,  7.88it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.752      0.615      0.744       0.63

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     96/100      1.25G     0.3162     0.2798     0.7877        170        416: 100%|██████████| 24/24 [00:03<00:00,  7.32it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.711      0.643      0.748      0.634

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     97/100      1.25G     0.3228     0.2796     0.7893        165        416: 100%|██████████| 24/24 [00:03<00:00,  6.00it
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.741      0.615      0.746      0.634
EarlyStopping: Training stopped early as no improvement observed in last 20 epochs. Best results observed at epoch 77, best model saved as best.pt.
To update EarlyStopping(patience=20) pass a new patience value, i.e. `patience=300` or use `patience=0` to disable EarlyStopping.

97 epochs completed in 0.128 hours.
Optimizer stripped from C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136\weights\last.pt, 5.5MB
Optimizer stripped from C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136\weights\best.pt, 5.5MB

Validating C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136\weights\best.pt...
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 1/1 [00:00<00:00
                   all         30        588      0.723       0.66      0.755      0.642
            white_pawn         30        150      0.799       0.94      0.957      0.802
          white_knight         18         18      0.556        0.5      0.553        0.5
          white_bishop         30         36      0.369      0.333      0.448      0.401
            white_rook         24         30      0.789      0.873      0.919      0.787
           white_queen         30         30          1      0.364      0.764      0.576
            white_king         30         30      0.771      0.673      0.761      0.604
            black_pawn         30        150      0.891      0.993      0.984      0.842
          black_knight         18         18      0.649      0.618      0.724      0.665
          black_bishop         30         36      0.476      0.639      0.613       0.54
            black_rook         24         30      0.641      0.933      0.899      0.818
           black_queen         30         30      0.782      0.433      0.658       0.49
            black_king         30         30      0.949       0.62      0.785      0.683
Speed: 0.1ms preprocess, 1.3ms inference, 0.0ms loss, 2.0ms postprocess per image
Results saved to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\simple_yolo\simple_basic_20250521_182136

==================================================
=== Training Results After 100 Epochs ===
Best mAP50: 0.7554 (Target: 0.9900)
Best Precision: 0.7227 (Target: 0.9900)
Best Recall: 0.6600 (Target: 0.9900)
Best achieved at epoch: 0

Some targets have not been met yet:
- mAP50: 0.7554 < 0.9900
- Precision: 0.7227 < 0.9900
- Recall: 0.6600 < 0.9900

Would you like to continue training for 10 more epochs? (y/n): n

Training completed without continuation.

=== Final Training Results ===
Best mAP50: 0.7554
Best mAP50-95: 0.6424
Best Precision: 0.7227
Best Recall: 0.6600
Best achieved at epoch: 0
Press any key to continue . . . 
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 