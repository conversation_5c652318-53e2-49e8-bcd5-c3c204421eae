"""
Fixed loss functions for chess board detection.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class DiceLoss(nn.Module):
    """
    Dice loss for segmentation tasks.
    """
    def __init__(self, smooth=1.0):
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = torch.sigmoid(pred)

        # Flatten
        pred_flat = pred.view(-1)
        target_flat = target.view(-1)

        # Calculate Dice coefficient
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum()

        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)
        return 1.0 - dice


class FixedHeatmapLoss(nn.Module):
    """
    Fixed version of the heatmap loss with proper handling of separation loss.
    """
    def __init__(self, separation_weight=0.5, peak_separation_weight=0.3):
        super(FixedHeatmapLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight

    def forward(self, pred, target):
        # Basic MSE loss for matching ground truth
        mse_loss = self.mse_loss(pred, target)
        
        # Add spatial separation loss to ensure corners are distinct
        batch_size = pred.size(0)
        separation_loss = 0.0
        peak_separation_loss = 0.0
        
        # For each pair of corner heatmaps, encourage separation
        for i in range(4):
            for j in range(i+1, 4):
                # Calculate overlap between different corner heatmaps
                # We want to minimize this overlap
                # Apply softmax to ensure values are positive and normalized
                pred_i = F.softmax(pred[:, i].view(batch_size, -1), dim=1)
                pred_j = F.softmax(pred[:, j].view(batch_size, -1), dim=1)
                
                # Calculate dot product as a measure of overlap
                overlap = torch.sum(pred_i * pred_j) / batch_size
                separation_loss += overlap
                
                # Find peak locations for each heatmap
                peak_i = self._find_peak_locations(pred[:, i])
                peak_j = self._find_peak_locations(pred[:, j])
                
                # Calculate distance between peaks
                # We want to maximize this distance (minimize negative distance)
                peak_distance = self._calculate_peak_distance(peak_i, peak_j)
                
                # Use a bounded function to prevent negative values
                # This encourages peaks to be far apart but has a maximum penalty
                peak_sep = 1.0 / (1.0 + peak_distance)  # Bounded between 0 and 1
                peak_separation_loss += peak_sep
        
        # Normalize by number of pairs
        num_pairs = 6  # 4 choose 2 = 6 pairs
        separation_loss = separation_loss / num_pairs
        peak_separation_loss = peak_separation_loss / num_pairs
        
        # Add small epsilon to ensure positive values
        epsilon = 1e-6
        
        # Combine losses with absolute value to ensure positive
        total_loss = (
            mse_loss + 
            self.separation_weight * (separation_loss + epsilon) + 
            self.peak_separation_weight * (peak_separation_loss + epsilon)
        )
        
        # Add debugging information
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print(f"WARNING: Invalid loss detected!")
            print(f"MSE Loss: {mse_loss.item()}")
            print(f"Separation Loss: {separation_loss.item()}")
            print(f"Peak Separation Loss: {peak_separation_loss.item()}")
            # Return a safe value to prevent training crash
            return torch.tensor(1.0, device=pred.device, requires_grad=True)
        
        return total_loss

    def _find_peak_locations(self, heatmaps):
        """Find the peak location in each heatmap in the batch."""
        batch_size = heatmaps.size(0)
        height = heatmaps.size(1)
        width = heatmaps.size(2)
        
        # Apply softmax to normalize heatmaps
        heatmaps_flat = F.softmax(heatmaps.view(batch_size, -1), dim=1)
        heatmaps = heatmaps_flat.view(batch_size, height, width)
        
        # Flatten and find max indices
        flat_indices = torch.argmax(heatmaps.view(batch_size, -1), dim=1)
        
        # Convert to 2D coordinates
        y = flat_indices // width
        x = flat_indices % width
        
        # Stack coordinates
        return torch.stack([x, y], dim=1).float()
    
    def _calculate_peak_distance(self, peaks1, peaks2):
        """Calculate Euclidean distance between peak locations."""
        # Calculate squared distance
        squared_dist = torch.sum((peaks1 - peaks2) ** 2, dim=1)
        
        # Take square root and mean across batch
        # Add epsilon to avoid numerical issues
        epsilon = 1e-6
        return torch.mean(torch.sqrt(squared_dist + epsilon))
