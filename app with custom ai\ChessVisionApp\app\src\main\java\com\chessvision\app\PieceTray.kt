package com.chessvision.app

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.res.painterResource
import androidx.compose.foundation.Image
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.unit.IntOffset
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.draw.shadow

@Composable
fun PieceTray(
    boardState: ChessBoardState,
    modifier: Modifier = Modifier
) {
    val allPieces = remember {
        listOf(
            ChessPiece(PieceType.KING, PieceColor.WHITE, R.drawable.wk),
            ChessPiece(PieceType.QUEEN, PieceColor.WHITE, R.drawable.wq),
            ChessPiece(PieceType.ROOK, PieceColor.WHITE, R.drawable.wr),
            ChessPiece(PieceType.BISHOP, PieceColor.WHITE, R.drawable.wb),
            ChessPiece(PieceType.KNIGHT, PieceColor.WHITE, R.drawable.wn),
            ChessPiece(PieceType.PAWN, PieceColor.WHITE, R.drawable.wp),
            ChessPiece(PieceType.KING, PieceColor.BLACK, R.drawable.bk),
            ChessPiece(PieceType.QUEEN, PieceColor.BLACK, R.drawable.bq),
            ChessPiece(PieceType.ROOK, PieceColor.BLACK, R.drawable.br),
            ChessPiece(PieceType.BISHOP, PieceColor.BLACK, R.drawable.bb),
            ChessPiece(PieceType.KNIGHT, PieceColor.BLACK, R.drawable.bn),
            ChessPiece(PieceType.PAWN, PieceColor.BLACK, R.drawable.bp)
        )
    }

    AnimatedVisibility(
        visible = boardState.isEditMode,
        enter = slideInVertically { it } + fadeIn(),
        exit = slideOutVertically { it } + fadeOut()
    ) {
        Card(
            modifier = modifier
                .fillMaxWidth()
                .padding(horizontal = 8.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF262421).copy(alpha = 0.95f)
            ),
            shape = RoundedCornerShape(20.dp),
            elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Piece Tray",
                        style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
                        color = Color.White
                    )

                    // Clear board button
                    Card(
                        onClick = {
                            // Clear the entire board
                            for (rank in 0..7) {
                                for (file in 0..7) {
                                    boardState.board[rank][file] = null
                                }
                            }
                            boardState.selectedPieceForPlacement = null
                        },
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFFD32F2F)
                        ),
                        shape = RoundedCornerShape(12.dp)
                    ) {
                        Box(
                            modifier = Modifier.padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.Default.Clear,
                                contentDescription = "Clear Board",
                                modifier = Modifier.size(18.dp),
                                tint = Color.White
                            )
                        }
                    }
                }

                Spacer(modifier = Modifier.height(12.dp))

                // Piece selection grid
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(horizontal = 4.dp)
                ) {
                    items(allPieces) { piece ->
                        PieceTrayItem(
                            piece = piece,
                            isSelected = boardState.selectedPieceForPlacement == piece,
                            onClick = {
                                boardState.selectedPieceForPlacement = if (boardState.selectedPieceForPlacement == piece) {
                                    null // Deselect if already selected
                                } else {
                                    piece // Select this piece
                                }
                            },
                            boardState = boardState
                        )
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Instructions
                Text(
                    text = if (boardState.selectedPieceForPlacement != null) {
                        "Tap a square to place the selected piece, or drag to position"
                    } else {
                        "Select a piece to place on the board"
                    },
                    style = MaterialTheme.typography.bodySmall,
                    color = Color(0xFFB0B0B0),
                    modifier = Modifier.fillMaxWidth()
                )
            }
        }
    }
}

@Composable
fun PieceTrayItem(
    piece: ChessPiece,
    isSelected: Boolean,
    onClick: () -> Unit,
    boardState: ChessBoardState
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) Color(0xFF769656).copy(alpha = 0.8f) else Color(0xFF3a3a3a).copy(alpha = 0.6f),
        animationSpec = tween(300, easing = EaseOutCubic),
        label = "tray_item_background"
    )

    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.1f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "tray_item_scale"
    )

    Card(
        onClick = onClick,
        modifier = Modifier
            .size(56.dp)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .let { mod ->
                // Add drag functionality for pieces in tray
                mod.pointerInput(piece) {
                    detectDragGestures(
                        onDragStart = { offset ->
                            boardState.startDragFromTray(piece, offset)
                        },
                        onDragEnd = {
                            boardState.endDrag()
                        },
                        onDrag = { _, dragAmount ->
                            boardState.updateDragPosition(boardState.dragOffset + dragAmount)
                        }
                    )
                }
            },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isSelected) 6.dp else 2.dp
        )
    ) {
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            // Show piece unless it's being dragged
            val isDraggedPiece = boardState.isDraggingFromTray && boardState.draggedFromTrayPiece == piece

            androidx.compose.animation.AnimatedVisibility(
                visible = !isDraggedPiece,
                enter = fadeIn(animationSpec = tween(200)),
                exit = fadeOut(animationSpec = tween(200))
            ) {
                Image(
                    painter = painterResource(id = piece.drawableRes),
                    contentDescription = "${piece.color} ${piece.type}",
                    modifier = Modifier.size(32.dp)
                )
            }

            // Selection indicator
            androidx.compose.animation.AnimatedVisibility(
                visible = isSelected,
                enter = scaleIn() + fadeIn(),
                exit = scaleOut() + fadeOut()
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.radialGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color(0xFF769656).copy(alpha = 0.3f)
                                )
                            ),
                            RoundedCornerShape(12.dp)
                        )
                )
            }
        }
    }
}
