# Targeted Chess Piece Augmentation System

This system creates specialized augmented datasets to improve detection of problematic chess pieces. It focuses on pieces that performed poorly in previous models, applying piece-specific augmentation techniques.

## Overview

The targeted augmentation system:

1. **Identifies images** containing problematic chess pieces
2. **Applies specialized augmentations** based on piece type
3. **Creates a balanced dataset** with proper train/validation split
4. **Prepares everything** for training a new, improved model

## Quick Start

### 1. Create the Targeted Dataset

Run the batch file to create an augmented dataset focused on problematic pieces:

```bash
.\chess_board_detection\piece_detection\create_targeted_dataset.bat
```

This will:
- Process images containing white_bishop, white_knight, white_queen, black_queen, and black_bishop
- Create 15 augmented versions of each image
- Save the augmented dataset to `chess_board_detection/piece_detection/targeted_dataset`

### 2. Train the Model with Enhanced Features

After dataset creation, use our enhanced training script:

```bash
.\chess_board_detection\piece_detection\train_targeted_model.bat
```

This script includes several improvements:

1. **Class Weighting**: Applies higher weights to problematic pieces during training
2. **Learning Rate Finder**: Option to find the optimal learning rate before training
3. **Confusion Matrix Analysis**: Generates and analyzes a confusion matrix for problematic pieces
4. **Visualization**: Creates visualizations of predictions for problematic pieces

The script will ask if you want to run the learning rate finder first. If you choose "y", it will:
1. Run the learning rate finder to determine the optimal learning rate
2. Use this learning rate for training
3. Apply all the other enhancements

If you choose "n", it will use the default learning rate (0.01) from the successful model.

## Detailed Usage

### Understanding the Script Parameters

The augmentation script accepts several parameters:

```bash
python chess_board_detection/piece_detection/create_targeted_dataset.py ^
  --source "SOURCE_DATASET_PATH" ^
  --output "OUTPUT_DATASET_PATH" ^
  --augmentations NUM_AUGMENTATIONS ^
  --pieces PIECE1 PIECE2 PIECE3 ...
```

- **source**: Path to the original dataset directory (containing images and labels folders)
- **output**: Path where the augmented dataset will be saved
- **augmentations**: Number of augmented versions to create per original image
- **pieces**: List of piece types to focus on (e.g., white_bishop black_queen)

### Available Piece Types

The system recognizes the following piece types:

- `white_pawn`
- `white_knight`
- `white_bishop`
- `white_rook`
- `white_queen`
- `white_king`
- `black_pawn`
- `black_knight`
- `black_bishop`
- `black_rook`
- `black_queen`
- `black_king`

### Customizing Augmentations

Each piece type has specialized augmentations designed to improve its detection:

1. **Bishops**: Enhanced perspective variations and contrast adjustments
2. **Queens**: Improved shadow handling and subtle rotations
3. **Knights**: More aggressive rotations and noise variations

To modify these augmentations, edit the corresponding transform functions in `create_targeted_dataset.py`:

- `get_bishop_transform()`
- `get_queen_transform()`
- `get_knight_transform()`
- `get_general_transform()`

### Creating Custom Augmentation Sets

You can create datasets focused on different pieces:

```bash
# Example: Focus on just the queens
python chess_board_detection/piece_detection/create_targeted_dataset.py ^
  --source "chess_board_detection/piece_detection/enhanced_dataset_99plus" ^
  --output "chess_board_detection/piece_detection/queens_dataset" ^
  --augmentations 20 ^
  --pieces white_queen black_queen
```

## Training Recommendations

### Optimal Training Parameters

For best results with the targeted dataset, use the configuration from the first successful v11n model:

```bash
python -m ultralytics train ^
  --model yolo11n.pt ^
  --data PATH_TO_DATASET/dataset.yaml ^
  --epochs 100 ^
  --batch 16 ^
  --img 416 ^
  --patience 100 ^
  --lr0 0.01 ^
  --lrf 0.01 ^
  --momentum 0.937 ^
  --weight_decay 0.0005 ^
  --warmup_epochs 3.0 ^
  --warmup_momentum 0.8 ^
  --warmup_bias_lr 0.1 ^
  --mosaic 1.0 ^
  --mixup 0.5 ^
  --degrees 15.0 ^
  --translate 0.2 ^
  --scale 0.5 ^
  --shear 2.0 ^
  --fliplr 0.5 ^
  --perspective 0.0005 ^
  --hsv_h 0.015 ^
  --hsv_s 0.7 ^
  --hsv_v 0.4 ^
  --close_mosaic 50 ^
  --box 7.5 ^
  --cls 0.5 ^
  --dfl 1.5 ^
  --amp ^
  --cache ^
  --iou 0.7 ^
  --max_det 300
```

### Key Parameter Explanations

#### Basic Configuration
- **epochs**: 100 epochs provides sufficient training time
- **patience**: 100 ensures training doesn't stop too early
- **img**: 416 sets the image size to 416x416 pixels

#### Optimization Settings
- **lr0**: 0.01 sets the initial learning rate
- **lrf**: 0.01 sets the final learning rate factor
- **momentum**: 0.937 controls the momentum of the optimizer
- **weight_decay**: 0.0005 controls L2 regularization

#### Augmentation Settings
- **mosaic**: 1.0 enables mosaic augmentation during training
- **mixup**: 0.5 applies mixup augmentation with 50% probability
- **degrees**: 15.0 allows rotation up to 15 degrees
- **translate**: 0.2 allows translation up to 20% of image size
- **scale**: 0.5 allows scaling between 0.5x and 1.5x
- **close_mosaic**: 50 disables mosaic augmentation after 50 epochs for fine-tuning

#### Loss Function Weights
- **box**: 7.5 sets the weight for bounding box loss
- **cls**: 0.5 sets the weight for classification loss
- **dfl**: 1.5 sets the weight for distribution focal loss

### Monitoring Training

During training, pay special attention to:

1. **Per-class metrics**: Check if problematic pieces are improving
2. **Validation mAP**: Should increase steadily without plateauing early
3. **Precision and recall**: Both should improve for target pieces

## Evaluating Results

After training, evaluate the model to see if problematic pieces have improved:

```bash
python -m ultralytics val ^
  --model PATH_TO_TRAINED_MODEL/best.pt ^
  --data PATH_TO_DATASET/dataset.yaml
```

Look for:
- Improved mAP50 for previously problematic pieces
- Better precision and recall for bishops, knights, and queens
- Overall mAP50 approaching or exceeding 0.99

## Troubleshooting

### Common Issues

1. **"No images found with target pieces"**
   - Check that your source dataset contains the specified pieces
   - Verify the directory structure has images/ and labels/ folders

2. **"Error augmenting [image]"**
   - Usually caused by invalid bounding boxes after augmentation
   - Try reducing the intensity of transformations in the transform functions

3. **Out of memory during training**
   - Reduce batch size
   - Use a smaller image size (e.g., 384 instead of 416)

### Getting Help

If you encounter issues:
1. Check the console output for specific error messages
2. Review the augmentation parameters in the script
3. Verify that the dataset YAML file was created correctly

## Enhanced Features

### 1. Class Weighting

The training script applies higher weights to problematic pieces:

```python
class_weights = {
    0: 1.0,  # white_pawn
    1: 2.0,  # white_knight - problematic
    2: 3.0,  # white_bishop - very problematic
    3: 1.0,  # white_rook
    4: 2.5,  # white_queen - problematic
    5: 1.0,  # white_king
    6: 1.0,  # black_pawn
    7: 1.0,  # black_knight
    8: 2.0,  # black_bishop - problematic
    9: 1.0,  # black_rook
    10: 2.5, # black_queen - problematic
    11: 1.0  # black_king
}
```

This helps the model focus more on the pieces that previously had poor detection performance.

### 2. Learning Rate Finder

The learning rate finder automatically determines the optimal learning rate for your specific dataset:

1. It runs a quick training session with an increasing learning rate
2. Analyzes the loss curve to find the point of fastest descent
3. Suggests an optimal learning rate value
4. Saves a plot of the learning rate vs. loss

This helps achieve faster convergence and better final performance.

### 3. Confusion Matrix Analysis

After training, the script generates a detailed confusion matrix and analyzes it:

1. Creates a visual heatmap showing which classes are confused with each other
2. Provides specific analysis for problematic pieces:
   - True Positives, False Positives, and False Negatives
   - Top classes that each problematic piece is confused with
   - Suggestions for improvement based on confusion patterns

### 4. Visualization of Problematic Pieces

The script automatically creates visualizations of predictions for problematic pieces:

1. Selects validation images containing problematic pieces
2. Runs the model to generate predictions
3. Saves the visualizations with bounding boxes and confidence scores
4. Helps you visually inspect how well the model is detecting problematic pieces

## Advanced Usage

### Combining with Existing Datasets

You can combine the targeted dataset with your original dataset:

```python
# In your dataset.yaml file
train: ['original_dataset/images/train', 'targeted_dataset/images/train']
val: ['original_dataset/images/val', 'targeted_dataset/images/val']
```

### Creating Ensemble Models

For even better results, train multiple models and create an ensemble:

1. Train on the original dataset
2. Train on the targeted dataset
3. Create an ensemble of both models

```bash
# Create ensemble
python -m ultralytics ensemble ^
  model1.pt model2.pt ^
  --save-dir ./ensemble_models
```

### Fine-tuning Existing Models

You can also fine-tune an existing model on the targeted dataset:

```bash
python -m ultralytics train ^
  --model PATH_TO_EXISTING_MODEL/best.pt ^
  --data targeted_dataset/dataset.yaml ^
  --epochs 50 ^
  --patience 50
```

## Performance Expectations

With the targeted augmentation approach, you should expect:

1. **White Bishop**: mAP50 improvement from 0.448 to ≥0.95
2. **White Knight**: mAP50 improvement from 0.553 to ≥0.95
3. **White Queen**: Recall improvement from 0.364 to ≥0.95
4. **Black Queen**: Recall improvement from 0.433 to ≥0.95
5. **Black Bishop**: mAP50 improvement from 0.613 to ≥0.95

Overall, the model should achieve:
- mAP50: ≥0.99
- Precision: ≥0.99
- Recall: ≥0.99
