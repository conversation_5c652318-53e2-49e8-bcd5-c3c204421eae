PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/piece_detection/train_yolo_gpu.py --model yolo11n.pt --data_yaml "chess_board_detection/piece_detection/augmented_dataset_416x416_enhanced/dataset.yaml" --epochs 100 --batch 16 --device 0 --img-size 416 --output_dir "chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu"
Training model yolo11n.pt on dataset chess_board_detection/piece_detection/augmented_dataset_416x416_enhanced/dataset.yaml for 100 epochs
Python version: 3.11.4
PyTorch version: 2.5.1+cu121
CUDA available: True
CUDA device: NVIDIA GeForce RTX 3050 6GB Laptop GPU
Training on: 0
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB
CUDA Version: 12.1
cuDNN Version: 90100
New https://pypi.org/project/ultralytics/8.3.141 available  Update with 'pip install -U ultralytics'
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=True, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=True, cfg=None, classes=None, close_mosaic=50, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=chess_board_detection/piece_detection/augmented_dataset_416x416_enhanced/dataset.yaml, degrees=15.0, deterministic=True, device=0, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=True, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.5, mode=train, model=yolo11n.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=chess_pieces_20250521_091541, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0005, plots=True, pose=12.0, pretrained=True, profile=False, project=chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541, save_frames=False, save_json=False, save_period=100, save_txt=False, scale=0.5, seed=42, shear=2.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.2, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=4, workspace=None
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments             

  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]         

  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]        

  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]        

  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]      

  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]   

  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]      

  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]   

  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]         

 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]         

 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']  

 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]  

 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']  

 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]   

 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]        

 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]  

 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]      

 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]                   

 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]   

 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]  

YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.00.0 ms, read: 911.1451.2 MB/s, size: 71.9 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\augm
WARNING cache='ram' may produce non-deterministic training results. Consider cache='disk' as a deterministic alternative if your disk space allows.
train: Caching images (0.1GB RAM): 100%|██████████| 198/198 [00:00<00:00, 3276.26it/s]
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 534.5160.0 MB/s, size: 76.7 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\augmen
WARNING cache='ram' may produce non-deterministic training results. Consider cache='disk' as a deterministic alternative if your disk space allows.
val: Caching images (0.0GB RAM): 100%|██████████| 5/5 [00:00<00:00, 1559.45it/s]
Plotting labels to chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\labels.jpg...
optimizer: 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically...
optimizer: AdamW(lr=0.000625, momentum=0.9) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 4 dataloader workers
Logging results to chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541
Starting training for 100 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/100     0.219G      1.971      4.715      1.262        303        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93          0          0          0          0

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/100       1.2G       1.48      4.496      1.025        186        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93   0.000366     0.0111   0.000285   2.85e-05

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/100      1.21G       1.34      4.302     0.9478        188        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93    0.00339     0.0713    0.00265    0.00137

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/100      1.32G       1.32      4.093     0.9279        226        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93    0.00825      0.136     0.0448     0.0157

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      5/100      1.32G      1.265      3.839      0.906        148        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93     0.0275      0.303      0.106     0.0645

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      6/100      1.32G      1.282      3.622     0.9286        303        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93     0.0434      0.463      0.174      0.118

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      7/100      1.32G       1.28      3.243     0.9236        193        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93     0.0514      0.614      0.251      0.179

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      8/100      1.32G      1.215      2.926     0.9095        124        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.727      0.163      0.349      0.251

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      9/100      1.32G      1.243      2.737     0.9131        269        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.584      0.284      0.399      0.281

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     10/100      1.32G      1.191      2.365     0.9163        251        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.48      0.528      0.531      0.361

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     11/100      1.32G      1.204      2.122     0.9212        252        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.398      0.638       0.62      0.462

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     12/100      1.32G      1.141      1.891     0.8988        172        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.698       0.67      0.673      0.534

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     13/100      1.32G      1.144      1.783     0.9048        228        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.832      0.609      0.686      0.549

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     14/100      1.32G      1.157      1.695     0.8987        203        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.832      0.599       0.72      0.577

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     15/100      1.32G      1.131      1.651     0.8912        169        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.864       0.56      0.747      0.592

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     16/100      1.32G      1.083      1.509     0.8908        117        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.826      0.669      0.777      0.631

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     17/100      1.32G      1.062      1.437     0.8863        229        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.667      0.727       0.81      0.676

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     18/100      1.32G      1.051      1.364     0.8811        203        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.693       0.73      0.801      0.668

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     19/100      1.32G      1.071      1.381      0.885        254        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.711      0.773      0.821      0.688

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     20/100      1.32G      1.037        1.3     0.8817        136        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.691      0.827      0.874      0.715

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     21/100      1.32G      1.053       1.27      0.883        323        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.814      0.816      0.887      0.769

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     22/100      1.32G      1.056      1.222     0.8895        221        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.799      0.901      0.914      0.759

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     23/100      1.45G      1.019      1.179     0.8787        248        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.84      0.836       0.89      0.766

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     24/100      1.45G      1.032      1.162     0.8764        123        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93        0.8      0.837      0.892      0.722

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     25/100      1.45G      1.018      1.131     0.8768        239        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.83      0.893      0.907      0.764

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     26/100      1.45G     0.9898      1.104     0.8792        312        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.821      0.891      0.916      0.752

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     27/100      1.45G      1.035      1.096     0.8733        295        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93        0.9      0.885      0.923       0.77

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     28/100      1.45G      1.014       1.07     0.8784        350        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.895      0.899      0.929      0.771

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     29/100      1.45G     0.9757      1.026     0.8851        152        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.849      0.887      0.917      0.778

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     30/100      1.45G      1.005      1.036     0.8705        321        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.896      0.887      0.921      0.777
  Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     31/100      1.45G     0.9731     0.9942     0.8781        265        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.95      0.863      0.918      0.729

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     32/100      1.45G     0.9392     0.9445     0.8664        151        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.943      0.868      0.919      0.785

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     33/100      1.45G     0.9677     0.9822     0.8731        278        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.925      0.867      0.929      0.765

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     34/100      1.45G     0.9439     0.9535     0.8676        314        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.932      0.858       0.94      0.759

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     35/100      1.45G     0.9606     0.9407     0.8719        113        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.872      0.907      0.947      0.731

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     36/100      1.45G      1.002     0.9521     0.8702        374        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.945      0.855      0.938      0.832

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     37/100      1.45G     0.9703     0.9257     0.8655        220        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.945      0.857      0.952      0.802

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     38/100      1.45G       0.98     0.9198     0.8705        195        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.933       0.88       0.95      0.832

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     39/100      1.45G     0.9378     0.8724     0.8677        200        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.938      0.896      0.954      0.834

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     40/100      1.45G     0.9849     0.8968     0.8742        187        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.938      0.886      0.948      0.826

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     41/100      1.45G     0.9215     0.8653     0.8586        193        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.942      0.864      0.956      0.822

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     42/100      1.45G     0.9032     0.8579     0.8597        195        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.931      0.924      0.961      0.842

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     43/100      1.45G      0.946     0.9167     0.8661        350        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.958      0.899      0.958      0.848

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     44/100      1.45G     0.9285     0.8595     0.8667        332        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.922      0.933      0.959      0.848

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     45/100      1.45G     0.9067     0.8198     0.8626        232        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.896      0.973      0.966      0.827

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     46/100      1.45G     0.9026      0.817     0.8632        271        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.905      0.945      0.962      0.861

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     47/100      1.45G      0.906     0.8238     0.8615        181        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.912       0.94      0.957      0.816

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     48/100      1.45G     0.8967     0.8258     0.8626        207        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.909       0.97      0.963      0.849

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     49/100      1.45G     0.9264     0.8332     0.8671        252        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.956      0.916      0.967      0.858

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     50/100      1.45G     0.8777     0.7991      0.853        216        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.952      0.911      0.961      0.854
Closing dataloader mosaic
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     51/100      1.45G     0.7173      0.727     0.8258        113        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.958      0.915      0.966      0.823

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     52/100      1.45G     0.6903     0.6669     0.8262        111        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.927      0.942       0.97      0.837

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     53/100      1.45G     0.6989     0.6279     0.8287        117        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.959      0.914      0.966      0.821

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     54/100      1.45G     0.7098      0.645      0.831        106        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.947      0.927      0.973      0.824

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     55/100      1.45G      0.662      0.613      0.817        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.939      0.955      0.979      0.835
   Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     56/100      1.45G     0.7034     0.6162     0.8332        105        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.915      0.956      0.966      0.842

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     57/100      1.45G     0.6626     0.5911      0.821        112        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.924      0.966      0.979      0.867

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     58/100      1.45G     0.6776     0.5822     0.8235        108        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.917      0.957      0.973      0.871

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     59/100      1.45G     0.6696     0.5766     0.8304        112        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.924      0.964      0.975      0.847

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     60/100      1.45G     0.6418     0.5506     0.8205        113        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.921      0.977      0.983      0.874

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     61/100      1.45G     0.6585     0.5712     0.8172        124        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.915       0.98      0.972      0.867

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     62/100      1.45G     0.6257     0.5478     0.8212        115        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.91      0.986      0.972      0.864

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     63/100      1.45G     0.6017     0.5294     0.8145        105        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.91      0.985      0.969      0.866

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     64/100      1.45G      0.639     0.5525     0.8217        121        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.914       0.98       0.97       0.86

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     65/100      1.45G     0.6302     0.5235     0.8202        106        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.909      0.985      0.974      0.882

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     66/100      1.45G     0.6555     0.5374     0.8225        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.91      0.982      0.975      0.858

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     67/100      1.45G     0.6489     0.5316     0.8248        115        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.903      0.974       0.97      0.883

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     68/100      1.45G     0.6579     0.5283     0.8284        111        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.909      0.975      0.969      0.842

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     69/100      1.45G     0.6289     0.5151     0.8164        110        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.913      0.983      0.968       0.86
  Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     70/100      1.45G     0.6583     0.5223      0.824        118        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.919       0.99      0.979      0.852

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     71/100      1.45G     0.6095     0.5141     0.8179        108        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.914      0.987      0.972       0.87

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     72/100      1.45G     0.6206     0.5154      0.826        105        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.908      0.983      0.972      0.858

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     73/100      1.45G     0.6507     0.5198     0.8273        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.954      0.936      0.973      0.879

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     74/100      1.45G      0.678     0.5096     0.8287        104        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.91      0.989      0.969      0.875

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     75/100      1.45G     0.6204     0.5182     0.8191        104        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.914      0.991      0.973      0.884

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     76/100      1.45G     0.5803     0.4926     0.8154        107        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.912      0.988      0.969      0.883

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     77/100      1.45G     0.5993     0.4894     0.8092        116        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.912      0.989      0.973      0.882

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     78/100      1.45G     0.5726     0.4841     0.8112        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.908      0.989      0.973      0.882

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     79/100      1.45G     0.5829     0.4847      0.814         88        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.917      0.984      0.972      0.867

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     80/100      1.45G     0.6147     0.4853     0.8112        111        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.94      0.973      0.979      0.884

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     81/100      1.45G     0.6395     0.5063     0.8132        118        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.919      0.981      0.977      0.882

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     82/100      1.45G     0.5951     0.4777     0.8185        115        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.917      0.983      0.968      0.856

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     83/100      1.45G     0.5978     0.4831     0.8189        122        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.916      0.987      0.972      0.882

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     84/100      1.45G      0.612     0.4898     0.8199        111        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.919      0.985      0.968      0.874

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     85/100      1.45G     0.5701     0.4712     0.8081        112        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93       0.92      0.985      0.968      0.867

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     86/100      1.45G     0.5832     0.4805     0.8082        113        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.942      0.971      0.971      0.885

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     87/100      1.45G     0.5967     0.4832       0.82        110        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.939      0.982      0.971      0.885

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     88/100      1.45G      0.563     0.4625     0.8135        108        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.937      0.983      0.974      0.881

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     89/100      1.45G     0.5917     0.4828     0.8167        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.933       0.98      0.975      0.885

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     90/100      1.45G     0.5929     0.4721     0.8138        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.934      0.978      0.975       0.89

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     91/100      1.45G     0.5929     0.4817     0.8144         98        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.937      0.977      0.975      0.884

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     92/100      1.45G     0.5816     0.4816     0.8094        111        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.938      0.975      0.971      0.877

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     93/100      1.45G     0.5934     0.4812     0.8093        114        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.938       0.97       0.97      0.875

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     94/100      1.45G     0.5788     0.4608     0.8183        113        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.913      0.982      0.972       0.89

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     95/100      1.45G     0.5881     0.4657     0.8161        112        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.912      0.981      0.968      0.887

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     96/100      1.45G     0.5669      0.469     0.8085        126        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.912      0.981      0.968      0.881

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     97/100      1.45G     0.5522     0.4585     0.8092        108        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.915      0.979      0.972      0.888

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     98/100      1.45G      0.572     0.4654     0.8144        123        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.941      0.963      0.971      0.885

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     99/100      1.45G     0.5897     0.4713     0.8216        108        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.942      0.963      0.971      0.888

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
    100/100      1.45G     0.5535     0.4562     0.8114        116        416: 100%|██████████| 13/13 [0
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.943      0.961      0.971      0.885

100 epochs completed in 0.072 hours.
Optimizer stripped from chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\last.pt, 5.5MB
Optimizer stripped from chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\best.pt, 5.5MB

Validating chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\best.pt...
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|█████████
                   all          5         93      0.953      0.927      0.971      0.852
            white_pawn          5         22      0.993          1      0.995      0.844
          white_knight          5          5          1       0.86      0.995      0.882
          white_bishop          5          6      0.983      0.833      0.972      0.861
            white_rook          5          9      0.986          1      0.995      0.855
            white_king          5          5      0.927          1      0.995      0.811
            black_pawn          5         22      0.936      0.955      0.933      0.776
          black_knight          5          5          1      0.824      0.995        0.9
          black_bishop          4          5      0.723        0.8       0.84      0.811
            black_rook          5          9      0.986          1      0.995      0.876
            black_king          5          5          1          1      0.995      0.905
Speed: 0.2ms preprocess, 176.7ms inference, 0.0ms loss, 2.2ms postprocess per image
Results saved to chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541
Creating ensemble from 3 checkpoints...
Ensemble model saved to chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu\ensemble_model_20250521_091541.pt
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CPU (12th Gen Intel Core(TM) i5-12450HX)
 ProTip: Export to OpenVINO format for best performance on Intel CPUs. Learn more at https://docs.ultralytics.com/integrations/openvino/
YOLO11n summary (fused): 100 layers, 2,584,492 parameters, 0 gradients, 6.3 GFLOPs

PyTorch: starting from 'chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\best.pt' with input shape (1, 3, 416, 416) BCHW and output shape(s) (1, 16, 3549) (5.2 MB)
requirements: Ultralytics requirements ['onnx>=1.12.0,<1.18.0', 'onnxslim>=0.1.53', 'onnxruntime-gpu'] not found, attempting AutoUpdate...
[notice] A new release of pip is available: 25.0.1 -> 25.1.1
[notice] To update, run: python.exe -m pip install --upgrade pip
Collecting onnx<1.18.0,>=1.12.0
  Downloading onnx-1.17.0-cp311-cp311-win_amd64.whl.metadata (16 kB)
Collecting onnxslim>=0.1.53
  Downloading onnxslim-0.1.53-py3-none-any.whl.metadata (5.0 kB)
Collecting onnxruntime-gpu
  Downloading onnxruntime_gpu-1.22.0-cp311-cp311-win_amd64.whl.metadata (5.1 kB)
Requirement already satisfied: numpy>=1.20 in e:\new folder\lib\site-packages (from onnx<1.18.0,>=1.12.0) (2.0.2)
Requirement already satisfied: protobuf>=3.20.2 in e:\new folder\lib\site-packages (from onnx<1.18.0,>=1.12.0) (5.29.0)
Requirement already satisfied: sympy in e:\new folder\lib\site-packages (from onnxslim>=0.1.53) (1.13.1) 
Requirement already satisfied: packaging in e:\new folder\lib\site-packages (from onnxslim>=0.1.53) (24.2)
Requirement already satisfied: coloredlogs in e:\new folder\lib\site-packages (from onnxruntime-gpu) (15.0.1)
Requirement already satisfied: flatbuffers in e:\new folder\lib\site-packages (from onnxruntime-gpu) (24.3.25)
Requirement already satisfied: humanfriendly>=9.1 in e:\new folder\lib\site-packages (from coloredlogs->onnxruntime-gpu) (10.0)
Requirement already satisfied: mpmath<1.4,>=1.1.0 in e:\new folder\lib\site-packages (from sympy->onnxslim>=0.1.53) (1.3.0)
Requirement already satisfied: pyreadline3 in e:\new folder\lib\site-packages (from humanfriendly>=9.1->coloredlogs->onnxruntime-gpu) (3.5.4)
Downloading onnx-1.17.0-cp311-cp311-win_amd64.whl (14.5 MB)
   ---------------------------------------- 14.5/14.5 MB 776.4 kB/s eta 0:00:00
Downloading onnxslim-0.1.53-py3-none-any.whl (146 kB)
Downloading onnxruntime_gpu-1.22.0-cp311-cp311-win_amd64.whl (214.9 MB)
   ---------------------------------------- 214.9/214.9 MB 1.1 MB/s eta 0:00:00
Installing collected packages: onnx, onnxslim, onnxruntime-gpu
Successfully installed onnx-1.17.0 onnxruntime-gpu-1.22.0 onnxslim-0.1.53

requirements: AutoUpdate success  245.6s
WARNING requirements: Restart runtime or rerun command for updates to take effect


ONNX: starting export with onnx 1.17.0 opset 19...
ONNX: slimming with onnxslim 0.1.53...
ONNX: export success  250.3s, saved as 'chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\best.onnx' (10.0 MB)

Export complete (250.5s)
Results saved to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights
Predict:         yolo predict task=detect model=chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\best.onnx imgsz=416
Validate:        yolo val task=detect model=chess_board_detection\piece_detection\models\yolo11n_416x416_enhanced_gpu\chess_pieces_20250521_091541\weights\best.onnx imgsz=416 data=chess_board_detection/piece_detection/augmented_dataset_416x416_enhanced/dataset.yaml
Visualize:       https://netron.app
Training complete. Model saved to chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1>                   