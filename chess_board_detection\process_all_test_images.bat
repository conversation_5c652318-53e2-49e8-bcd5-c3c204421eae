@echo off
REM Process all test images with improved visualization

set MODEL_PATH=chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
set OUTPUT_DIR=chess_board_detection/outputs/piece_detection

if "%~1"=="" (
    echo Usage: process_all_test_images.bat [input_directory]
    exit /b 1
)

echo Processing all images in "%~1" with improved visualization...

for %%f in ("%~1\*.jpg") do (
    echo Processing "%%f"...
    python chess_board_detection/run_piece_detection.py --model "%MODEL_PATH%" --input "%%f" --output_dir "%OUTPUT_DIR%" --font_size 0.4 --line_width 1 --conf 0.7
)

echo All images processed. Results saved to %OUTPUT_DIR%
