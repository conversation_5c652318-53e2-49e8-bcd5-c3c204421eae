{"logs": [{"outputFile": "com.chessvision.app-mergeDebugResources-51:/values-v23/values-v23.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bca7edd7dea9a9293306bd31b1a5bbbe\\transformed\\appcompat-1.1.0\\res\\values-v23\\values-v23.xml", "from": {"startLines": "2,3,4,5,6,20,34,35,36,37,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1371,2267,2386,2513,2618,2842,2957,3064,3177", "endLines": "2,3,4,5,19,33,34,35,36,40,41,42,43,47", "endColumns": "134,134,74,86,12,12,118,126,104,12,114,106,112,12", "endOffsets": "185,320,395,482,1366,2262,2381,2508,2613,2837,2952,3059,3172,3402"}, "to": {"startLines": "2,3,4,5,6,20,34,35,36,37,41,42,43,44", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,190,325,400,487,1225,1975,2094,2221,2326,2550,2665,2772,2885", "endLines": "2,3,4,5,19,33,34,35,36,40,41,42,43,47", "endColumns": "134,134,74,86,12,12,118,126,104,12,114,106,112,12", "endOffsets": "185,320,395,482,1220,1970,2089,2216,2321,2545,2660,2767,2880,3110"}}]}]}