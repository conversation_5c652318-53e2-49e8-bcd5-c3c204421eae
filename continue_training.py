"""
Continue Training Script for Chess Piece Detection

This script continues training from the best model (epoch 86) with a focus on reducing
classification loss to zero while maintaining high detection performance.

Features:
- Loads the best model from epoch 86
- Uses dynamic weight adjustment to focus on classification loss
- Implements memory optimization to prevent crashes
- Saves models in a new directory
"""

import os
import sys
import yaml
import torch
import gc
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from pathlib import Path
from ultralytics import YOLO

# Configuration
CONFIG = {
    # Paths
    "base_model_path": "runs/detect/train/weights/best.pt",  # Best model from epoch 86
    "dataset_yaml": "chess_board_detection/piece_detection/targeted_dataset/dataset.yaml",
    "output_dir": "runs/detect/continue_training",

    # Training parameters
    "start_epoch": 91,  # Continue from epoch 91 (after 90)
    "epochs": 15,       # Train for 15 more epochs
    "batch_size": 16,   # Same batch size as original training
    "img_size": 416,    # Same image size as original training
    "patience": 100,    # High patience to allow training to continue

    # Loss weights
    "initial_cls_weight": 10.0,  # Start with high classification weight
    "initial_box_weight": 0.5,   # Reduced box weight to prioritize classification
    "initial_dfl_weight": 0.5,   # Reduced DFL weight to prioritize classification

    # Targets
    "target_cls_loss": 0.01,     # Target classification loss near zero
    "target_precision": 0.99,    # Target precision
    "target_recall": 0.99,       # Target recall

    # Memory optimization
    "cache": False,              # Disable cache to reduce memory usage
    "workers": 4,                # Reduce number of workers
    "amp": True,                 # Use mixed precision to reduce memory usage

    # Dynamic weight adjustment
    "dynamic_weights": True,     # Enable dynamic weight adjustment
    "weight_adjustment_epochs": 3, # Adjust weights every 3 epochs
    "max_cls_weight": 50.0,      # Maximum classification weight
    "min_box_weight": 0.1,       # Minimum box weight
    "min_dfl_weight": 0.1,       # Minimum DFL weight
}

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def optimize_memory():
    """Optimize memory usage to prevent crashes."""
    # Clear CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # Run garbage collection
    gc.collect()

    # Print memory usage
    if torch.cuda.is_available():
        print(f"GPU Memory allocated: {torch.cuda.memory_allocated() / 1e9:.2f} GB")
        print(f"GPU Memory reserved: {torch.cuda.memory_reserved() / 1e9:.2f} GB")

def continue_training():
    """Continue training from the best model with focus on classification loss."""
    # Set random seed
    set_seed(42)

    # Print system information
    print_system_info()

    # Create output directory
    os.makedirs(CONFIG["output_dir"], exist_ok=True)

    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f'continue_training_{timestamp}'
    run_dir = os.path.join(CONFIG["output_dir"], run_name)
    os.makedirs(run_dir, exist_ok=True)

    # Save configuration
    with open(os.path.join(run_dir, 'config.yaml'), 'w') as f:
        yaml.dump(CONFIG, f, sort_keys=False)

    # Load model
    print(f"Loading model from {CONFIG['base_model_path']}...")
    model = YOLO(CONFIG["base_model_path"])

    # Optimize memory before training
    optimize_memory()

    # Current weights
    current_cls_weight = CONFIG["initial_cls_weight"]
    current_box_weight = CONFIG["initial_box_weight"]
    current_dfl_weight = CONFIG["initial_dfl_weight"]

    # Training history
    history = {
        "epochs": [],
        "cls_loss": [],
        "box_loss": [],
        "dfl_loss": [],
        "precision": [],
        "recall": [],
        "map50": [],
        "map": [],
        "cls_weight": [],
        "box_weight": [],
        "dfl_weight": []
    }

    # Train with dynamic weight adjustment
    if CONFIG["dynamic_weights"]:
        print("\n=== Dynamic Weight Adjustment Enabled ===")
        print(f"Target Classification Loss: {CONFIG['target_cls_loss']}")
        print(f"Target Precision: {CONFIG['target_precision']}")
        print(f"Target Recall: {CONFIG['target_recall']}")

        # Initial weights
        print(f"\nInitial weights:")
        print(f"Classification Weight: {current_cls_weight}")
        print(f"Box Weight: {current_box_weight}")
        print(f"DFL Weight: {current_dfl_weight}")

        # Train for specified number of epochs
        for epoch in range(CONFIG["start_epoch"], CONFIG["start_epoch"] + CONFIG["epochs"]):
            print(f"\n=== Epoch {epoch}/{CONFIG['start_epoch'] + CONFIG['epochs'] - 1} ===")

            # Train for one epoch
            results = model.train(
                data=CONFIG["dataset_yaml"],
                epochs=1,
                imgsz=CONFIG["img_size"],
                batch=CONFIG["batch_size"],
                patience=CONFIG["patience"],
                device=0,
                workers=CONFIG["workers"],
                project=CONFIG["output_dir"],
                name=f"{run_name}_epoch{epoch}",
                exist_ok=True,
                pretrained=False,
                verbose=True,
                seed=42,
                cache=CONFIG["cache"],
                close_mosaic=10,
                amp=CONFIG["amp"],
                # Minimal augmentation for fine-tuning
                augment=True,
                mosaic=0.0,
                mixup=0.0,
                degrees=0.0,
                translate=0.05,
                scale=0.1,
                shear=0.0,
                fliplr=0.5,
                perspective=0.0,
                # Learning rate settings
                lr0=0.0001,
                lrf=0.00001,
                # Loss weights
                box=current_box_weight,
                cls=current_cls_weight,
                dfl=current_dfl_weight,
                # Validation settings
                val=True,
                # NMS settings
                conf=0.001,
                iou=0.7
            )

            # Optimize memory after training
            optimize_memory()

            # Extract metrics
            try:
                # Get the last values from training results
                train_stats = results.results_dict
                cls_loss = None
                box_loss = None
                dfl_loss = None

                for key in train_stats:
                    if 'loss_cls' in key or 'cls_loss' in key:
                        cls_loss = float(train_stats[key][-1])
                    elif 'loss_box' in key or 'box_loss' in key:
                        box_loss = float(train_stats[key][-1])
                    elif 'loss_dfl' in key or 'dfl_loss' in key:
                        dfl_loss = float(train_stats[key][-1])

                # Get validation results
                val_results = results.validator.metrics
                precision = float(val_results.get('precision', 0))
                recall = float(val_results.get('recall', 0))
                map50 = float(val_results.get('map50', 0))
                map = float(val_results.get('map', 0))

                # Update history
                history["epochs"].append(epoch)
                history["cls_loss"].append(cls_loss)
                history["box_loss"].append(box_loss)
                history["dfl_loss"].append(dfl_loss)
                history["precision"].append(precision)
                history["recall"].append(recall)
                history["map50"].append(map50)
                history["map"].append(map)
                history["cls_weight"].append(current_cls_weight)
                history["box_weight"].append(current_box_weight)
                history["dfl_weight"].append(current_dfl_weight)

                # Print metrics
                print("\nTraining Metrics:")
                print(f"Classification Loss: {cls_loss:.6f}")
                print(f"Box Loss: {box_loss:.6f}")
                print(f"DFL Loss: {dfl_loss:.6f}")
                print("\nValidation Metrics:")
                print(f"Precision: {precision:.4f}")
                print(f"Recall: {recall:.4f}")
                print(f"mAP50: {map50:.4f}")
                print(f"mAP50-95: {map:.4f}")

                # Check if we've reached our targets
                if cls_loss <= CONFIG["target_cls_loss"] and precision >= CONFIG["target_precision"] and recall >= CONFIG["target_recall"]:
                    print(f"\n🎉 Target metrics achieved! Classification Loss: {cls_loss:.6f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
                    break

                # Adjust weights every few epochs
                if (epoch - CONFIG["start_epoch"]) % CONFIG["weight_adjustment_epochs"] == 0 and epoch < CONFIG["start_epoch"] + CONFIG["epochs"] - 1:
                    # Dynamic weight adjustment strategy focused on classification loss
                    if cls_loss > 0.1:  # High classification loss
                        # Significant increase in classification weight
                        current_cls_weight = min(current_cls_weight * 2.0, CONFIG["max_cls_weight"])
                        # Keep other weights the same to maintain detection performance
                        current_box_weight = current_box_weight
                        current_dfl_weight = current_dfl_weight
                    elif cls_loss > 0.05:  # Medium classification loss
                        # Moderate increase in classification weight
                        current_cls_weight = min(current_cls_weight * 1.5, CONFIG["max_cls_weight"])
                        # Slight reduction in other weights
                        current_box_weight = max(current_box_weight * 0.9, CONFIG["min_box_weight"])
                        current_dfl_weight = max(current_dfl_weight * 0.9, CONFIG["min_dfl_weight"])
                    elif cls_loss > 0.01:  # Low classification loss
                        # Small increase in classification weight
                        current_cls_weight = min(current_cls_weight * 1.2, CONFIG["max_cls_weight"])
                        # Minimal reduction in other weights
                        current_box_weight = max(current_box_weight * 0.95, CONFIG["min_box_weight"])
                        current_dfl_weight = max(current_dfl_weight * 0.95, CONFIG["min_dfl_weight"])
                    else:  # Very low classification loss
                        # Maintain classification weight
                        current_cls_weight = current_cls_weight
                        # Slightly increase other weights to improve detection
                        current_box_weight = min(current_box_weight * 1.05, 1.0)
                        current_dfl_weight = min(current_dfl_weight * 1.05, 1.0)

                    # Check if precision or recall has degraded
                    if len(history["precision"]) > 1 and (precision < history["precision"][-2] * 0.98 or recall < history["recall"][-2] * 0.98):
                        print("Warning: Precision or recall has degraded. Adjusting weights to compensate.")
                        # Increase box and dfl weights to improve detection
                        current_box_weight = min(current_box_weight * 1.2, 1.0)
                        current_dfl_weight = min(current_dfl_weight * 1.2, 1.0)
                        # Still maintain high classification weight
                        current_cls_weight = max(current_cls_weight * 0.95, 5.0)

                    print(f"\nAdjusted weights for next epoch:")
                    print(f"Classification Weight: {current_cls_weight}")
                    print(f"Box Weight: {current_box_weight}")
                    print(f"DFL Weight: {current_dfl_weight}")

            except Exception as e:
                print(f"Error extracting metrics: {e}")
                continue

        # Save training history
        with open(os.path.join(run_dir, 'training_history.yaml'), 'w') as f:
            yaml.dump(history, f, sort_keys=False)

        # Create visualizations
        create_visualizations(history, run_dir)

        print(f"\nTraining complete. Results saved to {run_dir}")

    else:
        # Standard training without dynamic weight adjustment
        print("\n=== Standard Training (No Dynamic Weight Adjustment) ===")

        # Train for remaining epochs
        results = model.train(
            data=CONFIG["dataset_yaml"],
            epochs=CONFIG["epochs"],
            imgsz=CONFIG["img_size"],
            batch=CONFIG["batch_size"],
            patience=CONFIG["patience"],
            device=0,
            workers=CONFIG["workers"],
            project=CONFIG["output_dir"],
            name=run_name,
            exist_ok=True,
            pretrained=False,
            verbose=True,
            seed=42,
            cache=CONFIG["cache"],
            close_mosaic=10,
            amp=CONFIG["amp"],
            # Minimal augmentation for fine-tuning
            augment=True,
            mosaic=0.0,
            mixup=0.0,
            degrees=0.0,
            translate=0.05,
            scale=0.1,
            shear=0.0,
            fliplr=0.5,
            perspective=0.0,
            # Learning rate settings
            lr0=0.0001,
            lrf=0.00001,
            # Loss weights
            box=CONFIG["initial_box_weight"],
            cls=CONFIG["initial_cls_weight"],
            dfl=CONFIG["initial_dfl_weight"],
            # Validation settings
            val=True,
            # NMS settings
            conf=0.001,
            iou=0.7
        )

        print(f"\nTraining complete. Results saved to {run_dir}")

def create_visualizations(history, output_dir):
    """Create visualizations of training history."""
    # Create directory for visualizations
    vis_dir = os.path.join(output_dir, 'visualizations')
    os.makedirs(vis_dir, exist_ok=True)

    # Plot loss values
    plt.figure(figsize=(12, 8))
    plt.plot(history["epochs"], history["cls_loss"], 'r-o', label='Classification Loss')
    plt.plot(history["epochs"], history["box_loss"], 'g-o', label='Box Loss')
    plt.plot(history["epochs"], history["dfl_loss"], 'b-o', label='DFL Loss')
    plt.axhline(y=CONFIG["target_cls_loss"], color='r', linestyle='--', label=f'Target Cls Loss ({CONFIG["target_cls_loss"]})')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Training Losses')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'losses.png'))
    plt.close()

    # Plot metrics
    plt.figure(figsize=(12, 8))
    plt.plot(history["epochs"], history["precision"], 'm-o', label='Precision')
    plt.plot(history["epochs"], history["recall"], 'c-o', label='Recall')
    plt.plot(history["epochs"], history["map50"], 'y-o', label='mAP50')
    plt.plot(history["epochs"], history["map"], 'k-o', label='mAP50-95')
    plt.axhline(y=CONFIG["target_precision"], color='m', linestyle='--', label=f'Target Precision ({CONFIG["target_precision"]})')
    plt.axhline(y=CONFIG["target_recall"], color='c', linestyle='--', label=f'Target Recall ({CONFIG["target_recall"]})')
    plt.xlabel('Epoch')
    plt.ylabel('Metric Value')
    plt.title('Validation Metrics')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'metrics.png'))
    plt.close()

    # Plot weights
    plt.figure(figsize=(12, 8))
    plt.plot(history["epochs"], history["cls_weight"], 'r-o', label='Classification Weight')
    plt.plot(history["epochs"], history["box_weight"], 'g-o', label='Box Weight')
    plt.plot(history["epochs"], history["dfl_weight"], 'b-o', label='DFL Weight')
    plt.xlabel('Epoch')
    plt.ylabel('Weight Value')
    plt.title('Loss Weights')
    plt.legend()
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig(os.path.join(vis_dir, 'weights.png'))
    plt.close()

if __name__ == "__main__":
    continue_training()
