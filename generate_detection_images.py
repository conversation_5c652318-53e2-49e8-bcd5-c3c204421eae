import sys
import os
import cv2
import numpy as np
from ultralytics import YOLO
import argparse

# Class names
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

# Define colors for each class (BGR format for OpenCV)
COLORS = {
    'white_pawn': (255, 255, 0),     # <PERSON>an
    'white_knight': (255, 0, 255),   # Magenta
    'white_bishop': (0, 255, 255),   # Yellow
    'white_rook': (0, 0, 255),       # Red
    'white_queen': (255, 0, 0),      # Blue
    'white_king': (0, 255, 0),       # <PERSON>
    'black_pawn': (128, 255, 0),     # Light Cyan
    'black_knight': (255, 128, 255), # Light Magenta
    'black_bishop': (128, 255, 255), # Light Yellow
    'black_rook': (128, 128, 255),   # Light Red
    'black_queen': (255, 128, 128),  # Light Blue
    'black_king': (128, 255, 128),   # Light Green
}

def run_inference(model_path, image_path, output_dir):
    """Run inference on an image and save the results"""
    # Load model
    model = YOLO(model_path)
    
    # Run inference
    results = model.predict(image_path, conf=0.25)[0]
    
    # Get the original image
    img = cv2.imread(image_path)
    
    # Get detection data
    boxes = results.boxes.xyxy.cpu().numpy()
    cls_ids = results.boxes.cls.cpu().numpy().astype(int)
    confs = results.boxes.conf.cpu().numpy()
    
    # Create a copy of the image for drawing
    img_with_boxes = img.copy()
    
    # Draw bounding boxes and labels
    for i, (box, cls_id, conf) in enumerate(zip(boxes, cls_ids, confs)):
        x1, y1, x2, y2 = box.astype(int)
        color = COLORS[CLASS_NAMES[cls_id]]
        
        # Draw box
        cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), color, 2)
        
        # Add label with detection number
        label = f"{i+1}: {CLASS_NAMES[cls_id]} ({conf:.2f})"
        cv2.putText(img_with_boxes, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    # Save the image with detections
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.basename(image_path)
    output_path = os.path.join(output_dir, f"detection_{base_name}")
    cv2.imwrite(output_path, img_with_boxes)
    
    # Create a text file with detection information
    txt_path = os.path.join(output_dir, f"detection_{os.path.splitext(base_name)[0]}.txt")
    with open(txt_path, 'w') as f:
        f.write(f"Detections for {base_name}:\n\n")
        for i, (box, cls_id, conf) in enumerate(zip(boxes, cls_ids, confs)):
            x1, y1, x2, y2 = box.astype(int)
            f.write(f"{i+1}. {CLASS_NAMES[cls_id]} (confidence: {conf:.2f}) at position ({x1}, {y1}, {x2}, {y2})\n")
    
    print(f"Processed {base_name}")
    print(f"Detection image saved to: {output_path}")
    print(f"Detection info saved to: {txt_path}")
    
    return output_path, txt_path

def main():
    parser = argparse.ArgumentParser(description="Generate detection images for feedback")
    parser.add_argument("--model", required=True, help="Path to the YOLO model")
    parser.add_argument("--image-dir", required=True, help="Directory containing images to process")
    parser.add_argument("--output-dir", default="detection_images", help="Directory to save output images")
    
    args = parser.parse_args()
    
    # Get all images in the directory
    image_files = [os.path.join(args.image_dir, f) for f in os.listdir(args.image_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    for image_file in image_files:
        run_inference(args.model, image_file, args.output_dir)

if __name__ == "__main__":
    main()
