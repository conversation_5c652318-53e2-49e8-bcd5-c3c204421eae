"""
Test Best Models Script for Chess Piece Detection

This script tests the best models from our training on the test images,
using only the highest confidence detection for each piece and eliminating duplicates.
"""

import os
import sys
import cv2
import numpy as np
import torch
import matplotlib.pyplot as plt
from pathlib import Path
from ultralytics import YOLO
from ultralytics.utils.plotting import Annotator, colors

# Configuration
CONFIG = {
    # Models to test
    "models": [
        {"path": "runs/detect/simple_continue/continue_epoch107/weights/best.pt", "name": "Epoch 107 (Best mAP)"},
        {"path": "runs/detect/simple_continue/continue_epoch110/weights/best.pt", "name": "Epoch 110 (Best Balance)"},
        {"path": "runs/detect/simple_continue/continue_epoch111/weights/best.pt", "name": "Epoch 111 (Best Loss)"},
    ],

    # Test images directory
    "test_dir": r"C:\Users\<USER>\OneDrive\Desktop\New folder (4)",

    # Output directory for visualizations
    "output_dir": "test_results",

    # Detection parameters
    "conf_threshold": 0.25,  # Confidence threshold for detections
    "iou_threshold": 0.7,    # IoU threshold for NMS
    "img_size": 416,         # Image size for inference

    # Class names
    "class_names": [
        "white_pawn", "white_knight", "white_bishop", "white_rook",
        "white_queen", "white_king", "black_pawn", "black_knight",
        "black_bishop", "black_rook", "black_queen", "black_king"
    ]
}

def create_color_legend(class_names):
    """Create a color legend for the class names."""
    legend_img = np.ones((len(class_names) * 30 + 10, 300, 3), dtype=np.uint8) * 255

    for i, name in enumerate(class_names):
        color = colors(i)
        cv2.rectangle(legend_img, (5, i * 30 + 5), (25, i * 30 + 25), color, -1)
        cv2.putText(legend_img, name, (35, i * 30 + 20),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)

    return legend_img

def remove_duplicate_detections(boxes, scores, class_ids, iou_threshold=0.5):
    """
    Remove duplicate detections by keeping only the highest confidence detection
    for each location based on IoU.
    """
    if len(boxes) == 0:
        return boxes, scores, class_ids

    # Sort by confidence
    indices = np.argsort(scores)[::-1]
    boxes = boxes[indices]
    scores = scores[indices]
    class_ids = class_ids[indices]

    # Initialize list of picked indices
    keep = []

    # Loop through boxes
    while len(boxes) > 0:
        # Pick the box with highest confidence
        keep.append(0)

        # Compute IoU of the picked box with the rest
        if len(boxes) == 1:
            break

        ious = compute_iou(boxes[0], boxes[1:])

        # Remove boxes with IoU over threshold
        mask = ious <= iou_threshold
        boxes = np.concatenate(([boxes[0]], boxes[1:][mask]))
        scores = np.concatenate(([scores[0]], scores[1:][mask]))
        class_ids = np.concatenate(([class_ids[0]], class_ids[1:][mask]))

    return boxes, scores, class_ids

def compute_iou(box, boxes):
    """Compute IoU between a box and an array of boxes."""
    # Box coordinates
    x1, y1, x2, y2 = box

    # Boxes coordinates
    x1s, y1s, x2s, y2s = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]

    # Intersection coordinates
    inter_x1 = np.maximum(x1, x1s)
    inter_y1 = np.maximum(y1, y1s)
    inter_x2 = np.minimum(x2, x2s)
    inter_y2 = np.minimum(y2, y2s)

    # Intersection area
    inter_area = np.maximum(0, inter_x2 - inter_x1) * np.maximum(0, inter_y2 - inter_y1)

    # Box areas
    box_area = (x2 - x1) * (y2 - y1)
    boxes_area = (x2s - x1s) * (y2s - y1s)

    # IoU
    iou = inter_area / (box_area + boxes_area - inter_area)

    return iou

def test_model(model_path, test_dir, output_dir, model_name):
    """Test a model on the test images."""
    print(f"\nTesting model: {model_name}")
    print(f"Model path: {model_path}")

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Load model
    model = YOLO(model_path)

    # Get test images
    test_images = [os.path.join(test_dir, f) for f in os.listdir(test_dir)
                  if f.lower().endswith(('.png', '.jpg', '.jpeg'))]

    print(f"Found {len(test_images)} test images")

    # Create color legend
    legend_img = create_color_legend(CONFIG["class_names"])
    cv2.imwrite(os.path.join(output_dir, "legend.png"), legend_img)

    # Process each image
    for i, img_path in enumerate(test_images):
        print(f"Processing image {i+1}/{len(test_images)}: {os.path.basename(img_path)}")

        # Read image
        img = cv2.imread(img_path)
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)

        # Run inference
        results = model.predict(
            img_rgb,
            conf=CONFIG["conf_threshold"],
            iou=CONFIG["iou_threshold"],
            imgsz=CONFIG["img_size"],
            verbose=False
        )[0]

        # Get detections
        boxes = results.boxes.xyxy.cpu().numpy()
        scores = results.boxes.conf.cpu().numpy()
        class_ids = results.boxes.cls.cpu().numpy().astype(int)

        # Remove duplicate detections
        if len(boxes) > 0:
            boxes, scores, class_ids = remove_duplicate_detections(
                boxes, scores, class_ids, CONFIG["iou_threshold"]
            )

        # Create output image
        output_img = img.copy()
        annotator = Annotator(output_img)

        # Draw detections
        for j, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
            # Get class name
            class_name = CONFIG["class_names"][class_id]

            # Draw box with class color
            color = colors(class_id)
            annotator.box_label(box, f"{j+1}", color=color)

        # Save output image
        output_path = os.path.join(output_dir, f"{os.path.splitext(os.path.basename(img_path))[0]}_{model_name.replace(' ', '_')}.jpg")
        cv2.imwrite(output_path, output_img)

        # Print detection details
        print(f"  Detections: {len(boxes)}")
        for j, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
            class_name = CONFIG["class_names"][class_id]
            print(f"    {j+1}. {class_name}: {score:.4f}")

    print(f"Results saved to {output_dir}")

def main():
    """Main function."""
    print("Chess Piece Detection - Test Best Models")
    print("=" * 50)

    # Create output directory
    os.makedirs(CONFIG["output_dir"], exist_ok=True)

    # Test each model
    for model_info in CONFIG["models"]:
        model_output_dir = os.path.join(CONFIG["output_dir"], model_info["name"].replace(" ", "_"))
        test_model(
            model_info["path"],
            CONFIG["test_dir"],
            model_output_dir,
            model_info["name"]
        )

    print("\nTesting completed!")
    print(f"Results saved to {CONFIG['output_dir']}")

if __name__ == "__main__":
    main()
