plugins {
    id("com.android.application")
    id("org.jetbrains.kotlin.android")
    // Temporarily disable kapt and hilt for initial build
    // id("kotlin-kapt")
    // id("dagger.hilt.android.plugin")
    id("kotlinx-serialization")
    // Temporarily disable Python plugin for initial build
    // id("com.chaquo.python") version "15.0.1"
}

android {
    namespace = "com.chessvision.app"
    compileSdk = 34

    defaultConfig {
        applicationId = "com.chessvision.app"
        minSdk = 24
        targetSdk = 34
        versionCode = 2  // Increment to replace original app
        versionName = "1.0"

        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"

        // NDK configuration - Optimized for modern devices
        ndk {
            // Only support 64-bit ARM (95%+ of modern Android devices)
            abiFilters += listOf("arm64-v8a")  // Saves ~11.5 MB by excluding other architectures
        }

        // externalNativeBuild {
        //     cmake {
        //         cppFlags += "-std=c++17"
        //         arguments += listOf(
        //             "-DANDROID_STL=c++_shared",
        //             "-DANDROID_PLATFORM=android-24"
        //         )
        //     }
        // }

        // Python configuration for FEN detection (temporarily disabled)
        // python {
        //     buildPython("python")
        //     pip {
        //         install("torch")
        //         install("torchvision")
        //         install("opencv-python")
        //         install("numpy")
        //         install("ultralytics")
        //     }
        // }
    }

    buildTypes {
        release {
            // 🏆 WORLD-CLASS: Enterprise-grade optimizations
            isMinifyEnabled = true  // Enable code shrinking and obfuscation
            isShrinkResources = true  // Remove unused resources
            isDebuggable = false  // Disable debugging for maximum performance

            // 🏆 CRITICAL: Use optimized ProGuard configuration
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )

            // 🏆 ENTERPRISE-GRADE: Performance optimizations
            ndk {
                debugSymbolLevel = "NONE"  // Remove debug symbols for smaller size
            }
        }
        debug {
            // 🏆 WORLD-CLASS: Fast debug builds with minimal overhead
            isMinifyEnabled = false  // Keep debug builds fast
            isShrinkResources = false
            isDebuggable = true

            // Enable compose compiler metrics for debugging
            buildConfigField("boolean", "COMPOSE_METRICS", "true")
        }

        // Unoptimized build for comparison
        create("unoptimized") {
            initWith(getByName("debug"))
            isMinifyEnabled = false
            isShrinkResources = false
            applicationIdSuffix = ".unoptimized"
            versionNameSuffix = "-unoptimized"
        }

        // Optimized release with debug signing for testing
        create("optimizedDebug") {
            initWith(getByName("release"))
            signingConfig = signingConfigs.getByName("debug")
            applicationIdSuffix = ".optimized"
            versionNameSuffix = "-optimized"
            isDebuggable = true
        }

        // Optimized version that replaces the original
        create("optimizedReplace") {
            initWith(getByName("release"))
            signingConfig = signingConfigs.getByName("debug")
            // No applicationIdSuffix - same package name as original
            versionNameSuffix = "-optimized"
            isDebuggable = true
        }
    }



    buildFeatures {
        compose = true
        viewBinding = true
        buildConfig = true  // Enable BuildConfig generation
    }

    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.3"
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = "1.8"

        // 🏆 WORLD-CLASS: Enterprise-grade Kotlin compiler optimizations
        freeCompilerArgs += listOf(
            "-Xsuppress-version-warnings",
            "-opt-in=kotlin.RequiresOptIn",
            "-opt-in=kotlinx.coroutines.ExperimentalCoroutinesApi",
            "-opt-in=androidx.compose.material3.ExperimentalMaterial3Api",
            "-opt-in=androidx.compose.foundation.ExperimentalFoundationApi",
            "-opt-in=androidx.camera.core.ExperimentalGetImage",

            // 🏆 CRITICAL: Performance optimizations
            "-Xjvm-default=all",  // Enable default methods for better performance
            "-Xassertions=jvm",   // Optimize assertions

            // 🏆 WORLD-CLASS: Compose optimizations
            "-P",
            "plugin:androidx.compose.compiler.plugins.kotlin:suppressKotlinVersionCompatibilityCheck=1.9.10",
            "-P",
            "plugin:androidx.compose.compiler.plugins.kotlin:reportsDestination=${layout.buildDirectory.get()}/compose_compiler"
        )
    }



    packaging {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
            // Exclude unnecessary files to reduce APK size
            excludes += "/META-INF/DEPENDENCIES"
            excludes += "/META-INF/LICENSE"
            excludes += "/META-INF/LICENSE.txt"
            excludes += "/META-INF/NOTICE"
            excludes += "/META-INF/NOTICE.txt"
            excludes += "**/*.kotlin_metadata"
            excludes += "**/*.version"
            excludes += "**/kotlin/**"
            excludes += "**/*.properties"
        }
    }
}

dependencies {
    // Core Android - Essential
    implementation("androidx.core:core-ktx:1.12.0")
    implementation("androidx.lifecycle:lifecycle-runtime-ktx:2.7.0")
    implementation("androidx.activity:activity-compose:1.8.2")

    // Compose BOM - Essential for UI
    implementation(platform("androidx.compose:compose-bom:2024.02.00"))
    implementation("androidx.compose.ui:ui")
    implementation("androidx.compose.ui:ui-graphics")
    implementation("androidx.compose.ui:ui-tooling-preview")
    implementation("androidx.compose.material3:material3")
    implementation("androidx.compose.material:material-icons-extended")

    // Camera - Essential for chess board capture
    implementation("androidx.camera:camera-core:1.3.1")
    implementation("androidx.camera:camera-camera2:1.3.1")
    implementation("androidx.camera:camera-lifecycle:1.3.1")
    implementation("androidx.camera:camera-view:1.3.1")

    // Permissions - Essential for camera access
    implementation("com.google.accompanist:accompanist-permissions:0.32.0")

    // ONNX Runtime - For using actual trained models (EXACT same as Python)
    implementation("com.microsoft.onnxruntime:onnxruntime-android:1.16.3")

    // Testing - Minimal
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
    debugImplementation("androidx.compose.ui:ui-tooling")
}
