@echo off
echo Training simple YOLO model for chess piece detection...
echo Max epochs: 100 (no phase system)
echo Target metrics: mAP50=0.99, Precision=0.99, Recall=0.99
echo After 100 epochs, you will be asked if you want to continue for 10 more epochs

cd chess_board_detection
python train_simple_yolo.py --model "../yolo11n.pt" --dataset "piece_detection/enhanced_dataset_99plus/dataset.yaml" --epochs 100 --patience 20 --target-map50 0.99 --target-precision 0.99 --target-recall 0.99
cd ..
pause
