"""
<PERSON><PERSON><PERSON> to run the enhanced chess board detection model.
This script provides a simple command-line interface to train and test the model.
"""

import os
import argparse
import torch
import subprocess
import sys

from config import MODELS_DIR, DATA_DIR, DEVICE


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Run enhanced chess board detection model')
    parser.add_argument('--mode', type=str, choices=['train', 'test', 'both'], default='both',
                        help='Mode to run (train, test, or both)')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR, help='Data directory')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs for training')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size for training')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate for training')
    parser.add_argument('--heatmap_weight', type=float, default=1.0, help='Weight for heatmap loss')
    parser.add_argument('--geometric_weight', type=float, default=0.2, help='Weight for geometric loss')
    parser.add_argument('--separation_weight', type=float, default=0.4, help='Weight for separation loss')
    parser.add_argument('--peak_separation_weight', type=float, default=0.3, help='Weight for peak separation loss')
    parser.add_argument('--edge_suppression_weight', type=float, default=0.5, help='Weight for edge suppression loss')
    parser.add_argument('--peak_enhancement_weight', type=float, default=0.3, help='Weight for peak enhancement loss')
    parser.add_argument('--test_image', type=str, default=None, help='Path to test image')
    parser.add_argument('--threshold', type=float, default=0.05, help='Confidence threshold for corner detection')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    args = parser.parse_args()

    # Set device
    device = 'cpu' if args.cpu else 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Train model
    if args.mode in ['train', 'both']:
        print("=== Training Enhanced Model ===")
        train_cmd = [
            sys.executable, 'train_enhanced.py',
            '--data_dir', args.data_dir,
            '--output_dir', args.output_dir,
            '--epochs', str(args.epochs),
            '--batch_size', str(args.batch_size),
            '--lr', str(args.lr),
            '--heatmap_weight', str(args.heatmap_weight),
            '--geometric_weight', str(args.geometric_weight),
            '--separation_weight', str(args.separation_weight),
            '--peak_separation_weight', str(args.peak_separation_weight),
            '--edge_suppression_weight', str(args.edge_suppression_weight),
            '--peak_enhancement_weight', str(args.peak_enhancement_weight)
        ]

        if args.cpu:
            train_cmd.append('--cpu')

        print(f"Running command: {' '.join(train_cmd)}")
        subprocess.run(train_cmd)

    # Test model
    if args.mode in ['test', 'both']:
        print("=== Testing Enhanced Model ===")

        # If no test image is provided, use the first image in the data directory
        if args.test_image is None:
            # Try to find a test image
            test_dir = os.path.join(args.data_dir, 'real')
            if os.path.exists(test_dir):
                for file in os.listdir(test_dir):
                    if file.endswith(('.jpg', '.jpeg', '.png')):
                        args.test_image = os.path.join(test_dir, file)
                        break

            if args.test_image is None:
                print("No test image found. Please provide a test image.")
                return

        test_cmd = [
            sys.executable, 'inference_enhanced.py',
            '--image_path', args.test_image,
            '--model_path', os.path.join(args.output_dir, 'checkpoints', 'v3', 'best_model.pth'),
            '--output_dir', os.path.join(args.output_dir, 'visualizations', 'v3', 'test_results'),
            '--threshold', str(args.threshold)
        ]

        if args.cpu:
            test_cmd.append('--cpu')

        print(f"Running command: {' '.join(test_cmd)}")
        subprocess.run(test_cmd)

    print("Done!")


if __name__ == "__main__":
    main()
