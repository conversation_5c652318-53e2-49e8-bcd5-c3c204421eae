package com.chessvision.app

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.ui.graphics.graphicsLayer

@Composable
fun ChessBoardControls(
    boardState: ChessBoardState,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF262421).copy(alpha = 0.95f)
        ),
        shape = RoundedCornerShape(20.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly,
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Edit Mode Toggle
            ControlButton(
                icon = if (boardState.isEditMode) Icons.Default.PlayArrow else Icons.Default.Edit,
                label = if (boardState.isEditMode) "Play" else "Edit",
                isActive = boardState.isEditMode,
                onClick = {
                    boardState.isEditMode = !boardState.isEditMode
                    boardState.selectedSquare = null
                    boardState.selectedPieceForPlacement = null
                }
            )

            // Flip Board
            ControlButton(
                icon = Icons.Default.Refresh,
                label = "Flip",
                isActive = boardState.isBoardFlipped,
                onClick = { boardState.flipBoard() }
            )

            // Reset to Initial Position
            ControlButton(
                icon = Icons.Default.RestartAlt,
                label = "Reset",
                isActive = false,
                onClick = { boardState.setupInitialPosition() }
            )

            // Clear Board (only in edit mode)
            AnimatedVisibility(
                visible = boardState.isEditMode,
                enter = slideInHorizontally { it } + fadeIn(),
                exit = slideOutHorizontally { it } + fadeOut()
            ) {
                ControlButton(
                    icon = Icons.Default.Clear,
                    label = "Clear",
                    isActive = false,
                    onClick = {
                        // Clear the entire board
                        for (rank in 0..7) {
                            for (file in 0..7) {
                                boardState.board[rank][file] = null
                            }
                        }
                        boardState.selectedPieceForPlacement = null
                    },
                    color = Color(0xFFD32F2F)
                )
            }
        }
    }
}

@Composable
fun ControlButton(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    label: String,
    isActive: Boolean,
    onClick: () -> Unit,
    color: Color = Color(0xFF769656)
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isActive) color else Color(0xFF3a3a3a),
        animationSpec = tween(300, easing = EaseOutCubic),
        label = "control_button_background"
    )

    val scale by animateFloatAsState(
        targetValue = if (isActive) 1.05f else 1f,
        animationSpec = spring(dampingRatio = Spring.DampingRatioMediumBouncy),
        label = "control_button_scale"
    )

    Card(
        onClick = onClick,
        modifier = Modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        ),
        shape = RoundedCornerShape(16.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isActive) 6.dp else 2.dp
        )
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                modifier = Modifier.size(24.dp),
                tint = Color.White
            )
            
            Spacer(modifier = Modifier.height(4.dp))
            
            Text(
                text = label,
                style = MaterialTheme.typography.labelSmall.copy(fontWeight = FontWeight.Medium),
                color = Color.White
            )
        }
    }
}
