# Chess Piece Detection Improvement Plan

## 1. Current Performance Analysis

### Best Model Performance
- **Overall**: mAP50: 0.971, Precision: 0.953, Recall: 0.927
- **Best Performing Pieces**:
  - White Pawn: Precision: 0.993, Recall: 1.000, mAP50: 0.995
  - White Rook: Precision: 0.986, Recall: 1.000, mAP50: 0.995
  - Black Rook: Precision: 0.986, Recall: 1.000, mAP50: 0.995
  - <PERSON> King: Precision: 1.000, Recall: 1.000, mAP50: 0.995

### Pieces Needing Improvement
- **<PERSON> Bishop**: Precision: 0.369, Recall: 0.333, mAP50: 0.448
- **<PERSON> Knight**: Precision: 0.556, Recall: 0.500, mAP50: 0.553
- **White Queen**: Precision: 1.000, Recall: 0.364, mAP50: 0.764
- **Black Queen**: Precision: 0.782, Recall: 0.433, mAP50: 0.658
- **<PERSON>**: Precision: 0.476, Recall: 0.639, mAP50: 0.613

## 2. Key Insights from Previous Training

1. **Successful Training Configuration**:
   - Mosaic augmentation for 50 epochs
   - Mixup with probability 0.5
   - Patience of 100 epochs
   - Image size of 416x416
   - AdamW optimizer

2. **Dataset Characteristics**:
   - Best model used a smaller but more focused dataset
   - Balanced representation of piece types is crucial
   - Quality of augmentation matters more than quantity

## 3. Targeted Augmentation Strategy

### A. Focus on Problematic Pieces
Create additional augmented samples specifically for:
1. **White and Black Bishops**
2. **White and Black Queens**
3. **White Knights**

### B. Augmentation Techniques

#### General Augmentations
```python
general_transform = A.Compose([
    A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.7),
    A.HueSaturationValue(hue_shift_limit=10, sat_shift_limit=15, val_shift_limit=10, p=0.5),
    A.GaussNoise(var_limit=(10.0, 50.0), p=0.3),
    A.OneOf([
        A.MotionBlur(blur_limit=7, p=0.2),
        A.MedianBlur(blur_limit=7, p=0.2),
        A.GaussianBlur(blur_limit=7, p=0.2),
    ], p=0.2),
    A.OneOf([
        A.RandomRotate90(p=0.5),
        A.Perspective(scale=(0.05, 0.1), p=0.5),
    ], p=0.7),
], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))
```

#### Bishop-Specific Augmentations
```python
bishop_transform = A.Compose([
    A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.8),
    A.HueSaturationValue(hue_shift_limit=15, sat_shift_limit=25, val_shift_limit=15, p=0.7),
    A.CLAHE(clip_limit=4.0, tile_grid_size=(8, 8), p=0.5),
    A.Perspective(scale=(0.05, 0.15), p=0.7),  # More perspective variation for bishops
    A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.2, rotate_limit=20, p=0.8),
], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))
```

#### Queen-Specific Augmentations
```python
queen_transform = A.Compose([
    A.RandomBrightnessContrast(brightness_limit=0.25, contrast_limit=0.25, p=0.8),
    A.HueSaturationValue(hue_shift_limit=10, sat_shift_limit=20, val_shift_limit=10, p=0.6),
    A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=0.5),
    A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.15, rotate_limit=15, p=0.8),
    A.RandomShadow(shadow_roi=(0, 0, 1, 1), num_shadows_lower=1, num_shadows_upper=2, p=0.3),
], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))
```

#### Knight-Specific Augmentations
```python
knight_transform = A.Compose([
    A.RandomBrightnessContrast(brightness_limit=0.25, contrast_limit=0.25, p=0.7),
    A.HueSaturationValue(hue_shift_limit=15, sat_shift_limit=20, val_shift_limit=15, p=0.6),
    A.GaussNoise(var_limit=(5.0, 30.0), p=0.4),
    A.ShiftScaleRotate(shift_limit=0.15, scale_limit=0.2, rotate_limit=25, p=0.8),  # More rotation for knights
    A.RandomShadow(shadow_roi=(0, 0, 1, 1), num_shadows_lower=1, num_shadows_upper=2, p=0.4),
], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))
```

## 4. Implementation Plan

### A. Dataset Preparation

1. **Identify Source Images**:
   - Find all images containing problematic pieces
   - Ensure variety in positions, lighting, and backgrounds

2. **Create Augmentation Pipeline**:
   ```python
   def create_targeted_dataset(source_dir, output_dir, piece_types, augmentations_per_image=10):
       """
       Create a targeted dataset with augmented images of specific chess pieces.

       Args:
           source_dir: Directory containing original images and annotations
           output_dir: Directory to save augmented dataset
           piece_types: List of piece types to focus on (e.g., ['white_bishop', 'black_queen'])
           augmentations_per_image: Number of augmented versions to create per original image
       """
       # Create output directories
       os.makedirs(os.path.join(output_dir, 'images', 'train'), exist_ok=True)
       os.makedirs(os.path.join(output_dir, 'images', 'val'), exist_ok=True)
       os.makedirs(os.path.join(output_dir, 'labels', 'train'), exist_ok=True)
       os.makedirs(os.path.join(output_dir, 'labels', 'val'), exist_ok=True)

       # Get all images containing target pieces
       target_images = find_images_with_pieces(source_dir, piece_types)

       # Split into train/val sets (80/20 split)
       train_images, val_images = train_test_split(target_images, test_size=0.2, random_state=42)

       # Process training images
       for img_path in train_images:
           augment_and_save(img_path, os.path.join(output_dir, 'images', 'train'),
                           os.path.join(output_dir, 'labels', 'train'),
                           piece_types, augmentations_per_image)

       # Process validation images (fewer augmentations)
       for img_path in val_images:
           augment_and_save(img_path, os.path.join(output_dir, 'images', 'val'),
                           os.path.join(output_dir, 'labels', 'val'),
                           piece_types, max(2, augmentations_per_image // 5))

       # Create dataset.yaml file
       create_dataset_yaml(output_dir, len(piece_types))
   ```

3. **Piece-Specific Augmentation**:
   ```python
   def augment_and_save(image_path, output_img_dir, output_label_dir, piece_types, num_augmentations):
       """Apply piece-specific augmentations and save results"""
       image = cv2.imread(image_path)
       image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

       # Get corresponding annotation
       annotation_path = image_path.replace('images', 'labels').replace('.jpg', '.txt')
       with open(annotation_path, 'r') as f:
           annotations = f.readlines()

       # Parse annotations to get bounding boxes and class labels
       bboxes = []
       class_labels = []
       for ann in annotations:
           parts = ann.strip().split()
           class_id = int(parts[0])
           x_center, y_center, width, height = map(float, parts[1:5])
           bboxes.append([x_center, y_center, width, height])
           class_labels.append(class_id)

       # Determine which pieces are in this image
       contained_pieces = [piece_types[i] for i, label in enumerate(class_labels)
                          if label in [get_class_id(pt) for pt in piece_types]]

       # Select appropriate transform based on pieces in the image
       if 'white_bishop' in contained_pieces or 'black_bishop' in contained_pieces:
           transform = bishop_transform
       elif 'white_queen' in contained_pieces or 'black_queen' in contained_pieces:
           transform = queen_transform
       elif 'white_knight' in contained_pieces or 'black_knight' in contained_pieces:
           transform = knight_transform
       else:
           transform = general_transform

       # Generate augmented samples
       for i in range(num_augmentations):
           try:
               augmented = transform(image=image, bboxes=bboxes, class_labels=class_labels)

               # Save augmented image
               img_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_aug{i}.jpg"
               cv2.imwrite(os.path.join(output_img_dir, img_filename),
                          cv2.cvtColor(augmented['image'], cv2.COLOR_RGB2BGR))

               # Save augmented annotation
               label_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_aug{i}.txt"
               with open(os.path.join(output_label_dir, label_filename), 'w') as f:
                   for bbox, class_id in zip(augmented['bboxes'], augmented['class_labels']):
                       f.write(f"{class_id} {bbox[0]} {bbox[1]} {bbox[2]} {bbox[3]}\n")
           except Exception as e:
               print(f"Error augmenting {image_path}: {e}")
   ```

### B. Training Configuration

1. **Model Setup**:
   - Use YOLO11n model with pretrained weights
   - Image size: 416x416
   - Batch size: 16

2. **Training Parameters Based on First Successful v11n Model**:
   ```
   # Basic Configuration
   --model yolo11n.pt
   --data [path_to_augmented_dataset]/dataset.yaml
   --epochs 100
   --batch 16
   --device 0
   --img-size 416
   --patience 100

   # Optimization Settings
   --lr0 0.01
   --lrf 0.01
   --momentum 0.937
   --weight_decay 0.0005
   --warmup_epochs 3.0
   --warmup_momentum 0.8
   --warmup_bias_lr 0.1

   # Augmentation Settings
   --mosaic 1.0
   --mixup 0.5
   --degrees 15.0
   --translate 0.2
   --scale 0.5
   --shear 2.0
   --fliplr 0.5
   --perspective 0.0005
   --hsv_h 0.015
   --hsv_s 0.7
   --hsv_v 0.4
   --close_mosaic 50

   # Advanced Features
   --amp true
   --cache true
   --iou 0.7
   --max_det 300
   ```

3. **Loss Function Weights**:
   - Box Loss Weight: 7.5
   - Classification Loss Weight: 0.5
   - DFL Loss Weight: 1.5

4. **Monitoring**:
   - Track per-class metrics (precision, recall, mAP50)
   - Save checkpoints every 10 epochs
   - Use early stopping with patience=100

## 5. Evaluation and Iteration

1. **Performance Metrics**:
   - Overall mAP50 target: ≥ 0.99
   - Per-class precision target: ≥ 0.95
   - Per-class recall target: ≥ 0.95

2. **Error Analysis**:
   - Analyze false positives and false negatives for each piece type
   - Identify specific scenarios where detection fails
   - Create additional augmentations targeting failure cases

3. **Iterative Improvement**:
   - Train initial model with targeted augmentations
   - Evaluate performance on validation set
   - Identify remaining problematic pieces
   - Create additional augmentations for those pieces
   - Retrain model with updated dataset

## 6. Timeline and Resources

1. **Dataset Preparation**: 1-2 days
   - Identify source images: 2 hours
   - Implement augmentation pipeline: 4 hours
   - Generate augmented dataset: 4-8 hours

2. **Model Training**: 1-2 days
   - Initial training run: 8-12 hours
   - Evaluation and analysis: 2 hours
   - Refinement and retraining: 8-12 hours

3. **Hardware Requirements**:
   - NVIDIA GeForce RTX 3050 6GB Laptop GPU
   - 16GB RAM
   - 10GB disk space for dataset and checkpoints

## 7. Expected Outcomes

1. **Improved Detection**:
   - White Bishop: mAP50 from 0.448 to ≥ 0.95
   - White Knight: mAP50 from 0.553 to ≥ 0.95
   - White Queen: Recall from 0.364 to ≥ 0.95
   - Black Queen: Recall from 0.433 to ≥ 0.95
   - Black Bishop: mAP50 from 0.613 to ≥ 0.95

2. **Overall Performance**:
   - mAP50: from 0.971 to ≥ 0.99
   - Precision: from 0.953 to ≥ 0.99
   - Recall: from 0.927 to ≥ 0.99

3. **Robustness**:
   - Consistent performance across all piece types
   - Reliable detection in varied lighting and positions
   - Minimal confusion between similar pieces
