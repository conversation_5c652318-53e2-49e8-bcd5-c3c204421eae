PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_breakthrough_v4.py
🚀 BREAKTHROUGH U-NET V4 TRAINING
============================================================
🎯 TARGET: Exceed V1's 0.9100 Dice with fewer parameters!
Configuration: {
  "dataset_dir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\data\\augmented\\v5.2\\augmented_20250518_153326",
  "save_dir": "chess_board_detection/breakthrough_v4_results",
  "epochs": 100,
  "batch_size": 4,
  "learning_rate": 0.001,
  "base_channels": 20,
  "accumulation_steps": 2,
  "num_workers": 0,
  "use_knowledge_distillation": false
}
Using device: cuda
Creating enhanced dataloaders...
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Creating Breakthrough U-Net V4...
Model parameters: 3,296,335
Efficiency vs V1: 0.191x (80.9% reduction)
🎯 Target: Achieve 0.91+ Dice with 3,296,335 params!
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v4.py:306: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
🚀 Starting breakthrough training for 100 epochs...

🔥 Epoch 1/100
🚀 Breakthrough Training:   0%|                                        | 0/21 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  image = torch.load(image_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  mask = torch.load(mask_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v4.py:151: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
🚀 Breakthrough Training: 100%|█| 21/21 [00:06<00:00,  3.50it/s, Loss=0.6248, Dice=0.3987, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  6.40it/s, Loss=1.0164, Dice=0.0000, IoU 
Train - Loss: 0.6816, Dice: 0.3569, IoU: 0.2274
Val   - Loss: 1.1877, Dice: 0.3412, IoU: 0.2261, F1: 0.3412
LR: 0.000040
✅ New best model saved! Val Dice: 0.3412

🔥 Epoch 2/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.80it/s, Loss=0.5450, Dice=0.5475, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.48it/s, Loss=0.9884, Dice=0.0000, IoU 
Train - Loss: 0.6062, Dice: 0.4688, IoU: 0.3155
Val   - Loss: 1.1866, Dice: 0.2974, IoU: 0.1881, F1: 0.2974
LR: 0.000040

🔥 Epoch 3/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.4505, Dice=0.6557, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.62it/s, Loss=0.9215, Dice=0.8220, IoU 
Train - Loss: 0.5333, Dice: 0.6060, IoU: 0.4524
Val   - Loss: 2.0746, Dice: 0.6042, IoU: 0.4468, F1: 0.6042
LR: 0.000040
✅ New best model saved! Val Dice: 0.6042

🔥 Epoch 4/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.67it/s, Loss=0.3589, Dice=0.8100, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.54it/s, Loss=0.7708, Dice=0.7670, IoU 
Train - Loss: 0.4744, Dice: 0.6673, IoU: 0.5249
Val   - Loss: 13.2881, Dice: 0.5915, IoU: 0.4293, F1: 0.5915
LR: 0.000041

🔥 Epoch 5/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.3982, Dice=0.8178, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.79it/s, Loss=0.7029, Dice=0.7654, IoU 
Train - Loss: 0.4210, Dice: 0.7373, IoU: 0.6072
Val   - Loss: 15.9780, Dice: 0.5965, IoU: 0.4331, F1: 0.5965
LR: 0.000041

🔥 Epoch 6/100
Val   - Loss: 13.2881, Dice: 0.5915, IoU: 0.4293, F1: 0.5915
LR: 0.000041

🔥 Epoch 5/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.3982, Dice=0.8178, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.79it/s, Loss=0.7029, Dice=0.7654, IoU 
Train - Loss: 0.4210, Dice: 0.7373, IoU: 0.6072
Val   - Loss: 15.9780, Dice: 0.5965, IoU: 0.4331, F1: 0.5965
LR: 0.000041

🔥 Epoch 6/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.3982, Dice=0.8178, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.79it/s, Loss=0.7029, Dice=0.7654, IoU 
Train - Loss: 0.4210, Dice: 0.7373, IoU: 0.6072
Val   - Loss: 15.9780, Dice: 0.5965, IoU: 0.4331, F1: 0.5965
LR: 0.000041

🔥 Epoch 6/100
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.79it/s, Loss=0.7029, Dice=0.7654, IoU 
Train - Loss: 0.4210, Dice: 0.7373, IoU: 0.6072
Val   - Loss: 15.9780, Dice: 0.5965, IoU: 0.4331, F1: 0.5965
LR: 0.000041

🔥 Epoch 6/100
Train - Loss: 0.4210, Dice: 0.7373, IoU: 0.6072
Val   - Loss: 15.9780, Dice: 0.5965, IoU: 0.4331, F1: 0.5965
LR: 0.000041

🔥 Epoch 6/100
Val   - Loss: 15.9780, Dice: 0.5965, IoU: 0.4331, F1: 0.5965
LR: 0.000041

🔥 Epoch 6/100
LR: 0.000041

🔥 Epoch 6/100

🔥 Epoch 6/100
🔥 Epoch 6/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.68it/s, Loss=0.2354, Dice=0.8937, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.60it/s, Loss=0.6140, Dice=0.7653, IoU 
Train - Loss: 0.3549, Dice: 0.7986, IoU: 0.6751
Val   - Loss: 23.5445, Dice: 0.5664, IoU: 0.4069, F1: 0.5664
LR: 0.000042

🔥 Epoch 7/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.2167, Dice=0.8681, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.66it/s, Loss=0.6251, Dice=0.7640, IoU 
Train - Loss: 0.3400, Dice: 0.8107, IoU: 0.6896
Val   - Loss: 14.5235, Dice: 0.5919, IoU: 0.4287, F1: 0.5919
LR: 0.000043

🔥 Epoch 8/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.71it/s, Loss=0.4270, Dice=0.7191, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.67it/s, Loss=0.6739, Dice=0.7637, IoU 
Train - Loss: 0.3012, Dice: 0.8474, IoU: 0.7416
Val   - Loss: 13.6930, Dice: 0.5922, IoU: 0.4288, F1: 0.5922
LR: 0.000043

🔥 Epoch 9/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.1310, Dice=0.9432, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.47it/s, Loss=0.7727, Dice=0.7635, IoU 
Train - Loss: 0.2688, Dice: 0.8635, IoU: 0.7708
Val   - Loss: 24.3165, Dice: 0.5884, IoU: 0.4252, F1: 0.5884
LR: 0.000044

🔥 Epoch 10/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.1656, Dice=0.9318, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.68it/s, Loss=0.6921, Dice=0.7639, IoU 
Train - Loss: 0.2555, Dice: 0.8569, IoU: 0.7576
Val   - Loss: 17.9185, Dice: 0.5994, IoU: 0.4360, F1: 0.5994
LR: 0.000045

🔥 Epoch 11/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.80it/s, Loss=0.1440, Dice=0.9404, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=0.7179, Dice=0.7638, IoU 
Train - Loss: 0.2216, Dice: 0.8886, IoU: 0.8044
Val   - Loss: 13.9361, Dice: 0.5972, IoU: 0.4342, F1: 0.5972
LR: 0.000047

🔥 Epoch 12/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.85it/s, Loss=0.1593, Dice=0.9276, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.80it/s, Loss=0.7795, Dice=0.7636, IoU 
Train - Loss: 0.2221, Dice: 0.8721, IoU: 0.7799
Val   - Loss: 8.3088, Dice: 0.5953, IoU: 0.4320, F1: 0.5953
LR: 0.000048

🔥 Epoch 13/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0852, Dice=0.9701, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.79it/s, Loss=0.7851, Dice=0.7637, IoU 
Train - Loss: 0.1859, Dice: 0.9013, IoU: 0.8246
Val   - Loss: 17.7874, Dice: 0.5994, IoU: 0.4365, F1: 0.5994
LR: 0.000049

🔥 Epoch 14/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.3548, Dice=0.7863, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.68it/s, Loss=0.7676, Dice=0.7637, IoU 
Train - Loss: 0.1860, Dice: 0.8954, IoU: 0.8163
Val   - Loss: 7.7127, Dice: 0.5988, IoU: 0.4352, F1: 0.5988
LR: 0.000051

🔥 Epoch 15/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.85it/s, Loss=0.1175, Dice=0.9559, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.61it/s, Loss=0.7128, Dice=0.7644, IoU 
Train - Loss: 0.1697, Dice: 0.9039, IoU: 0.8305
Val   - Loss: 6.5029, Dice: 0.6052, IoU: 0.4418, F1: 0.6052
LR: 0.000052
✅ New best model saved! Val Dice: 0.6052

🔥 Epoch 16/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.68it/s, Loss=0.0916, Dice=0.9499, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.58it/s, Loss=0.4777, Dice=0.8418, IoU 
Train - Loss: 0.1451, Dice: 0.9184, IoU: 0.8550
Val   - Loss: 8.4320, Dice: 0.6560, IoU: 0.4994, F1: 0.6560
LR: 0.000054
✅ New best model saved! Val Dice: 0.6560

🔥 Epoch 17/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.1325, Dice=0.9433, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.24it/s, Loss=0.5088, Dice=0.8614, IoU 
Train - Loss: 0.1548, Dice: 0.9094, IoU: 0.8382
Val   - Loss: 10.1337, Dice: 0.6736, IoU: 0.5196, F1: 0.6736
LR: 0.000056
✅ New best model saved! Val Dice: 0.6736

🔥 Epoch 18/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.68it/s, Loss=0.0967, Dice=0.9514, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.48it/s, Loss=0.6855, Dice=0.8874, IoU
Train - Loss: 0.1309, Dice: 0.9269, IoU: 0.8665
Val   - Loss: 8.6833, Dice: 0.7459, IoU: 0.6005, F1: 0.7459
LR: 0.000057
✅ New best model saved! Val Dice: 0.7459

🔥 Epoch 19/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.69it/s, Loss=0.1146, Dice=0.9652, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.30it/s, Loss=0.9653, Dice=0.0000, IoU
Train - Loss: 0.1269, Dice: 0.9246, IoU: 0.8662
Val   - Loss: 15.4281, Dice: 0.4543, IoU: 0.3229, F1: 0.4543
LR: 0.000059

🔥 Epoch 20/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.70it/s, Loss=0.0633, Dice=0.9746, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.78it/s, Loss=0.9233, Dice=0.3211, IoU 
Train - Loss: 0.1210, Dice: 0.9327, IoU: 0.8776
Val   - Loss: 7.8226, Dice: 0.6623, IoU: 0.5221, F1: 0.6623
LR: 0.000062

🔥 Epoch 21/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.1081, Dice=0.9480, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.54it/s, Loss=0.6183, Dice=0.9011, IoU 
Train - Loss: 0.1111, Dice: 0.9392, IoU: 0.8880
Val   - Loss: 8.0163, Dice: 0.7400, IoU: 0.5950, F1: 0.7400
LR: 0.000064

🔥 Epoch 22/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0692, Dice=0.9673, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.44it/s, Loss=0.5165, Dice=0.8326, IoU 
Train - Loss: 0.0995, Dice: 0.9470, IoU: 0.9014
Val   - Loss: 5.9912, Dice: 0.6623, IoU: 0.5037, F1: 0.6623
LR: 0.000066

🔥 Epoch 23/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.82it/s, Loss=0.1150, Dice=0.9285, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.16it/s, Loss=0.5956, Dice=0.8799, IoU 
Train - Loss: 0.1091, Dice: 0.9372, IoU: 0.8854
Val   - Loss: 9.8161, Dice: 0.7159, IoU: 0.5658, F1: 0.7159
LR: 0.000068

🔥 Epoch 24/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.69it/s, Loss=0.0647, Dice=0.9711, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.85it/s, Loss=0.8885, Dice=0.6780, IoU 
Train - Loss: 0.0851, Dice: 0.9545, IoU: 0.9153
Val   - Loss: 12.7799, Dice: 0.7836, IoU: 0.6521, F1: 0.7836
LR: 0.000071
✅ New best model saved! Val Dice: 0.7836

🔥 Epoch 25/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0844, Dice=0.9627, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.69it/s, Loss=1.8190, Dice=0.0000, IoU 
Train - Loss: 0.1097, Dice: 0.9372, IoU: 0.8847
Val   - Loss: 4.7110, Dice: 0.3620, IoU: 0.2596, F1: 0.3620
LR: 0.000073

🔥 Epoch 26/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.1236, Dice=0.9149, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.90it/s, Loss=1.9299, Dice=0.0000, IoU 
Train - Loss: 0.0833, Dice: 0.9553, IoU: 0.9165
Val   - Loss: 4.2115, Dice: 0.3902, IoU: 0.2793, F1: 0.3902
LR: 0.000076

🔥 Epoch 27/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.83it/s, Loss=0.0458, Dice=0.9792, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.69it/s, Loss=1.9843, Dice=0.0000, IoU 
Train - Loss: 0.0712, Dice: 0.9632, IoU: 0.9306
Val   - Loss: 5.1068, Dice: 0.3431, IoU: 0.2376, F1: 0.3431
LR: 0.000079

🔥 Epoch 28/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.0443, Dice=0.9794, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=1.2907, Dice=0.0000, IoU 
Train - Loss: 0.0711, Dice: 0.9620, IoU: 0.9284
Val   - Loss: 11.9365, Dice: 0.3546, IoU: 0.2452, F1: 0.3546
LR: 0.000082

🔥 Epoch 29/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.0336, Dice=0.9874, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.63it/s, Loss=0.4652, Dice=0.9024, IoU 
Train - Loss: 0.0705, Dice: 0.9612, IoU: 0.9267
Val   - Loss: 5.4048, Dice: 0.7410, IoU: 0.5964, F1: 0.7410
LR: 0.000085

🔥 Epoch 30/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.83it/s, Loss=0.3468, Dice=0.8034, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.20it/s, Loss=0.6225, Dice=0.7870, IoU 
Train - Loss: 0.0852, Dice: 0.9535, IoU: 0.9142
Val   - Loss: 12.5432, Dice: 0.6398, IoU: 0.4767, F1: 0.6398
LR: 0.000088

🔥 Epoch 31/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.65it/s, Loss=0.0319, Dice=0.9859, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.43it/s, Loss=0.4076, Dice=0.8717, IoU 
Train - Loss: 0.0694, Dice: 0.9612, IoU: 0.9284
Val   - Loss: 6.6080, Dice: 0.7053, IoU: 0.5524, F1: 0.7053
LR: 0.000091

🔥 Epoch 32/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.71it/s, Loss=0.0331, Dice=0.9887, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.56it/s, Loss=1.7215, Dice=0.0000, IoU 
Train - Loss: 0.0606, Dice: 0.9699, IoU: 0.9424
Val   - Loss: 3.9237, Dice: 0.3986, IoU: 0.2895, F1: 0.3986
LR: 0.000094

🔥 Epoch 33/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.82it/s, Loss=0.1188, Dice=0.9303, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=0.6460, Dice=0.9070, IoU 
Train - Loss: 0.0697, Dice: 0.9626, IoU: 0.9296
Val   - Loss: 2.8224, Dice: 0.8365, IoU: 0.7206, F1: 0.8365
LR: 0.000098
✅ New best model saved! Val Dice: 0.8365

🔥 Epoch 34/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.86it/s, Loss=0.0267, Dice=0.9904, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.70it/s, Loss=0.5640, Dice=0.9204, IoU 
Train - Loss: 0.0571, Dice: 0.9701, IoU: 0.9428
Val   - Loss: 3.9387, Dice: 0.8339, IoU: 0.7176, F1: 0.8339
LR: 0.000101

🔥 Epoch 35/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.83it/s, Loss=0.0717, Dice=0.9553, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.83it/s, Loss=0.7290, Dice=0.7724, IoU 
Train - Loss: 0.0524, Dice: 0.9734, IoU: 0.9488
Val   - Loss: 3.2491, Dice: 0.6183, IoU: 0.4549, F1: 0.6183
LR: 0.000105

🔥 Epoch 36/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.85it/s, Loss=0.0287, Dice=0.9864, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.59it/s, Loss=0.4831, Dice=0.8209, IoU 
Train - Loss: 0.0549, Dice: 0.9706, IoU: 0.9444
Val   - Loss: 5.2882, Dice: 0.6735, IoU: 0.5143, F1: 0.6735
LR: 0.000109

🔥 Epoch 37/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0553, Dice=0.9789, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.75it/s, Loss=0.5082, Dice=0.8612, IoU 
Train - Loss: 0.0501, Dice: 0.9751, IoU: 0.9519
Val   - Loss: 6.2601, Dice: 0.7700, IoU: 0.6288, F1: 0.7700
LR: 0.000112

🔥 Epoch 38/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.81it/s, Loss=0.3796, Dice=0.7144, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.48it/s, Loss=0.7292, Dice=0.8227, IoU 
Train - Loss: 0.0680, Dice: 0.9581, IoU: 0.9255
Val   - Loss: 4.3217, Dice: 0.8156, IoU: 0.7015, F1: 0.8156
LR: 0.000116

🔥 Epoch 39/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.71it/s, Loss=0.0359, Dice=0.9810, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.79it/s, Loss=0.4413, Dice=0.8346, IoU 
Train - Loss: 0.0538, Dice: 0.9704, IoU: 0.9436
Val   - Loss: 5.7011, Dice: 0.6955, IoU: 0.5390, F1: 0.6955
LR: 0.000120

🔥 Epoch 40/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.81it/s, Loss=0.0221, Dice=0.9898, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.60it/s, Loss=0.4985, Dice=0.8665, IoU 
Train - Loss: 0.0505, Dice: 0.9728, IoU: 0.9488
Val   - Loss: 6.3578, Dice: 0.7811, IoU: 0.6431, F1: 0.7811
LR: 0.000124

🔥 Epoch 41/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.82it/s, Loss=0.0414, Dice=0.9783, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.60it/s, Loss=1.7186, Dice=0.0000, IoU 
Train - Loss: 0.0545, Dice: 0.9699, IoU: 0.9429
Val   - Loss: 4.0568, Dice: 0.3804, IoU: 0.2780, F1: 0.3804
LR: 0.000128

🔥 Epoch 42/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.0276, Dice=0.9851, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.71it/s, Loss=0.4717, Dice=0.8887, IoU 
Train - Loss: 0.0555, Dice: 0.9671, IoU: 0.9411
Val   - Loss: 2.9110, Dice: 0.8248, IoU: 0.7038, F1: 0.8248
LR: 0.000133

🔥 Epoch 43/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0646, Dice=0.9732, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=0.5625, Dice=0.8240, IoU 
Train - Loss: 0.0537, Dice: 0.9695, IoU: 0.9427
Val   - Loss: 3.4294, Dice: 0.6690, IoU: 0.5093, F1: 0.6690
LR: 0.000137

🔥 Epoch 44/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.84it/s, Loss=0.0246, Dice=0.9883, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.74it/s, Loss=0.6841, Dice=0.8136, IoU 
Train - Loss: 0.0400, Dice: 0.9804, IoU: 0.9618
Val   - Loss: 4.1114, Dice: 0.8187, IoU: 0.6963, F1: 0.8187
LR: 0.000141

🔥 Epoch 45/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.74it/s, Loss=0.0257, Dice=0.9852, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.48it/s, Loss=0.4696, Dice=0.8234, IoU 
Train - Loss: 0.0401, Dice: 0.9787, IoU: 0.9586
Val   - Loss: 2.9339, Dice: 0.6689, IoU: 0.5093, F1: 0.6689
LR: 0.000146

🔥 Epoch 46/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0200, Dice=0.9915, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.68it/s, Loss=0.4202, Dice=0.8907, IoU 
Train - Loss: 0.0382, Dice: 0.9801, IoU: 0.9612
Val   - Loss: 5.9833, Dice: 0.8048, IoU: 0.6764, F1: 0.8048
LR: 0.000150

🔥 Epoch 47/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.82it/s, Loss=0.0205, Dice=0.9917, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.74it/s, Loss=0.3782, Dice=0.8557, IoU 
Train - Loss: 0.0431, Dice: 0.9757, IoU: 0.9534
Val   - Loss: 3.6417, Dice: 0.6966, IoU: 0.5412, F1: 0.6966
LR: 0.000155

🔥 Epoch 48/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.68it/s, Loss=0.0478, Dice=0.9804, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.38it/s, Loss=0.6086, Dice=0.8164, IoU 
Train - Loss: 0.0378, Dice: 0.9802, IoU: 0.9614
Val   - Loss: 3.0149, Dice: 0.6860, IoU: 0.5277, F1: 0.6860
LR: 0.000160

🔥 Epoch 49/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.65it/s, Loss=0.0199, Dice=0.9898, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.53it/s, Loss=0.6091, Dice=0.8077, IoU 
Train - Loss: 0.0499, Dice: 0.9691, IoU: 0.9430
Val   - Loss: 3.6484, Dice: 0.6534, IoU: 0.4924, F1: 0.6534
LR: 0.000164

🔥 Epoch 50/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.69it/s, Loss=0.0147, Dice=0.9948, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.51it/s, Loss=0.2847, Dice=0.9231, IoU 
Train - Loss: 0.0362, Dice: 0.9806, IoU: 0.9625
Val   - Loss: 4.0455, Dice: 0.8521, IoU: 0.7440, F1: 0.8521
LR: 0.000169
✅ New best model saved! Val Dice: 0.8521

🔥 Epoch 51/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.73it/s, Loss=0.0307, Dice=0.9825, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.56it/s, Loss=0.3688, Dice=0.8586, IoU 
Train - Loss: 0.0327, Dice: 0.9825, IoU: 0.9659
Val   - Loss: 2.2593, Dice: 0.7208, IoU: 0.5696, F1: 0.7208
LR: 0.000174

🔥 Epoch 52/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.73it/s, Loss=0.1193, Dice=0.9134, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.47it/s, Loss=0.3444, Dice=0.8708, IoU 
Train - Loss: 0.0455, Dice: 0.9730, IoU: 0.9487
Val   - Loss: 2.7137, Dice: 0.7409, IoU: 0.5932, F1: 0.7409
LR: 0.000179

🔥 Epoch 53/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.69it/s, Loss=0.2784, Dice=0.8025, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.44it/s, Loss=0.8066, Dice=0.7869, IoU 
Train - Loss: 0.0445, Dice: 0.9731, IoU: 0.9505
Val   - Loss: 3.5958, Dice: 0.6454, IoU: 0.4816, F1: 0.6454
LR: 0.000184

🔥 Epoch 54/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.0151, Dice=0.9930, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.67it/s, Loss=0.7222, Dice=0.7911, IoU 
Train - Loss: 0.0283, Dice: 0.9854, IoU: 0.9714
Val   - Loss: 2.1355, Dice: 0.6524, IoU: 0.4894, F1: 0.6524
LR: 0.000190

🔥 Epoch 55/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.81it/s, Loss=0.0374, Dice=0.9769, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.45it/s, Loss=0.6269, Dice=0.8027, IoU 
Train - Loss: 0.0325, Dice: 0.9834, IoU: 0.9675
Val   - Loss: 3.6690, Dice: 0.6559, IoU: 0.4936, F1: 0.6559
LR: 0.000195

🔥 Epoch 56/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0690, Dice=0.9598, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.51it/s, Loss=0.6300, Dice=0.8182, IoU 
Train - Loss: 0.0329, Dice: 0.9822, IoU: 0.9653
Val   - Loss: 1.5683, Dice: 0.8467, IoU: 0.7484, F1: 0.8467
LR: 0.000200

🔥 Epoch 57/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.0231, Dice=0.9874, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.52it/s, Loss=0.5198, Dice=0.8806, IoU 
Train - Loss: 0.0302, Dice: 0.9834, IoU: 0.9679
Val   - Loss: 2.0882, Dice: 0.8405, IoU: 0.7268, F1: 0.8405
LR: 0.000206

🔥 Epoch 58/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.62it/s, Loss=0.0166, Dice=0.9916, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.48it/s, Loss=0.3718, Dice=0.9291, IoU 
Train - Loss: 0.0287, Dice: 0.9842, IoU: 0.9692
Val   - Loss: 2.2244, Dice: 0.8196, IoU: 0.6984, F1: 0.8196
LR: 0.000211

🔥 Epoch 59/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.65it/s, Loss=0.0237, Dice=0.9871, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.68it/s, Loss=0.6653, Dice=0.7999, IoU 
Train - Loss: 0.0360, Dice: 0.9804, IoU: 0.9621
Val   - Loss: 3.2809, Dice: 0.6616, IoU: 0.5000, F1: 0.6616
LR: 0.000217

🔥 Epoch 60/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.85it/s, Loss=0.0773, Dice=0.9442, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.46it/s, Loss=0.2781, Dice=0.9291, IoU 
Train - Loss: 0.0366, Dice: 0.9793, IoU: 0.9601
Val   - Loss: 3.1651, Dice: 0.7830, IoU: 0.6500, F1: 0.7830
LR: 0.000222

🔥 Epoch 61/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.68it/s, Loss=0.0209, Dice=0.9920, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.33it/s, Loss=2.4450, Dice=0.0000, IoU 
Train - Loss: 0.0301, Dice: 0.9834, IoU: 0.9677
Val   - Loss: 2.6060, Dice: 0.3989, IoU: 0.2873, F1: 0.3989
LR: 0.000228

🔥 Epoch 62/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.62it/s, Loss=0.0230, Dice=0.9900, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.43it/s, Loss=0.8231, Dice=0.7931, IoU 
Train - Loss: 0.0276, Dice: 0.9841, IoU: 0.9689
Val   - Loss: 19.0418, Dice: 0.6589, IoU: 0.4972, F1: 0.6589
LR: 0.000234

🔥 Epoch 63/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.70it/s, Loss=0.0152, Dice=0.9923, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.49it/s, Loss=0.4874, Dice=0.8204, IoU 
Train - Loss: 0.0263, Dice: 0.9863, IoU: 0.9730
Val   - Loss: 3.2602, Dice: 0.6963, IoU: 0.5390, F1: 0.6963
LR: 0.000240

🔥 Epoch 64/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.0140, Dice=0.9932, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.78it/s, Loss=0.7280, Dice=0.7970, IoU 
Train - Loss: 0.0237, Dice: 0.9873, IoU: 0.9749
Val   - Loss: 3.0215, Dice: 0.6591, IoU: 0.4972, F1: 0.6591
LR: 0.000246

🔥 Epoch 65/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.81it/s, Loss=0.0359, Dice=0.9772, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.63it/s, Loss=0.6842, Dice=0.8033, IoU 
Train - Loss: 0.0219, Dice: 0.9880, IoU: 0.9764
Val   - Loss: 5.9544, Dice: 0.6642, IoU: 0.5034, F1: 0.6642
LR: 0.000251

🔥 Epoch 66/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.1574, Dice=0.9074, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.50it/s, Loss=0.8374, Dice=0.7885, IoU 
Train - Loss: 0.0299, Dice: 0.9835, IoU: 0.9681
Val   - Loss: 3.1211, Dice: 0.6539, IoU: 0.4911, F1: 0.6539
LR: 0.000257

🔥 Epoch 67/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.0112, Dice=0.9948, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.70it/s, Loss=0.5850, Dice=0.8137, IoU 
Train - Loss: 0.0250, Dice: 0.9863, IoU: 0.9737
Val   - Loss: 2.7401, Dice: 0.6759, IoU: 0.5168, F1: 0.6759
LR: 0.000264

🔥 Epoch 68/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.80it/s, Loss=0.2275, Dice=0.8412, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.72it/s, Loss=0.5109, Dice=0.8280, IoU 
Train - Loss: 0.0327, Dice: 0.9805, IoU: 0.9635
Val   - Loss: 3.0888, Dice: 0.6750, IoU: 0.5157, F1: 0.6750
LR: 0.000270

🔥 Epoch 69/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0273, Dice=0.9904, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.58it/s, Loss=0.8565, Dice=0.7866, IoU 
Train - Loss: 0.0211, Dice: 0.9893, IoU: 0.9788
Val   - Loss: 3.0955, Dice: 0.6468, IoU: 0.4850, F1: 0.6468
LR: 0.000276

🔥 Epoch 70/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0221, Dice=0.9890, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.80it/s, Loss=0.6813, Dice=0.8044, IoU 
Train - Loss: 0.0184, Dice: 0.9902, IoU: 0.9807
Val   - Loss: 2.5035, Dice: 0.6614, IoU: 0.5004, F1: 0.6614
LR: 0.000282

🔥 Epoch 71/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.74it/s, Loss=0.0126, Dice=0.9940, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.64it/s, Loss=0.5140, Dice=0.8339, IoU 
Train - Loss: 0.0235, Dice: 0.9882, IoU: 0.9768
Val   - Loss: 4.9695, Dice: 0.6841, IoU: 0.5264, F1: 0.6841
LR: 0.000288

🔥 Epoch 72/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.83it/s, Loss=0.1657, Dice=0.8957, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=0.6546, Dice=0.7642, IoU 
Train - Loss: 0.0269, Dice: 0.9848, IoU: 0.9708
Val   - Loss: 1.6365, Dice: 0.8292, IoU: 0.7225, F1: 0.8292
LR: 0.000295

🔥 Epoch 73/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0260, Dice=0.9859, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.26it/s, Loss=0.5468, Dice=0.8224, IoU 
Train - Loss: 0.0174, Dice: 0.9906, IoU: 0.9815
Val   - Loss: 2.1703, Dice: 0.8575, IoU: 0.7550, F1: 0.8575
LR: 0.000301
✅ New best model saved! Val Dice: 0.8575

🔥 Epoch 74/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.82it/s, Loss=0.0192, Dice=0.9891, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.69it/s, Loss=0.2728, Dice=0.9068, IoU 
Train - Loss: 0.0202, Dice: 0.9887, IoU: 0.9777
Val   - Loss: 4.1665, Dice: 0.7508, IoU: 0.6080, F1: 0.7508
LR: 0.000308

🔥 Epoch 75/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.85it/s, Loss=0.0138, Dice=0.9926, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.66it/s, Loss=0.8123, Dice=0.7959, IoU 
Train - Loss: 0.0193, Dice: 0.9894, IoU: 0.9791
Val   - Loss: 2.0663, Dice: 0.6603, IoU: 0.4988, F1: 0.6603
LR: 0.000314

🔥 Epoch 76/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.80it/s, Loss=0.0129, Dice=0.9927, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.60it/s, Loss=0.5605, Dice=0.8224, IoU 
Train - Loss: 0.0177, Dice: 0.9905, IoU: 0.9812
Val   - Loss: 2.1531, Dice: 0.6648, IoU: 0.5047, F1: 0.6648
LR: 0.000321

🔥 Epoch 77/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.72it/s, Loss=0.0173, Dice=0.9894, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.49it/s, Loss=0.4405, Dice=0.8692, IoU 
Train - Loss: 0.0257, Dice: 0.9844, IoU: 0.9713
Val   - Loss: 1.6157, Dice: 0.7023, IoU: 0.5488, F1: 0.7023
LR: 0.000327

🔥 Epoch 78/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.69it/s, Loss=0.0137, Dice=0.9918, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.37it/s, Loss=2.6029, Dice=0.0000, IoU 
Train - Loss: 0.0191, Dice: 0.9889, IoU: 0.9785
Val   - Loss: 5.3474, Dice: 0.3653, IoU: 0.2661, F1: 0.3653
LR: 0.000334

🔥 Epoch 79/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0092, Dice=0.9945, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.35it/s, Loss=0.4621, Dice=0.9186, IoU 
Train - Loss: 0.0213, Dice: 0.9880, IoU: 0.9767
Val   - Loss: 2.4309, Dice: 0.8721, IoU: 0.7769, F1: 0.8721
LR: 0.000340
✅ New best model saved! Val Dice: 0.8721

🔥 Epoch 80/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.67it/s, Loss=0.0097, Dice=0.9954, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.40it/s, Loss=0.3926, Dice=0.8722, IoU 
Train - Loss: 0.0169, Dice: 0.9911, IoU: 0.9823
Val   - Loss: 2.0016, Dice: 0.7110, IoU: 0.5588, F1: 0.7110
LR: 0.000347

🔥 Epoch 81/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.68it/s, Loss=0.0135, Dice=0.9942, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.66it/s, Loss=0.5093, Dice=0.8684, IoU 
Train - Loss: 0.0229, Dice: 0.9865, IoU: 0.9740
Val   - Loss: 1.9133, Dice: 0.7501, IoU: 0.6038, F1: 0.7501
LR: 0.000354

🔥 Epoch 82/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.2581, Dice=0.8239, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.65it/s, Loss=0.6310, Dice=0.7985, IoU 
Train - Loss: 0.0320, Dice: 0.9800, IoU: 0.9630
Val   - Loss: 1.7260, Dice: 0.6576, IoU: 0.4960, F1: 0.6576
LR: 0.000361

🔥 Epoch 83/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0095, Dice=0.9947, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.74it/s, Loss=2.1791, Dice=0.0000, IoU 
Train - Loss: 0.0173, Dice: 0.9894, IoU: 0.9792
Val   - Loss: 5.5948, Dice: 0.3579, IoU: 0.2623, F1: 0.3579
LR: 0.000368

🔥 Epoch 84/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0731, Dice=0.9524, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.70it/s, Loss=2.7513, Dice=0.0000, IoU 
Train - Loss: 0.0211, Dice: 0.9880, IoU: 0.9764
Val   - Loss: 3.8731, Dice: 0.3765, IoU: 0.2810, F1: 0.3765
LR: 0.000374

🔥 Epoch 85/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.81it/s, Loss=0.0071, Dice=0.9969, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=0.5106, Dice=0.8396, IoU 
Train - Loss: 0.0163, Dice: 0.9905, IoU: 0.9813
Val   - Loss: 5.9345, Dice: 0.6790, IoU: 0.5211, F1: 0.6790
LR: 0.000381

🔥 Epoch 86/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.0117, Dice=0.9946, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.64it/s, Loss=0.3078, Dice=0.8929, IoU 
Train - Loss: 0.0199, Dice: 0.9883, IoU: 0.9772
Val   - Loss: 2.4625, Dice: 0.7402, IoU: 0.5940, F1: 0.7402
LR: 0.000388

🔥 Epoch 87/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.75it/s, Loss=0.0218, Dice=0.9868, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.47it/s, Loss=0.6375, Dice=0.8033, IoU 
Train - Loss: 0.0155, Dice: 0.9913, IoU: 0.9829
Val   - Loss: 2.3179, Dice: 0.6588, IoU: 0.4971, F1: 0.6588
LR: 0.000395

🔥 Epoch 88/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.71it/s, Loss=0.0130, Dice=0.9946, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.66it/s, Loss=0.7002, Dice=0.7981, IoU 
Train - Loss: 0.0139, Dice: 0.9927, IoU: 0.9855
Val   - Loss: 2.0448, Dice: 0.6529, IoU: 0.4905, F1: 0.6529
LR: 0.000402

🔥 Epoch 89/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.80it/s, Loss=0.0095, Dice=0.9941, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.58it/s, Loss=0.4422, Dice=0.8381, IoU 
Train - Loss: 0.0162, Dice: 0.9911, IoU: 0.9824
Val   - Loss: 2.2528, Dice: 0.6894, IoU: 0.5329, F1: 0.6894
LR: 0.000409

🔥 Epoch 90/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.74it/s, Loss=0.0193, Dice=0.9895, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.75it/s, Loss=0.7767, Dice=0.7761, IoU 
Train - Loss: 0.0166, Dice: 0.9905, IoU: 0.9813
Val   - Loss: 3.9950, Dice: 0.6433, IoU: 0.4793, F1: 0.6433
LR: 0.000416

🔥 Epoch 91/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.63it/s, Loss=0.0229, Dice=0.9858, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.45it/s, Loss=0.3597, Dice=0.8491, IoU 
Train - Loss: 0.0157, Dice: 0.9906, IoU: 0.9814
Val   - Loss: 1.3497, Dice: 0.7009, IoU: 0.5463, F1: 0.7009
LR: 0.000423

🔥 Epoch 92/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.62it/s, Loss=0.0153, Dice=0.9910, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.17it/s, Loss=0.8647, Dice=0.7815, IoU 
Train - Loss: 0.0140, Dice: 0.9919, IoU: 0.9840
Val   - Loss: 2.6404, Dice: 0.6423, IoU: 0.4780, F1: 0.6423
LR: 0.000430

🔥 Epoch 93/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.76it/s, Loss=0.0095, Dice=0.9942, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.75it/s, Loss=0.4493, Dice=0.8318, IoU 
Train - Loss: 0.0136, Dice: 0.9920, IoU: 0.9841
Val   - Loss: 1.3644, Dice: 0.6958, IoU: 0.5392, F1: 0.6958
LR: 0.000437

🔥 Epoch 94/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.78it/s, Loss=0.0255, Dice=0.9844, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  8.53it/s, Loss=0.4422, Dice=0.8511, IoU 
Train - Loss: 0.0173, Dice: 0.9898, IoU: 0.9800
Val   - Loss: 1.3774, Dice: 0.7080, IoU: 0.5551, F1: 0.7080
LR: 0.000445

🔥 Epoch 95/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.0249, Dice=0.9881, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.75it/s, Loss=0.3985, Dice=0.8604, IoU 
Train - Loss: 0.0146, Dice: 0.9919, IoU: 0.9840
Val   - Loss: 1.2044, Dice: 0.7276, IoU: 0.5781, F1: 0.7276
LR: 0.000452

🔥 Epoch 96/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.79it/s, Loss=0.0167, Dice=0.9910, IoU
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.39it/s, Loss=0.6314, Dice=0.8351, IoU 
Train - Loss: 0.0138, Dice: 0.9921, IoU: 0.9844
Val   - Loss: 3.4574, Dice: 0.6828, IoU: 0.5245, F1: 0.6828
LR: 0.000459

🔥 Epoch 97/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.72it/s, Loss=0.0077, Dice=0.9948, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.73it/s, Loss=0.4344, Dice=0.8500, IoU 
Train - Loss: 0.0120, Dice: 0.9928, IoU: 0.9858
Val   - Loss: 3.6446, Dice: 0.7115, IoU: 0.5581, F1: 0.7115
LR: 0.000466

🔥 Epoch 98/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0216, Dice=0.9857, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.43it/s, Loss=0.4903, Dice=0.8455, IoU 
Train - Loss: 0.0121, Dice: 0.9928, IoU: 0.9858
Val   - Loss: 2.0542, Dice: 0.6938, IoU: 0.5378, F1: 0.6938
LR: 0.000473

🔥 Epoch 99/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.77it/s, Loss=0.0280, Dice=0.9842, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.67it/s, Loss=0.9593, Dice=0.6311, IoU 
Train - Loss: 0.0117, Dice: 0.9933, IoU: 0.9868
Val   - Loss: 0.9638, Dice: 0.7166, IoU: 0.6004, F1: 0.7166
LR: 0.000480

🔥 Epoch 100/100
🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.82it/s, Loss=0.0063, Dice=0.9967, IoU 
🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.61it/s, Loss=0.2180, Dice=0.9353, IoU 
Train - Loss: 0.0110, Dice: 0.9938, IoU: 0.9877
Val   - Loss: 0.5248, Dice: 0.8846, IoU: 0.7942, F1: 0.8846
LR: 0.000488
✅ New best model saved! Val Dice: 0.8846

🎉 Training completed in 0.14 hours
🏆 Best validation Dice: 0.8846

================================================================================
🚀 BREAKTHROUGH U-NET V4 TRAINING COMPLETED!
================================================================================
🎯 Best Dice: 0.8846
✅ Excellent performance! Consider V4-28 for breakthrough.

📊 FINAL COMPARISON:
V1: 0.9100 Dice (17.3M params)
V4: 0.8846 Dice (5.1M params)
Improvement: -2.79%
Efficiency: 71% fewer parameters

🎯 NEXT STEPS: Try V4-28 or V4-32 for breakthrough!
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_breakthrough_v4.py
Get-Process : A positional parameter cannot be found that accepts argument 
'Engti\OneDrive\Desktop\a1'.
At line:1 char:1
+ PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_ ...
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : InvalidArgument: (:) [Get-Process], ParameterBindingException    
    + FullyQualifiedErrorId : PositionalParameterNotFound,Microsoft.PowerShell.Commands.GetPr  
   ocessCommand

PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 🚀 BREAKTHROUGH U-NET V4 TRAINING
🚀 : The term '🚀' is not recognized as the name of a cmdlet, function, script file, or 
operable program. Check the spelling of the name, or if a path was included, verify that the   
path is correct and try again.
At line:1 char:1
+ 🚀 BREAKTHROUGH U-NET V4 TRAINING
+ ~~
    + CategoryInfo          : ObjectNotFound: (🚀:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> ============================================================
============================================================ : The term 
'============================================================' is not recognized as the name   
of a cmdlet, function, script file, or operable program. Check the spelling of the name, or    
if a path was included, verify that the path is correct and try again.
At line:1 char:1
+ ============================================================
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : ObjectNotFound: (===============...===============:String) [],   
   CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> �� TARGET: Exceed V1's 0.9100 Dice with fewer parameters!
>> Configuration: {
>>   "dataset_dir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\data\\augmented\\v5.2\\augmented_20250518_153326",
>>   "save_dir": "chess_board_detection/breakthrough_v4_results",
>>   "epochs": 100,
>>   "batch_size": 4,
>>   "learning_rate": 0.001,
>>   "base_channels": 20,
>>   "accumulation_steps": 2,
>>   "num_workers": 0,
>>   "use_knowledge_distillation": false
>> }
>> Using device: cuda
>> Creating enhanced dataloaders...
>> Found 102 total sample folders
>> Train samples: 81
>> Val samples: 21
>> Found 81 valid samples
>> Found 21 valid samples
>> Creating Breakthrough U-Net V4...
>> Model parameters: 3,296,335
>> Efficiency vs V1: 0.191x (80.9% reduction)
>> 🎯 Target: Achieve 0.91+ Dice with 3,296,335 params!
>> C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v4.py:306: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
>>   scaler = GradScaler()
>> 🚀 Starting breakthrough training for 100 epochs...
>>
>> 🔥 Epoch 1/100
>> 🚀 Breakthrough Training:   0%|                                        | 0/21 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
�� : The term '��' is not recognized as the name of a cmdlet, function, script file, or 
operable program. Check the spelling of the name, or if a path was included, verify that the   
path is correct and try again.
At line:1 char:1
+ �� TARGET: Exceed V1's 0.9100 Dice with fewer parameters!
+ ~~
    + CategoryInfo          : ObjectNotFound: (��:String) [], CommandNotFoundException
    + FullyQualifiedErrorId : CommandNotFoundException

PS C:\Users\<USER>\OneDrive\Desktop\a1 v1>   image = torch.load(image_path, map_location='cpu')
At line:1 char:32
+   image = torch.load(image_path, map_location='cpu')
+                                ~
Missing argument in parameter list.
    + CategoryInfo          : ParserError: (:) [], ParentContainsErrorRecordException
    + FullyQualifiedErrorId : MissingArgument

PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
>>   mask = torch.load(mask_path, map_location='cpu')
>> C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v4.py:151: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
>>   with autocast():
>> �� Breakthrough Training: 100%|█| 21/21 [00:06<00:00,  3.50it/s, Loss=0.6248, Dice=0.3987, IoU
>> 🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  6.40it/s, Loss=1.0164, Dice=0.0000, IoU
>> Train - Loss: 0.6816, Dice: 0.3569, IoU: 0.2274
>> Val   - Loss: 1.1877, Dice: 0.3412, IoU: 0.2261, F1: 0.3412
>> LR: 0.000040
>> ✅ New best model saved! Val Dice: 0.3412
>>
>> 🔥 Epoch 2/100
>> 🚀 Breakthrough Training: 100%|█| 21/21 [00:04<00:00,  4.80it/s, Loss=0.5450, Dice=0.5475, IoU
>> 🎯 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  9.48it/s, Loss=0.9884, Dice=0.0000, IoU
>> Train - Loss: 0.6062, Dice: 0.4688, IoU: 0.3155
>> Val   - Loss: 1.1866, Dice: 0.2974, IoU: 0.1881, F1: 0.2974
>> LR: 0.000040
>>