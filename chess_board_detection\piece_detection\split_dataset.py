import os
import shutil
import random
import argparse
from pathlib import Path

def split_dataset(input_dir, output_dir, train_ratio=0.8, seed=42):
    """
    Split the labeled dataset into training and validation sets.
    
    Args:
        input_dir: Directory containing the labeled images and annotations
        output_dir: Directory to save the split dataset
        train_ratio: Ratio of images to use for training (default: 0.8)
        seed: Random seed for reproducibility
    """
    random.seed(seed)
    
    # Create output directories
    train_img_dir = os.path.join(output_dir, 'images', 'train')
    val_img_dir = os.path.join(output_dir, 'images', 'val')
    train_label_dir = os.path.join(output_dir, 'labels', 'train')
    val_label_dir = os.path.join(output_dir, 'labels', 'val')
    
    os.makedirs(train_img_dir, exist_ok=True)
    os.makedirs(val_img_dir, exist_ok=True)
    os.makedirs(train_label_dir, exist_ok=True)
    os.makedirs(val_label_dir, exist_ok=True)
    
    # Get all image files
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png']:
        image_files.extend(list(Path(input_dir).glob(f'*{ext}')))
    
    # Shuffle and split
    random.shuffle(image_files)
    split_idx = int(len(image_files) * train_ratio)
    train_files = image_files[:split_idx]
    val_files = image_files[split_idx:]
    
    print(f"Total images: {len(image_files)}")
    print(f"Training images: {len(train_files)}")
    print(f"Validation images: {len(val_files)}")
    
    # Copy files to their respective directories
    for img_path in train_files:
        # Copy image
        shutil.copy(img_path, os.path.join(train_img_dir, img_path.name))
        
        # Copy corresponding label if it exists
        label_path = os.path.join(input_dir, 'labels', f"{img_path.stem}.txt")
        if os.path.exists(label_path):
            shutil.copy(label_path, os.path.join(train_label_dir, f"{img_path.stem}.txt"))
        else:
            print(f"Warning: No label file found for {img_path.name}")
    
    for img_path in val_files:
        # Copy image
        shutil.copy(img_path, os.path.join(val_img_dir, img_path.name))
        
        # Copy corresponding label if it exists
        label_path = os.path.join(input_dir, 'labels', f"{img_path.stem}.txt")
        if os.path.exists(label_path):
            shutil.copy(label_path, os.path.join(val_label_dir, f"{img_path.stem}.txt"))
        else:
            print(f"Warning: No label file found for {img_path.name}")
    
    print(f"Dataset split complete. Files saved to {output_dir}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Split dataset into train and validation sets")
    parser.add_argument("--input_dir", type=str, required=True, help="Directory containing labeled images")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory to save split dataset")
    parser.add_argument("--train_ratio", type=float, default=0.8, help="Ratio of images for training (default: 0.8)")
    parser.add_argument("--seed", type=int, default=42, help="Random seed (default: 42)")
    
    args = parser.parse_args()
    split_dataset(args.input_dir, args.output_dir, args.train_ratio, args.seed)
