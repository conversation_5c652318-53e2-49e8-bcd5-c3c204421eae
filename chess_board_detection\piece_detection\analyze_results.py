"""
Analyze chess piece detection results and generate a confusion matrix.
This script:
1. Takes ground truth annotations and detection results
2. Generates a confusion matrix
3. Visualizes the results
"""

import os
import sys
import argparse
import numpy as np
import matplotlib.pyplot as plt
import json
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay
import cv2
from ultralytics import YOLO

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def load_ground_truth(annotation_file):
    """
    Load ground truth annotations from a JSON file.
    
    Args:
        annotation_file: Path to the annotation file
        
    Returns:
        ground_truth: Dictionary mapping chess coordinates to piece names
    """
    with open(annotation_file, 'r') as f:
        annotations = json.load(f)
    
    ground_truth = {}
    
    for item in annotations:
        image_path = item.get('image_path', '')
        pieces = item.get('pieces', [])
        
        image_ground_truth = {}
        for piece in pieces:
            coord = piece.get('coord', '')
            piece_type = piece.get('type', '')
            color = piece.get('color', '')
            
            if coord and piece_type and color:
                class_name = f"{color}_{piece_type}"
                image_ground_truth[coord] = class_name
        
        ground_truth[image_path] = image_ground_truth
    
    return ground_truth

def detect_pieces(model, image_path, confidence=0.25):
    """
    Detect chess pieces in an image.
    
    Args:
        model: The YOLO model
        image_path: Path to the input image
        confidence: Detection confidence threshold
        
    Returns:
        detections: Dictionary mapping chess coordinates to piece names and confidences
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        print(f"Could not load image from {image_path}")
        return {}
    
    # Run inference
    results = model(image, conf=confidence)
    
    # Extract detections
    detections = {}
    
    for result in results:
        boxes = result.boxes
        
        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes.xyxy[i].tolist()
            confidence = boxes.conf[i].item()
            class_id = int(boxes.cls[i].item())
            class_name = result.names[class_id]
            
            # Calculate center point
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            # Calculate chess coordinates
            board_size = image.shape[:2]
            square_width = board_size[1] / 8
            square_height = board_size[0] / 8
            
            file_idx = int(center_x / square_width)
            rank_idx = int(center_y / square_height)
            
            # Convert to chess notation (A1-H8)
            file_letter = chr(ord('a') + file_idx)
            rank_number = 8 - rank_idx
            
            # Create chess coordinate
            chess_coord = f"{file_letter}{rank_number}"
            
            # Store in dictionary (keep highest confidence piece if multiple detections in same square)
            if chess_coord not in detections or confidence > detections[chess_coord]['confidence']:
                detections[chess_coord] = {
                    'piece': class_name,
                    'confidence': confidence
                }
    
    return detections

def generate_confusion_matrix(ground_truth, detections, class_names):
    """
    Generate a confusion matrix from ground truth and detections.
    
    Args:
        ground_truth: Dictionary mapping chess coordinates to piece names
        detections: Dictionary mapping chess coordinates to piece names and confidences
        class_names: List of class names
        
    Returns:
        cm: Confusion matrix
        y_true: True labels
        y_pred: Predicted labels
    """
    y_true = []
    y_pred = []
    
    # Add empty class for empty squares
    all_classes = ['empty'] + class_names
    class_to_idx = {class_name: i for i, class_name in enumerate(all_classes)}
    
    # Process all coordinates on the chess board
    for file_letter in 'abcdefgh':
        for rank_number in range(1, 9):
            coord = f"{file_letter}{rank_number}"
            
            # Get ground truth
            true_class = ground_truth.get(coord, 'empty')
            true_idx = class_to_idx.get(true_class, 0)  # Default to empty
            
            # Get prediction
            if coord in detections:
                pred_class = detections[coord]['piece']
                pred_idx = class_to_idx.get(pred_class, 0)  # Default to empty
            else:
                pred_idx = 0  # Empty
            
            y_true.append(true_idx)
            y_pred.append(pred_idx)
    
    # Generate confusion matrix
    cm = confusion_matrix(y_true, y_pred, labels=range(len(all_classes)))
    
    return cm, y_true, y_pred, all_classes

def visualize_confusion_matrix(cm, class_names, output_path):
    """
    Visualize the confusion matrix.
    
    Args:
        cm: Confusion matrix
        class_names: List of class names
        output_path: Path to save the visualization
    """
    plt.figure(figsize=(12, 10))
    disp = ConfusionMatrixDisplay(confusion_matrix=cm, display_labels=class_names)
    disp.plot(cmap=plt.cm.Blues, xticks_rotation=45)
    plt.title('Chess Piece Detection Confusion Matrix')
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

def analyze_results(model_path, annotation_file, image_dir, output_dir):
    """
    Analyze chess piece detection results.
    
    Args:
        model_path: Path to the YOLO model
        annotation_file: Path to the annotation file
        image_dir: Directory containing input images
        output_dir: Directory to save the output
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load model
    print(f"Loading YOLO model from {model_path}")
    model = YOLO(model_path)
    
    # Load ground truth
    print(f"Loading ground truth from {annotation_file}")
    ground_truth_all = load_ground_truth(annotation_file)
    
    # Define class names
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]
    
    # Process each image
    all_y_true = []
    all_y_pred = []
    
    for image_path, ground_truth in ground_truth_all.items():
        # Get full image path
        full_image_path = os.path.join(image_dir, os.path.basename(image_path))
        
        if not os.path.exists(full_image_path):
            print(f"Image not found: {full_image_path}")
            continue
        
        print(f"Processing {full_image_path}")
        
        # Detect pieces
        detections = detect_pieces(model, full_image_path)
        
        # Generate confusion matrix
        cm, y_true, y_pred, all_classes = generate_confusion_matrix(ground_truth, detections, class_names)
        
        # Accumulate results
        all_y_true.extend(y_true)
        all_y_pred.extend(y_pred)
        
        # Visualize confusion matrix
        output_path = os.path.join(output_dir, f"{os.path.splitext(os.path.basename(image_path))[0]}_cm.png")
        visualize_confusion_matrix(cm, all_classes, output_path)
    
    # Generate overall confusion matrix
    overall_cm = confusion_matrix(all_y_true, all_y_pred, labels=range(len(all_classes)))
    output_path = os.path.join(output_dir, "overall_cm.png")
    visualize_confusion_matrix(overall_cm, all_classes, output_path)
    
    # Calculate metrics
    accuracy = np.sum(np.diag(overall_cm)) / np.sum(overall_cm)
    print(f"Overall accuracy: {accuracy:.4f}")
    
    # Calculate per-class metrics
    print("\nPer-class metrics:")
    for i, class_name in enumerate(all_classes):
        if np.sum(overall_cm[i, :]) > 0:
            precision = overall_cm[i, i] / np.sum(overall_cm[:, i])
            recall = overall_cm[i, i] / np.sum(overall_cm[i, :])
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            print(f"{class_name}: Precision={precision:.4f}, Recall={recall:.4f}, F1={f1:.4f}")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Analyze chess piece detection results')
    parser.add_argument('--model_path', type=str, required=True,
                        help='Path to YOLO model')
    parser.add_argument('--annotation_file', type=str, required=True,
                        help='Path to annotation file')
    parser.add_argument('--image_dir', type=str, required=True,
                        help='Directory containing input images')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/outputs/analysis',
                        help='Directory to save the output')
    args = parser.parse_args()
    
    # Analyze results
    analyze_results(args.model_path, args.annotation_file, args.image_dir, args.output_dir)

if __name__ == "__main__":
    main()
