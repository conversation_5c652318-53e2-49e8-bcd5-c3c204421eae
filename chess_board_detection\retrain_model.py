"""
<PERSON><PERSON>t to retrain the chess board detection model with improved corner detection.
"""

import os
import argparse
import torch
import torch.optim as optim
from tqdm import tqdm

from models.unet import ChessBoardUNet
from utils.real_dataset import get_data_loaders
from train_real import <PERSON>ceLoss, HeatmapLoss, train_model
from config import (
    DATA_DIR, MODELS_DIR, DEVICE,
    LEARNING_RATE, NUM_EPOCHS, INPUT_SIZE
)


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Retrain Chess Board Detection Model')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs to train')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--separation_weight', type=float, default=0.2, 
                        help='Weight for separation loss in heatmap loss')
    parser.add_argument('--heatmap_weight', type=float, default=1.5, 
                        help='Weight for heatmap loss in total loss')
    parser.add_argument('--model_name', type=str, default='improved_model.pth',
                        help='Name for the saved model')
    parser.add_argument('--load_model', type=str, default=None,
                        help='Path to existing model to continue training from')
    args = parser.parse_args()

    # Create directories if they don't exist
    os.makedirs(MODELS_DIR, exist_ok=True)

    # Get data loaders
    print("Loading data...")
    dataloaders = get_data_loaders(DATA_DIR, batch_size=args.batch_size)

    # Initialize model
    print("Initializing model...")
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    
    # Load existing model if specified
    if args.load_model:
        model_path = args.load_model
        if not os.path.isabs(model_path):
            model_path = os.path.join(MODELS_DIR, model_path)
        
        print(f"Loading existing model from {model_path}")
        model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    
    model = model.to(DEVICE)
    print(f"Model moved to {DEVICE}")

    # Define loss functions and optimizer
    criterion_seg = DiceLoss()
    criterion_heatmap = HeatmapLoss(separation_weight=args.separation_weight)
    optimizer = optim.Adam(model.parameters(), lr=args.lr)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Define a custom training function to use the heatmap weight
    def custom_train_model(model, dataloaders, criterion_seg, criterion_heatmap, optimizer, num_epochs=25):
        """
        Custom training function that uses the specified heatmap weight.
        """
        # Create a wrapper for the train_model function
        def custom_train_step(model, inputs, masks, heatmaps, optimizer, phase):
            # Zero the parameter gradients
            optimizer.zero_grad()

            # Forward
            with torch.set_grad_enabled(phase == 'train'):
                outputs = model(inputs)
                seg_outputs = outputs['segmentation']
                heatmap_outputs = outputs['corner_heatmaps']

                # Calculate losses
                seg_loss = criterion_seg(seg_outputs, masks)
                heatmap_loss = criterion_heatmap(heatmap_outputs, heatmaps)

                # Combined loss with custom weight for heatmap loss
                loss = seg_loss + args.heatmap_weight * heatmap_loss

                # Backward + optimize only if in training phase
                if phase == 'train':
                    loss.backward()
                    optimizer.step()

            return loss, seg_loss, heatmap_loss

        # Call the original train_model function with our custom train step
        from types import MethodType
        train_model.__globals__['custom_train_step'] = custom_train_step
        
        return train_model(model, dataloaders, criterion_seg, criterion_heatmap, optimizer, num_epochs)

    # Train model
    print(f"Starting training for {args.epochs} epochs...")
    model, history = custom_train_model(
        model=model,
        dataloaders={'train': dataloaders['train'], 'val': dataloaders['val']},
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap,
        optimizer=optimizer,
        num_epochs=args.epochs
    )

    # Save the model
    model_save_path = os.path.join(MODELS_DIR, args.model_name)
    torch.save(model.state_dict(), model_save_path)
    print(f"Model saved to {model_save_path}")

    # Save training history
    import json
    history_save_path = os.path.join(MODELS_DIR, f"training_history_{args.model_name.split('.')[0]}.json")
    with open(history_save_path, 'w') as f:
        json.dump(history, f)
    print(f"Training history saved to {history_save_path}")

    print("Training completed!")


if __name__ == "__main__":
    main()
