"""
Export chess board detection model for mobile deployment.

This script loads a trained model and exports it to TorchScript format
optimized for mobile deployment.
"""

import os
import argparse
import torch
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from utils.model_optimization import optimize_for_mobile_deployment
from config import MODELS_DIR, INPUT_SIZE


def main():
    parser = argparse.ArgumentParser(description='Export chess board detection model for mobile')
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'deployment'),
                        help='Output directory for mobile model')
    parser.add_argument('--quantize', action='store_true',
                        help='Quantize model for mobile deployment')
    args = parser.parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Set device
    device = torch.device('cpu')  # Mobile optimization is done on CPU
    print(f"Using device: {device}")

    # Initialize model
    print("Initializing model...")
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    
    # Load checkpoint
    print(f"Loading checkpoint from {args.checkpoint}...")
    checkpoint = torch.load(args.checkpoint, map_location=device)
    model.load_state_dict(checkpoint)
    
    # Set model to evaluation mode
    model.eval()
    
    # Create sample input
    height, width = INPUT_SIZE
    dummy_input = torch.randn(1, 3, height, width)
    
    # Define output path
    model_name = os.path.splitext(os.path.basename(args.checkpoint))[0]
    output_path = os.path.join(args.output_dir, f"{model_name}_mobile.pt")
    
    # Quantize model if requested
    if args.quantize:
        try:
            from utils.model_optimization import quantize_model
            print("Quantizing model...")
            model = quantize_model(model)
            output_path = os.path.join(args.output_dir, f"{model_name}_mobile_quantized.pt")
        except Exception as e:
            print(f"Error quantizing model: {e}")
            print("Continuing with non-quantized model...")
    
    # Export model for mobile
    print(f"Exporting model for mobile deployment...")
    optimize_for_mobile_deployment(
        model=model,
        sample_input=dummy_input,
        output_path=output_path
    )
    
    print(f"Model successfully exported for mobile deployment: {output_path}")
    
    # Additional information for mobile deployment
    print("\nMobile Deployment Information:")
    print("1. For Android deployment, use the PyTorch Android API:")
    print("   - Add implementation 'org.pytorch:pytorch_android:1.10.0' to your build.gradle")
    print("   - Add implementation 'org.pytorch:pytorch_android_torchvision:1.10.0'")
    print("2. For iOS deployment, use the PyTorch iOS API:")
    print("   - Add pod 'LibTorch', '~> 1.10.0' to your Podfile")
    print("3. Copy the exported model to your mobile app's assets folder")
    print("4. Load the model in your app using the PyTorch Mobile API")
    print("5. For more information, visit: https://pytorch.org/mobile/home/")


if __name__ == "__main__":
    main()
