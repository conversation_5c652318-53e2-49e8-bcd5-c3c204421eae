Chess Piece Detection - Single-Stage Approach
==================================================

Original Best Model (Epoch 86) Detections:
--------------------------------------------------
Detection #1: black_pawn (Confidence: 0.9474)
   Bounding Box: [246, 212, 267, 242]
Detection #2: black_pawn (Confidence: 0.9444)
   Bounding Box: [349, 295, 371, 326]
Detection #3: black_pawn (Confidence: 0.9409)
   Bounding Box: [185, 300, 205, 328]
Detection #4: white_pawn (Confidence: 0.9401)
   Bounding Box: [310, 75, 331, 107]
Detection #5: black_queen (Confidence: 0.9401)
   Bounding Box: [247, 333, 273, 368]
Detection #6: black_knight (Confidence: 0.9399)
   Bounding Box: [183, 254, 205, 287]
Detection #7: black_bishop (Confidence: 0.9361)
   Bounding Box: [248, 249, 268, 286]
Detection #8: white_bishop (Confidence: 0.9343)
   Bounding Box: [246, 164, 265, 200]
Detection #9: black_pawn (Confidence: 0.9335)
   Bounding Box: [124, 301, 143, 329]
Detection #10: black_rook (Confidence: 0.9334)
   Bounding Box: [281, 249, 301, 282]
Detection #11: black_pawn (Confidence: 0.9243)
   Bounding Box: [154, 300, 174, 330]
Detection #12: white_rook (Confidence: 0.9236)
   Bounding Box: [241, 33, 261, 69]
Detection #13: white_bishop (Confidence: 0.9226)
   Bounding Box: [244, 121, 264, 156]
Detection #14: white_pawn (Confidence: 0.9221)
   Bounding Box: [146, 92, 165, 122]
Detection #15: white_pawn (Confidence: 0.9203)
   Bounding Box: [115, 94, 135, 125]
Detection #16: white_knight (Confidence: 0.9191)
   Bounding Box: [346, 160, 369, 194]
Detection #17: black_bishop (Confidence: 0.9148)
   Bounding Box: [350, 333, 371, 368]
Detection #18: white_pawn (Confidence: 0.9096)
   Bounding Box: [344, 72, 366, 105]
Detection #19: black_rook (Confidence: 0.9067)
   Bounding Box: [187, 335, 206, 368]
Detection #20: black_queen (Confidence: 0.9035)
   Bounding Box: [154, 336, 177, 370]
Detection #21: white_king (Confidence: 0.8917)
   Bounding Box: [142, 40, 165, 80]
Detection #22: white_pawn (Confidence: 0.8912)
   Bounding Box: [178, 88, 197, 119]
Detection #23: white_rook (Confidence: 0.8843)
   Bounding Box: [208, 36, 228, 73]
Detection #24: white_queen (Confidence: 0.8781)
   Bounding Box: [307, 114, 334, 151]

Total detections: 24
Average confidence: 0.9209

==================================================

Epoch 107 (Best mAP) Detections:
--------------------------------------------------
Detection #1: white_pawn (Confidence: 0.9527)
   Bounding Box: [146, 91, 165, 122]
Detection #2: black_pawn (Confidence: 0.9517)
   Bounding Box: [246, 212, 267, 242]
Detection #3: white_pawn (Confidence: 0.9515)
   Bounding Box: [309, 75, 331, 107]
Detection #4: black_bishop (Confidence: 0.9454)
   Bounding Box: [247, 249, 268, 286]
Detection #5: black_queen (Confidence: 0.9447)
   Bounding Box: [247, 333, 273, 368]
Detection #6: black_pawn (Confidence: 0.9431)
   Bounding Box: [349, 295, 371, 326]
Detection #7: white_knight (Confidence: 0.9420)
   Bounding Box: [345, 160, 369, 194]
Detection #8: white_pawn (Confidence: 0.9414)
   Bounding Box: [114, 93, 135, 125]
Detection #9: black_rook (Confidence: 0.9411)
   Bounding Box: [281, 249, 302, 283]
Detection #10: black_pawn (Confidence: 0.9409)
   Bounding Box: [185, 299, 205, 328]
Detection #11: white_bishop (Confidence: 0.9380)
   Bounding Box: [245, 164, 266, 200]
Detection #12: black_bishop (Confidence: 0.9378)
   Bounding Box: [350, 333, 372, 367]
Detection #13: white_rook (Confidence: 0.9376)
   Bounding Box: [208, 36, 228, 73]
Detection #14: white_pawn (Confidence: 0.9367)
   Bounding Box: [177, 87, 197, 119]
Detection #15: black_pawn (Confidence: 0.9350)
   Bounding Box: [154, 300, 174, 329]
Detection #16: black_pawn (Confidence: 0.9350)
   Bounding Box: [124, 300, 143, 330]
Detection #17: white_rook (Confidence: 0.9306)
   Bounding Box: [241, 34, 261, 69]
Detection #18: white_pawn (Confidence: 0.9296)
   Bounding Box: [344, 72, 366, 104]
Detection #19: white_bishop (Confidence: 0.9267)
   Bounding Box: [244, 121, 264, 156]
Detection #20: white_queen (Confidence: 0.9248)
   Bounding Box: [307, 114, 335, 151]
Detection #21: black_king (Confidence: 0.9183)
   Bounding Box: [153, 336, 177, 370]
Detection #22: black_rook (Confidence: 0.9037)
   Bounding Box: [187, 335, 206, 368]
Detection #23: white_king (Confidence: 0.8787)
   Bounding Box: [142, 41, 166, 80]
Detection #24: black_knight (Confidence: 0.8787)
   Bounding Box: [182, 255, 205, 287]
Detection #25: white_king (Confidence: 0.5272)
   Bounding Box: [34, 336, 53, 361]

Total detections: 25
Average confidence: 0.9157

==================================================

Epoch 111 (Best Loss) Detections:
--------------------------------------------------
Detection #1: black_pawn (Confidence: 0.9559)
   Bounding Box: [246, 212, 267, 242]
Detection #2: black_bishop (Confidence: 0.9530)
   Bounding Box: [247, 249, 268, 285]
Detection #3: black_queen (Confidence: 0.9522)
   Bounding Box: [247, 333, 273, 368]
Detection #4: white_pawn (Confidence: 0.9497)
   Bounding Box: [146, 91, 166, 121]
Detection #5: white_pawn (Confidence: 0.9471)
   Bounding Box: [309, 75, 331, 107]
Detection #6: white_bishop (Confidence: 0.9427)
   Bounding Box: [244, 121, 264, 156]
Detection #7: black_rook (Confidence: 0.9426)
   Bounding Box: [281, 249, 302, 282]
Detection #8: white_pawn (Confidence: 0.9424)
   Bounding Box: [114, 94, 135, 124]
Detection #9: black_pawn (Confidence: 0.9420)
   Bounding Box: [124, 301, 143, 329]
Detection #10: black_bishop (Confidence: 0.9420)
   Bounding Box: [350, 334, 371, 367]
Detection #11: black_pawn (Confidence: 0.9385)
   Bounding Box: [185, 299, 205, 328]
Detection #12: white_rook (Confidence: 0.9334)
   Bounding Box: [241, 34, 262, 68]
Detection #13: white_rook (Confidence: 0.9332)
   Bounding Box: [208, 36, 229, 73]
Detection #14: white_knight (Confidence: 0.9311)
   Bounding Box: [346, 160, 369, 193]
Detection #15: black_pawn (Confidence: 0.9281)
   Bounding Box: [349, 295, 371, 325]
Detection #16: white_pawn (Confidence: 0.9232)
   Bounding Box: [177, 88, 197, 119]
Detection #17: white_bishop (Confidence: 0.9195)
   Bounding Box: [246, 164, 266, 200]
Detection #18: white_pawn (Confidence: 0.9192)
   Bounding Box: [344, 72, 366, 104]
Detection #19: black_pawn (Confidence: 0.9076)
   Bounding Box: [154, 300, 174, 329]
Detection #20: black_rook (Confidence: 0.8933)
   Bounding Box: [187, 335, 206, 367]
Detection #21: white_king (Confidence: 0.8840)
   Bounding Box: [142, 41, 165, 79]
Detection #22: black_king (Confidence: 0.8642)
   Bounding Box: [154, 336, 177, 369]
Detection #23: black_knight (Confidence: 0.8382)
   Bounding Box: [183, 255, 205, 287]
Detection #24: white_queen (Confidence: 0.8348)
   Bounding Box: [307, 114, 334, 150]
Detection #25: white_king (Confidence: 0.6586)
   Bounding Box: [33, 334, 54, 362]

Total detections: 25
Average confidence: 0.9111

==================================================

