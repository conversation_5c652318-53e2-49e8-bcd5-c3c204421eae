"""
V6 Enhanced FEN Generation Script:
Three-Stage Chess Position Analysis with Breakthrough V6 Segmentation:
1. Detect chessboard using V6 breakthrough segmentation model (0.9391 Dice)
2. Detect chess pieces using YOLO model
3. Generate FEN notation from detected pieces

Uses the superior V6 model for precise chess board detection.
"""

import os
import sys
import cv2
import numpy as np
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
import argparse
import time

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the breakthrough V6 model
from chess_board_detection.models.breakthrough_unet_v6_simple import get_breakthrough_v6_model

# Configuration
CONFIG = {
    "v6_model": "chess_board_detection/breakthrough_v6_results/best_model.pth",
    "piece_model": "chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt",
    "fen_symbols": {
        "w_pawn": "P", "w_knight": "N", "w_bishop": "B", "w_rook": "R", "w_queen": "Q", "w_king": "K",
        "b_pawn": "p", "b_knight": "n", "b_bishop": "b", "b_rook": "r", "b_queen": "q", "b_king": "k"
    }
}

def load_v6_model(model_path, device):
    """Load the breakthrough V6 segmentation model."""
    print(f"🚀 Loading V6 breakthrough model from: {model_path}")
    
    # Create V6 model
    model = get_breakthrough_v6_model(base_channels=32)
    
    # Load weights
    if os.path.exists(model_path):
        state_dict = torch.load(model_path, map_location=device, weights_only=True)
        model.load_state_dict(state_dict)
        print("✅ V6 model loaded successfully")
    else:
        print(f"❌ V6 model file not found: {model_path}")
        return None
    
    model = model.to(device)
    model.eval()
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"📊 V6 Parameters: {total_params:,} (4.6M breakthrough model)")
    
    return model

def detect_chessboard_v6(model, image_path, device):
    """
    Detect chessboard using V6 breakthrough segmentation model.
    Returns the segmentation mask and processed board region.
    """
    print(f"🔍 V6 Chess board detection on: {image_path}")
    
    # Load image
    original_image = cv2.imread(image_path)
    if original_image is None:
        print(f"❌ Could not load image from {image_path}")
        return None
    
    # Convert to RGB
    image_rgb = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
    original_h, original_w = image_rgb.shape[:2]
    
    # Resize for V6 model (256x256)
    target_size = 256
    image_resized = cv2.resize(image_rgb, (target_size, target_size))
    
    # Normalize and convert to tensor
    image_normalized = image_resized.astype(np.float32) / 255.0
    image_tensor = torch.from_numpy(image_normalized).permute(2, 0, 1).unsqueeze(0).to(device)
    
    # Run V6 inference
    start_time = time.time()
    with torch.no_grad():
        output = model(image_tensor)
        prediction = torch.sigmoid(output)
    inference_time = (time.time() - start_time) * 1000
    
    # Convert to numpy
    mask = prediction.cpu().squeeze().numpy()
    
    print(f"✅ V6 inference completed in {inference_time:.2f}ms")
    print(f"📊 V6 prediction range: [{mask.min():.4f}, {mask.max():.4f}]")
    
    # Create binary mask
    binary_mask = (mask > 0.5).astype(np.uint8)
    
    # Resize mask back to original size
    mask_resized = cv2.resize(binary_mask, (original_w, original_h))
    
    # Find contours to get the largest chess board region
    contours, _ = cv2.findContours(mask_resized, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        print("❌ No chess board contours found")
        return None
    
    # Get the largest contour
    largest_contour = max(contours, key=cv2.contourArea)
    
    # Get bounding rectangle
    x, y, w, h = cv2.boundingRect(largest_contour)
    
    # Add some padding
    padding = 20
    x = max(0, x - padding)
    y = max(0, y - padding)
    w = min(original_w - x, w + 2 * padding)
    h = min(original_h - y, h + 2 * padding)
    
    # Extract chess board region
    board_region = original_image[y:y+h, x:x+w]
    
    # Resize to square for piece detection
    board_size = 512
    board_square = cv2.resize(board_region, (board_size, board_size))
    
    print(f"✅ Chess board extracted: {w}x{h} -> {board_size}x{board_size}")
    
    return {
        'original_image': original_image,
        'mask': mask_resized,
        'board_region': board_region,
        'board_square': board_square,
        'bbox': (x, y, w, h),
        'inference_time': inference_time
    }

def detect_pieces(model_path, board_image):
    """Detect chess pieces using YOLO model."""
    print("🎯 Detecting chess pieces...")
    
    # Load YOLO model
    model = YOLO(model_path)
    
    # Run inference
    results = model(board_image, imgsz=416, conf=0.25, iou=0.7)[0]
    
    # Extract detections
    pieces = []
    if len(results.boxes) > 0:
        boxes = results.boxes.xyxy.cpu().numpy()
        scores = results.boxes.conf.cpu().numpy()
        class_ids = results.boxes.cls.cpu().numpy()
        class_names = results.names
        
        for i, (box, score, class_id) in enumerate(zip(boxes, scores, class_ids)):
            x1, y1, x2, y2 = box
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2
            
            pieces.append({
                'bbox': box,
                'center': (center_x, center_y),
                'confidence': score,
                'class_id': int(class_id),
                'class_name': class_names[int(class_id)]
            })
    
    print(f"✅ Detected {len(pieces)} chess pieces")
    return pieces

def map_pieces_to_grid(pieces, board_size=512):
    """Map detected pieces to 8x8 chess grid."""
    print("🗺️ Mapping pieces to chess grid...")
    
    # Initialize 8x8 grid
    grid = [[None for _ in range(8)] for _ in range(8)]
    
    # Calculate grid cell size
    cell_size = board_size / 8
    
    # Sort pieces by confidence
    sorted_pieces = sorted(pieces, key=lambda p: p['confidence'], reverse=True)
    
    for piece in sorted_pieces:
        center_x, center_y = piece['center']
        
        # Calculate grid position
        col = int(center_x // cell_size)
        row = int(center_y // cell_size)
        
        # Ensure within bounds
        if 0 <= row < 8 and 0 <= col < 8:
            # Convert to chess coordinates (a1 is bottom-left)
            chess_row = 7 - row
            chess_col = col
            
            # Place piece if square is empty
            if grid[chess_row][chess_col] is None:
                piece_with_pos = piece.copy()
                piece_with_pos['chess_pos'] = (chess_row, chess_col)
                piece_with_pos['grid_pos'] = (row, col)
                grid[chess_row][chess_col] = piece_with_pos
    
    return grid

def generate_fen(grid):
    """Generate FEN notation from piece grid."""
    print("📝 Generating FEN notation...")
    
    fen_rows = []
    
    # Process each row (rank 8 to rank 1)
    for row in grid:
        empty_count = 0
        row_fen = ""
        
        for square in row:
            if square is None:
                empty_count += 1
            else:
                # Add empty squares count if any
                if empty_count > 0:
                    row_fen += str(empty_count)
                    empty_count = 0
                
                # Add piece symbol
                class_name = square['class_name']
                if class_name in CONFIG["fen_symbols"]:
                    row_fen += CONFIG["fen_symbols"][class_name]
                else:
                    print(f"⚠️ Unknown piece class: {class_name}")
                    row_fen += "?"
        
        # Add remaining empty squares
        if empty_count > 0:
            row_fen += str(empty_count)
        
        fen_rows.append(row_fen)
    
    fen = "/".join(fen_rows)
    print(f"✅ FEN generated: {fen}")
    return fen

def visualize_results(chessboard_results, pieces, grid, fen, output_path):
    """Create comprehensive visualization of results."""
    print("🎨 Creating visualization...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('V6 Enhanced FEN Generation Results', fontsize=16, fontweight='bold')
    
    # Original image
    axes[0, 0].imshow(cv2.cvtColor(chessboard_results['original_image'], cv2.COLOR_BGR2RGB))
    axes[0, 0].set_title('Original Image')
    axes[0, 0].axis('off')
    
    # V6 segmentation mask
    axes[0, 1].imshow(chessboard_results['mask'], cmap='hot')
    axes[0, 1].set_title(f'V6 Segmentation\n(Inference: {chessboard_results["inference_time"]:.1f}ms)')
    axes[0, 1].axis('off')
    
    # Extracted board
    axes[0, 2].imshow(cv2.cvtColor(chessboard_results['board_square'], cv2.COLOR_BGR2RGB))
    axes[0, 2].set_title('Extracted Chess Board')
    axes[0, 2].axis('off')
    
    # Board with piece detections
    board_with_pieces = chessboard_results['board_square'].copy()
    for piece in pieces:
        x1, y1, x2, y2 = piece['bbox'].astype(int)
        cv2.rectangle(board_with_pieces, (x1, y1), (x2, y2), (0, 255, 0), 2)
        cv2.putText(board_with_pieces, f"{piece['class_name']}", 
                   (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
    
    axes[1, 0].imshow(cv2.cvtColor(board_with_pieces, cv2.COLOR_BGR2RGB))
    axes[1, 0].set_title(f'Piece Detections ({len(pieces)} pieces)')
    axes[1, 0].axis('off')
    
    # Chess grid visualization
    board_with_grid = chessboard_results['board_square'].copy()
    cell_size = 512 // 8
    
    # Draw grid lines
    for i in range(9):
        cv2.line(board_with_grid, (i * cell_size, 0), (i * cell_size, 512), (255, 255, 255), 1)
        cv2.line(board_with_grid, (0, i * cell_size), (512, i * cell_size), (255, 255, 255), 1)
    
    # Draw pieces on grid
    for row_idx, row in enumerate(grid):
        for col_idx, piece in enumerate(row):
            if piece is not None:
                # Calculate center of grid square
                center_x = col_idx * cell_size + cell_size // 2
                center_y = (7 - row_idx) * cell_size + cell_size // 2
                
                # Draw piece symbol
                symbol = CONFIG["fen_symbols"].get(piece['class_name'], '?')
                cv2.putText(board_with_grid, symbol, 
                           (center_x - 10, center_y + 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    
    axes[1, 1].imshow(cv2.cvtColor(board_with_grid, cv2.COLOR_BGR2RGB))
    axes[1, 1].set_title('Chess Grid Mapping')
    axes[1, 1].axis('off')
    
    # FEN representation
    axes[1, 2].text(0.1, 0.5, f"FEN Position:\n\n{fen}\n\nV6 Model Performance:\n• Dice Score: 0.9391\n• Inference: {chessboard_results['inference_time']:.1f}ms\n• Pieces Detected: {len(pieces)}", 
                   fontsize=12, fontfamily='monospace', 
                   bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue"))
    axes[1, 2].set_title('FEN Notation')
    axes[1, 2].axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"💾 Visualization saved to: {output_path}")

def main():
    """Main function for V6 enhanced FEN generation."""
    parser = argparse.ArgumentParser(description='Generate FEN notation using V6 breakthrough segmentation model')
    parser.add_argument('image_path', type=str, help='Path to chess board image')
    parser.add_argument('--output', type=str, default='v6_fen_output.png', help='Output visualization path')
    parser.add_argument('--v6_model', type=str, default=CONFIG["v6_model"], help='Path to V6 model')
    parser.add_argument('--piece_model', type=str, default=CONFIG["piece_model"], help='Path to YOLO piece model')
    args = parser.parse_args()
    
    print("🚀 V6 ENHANCED FEN GENERATION")
    print("=" * 50)
    print(f"📸 Input Image: {args.image_path}")
    print(f"🎯 V6 Model: {args.v6_model}")
    print(f"🎲 Piece Model: {args.piece_model}")
    
    # Setup device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"🖥️ Device: {device}")
    
    try:
        # Stage 1: Load V6 model and detect chessboard
        print("\n🔍 Stage 1: V6 Chess Board Detection")
        v6_model = load_v6_model(args.v6_model, device)
        if v6_model is None:
            return
        
        chessboard_results = detect_chessboard_v6(v6_model, args.image_path, device)
        if chessboard_results is None:
            print("❌ No chess board detected")
            return
        
        # Stage 2: Detect pieces
        print("\n🎯 Stage 2: Chess Piece Detection")
        pieces = detect_pieces(args.piece_model, chessboard_results['board_square'])
        
        # Stage 3: Generate FEN
        print("\n📝 Stage 3: FEN Generation")
        grid = map_pieces_to_grid(pieces)
        fen = generate_fen(grid)
        
        # Create visualization
        print("\n🎨 Stage 4: Visualization")
        visualize_results(chessboard_results, pieces, grid, fen, args.output)
        
        # Save FEN to file
        fen_file = os.path.splitext(args.output)[0] + ".fen"
        with open(fen_file, 'w') as f:
            f.write(fen)
        
        print(f"\n✅ SUCCESS!")
        print(f"📝 FEN: {fen}")
        print(f"💾 Visualization: {args.output}")
        print(f"📄 FEN file: {fen_file}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
