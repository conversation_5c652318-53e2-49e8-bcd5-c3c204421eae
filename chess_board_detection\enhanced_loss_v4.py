"""
Enhanced loss functions for chess board detection with improved corner localization.
Version 4 with specific improvements for peak-to-second ratio, detection rate,
and segmentation-guided corner detection.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math
from segmentation_guided_corner_detection import SegmentationGuidedCornerDetectionLoss


class EnhancedDiceLoss(nn.Module):
    """
    Enhanced Dice Loss for segmentation with boundary weighting.
    """
    def __init__(self, smooth=1.0):
        super(EnhancedDiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, pred, target):
        pred = torch.sigmoid(pred)

        # Ensure target has the same dimensions as pred
        if len(target.shape) != len(pred.shape):
            if len(target.shape) == 3 and len(pred.shape) == 4:
                # If target is [B, H, W] and pred is [B, C, H, W]
                target = target.unsqueeze(1)
            elif len(target.shape) == 4 and target.shape[1] != pred.shape[1]:
                # If channel dimensions don't match
                if target.shape[1] == 1 and pred.shape[1] > 1:
                    # Expand target to match pred's channels
                    target = target.expand(-1, pred.shape[1], -1, -1)

        # Calculate standard Dice loss
        # Use the last two dimensions (height and width)
        dims = tuple(range(2, len(pred.shape)))
        intersection = (pred * target).sum(dim=dims)
        union = pred.sum(dim=dims) + target.sum(dim=dims)
        dice = (2.0 * intersection + self.smooth) / (union + self.smooth)

        return 1.0 - dice.mean()


class ImprovedCornerFocusedHeatmapLoss(nn.Module):
    """
    Improved heatmap loss with enhanced peak-to-second ratio and detection rate.

    This loss function:
    1. Uses MSE for basic heatmap prediction
    2. Adds a separation penalty to ensure corners are distinct
    3. Adds an edge suppression term to penalize activations along edges
    4. Includes a peak enhancement term to make corner peaks sharper
    5. Adds a peak-to-second ratio enhancement term
    6. Adds a detection rate penalty
    7. Adds a segmentation-guided corner detection component
    """
    def __init__(self,
                 separation_weight=0.6,
                 peak_separation_weight=0.5,
                 edge_suppression_weight=0.7,
                 peak_enhancement_weight=0.5,
                 peak_to_second_ratio_weight=1.0,
                 detection_rate_weight=1.0,
                 segmentation_guidance_weight=0.8):
        super(ImprovedCornerFocusedHeatmapLoss, self).__init__()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight
        self.edge_suppression_weight = edge_suppression_weight
        self.peak_enhancement_weight = peak_enhancement_weight
        self.peak_to_second_ratio_weight = peak_to_second_ratio_weight
        self.detection_rate_weight = detection_rate_weight
        self.segmentation_guidance_weight = segmentation_guidance_weight

        # Create segmentation guidance loss
        self.segmentation_guidance_loss = SegmentationGuidedCornerDetectionLoss(weight=1.0)

        # Small epsilon to prevent numerical instability
        self.eps = 1e-6

    def forward(self, pred_heatmaps, target_heatmaps, segmentation=None):
        """
        Args:
            pred_heatmaps: Predicted heatmaps (B, 4, H, W)
            target_heatmaps: Target heatmaps (B, 4, H, W)
        """
        batch_size = pred_heatmaps.size(0)

        # Apply sigmoid to predicted heatmaps to ensure values are in [0, 1]
        pred_heatmaps = torch.sigmoid(pred_heatmaps)

        # Clip target heatmaps to [0, 1] for stability
        target_heatmaps = torch.clamp(target_heatmaps, 0.0, 1.0)

        # Basic MSE loss for heatmap prediction
        mse_loss = F.mse_loss(pred_heatmaps, target_heatmaps)

        # Separation loss: penalize overlap between different corner heatmaps
        separation_loss = 0.0
        for i in range(4):
            for j in range(i+1, 4):
                # Use positive overlap only
                overlap = torch.clamp(torch.min(pred_heatmaps[:, i], pred_heatmaps[:, j]), min=0.0)
                separation_loss += overlap.sum() / (batch_size + self.eps)

        # Peak separation loss: ensure peaks are well separated
        peak_separation_loss = 0.0
        for i in range(batch_size):
            for c in range(4):
                # Get the location of the maximum value (peak) for this heatmap
                heatmap = pred_heatmaps[i, c]
                max_val, _ = torch.max(heatmap.view(-1), dim=0)

                # Skip if max_val is too small (no clear peak)
                if max_val < 0.1:
                    continue

                # Create a mask of values that are close to the maximum
                # Use a smaller threshold to create sharper peaks
                peak_mask = (heatmap > 0.5 * max_val).float()

                # Penalize if the peak area is too large
                peak_size = peak_mask.sum()
                ideal_peak_size = torch.tensor(9.0, device=pred_heatmaps.device)  # 3x3 peak
                peak_separation_loss += torch.abs(peak_size - ideal_peak_size) / (ideal_peak_size + self.eps)

        peak_separation_loss = peak_separation_loss / (batch_size * 4 + self.eps)

        # Edge suppression loss: penalize activations along edges that aren't near corners
        edge_suppression_loss = 0.0
        for i in range(batch_size):
            # Create edge masks (top, right, bottom, left)
            h, w = pred_heatmaps.shape[2], pred_heatmaps.shape[3]

            # Define corner regions (where activations are allowed)
            corner_size = max(3, min(h, w) // 8)  # Size of corner region, at least 3 pixels

            # Create masks for the four corner regions
            corner_masks = torch.zeros((4, h, w), device=pred_heatmaps.device)

            # Top-left corner
            corner_masks[0, :corner_size, :corner_size] = 1.0
            # Top-right corner
            corner_masks[1, :corner_size, -corner_size:] = 1.0
            # Bottom-right corner
            corner_masks[2, -corner_size:, -corner_size:] = 1.0
            # Bottom-left corner
            corner_masks[3, -corner_size:, :corner_size] = 1.0

            # Create edge masks (excluding corner regions)
            edge_masks = torch.zeros((4, h, w), device=pred_heatmaps.device)

            # Ensure we have enough pixels for edge masks
            if h > 2*corner_size and w > 2*corner_size:
                # Top edge (excluding corners)
                edge_masks[0, :3, corner_size:-corner_size] = 1.0
                # Right edge (excluding corners)
                edge_masks[1, corner_size:-corner_size, -3:] = 1.0
                # Bottom edge (excluding corners)
                edge_masks[2, -3:, corner_size:-corner_size] = 1.0
                # Left edge (excluding corners)
                edge_masks[3, corner_size:-corner_size, :3] = 1.0

            # Calculate edge activations for each heatmap
            for c in range(4):
                heatmap = pred_heatmaps[i, c]

                # Penalize activations along edges that aren't in the corresponding corner
                for e in range(4):
                    # Skip if this is the corresponding corner
                    if c == e:
                        continue

                    # Penalize activations along this edge
                    edge_activations = (heatmap * edge_masks[e]).sum()
                    edge_suppression_loss += edge_activations

        edge_suppression_loss = edge_suppression_loss / (batch_size * 4 + self.eps)

        # Peak enhancement loss: encourage sharper peaks at corner locations
        peak_enhancement_loss = 0.0
        valid_peaks = 0
        for i in range(batch_size):
            for c in range(4):
                # Get target and predicted heatmaps
                target_hm = target_heatmaps[i, c]
                pred_hm = pred_heatmaps[i, c]

                # Find the location of the maximum value in the target heatmap
                max_val, max_idx = torch.max(target_hm.view(-1), dim=0)

                # Skip if max_val is too small (no clear peak in target)
                if max_val < 0.1:
                    continue

                y, x = max_idx // target_hm.size(1), max_idx % target_hm.size(1)

                # Calculate the value at this location in the predicted heatmap
                pred_val = pred_hm[y, x]

                # Penalize if the predicted value is not high enough
                peak_enhancement_loss += torch.abs(pred_val - max_val) / (max_val + self.eps)
                valid_peaks += 1

        peak_enhancement_loss = peak_enhancement_loss / (valid_peaks + self.eps)

        # NEW: Peak-to-second ratio enhancement
        peak_to_second_ratio_loss = 0.0
        valid_ratios = 0
        for i in range(batch_size):
            for c in range(4):
                # Get the predicted heatmap
                pred_hm = pred_heatmaps[i, c]

                # Find the location of the maximum value
                max_val, max_idx = torch.max(pred_hm.view(-1), dim=0)

                # Skip if max_val is too small
                if max_val < 0.1:
                    continue

                # Create a mask to exclude the main peak and its immediate surroundings
                mask = torch.ones_like(pred_hm)
                y, x = max_idx // pred_hm.size(1), max_idx % pred_hm.size(1)
                y_size, x_size = pred_hm.shape
                for y_offset in range(-3, 4):
                    for x_offset in range(-3, 4):
                        y_pos = y + y_offset
                        x_pos = x + x_offset
                        if 0 <= y_pos < y_size and 0 <= x_pos < x_size:
                            mask[y_pos, x_pos] = 0

                # Find second peak
                masked_hm = pred_hm * mask
                second_max_val = torch.max(masked_hm)

                # Ensure second_max_val is positive
                second_max_val = torch.clamp(second_max_val, min=self.eps)

                # Calculate ratio and penalize if it's too low
                ratio = max_val / second_max_val
                target_ratio = torch.tensor(2.0, device=pred_heatmaps.device)
                if ratio < target_ratio:
                    peak_to_second_ratio_loss += (target_ratio - ratio) / target_ratio
                    valid_ratios += 1

        peak_to_second_ratio_loss = peak_to_second_ratio_loss / (valid_ratios + self.eps) if valid_ratios > 0 else torch.tensor(0.0, device=pred_heatmaps.device)

        # NEW: Detection rate penalty
        detection_rate_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(batch_size):
            for c in range(4):
                # Get the predicted heatmap
                pred_hm = pred_heatmaps[i, c]

                # Find the maximum value
                max_val = torch.max(pred_hm)

                # Penalize if max_val is below detection threshold
                detection_threshold = torch.tensor(0.5, device=pred_heatmaps.device)
                if max_val < detection_threshold:
                    detection_rate_loss += (detection_threshold - max_val) / detection_threshold

        detection_rate_loss = detection_rate_loss / (batch_size * 4 + self.eps)

        # Scale the loss components to similar magnitudes
        mse_loss = mse_loss
        separation_loss = separation_loss * 0.1
        peak_separation_loss = peak_separation_loss * 0.1
        edge_suppression_loss = edge_suppression_loss * 0.1
        peak_enhancement_loss = peak_enhancement_loss * 0.1
        peak_to_second_ratio_loss = peak_to_second_ratio_loss * 0.1
        detection_rate_loss = detection_rate_loss * 0.1

        # Add segmentation guidance loss if segmentation is provided
        segmentation_guidance_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        if segmentation is not None:
            segmentation_guidance_loss = self.segmentation_guidance_loss(segmentation, pred_heatmaps)
            # Scale to similar magnitude as other losses
            segmentation_guidance_loss = segmentation_guidance_loss * 0.1

        # Combine all losses with weights
        total_loss = (mse_loss +
                     self.separation_weight * separation_loss +
                     self.peak_separation_weight * peak_separation_loss +
                     self.edge_suppression_weight * edge_suppression_loss +
                     self.peak_enhancement_weight * peak_enhancement_loss +
                     self.peak_to_second_ratio_weight * peak_to_second_ratio_loss +
                     self.detection_rate_weight * detection_rate_loss +
                     self.segmentation_guidance_weight * segmentation_guidance_loss)

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("WARNING: NaN or Inf detected in loss calculation. Using fallback loss.")
            total_loss = mse_loss

        return total_loss, {
            'mse_loss': mse_loss.item(),
            'separation_loss': separation_loss.item(),
            'peak_separation_loss': peak_separation_loss.item(),
            'edge_suppression_loss': edge_suppression_loss.item(),
            'peak_enhancement_loss': peak_enhancement_loss.item(),
            'peak_to_second_ratio_loss': peak_to_second_ratio_loss.item(),
            'detection_rate_loss': detection_rate_loss.item(),
            'segmentation_guidance_loss': segmentation_guidance_loss.item() if segmentation is not None else 0.0
        }


class ImprovedGeometricConsistencyLoss(nn.Module):
    """
    Improved loss function that enforces geometric consistency between detected corners.
    Ensures that the four corners form a proper quadrilateral with more stable gradients.

    This version includes:
    1. Aspect ratio consistency
    2. Parallelogram check
    3. Convexity check
    4. Angle constraints
    5. Stability improvements
    """
    def __init__(self, weight=0.2):
        super(ImprovedGeometricConsistencyLoss, self).__init__()
        self.weight = weight
        self.eps = 1e-6

    def forward(self, pred_heatmaps):
        batch_size = pred_heatmaps.size(0)
        h, w = pred_heatmaps.shape[2], pred_heatmaps.shape[3]

        # Apply sigmoid to predicted heatmaps to ensure values are in [0, 1]
        pred_heatmaps = torch.sigmoid(pred_heatmaps)

        # Extract corner coordinates from heatmaps
        corners = []
        corner_confidences = []
        for c in range(4):
            heatmaps_c = pred_heatmaps[:, c]  # (B, H, W)

            # Get indices of maximum values
            max_vals, _ = torch.max(heatmaps_c.view(batch_size, -1), dim=1)
            max_indices = torch.argmax(heatmaps_c.view(batch_size, -1), dim=1)

            # Convert to y, x coordinates
            y = max_indices // w
            x = max_indices % w

            # Normalize to [0, 1]
            x_norm = x.float() / max(w - 1, 1)
            y_norm = y.float() / max(h - 1, 1)

            corners.append(torch.stack([x_norm, y_norm], dim=1))  # (B, 2)
            corner_confidences.append(max_vals)

        # Calculate geometric consistency loss
        loss = 0.0
        valid_samples = 0

        for i in range(batch_size):
            # Skip samples with low confidence corners
            if any(conf[i] < 0.1 for conf in corner_confidences):
                continue

            valid_samples += 1

            # Get corners for this sample
            c0 = corners[0][i]  # Top-left
            c1 = corners[1][i]  # Top-right
            c2 = corners[2][i]  # Bottom-right
            c3 = corners[3][i]  # Bottom-left

            # 1. Aspect ratio consistency
            # The width/height ratio should be consistent
            width_top = torch.norm(c1 - c0, dim=0) + self.eps
            width_bottom = torch.norm(c2 - c3, dim=0) + self.eps
            height_left = torch.norm(c3 - c0, dim=0) + self.eps
            height_right = torch.norm(c2 - c1, dim=0) + self.eps

            # Width should be consistent
            width_diff = torch.abs(width_top - width_bottom) / (0.5 * (width_top + width_bottom) + self.eps)
            # Height should be consistent
            height_diff = torch.abs(height_left - height_right) / (0.5 * (height_left + height_right) + self.eps)

            # 2. Parallelogram check
            # Opposite sides should be parallel
            top_vector = F.normalize(c1 - c0, dim=0)
            bottom_vector = F.normalize(c2 - c3, dim=0)
            left_vector = F.normalize(c3 - c0, dim=0)
            right_vector = F.normalize(c2 - c1, dim=0)

            # Dot product of normalized vectors should be close to 1 for parallel lines
            # Use absolute value to handle direction flips
            parallel_top_bottom = 1.0 - torch.abs(torch.dot(top_vector, bottom_vector))
            parallel_left_right = 1.0 - torch.abs(torch.dot(left_vector, right_vector))

            # 3. Convexity check
            # For a convex quadrilateral, all interior angles should be less than 180 degrees
            # We can check this by ensuring all cross products have the same sign

            # Compute vectors for each side
            v01 = c1 - c0
            v12 = c2 - c1
            v23 = c3 - c2
            v30 = c0 - c3

            # Compute cross products (in 2D, just the z-component)
            cross1 = v01[0] * v12[1] - v01[1] * v12[0]
            cross2 = v12[0] * v23[1] - v12[1] * v23[0]
            cross3 = v23[0] * v30[1] - v23[1] * v30[0]
            cross4 = v30[0] * v01[1] - v30[1] * v01[0]

            # All cross products should have the same sign for a convex quadrilateral
            # We'll use the sum of absolute differences between signs
            convexity_loss = (
                torch.abs(torch.sign(cross1) - torch.sign(cross2)) +
                torch.abs(torch.sign(cross2) - torch.sign(cross3)) +
                torch.abs(torch.sign(cross3) - torch.sign(cross4)) +
                torch.abs(torch.sign(cross4) - torch.sign(cross1))
            ) / 8.0  # Normalize to [0, 1]

            # 4. Angle constraints
            # Interior angles should be close to 90 degrees for a rectangular board
            # We can check this using dot products of adjacent sides

            # Normalize the vectors
            v01_norm = F.normalize(v01, dim=0)
            v12_norm = F.normalize(v12, dim=0)
            v23_norm = F.normalize(v23, dim=0)
            v30_norm = F.normalize(v30, dim=0)

            # Compute dot products (should be close to 0 for 90 degree angles)
            dot1 = torch.abs(torch.dot(v01_norm, v12_norm))
            dot2 = torch.abs(torch.dot(v12_norm, v23_norm))
            dot3 = torch.abs(torch.dot(v23_norm, v30_norm))
            dot4 = torch.abs(torch.dot(v30_norm, v01_norm))

            angle_loss = (dot1 + dot2 + dot3 + dot4) / 4.0

            # Combine all geometric constraints with weights
            sample_loss = (
                0.3 * width_diff +
                0.3 * height_diff +
                0.2 * parallel_top_bottom +
                0.2 * parallel_left_right +
                0.3 * convexity_loss +
                0.3 * angle_loss
            )

            loss += sample_loss

        # Average loss over valid samples
        if valid_samples > 0:
            loss = loss / valid_samples
        else:
            # If no valid samples, return a small constant loss
            loss = torch.tensor(0.01, device=pred_heatmaps.device)

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(loss) or torch.isinf(loss):
            print("WARNING: NaN or Inf detected in geometric loss calculation. Using fallback loss.")
            return torch.tensor(0.01, device=pred_heatmaps.device)

        return self.weight * loss
