"""
Utilities for optimizing models for deployment.

This module provides functions for:
1. Model quantization (FP32 -> INT8)
2. ONNX export
3. TorchScript conversion
4. Model size reduction
"""

import os
import torch
import torch.nn as nn
import torch.quantization as quantization
import numpy as np
from torch.utils.mobile_optimizer import optimize_for_mobile


def quantize_model(model, calibration_data_loader=None, backend='fbgemm'):
    """
    Quantize a model from FP32 to INT8 for faster inference and smaller size.
    
    Args:
        model: The PyTorch model to quantize
        calibration_data_loader: DataLoader for calibration data (for static quantization)
        backend: Quantization backend ('fbgemm' for x86, 'qnnpack' for ARM)
        
    Returns:
        Quantized model
    """
    # Set the backend
    torch.backends.quantized.engine = backend
    
    # Create a copy of the model for quantization
    model_to_quantize = model.cpu()
    
    # Set model to eval mode
    model_to_quantize.eval()
    
    # Fuse Conv, BN, ReLU layers for better quantization
    model_fused = fuse_model_layers(model_to_quantize)
    
    # Prepare model for static quantization
    model_prepared = prepare_for_static_quantization(model_fused)
    
    # Calibrate with data if provided (for static quantization)
    if calibration_data_loader is not None:
        calibrate_model(model_prepared, calibration_data_loader)
    
    # Convert to quantized model
    quantized_model = convert_to_quantized_model(model_prepared)
    
    print(f"Original model size: {get_model_size_mb(model):.2f} MB")
    print(f"Quantized model size: {get_model_size_mb(quantized_model):.2f} MB")
    
    return quantized_model


def fuse_model_layers(model):
    """
    Fuse Conv/Linear -> BN -> ReLU layers for better quantization results.
    
    Args:
        model: PyTorch model
        
    Returns:
        Model with fused layers
    """
    # Make a copy of the model
    model_fused = model
    
    # Example of how to fuse modules
    # This needs to be customized based on your model architecture
    for module_name, module in model_fused.named_children():
        if hasattr(module, 'fuse_model'):
            module.fuse_model()
        else:
            # Try to fuse Conv/Linear -> BN -> ReLU patterns
            # This is a simplified example and may need to be adapted
            for name, child in module.named_children():
                if isinstance(child, nn.Sequential):
                    # Check if we have Conv/Linear -> BN -> ReLU pattern
                    if len(child) >= 3:
                        if (isinstance(child[0], (nn.Conv2d, nn.Linear)) and
                            isinstance(child[1], nn.BatchNorm2d) and
                            isinstance(child[2], nn.ReLU)):
                            # Fuse these layers
                            torch.quantization.fuse_modules(
                                child, [['0', '1', '2']], inplace=True)
    
    return model_fused


def prepare_for_static_quantization(model):
    """
    Prepare model for static quantization.
    
    Args:
        model: PyTorch model
        
    Returns:
        Model prepared for static quantization
    """
    # Define quantization configuration
    qconfig = quantization.get_default_qconfig('fbgemm')
    
    # Set quantization configuration for the model
    model.qconfig = qconfig
    
    # Prepare model for quantization
    model_prepared = quantization.prepare(model)
    
    return model_prepared


def calibrate_model(model, data_loader):
    """
    Calibrate model using data for static quantization.
    
    Args:
        model: Model prepared for static quantization
        data_loader: DataLoader with calibration data
    """
    print("Calibrating model with data...")
    with torch.no_grad():
        for batch_idx, (data, _) in enumerate(data_loader):
            # Forward pass for calibration
            model(data)
            
            # Limit calibration to a few batches for speed
            if batch_idx >= 10:
                break
    print("Calibration complete")


def convert_to_quantized_model(model_prepared):
    """
    Convert prepared model to quantized model.
    
    Args:
        model_prepared: Model prepared for quantization
        
    Returns:
        Quantized model
    """
    quantized_model = quantization.convert(model_prepared)
    return quantized_model


def export_to_onnx(model, sample_input, output_path, input_names=None, output_names=None, dynamic_axes=None):
    """
    Export PyTorch model to ONNX format.
    
    Args:
        model: PyTorch model
        sample_input: Sample input tensor for tracing
        output_path: Path to save the ONNX model
        input_names: Names for input tensors
        output_names: Names for output tensors
        dynamic_axes: Dynamic axes configuration
    """
    # Set default input and output names if not provided
    if input_names is None:
        input_names = ['input']
    if output_names is None:
        output_names = ['segmentation', 'heatmaps']
    
    # Set default dynamic axes if not provided
    if dynamic_axes is None:
        dynamic_axes = {
            'input': {0: 'batch_size', 2: 'height', 3: 'width'},
            'segmentation': {0: 'batch_size', 2: 'height', 3: 'width'},
            'heatmaps': {0: 'batch_size', 2: 'height', 3: 'width'}
        }
    
    # Make sure model is in eval mode
    model.eval()
    
    # Export to ONNX
    torch.onnx.export(
        model,
        sample_input,
        output_path,
        export_params=True,
        opset_version=12,
        do_constant_folding=True,
        input_names=input_names,
        output_names=output_names,
        dynamic_axes=dynamic_axes
    )
    
    print(f"Model exported to ONNX format at: {output_path}")
    print(f"ONNX model size: {os.path.getsize(output_path) / (1024 * 1024):.2f} MB")


def optimize_for_mobile_deployment(model, sample_input, output_path):
    """
    Optimize model for mobile deployment using TorchScript.
    
    Args:
        model: PyTorch model
        sample_input: Sample input tensor for tracing
        output_path: Path to save the optimized model
    """
    # Set model to eval mode
    model.eval()
    
    # Trace the model with sample input
    traced_model = torch.jit.trace(model, sample_input)
    
    # Optimize for mobile
    optimized_model = optimize_for_mobile(traced_model)
    
    # Save the optimized model
    optimized_model.save(output_path)
    
    print(f"Model optimized for mobile deployment at: {output_path}")
    print(f"Mobile model size: {os.path.getsize(output_path) / (1024 * 1024):.2f} MB")


def get_model_size_mb(model):
    """
    Calculate the size of a PyTorch model in MB.
    
    Args:
        model: PyTorch model
        
    Returns:
        Size in MB
    """
    torch.save(model.state_dict(), "temp_model.pt")
    size_mb = os.path.getsize("temp_model.pt") / (1024 * 1024)
    os.remove("temp_model.pt")
    return size_mb


def prune_model(model, amount=0.3):
    """
    Prune model weights to reduce size.
    
    Args:
        model: PyTorch model
        amount: Amount of weights to prune (0.0 to 1.0)
        
    Returns:
        Pruned model
    """
    import torch.nn.utils.prune as prune
    
    # Make a copy of the model
    model_pruned = model
    
    # Prune convolutional and linear layers
    for name, module in model_pruned.named_modules():
        if isinstance(module, (nn.Conv2d, nn.Linear)):
            prune.l1_unstructured(module, name='weight', amount=amount)
            # Make pruning permanent
            prune.remove(module, 'weight')
    
    return model_pruned
