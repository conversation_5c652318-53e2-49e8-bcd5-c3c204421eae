PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> cd chess_board_detection; python train_v5_2.py --augmented_data_dir "data\augmented\v5.2\augmented_20250518_153326" --cpu
E:\New folder\Lib\site-packages\albumentations\__init__.py:28: UserWarning: A new version of Albumentations is available: '2.0.7' (you have '2.0.6'). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.
  check_for_updates()
2025-05-18 15:46:25.645858: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-05-18 15:46:26.688159: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
CUDA not available. Using CPU.
Memory limits: 3GB maximum usage
Using device: cpu
Using pre-augmented dataset from: data\augmented\v5.2\augmented_20250518_153326
Loaded 85 augmented samples from data\augmented\v5.2\augmented_20250518_153326
Created dataloaders with 68 training samples and 17 validation samples
Train dataset size: 68
Validation dataset size: 17
Initializing v5.2 model...
Model moved to cpu
Model parameters: 17874848
Trainable parameters: 17874848

=== Phase 1: Balanced foundation with smooth convergence path ===
Saving outputs to v5.2 folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Skipping model graph visualization in TensorBoard to conserve memory
Generating data augmentation visualizations...
Augmentation visualization directory created at C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2\augmentations
Generated augmentation visualizations in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2\augmentation
Epoch 1/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:08<00:00,  7.58s/it] 
train Loss: 309.5493, Seg Loss: 0.5544, Heatmap Loss: 205.7379, Geometric Loss: 0.9702
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 12.0298 🟢 (601.5% of target 2.0)
  avg_peak_to_mean_ratio: 77.0587
  avg_peak_to_second_ratio: 1.1623
  detection_rate: 0.9926 🟡 (99.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0063
  p2s_ratio_max: 1.7234
  p2s_ratio_std: 0.1993
  secondary_peak_distance: 82.4196
  primary_peak_sharpness: 2.1492
  primary_peak_isolation: 3.3011

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1693
  Top-Right: 1.1286
  Bottom-Right: 1.1890
  Bottom-Left: 1.1625

Secondary Peak Relative Position: x=-2.97, y=9.50
train Heatmap Components:
  mse_loss: 1.2227
  separation_loss: 5.3226
  peak_separation_loss: 82.3876
  edge_suppression_loss: 0.0037
  peak_enhancement_loss: 1.4447
  peak_to_second_ratio_loss: 1.0412
  avg_peak_to_second_ratio: 1.1149
  detection_rate_loss: 0.0011
  segmentation_guidance_loss: 2.2624
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.27s/it] 
val Loss: 2611.8930, Seg Loss: 0.4797, Heatmap Loss: 1740.7148, Geometric Loss: 0.8527
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 0.5631 🔴 (28.2% of target 2.0)
  avg_peak_to_mean_ratio: 16.2885
  avg_peak_to_second_ratio: 3.4871
  detection_rate: 0.4265 🔴 (42.6% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0043
  p2s_ratio_max: 38.2704
  p2s_ratio_std: 9.2959
  secondary_peak_distance: 63.1701
  primary_peak_sharpness: 0.1585
  primary_peak_isolation: 3.4349

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2696
  Top-Right: 1.1249
  Bottom-Right: 1.2490
  Bottom-Left: 10.3049

Secondary Peak Relative Position: x=-15.60, y=0.54
val Heatmap Components:
  mse_loss: 0.0802
  separation_loss: 5.8045
  peak_separation_loss: 391.9003
  edge_suppression_loss: -0.0120
  peak_enhancement_loss: 1.3009
  peak_to_second_ratio_loss: 1.1895
  avg_peak_to_second_ratio: 1.1268
  detection_rate_loss: 0.1636
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000224
New best model saved with loss: 2611.8930
New best model saved with peak-to-second ratio: 3.4871
New best model saved with detection rate: 0.4265
New best model saved with combined score: 0.7353 (loss: 2611.8930, p2s: 3.4871, detection: 0.4265)
Early stopping: New best score: 0.7353

Epoch 2/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:18<00:00,  8.13s/it] 
train Loss: 868.8517, Seg Loss: 0.4685, Heatmap Loss: 578.7019, Geometric Loss: 0.8260
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 6.1022 🟢 (305.1% of target 2.0)
  avg_peak_to_mean_ratio: 91.4007
  avg_peak_to_second_ratio: 1.1980
  detection_rate: 0.9853 🟡 (98.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0076
  p2s_ratio_max: 1.8170
  p2s_ratio_std: 0.2336
  secondary_peak_distance: 81.8277
  primary_peak_sharpness: 1.1667
  primary_peak_isolation: 3.2650

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1950
  Top-Right: 1.1672
  Bottom-Right: 1.2872
  Bottom-Left: 1.1427

Secondary Peak Relative Position: x=5.61, y=-3.44
train Heatmap Components:
  mse_loss: 0.6644
  separation_loss: 4.5553
  peak_separation_loss: 130.7708
  edge_suppression_loss: -0.0025
  peak_enhancement_loss: 1.3040
  peak_to_second_ratio_loss: 1.0924
  avg_peak_to_second_ratio: 1.1449
  detection_rate_loss: 0.0013
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.18s/it] 
val Loss: 3732.6446, Seg Loss: 0.3827, Heatmap Loss: 2487.9750, Geometric Loss: 0.7484
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.5223 🔴 (76.1% of target 2.0)
  avg_peak_to_mean_ratio: 28.2659
  avg_peak_to_second_ratio: 1.1545
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0052
  p2s_ratio_max: 1.7292
  p2s_ratio_std: 0.2245
  secondary_peak_distance: 126.1683
  primary_peak_sharpness: 0.2571
  primary_peak_isolation: 3.1236

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.0392
  Top-Right: 1.1170
  Bottom-Right: 1.3685
  Bottom-Left: 1.0932

Secondary Peak Relative Position: x=36.41, y=-22.38
val Heatmap Components:
  mse_loss: 0.2589
  separation_loss: 1.3697
  peak_separation_loss: 591.3348
  edge_suppression_loss: -0.0038
  peak_enhancement_loss: 1.0876
  peak_to_second_ratio_loss: 1.2073
  avg_peak_to_second_ratio: 1.1405
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000368
New best model saved with detection rate: 1.0000
Early stopping: No improvement for 1/15 epochs. Best: 0.7353, Current: 0.6063

Epoch 3/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:08<00:00,  7.54s/it] 
train Loss: 447.9486, Seg Loss: 0.4170, Heatmap Loss: 298.1342, Geometric Loss: 0.8258
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.5516 🟢 (227.6% of target 2.0)
  avg_peak_to_mean_ratio: 116.6855
  avg_peak_to_second_ratio: 1.2640
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0059
  p2s_ratio_max: 2.1151
  p2s_ratio_std: 0.3155
  secondary_peak_distance: 105.3773
  primary_peak_sharpness: 1.0004
  primary_peak_isolation: 3.4209

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2388
  Top-Right: 1.2175
  Bottom-Right: 1.4219
  Bottom-Left: 1.1780

Secondary Peak Relative Position: x=18.65, y=-24.46
train Heatmap Components:
  mse_loss: 0.3620
  separation_loss: 7.2464
  peak_separation_loss: 70.3505
  edge_suppression_loss: -0.0013
  peak_enhancement_loss: 1.0963
  peak_to_second_ratio_loss: 1.0835
  avg_peak_to_second_ratio: 1.1902
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1888
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.16s/it] 
val Loss: 848.5539, Seg Loss: 0.3795, Heatmap Loss: 565.2219, Geometric Loss: 0.8541
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.7006 🟢 (135.0% of target 2.0)
  avg_peak_to_mean_ratio: 35.7139
  avg_peak_to_second_ratio: 1.3755
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0078
  p2s_ratio_max: 2.2732
  p2s_ratio_std: 0.4247
  secondary_peak_distance: 116.0555
  primary_peak_sharpness: 0.5845
  primary_peak_isolation: 3.3241

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2697
  Top-Right: 1.4534
  Bottom-Right: 1.7324
  Bottom-Left: 1.0467

Secondary Peak Relative Position: x=11.63, y=-44.10
val Heatmap Components:
  mse_loss: 0.0943
  separation_loss: 8.3488
  peak_separation_loss: 136.5980
  edge_suppression_loss: 0.0019
  peak_enhancement_loss: 0.8959
  peak_to_second_ratio_loss: 0.9423
  avg_peak_to_second_ratio: 1.2992
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000512
New best model saved with loss: 848.5539
Early stopping: No improvement for 2/15 epochs. Best: 0.7353, Current: 0.7321

Epoch 4/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.44s/it] 
train Loss: 277.5456, Seg Loss: 0.4172, Heatmap Loss: 184.5361, Geometric Loss: 0.8107
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 5.3013 🟢 (265.1% of target 2.0)
  avg_peak_to_mean_ratio: 61.7716
  avg_peak_to_second_ratio: 1.3510
  detection_rate: 0.9926 🟡 (99.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0091
  p2s_ratio_max: 2.3456
  p2s_ratio_std: 0.3843
  secondary_peak_distance: 104.2756
  primary_peak_sharpness: 1.1390
  primary_peak_isolation: 3.3773

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2960
  Top-Right: 1.3469
  Bottom-Right: 1.4453
  Bottom-Left: 1.3157

Secondary Peak Relative Position: x=13.36, y=-32.23
train Heatmap Components:
  mse_loss: 0.2373
  separation_loss: 8.3884
  peak_separation_loss: 43.9212
  edge_suppression_loss: 0.0021
  peak_enhancement_loss: 0.9147
  peak_to_second_ratio_loss: 1.0343
  avg_peak_to_second_ratio: 1.2547
  detection_rate_loss: 0.0017
  segmentation_guidance_loss: 0.2926
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.10s/it] 
val Loss: 189.6512, Seg Loss: 0.3483, Heatmap Loss: 125.9972, Geometric Loss: 0.7676
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.6827 🟢 (234.1% of target 2.0)
  avg_peak_to_mean_ratio: 26.9016
  avg_peak_to_second_ratio: 1.5263
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0221
  p2s_ratio_max: 2.5428
  p2s_ratio_std: 0.5440
  secondary_peak_distance: 104.0748
  primary_peak_sharpness: 0.7415
  primary_peak_isolation: 3.0569

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1998
  Top-Right: 1.5958
  Bottom-Right: 1.9141
  Bottom-Left: 1.3956

Secondary Peak Relative Position: x=42.82, y=-36.79
val Heatmap Components:
  mse_loss: 0.2937
  separation_loss: 10.3284
  peak_separation_loss: 30.0784
  edge_suppression_loss: 0.0046
  peak_enhancement_loss: 0.8552
  peak_to_second_ratio_loss: 0.9018
  avg_peak_to_second_ratio: 1.4227
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000656
New best model saved with loss: 189.6512
New best model saved with combined score: 0.7740 (loss: 189.6512, p2s: 1.5263, detection: 1.0000)
Early stopping: New best score: 0.7740


