"""
<PERSON><PERSON><PERSON> to visualize how the models perform on augmented versions of real test images.
This applies the same augmentations used during training to showcase model robustness.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import albumentations as A
from albumentations.pytorch import ToTensorV2
import time
import random

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def get_training_augmentations():
    """
    Returns a list of augmentation pipelines used during training.
    These are based on the actual augmentations used to train the models.
    """
    # Basic augmentation (similar to v5.2 training)
    basic_aug = A.Compose([
        # Spatial augmentations
        A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
        A.Affine(scale=(0.8, 1.2), translate_percent=(0.1, 0.1), rotate=(-30, 30), p=1.0),
        
        # Color augmentations
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=1.0),
        A.HueSaturationValue(hue_shift_limit=15, sat_shift_limit=25, val_shift_limit=15, p=1.0),
    ])
    
    # Perspective augmentation
    perspective_aug = A.Compose([
        A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
        A.Perspective(scale=(0.05, 0.15), p=1.0),
    ])
    
    # Lighting augmentation
    lighting_aug = A.Compose([
        A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
        A.RandomBrightnessContrast(brightness_limit=0.4, contrast_limit=0.4, p=1.0),
        A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=1.0),
        A.CLAHE(clip_limit=4.0, p=1.0),
        A.RandomGamma(gamma_limit=(80, 120), p=1.0),
    ])
    
    # Noise and blur augmentation
    noise_blur_aug = A.Compose([
        A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
        A.OneOf([
            A.GaussNoise(p=1.0),
            A.GaussianBlur(blur_limit=7, p=1.0),
            A.MotionBlur(blur_limit=7, p=1.0),
            A.Blur(blur_limit=5, p=1.0)
        ], p=1.0),
    ])
    
    # Grid distortion augmentation
    distortion_aug = A.Compose([
        A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
        A.GridDistortion(num_steps=5, distort_limit=0.3, p=1.0),
    ])
    
    # Combined challenging augmentation
    challenging_aug = A.Compose([
        A.RandomResizedCrop(size=(256, 256), scale=(0.7, 1.0), p=1.0),
        A.Affine(
            scale=(0.8, 1.2),
            translate_percent=(0.12, 0.12),
            rotate=(-45, 45),
            shear=(-10, 10),
            p=1.0
        ),
        A.Perspective(scale=(0.05, 0.2), p=1.0),
        A.RandomBrightnessContrast(brightness_limit=0.4, contrast_limit=0.4, p=1.0),
        A.GaussNoise(p=1.0),
    ])
    
    return {
        "Original": None,
        "Basic Augmentation": basic_aug,
        "Perspective": perspective_aug,
        "Lighting": lighting_aug,
        "Noise & Blur": noise_blur_aug,
        "Distortion": distortion_aug,
        "Challenging": challenging_aug
    }

def preprocess_for_model(image, target_size=(256, 256)):
    """Preprocess an image for model input."""
    # Convert to tensor and normalize
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    input_tensor = transform(image).unsqueeze(0)
    return input_tensor

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']
    
    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)
    
    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []
    
    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))
    
    return segmentation, heatmaps, keypoints

def visualize_augmented_results(model_name, model, original_image, augmentations, output_path):
    """Create a visualization showing model performance on augmented versions of the test image."""
    # Set random seed for reproducibility
    random.seed(42)
    np.random.seed(42)
    
    # Create figure with rows for each augmentation
    n_augmentations = len(augmentations)
    fig, axs = plt.subplots(n_augmentations, 3, figsize=(15, 5 * n_augmentations))
    fig.suptitle(f'Model Performance on Augmented Images - {model_name}', fontsize=16)
    
    # Corner names and colors
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['red', 'green', 'blue', 'yellow']
    
    # Process each augmentation
    for i, (aug_name, aug_transform) in enumerate(augmentations.items()):
        # Get the row of axes for this augmentation
        if n_augmentations == 1:
            row_axs = axs
        else:
            row_axs = axs[i]
        
        # Apply augmentation if not original
        if aug_transform is None:
            # Use original image
            aug_img = original_image
        else:
            # Apply augmentation
            aug_result = aug_transform(image=np.array(original_image))
            aug_img = aug_result['image']
        
        # Convert to PIL for model input
        if isinstance(aug_img, np.ndarray):
            aug_img_pil = Image.fromarray(aug_img)
        else:
            aug_img_pil = aug_img
            
        # Resize to 256x256 if needed
        if aug_img_pil.size != (256, 256):
            aug_img_pil = aug_img_pil.resize((256, 256), Image.LANCZOS)
        
        # Preprocess for model
        input_tensor = preprocess_for_model(aug_img_pil)
        
        # Run inference
        segmentation, heatmaps, keypoints = detect_corners(model, input_tensor)
        
        # Calculate average confidence
        avg_confidence = np.mean([kp[2] for kp in keypoints])
        
        # Display augmented image with keypoints
        row_axs[0].imshow(aug_img_pil)
        row_axs[0].set_title(f'{aug_name}\nAvg Confidence: {avg_confidence:.3f}')
        
        # Plot keypoints
        for j, (x, y, conf) in enumerate(keypoints):
            row_axs[0].scatter(x, y, c=colors[j], marker='x', s=100, linewidths=2)
            row_axs[0].text(x+5, y+5, f"{corner_names[j]}\n{conf:.3f}", 
                         color=colors[j], fontsize=8,
                         bbox=dict(facecolor='white', alpha=0.7))
        
        # Draw lines connecting the keypoints
        xs = [kp[0] for kp in keypoints]
        ys = [kp[1] for kp in keypoints]
        xs.append(xs[0])  # Close the polygon
        ys.append(ys[0])
        row_axs[0].plot(xs, ys, 'g-', linewidth=2, alpha=0.7)
        row_axs[0].axis('off')
        
        # Display segmentation mask
        row_axs[1].imshow(segmentation[0, 0].cpu().numpy(), cmap='gray')
        row_axs[1].set_title('Segmentation Mask')
        row_axs[1].axis('off')
        
        # Display combined heatmap
        combined_hm = np.zeros((heatmaps.shape[2], heatmaps.shape[3], 3))
        
        for j in range(4):
            hm = heatmaps[0, j].cpu().numpy()
            
            # Normalize heatmap
            if np.max(hm) > 0:
                hm = hm / np.max(hm)
            
            # Add to combined heatmap with color
            color_idx = j / 3.0
            color = plt.cm.jet(color_idx)[:3]
            
            for ch in range(3):
                combined_hm[:, :, ch] += hm * color[ch]
        
        # Normalize combined heatmap
        if np.max(combined_hm) > 0:
            combined_hm = combined_hm / np.max(combined_hm)
        
        row_axs[2].imshow(combined_hm)
        row_axs[2].set_title('Combined Heatmaps')
        row_axs[2].axis('off')
    
    # Adjust layout and save
    plt.tight_layout(rect=[0, 0, 1, 0.97])
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs"
    
    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the original image
    original_image = Image.open(image_path).convert('RGB')
    
    # Get augmentations
    augmentations = get_training_augmentations()
    
    # Process each model
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} with augmentations...")
        
        # Load model
        model = load_model(model_path)
        
        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_augmented_performance.png")
        visualize_augmented_results(
            model_name,
            model,
            original_image,
            augmentations,
            output_path
        )
        
        print(f"Augmented performance visualization saved to: {output_path}")
    
    print("All visualizations completed!")

if __name__ == "__main__":
    main()
