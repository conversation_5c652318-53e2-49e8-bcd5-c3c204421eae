"""
Test script to visualize the improved heatmap generation.
"""

import os
import argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt
from utils.real_dataset import generate_gaussian_heatmap
from config import DATA_DIR, INPUT_SIZE, HEATMAP_SIGMA


def visualize_heatmaps(image_path, corners, output_path=None, sigma=HEATMAP_SIGMA):
    """
    Visualize the heatmaps for a given image and corners.
    
    Args:
        image_path (str): Path to the image.
        corners (list): List of corner coordinates [x1, y1, x2, y2, x3, y3, x4, y4].
        output_path (str, optional): Path to save the visualization.
        sigma (float): Sigma value for Gaussian heatmap generation.
    """
    # Load image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Get original dimensions
    orig_height, orig_width = image.shape[:2]
    
    # Resize image to INPUT_SIZE
    image_resized = cv2.resize(image, (INPUT_SIZE[1], INPUT_SIZE[0]))
    
    # Calculate scale factors
    scale_x = INPUT_SIZE[1] / orig_width
    scale_y = INPUT_SIZE[0] / orig_height
    
    # Create corner heatmaps
    corner_heatmaps = []
    scaled_corners = []
    
    for i in range(0, len(corners), 2):
        # Scale corner coordinates
        x, y = corners[i], corners[i+1]
        x_scaled = x * scale_x
        y_scaled = y * scale_y
        scaled_corners.append((x_scaled, y_scaled))
        
        # Generate heatmap
        heatmap = generate_gaussian_heatmap(
            INPUT_SIZE[0], INPUT_SIZE[1], x_scaled, y_scaled, sigma=sigma
        )
        corner_heatmaps.append(heatmap)
    
    # Create visualization
    plt.figure(figsize=(15, 10))
    
    # Original image
    plt.subplot(2, 3, 1)
    plt.imshow(image)
    plt.title('Original Image')
    plt.axis('off')
    
    # Resized image with corners
    plt.subplot(2, 3, 2)
    plt.imshow(image_resized)
    for i, (x, y) in enumerate(scaled_corners):
        plt.plot(x, y, 'ro', markersize=8)
        plt.text(x, y, str(i+1), color='white', fontsize=12,
                bbox=dict(facecolor='red', alpha=0.7))
    plt.title('Resized Image with Corners')
    plt.axis('off')
    
    # Corner heatmaps
    for i in range(4):
        plt.subplot(2, 3, i+3)
        plt.imshow(corner_heatmaps[i], cmap='jet')
        plt.title(f'Corner {i+1} Heatmap (sigma={sigma})')
        plt.axis('off')
        plt.plot(scaled_corners[i][0], scaled_corners[i][1], 'ro', markersize=8)
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path)
        print(f"Visualization saved to {output_path}")
    else:
        plt.show()
    
    plt.close()


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Test Heatmap Generation')
    parser.add_argument('--image', type=str, required=True,
                        help='Path to the image')
    parser.add_argument('--corners', type=float, nargs=8, required=True,
                        help='Corner coordinates [x1, y1, x2, y2, x3, y3, x4, y4]')
    parser.add_argument('--output', type=str, default=None,
                        help='Path to save the visualization')
    parser.add_argument('--sigma', type=float, default=HEATMAP_SIGMA,
                        help='Sigma value for Gaussian heatmap generation')
    args = parser.parse_args()
    
    # Visualize heatmaps
    visualize_heatmaps(args.image, args.corners, args.output, args.sigma)


if __name__ == "__main__":
    main()
