"""
Benchmark the performance of the original and distilled models.
Measure inference time, memory usage, and accuracy.
"""

import os
import sys
import argparse
import time
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import psutil
import pandas as pd
from tqdm import tqdm

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from chess_board_detection.models.segmentation_only_model import SegmentationOnlyModel, TinySegmentationModel

def load_original_model(model_path):
    """Load the original model."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def load_distilled_model(model_path, model_type='tiny'):
    """Load the distilled model."""
    if model_type == 'tiny':
        model = TinySegmentationModel(n_channels=3)
    else:
        model = SegmentationOnlyModel(n_channels=3)
    
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for model input."""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize to target size
    image_resized = cv2.resize(image, target_size, interpolation=cv2.INTER_AREA)
    
    return image_resized

def normalize_for_model(image):
    """Normalize image for model input."""
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)
    
    return input_tensor

def measure_inference_time(model, input_tensor, num_runs=10):
    """
    Measure the inference time of a model.
    
    Args:
        model: The model to benchmark
        input_tensor: Input tensor for the model
        num_runs: Number of runs to average over
        
    Returns:
        avg_time: Average inference time in seconds
        std_time: Standard deviation of inference time
    """
    times = []
    
    # Warm-up run
    with torch.no_grad():
        _ = model(input_tensor)
    
    # Measure inference time
    for _ in range(num_runs):
        start_time = time.time()
        with torch.no_grad():
            _ = model(input_tensor)
        end_time = time.time()
        times.append(end_time - start_time)
    
    avg_time = np.mean(times)
    std_time = np.std(times)
    
    return avg_time, std_time

def measure_memory_usage(model, input_tensor):
    """
    Measure the memory usage of a model during inference.
    
    Args:
        model: The model to benchmark
        input_tensor: Input tensor for the model
        
    Returns:
        memory_usage: Memory usage in MB
    """
    # Get baseline memory usage
    baseline = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024
    
    # Run inference
    with torch.no_grad():
        _ = model(input_tensor)
    
    # Get memory usage after inference
    memory_usage = psutil.Process(os.getpid()).memory_info().rss / 1024 / 1024 - baseline
    
    return memory_usage

def calculate_iou(segmentation1, segmentation2, threshold=0.5):
    """
    Calculate IoU between two segmentation masks.
    
    Args:
        segmentation1: First segmentation mask
        segmentation2: Second segmentation mask
        threshold: Threshold for binary mask
        
    Returns:
        iou: IoU score
    """
    # Create binary masks
    mask1 = (segmentation1 > threshold).astype(np.uint8)
    mask2 = (segmentation2 > threshold).astype(np.uint8)
    
    # Calculate intersection and union
    intersection = np.logical_and(mask1, mask2).sum()
    union = np.logical_or(mask1, mask2).sum()
    
    # Calculate IoU
    iou = intersection / union if union > 0 else 0
    
    return iou

def benchmark_models(original_model, distilled_model, image_paths, batch_sizes=[1, 2, 4, 8]):
    """
    Benchmark the performance of the original and distilled models.
    
    Args:
        original_model: The original model
        distilled_model: The distilled model
        image_paths: List of paths to test images
        batch_sizes: List of batch sizes to test
        
    Returns:
        results: Dictionary containing benchmark results
    """
    results = {
        'batch_size': [],
        'original_time': [],
        'original_time_std': [],
        'distilled_time': [],
        'distilled_time_std': [],
        'speedup': [],
        'original_memory': [],
        'distilled_memory': [],
        'memory_reduction': [],
        'iou': []
    }
    
    # Preprocess images
    preprocessed_images = [preprocess_image(path) for path in image_paths]
    
    # Benchmark each batch size
    for batch_size in batch_sizes:
        print(f"Benchmarking with batch size {batch_size}...")
        
        # Create input batch
        batch_images = preprocessed_images[:batch_size]
        if len(batch_images) < batch_size:
            # Repeat images to fill batch
            batch_images = batch_images * (batch_size // len(batch_images) + 1)
            batch_images = batch_images[:batch_size]
        
        # Convert to tensors
        input_tensors = [normalize_for_model(img) for img in batch_images]
        input_batch = torch.cat(input_tensors, dim=0)
        
        # Measure inference time
        original_time, original_time_std = measure_inference_time(original_model, input_batch)
        distilled_time, distilled_time_std = measure_inference_time(distilled_model, input_batch)
        speedup = original_time / distilled_time
        
        # Measure memory usage
        original_memory = measure_memory_usage(original_model, input_batch)
        distilled_memory = measure_memory_usage(distilled_model, input_batch)
        memory_reduction = original_memory / distilled_memory
        
        # Measure segmentation accuracy
        ious = []
        for tensor in input_tensors:
            with torch.no_grad():
                original_output = original_model(tensor)
                distilled_output = distilled_model(tensor)
            
            original_segmentation = torch.sigmoid(original_output['segmentation']).cpu().numpy()[0, 0]
            distilled_segmentation = torch.sigmoid(distilled_output['segmentation']).cpu().numpy()[0, 0]
            
            iou = calculate_iou(original_segmentation, distilled_segmentation)
            ious.append(iou)
        
        avg_iou = np.mean(ious)
        
        # Store results
        results['batch_size'].append(batch_size)
        results['original_time'].append(original_time)
        results['original_time_std'].append(original_time_std)
        results['distilled_time'].append(distilled_time)
        results['distilled_time_std'].append(distilled_time_std)
        results['speedup'].append(speedup)
        results['original_memory'].append(original_memory)
        results['distilled_memory'].append(distilled_memory)
        results['memory_reduction'].append(memory_reduction)
        results['iou'].append(avg_iou)
        
        print(f"  Original: {original_time:.4f}s ± {original_time_std:.4f}s, {original_memory:.2f}MB")
        print(f"  Distilled: {distilled_time:.4f}s ± {distilled_time_std:.4f}s, {distilled_memory:.2f}MB")
        print(f"  Speedup: {speedup:.2f}x, Memory reduction: {memory_reduction:.2f}x, IoU: {avg_iou:.4f}")
    
    return results

def plot_benchmark_results(results, output_dir):
    """
    Plot benchmark results.
    
    Args:
        results: Dictionary containing benchmark results
        output_dir: Directory to save plots
    """
    # Create DataFrame
    df = pd.DataFrame(results)
    
    # Plot inference time
    plt.figure(figsize=(10, 6))
    plt.errorbar(df['batch_size'], df['original_time'], yerr=df['original_time_std'], 
                 marker='o', label='Original Model')
    plt.errorbar(df['batch_size'], df['distilled_time'], yerr=df['distilled_time_std'], 
                 marker='s', label='Distilled Model')
    plt.xlabel('Batch Size')
    plt.ylabel('Inference Time (s)')
    plt.title('Inference Time vs. Batch Size')
    plt.grid(True)
    plt.legend()
    plt.savefig(os.path.join(output_dir, 'inference_time.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # Plot speedup
    plt.figure(figsize=(10, 6))
    plt.plot(df['batch_size'], df['speedup'], marker='o')
    plt.xlabel('Batch Size')
    plt.ylabel('Speedup (x)')
    plt.title('Speedup vs. Batch Size')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'speedup.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # Plot memory usage
    plt.figure(figsize=(10, 6))
    plt.plot(df['batch_size'], df['original_memory'], marker='o', label='Original Model')
    plt.plot(df['batch_size'], df['distilled_memory'], marker='s', label='Distilled Model')
    plt.xlabel('Batch Size')
    plt.ylabel('Memory Usage (MB)')
    plt.title('Memory Usage vs. Batch Size')
    plt.grid(True)
    plt.legend()
    plt.savefig(os.path.join(output_dir, 'memory_usage.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # Plot memory reduction
    plt.figure(figsize=(10, 6))
    plt.plot(df['batch_size'], df['memory_reduction'], marker='o')
    plt.xlabel('Batch Size')
    plt.ylabel('Memory Reduction (x)')
    plt.title('Memory Reduction vs. Batch Size')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'memory_reduction.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # Plot IoU
    plt.figure(figsize=(10, 6))
    plt.plot(df['batch_size'], df['iou'], marker='o')
    plt.xlabel('Batch Size')
    plt.ylabel('IoU')
    plt.title('Segmentation IoU vs. Batch Size')
    plt.grid(True)
    plt.savefig(os.path.join(output_dir, 'iou.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # Save results to CSV
    df.to_csv(os.path.join(output_dir, 'benchmark_results.csv'), index=False)
    
    # Create summary table
    summary = pd.DataFrame({
        'Metric': ['Average Speedup', 'Average Memory Reduction', 'Average IoU'],
        'Value': [
            f"{df['speedup'].mean():.2f}x",
            f"{df['memory_reduction'].mean():.2f}x",
            f"{df['iou'].mean():.4f}"
        ]
    })
    
    summary.to_csv(os.path.join(output_dir, 'benchmark_summary.csv'), index=False)
    
    # Print summary
    print("\nBenchmark Summary:")
    print(f"Average Speedup: {df['speedup'].mean():.2f}x")
    print(f"Average Memory Reduction: {df['memory_reduction'].mean():.2f}x")
    print(f"Average IoU: {df['iou'].mean():.4f}")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Benchmark models')
    parser.add_argument('--original_model', type=str, default='chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth',
                        help='Path to original model')
    parser.add_argument('--distilled_model', type=str, required=True,
                        help='Path to distilled model')
    parser.add_argument('--model_type', type=str, default='tiny', choices=['small', 'tiny'],
                        help='Type of distilled model')
    parser.add_argument('--image_dir', type=str, default="C:\\Users\\<USER>\\OneDrive\\Desktop",
                        help='Directory containing test images')
    parser.add_argument('--output_dir', type=str, default="C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\benchmark",
                        help='Path to output directory')
    parser.add_argument('--batch_sizes', type=int, nargs='+', default=[1, 2, 4, 8],
                        help='Batch sizes to test')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load models
    print(f"Loading original model from {args.original_model}")
    original_model = load_original_model(args.original_model)
    
    print(f"Loading distilled model from {args.distilled_model}")
    distilled_model = load_distilled_model(args.distilled_model, args.model_type)
    
    # Print model sizes
    original_params = sum(p.numel() for p in original_model.parameters() if p.requires_grad)
    distilled_params = sum(p.numel() for p in distilled_model.parameters() if p.requires_grad)
    print(f"Original model parameters: {original_params:,}")
    print(f"Distilled model parameters: {distilled_params:,}")
    print(f"Size reduction: {original_params / distilled_params:.2f}x")
    
    # Find test images
    image_paths = []
    for file in os.listdir(args.image_dir):
        if file.lower().endswith(('.png', '.jpg', '.jpeg')):
            image_paths.append(os.path.join(args.image_dir, file))
    
    if not image_paths:
        print(f"No images found in {args.image_dir}")
        return
    
    print(f"Found {len(image_paths)} test images")
    
    # Benchmark models
    results = benchmark_models(original_model, distilled_model, image_paths, args.batch_sizes)
    
    # Plot results
    plot_benchmark_results(results, args.output_dir)
    
    print(f"\nAll benchmark results saved to {args.output_dir}")

if __name__ == "__main__":
    main()
