#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ensemble inference script for chess board detection model v5.2.
This script loads multiple model checkpoints and performs ensemble inference.
"""

import os
import sys
import argparse
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torchvision
from PIL import Image
import cv2
import albumentations as A
from albumentations.pytorch import ToTensorV2

from config import MODELS_DIR, DATA_DIR, DEVICE, INPUT_SIZE
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from utils.visualization import visualize_heatmaps_and_keypoints, visualize_peak_to_second_ratio
from utils.metrics import calculate_corner_confidence_metrics, calculate_corner_confusion_matrix

def load_ensemble_models(ensemble_path, device=DEVICE):
    """
    Load ensemble models from the ensemble configuration file.
    
    Args:
        ensemble_path: Path to the ensemble configuration file
        device: Device to load the models on
        
    Returns:
        List of (name, model) tuples
    """
    # Load ensemble configuration
    ensemble_config = torch.load(ensemble_path, map_location=device)
    
    # Get model paths and weights
    model_paths = ensemble_config.get('model_paths', [])
    model_weights = ensemble_config.get('model_weights', [1.0] * len(model_paths))
    
    # Load models
    ensemble_models = []
    
    for i, (path, weight) in enumerate(zip(model_paths, model_weights)):
        # Skip 'final' placeholder
        if path == 'final':
            continue
            
        try:
            # Create model
            model = EnhancedChessBoardUNetV5_2(
                n_channels=3,
                dropout_rate=0.3,
                use_batch_norm=True
            ).to(device)
            
            # Load weights
            model.load_state_dict(torch.load(path, map_location=device))
            model.eval()
            
            # Add to ensemble
            name = f"model_{i}"
            ensemble_models.append((name, model, weight))
            print(f"Loaded model from {path} with weight {weight}")
        except Exception as e:
            print(f"Warning: Could not load model from {path}: {e}")
    
    return ensemble_models

def ensemble_predict(models, x):
    """
    Perform ensemble prediction by averaging the outputs of multiple models.
    
    Args:
        models: List of (name, model, weight) tuples
        x: Input tensor
        
    Returns:
        Dictionary of ensemble outputs
    """
    with torch.no_grad():
        # Get predictions from all models
        all_segmentations = []
        all_heatmaps = []
        
        for name, model, weight in models:
            outputs = model(x)
            all_segmentations.append(outputs['segmentation'] * weight)
            all_heatmaps.append(outputs['corner_heatmaps'] * weight)
        
        # Calculate weighted average
        total_weight = sum(weight for _, _, weight in models)
        avg_segmentation = torch.sum(torch.stack(all_segmentations), dim=0) / total_weight
        avg_heatmaps = torch.sum(torch.stack(all_heatmaps), dim=0) / total_weight
        
        return {
            'segmentation': avg_segmentation,
            'corner_heatmaps': avg_heatmaps
        }

def preprocess_image(image_path, input_size=INPUT_SIZE):
    """
    Preprocess an image for inference.
    
    Args:
        image_path: Path to the image
        input_size: Input size for the model
        
    Returns:
        Preprocessed image tensor
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Create transform
    transform = A.Compose([
        A.Resize(height=input_size, width=input_size),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ])
    
    # Apply transform
    transformed = transform(image=image)
    image_tensor = transformed['image'].unsqueeze(0)  # Add batch dimension
    
    return image_tensor, image

def main():
    """
    Main function for ensemble inference.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Ensemble inference for chess board detection model v5.2')
    parser.add_argument('--ensemble_path', type=str, required=True, help='Path to ensemble model configuration')
    parser.add_argument('--image_path', type=str, required=True, help='Path to input image')
    parser.add_argument('--output_dir', type=str, default='outputs', help='Output directory')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    args = parser.parse_args()
    
    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load ensemble models
    ensemble_models = load_ensemble_models(args.ensemble_path, device)
    
    if not ensemble_models:
        print("Error: No models loaded for ensemble")
        return
    
    # Preprocess image
    try:
        image_tensor, original_image = preprocess_image(args.image_path)
        image_tensor = image_tensor.to(device)
    except Exception as e:
        print(f"Error preprocessing image: {e}")
        return
    
    # Perform ensemble prediction
    outputs = ensemble_predict(ensemble_models, image_tensor)
    
    # Visualize results
    output_path = os.path.join(args.output_dir, f'ensemble_prediction_{os.path.basename(args.image_path)}')
    
    # Create dummy target heatmaps (all zeros)
    dummy_targets = torch.zeros_like(outputs['corner_heatmaps'])
    
    # Visualize heatmaps and keypoints
    visualize_heatmaps_and_keypoints(
        images=image_tensor,
        heatmaps=outputs['corner_heatmaps'],
        segmentations=outputs['segmentation'],
        target_heatmaps=dummy_targets,
        save_path=output_path,
        max_samples=1,
        show_targets=False
    )
    
    # Calculate confidence metrics
    confidence_metrics = calculate_corner_confidence_metrics(outputs['corner_heatmaps'])
    
    # Print confidence metrics
    print("\nConfidence Metrics:")
    for key, value in confidence_metrics.items():
        if isinstance(value, (int, float)):
            print(f"  {key}: {value:.4f}")
    
    # Save confidence metrics
    metrics_path = os.path.join(args.output_dir, f'metrics_{os.path.basename(args.image_path)}.json')
    with open(metrics_path, 'w') as f:
        json.dump({k: float(v) if isinstance(v, (int, float)) else v for k, v in confidence_metrics.items()}, f, indent=4)
    
    print(f"\nResults saved to {args.output_dir}")

if __name__ == '__main__':
    main()
