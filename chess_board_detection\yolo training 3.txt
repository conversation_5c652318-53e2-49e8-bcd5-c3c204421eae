PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> .\chess_board_detection\piece_detection\train_targeted_model.bat
Training YOLO model on targeted augmented dataset...
Using configuration from the first successful v11n model

This script will:
1. Train the model for 100 epochs with patience=100
2. Use the exact parameters from the first successful v11n model
3. Apply class weighting to focus on problematic pieces
4. Generate confusion matrix and visualizations for problematic pieces
5. Display per-class metrics to evaluate improvement

Would you like to run the learning rate finder first? (y/n): y
Running with learning rate finder...
Python version: 3.11.4 (tags/v3.11.4:d2340ef, Jun  7 2023, 05:45:37) [MSC v.1934 64 bit (AMD64)]
PyTorch version: 2.5.1+cu121
CUDA available: True
CUDA device: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB
CUDA Version: 12.1
cuDNN Version: 90100
Training on: 0
GPU: NVIDIA GeForce RTX 3050 6GB Laptop GPU
GPU Memory: 6.44 GB

=== Training Targeted Model (max 100 epochs) ===
Dataset: chess_board_detection/piece_detection/targeted_dataset/dataset.yaml
Model: yolo11n.pt
Project directory: chess_board_detection/piece_detection/models/targeted_yolo
Finding optimal learning rate...

Running learning rate finder...
Tuner: Initialized Tuner instance with 'tune_dir=runs\detect\tune'
Tuner:  Learn about tuning at https://docs.ultralytics.com/guides/hyperparameter-tuning
Tuner: Starting iteration 1/10 with hyperparameters: {'lr0': 0.01, 'lrf': 0.01, 'momentum': 0.937, 'weight_decay': 0.0005, 'warmup_epochs': 3.0, 'warmup_momentum': 0.8, 'box': 7.5, 'cls': 0.5, 'dfl': 1.5, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.1, 'scale': 0.5, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5, 'bgr': 0.0, 'mosaic': 1.0, 'mixup': 0.0, 'cutmix': 0.0, 'copy_paste': 0.0}
New https://pypi.org/project/ultralytics/8.3.141 available  Update with 'pip install -U ultralytics'
Ultralytics 8.3.139  Python-3.11.4 torch-2.5.1+cu121 CUDA:0 (NVIDIA GeForce RTX 3050 6GB Laptop GPU, 6144MiB)
engine\trainer: agnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=chess_board_detection/piece_detection/targeted_dataset/dataset.yaml, degrees=0.0, deterministic=True, device=0, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=416, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=yolo11n.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs\detect\train, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None
Overriding model.yaml nc=80 with nc=12

                   from  n    params  module                                       arguments              

  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]          

  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]         

  2                  -1  1      6640  ultralytics.nn.modules.block.C3k2            [32, 64, 1, False, 0.25]
  3                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]         

  4                  -1  1     26080  ultralytics.nn.modules.block.C3k2            [64, 128, 1, False, 0.25]
  5                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]       

  6                  -1  1     87040  ultralytics.nn.modules.block.C3k2            [128, 128, 1, True]    

  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]       

  8                  -1  1    346112  ultralytics.nn.modules.block.C3k2            [256, 256, 1, True]    

  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]          

 10                  -1  1    249728  ultralytics.nn.modules.block.C2PSA           [256, 256, 1]          

 11                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']   

 12             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                    

 13                  -1  1    111296  ultralytics.nn.modules.block.C3k2            [384, 128, 1, False]   

 14                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']   

 15             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                    

 16                  -1  1     32096  ultralytics.nn.modules.block.C3k2            [256, 64, 1, False]    

 17                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]         

 18            [-1, 13]  1         0  ultralytics.nn.modules.conv.Concat           [1]                    

 19                  -1  1     86720  ultralytics.nn.modules.block.C3k2            [192, 128, 1, False]   

 20                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]       

 21            [-1, 10]  1         0  ultralytics.nn.modules.conv.Concat           [1]                    

 22                  -1  1    378880  ultralytics.nn.modules.block.C3k2            [384, 256, 1, True]    

 23        [16, 19, 22]  1    433012  ultralytics.nn.modules.head.Detect           [12, [64, 128, 256]]   

YOLO11n summary: 181 layers, 2,592,180 parameters, 2,592,164 gradients, 6.5 GFLOPs

Transferred 448/499 items from pretrained weights
Freezing layer 'model.23.dfl.conv.weight'
AMP: running Automatic Mixed Precision (AMP) checks...
AMP: checks passed 
train: Fast image access  (ping: 0.10.0 ms, read: 122.352.7 MB/s, size: 51.1 KB)
train: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\targe
train: New cache created: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\targeted_dataset\labels\train.cache
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
val: Fast image access  (ping: 0.10.0 ms, read: 495.0208.4 MB/s, size: 53.8 KB)
val: Scanning C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\targete
val: New cache created: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\piece_detection\targeted_dataset\labels\val.cache
Plotting labels to runs\detect\train\labels.jpg...
optimizer: 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically...
optimizer: AdamW(lr=0.000625, momentum=0.9) with parameter groups 81 weight(decay=0.0), 88 weight(decay=0.0005), 87 bias(decay=0.0)
Image sizes 416 train, 416 val
Using 8 dataloader workers
Logging results to runs\detect\train
Starting training for 100 epochs...

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      1/100      1.45G      1.341      2.978     0.9849        271        416: 100%|██████████| 289/289 [
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<0
                   all        243       4250      0.639      0.555      0.617      0.446

    Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      2/100      1.45G      1.074      1.057     0.9201        354        416: 100%|██████████| 289/289 [00:49<00:00,  
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<0 
                   all        243       4250      0.899      0.781      0.856      0.618

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      3/100      1.52G     0.9823     0.7879     0.9054        284        416: 100%|██████████| 289/289 [00:46<00:00,  
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<0
                   all        243       4250       0.94      0.848        0.9      0.633

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      4/100      1.52G     0.9265     0.6848      0.893        188        416: 100%|██████████| 289/289 [00:47<00:00,
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<0
                   all        243       4250       0.95      0.863      0.916      0.681    

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      5/100      1.52G     0.9262     0.6683     0.8985        408        416:  22%|██▏      | 63/289 [00:10<00:3      5/100      1.52G     0.9263     0.6688     0.8984        425       416:  22%|██▏       | 63/289 [00:11<00:3      5/100      1.52G     0.9263     0.6688         5/100      1.52G     0.9256     0.6685     0.8981        372        416:  22%|██▏          5/100      1.52G     0.9256     0.6685     0.8981        372        416:  22%|██▏          5/100      1.52G     0.9246     0.6682     0.8977        332        416:  22%|██▏          5/100      1.52G     0.9246     0.6682     0.8977        332        416:  23%|██▎          5/100      1.52G     0.9246     0.6677     0.8979        272        416:  23%|██▎          5/100      1.52G     0.9246     0.6677     0.8979        272        416:  23%|██▎          5/100      1.52G     0.9262     0.6684      0.898        338        416:  23%|██▎          5/100      1.52G     0.9262     0.6684      0.898        338        416:  24%|██▎          5/100      1.52G     0.9252     0.6677     0.8977        370        416:  24%|██▎          5/100      1.52G     0.9252     0.6677     0.8977        370        416:  24%|██▍          5/100      1.52G     0.9229     0.6666     0.8972        351        416:  24%|██▍          5/100      1.52G     0.9229     0.6666     0.8972        351        416:  24%|██▍          5/100      1.52G     0.9234     0.6668     0.8974        329        416:  24%|██▍          5/100      1.52G     0.9234     0.6668     0.8974        329        416:  25%|██▍          5/100      1.52G     0.9241     0.6668     0.8976        359        416:  25%|██▍          5/100      1.52G     0.9241     0.6668     0.8976        359        416:  25%|██▍          5/100      1.52G     0.9246     0.6669     0.8978        321        416:  25%|██▍          5/100      1.52G     0.9246     0.6669     0.8978        321        416:  25%|██▌          5/100      1.52G      0.924     0.6663      0.897        473        416:  25%|██▌          5/100      1.52G      0.924     0.6663      0.897        473        416:  26%|██▌          5/100      1.52G     0.9232     0.6661     0.8968        340        416:  26%|██▌          5/100      1.52G     0.9232     0.6661     0.8968        340        416:  26%|██▌          5/100      1.52G     0.9242     0.6659     0.8964        494        416:  26%|██▌          5/100      1.52G     0.9242     0.6659     0.8964        494        416:  26%|██▋          5/100      1.52G     0.9254     0.6661     0.8968        324        416:  26%|██▋          5/100      1.52G     0.9254     0.6661     0.8968        324        416:  27%|██▋          5/100      1.52G     0.9248      0.666     0.8964        464        416:  27%|██▋          5/100      1.52G     0.9248      0.666     0.8964        464        416:  27%|██▋          5/100      1.52G     0.9255     0.6659     0.8972        281        416:  27%|██▋          5/100      1.52G     0.9255     0.6659     0.8972        281        416:  27%|██▋          5/100      1.52G     0.9244     0.6653     0.8967        416        416:  27%|██▋          5/100      1.52G     0.9244     0.6653     0.8967        416        416:  28%|██▊          5/100      1.52G     0.9248     0.6652     0.8964        448        416:  28%|██▊          5/100      1.52G     0.9248     0.6652     0.8964        448        416:  28%|██▊          5/100      1.52G     0.9274     0.6657     0.8971        370        416:  28%|██▊          5/100      1.52G     0.9274     0.6657     0.8971        370        416:  28%|██▊          5/100      1.52G     0.9274     0.6663     0.8968        494        416:  28%|██▊          5/100      1.52G     0.9274     0.6663     0.8968        494        416:  29%|██▊          5/100      1.52G     0.9286     0.6668     0.8972        405        416:  29%|██▊          5/100      1.52G     0.9286     0.6668     0.8972        405        416:  29%|██▉          5/100      1.52G     0.9284     0.6665     0.8975        371        416:  29%|██▉          5/100      1.52G     0.9284     0.6665     0.8975        371        416:  29%|██▉          5/100      1.52G     0.9264     0.6653      0.897        372        416:  29%|██▉          5/100      1.52G     0.9264     0.6653      0.897        372        416:  30%|██▉          5/100      1.52G     0.9258     0.6658     0.8968        482        416:  30%|██▉          5/100      1.52G     0.9258     0.6658     0.8968        482        416:  30%|███          5/100      1.52G     0.9247     0.6648     0.8966        296        416:  30%|███          5/100      1.52G     0.9247     0.6648     0.8966        296        416:  30%|███          5/100      1.52G     0.9228     0.6637     0.8961        312        416:  30%|███          5/100      1.52G     0.9228     0.6637     0.8961        312        416:  31%|███          5/100      1.52G     0.9222     0.6634     0.8959        434        416:  31%|███          5/100      1.52G     0.9222     0.6634     0.8959        434        416:  31%|███          5/100      1.52G     0.9219      0.663     0.8958        436        416:  31%|███          5/100      1.52G     0.9219      0.663     0.8958        436        416:  31%|███▏         5/100      1.52G     0.9212     0.6626     0.8954        355        416:  31%|███▏         5/100      1.52G     0.9212     0.6626     0.8954        355        416:  32%|███▏         5/100      1.52G     0.9199     0.6619     0.8952        324        416:  32%|███▏         5/100      1.52G     0.9199     0.6619     0.8952        324        416:  32%|███▏         5/100      1.52G     0.9202      0.662     0.8952        401        416:  32%|███▏         5/100      1.52G     0.9202      0.662     0.8952        401        416:  33%|███▎         5/100      1.52G     0.9226      0.663     0.8965        382        416:  33%|███▎         5/100      1.52G     0.9226      0.663     0.8965        382        416:  33%|███▎         5/100      1.52G     0.9217     0.6626     0.8963        365        416:  33%|███▎         5/100      1.52G     0.9217     0.6626     0.8963        365        416:  33%|███▎         5/100      1.52G     0.9214     0.6624      0.896        335        416:  33%|███▎         5/100      1.52G     0.9214     0.6624      0.896        335        416:  34%|███▎         5/100      1.52G     0.9231     0.6627      0.896        431        416:  34%|███▎         5/100      1.52G     0.9231     0.6627      0.896        431        416:  34%|███▍         5/100      1.52G     0.9221     0.6617     0.8959        282        416:  34%|███▍         5/100      1.52G     0.9221     0.6617     0.8959        282        416:  34%|███▍         5/100      1.52G     0.9237      0.663      0.896        471        416:  34%|███▍         5/100      1.52G     0.9237      0.663      0.896        471        416:  35%|███▍         5/100      1.52G     0.9227      0.663     0.8959        356        416:  35%|███▍         5/100      1.52G     0.9227      0.663     0.8959        356        416:  35%|███▍         5/100      1.52G     0.9231     0.6638     0.8963        283        416:  35%|███▍         5/100      1.52G     0.9231     0.6638     0.8963        283        416:  35%|███▌         5/100      1.52G     0.9249     0.6644     0.8968        409        416:  35%|███▌         5/100      1.52G     0.9249     0.6644     0.8968        409        416:  36%|███▌         5/100      1.52G     0.9253     0.6644     0.8966        352        416:  36%|███▌         5/100      1.52G     0.9253     0.6644     0.8966        352        416:  36%|███▌         5/100      1.52G     0.9247     0.6644     0.8966        285        416:  36%|███▌         5/100      1.52G     0.9247     0.6644     0.8966        285        416:  36%|███▋         5/100      1.52G     0.9263     0.6652     0.8971        308        416:  36%|███▋         5/100      1.52G     0.9263     0.6652     0.8971        308        416:  37%|███▋         5/100      1.52G      0.925     0.6647     0.8969        300        416:  37%|███▋         5/100      1.52G      0.925     0.6647     0.8969        300        416:  37%|███▋         5/100      1.52G     0.9259     0.6651     0.8968        419        416:  37%|███▋         5/100      1.52G     0.9259     0.6651     0.8968        419        416:  37%|███▋         5/100      1.52G     0.9257     0.6648     0.8967        390        416:  37%|███▋         5/100      1.52G     0.9257     0.6648     0.8967        390        416:  38%|███▊         5/100      1.52G     0.9252     0.6645     0.8963        410        416:  38%|███▊         5/100      1.52G     0.9252     0.6645     0.8963        410        416:  38%|███▊         5/100      1.52G     0.9259     0.6645     0.8964        340        416:  38%|███▊         5/100      1.52G     0.9259     0.6645     0.8964        340        416:  38%|███▊         5/100      1.52G     0.9256     0.6643     0.8964        345        416:  38%|███▊         5/100      1.52G     0.9256     0.6643     0.8964        345        416:  39%|███▉         5/100      1.52G     0.9257     0.6641     0.8964        396        416:  39%|███▉         5/100      1.52G     0.9257     0.6641     0.8964        396        416:  39%|███▉         5/100      1.52G      0.925     0.6636     0.8961        387        416:  39%|███▉         5/100      1.52G      0.925     0.6636     0.8961        387        416:  39%|███▉         5/100      1.52G     0.9247     0.6641     0.8961        289        416:  39%|███▉         5/100      1.52G     0.9247     0.6641     0.8961        289        416:  40%|███▉         5/100      1.52G     0.9256     0.6651     0.8965        372        416:  40%|███▉         5/100      1.52G     0.9256     0.6651     0.8965        372        416:  40%|████         5/100      1.52G     0.9254     0.6642     0.8965        329        416:  40%|████         5/100      1.52G     0.9254     0.6642     0.8965        329        416:  40%|████         5/100      1.52G     0.9241     0.6641     0.8961        470        416:  40%|████         5/100      1.52G     0.9241     0.6641     0.8961        470        416:  41%|████         5/100      1.52G     0.9232     0.6635     0.8958        405        416:  41%|████         5/100      1.52G     0.9232     0.6635     0.8958        405        416:  41%|████         5/100      1.52G     0.9236     0.6634     0.8958        458        416:  41%|████         5/100      1.52G     0.9236     0.6634     0.8958        458        416:  42%|████▏        5/100      1.52G     0.9236     0.6637     0.8957        380        416:  42%|████▏        5/100      1.52G     0.9236     0.6637     0.8957        380        416:  42%|████▏        5/100      1.52G     0.9246     0.6638     0.8961        397        416:  42%|████▏        5/100      1.52G     0.9246     0.6638     0.8961        397        416:  42%|████▏        5/100      1.52G     0.9247     0.6631     0.8961        375        416:  42%|████▏        5/100      1.52G     0.9247     0.6631     0.8961        375        416:  43%|████▎        5/100      1.52G     0.9273      0.664     0.8963        449        416:  43%|████▎        5/100      1.52G     0.9273      0.664     0.8963        449        416:  43%|████▎        5/100      1.52G     0.9278     0.6645     0.8963        448        416:  43%|████▎        5/100      1.52G     0.9278     0.6645     0.8963        448        416:  43%|████▎        5/100      1.52G     0.9278     0.6644     0.8959        532        416:  43%|████▎        5/100      1.52G     0.9278     0.6644     0.8959        532        416:  44%|████▎        5/100      1.52G     0.9278     0.6644     0.8961        317        416:  44%|████▎        5/100      1.52G     0.9278     0.6644     0.8961        317        416:  44%|████▍        5/100      1.52G     0.9283     0.6643     0.8963        351        416:  44%|████▍        5/100      1.52G     0.9283     0.6643     0.8963        351        416:  44%|████▍        5/100      1.52G      0.928     0.6642     0.8963        399        416:  44%|████▍        5/100      1.52G      0.928     0.6642     0.8963        399        416:  45%|████▍        5/100      1.52G     0.9286     0.6641     0.8963        428        416:  45%|████▍        5/100      1.52G     0.9286     0.6641     0.8963        428        416:  45%|████▍        5/100      1.52G     0.9275     0.6634     0.8961        349        416:  45%|████▍        5/100      1.52G     0.9275     0.6634     0.8961        349        416:  45%|████▌        5/100      1.52G     0.9281     0.6636      0.896        435        416:  45%|████▌        5/100      1.52G     0.9281     0.6636      0.896        435        416:  46%|████▌        5/100      1.52G     0.9272     0.6632     0.8959        293        416:  46%|████▌        5/100      1.52G     0.9272     0.6632     0.8959        293        416:  46%|████▌        5/100      1.52G     0.9265     0.6631     0.8956        387        416:  46%|████▌        5/100      1.52G     0.9265     0.6631     0.8956        387        416:  46%|████▋        5/100      1.52G      0.926     0.6628     0.8955        324        416:  46%|████▋        5/100      1.52G      0.926     0.6628     0.8955        324        416:  47%|████▋        5/100      1.52G     0.9257     0.6625     0.8954        337        416:  47%|████▋        5/100      1.52G     0.9257     0.6625     0.8954        337        416:  47%|████▋        5/100      1.52G     0.9253      0.662     0.8953        438        416:  47%|████▋        5/100      1.52G     0.9253      0.662     0.8953        438        416:  47%|████▋        5/100      1.52G      0.925     0.6615     0.8951        437        416:  47%|████▋        5/100      1.52G      0.925     0.6615     0.8951        437        416:  48%|████▊        5/100      1.52G      0.925     0.6613      0.895        427        416:  48%|████▊        5/100      1.52G      0.925     0.6613      0.895        427        416:  48%|████▊        5/100      1.52G     0.9263     0.6618     0.8954        410        416:  48%|████▊        5/100      1.52G     0.9263     0.6618     0.8954        410        416:  48%|████▊        5/100      1.52G     0.9275     0.6619     0.8965        357        416:  48%|████▊        5/100      1.52G     0.9275     0.6619     0.8965        357        416:  49%|████▉        5/100      1.52G     0.9282     0.6618     0.8967        372        416:  49%|████▉        5/100      1.52G     0.9282     0.6618     0.8967        372        416:  49%|████▉        5/100      1.52G     0.9279     0.6618     0.8967        466        416:  49%|████▉        5/100      1.52G     0.9279     0.6618     0.8967        466        416:  49%|████▉        5/100      1.52G     0.9284     0.6619     0.8972        274        416:  49%|████▉        5/100      1.52G     0.9284     0.6619     0.8972        274        416:  50%|████▉        5/100      1.52G       0.93     0.6629     0.8974        407        416:  50%|████▉        5/100      1.52G       0.93     0.6629     0.8974        407        416:  50%|█████        5/100      1.52G     0.9309     0.6631     0.8979        288        416:  50%|█████        5/100      1.52G     0.9309     0.6631     0.8979        288        416:  51%|█████        5/100      1.52G     0.9303     0.6625     0.8977        340        416:  51%|█████        5/100      1.52G     0.9303     0.6625     0.8977        340        416:  51%|█████        5/100      1.52G     0.9299     0.6624     0.8975        367        416:  51%|█████        5/100      1.52G     0.9299     0.6624     0.8975        367        416:  51%|█████        5/100      1.52G     0.9299     0.6625     0.8974        493        416:  51%|█████        5/100      1.52G     0.9299     0.6625     0.8974        493        416:  52%|█████▏       5/100      1.52G     0.9297     0.6625     0.8974        357        416:  52%|█████▏       5/100      1.52G     0.9297     0.6625     0.8974        357        416:  52%|█████▏       5/100      1.52G     0.9287      0.662     0.8972        343        416:  52%|█████▏       5/100      1.52G     0.9287      0.662     0.8972        343        416:  52%|█████▏       5/100      1.52G     0.9288     0.6619     0.8974        343        416:  52%|█████▏       5/100      1.52G     0.9288     0.6619     0.8974        343        416:  53%|█████▎       5/100      1.52G     0.9285     0.6613     0.8972        420        416:  53%|█████▎       5/100      1.52G     0.9285     0.6613     0.8972        420        416:  53%|█████▎       5/100      1.52G     0.9289     0.6612     0.8973        377        416:  53%|█████▎       5/100      1.52G     0.9289     0.6612     0.8973        377        416:  53%|█████▎       5/100      1.52G     0.9289     0.6611     0.8974        412        416:  53%|█████▎       5/100      1.52G     0.9289     0.6611     0.8974        412        416:  54%|█████▎       5/100      1.52G     0.9292     0.6615     0.8976        383        416:  54%|█████▎       5/100      1.52G     0.9292     0.6615     0.8976        383        416:  54%|█████▍       5/100      1.52G     0.9294     0.6618     0.8974        613        416:  54%|█████▍       5/100      1.52G     0.9294     0.6618     0.8974        613        416:  54%|█████▍       5/100      1.52G     0.9284      0.661     0.8971        340        416:  54%|█████▍ 00      1.52G     0.9284      0.661     0.8971        340        416:  55%|█████▍        5/.52G     0.9282     0.6608      0.897        315        416:  55%|█████▍        5/100      0.9282     0.6608      0.897        315        416:  55%|█████▌        5/100      1.52G      0.6605     0.8969        421        416:  55%|█████▌        5/100      1.52G     0.9281      0.8969        421        416:  55%|█████▌        5/100      1.52G     0.9284      0.6617        502        416:  55%|█████▌        5/100      1.52G     0.9284      0.661      0.8502        416:  56%|█████▌        5/100      1.52G     0.9278     0.6607     0.8969         416:  56%|█████▌        5/100      1.52G     0.9278     0.6607     0.8969        378     6%|█████▌        5/100      1.52G     0.9275     0.6607     0.8969        402        416:          5/100      1.52G     0.9275     0.6607     0.8969        402        416:  56%|█████/100      1.52G     0.9272     0.6603     0.8968        478        416:  56%|█████▋         1.52G     0.9272     0.6603     0.8968        478        416:  57%|█████▋        5/100      0.9273     0.6603     0.8969        423        416:  57%|█████▋        5/100      1.52G      0.6603     0.8969        423        416:  57%|█████▋        5/100      1.52G     0.92712     0.8969        335        416:  57%|█████▋        5/100      1.52G     0.9271     0.66969        335        416:  57%|█████▋        5/100      1.52G     0.9269     0.6602     0.  325        416:  57%|█████▋        5/100      1.52G     0.9269     0.6602     0.8969         416:  58%|█████▊        5/100      1.52G     0.9273     0.6603     0.8972        357    58%|█████▊        5/100      1.52G     0.9273     0.6603     0.8972        357        416:      5/100      1.52G     0.9276     0.6602     0.8972        440        416:  58%|█████▊       5/100      1.52G     0.9276     0.6602     0.8972        440        416:  58%|█████▊       5/100      1.52G     0.9277     0.6601     0.8972        465        416:  58%|█████▊       5/100      1.52G     0.9277     0.6601     0.8972        465        416:  59%|█████▉       5/100      1.52G     0.9279     0.6604     0.8971        400        416:  59%|█████▉       5/100      1.52G     0.9279     0.6604     0.8971        400        416:  59%|█████▉       5/100      1.52G     0.9275       0.66     0.8969        448        416:  59%|█████▉       5/100      1.52G     0.9275       0.66     0.8969        448        416:  60%|█████▉       5/100      1.52G      0.929     0.6602     0.8977        360        416:  60%|█████▉       5/100      1.52G      0.929     0.6602     0.8977        360        416:  60%|█████▉       5/100      1.52G     0.9286     0.6601     0.8976        314        416:  60%|█████▉       5/100      1.52G     0.9286     0.6601     0.8976        314        416:  60%|██████       5/100      1.52G     0.9289     0.6601     0.8976        395        416:  60%|██████       5/100      1.52G     0.9289     0.6601     0.8976        395        416:  61%|██████       5/100      1.52G     0.9292     0.6601     0.8977        396        416:  61%|██████       5/100      1.52G     0.9292     0.6601     0.8977        396        416:  61%|██████       5/100      1.52G      0.929     0.6599     0.8976        437        416:  61%|██████       5/100      1.52G      0.929     0.6599     0.8976        437        416:  61%|██████       5/100      1.52G     0.9291     0.6598     0.8975        404        416:  61%|██████       5/100      1.52G     0.9291     0.6598     0.8975        404        416:  62%|██████▏      5/100      1.52G     0.9291     0.6601     0.8974        427        416:  62%|██████▏      5/100      1.52G     0.9291     0.6601     0.8974        427        416:  62%|██████▏      5/100      1.52G     0.9291     0.6599     0.8973        444        416:  62%|██████▏      5/100      1.52G     0.9291     0.6599     0.8973        444        416:  62%|██████▏3%|██████▎   | 181/2      5/100      1.52G     0.9287     0.6597      0.897        352        416:  63%|██████▎   | 181/2      5/100      1.52G     0.9287     0.6597      0.897        352        416:  63%|██████▎   | 182/2      5/100      1.52G     0.9291     0.6599      0.897        328        416:  63%|██████▎   | 182/2      5/100      1.52G     0.9291     0.6599      0.897        328        416:  63%|██████▎   | 183/2      5/100      1.52G     0.9293       0.66     0.8972        432        416:  63%|██████▎   | 183/2      5/100      1.52G     0.9293       0.66     0.8972        432        416:  64%|██████▎   | 184/2      5/100      1.52G     0.9292     0.6599     0.8973        350        416:  64%|██████▎   | 184/2      5/100      1.52G     0.9292     0.6599     0.8973        350        416:  64%|██████▍   | 185/2      5/100      1.52G       0.93     0.6602     0.8974        454        416:  64%|██████▍   | 185/2      5/100      1.52G       0.93     0.6602     0.8974        454        416:  64%|██████▍   | 186/2      5/100      1.52G     0.9302     0.6606     0.8974        327        416:  64%|██████▍   | 186/2      5/100      1.52G     0.9302     0.6606     0.8974        327        416:  65%|██████▍   | 187/2      5/100      1.52G       0.93     0.6604     0.8973        394        416:  65%|██████▍   | 187/2      5/100      1.52G       0.93     0.6604     0.8973        394        416:  65%|██████▌   | 188/2      5/100      1.52G     0.9291     0.6602     0.8971        401        416:  65%|██████▌   | 188/2      5/100      1.52G     0.9291     0.6602     0.8971        401        416:  65%|██████▌   | 189/2      5/100      1.52G     0.9287     0.6598      0.897        349        416:  65%|██████▌   | 189/2      5/100      1.52G     0.9287     0.6598      0.897        349        416:  66%|██████▌   | 190/2      5/100      1.52G     0.9288     0.6598     0.8973        331        416:  66%|██████▌   | 190/2      5/100      1.52G     0.9288     0.6598     0.8973              5/100      1.52G     0.9282     0.6593     0.8973        319        416:  66%|██████▌      5/100      1.52G     0.9282     0.6593     0.8973        319        416:  66%|██████▋      5/100      1.52G     0.9279     0.6594     0.8973        337        416:  66%|██████▋      5/100      1.52G     0.9279     0.6594     0.8973        337        416:  67%|██████▋      5/100      1.52G     0.9273      0.659     0.8971        376        416:  67%|██████▋      5/100      1.52G     0.9273      0.659     0.8971        376        416:  67%|██████▋      5/100      1.52G      0.928     0.6597     0.8973        306        416:  67%|██████▋      5/100      1.52G      0.928     0.6597     0.8973        306        416:  67%|██████▋      5/100      1.52G     0.9277     0.6593     0.8973        396        416:  67%|██████▋      5/100      1.52G     0.9277     0.6593     0.8973        396        416:  68%|██████▊      5/100      1.52G     0.9281     0.6593     0.8975        332        416:  68%|██████▊      5/100      1.52G     0.9281     0.6593     0.8975        332        416:  68%|██████▊      5/100      1.52G     0.9279     0.6594     0.8974        456        416:  68%|██████▊      5/100      1.52G     0.9279     0.6594     0.8974        456        416:  69%|██████▊      5/100      1.52G     0.9274     0.6592     0.8973        491        416:  69%|██████▊      5/100      1.52G     0.9274     0.6592     0.8973        491        416:  69%|██████▉      5/100      1.52G     0.9263     0.6586     0.8971        294        416:  69%|██████▉      5/100      1.52G     0.9263     0.6586     0.8971        294        416:  69%|██████▉      5/100      1.52G     0.9254     0.6584     0.8969        318        416:  69%|██████▉      5/100      1.52G     0.9254     0.6584     0.8969        318        416:  70%|██████▉      5/100      1.52G     0.9253     0.6587     0.8971        249        416:  70%|██████▉      5/100      1.52G     0.9253     0.6587     0.8971        249         5/100      1.52G     0.9253     0.6586     0.8969        367        416:  70%|██████▉   | 202      5/100      1.52G     0.9253     0.6586     0.8969        367        416:  70%|███████   | 203      5/100      1.52G      0.925     0.6586     0.8967        338        416:  70%|███████   | 203      5/100      1.52G      0.925     0.6586     0.8967        338        416:  71%|███████   | 204      5/100      1.52G     0.9244     0.6582     0.8965        440        416:  71%|███████   | 204      5/100      1.52G     0.9244     0.6582     0.8965        440        416:  71%|███████   | 205      5/100      1.52G     0.9235     0.6577     0.8965        342        416:  71%|███████   | 205      5/100      1.52G     0.9235     0.6577     0.8965        342        416:  71%|███████▏  | 206      5/100      1.52G     0.9228     0.6572     0.8962        368        416:  71%|███████▏  | 206      5/100      1.52G     0.9228     0.6572     0.8962        368        416:  72%|███████▏  | 207      5/100      1.52G     0.9229     0.6572     0.8962        484        416:  72%|███████▏  | 207      5/100      1.52G     0.9229     0.6572     0.8962        484        416:  72%|███████▏  | 208      5/100      1.52G     0.9239     0.6576     0.8963        359        416:  72%|███████▏  | 208      5/100      1.52G     0.9239     0.6576     0.8963        359        416:  72%|███████▏  | 209      5/100      1.52G     0.9235     0.6572     0.8962        351        416:  72%|███████▏  | 209      5/100      1.52G     0.9235     0.6572     0.8962        351        416:  73%|███████▎  | 210      5/100      1.52G     0.9238     0.6575     0.8962        341        416:  73%|███████▎  | 210      5/100      1.52G     0.9238     0.6575     0.8962        341        416:  73%|███████▎  | 211      5/100      1.52G     0.9235     0.6571     0.8963        339        416:  73%|███████▎  | 211      5/100      1.52G     0.9235     0.6571     0.8963        339        416:  73%|███████▎  | 212      5/100      1.52G     0.9233      0.657     0.8962        328        416:  73%|███████▎  | 212      5/100      1.52G     0.9233      0.657     0.8962        328        416:  74%|███████▎  | 213      5/100      1.52G     0.9235     0.6572     0.8961        587        416:  74%|███████▎  | 213      5/100      1.52G     0.9235     0.6572     0.8961        587        416:  74%|███████▍  | 214      5/100      1.52G     0.9241     0.6571     0.8961        399        416:  74%|███████▍  | 214      5/100      1.52G     0.9241     0.6571     0.8961        399        416:  74%|███████▍  | 215      5/100      1.52G     0.9241     0.6574     0.8963        307        416:  74%|███████▍  | 215      5/100      1.52G     0.9241     0.6574     0.8963        307        416:  75%|███████▍  | 216      5/100      1.52G     0.9235     0.6572     0.8961        340        416:  75%|███████▍  | 216      5/100      1.52G     0.9235     0.6572     0.8961        340        416:  75%|███████▌  | 217      5/100      1.52G     0.9231     0.6571      0.896        388        416:  75%|███████▌  | 217      5/100      1.52G     0.9231     0.6571      0.896        388        416:  75%|███████▌  | 218      5/100      1.52G     0.9228     0.6569     0.8958        397        416:  75%|███████▌  | 218      5/100      1.52G     0.9228     0.6569     0.8958        397        416:  76%|███████▌  | 219      5/100      1.52G     0.9227     0.6566     0.8957        402        416:  76%|███████▌  | 219      5/100      1.52G     0.9227     0.6566     0.8957        402        416:  76%|███████▌  | 220      5/100      1.52G     0.9231     0.6567     0.8958        325        416:  76%|███████▌  | 220      5/100      1.52G     0.9231     0.6567     0.8958        325        416:  76%|███████▋  | 221      5/100      1.52G     0.9231     0.6568     0.8959        352        416:  76%|███████▋  | 221      5/100      1.52G     0.9231     0.6568     0.8959        352        416:  77%|███████▋  | 222      5/100      1.52G      0.923     0.6568     0.8958        495        416:  77%|███████▋  | 222      5/100      1.52G      0.923     0.6568     0.8958        495        416:  77%|███████▋  | 223      5/100      1.52G     0.9224     0.6567     0.8958        248        416:  77%|███████▋  | 223      5/100      1.52G     0.9224     0.6567     0.8958        248        416:  78%|███████▊  | 224      5/100      1.52G      0.922      0.657     0.8958        283        416:  78%|███████▊  | 224      5/100      1.52G      0.922      0.657     0.8958        283        416:  78%|███████▊  | 225      5/100      1.52G     0.9211     0.6564     0.8956        389        416:  78%|███████▊  | 225      5/100      1.52G     0.9211     0.6564     0.8956        389        416:  78%|      5/100      1.52G     0.9208     0.6562     0.8955        351        416:  78%|███████▊  | 226/289      5/100      1.52G     0.9208     0.6562     0.8955        351        416:  79%|███████▊  | 227/289      5/100      1.52G     0.9205     0.6559     0.8954        391        416:  79%|███████▊  | 227/289      5/100      1.52G     0.9205     0.6559     0.8954        391        416:  79%|███████▉  | 228/289      5/100      1.52G     0.9196     0.6555     0.8953        336        416:  79%|███████▉  | 228/289      5/100      1.52G     0.9196     0.6555     0.8953        336        416:  79%|███████▉  | 229/289      5/100      1.52G     0.9192     0.6554     0.8952        423        416:  79%|███████▉  | 229/289      5/100      1.52G     0.9192     0.6554     0.8952        423        416:  80%|███████▉  | 230/289      5/100      1.52G     0.9191     0.6554     0.8951        459        416:  80%|███████▉  | 230/289      5/100      1.52G     0.9191     0.6554     0.8951        459        416:  80%|███████▉  | 231/289      5/100      1.52G     0.9194     0.6555     0.8953        332        416:  80%|███████▉  | 231/289      5/100      1.52G     0.9194     0.6555     0.8953        332        416:  80%|████████  | 232/289      5/100      1.52G     0.9193     0.6552     0.8953        312        416:  80%|████████  | 232/289      5/100      1.52G     0.9193     0.6552     0.8953        312        416:  81%|████████  | 233/289      5/100      1.52G     0.9192      0.655     0.8951        449        416:  81%|████████  | 233/289      5/100      1.52G     0.9192      0.655     0.8951        449        416:  81%|████████  | 234/289      5/100      1.52G     0.9193     0.6546     0.8951        330        416:  81%|████████  | 234/289      5/100      1.52G     0.9193     0.6546     0.8951        330        416:  81%|████████▏ | 235/289      5/100      1.52G     0.9197     0.6547     0.8951        448        416:  81%|████████▏ | 235/289      5/100      1.52G     0.9197     0.6547     0.8951        448        416:  82%|████████▏ | 236/289      5/100      1.52G     0.9201     0.6547     0.8951        416        416:  82%|████████▏ | 236/289      5/100      1.52G     0.9201     0.6547     0.8951        416        416:  82%|████████▏ | 237/289      5/100      1.52G     0.9202     0.6546      0.895        454        416:  82%|████████▏ | 237/289      5/100      1.52G     0.9202     0.6546      0.895        454        416:  82%|████████▏ | 238/289      5/100      1.52G     0.9199     0.6542     0.8951        287        416:  82%|████████▏ | 238/289      5/100      1.52G     0.9199     0.6542     0.8951        287        416:  83%|████████▎ | 239/289      5/100      1.52G     0.9197      0.654      0.895        316        416:  83%|████████▎ | 239/289      5/100      1.52G     0.9197      0.654      0.895        316        416:  83%|████████▎ | 240/289      5/100      1.52G     0.9201     0.6542     0.8952        345        416:  83%|████████▎ | 240/289      5/100      1.52G     0.9201     0.6542     0.8952        345        416:  83%|████████▎ | 241/289      5/100      1.52G     0.9209     0.6544     0.8953        289        416:  83%|████████▎ | 241/289      5/100      1.52G     0.9209     0.6544     0.8953        289        416:  84%|████████▎ | 242/289      5/100      1.52G     0.9206     0.6541     0.8954        285        416:  84%|████████▎ | 242/289      5/100      1.52G     0.9206     0.6541     0.8954        285        416:  84%|████████▍ | 243/289      5/100      1.52G     0.9205     0.6539     0.8954        395        416:  84%|████████▍ | 243/289      5/100      1.52G     0.9205     0.6539     0.8954        395        416:  84%|████████▍ | 244/289      5/100      1.52G     0.9203     0.6536     0.8954        300        416:  84%|████████▍ | 244/289      5/100      1.52G     0.9203     0.6536     0.8954        300        416:  85%|████████▍ | 245/289      5/100      1.52G     0.9205     0.6536     0.8954        413        416:  85%|████████▍ | 245/289      5/100      1.52G     0.9205     0.6536     0.8954        413        416:  85%|████████▌ | 246/289      5/100      1.52G     0.9202     0.6535     0.8952        357        416:  85%|████████▌ | 246/289      5/100      1.52G      5/100      1.52G     0.9205     0.6536     0.8954        342        416:  85%|████████▌ | 247/289 [00:43<00:0      5/100      1.52G     0.9205     0.6536     0.8954        342        416:  86%|████████▌ | 248/289 [00:43<00:0      5/100      1.52G     0.9206     0.6535     0.8957        316        416:  86%|████████▌ | 248/289 [00:43<00:0      5/100      1.52G     0.9206     0.6535     0.8957        316        416:  86%|████████▌ | 249/289 [00:43<00:0      5/100      1.52G     0.9209     0.6536     0.8958        364        416:  86%|████████▌ | 249/289 [00:43<00:0      5/100      1.52G     0.9209     0.6536     0.8958        364        416:  87%|████████▋ | 250/289 [00:43<00:0      5/100      1.52G     0.9211     0.6537      0.896        251        416:  87%|████████▋ | 250/289 [00:43<00:0      5/100      1.52G     0.9211     0.6537      0.896        251        416:  87%|████████▋ | 251/289 [00:43<00:0      5/100      1.52G     0.9211     0.6535     0.8958        410        416:  87%|████████▋ | 251/289 [00:43<00:0      5/100      1.52G     0.9211     0.6535     0.8958        410        416:  87%|████████▋ | 252/289 [00:43<00:0      5/100      1.52G     0.9213     0.6535     0.8959        456        416:  87%|████████▋ | 252/289 [00:43<00:0      5/100      1.52G     0.9213     0.6535     0.8959        456        416:  88%|████████▊ | 253/289 [00:43<00:0      5/100      1.52G     0.9209     0.6534     0.8958        350        416:  88%|████████▊ | 253/289 [00:43<00:0      5/100      1.52G     0.9209     0.6534     0.8958        350        416:  88%|████████▊ | 254/289 [00:43<00:0      5/100      1.52G     0.9209     0.6533     0.8958        319        416:  88%|████████▊ | 254/289 [00:44<00:0      5/100      1.52G     0.9209     0.6533     0.8958        319        416:  88%|████████▊ | 255/289 [00:44<00:0      5/100      1.52G     0.9209     0.6532     0.8959        355        416:  88%|████████▊ | 255/289 [00:44<00:0      5/100      1.52G     0.9209     0.6532     0.8959        355        416:  89%|████████▊ | 256/289 [00:44<00:0      5/100      1.52G     0.9212     0.6537     0.8959        377        416:  89%|████████▊ | 256/289 [00:44<00:0      5/100      1.52G     0.9212     0.6537     0.8959        377        416:  89%|████████▉ | 257/289 [00:44<00:0      5/100      1.52G     0.9205     0.6533     0.8958        292        416:  89%|████████▉ | 257/289 [00:44<00:0      5/100      1.52G     0.9205     0.6533     0.8958        292        416:  89%|████████▉ | 258/289 [00:44<00:0      5/100      1.52G     0.9201     0.6531     0.8956        298        416:  89%|████████▉ | 258/289 [00:44<00:0      5/100      1.52G     0.9201     0.6531     0.8956        298        416:  90%|████████▉ | 259/289 [00:44<00:0      5/100      1.52G     0.9204     0.6531     0.8958        334        416:  90%|████████▉ | 259/289 [00:44<00:0      5/100      1.52G     0.9204     0.6531     0.8958        334        416:  90%|████████▉ | 260/289 [00:44<00:0      5/100      1.52G     0.9208     0.6534     0.8958        347        416:  90%|████████▉ | 260/289 [00:44<00:0      5/100      1.52G     0.9208     0.6534     0.8958        347        416:  90%|█████████ | 261/289 [00:44<00:0      5/100      1.52G      0.921     0.6534     0.8958        473        416:  90%|█████████ | 261/289 [00:44<00:0      5/100      1.52G      0.921     0.6534     0.8958        473        416:  91%|█████████ | 262/289 [00:44<00:0      5/100      1.52G     0.9202      0.653     0.8957        272        416:  91%|█████████ | 262/289 [00:45<00:0      5/100      1.52G     0.9202      0.653     0.8957        272        416:  91%|█████████ | 263/289 [00:45<00:0      5/100      1.52G     0.9203     0.6528     0.8957        303        416:  91%|█████████ | 263/289 [00:45<00:0      5/100      1.52G     0.9203     0.6528     0.8957        303        416:  91%|█████████▏| 264/289 [00:45<00:0      5/100      1.52G     0.9204     0.6528     0.8956        444        416:  91%|█████████▏| 264/289 [00:45<00:0      5/100      1.52G     0.9204     0.6528     0.8956        444        416:  92%|█████████▏| 265/289 [00:45<00:0      5/100      1.52G     0.9206     0.6528     0.8957        303        416:  92%|█████████▏| 265/289 [00:45<00:0      5/100      1.52G     0.9206     0.6528     0.8957        303        416:  92%|█████████▏| 266/289 [00:45<00:0      5/100      1.52G     0.9205     0.6527     0.8956        484        416:  92%|█████████▏| 266/289 [00:45<00:0      5/100      1.52G     0.9205     0.6527     0.8956        484        416:  92%|█████████▏| 267/289 [00:45<00:0      5/100      1.52G     0.9205     0.6526     0.8955        353        416:  92%|█████████▏| 267/289 [00:46<00:0      5/100      1.52G     0.9205     0.6526     0.8955        353        416:  93%|█████████▎| 268/289 [00:46<00:0      5/100      1.52G     0.9205     0.6526     0.8954        484        416:  93%|█████████▎| 268/289 [00:46<00:0      5/100      1.52G     0.9205     0.6526     0.8954        484        416:  93%|█████████▎| 269/289 [00:46<00:0      5/100      1.52G     0.9199     0.6522     0.8952        416        416:  93%|█████████▎| 269/289 [00:46<00:0      5/100      1.52G     0.9199     0.6522     0.8952        416        416:  93%|█████████▎| 270/289 [00:46<00:0      5/100      1.52G     0.9199     0.6521     0.8953        408        416:  93%|█████████▎| 270/289 [00:46<00:0      5/100      1.52G     0.9199     0.6521     0.8953        408        416:  94%|█████████▍| 271/289 [00:46<00:0      5/100      1.52G     0.9197     0.6519     0.8952        337        416:  94%|█████████▍| 271/289 [00:47<00:0      5/100      1.52G     0.9197     0.6519     0.8952        337        416:  94%|█████████▍| 272/289 [00:47<00:0      5/100      1.52G     0.9196     0.6519     0.8951        569        416:  94%|█████████▍| 272/289 [00:47<00:0      5/100      1.52G     0.9196     0.6519     0.8951        569        416:  94%|█████████▍| 273/289 [00:47<00:0      5/100      1.52G     0.9197     0.6517     0.8951        460        416:  94%|█████████▍| 273/289 [00:47<00:0      5/100      1.52G     0.9197     0.6517     0.8951        460        416:  95%|█████████▍| 274/289 [00:47<00:0      5/100      1.52G     0.9194     0.6516     0.8949        433        416:  95%|█████████▍| 274/289 [00:47<00:0      5/100      1.52G     0.9194     0.6516     0.8949        433        416:  95%|█████████▌| 275/289 [00:47<00:0      5/100      1.52G     0.9193     0.6516     0.8949        464        416:  95%|█████████▌| 275/289 [00:48<00:0      5/100      1.52G     0.9193     0.6516     0.8949        464        416:  96%|█████████▌| 276/289 [00:48<00:0      5/100      1.52G     0.9193     0.6514     0.8948        487        416:  96%|█████████▌| 276/289 [00:48<00:0      5/100      1.52G     0.9193     0.6514     0.8948        487        416:  96%|█████████▌| 277/289 [00:48<00:0      5/100      1.52G     0.9194     0.6514     0.8949        352        416:  96%|█████████▌| 277/289 [00:48<00:0      5/100      1.52G     0.9194     0.6514     0.8949        352        416:  96%|█████████▌| 278/289 [00:48<00:0      5/100      1.52G     0.9194     0.6514     0.8949        328        416:  96%|█████████▌| 278/289 [00:48<00:0      5/100      1.52G     0.9194     0.6514     0.8949        328        416:  97%|█████████▋| 279/289 [00:48<00:0      5/100      1.52G     0.9191     0.6512     0.8949        359        416:  97%|█████████▋| 279/289 [00:48<00:0      5/100      1.52G     0.9191     0.6512     0.8949        359        416:  97%|█████████▋| 280/289 [00:48<00:0      5/100      1.52G     0.9188     0.6509     0.8948        342        416:  97%|█████████▋| 280/289 [00:48<00:0      5/100      1.52G     0.9188     0.6509     0.8948        342        416:  97%|█████████▋| 281/289 [00:48<00:0      5/100      1.52G     0.9186     0.6508     0.8947        417        416:  97%|█████████▋| 281/289 [00:48<00:0      5/100      1.52G     0.9186     0.6508     0.8947        417        416:  98%|█████████▊| 282/289 [00:48<00:0      5/100      1.52G     0.9185     0.6506     0.8946        336        416:  98%|█████████▊| 282/289 [00:48<00:0      5/100      1.52G     0.9185     0.6506     0.8946        336        416:  98%|█████████▊| 283/289 [00:48<00:0      5/100      1.52G     0.9189     0.6506     0.8946        440        416:  98%|█████████▊| 283/289 [00:49<00:0      5/100      1.52G     0.9189     0.6506     0.8946        440        416:  98%|█████████▊| 284/289 [00:49<00:0      5/100      1.52G     0.9191     0.6506     0.8946        433        416:  98%|█████████▊| 284/289 [00:49<00:0      5/100      1.52G     0.9191     0.6506     0.8946        433        416:  99%|█████████▊| 285/289 [00:49<00:0      5/100      1.52G     0.9191     0.6505     0.8945        366        416:  99%|█████████▊| 285/289 [00:49<00:0      5/100      1.52G     0.9191     0.6505     0.8945        366        416:  99%|█████████▉| 286/289 [00:49<00:0      5/100      1.52G     0.9192     0.6504     0.8944        437        416:  99%|█████████▉| 286/289 [00:49<00:0      5/100      1.52G     0.9192     0.6504     0.8944        437        416:  99%|█████████▉| 287/289 [00:49<00:0      5/100      1.52G     0.9192     0.6504     0.8945        235        416:  99%|█████████▉| 287/289 [00:49<00:0      5/100      1.52G     0.9192     0.6504     0.8945        235        416: 100%|█████████▉| 288/289 [00:49<00:0      5/100      1.52G     0.9191     0.6505     0.8945        311        416: 100%|█████████▉| 288/289 [00:49<00:0      5/100      1.52G     0.9191     0.6505     0.8945        311        416: 100%|██████████| 289/289 [00:49<00:0      5/100      1.52G     0.9191     0.6505     0.8945        311        416: 100%|██████████| 289/289 [00:49<00:00,
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.962      0.871      0.924      0.704

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      6/100      1.52G     0.9039      0.618     0.8911        264        416: 100%|██████████| 289/289 [00:48<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all        243       4250       0.97      0.861      0.923      0.714

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      7/100      1.52G      0.873     0.5852     0.8861        348        416: 100%|██████████| 289/289 [00:47<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.965      0.874      0.931      0.718

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      8/100       1.6G      0.864      0.565     0.8826        300        416: 100%|██████████| 289/289 [00:48<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.954      0.891      0.934      0.704
 
      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
      9/100       1.6G     0.8626     0.5466     0.8781        266        416: 100%|██████████| 289/289 [00:48<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.977      0.885      0.938       0.73

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     10/100       1.6G      0.823      0.526     0.8718        284        416: 100%|██████████| 289/289 [00:46<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all        243       4250      0.963      0.878      0.934      0.718

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     11/100       1.6G     0.8233     0.5136     0.8744        283        416: 100%|██████████| 289/289 [00:45<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.971      0.891      0.942      0.744
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     12/100       1.6G      0.797     0.4977     0.8691        324        416: 100%|██████████| 289/289 [00:46<00: 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00 
                   all        243       4250      0.976      0.876      0.938      0.751

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     13/100       1.6G     0.7941     0.4879     0.8677        258        416: 100%|██████████| 289/289 [00:46<00: 
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.7
                   all        243       4250      0.972      0.893      0.941      0.736

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     14/100       1.6G     0.7825     0.4778     0.8659        495        416:  57%|█████▋    | 165/289 [00:26<00:1     14/100       1.6G     0.7826     0.4778     0.8659        351        416:  57%|█████▋    | 165/289 [00:26<00:1     14/100       1.6G     0.7826     0.4778     0.8659        351        416:  57%|█████▋    | 166/289 [00:26<00:1     14/100       1.6G     0.7824     0.4778     0.8658        516        416:  57%|█████▋    | 166/289 [00:26<00:1     14/100       1.6G     0.7824     0.4778     0.8658        516        416:  58%|█████▊    | 167/289 [00:26<00:1     14/100       1.6G     0.7823     0.4778     0.8658        380        416:  58%|█████▊    | 167/289 [00:26<00:1     14/100       1.6G     0.7823     0.4778     0.8658        380        416:  58%|█████▊    | 168/289 [00:26<00:1     14/100       1.6G     0.7819     0.4776     0.8656        379        416:  58%|█████▊    | 168/289 [00:26<00:1     14/100       1.6G     0.7819     0.4776     0.8656        379        416:  58%|█████▊    | 169/289 [00:26<00:1     14/100       1.6G     0.7811     0.4773     0.8655        385        416:  58%|█████▊    | 169/289 [00:26<00:1     14/100       1.6G     0.7811     0.4773     0.8655        385        416:  59%|█████▉    | 170/289 [00:26<00:1     14/100       1.6G     0.7815     0.4774     0.8655        385        416:  59%|█████▉    | 170/289 [00:27<00:1     14/100       1.6G     0.7815     0.4774     0.8655        385        416:  59%|█████▉    | 171/289 [00:27<00:1     14/100       1.6G     0.7816     0.4774     0.8658        244        416:  59%|█████▉    | 171/289 [00:27<00:1     14/100       1.6G     0.7816     0.4774     0.8658        244        416:  60%|█████▉    | 172/289 [00:27<00:1     14/100       1.6G     0.7817     0.4773     0.8658        408        416:  60%|█████▉    | 172/289 [00:27<00:1     14/100       1.6G     0.7817     0.4773     0.8658        408        416:  60%|█████▉    | 173/289 [00:27<00:1     14/100       1.6G     0.7814     0.4772     0.8657        306        416:  60%|█████▉    | 173/289 [00:27<00:1     14/100       1.6G     0.7814     0.4772     0.8657        306        416:  60%|██████    | 174/289 [00:27<00:1     14/100       1.6G     0.7815     0.4771     0.8659        295        416:  60%|██████    | 174/289 [00:27<00:1     14/100       1.6G     0.7815     0.4771     0.8659        295        416:  61%|██████    | 175/289 [00:27<00:1     14/100       1.6G     0.7811      0.477     0.8657        428        416:  61%|██████    | 175/289 [00:28<00:1     14/100       1.6G     0.7811      0.477     0.8657        428        416:  61%|██████    | 176/289 [00:28<00:2     14/100       1.6G     0.7812      0.477     0.8658        394        416:  61%|██████    | 176/289 [00:28<00:2     14/100       1.6G     0.7812      0.477     0.8658        394        416:  61%|██████    | 177/289 [00:28<00:3     14/100       1.6G     0.7813     0.4772     0.8657        448        416:  61%|██████    | 177/289 [00:28<00:3     14/100       1.6G     0.7813     0.4772     0.8657        448        416:  62%|██████▏   | 178/289 [00:28<00:2     14/100       1.6G     0.7806     0.4769     0.8655        321        416:  62%|██████▏   | 178/289 [00:29<00:2     14/100       1.6G     0.7806     0.4769     0.8655        321        416:  62%|██████▏   | 179/289 [00:29<00:2     14/100       1.6G     0.7805     0.4766     0.8653        454        416:  62%|██████▏   | 179/289 [00:29<00:2     14/100       1.6G     0.7805     0.4766     0.8653        454        416:  62%|██████▏   | 180/289 [00:29<00:2     14/100       1.6G     0.7802     0.4764     0.8653        418        416:  62%|██████▏   | 180/289 [00:29<00:2     14/100       1.6G     0.7802     0.4764     0.8653        418        416:  63%|██████▎   | 181/289 [00:29<00:2     14/100       1.6G       0.78     0.4762     0.8653        350        416:  63%|██████▎   | 181/289 [00:29<00:2     14/100       1.6G       0.78     0.4762     0.8653        350        416:  63%|██████▎   | 182/289 [00:29<00:2     14/100       1.6G     0.7799     0.4761     0.8652        383        416:  63%|██████▎   | 182/289 [00:30<00:2     14/100       1.6G     0.7799     0.4761     0.8652        383        416:  63%|██████▎   | 183/289 [00:30<00:2     14/100       1.6G     0.7803     0.4761     0.8655        309        416:  63%|██████▎   | 183/289 [00:30<00:2     14/100       1.6G     0.7803     0.4761     0.8655        309        416:  64%|██████▎   | 184/289 [00:30<00:2     14/100       1.6G       0.78      0.476     0.8654        375        416:  64%|██████▎   | 184/289 [00:30<00:2     14/100       1.6G       0.78      0.476     0.8654        375        416:  64%|██████▍   | 185/289 [00:30<00:2     14/100       1.6G     0.7803     0.4761     0.8653        534        416:  64%|██████▍   | 185/289 [00:30<00:2     14/100       1.6G     0.7803     0.4761     0.8653        534        416:  64%|██████▍   | 186/289 [00:30<00:2     14/100       1.6G     0.7811     0.4763     0.8653        336        416:  64%|██████▍   | 186/289 [00:30<00:2     14/100       1.6G     0.7811     0.4763     0.8653        336        416:  65%|██████▍   | 187/289 [00:30<00:1     14/100       1.6G     0.7813     0.4764     0.8652        486        416:  65%|██████▍   | 187/289 [00:30<00:1     14/100       1.6G     0.7813     0.4764     0.8652        486        416:  65%|██████▌   | 188/289 [00:30<00:1     14/100       1.6G     0.7817     0.4768     0.8653        412        416:  65%|██████▌   | 188/289 [00:31<00:1     14/100       1.6G     0.7817     0.4768     0.8653        412        416:  65%|██████▌   | 189/289 [00:31<00:1     14/100       1.6G     0.7816     0.4769     0.8652        395        416:  65%|██████▌   | 189/289 [00:31<00:1     14/100       1.6G     0.7816     0.4769     0.8652        395        416:  66%|██████▌   | 190/289 [00:31<00:1     14/100       1.6G     0.7817     0.4768     0.8654        345        416:  66%|██████▌   | 190/289 [00:31<00:1     14/100       1.6G     0.7817     0.4768     0.8654        345        416:  66%|██████▌   | 191/289 [00:31<00:1     14/100       1.6G     0.7816     0.4766     0.8653        386        416:  66%|██████▌   | 191/289 [00:31<00:1     14/100       1.6G     0.7816     0.4766     0.8653        386        416:  66%|██████▋   | 192/289 [00:31<00:1     14/100       1.6G     0.7813     0.4764     0.8652        410        416:  66%|██████▋   | 192/289 [00:31<00:1     14/100       1.6G     0.7813     0.4764     0.8652        410        416:  67%|██████▋   | 193/289 [00:31<00:1     14/100       1.6G      0.782     0.4765     0.8653        407        416:  67%|██████▋   | 193/289 [00:31<00:1     14/100       1.6G      0.782     0.4765     0.8653        407        416:  67%|██████▋   | 194/289 [00:31<00:1     14/100       1.6G      0.782     0.4767     0.8655        266        416:  67%|██████▋   | 194/289 [00:31<00:1     14/100       1.6G      0.782     0.4767     0.8655        266        416:  67%|██████▋   | 195/289 [00:31<00:1     14/100       1.6G      0.782     0.4768     0.8655        426        416:  67%|██████▋   | 195/289 [00:32<00:1     14/100       1.6G      0.782     0.4768     0.8655        426        416:  68%|██████▊   | 196/289 [00:32<00:1     14/100       1.6G     0.7824     0.4768     0.8655        326        416:  68%|██████▊   | 196/289 [00:32<00:1     14/100       1.6G     0.7824     0.4768     0.8655        326        416:  68%|██████▊   | 197/289 [00:32<00:1     14/100       1.6G     0.7823     0.4768     0.8655        385        416:  68%|██████▊   | 197/289 [00:32<00:1     14/100       1.6G     0.7823     0.4768     0.8655        385        416:  69%|██████▊   | 198/289 [00:32<00:1     14/100       1.6G     0.7818     0.4766     0.8654        394        416:  69%|██████▊   | 198/289 [00:32<00:1     14/100       1.6G     0.7818     0.4766     0.8654        394        416:  69%|██████▉   | 199/289 [00:32<00:1     14/100       1.6G     0.7817     0.4766     0.8654        361        416:  69%|██████▉   | 199/289 [00:32<00:1     14/100       1.6G     0.7817     0.4766     0.8654        361        416:  69%|██████▉   | 200/289 [00:32<00:1     14/100       1.6G     0.7818     0.4764     0.8654        324        416:  69%|██████▉   | 200/289 [00:32<00:1     14/100       1.6G     0.7818     0.4764     0.8654        324        416:  70%|██████▉   | 201/289 [00:32<00:1     14/100       1.6G     0.7818     0.4764     0.8653        360        416:  70%|██████▉   | 201/289 [00:32<00:1     14/100       1.6G     0.7818     0.4764     0.8653        360        416:  70%|██████▉   | 202/289 [00:32<00:1     14/100       1.6G     0.7819     0.4764     0.8653        361        416:  70%|██████▉   | 202/289 [00:32<00:1     14/100       1.6G     0.7819     0.4764     0.8653        361        416:  70%|███████   | 203/289 [00:32<00:1     14/100       1.6G     0.7823     0.4766     0.8652        471        416:  70%|███████   | 203/289 [00:33<00:1     14/100       1.6G     0.7823     0.4766     0.8652        471        416:  71%|███████   | 204/289 [00:33<00:1     14/100       1.6G     0.7824     0.4767     0.8651        569        416:  71%|███████   | 204/289 [00:33<00:1     14/100       1.6G     0.7824     0.4767     0.8651        569        416:  71%|███████   | 205/289 [00:33<00:1     14/100       1.6G     0.7834     0.4773     0.8652        463        416:  71%|███████   | 205/289 [00:33<00:1     14/100       1.6G     0.7834     0.4773     0.8652        463        416:  71%|███████▏  | 206/289 [00:33<00:1     14/100       1.6G     0.7829     0.4771     0.8651        274        416:  71%|███████▏  | 206/289 [00:33<00:1     14/100       1.6G     0.7829     0.4771     0.8651        274        416:  72%|███████▏  | 207/289 [00:33<00:1     14/100       1.6G     0.7829     0.4772     0.8653        333        416:  72%|███████▏  | 207/289 [00:33<00:1     14/100       1.6G     0.7829     0.4772     0.8653        333        416:  72%|███████▏  | 208/289 [00:33<00:1     14/100       1.6G     0.7829     0.4772     0.8653        346        416:  72%|███████▏  | 208/289 [00:33<00:1     14/100       1.6G     0.7829     0.4772     0.8653        346        416:  72%|███████▏  | 209/289 [00:33<00:1     14/100       1.6G     0.7825      0.477     0.8652        323        416:  72%|███████▏  | 209/289 [00:33<00:1     14/100       1.6G     0.7825      0.477     0.8652        323        416:  73%|███████▎  | 210/289 [00:33<00:1     14/100       1.6G     0.7828      0.477     0.8652        395        416:  73%|███████▎  | 210/289 [00:33<00:1     14/100       1.6G     0.7828      0.477     0.8652        395        416:  73%|███████▎  | 211/289 [00:33<00:1     14/100       1.6G     0.7824     0.4768     0.8651        414        416:  73%|███████▎  | 211/289 [00:34<00:1     14/100       1.6G     0.7824     0.4768     0.8651        414        416:  73%|███████▎  | 212/289 [00:34<00:1     14/100       1.6G     0.7828     0.4769     0.8653        278        416:  73%|███████▎  | 212/289 [00:34<00:1     14/100       1.6G     0.7828     0.4769     0.8653        278        416:  74%|███████▎  | 213/289 [00:34<00:1     14/100       1.6G     0.7828      0.477     0.8653        444        416:  74%|███████▎  | 213/289 [00:34<00:1     14/100       1.6G     0.7828      0.477     0.8653        444        416:  74%|███████▍  | 214/289 [00:34<00:1     14/100       1.6G     0.7829     0.4771     0.8654        224        416:  74%|███████▍  | 214/289 [00:34<00:1     14/100       1.6G     0.7829     0.4771     0.8654        224        416:  74%|███████▍  | 215/289 [00:34<00:1     14/100       1.6G     0.7827      0.477     0.8654        311        416:  74%|███████▍  | 215/289 [00:35<00:1     14/100       1.6G     0.7827      0.477     0.8654        311        416:  75%|███████▍  | 216/289 [00:35<00:1     14/100       1.6G     0.7844     0.4775     0.8661        296        416:  75%|███████▍  | 216/289 [00:35<00:1     14/100       1.6G     0.7844     0.4775     0.8661        296        416:  75%|███████▌  | 217/289 [00:35<00:1     14/100       1.6G     0.7844     0.4775     0.8661        449        416:  75%|███████▌  | 217/289 [00:35<00:1     14/100       1.6G     0.7844     0.4775     0.8661        449        416:  75%|███████▌  | 218/289 [00:35<00:1     14/100       1.6G     0.7845     0.4775     0.8662        311        416:  75%|███████▌  | 218/289 [00:35<00:1     14/100       1.6G     0.7845     0.4775     0.8662        311        416:  76%|███████▌  | 219/289 [00:35<00:1     14/100       1.6G     0.7843     0.4775      0.866        544        416:  76%|███████▌  | 219/289 [00:35<00:1     14/100       1.6G     0.7843     0.4775      0.866        544        416:  76%|███████▌  | 220/289 [00:35<00:1     14/100       1.6G     0.7844     0.4775     0.8659        388        416:  76%|███████▌  | 220/289 [00:36<00:1     14/100       1.6G     0.7844     0.4775     0.8659        388        416:  76%|███████▋  | 221/289 [00:36<00:1     14/100       1.6G     0.7848     0.4776     0.8659        477        416:  76%|███████▋  | 221/289 [00:36<00:1     14/100       1.6G     0.7848     0.4776     0.8659        477        416:  77%|███████▋  | 222/289 [00:36<00:1     14/100       1.6G     0.7849     0.4776     0.8658        454        416:  77%|███████▋  | 222/289 [00:36<00:1     14/100       1.6G     0.7849     0.4776     0.8658        454        416:  77%|███████▋  | 223/289 [00:36<00:1     14/100       1.6G     0.7848     0.4775     0.8657        435        416:  77%|███████▋  | 223/289 [00:36<00:1     14/100       1.6G     0.7848     0.4775     0.8657        435        416:  78%|███████▊  | 224/289 [00:36<00:1     14/100       1.6G     0.7849     0.4775     0.8656        379        416:  78%|███████▊  | 224/289 [00:36<00:1     14/100       1.6G     0.7849     0.4775     0.8656        379        416:  78%|███████▊  | 225/289 [00:36<00:1     14/100       1.6G     0.7845     0.4774     0.8657        275        416:  78%|███████▊  | 225/289 [00:36<00:1     14/100       1.6G     0.7845     0.4774     0.8657        275        416:  78%|███████▊  | 226/289 [00:36<00:0     14/100       1.6G     0.7851     0.4776     0.8657        352        416:  78%|███████▊  | 226/289 [00:37<00:0     14/100       1.6G     0.7851     0.4776     0.8657        352        416:  79%|███████▊  | 227/289 [00:37<00:0     14/100       1.6G     0.7855     0.4779     0.8659        409        416:  79%|███████▊  | 227/289 [00:37<00:0     14/100       1.6G     0.7855     0.4779     0.8659        409        416:  79%|███████▉  | 228/289 [00:37<00:0     14/100       1.6G     0.7862     0.4782     0.8662        341        416:  79%|███████▉  | 228/289 [00:37<00:0     14/100       1.6G     0.7862     0.4782     0.8662        341        416:  79%|███████▉  | 229/289 [00:37<00:0     14/100       1.6G      0.786     0.4781     0.8661        365        416:  79%|███████▉  | 229/289 [00:37<00:0     14/100       1.6G      0.786     0.4781     0.8661        365        416:  80%|███████▉  | 230/289 [00:37<00:0     14/100       1.6G     0.7859     0.4782      0.866        432        416:  80%|███████▉  | 230/289 [00:37<00:0     14/100       1.6G     0.7859     0.4782      0.866        432        416:  80%|███████▉  | 231/289 [00:37<00:0     14/100       1.6G     0.7857     0.4781     0.8659        352        416:  80%|███████▉  | 231/289 [00:37<00:0     14/100       1.6G     0.7857     0.4781     0.8659        352        416:  80%|████████  | 232/289 [00:37<00:0     14/100       1.6G     0.7858      0.478      0.866        337        416:  80%|████████  | 232/289 [00:37<00:0     14/100       1.6G     0.7858      0.478      0.866        337        416:  81%|████████  | 233/289 [00:37<00:0     14/100       1.6G     0.7859     0.4778      0.866        398        416:  81%|████████  | 233/289 [00:37<00:0     14/100       1.6G     0.7859     0.4778      0.866        398        416:  81%|████████  | 234/289 [00:37<00:0     14/100       1.6G     0.7859     0.4778     0.8661        299        416:  81%|████████  | 234/289 [00:38<00:0     14/100       1.6G     0.7859     0.4778     0.8661        299        416:  81%|████████▏ | 235/289 [00:38<00:0     14/100       1.6G     0.7864     0.4782     0.8662        467        416:  81%|████████▏ | 235/289 [00:38<00:0     14/100       1.6G     0.7864     0.4782     0.8662        467        416:  82%|████████▏ | 236/289 [00:38<00:0     14/100       1.6G     0.7864     0.4781     0.8662        370        416:  82%|████████▏ | 236/289 [00:38<00:0     14/100       1.6G     0.7864     0.4781     0.8662        370        416:  82%|████████▏ | 237/289 [00:38<00:0     14/100       1.6G     0.7863     0.4779     0.8662        377        416:  82%|████████▏ | 237/289 [00:38<00:0     14/100       1.6G     0.7863     0.4779     0.8662        377        416:  82%|████████▏ | 238/289 [00:38<00:0     14/100       1.6G     0.7866     0.4782     0.8662        384        416:  82%|████████▏ | 238/289 [00:38<00:0     14/100       1.6G     0.7866     0.4782     0.8662        384        416:  83%|████████▎ | 239/289 [00:38<00:0     14/100       1.6G     0.7863     0.4781     0.8661        451        416:  83%|████████▎ | 239/289 [00:38<00:0     14/100       1.6G     0.7863     0.4781     0.8661        451        416:  83%|████████▎ | 240/289 [00:38<00:0     14/100       1.6G     0.7865     0.4782     0.8661        366        416:  83%|████████▎ | 240/289 [00:38<00:0     14/100       1.6G     0.7865     0.4782     0.8661        366        416:  83%|████████▎ | 241/289 [00:38<00:0     14/100       1.6G     0.7867     0.4783     0.8661        377        416:  83%|████████▎ | 241/289 [00:38<00:0     14/100       1.6G     0.7867     0.4783     0.8661        377        416:  84%|████████▎ | 242/289 [00:38<00:0     14/100       1.6G     0.7864     0.4782      0.866        348        416:  84%|████████▎ | 242/289 [00:39<00:0     14/100       1.6G     0.7864     0.4782      0.866        348        416:  84%|████████▍ | 243/289 [00:39<00:0     14/100       1.6G     0.7866     0.4785     0.8662        320        416:  84%|████████▍ | 243/289 [00:39<00:0     14/100       1.6G     0.7866     0.4785     0.8662        320        416:  84%|████████▍ | 244/289 [00:39<00:0     14/100       1.6G     0.7866     0.4784     0.8661        410        416:  84%|████████▍ | 244/289 [00:39<00:0     14/100       1.6G     0.7866     0.4784     0.8661        410        416:  85%|████████▍ | 245/289 [00:39<00:0     14/100       1.6G     0.7865     0.4784     0.8662        304        416:  85%|████████▍ | 245/289 [00:39<00:0     14/100       1.6G     0.7865     0.4784     0.8662        304        416:  85%|████████▌ | 246/289 [00:39<00:0     14/100       1.6G     0.7867     0.4783     0.8662        412        416:  85%|████████▌ | 246/289 [00:39<00:0     14/100       1.6G     0.7867     0.4783     0.8662        412        416:  85%|████████▌ | 247/289 [00:39<00:0     14/100       1.6G     0.7866     0.4782     0.8661        354        416:  85%|████████▌ | 247/289 [00:39<00:0     14/100       1.6G     0.7866     0.4782     0.8661        354        416:  86%|████████▌ | 248/289 [00:39<00:0     14/100       1.6G     0.7861      0.478      0.866        263        416:  86%|████████▌ | 248/289 [00:39<00:0     14/100       1.6G     0.7861      0.478      0.866        263        416:  86%|████████▌ | 249/289 [00:39<00:0     14/100       1.6G     0.7861     0.4779      0.866        329        416:  86%|████████▌ | 249/289 [00:39<00:0     14/100       1.6G     0.7861     0.4779      0.866        329        416:  87%|████████▋ | 250/289 [00:39<00:0     14/100       1.6G     0.7861     0.4779     0.8659        353        416:  87%|████████▋ | 250/289 [00:40<00:0     14/100       1.6G     0.7861     0.4779     0.8659        353        416:  87%|████████▋ | 251/289 [00:40<00:0     14/100       1.6G     0.7862     0.4777     0.8659        350        416:  87%|████████▋ | 251/289 [00:40<00:0     14/100       1.6G     0.7862     0.4777     0.8659        350        416:  87%|████████▋ | 252/289 [00:40<00:0     14/100       1.6G     0.7863     0.4778     0.8659        406        416:  87%|████████▋ | 252/289 [00:40<00:0     14/100       1.6G     0.7863     0.4778     0.8659        406        416:  88%|████████▊ | 253/289 [00:40<00:0     14/100       1.6G     0.7862     0.4778     0.8658        493        416:  88%|████████▊ | 253/289 [00:41<00:0     14/100       1.6G     0.7862     0.4778     0.8658        493        416:  88%|████████▊ | 254/289 [00:41<00:0     14/100       1.6G     0.7859     0.4776     0.8658        287        416:  88%|████████▊ | 254/289 [00:41<00:0     14/100       1.6G     0.7859     0.4776     0.8658        287        416:  88%|████████▊ | 255/289 [00:41<00:0     14/100       1.6G     0.7867     0.4779     0.8663        302        416:  88%|████████▊ | 255/289 [00:41<00:0     14/100       1.6G     0.7867     0.4779     0.8663        302        416:  89%|████████▊ | 256/289 [00:41<00:0     14/100       1.6G     0.7867     0.4779     0.8662        425        416:  89%|████████▊ | 256/289 [00:41<00:0     14/100       1.6G     0.7867     0.4779     0.8662        425        416:  89%|████████▉ | 257/289 [00:41<00:0     14/100       1.6G     0.7862     0.4778     0.8662        250        416:  89%|████████▉ | 257/289 [00:41<00:0     14/100       1.6G     0.7862     0.4778     0.8662        250        416:  89%|████████▉ | 258/289 [00:41<00:0     14/100       1.6G     0.7863     0.4779     0.8662        386        416:  89%|████████▉ | 258/289 [00:42<00:0     14/100       1.6G     0.7863     0.4779     0.8662        386        416:  90%|████████▉ | 259/289 [00:42<00:0     14/100       1.6G     0.7865     0.4779     0.8662        400        416:  90%|████████▉ | 259/289 [00:42<00:0     14/100       1.6G     0.7865     0.4779     0.8662        400        416:  90%|████████▉ | 260/289 [00:42<00:0     14/100       1.6G     0.7869     0.4779     0.8663        400        416:  90%|████████▉ | 260/289 [00:42<00:0     14/100       1.6G     0.7869     0.4779     0.8663        400        416:  90%|█████████ | 261/289 [00:42<00:0     14/100       1.6G     0.7872     0.4784     0.8663        313        416:  90%|█████████ | 261/289 [00:42<00:0     14/100       1.6G     0.7872     0.4784     0.8663        313        416:  91%|█████████ | 262/289 [00:42<00:0     14/100       1.6G     0.7878     0.4787     0.8664        532        416:  91%|█████████ | 262/289 [00:42<00:0     14/100       1.6G     0.7878     0.4787     0.8664        532        416:  91%|█████████ | 263/289 [00:42<00:0     14/100       1.6G     0.7875     0.4786     0.8663        351        416:  91%|█████████ | 263/289 [00:42<00:0     14/100       1.6G     0.7875     0.4786     0.8663        351        416:  91%|█████████▏| 264/289 [00:42<00:0     14/100       1.6G     0.7875     0.4787     0.8663        371        416:  91%|█████████▏| 264/289 [00:42<00:0     14/100       1.6G     0.7875     0.4787     0.8663        371        416:  92%|█████████▏| 265/289 [00:42<00:0     14/100       1.6G     0.7876     0.4785     0.8664        413        416:  92%|█████████▏| 265/289 [00:42<00:0     14/100       1.6G     0.7876     0.4785     0.8664        413        416:  92%|█████████▏| 266/289 [00:42<00:0     14/100       1.6G     0.7873     0.4783     0.8664        345        416:  92%|█████████▏| 266/289 [00:43<00:0     14/100       1.6G     0.7873     0.4783     0.8664        345        416:  92%|█████████▏| 267/289 [00:43<00:0     14/100       1.6G     0.7873     0.4785     0.8665        341        416:  92%|█████████▏| 267/289 [00:43<00:0     14/100       1.6G     0.7873     0.4785     0.8665        341        416:  93%|█████████▎| 268/289 [00:43<00:0     14/100       1.6G     0.7872     0.4784     0.8664        380        416:  93%|█████████▎| 268/289 [00:43<00:0     14/100       1.6G     0.7872     0.4784     0.8664        380        416:  93%|█████████▎| 269/289 [00:43<00:0     14/100       1.6G     0.7869     0.4783     0.8664        327        416:  93%|█████████▎| 269/289 [00:43<00:0     14/100       1.6G     0.7869     0.4783     0.8664        327        416:  93%|█████████▎| 270/289 [00:43<00:0     14/100       1.6G     0.7869     0.4784     0.8663        442        416:  93%|█████████▎| 270/289 [00:43<00:0     14/100       1.6G     0.7869     0.4784     0.8663        442        416:  94%|█████████▍| 271/289 [00:43<00:0     14/100       1.6G     0.7868     0.4784     0.8661        417        416:  94%|█████████▍| 271/289 [00:43<00:0     14/100       1.6G     0.7868     0.4784     0.8661        417        416:  94%|█████████▍| 272/289 [00:43<00:0     14/100       1.6G     0.7868     0.4784      0.866        479        416:  94%|█████████▍| 272/289 [00:43<00:0     14/100       1.6G     0.7868     0.4784      0.866        479        416:  94%|█████████▍| 273/289 [00:43<00:0     14/100       1.6G     0.7866     0.4782     0.8661        279        416:  94%|█████████▍| 273/289 [00:43<00:0     14/100       1.6G     0.7866     0.4782     0.8661        279        416:  95%|█████████▍| 274/289 [00:43<00:0     14/100       1.6G     0.7866     0.4781      0.866        390        416:  95%|█████████▍| 274/289 [00:44<00:0     14/100       1.6G     0.7866     0.4781      0.866        390        416:  95%|█████████▌| 275/289 [00:44<00:0     14/100       1.6G      0.787     0.4783     0.8661        377        416:  95%|█████████▌| 275/289 [00:44<00:0     14/100       1.6G      0.787     0.4783     0.8661        377        416:  96%|█████████▌| 276/289 [00:44<00:0     14/100       1.6G     0.7867      0.478     0.8661        305        416:  96%|█████████▌| 276/289 [00:44<00:0     14/100       1.6G     0.7867      0.478     0.8661        305        416:  96%|█████████▌| 277/289 [00:44<00:0     14/100       1.6G     0.7868     0.4783     0.8661        322        416:  96%|█████████▌| 277/289 [00:44<00:0     14/100       1.6G     0.7868     0.4783     0.8661        322        416:  96%|█████████▌| 278/289 [00:44<00:0     14/100       1.6G     0.7871     0.4785     0.8661        466        416:  96%|█████████▌| 278/289 [00:44<00:0     14/100       1.6G     0.7871     0.4785     0.8661        466        416:  97%|█████████▋| 279/289 [00:44<00:0     14/100       1.6G     0.7873     0.4786     0.8661        474        416:  97%|█████████▋| 279/289 [00:44<00:0     14/100       1.6G     0.7873     0.4786     0.8661        474         14/100       1.6G     0.7877     0.4789      0.866        435        416:  97%|█████████▋| 281/289 [00:44<00:0     14/100       1.6G     0.7877     0.4789      0.866        435        416:  98%|█████████▊| 282/289 [00:44<00:0     14/100       1.6G     0.7876     0.4788      0.866        288        416:  98%|█████████▊| 282/289 [00:45<00:0     14/100       1.6G     0.7876     0.4788      0.866        288        416:  98%|█████████▊| 283/289 [00:45<00:0     14/100       1.6G     0.7879     0.4789     0.8662        338        416:  98%|█████████▊| 283/289 [00:45<00:0     14/100       1.6G     0.7879     0.4789     0.8662        338        416:  98%|█████████▊| 284/289 [00:45<00:0     14/100       1.6G     0.7877     0.4788     0.8661        454        416:  98%|█████████▊| 284/289 [00:45<00:0     14/100       1.6G     0.7877     0.4788     0.8661        454        416:  99%|█████████▊| 285/289 [00:45<00:0     14/100       1.6G     0.7877     0.4789     0.8662        374        416:  99%|█████████▊| 285/289 [00:45<00:0     14/100       1.6G     0.7877     0.4789     0.8662        374        416:  99%|█████████▉| 286/289 [00:45<00:0     14/100       1.6G     0.7872     0.4786     0.8661        361        416:  99%|█████████▉| 286/289 [00:45<00:0     14/100       1.6G     0.7872     0.4786     0.8661        361        416:  99%|█████████▉| 287/289 [00:45<00:0     14/100       1.6G     0.7869     0.4785      0.866        397        416:  99%|█████████▉| 287/289 [00:45<00:0     14/100       1.6G     0.7869     0.4785      0.866        397        416: 100%|█████████▉| 288/289 [00:45<00:0     14/100       1.6G     0.7867     0.4783     0.8659        316        416: 100%|█████████▉| 288/289 [00:45<00:0     14/100       1.6G     0.7867     0.4783     0.8659        316        416: 100%|██████████| 289/289 [00:45<00:0     14/100       1.6G     0.7867     0.4783     0.8659        316        416: 100%|██████████| 289/289 [00:45<00:00,  6.30it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.981       0.89      0.947      0.758

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     15/100       1.6G     0.7701     0.4652     0.8643        283        416: 100%|██████████| 289/289 [00:46<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.1
                   all        243       4250      0.957      0.907      0.946      0.757

  Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     16/100       1.6G     0.7663      0.455     0.8609        275        416: 100%|██████████| 289/289 [00:46<00:00,  6.20it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.6
                   all        243       4250      0.976      0.899       0.95      0.776

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     17/100       1.6G     0.7577     0.4504     0.8596        338        416: 100%|██████████| 289/289 [00:45<00:00,  6.35it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.1
                   all        243       4250      0.979       0.89      0.946      0.761

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     18/100       1.6G     0.7555      0.448     0.8626        283        416: 100%|██████████| 289/289 [00:44<00:00,  6.45it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.8 
                   all        243       4250      0.975      0.908      0.951      0.766

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     19/100       1.6G     0.7504     0.4422     0.8586        297        416: 100%|██████████| 289/289 [00:46<00:00,  6.15it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.6
                   all        243       4250      0.975      0.895      0.951      0.765

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     20/100       1.6G     0.7578     0.4419      0.858        268        416:  34%|███▎      | 97/289 [00:16<00:51     20/100       1.6G     0.7577     0.4418      0.858        349        416:  34%|███▎      | 97/289 [00:16<00:51     20/100       1.6G     0.7577     0.4418      0.858        349        416:  34%|███▍      | 98/289 [00:16<00:48     20/100       1.6G     0.7569     0.4416      0.858        350        416:  34%|███▍      | 98/289 [00:16<00:48     20/100       1.6G     0.7569     0.4416      0.858        350        416:  34%|███▍      | 99/289 [00:16<00:40     20/100       1.6G     0.7554     0.4411     0.8577        421        416:  34%|███▍      | 99/289 [00:17<00:40     20/100       1.6G     0.7554     0.4411     0.8577        421        416:  35%|███▍      | 100/289 [00:17<00:3     20/100       1.6G     0.7555     0.4415     0.8578        333        416:  35%|███▍      | 100/289 [00:17<00:3     20/100       1.6G     0.7555     0.4415     0.8578        333        416:  35%|███▍      | 101/289 [00:17<00:3     20/100       1.6G     0.7548     0.4412     0.8576        338        416:  35%|███▍      | 101/289 [00:17<00:3     20/100       1.6G     0.7548     0.4412     0.8576        338        416:  35%|███▌      | 102/289 [00:17<00:3     20/100       1.6G     0.7546      0.441     0.8574        395        416:  35%|███▌      | 102/289 [00:17<00:3     20/100       1.6G     0.7546      0.441     0.8574        395        416:  36%|███▌      | 103/289 [00:17<00:3     20/100       1.6G      0.755     0.4409     0.8574        440        416:  36%|███▌      | 103/289 [00:17<00:3     20/100       1.6G      0.755     0.4409     0.8574        440        416:  36%|███▌      | 104/289 [00:17<00:2     20/100       1.6G     0.7551     0.4408     0.8573        448        416:  36%|███▌      | 104/289 [00:17<00:2     20/100       1.6G     0.7551     0.4408     0.8573        448        416:  36%|███▋      | 105/289 [00:17<00:2     20/100       1.6G     0.7542     0.4405     0.8573        279        416:  36%|███▋      | 105/289 [00:17<00:2     20/100       1.6G     0.7542     0.4405     0.8573        279        416:  37%|███▋      | 106/289 [00:17<00:2     20/100       1.6G     0.7534     0.4402      0.857        404        416:  37%|███▋      | 106/289 [00:17<00:2     20/100       1.6G     0.7534     0.4402      0.857        404        416:  37%|███▋      | 107/289 [00:17<00:2     20/100       1.6G     0.7533     0.4398     0.8575        328        416:  37%|███▋      | 107/289 [00:18<00:2     20/100       1.6G     0.7533     0.4398     0.8575        328        416:  37%|███▋      | 108/289 [00:18<00:2     20/100       1.6G      0.753     0.4395     0.8574        423        416:  37%|███▋      | 108/289 [00:18<00:2     20/100       1.6G      0.753     0.4395     0.8574        423        416:  38%|███▊      | 109/289 [00:18<00:2     20/100       1.6G     0.7524     0.4394     0.8571        409        416:  38%|███▊      | 109/289 [00:18<00:2     20/100       1.6G     0.7524     0.4394     0.8571        409        416:  38%|███▊      | 110/289 [00:18<00:2     20/100       1.6G     0.7526     0.4394     0.8571        407        416:  38%|███▊      | 110/289 [00:18<00:2     20/100       1.6G     0.7526     0.4394     0.8571        407        416:  38%|███▊      | 111/289 [00:18<00:2     20/100       1.6G     0.7515      0.439     0.8569        362        416:  38%|███▊      | 111/289 [00:18<00:2     20/100       1.6G     0.7515      0.439     0.8569        362        416:  39%|███▉      | 112/289 [00:18<00:2     20/100       1.6G     0.7533     0.4401     0.8577        311        416:  39%|███▉      | 112/289 [00:18<00:2     20/100       1.6G     0.7533     0.4401     0.8577        311        416:  39%|███▉      | 113/289 [00:18<00:2     20/100       1.6G      0.752     0.4396     0.8577        352        416:  39%|███▉      | 113/289 [00:19<00:2     20/100       1.6G      0.752     0.4396     0.8577        352        416:  39%|███▉      | 114/289 [00:19<00:2     20/100       1.6G     0.7517     0.4397     0.8575        402        416:  39%|███▉      | 114/289 [00:19<00:2     20/100       1.6G     0.7517     0.4397     0.8575        402        416:  40%|███▉      | 115/289 [00:19<00:2     20/100       1.6G     0.7511     0.4391     0.8574        344        416:  40%|███▉      | 115/289 [00:19<00:2     20/100       1.6G     0.7511     0.4391     0.8574        344        416:  40%|████      | 116/289 [00:19<00:2     20/100       1.6G     0.7517     0.4393     0.8575        457        416:  40%|████      | 116/289 [00:19<00:2     20/100       1.6G     0.7517     0.4393     0.8575        457        416:  40%|████      | 117/289 [00:19<00:2     20/100       1.6G     0.7529     0.4395     0.8577        454        416:  40%|████      | 117/289 [00:19<00:2     20/100       1.6G     0.7529     0.4395     0.8577        454        416:  41%|████      | 118/289 [00:19<00:2     20/100       1.6G     0.7529     0.4395     0.8578        323        416:  41%|████      | 118/289 [00:19<00:2     20/100       1.6G     0.7529     0.4395     0.8578        323        416:  41%|████      | 119/289 [00:19<00:2     20/100       1.6G     0.7524     0.4393     0.8577        332        416:  41%|████      | 119/289 [00:19<00:2     20/100       1.6G     0.7524     0.4393     0.8577        332        416:  42%|████▏     | 120/289 [00:19<00:2     20/100       1.6G     0.7525     0.4397     0.8578        316        416:  42%|████▏     | 120/289 [00:20<00:2     20/100       1.6G     0.7525     0.4397     0.8578        316        416:  42%|████▏     | 121/289 [00:20<00:2     20/100       1.6G     0.7522     0.4394     0.8575        500        416:  42%|████▏     | 121/289 [00:20<00:2     20/100       1.6G     0.7522     0.4394     0.8575        500        416:  42%|████▏     | 122/289 [00:20<00:2     20/100       1.6G     0.7519     0.4392     0.8574        426        416:  42%|████▏     | 122/289 [00:20<00:2     20/100       1.6G     0.7519     0.4392     0.8574        426        416:  43%|████▎     | 123/289 [00:20<00:2     20/100       1.6G     0.7515     0.4392     0.8573        339        416:  43%|████▎     | 123/289 [00:20<00:2     20/100       1.6G     0.7515     0.4392     0.8573        339        416:  43%|████▎     | 124/289 [00:20<00:2     20/100       1.6G     0.7503     0.4387      0.857        281        416:  43%|████▎     | 124/289 [00:20<00:2     20/100       1.6G     0.7503     0.4387      0.857        281        416:  43%|████▎     | 125/289 [00:20<00:2     20/100       1.6G       0.75     0.4386      0.857        343        416:  43%|████▎     | 125/289 [00:20<00:2     20/100       1.6G       0.75     0.4386      0.857        343        416:  44%|████▎     | 126/289 [00:20<00:2     20/100       1.6G     0.7497     0.4386      0.857        459        416:  44%|████▎     | 126/289 [00:20<00:2     20/100       1.6G     0.7497     0.4386      0.857        459        416:  44%|████▍     | 127/289 [00:20<00:2     20/100       1.6G     0.7491     0.4386     0.8568        443        416:  44%|████▍     | 127/289 [00:20<00:2     20/100       1.6G     0.7491     0.4386     0.8568        443        416:  44%|████▍     | 128/289 [00:20<00:2     20/100       1.6G     0.7485     0.4383     0.8566        417        416:  44%|████▍     | 128/289 [00:21<00:2     20/100       1.6G     0.7485     0.4383     0.8566        417        416:  45%|████▍     | 129/289 [00:21<00:2     20/100       1.6G     0.7485     0.4383     0.8567        364        416:  45%|████▍     | 129/289 [00:21<00:2     20/100       1.6G     0.7485     0.4383     0.8567        364        416:  45%|████▍     | 130/289 [00:21<00:3     20/100       1.6G     0.7483     0.4382     0.8566        359        416:  45%|████▍     | 130/289 [00:21<00:3     20/100       1.6G     0.7483     0.4382     0.8566        359        416:  45%|████▌     | 131/289 [00:21<00:3     20/100       1.6G     0.7479     0.4382     0.8568        333        416:  45%|████▌     | 131/289 [00:21<00:3     20/100       1.6G     0.7479     0.4382     0.8568        333        416:  46%|████▌     | 132/289 [00:21<00:3     20/100       1.6G     0.7475     0.4382     0.8567        434        416:  46%|████▌     | 132/289 [00:22<00:3     20/100       1.6G     0.7475     0.4382     0.8567        434        416:  46%|████▌     | 133/289 [00:22<00:3     20/100       1.6G     0.7478     0.4383     0.8568        394        416:  46%|████▌     | 133/289 [00:22<00:3     20/100       1.6G     0.7478     0.4383     0.8568        394        416:  46%|████▋     | 134/289 [00:22<00:3     20/100       1.6G     0.7479     0.4382     0.8569        319        416:  46%|████▋     | 134/289 [00:22<00:3     20/100       1.6G     0.7479     0.4382     0.8569        319        416:  47%|████▋     | 135/289 [00:22<00:3     20/100       1.6G     0.7482     0.4382      0.857        365        416:  47%|████▋     | 135/289 [00:22<00:3     20/100       1.6G     0.7482     0.4382      0.857        365        416:  47%|████▋     | 136/289 [00:22<00:3     20/100       1.6G     0.7483     0.4384      0.857        444        416:  47%|████▋     | 136/289 [00:23<00:3     20/100       1.6G     0.7483     0.4384      0.857        444        416:  47%|████▋     | 137/289 [00:23<00:3     20/100       1.6G     0.7486     0.4385     0.8571        390        416:  47%|████▋     | 137/289 [00:23<00:3     20/100       1.6G     0.7486     0.4385     0.8571        390        416:  48%|████▊     | 138/289 [00:23<00:3     20/100       1.6G     0.7494     0.4387     0.8576        302        416:  48%|████▊     | 138/289 [00:23<00:3     20/100       1.6G     0.7494     0.4387     0.8576        302        416:  48%|████▊     | 139/289 [00:23<00:3     20/100       1.6G     0.7494      0.439     0.8577        371        416:  48%|████▊     | 139/289 [00:23<00:3     20/100       1.6G     0.7494      0.439     0.8577        371        416:  48%|████▊     | 140/289 [00:23<00:2     20/100       1.6G     0.7492     0.4389     0.8579        301        416:  48%|████▊     | 140/289 [00:23<00:2     20/100       1.6G     0.7492     0.4389     0.8579        301        416:  49%|████▉     | 141/289 [00:23<00:2     20/100       1.6G      0.749     0.4387     0.8576        442        416:  49%|████▉     | 141/289 [00:23<00:2     20/100       1.6G      0.749     0.4387     0.8576        442        416:  49%|████▉     | 142/289 [00:23<00:2     20/100       1.6G     0.7484     0.4385     0.8574        394        416:  49%|████▉     | 142/289 [00:24<00:2     20/100       1.6G     0.7484     0.4385     0.8574        394        416:  49%|████▉     | 143/289 [00:24<00:2     20/100       1.6G     0.7483     0.4383     0.8573        426        416:  49%|████▉     | 143/289 [00:24<00:2     20/100       1.6G     0.7483     0.4383     0.8573        426        416:  50%|████▉     | 144/289 [00:24<00:2     20/100       1.6G      0.748     0.4383     0.8574        323        416:  50%|████▉     | 144/289 [00:24<00:2     20/100       1.6G      0.748     0.4383     0.8574        323        416:  50%|█████     | 145/289 [00:24<00:1     20/100       1.6G     0.7479     0.4381     0.8573        379        416:  50%|█████     | 145/289 [00:24<00:1     20/100       1.6G     0.7479     0.4381     0.8573        379        416:  51%|█████     | 146/289 [00:24<00:2     20/100       1.6G     0.7476      0.438     0.8571        379        416:  51%|█████     | 146/289 [00:24<00:2     20/100       1.6G     0.7476      0.438     0.8571        379        416:  51%|█████     | 147/289 [00:24<00:1     20/100       1.6G     0.7483     0.4381     0.8572        444        416:  51%|█████     | 147/289 [00:24<00:1     20/100       1.6G     0.7483     0.4381     0.8572        444        416:  51%|█████     | 148/289 [00:24<00:1     20/100       1.6G     0.7477     0.4376     0.8572        269        416:  51%|█████     | 148/289 [00:24<00:1     20/100       1.6G     0.7477     0.4376     0.8572        269        416:  52%|█████▏    | 149/289 [00:24<00:1     20/100       1.6G      0.748     0.4378     0.8571        331        416:  52%|█████▏    | 149/289 [00:24<00:1     20/100       1.6G      0.748     0.4378     0.8571        331        416:  52%|█████▏    | 150/289 [00:24<00:1     20/100       1.6G     0.7489     0.4382     0.8573        398        416:  52%|█████▏    | 150/289 [00:25<00:1     20/100       1.6G     0.7489     0.4382     0.8573        398        416:  52%|█████▏    | 151/289 [00:25<00:1     20/100       1.6G     0.7501     0.4386     0.8576        462        416:  52%|█████▏    | 151/289 [00:25<00:1     20/100       1.6G     0.7501     0.4386     0.8576        462        416:  53%|█████▎    | 152/289 [00:25<00:1     20/100       1.6G      0.751     0.4389     0.8579        297        416:  53%|█████▎    | 152/289 [00:25<00:1     20/100       1.6G      0.751     0.4389     0.8579        297        416:  53%|█████▎    | 153/289 [00:25<00:1     20/100       1.6G     0.7503     0.4385     0.8577        402        416:  53%|█████▎    | 153/289 [00:25<00:1     20/100       1.6G     0.7503     0.4385     0.8577        402        416:  53%|█████▎    | 154/289 [00:25<00:1     20/100       1.6G     0.7503     0.4385     0.8577        326        416:  53%|█████▎    | 154/289 [00:25<00:1     20/100       1.6G     0.7503     0.4385     0.8577        326        416:  54%|█████▎    | 155/289 [00:25<00:1     20/100       1.6G     0.7507     0.4385     0.8579        381        416:  54%|█████▎    | 155/289 [00:25<00:1     20/100       1.6G     0.7507     0.4385     0.8579        381        416:  54%|█████▍    | 156/289 [00:25<00:1     20/100       1.6G     0.7506     0.4385     0.8579        377        416:  54%|█████▍    | 156/289 [00:25<00:1     20/100       1.6G     0.7506     0.4385     0.8579        377        416:  54%|█████▍    | 157/289 [00:25<00:1     20/100       1.6G     0.7508     0.4384     0.8579        421        416:  54%|█████▍    | 157/289 [00:25<00:1     20/100       1.6G     0.7508     0.4384     0.8579        421        416:  55%|█████▍    | 158/289 [00:25<00:1     20/100       1.6G     0.7509     0.4382     0.8579        383        416:  55%|█████▍    | 158/289 [00:26<00:1     20/100       1.6G     0.7509     0.4382     0.8579        383        416:  55%|█████▌    | 159/289 [00:26<00:1     20/100       1.6G     0.7511     0.4382     0.8578        377        416:  55%|█████▌    | 159/289 [00:26<00:1     20/100       1.6G     0.7511     0.4382     0.8578        377        416:  55%|█████▌    | 160/289 [00:26<00:1     20/100       1.6G      0.751     0.4381     0.8577        373        416:  55%|█████▌    | 160/289 [00:26<00:1     20/100       1.6G      0.751     0.4381     0.8577        373        416:  56%|█████▌    | 161/289 [00:26<00:1     20/100       1.6G     0.7505     0.4378     0.8577        340        416:  56%|█████▌    | 161/289 [00:26<00:1     20/100       1.6G     0.7505     0.4378     0.8577        340        416:  56%|█████▌    | 162/289 [00:26<00:1     20/100       1.6G     0.7501     0.4376     0.8576        390        416:  56%|█████▌    | 162/289 [00:26<00:1     20/100       1.6G     0.7501     0.4376     0.8576        390        416:  56%|█████▋    | 163/289 [00:26<00:1     20/100       1.6G     0.7502     0.4376     0.8577        347        416:  56%|█████▋    | 163/289 [00:26<00:1     20/100       1.6G     0.7502     0.4376     0.8577        347        416:  57%|█████▋    | 164/289 [00:26<00:1     20/100       1.6G     0.7496     0.4374     0.8576        302        416:  57%|█████▋    | 164/289 [00:26<00:1     20/100       1.6G     0.7496     0.4374     0.8576        302        416:  57%|█████▋    | 165/289 [00:26<00:1     20/100       1.6G     0.7503     0.4375     0.8577        354        416:  57%|█████▋    | 165/289 [00:27<00:1     20/100       1.6G     0.7503     0.4375     0.8577        354        416:  57%|█████▋    | 166/289 [00:27<00:1     20/100       1.6G     0.7496     0.4374     0.8577        282        416:  57%|█████▋    | 166/289 [00:27<00:1     20/100       1.6G     0.7496     0.4374     0.8577        282        416:  58%|█████▊    | 167/289 [00:27<00:1     20/100       1.6G     0.7503     0.4376     0.8578        339        416:  58%|█████▊    | 167/289 [00:27<00:1     20/100       1.6G     0.7503     0.4376     0.8578        339        416:  58%|█████▊    | 168/289 [00:27<00:1     20/100       1.6G     0.7504     0.4377     0.8578        347        416:  58%|█████▊    | 168/289 [00:27<00:1     20/100       1.6G     0.7504     0.4377     0.8578        347        416:  58%|█████▊    | 169/289 [00:27<00:2     20/100       1.6G     0.7501     0.4376     0.8577        391        416:  58%|█████▊    | 169/289 [00:27<00:2     20/100       1.6G     0.7501     0.4376     0.8577        391        416:  59%|█████▉    | 170/289 [00:27<00:2     20/100       1.6G     0.7504     0.4375      0.858        297        416:  59%|█████▉    | 170/289 [00:28<00:2     20/100       1.6G     0.7504     0.4375      0.858        297        416:  59%|█████▉    | 171/289 [00:28<00:2     20/100       1.6G     0.7501     0.4373     0.8581        331        416:  59%|█████▉    | 171/289 [00:28<00:2     20/100       1.6G     0.7501     0.4373     0.8581        331        416:  60%|█████▉    | 172/289 [00:28<00:2     20/100       1.6G     0.7492      0.437     0.8579        413        416:  60%|█████▉    | 172/289 [00:28<00:2     20/100       1.6G     0.7492      0.437     0.8579        413        416:  60%|█████▉    | 173/289 [00:28<00:2     20/100       1.6G     0.7493     0.4371     0.8578        486        416:  60%|█████▉    | 173/289 [00:28<00:2     20/100       1.6G     0.7493     0.4371     0.8578        486        416:  60%|██████    | 174/289 [00:28<00:2     20/100       1.6G     0.7486     0.4368     0.8578        368        416:  60%|██████    | 174/289 [00:28<00:2     20/100       1.6G     0.7486     0.4368     0.8578        368        416:  61%|██████    | 175/289 [00:28<00:2     20/100       1.6G     0.7497     0.4372     0.8581        355        416:  61%|██████    | 175/289 [00:29<00:2     20/100       1.6G     0.7497     0.4372     0.8581        355        416:  61%|██████    | 176/289 [00:29<00:2     20/100       1.6G     0.7499     0.4371     0.8582        299        416:  61%|██████    | 176/289 [00:29<00:2     20/100       1.6G     0.7499     0.4371     0.8582        299        416:  61%|██████    | 177/289 [00:29<00:2     20/100       1.6G     0.7502     0.4372     0.8582        366        416:  61%|██████    | 177/289 [00:29<00:2     20/100       1.6G     0.7502     0.4372     0.8582        366        416:  62%|██████▏   | 178/289 [00:29<00:2     20/100       1.6G     0.7502     0.4373     0.8582        394        416:  62%|██████▏   | 178/289 [00:29<00:2     20/100       1.6G     0.7502     0.4373     0.8582        394        416:  62%|██████▏   | 179/289 [00:29<00:2     20/100       1.6G     0.7507     0.4373     0.8583        364        416:  62%|██████▏   | 179/289 [00:29<00:2     20/100       1.6G     0.7507     0.4373     0.8583        364        416:  62%|██████▏   | 180/289 [00:29<00:1     20/100       1.6G     0.7504     0.4371     0.8583        344        416:  62%|██████▏   | 180/289 [00:30<00:1     20/100       1.6G     0.7504     0.4371     0.8583        344        416:  63%|██████▎   | 181/289 [00:30<00:1     20/100       1.6G     0.7503     0.4371     0.8584        342        416:  63%|██████▎   | 181/289 [00:30<00:1     20/100       1.6G     0.7503     0.4371     0.8584        342        416:  63%|██████▎   | 182/289 [00:30<00:1     20/100       1.6G     0.7501     0.4371     0.8584        315        416:  63%|██████▎   | 182/289 [00:30<00:1     20/100       1.6G     0.7501     0.4371     0.8584        315        416:  63%|██████▎   | 183/289 [00:30<00:1     20/100       1.6G       0.75     0.4371     0.8582        501        416:  63%|██████▎   | 183/289 [00:30<00:1     20/100       1.6G       0.75     0.4371     0.8582        501        416:  64%|██████▎   | 184/289 [00:30<00:1     20/100       1.6G     0.7496     0.4372     0.8581        426        416:  64%|██████▎   | 184/289 [00:30<00:1     20/100       1.6G     0.7496     0.4372     0.8581        426        416:  64%|██████▍   | 185/289 [00:30<00:1     20/100       1.6G     0.7493      0.437     0.8581        346        416:  64%|██████▍   | 185/289 [00:30<00:1     20/100       1.6G     0.7493      0.437     0.8581        346        416:  64%|██████▍   | 186/289 [00:30<00:1     20/100       1.6G     0.7495     0.4373     0.8582        350        416:  64%|██████▍   | 186/289 [00:30<00:1     20/100       1.6G     0.7495     0.4373     0.8582        350        416:  65%|██████▍   | 187/289 [00:30<00:1     20/100       1.6G     0.7497     0.4374     0.8582        310        416:  65%|██████▍   | 187/289 [00:30<00:1     20/100       1.6G     0.7497     0.4374     0.8582        310        416:  65%|██████▌   | 188/289 [00:30<00:1     20/100       1.6G     0.7489     0.4372     0.8581        471        416:  65%|██████▌   | 188/289 [00:31<00:1     20/100       1.6G     0.7489     0.4372     0.8581        471        416:  65%|██████▌   | 189/289 [00:31<00:1     20/100       1.6G     0.7494     0.4375     0.8581        408        416:  65%|██████▌   | 189/289 [00:31<00:1     20/100       1.6G     0.7494     0.4375     0.8581        408        416:  66%|██████▌   | 190/289 [00:31<00:1     20/100       1.6G     0.7491     0.4374      0.858        504        416:  66%|██████▌   | 190/289 [00:31<00:1     20/100       1.6G     0.7491     0.4374      0.858        504        416:  66%|██████▌   | 191/289 [00:31<00:1     20/100       1.6G     0.7493     0.4372      0.858        405        416:  66%|██████▌   | 191/289 [00:31<00:1     20/100       1.6G     0.7493     0.4372      0.858        405        416:  66%|██████▋   | 192/289 [00:31<00:1     20/100       1.6G     0.7487      0.437     0.8579        402        416:  66%|██████▋   | 192/289 [00:31<00:1     20/100       1.6G     0.7487      0.437     0.8579        402        416:  67%|██████▋   | 193/289 [00:31<00:1     20/100       1.6G     0.7494     0.4371     0.8581        347        416:  67%|██████▋   | 193/289 [00:31<00:1     20/100       1.6G     0.7494     0.4371     0.8581        347        416:  67%|██████▋   | 194/289 [00:31<00:1     20/100       1.6G      0.749     0.4369     0.8581        375        416:  67%|██████▋   | 194/289 [00:31<00:1     20/100       1.6G      0.749     0.4369     0.8581        375        416:  67%|██████▋   | 195/289 [00:31<00:1     20/100       1.6G     0.7489     0.4369     0.8581        401        416:  67%|██████▋   | 195/289 [00:32<00:1     20/100       1.6G     0.7489     0.4369     0.8581        401        416:  68%|██████▊   | 196/289 [00:32<00:1     20/100       1.6G     0.7486     0.4367     0.8581        435        416:  68%|██████▊   | 196/289 [00:32<00:1     20/100       1.6G     0.7486     0.4367     0.8581        435        416:  68%|██████▊   | 197/289 [00:32<00:1     20/100       1.6G     0.7488     0.4368     0.8582        371        416:  68%|██████▊   | 197/289 [00:32<00:1     20/100       1.6G     0.7488     0.4368     0.8582        371        416:  69%|██████▊   | 198/289 [00:32<00:1     20/100       1.6G     0.7488     0.4369     0.8581        453        416:  69%|██████▊   | 198/289 [00:32<00:1     20/100       1.6G     0.7488     0.4369     0.8581        453        416:  69%|██████▉   | 199/289 [00:32<00:1     20/100       1.6G      0.749     0.4369     0.8582        465        416:  69%|██████▉   | 199/289 [00:32<00:1     20/100       1.6G      0.749     0.4369     0.8582        465        416:  69%|██████▉   | 200/289 [00:32<00:1     20/100       1.6G     0.7494     0.4372     0.8583        349        416:  69%|██████▉   | 200/289 [00:32<00:1     20/100       1.6G     0.7494     0.4372     0.8583        349        416:  70%|██████▉   | 201/289 [00:32<00:1     20/100       1.6G     0.7493     0.4371     0.8582        392        416:  70%|██████▉   | 201/289 [00:32<00:1     20/100       1.6G     0.7493     0.4371     0.8582        392        416:  70%|██████▉   | 202/289 [00:32<00:1     20/100       1.6G      0.749     0.4369     0.8582        421        416:  70%|██████▉   | 202/289 [00:32<00:1     20/100       1.6G      0.749     0.4369     0.8582        421        416:  70%|███████   | 203/289 [00:32<00:1     20/100       1.6G     0.7495     0.4372     0.8584        290        416:  70%|███████   | 203/289 [00:33<00:1     20/100       1.6G     0.7495     0.4372     0.8584        290        416:  71%|███████   | 204/289 [00:33<00:1     20/100       1.6G     0.7499     0.4373     0.8586        347        416:  71%|███████   | 204/289 [00:33<00:1     20/100       1.6G     0.7499     0.4373     0.8586        347        416:  71%|███████   | 205/289 [00:33<00:1     20/100       1.6G     0.7497     0.4372     0.8584        459        416:  71%|███████   | 205/289 [00:33<00:1     20/100       1.6G     0.7497     0.4372     0.8584        459        416:  71%|███████▏  | 206/289 [00:33<00:1     20/100       1.6G     0.7499     0.4372     0.8585        392        416:  71%|███████▏  | 206/289 [00:33<00:1     20/100       1.6G     0.7499     0.4372     0.8585        392        416:  72%|███████▏  | 207/289 [00:33<00:1     20/100       1.6G     0.7501     0.4371     0.8584        364        416:  72%|███████▏  | 207/289 [00:33<00:1     20/100       1.6G     0.7501     0.4371     0.8584        364        416:  72%|███████▏  | 208/289 [00:33<00:1     20/100       1.6G     0.7504     0.4373     0.8585        466        416:  72%|███████▏  | 208/289 [00:34<00:1     20/100       1.6G     0.7504     0.4373     0.8585        466        416:  72%|███████▏  | 209/289 [00:34<00:1     20/100       1.6G     0.7511     0.4375     0.8586        302        416:  72%|███████▏  | 209/289 [00:34<00:1     20/100       1.6G     0.7511     0.4375     0.8586        302        416:  73%|███████▎  | 210/289 [00:34<00:1     20/100       1.6G     0.7508     0.4373     0.8585        341        416:  73%|███████▎  | 210/289 [00:34<00:1     20/100       1.6G     0.7508     0.4373     0.8585        341        416:  73%|███████▎  | 211/289 [00:34<00:1     20/100       1.6G     0.7513     0.4375     0.8585        450        416:  73%|███████▎  | 211/289 [00:34<00:1     20/100       1.6G     0.7513     0.4375     0.8585        450        416:  73%|███████▎  | 212/289 [00:34<00:1     20/100       1.6G      0.751     0.4374     0.8584        429        416:  73%|███████▎  | 212/289 [00:34<00:1     20/100       1.6G      0.751     0.4374     0.8584        429        416:  74%|███████▎  | 213/289 [00:34<00:1     20/100       1.6G     0.7511     0.4373     0.8583        409        416:  74%|███████▎  | 213/289 [00:35<00:1     20/100       1.6G     0.7511     0.4373     0.8583        409        416:  74%|███████▍  | 214/289 [00:35<00:1     20/100       1.6G     0.7508     0.4372     0.8583        267        416:  74%|███████▍  | 214/289 [00:35<00:1     20/100       1.6G     0.7508     0.4372     0.8583        267        416:  74%|███████▍  | 215/289 [00:35<00:1     20/100       1.6G     0.7507     0.4374     0.8583        407        416:  74%|███████▍  | 215/289 [00:35<00:1     20/100       1.6G     0.7507     0.4374     0.8583        407        416:  75%|███████▍  | 216/289 [00:35<00:1     20/100       1.6G     0.7504     0.4374     0.8582        340        416:  75%|███████▍  | 216/289 [00:35<00:1     20/100       1.6G     0.7504     0.4374     0.8582        340        416:  75%|███████▌  | 217/289 [00:35<00:1     20/100       1.6G      0.751     0.4377     0.8584        370        416:  75%|███████▌  | 217/289 [00:36<00:1     20/100       1.6G      0.751     0.4377     0.8584        370        416:  75%|███████▌  | 218/289 [00:36<00:1     20/100       1.6G     0.7507     0.4377     0.8584        350        416:  75%|███████▌  | 218/289 [00:36<00:1     20/100       1.6G     0.7507     0.4377     0.8584        350        416:  76%|███████▌  | 219/289 [00:36<00:1     20/100       1.6G     0.7511     0.4378     0.8584        340        416:  76%|███████▌  | 219/289 [00:36<00:1     20/100       1.6G     0.7511     0.4378     0.8584        340        416:  76%|███████▌  | 220/289 [00:36<00:1     20/100       1.6G     0.7509     0.4377     0.8584        368        416:  76%|███████▌  | 220/289 [00:36<00:1     20/100       1.6G     0.7509     0.4377     0.8584        368        416:  76%|███████▋  | 221/289 [00:36<00:1     20/100       1.6G     0.7508     0.4378     0.8584        394        416:  76%|███████▋  | 221/289 [00:36<00:1     20/100       1.6G     0.7508     0.4378     0.8584        394        416:  77%|███████▋  | 222/289 [00:36<00:1     20/100       1.6G     0.7508     0.4379     0.8585        398        416:  77%|███████▋  | 222/289 [00:36<00:1     20/100       1.6G     0.7508     0.4379     0.8585        398        416:  77%|███████▋  | 223/289 [00:36<00:0     20/100       1.6G     0.7511     0.4381     0.8586        324        416:  77%|███████▋  | 223/289 [00:36<00:0     20/100       1.6G     0.7511     0.4381     0.8586        324        416:  78%|███████▊  | 224/289 [00:36<00:0     20/100       1.6G     0.7507      0.438     0.8585        364        416:  78%|███████▊  | 224/289 [00:36<00:0     20/100       1.6G     0.7507      0.438     0.8585        364        416:  78%|███████▊  | 225/289 [00:36<00:0     20/100       1.6G     0.7505     0.4379     0.8585        331        416:  78%|███████▊  | 225/289 [00:37<00:0     20/100       1.6G     0.7505     0.4379     0.8585        331        416:  78%|███████▊  | 226/289 [00:37<00:0     20/100       1.6G     0.7507     0.4378     0.8584        370        416:  78%|███████▊  | 226/289 [00:37<00:0     20/100       1.6G     0.7507     0.4378     0.8584        370        416:  79%|███████▊  | 227/289 [00:37<00:0     20/100       1.6G     0.7513      0.438     0.8586        418        416:  79%|███████▊  | 227/289 [00:37<00:0     20/100       1.6G     0.7513      0.438     0.8586        418        416:  79%|███████▉  | 228/289 [00:37<00:0     20/100       1.6G     0.7515      0.438     0.8586        333        416:  79%|███████▉  | 228/289 [00:37<00:0     20/100       1.6G     0.7515      0.438     0.8586        333        416:  79%|███████▉  | 229/289 [00:37<00:0     20/100       1.6G     0.7515      0.438     0.8585        428        416:  79%|███████▉  | 229/289 [00:37<00:0     20/100       1.6G     0.7515      0.438     0.8585        428        416:  80%|███████▉  | 230/289 [00:37<00:0     20/100       1.6G      0.751     0.4378     0.8584        306        416:  80%|███████▉  | 230/289 [00:37<00:0     20/100       1.6G      0.751     0.4378     0.8584        306        416:  80%|███████▉  | 231/289 [00:37<00:0     20/100       1.6G     0.7505     0.4375     0.8584        342        416:  80%|███████▉  | 231/289 [00:37<00:0     20/100       1.6G     0.7505     0.4375     0.8584        342        416:  80%|████████  | 232/289 [00:37<00:0     20/100       1.6G     0.7502     0.4373     0.8583        307        416:  80%|████████  | 232/289 [00:38<00:0     20/100       1.6G     0.7502     0.4373     0.8583        307        416:  81%|████████  | 233/289 [00:38<00:0     20/100       1.6G     0.7501     0.4373     0.8583        430        416:  81%|████████  | 233/289 [00:38<00:0     20/100       1.6G     0.7501     0.4373     0.8583        430        416:  81%|████████  | 234/289 [00:38<00:0     20/100       1.6G     0.7503     0.4374     0.8582        476        416:  81%|████████  | 234/289 [00:38<00:0     20/100       1.6G     0.7503     0.4374     0.8582        476        416:  81%|████████▏ | 235/289 [00:38<00:0     20/100       1.6G     0.7509     0.4374     0.8583        326        416:  81%|████████▏ | 235/289 [00:38<00:0     20/100       1.6G     0.7509     0.4374     0.8583        326        416:  82%|████████▏ | 236/289 [00:38<00:0     20/100       1.6G     0.7508     0.4375     0.8583        437        416:  82%|████████▏ | 236/289 [00:38<00:0     20/100       1.6G     0.7508     0.4375     0.8583        437        416:  82%|████████▏ | 237/289 [00:38<00:0     20/100       1.6G     0.7509     0.4374     0.8584        289        416:  82%|████████▏ | 237/289 [00:38<00:0     20/100       1.6G     0.7509     0.4374     0.8584        289        416:  82%|████████▏ | 238/289 [00:38<00:0     20/100       1.6G     0.7507     0.4373     0.8583        444        416:  82%|████████▏ | 238/289 [00:38<00:0     20/100       1.6G     0.7507     0.4373     0.8583        444        416:  83%|████████▎ | 239/289 [00:38<00:0     20/100       1.6G     0.7509     0.4373     0.8583        438        416:  83%|████████▎ | 239/289 [00:39<00:0     20/100       1.6G     0.7509     0.4373     0.8583        438        416:  83%|████████▎ | 240/289 [00:39<00:0     20/100       1.6G     0.7512     0.4373     0.8583        366        416:  83%|████████▎ | 240/289 [00:39<00:0     20/100       1.6G     0.7512     0.4373     0.8583        366        416:  83%|████████▎ | 241/289 [00:39<00:0     20/100       1.6G     0.7512     0.4371     0.8584        371        416:  83%|████████▎ | 241/289 [00:39<00:0     20/100       1.6G     0.7512     0.4371     0.8584        371        416:  84%|████████▎ | 242/289 [00:39<00:0     20/100       1.6G     0.7516     0.4372     0.8586        286        416:  84%|████████▎ | 242/289 [00:39<00:0     20/100       1.6G     0.7516     0.4372     0.8586        286        416:  84%|████████▍ | 243/289 [00:39<00:0     20/100       1.6G     0.7519     0.4372     0.8586        378        416:  84%|████████▍ | 243/289 [00:39<00:0     20/100       1.6G     0.7519     0.4372     0.8586        378        416:  84%|████████▍ | 244/289 [00:39<00:0     20/100       1.6G     0.7521     0.4373     0.8587        395        416:  84%|████████▍ | 244/289 [00:39<00:0     20/100       1.6G     0.7521     0.4373     0.8587        395        416:  85%|████████▍ | 245/289 [00:39<00:0     20/100       1.6G      0.752     0.4372     0.8587        362        416:  85%|████████▍ | 245/289 [00:40<00:0     20/100       1.6G      0.752     0.4372     0.8587        362        416:  85%|████████▌ | 246/289 [00:40<00:1     20/100       1.6G     0.7521     0.4374     0.8588        303        416:  85%|████████▌ | 246/289 [00:40<00:1     20/100       1.6G     0.7521     0.4374     0.8588        303        416:  85%|████████▌ | 247/289 [00:40<00:0     20/100       1.6G     0.7525     0.4376     0.8592        286        416:  85%|████████▌ | 247/289 [00:40<00:0     20/100       1.6G     0.7525     0.4376     0.8592        286        416:  86%|████████▌ | 248/289 [00:40<00:0     20/100       1.6G     0.7523     0.4375     0.8591        401        416:  86%|████████▌ | 248/289 [00:40<00:0     20/100       1.6G     0.7523     0.4375     0.8591        401        416:  86%|████████▌ | 249/289 [00:40<00:0     20/100       1.6G     0.7521     0.4375     0.8593        284        416:  86%|████████▌ | 249/289 [00:41<00:0     20/100       1.6G     0.7521     0.4375     0.8593        284        416:  87%|████████▋ | 250/289 [00:41<00:0     20/100       1.6G      0.752     0.4375     0.8592        452        416:  87%|████████▋ | 250/289 [00:41<00:0     20/100       1.6G      0.752     0.4375     0.8592        452        416:  87%|████████▋ | 251/289 [00:41<00:0     20/100       1.6G     0.7518     0.4374     0.8591        422        416:  87%|████████▋ | 251/289 [00:41<00:0     20/100       1.6G     0.7518     0.4374     0.8591        422        416:  87%|████████▋ | 252/289 [00:41<00:0     20/100       1.6G      0.752     0.4373     0.8592        331        416:  87%|████████▋ | 252/289 [00:41<00:0     20/100       1.6G      0.752     0.4373     0.8592        331        416:  88%|████████▊ | 253/289 [00:41<00:0     20/100       1.6G     0.7518     0.4374     0.8592        276        416:  88%|████████▊ | 253/289 [00:42<00:0     20/100       1.6G     0.7518     0.4374     0.8592        276        416:  88%|████████▊ | 254/289 [00:42<00:0     20/100       1.6G     0.7521     0.4374     0.8593        366        416:  88%|████████▊ | 254/289 [00:42<00:0     20/100       1.6G     0.7521     0.4374     0.8593        366        416:  88%|████████▊ | 255/289 [00:42<00:0     20/100       1.6G     0.7522     0.4373     0.8593        292        416:  88%|████████▊ | 255/289 [00:42<00:0     20/100       1.6G     0.7522     0.4373     0.8593        292        416:  89%|████████▊ | 256/289 [00:42<00:0     20/100       1.6G     0.7521     0.4373     0.8592        482        416:  89%|████████▊ | 256/289 [00:42<00:0     20/100       1.6G     0.7521     0.4373     0.8592        482        416:  89%|████████▉ | 257/289 [00:42<00:0     20/100       1.6G      0.752     0.4373     0.8592        382        416:  89%|████████▉ | 257/289 [00:42<00:0     20/100       1.6G      0.752     0.4373     0.8592        382        416:  89%|████████▉ | 258/289 [00:42<00:0     20/100       1.6G     0.7518     0.4373     0.8591        422        416:  89%|████████▉ | 258/289 [00:42<00:0     20/100       1.6G     0.7518     0.4373     0.8591        422        416:  90%|████████▉ | 259/289 [00:42<00:0     20/100       1.6G     0.7517     0.4373     0.8591        371        416:  90%|████████▉ | 259/289 [00:42<00:0     20/100       1.6G     0.7517     0.4373     0.8591        371        416:  90%|████████▉ | 260/289 [00:42<00:0     20/100       1.6G     0.7515     0.4371      0.859        373        416:  90%|████████▉ | 260/289 [00:42<00:0     20/100       1.6G     0.7515     0.4371      0.859        373        416:  90%|█████████ | 261/289 [00:42<00:0     20/100       1.6G     0.7513      0.437     0.8589        408        416:  90%|█████████ | 261/289 [00:43<00:0     20/100       1.6G     0.7513      0.437     0.8589        408        416:  91%|█████████ | 262/289 [00:43<00:0     20/100       1.6G     0.7507     0.4367     0.8589        373        416:  91%|█████████ | 262/289 [00:43<00:0     20/100       1.6G     0.7507     0.4367     0.8589        373        416:  91%|█████████ | 263/289 [00:43<00:0     20/100       1.6G     0.7507     0.4369     0.8588        583        416:  91%|█████████ | 263/289 [00:43<00:0     20/100       1.6G     0.7507     0.4369     0.8588        583        416:  91%|█████████▏| 264/289 [00:43<00:0     20/100       1.6G      0.751     0.4369     0.8588        355        416:  91%|█████████▏| 264/289 [00:43<00:0     20/100       1.6G      0.751     0.4369     0.8588        355        416:  92%|█████████▏| 265/289 [00:43<00:0     20/100       1.6G     0.7513     0.4369     0.8589        267        416:  92%|█████████▏| 265/289 [00:43<00:0     20/100       1.6G     0.7513     0.4369     0.8589        267        416:  92%|█████████▏| 266/289 [00:43<00:0     20/100       1.6G     0.7512     0.4368     0.8588        416        416:  92%|█████████▏| 266/289 [00:43<00:0     20/100       1.6G     0.7512     0.4368     0.8588        416        416:  92%|█████████▏| 267/289 [00:43<00:0     20/100       1.6G      0.751     0.4368     0.8588        391        416:  92%|█████████▏| 267/289 [00:43<00:0     20/100       1.6G      0.751     0.4368     0.8588        391        416:  93%|█████████▎| 268/289 [00:43<00:0     20/100       1.6G     0.7512     0.4368     0.8589        396        416:  93%|█████████▎| 268/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368     0.8589        396        416:  93%|█████████▎| 269/289 [00:44<00:0     20/100       1.6G     0.7513     0.4369      0.859        378        416:  93%|█████████▎| 269/289 [00:44<00:0     20/100       1.6G     0.7513     0.4369      0.859        378        416:  93%|█████████▎| 270/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368      0.859        427        416:  93%|█████████▎| 270/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368      0.859        427        416:  94%|█████████▍| 271/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368      0.859        304        416:  94%|█████████▍| 271/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368      0.859        304        416:  94%|█████████▍| 272/289 [00:44<00:0     20/100       1.6G     0.7512     0.4367     0.8589        325        416:  94%|█████████▍| 272/289 [00:44<00:0     20/100       1.6G     0.7512     0.4367     0.8589        325        416:  94%|█████████▍| 273/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368     0.8591        289        416:  94%|█████████▍| 273/289 [00:44<00:0     20/100       1.6G     0.7512     0.4368     0.8591        289        416:  95%|█████████▍| 274/289 [00:44<00:0     20/100       1.6G     0.7515      0.437     0.8591        379        416:  95%|█████████▍| 274/289 [00:44<00:0     20/100       1.6G     0.7515      0.437     0.8591        379        416:  95%|█████████▌| 275/289 [00:44<00:0     20/100       1.6G     0.7516      0.437     0.8592        356        416:  95%|█████████▌| 275/289 [00:44<00:0     20/100       1.6G     0.7516      0.437     0.8592        356        416:  96%|█████████▌| 276/289 [00:44<00:0     20/100       1.6G     0.7515     0.4369     0.8593        289        416:  96%|█████████▌| 276/289 [00:45<00:0     20/100       1.6G     0.7515     0.4369     0.8593        289        416:  96%|█████████▌| 277/289 [00:45<00:0     20/100       1.6G     0.7516     0.4369     0.8594        377        416:  96%|█████████▌| 277/289 [00:45<00:0     20/100       1.6G     0.7516     0.4369     0.8594        377        416:  96%|█████████▌| 278/289 [00:45<00:0     20/100       1.6G     0.7514     0.4369     0.8594        445        416:  96%|█████████▌| 278/289 [00:45<00:0     20/100       1.6G     0.7514     0.4369     0.8594        445        416:  97%|█████████▋| 279/289 [00:45<00:0     20/100       1.6G     0.7508     0.4367     0.8593        321        416:  97%|█████████▋| 279/289 [00:45<00:0     20/100       1.6G     0.7     20/100       1.6G     0.7505     0.4364     0.8591        438        416:  97%|█████████▋| 281/289 [00:45<00:0     20/100       1.6G     0.7505     0.4364     0.8591        438        416:  98%|█████████▊| 282/289 [00:45<00:0     20/100       1.6G     0.7502     0.4364      0.859        306        416:  98%|█████████▊| 282/289 [00:46<00:0     20/100       1.6G     0.7502     0.4364      0.859        306        416:  98%|█████████▊| 283/289 [00:46<00:0     20/100       1.6G     0.7505     0.4364     0.8591        343        416:  98%|█████████▊| 283/289 [00:46<00:0     20/100       1.6G     0.7505     0.4364     0.8591        343        416:  98%|█████████▊| 284/289 [00:46<00:0     20/100       1.6G     0.7504     0.4363     0.8591        412        416:  98%|█████████▊| 284/289 [00:46<00:0     20/100       1.6G     0.7504     0.4363     0.8591        412        416:  99%|█████████▊| 285/289 [00:46<00:0     20/100       1.6G     0.7504     0.4363     0.8591        425        416:  99%|█████████▊| 285/289 [00:46<00:0     20/100       1.6G     0.7504     0.4363     0.8591        425        416:  99%|█████████▉| 286/289 [00:46<00:0     20/100       1.6G     0.7502     0.4363     0.8591        479        416:  99%|█████████▉| 286/289 [00:47<00:0     20/100       1.6G     0.7502     0.4363     0.8591        479        416:  99%|█████████▉| 287/289 [00:47<00:0     20/100       1.6G     0.7499     0.4363     0.8589        356        416:  99%|█████████▉| 287/289 [00:47<00:0     20/100       1.6G     0.7499     0.4363     0.8589        356        416: 100%|█████████▉| 288/289 [00:47<00:0     20/100       1.6G     0.7498     0.4362     0.8589        304        416: 100%|█████████▉| 288/289 [00:47<00:0     20/100       1.6G     0.7498     0.4362     0.8589        304        416: 100%|██████████| 289/289 [00:47<00:0     20/100       1.6G     0.7498     0.4362     0.8589        304        416: 100%|██████████| 289/289 [00:47<00:00,  6.10it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00
                   all        243       4250      0.982      0.907      0.953      0.764

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     21/100       1.6G     0.7436     0.4317     0.8586        240        416: 100%|██████████| 289/289 [00:46<00:
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.5
                   all        243       4250      0.983      0.901      0.952      0.778

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     22/100       1.6G     0.7352     0.4274     0.8578        317        416: 100%|██████████| 289/289 [00:47<00:00,  6.05it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.4
                   all        243       4250      0.979      0.901      0.951      0.764

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     23/100       1.6G      0.724     0.4201      0.855        384        416: 100%|██████████| 289/289 [00:45<00:00,  6.31it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  2.9
                   all        243       4250      0.971      0.913      0.953      0.765

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     24/100       1.6G     0.7239     0.4197     0.8555        238        416: 100%|██████████| 289/289 [00:45<00:00,  6.42it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3
                   all        243       4250      0.976      0.904       0.95      0.749

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     25/100       1.6G     0.7228     0.4157     0.8531        285        416: 100%|██████████| 289/289 [00:46<00:00,  6.18it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9 
                   all        243       4250      0.984      0.902      0.953      0.783

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     26/100       1.6G     0.7122     0.4124     0.8534        347        416: 100%|██████████| 289/289 [00:44<00:00,  6.46it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9
                   all        243       4250      0.972      0.912      0.954      0.775

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     27/100       1.6G     0.7103     0.4088     0.8513        290        416: 100%|██████████| 289/289 [00:47<00:00,  6.10it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.9 
                   all        243       4250      0.984      0.904      0.953      0.786

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     28/100       1.6G     0.6984      0.402     0.8487        307        416: 100%|██████████| 289/289 [00:46<00:00,  6.20it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.9
                   all        243       4250      0.976       0.91      0.955      0.777

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     29/100       1.6G     0.7038      0.403     0.8513        208        416: 100%|██████████| 289/289 [00:47<00:00,  6.07it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.1 
                   all        243       4250      0.966      0.921      0.957      0.788

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     30/100       1.6G     0.6949     0.3971     0.8494        334        416: 100%|██████████| 289/289 [00:45<00:00,  6.30it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  2.9 
                   all        243       4250      0.974      0.919      0.956      0.792

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     31/100       1.6G     0.6903     0.3952     0.8482        236        416: 100%|██████████| 289/289 [00:47<00:00,  6.09it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3
                   all        243       4250      0.976      0.914      0.957      0.791

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     32/100       1.6G     0.6815     0.3898     0.8468        370        416: 100%|██████████| 289/289 [00:48<00:00,  6.00it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.7
                   all        243       4250      0.979      0.914      0.957      0.803

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     34/100       1.6G     0.6783     0.3867     0.8465        220        416: 100%|██████████| 289/289 [00:44<00:00,  6.46it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3
                   all        243       4250      0.974       0.91       0.96      0.794

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     35/100       1.6G     0.6869     0.3872     0.8485        321        416: 100%|██████████| 289/289 [00:44<00:00,  6.45it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.5
                   all        243       4250      0.979      0.909      0.958      0.807
 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     36/100       1.6G     0.6768     0.3811     0.8451        299        416: 100%|██████████| 289/289 [00:45<00:00,  6.28it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9 
                   all        243       4250      0.977      0.915      0.959      0.803

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     37/100       1.6G     0.6608     0.3755     0.8431        335        416: 100%|██████████| 289/289 [00:46<00:00,  6.26it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.0 
                   all        243       4250      0.974      0.917       0.96      0.802

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     38/100       1.6G     0.6603     0.3756     0.8434        208        416: 100%|██████████| 289/289 [00:44<00:00,  6.54it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3 
                   all        243       4250      0.976      0.915      0.962      0.802

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     39/100       1.6G     0.6628      0.377     0.8449        291        416: 100%|██████████| 289/289 [00:43<00:00,  6.67it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.8
                   all        243       4250       0.98      0.913       0.96      0.808

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     40/100       1.6G     0.6527     0.3725      0.842        312        416: 100%|██████████| 289/289 [00:44<00:00,  6.47it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.6 
                   all        243       4250      0.983      0.915      0.961      0.816


      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     41/100       1.6G     0.6478     0.3664     0.8415        344        416: 100%|██████████| 289/289 [00:46<00:00,  6.28it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.4
                   all        243       4250      0.976      0.922      0.965      0.816

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     42/100       1.6G     0.6549      0.367     0.8399        280        416: 100%|██████████| 289/289 [00:46<00:00,  6.18it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.5
                   all        243       4250      0.986      0.917      0.965      0.822

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     43/100       1.6G     0.6387     0.3643     0.8382        262        416: 100%|██████████| 289/289 [00:46<00:00,  6.26it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.6 
                   all        243       4250      0.983      0.913      0.963      0.826


      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     44/100       1.6G     0.6533     0.3679      0.841        241        416: 100%|██████████| 289/289 [00:44<00:00,  6.49it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.4 
                   all        243       4250      0.983      0.922      0.965      0.816

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     45/100       1.6G     0.6326     0.3583     0.8384        310        416: 100%|██████████| 289/289 [00:46<00:00,  6.19it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.6
                   all        243       4250      0.979      0.927      0.967       0.83

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     46/100       1.6G     0.6341     0.3569     0.8384        291        416: 100%|██████████| 289/289 [00:44<00:00,  6.46it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.2
                   all        243       4250      0.982      0.924      0.963      0.817

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     47/100       1.6G     0.6337     0.3589     0.8393        373        416: 100%|██████████| 289/289 [00:46<00:00,  6.22it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3
                   all        243       4250      0.981      0.925      0.965      0.829

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     48/100       1.6G     0.6277     0.3552     0.8377        277        416: 100%|██████████| 289/289 [00:44<00:00,  6.52it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3
                   all        243       4250      0.976      0.925      0.964      0.826

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     49/100       1.6G     0.6199     0.3511     0.8348        256        416: 100%|██████████| 289/289 [00:45<00:00,  6.40it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.7
                   all        243       4250      0.978      0.926      0.965      0.827

Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     50/100       1.6G     0.6298     0.3536     0.8376        224        416: 100%|██████████| 289/289 [00:47<00:00,  6.12it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.6 
                   all        243       4250      0.975      0.922      0.966      0.824

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     51/100       1.6G     0.6121     0.3476     0.8325        309        416: 100%|██████████| 289/289 [00:45<00:00,  6.38it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.3 
                   all        243       4250      0.977      0.924      0.965      0.829

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     52/100       1.6G     0.6149     0.3473     0.8347        302        416: 100%|██████████| 289/289 [00:44<00:00,  6.54it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.8
                   all        243       4250      0.971      0.932      0.967      0.831

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     53/100       1.6G     0.6134     0.3476     0.8364        285        416: 100%|██████████| 289/289 [00:47<00:00,  6.13it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.7 
                   all        243       4250      0.973      0.926      0.966      0.837

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     54/100       1.6G     0.6125     0.3479     0.8338        323        416: 100%|██████████| 289/289 [00:45<00:00,  6.32it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.1
                   all        243       4250       0.98      0.924      0.965       0.83

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     55/100       1.6G     0.6051     0.3456     0.8338        266        416: 100%|██████████| 289/289 [00:46<00:00,  6.23it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9
                   all        243       4250      0.983      0.918      0.968      0.831

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     56/100       1.6G     0.6141     0.3464     0.8342        299        416: 100%|██████████| 289/289 [00:46<00:00,  6.26it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.1 
                   all        243       4250      0.973       0.93      0.967      0.834

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     57/100       1.6G     0.6084     0.3412     0.8327        274        416: 100%|██████████| 289/289 [00:45<00:00,  6.34it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.1 
                   all        243       4250      0.975      0.928      0.965      0.836

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     58/100       1.6G     0.5945     0.3382     0.8303        253        416: 100%|██████████| 289/289 [00:47<00:00,  6.11it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.6 
                   all        243       4250      0.979      0.928      0.967      0.834

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     59/100       1.6G     0.5981      0.338     0.8324        259        416: 100%|██████████| 289/289 [00:45<00:00,  6.32it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9
                   all        243       4250      0.979      0.926      0.967       0.83


      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     60/100       1.6G     0.5984      0.339     0.8319        330        416: 100%|██████████| 289/289 [00:47<00:00,  6.11it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.0
                   all        243       4250      0.981      0.924      0.966      0.836

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     61/100       1.6G     0.5936      0.336     0.8323        266        416: 100%|██████████| 289/289 [00:44<00:00,  6.53it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.8
                   all        243       4250      0.981      0.921      0.968       0.84

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     62/100       1.6G     0.5892     0.3332     0.8304        269        416: 100%|██████████| 289/289 [00:43<00:00,  6.61it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:02<00:00,  3.1 
                   all        243       4250      0.979      0.925      0.968      0.838

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     63/100       1.6G     0.5848      0.331     0.8289        350        416: 100%|██████████| 289/289 [00:45<00:00,  6.36it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.8
                   all        243       4250      0.984      0.921      0.966      0.842

Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     64/100       1.6G     0.5831     0.3318     0.8287        254        416: 100%|██████████| 289/289 [00:46<00:00,  6.19it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.0 
                   all        243       4250      0.981      0.925      0.968       0.84

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     65/100       1.6G     0.5789     0.3286     0.8291        416        416: 100%|██████████| 289/289 [00:45<00:00,  6.40it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9
                   all        243       4250      0.973      0.933      0.968      0.842

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     66/100       1.6G     0.5819     0.3292      0.829        297        416: 100%|██████████| 289/289 [00:47<00:00,  6.12it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.8
                   all        243       4250      0.982      0.922      0.968      0.839

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     67/100      1.67G     0.5763     0.3288     0.8278        276        416: 100%|██████████| 289/289 [00:44<00:00,  6.45it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.7 
                   all        243       4250      0.974       0.93      0.968      0.838

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     68/100      1.67G     0.5741     0.3273     0.8296        287        416: 100%|██████████| 289/289 [00:43<00:00,  6.64it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.0
                   all        243       4250      0.981      0.925       0.97      0.838

Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     69/100      1.67G      0.574     0.3248     0.8261        302        416: 100%|██████████| 289/289 [00:46<00:00,  6.26it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.2
                   all        243       4250      0.974       0.93      0.969      0.845

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     70/100      1.67G      0.563     0.3214     0.8266        290        416: 100%|██████████| 289/289 [00:45<00:00,  6.37it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.3 
                   all        243       4250      0.975      0.932       0.97       0.84

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     71/100      1.67G     0.5692     0.3236     0.8261        287        416: 100%|██████████| 289/289 [00:44<00:00,  6.56it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  4.3
                   all        243       4250      0.983      0.924       0.97      0.845

Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     72/100      1.67G      0.564     0.3205     0.8263        222        416: 100%|██████████| 289/289 [00:47<00:00,  6.11it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.8
                   all        243       4250      0.976      0.933      0.971      0.843

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     73/100      1.67G     0.5578     0.3182     0.8258        288        416: 100%|██████████| 289/289 [00:46<00:00,  6.22it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.3 
                   all        243       4250      0.974      0.932      0.969      0.843

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     74/100      1.67G     0.5559     0.3167      0.824        343        416: 100%|██████████| 289/289 [00:44<00:00,  6.50it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.1 
                   all        243       4250      0.972      0.934       0.97      0.848

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     75/100      1.67G     0.5576     0.3177     0.8241        188        416: 100%|██████████| 289/289 [00:35<00:00,  8.07it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.0 
                   all        243       4250      0.976      0.933       0.97      0.844

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     76/100      1.67G     0.5491     0.3146      0.823        325        416: 100%|██████████| 289/289 [00:36<00:00,  7.95it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.8 
                   all        243       4250      0.979      0.928       0.97      0.848

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     77/100      1.67G     0.5571     0.3157     0.8249        287        416: 100%|██████████| 289/289 [00:35<00:00,  8.04it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.2
                   all        243       4250      0.976       0.93      0.971      0.846

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     78/100      1.67G       0.55     0.3131     0.8248        308        416: 100%|██████████| 289/289 [00:35<00:00,  8.23it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.4 
                   all        243       4250      0.975      0.932      0.972       0.85

Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     79/100      1.67G     0.5431     0.3101     0.8222        299        416: 100%|██████████| 289/289 [00:36<00:00,  7.94it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.5
                   all        243       4250      0.968       0.94      0.972       0.85

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     80/100      1.67G     0.5471      0.311     0.8233        333        416: 100%|██████████| 289/289 [00:35<00:00,  8.07it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9
                   all        243       4250      0.975      0.931      0.971      0.849

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     81/100      1.67G     0.5441     0.3102     0.8223        302        416: 100%|██████████| 289/289 [00:35<00:00,  8.16it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.4
                   all        243       4250      0.985      0.921       0.97      0.853

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     82/100      1.67G     0.5447       0.31     0.8237        265        416: 100%|██████████| 289/289 [00:34<00:00,  8.29it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.0 
                   all        243       4250       0.98      0.929       0.97      0.854

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     83/100      1.67G     0.5389      0.308     0.8223        267        416: 100%|██████████| 289/289 [00:36<00:00,  7.90it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.9 
                   all        243       4250      0.981       0.93       0.97      0.852

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     84/100      1.67G     0.5371     0.3067     0.8216        449        416: 100%|██████████| 289/289 [00:35<00:00,  8.06it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.3 
                   all        243       4250      0.979      0.932      0.971      0.849

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     85/100      1.67G     0.5321     0.3035     0.8202        243        416: 100%|██████████| 289/289 [00:35<00:00,  8.17it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.0 
                   all        243       4250      0.983      0.931      0.972      0.853

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     86/100      1.67G      0.533      0.305     0.8202        274        416: 100%|██████████| 289/289 [00:35<00:00,  8.08it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.1 
                   all        243       4250       0.98      0.936      0.973      0.852

 Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     87/100      1.67G     0.5337     0.3053     0.8216        197        416: 100%|██████████| 289/289 [00:36<00:00,  7.98it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.8 
                   all        243       4250      0.978      0.935      0.972      0.852

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     88/100      1.67G     0.5284     0.3015     0.8183        317        416: 100%|██████████| 289/289 [00:35<00:00,  8.04it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  5.8
                   all        243       4250      0.979      0.929      0.971      0.853

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     89/100      1.67G     0.5302     0.3029     0.8208        276        416: 100%|██████████| 289/289 [00:35<00:00,  8.18it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.3 
                   all        243       4250      0.978      0.934      0.972      0.852

      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size
     90/100      1.67G     0.5201     0.3005     0.8177        253        416: 100%|██████████| 289/289 [00:35<00:00,  8.25it/s]
                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 8/8 [00:01<00:00,  6.4
                   all        243       4250      0.977      0.933      0.971      0.854
Closing dataloader mosaic
albumentations: Blur(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))
Traceback (most recent call last):
  File "<string>", line 1, in <module>
  File "E:\New folder\Lib\multiprocessing\spawn.py", line 120, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\New folder\Lib\multiprocessing\spawn.py", line 129, in _main
    prepare(preparation_data)
  File "E:\New folder\Lib\multiprocessing\spawn.py", line 238, in prepare
    _fixup_main_from_name(data['init_main_from_name'])
  File "E:\New folder\Lib\multiprocessing\spawn.py", line 262, in _fixup_main_from_name
    main_content = runpy.run_module(mod_name,
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen runpy>", line 222, in run_module
  File "<frozen runpy>", line 112, in _get_module_details
  File "E:\New folder\Lib\site-packages\ultralytics\__init__.py", line 11, in <module>
    from ultralytics.models import NAS, RTDETR, SAM, YOLO, YOLOE, FastSAM, YOLOWorld
  File "E:\New folder\Lib\site-packages\ultralytics\models\__init__.py", line 3, in <module>
    from .fastsam import FastSAM
  File "E:\New folder\Lib\site-packages\ultralytics\models\fastsam\__init__.py", line 3, in <module>
    from .model import FastSAM
  File "E:\New folder\Lib\site-packages\ultralytics\models\fastsam\model.py", line 5, in <module>
    from ultralytics.engine.model import Model
  File "E:\New folder\Lib\site-packages\ultralytics\engine\model.py", line 11, in <module>
    from ultralytics.cfg import TASK2DATA, get_cfg, get_save_dir
  File "E:\New folder\Lib\site-packages\ultralytics\cfg\__init__.py", line 11, in <module>
    from ultralytics.utils import (
  File "E:\New folder\Lib\site-packages\ultralytics\utils\__init__.py", line 22, in <module>
    import cv2
  File "E:\New folder\Lib\site-packages\cv2\__init__.py", line 181, in <module>
    bootstrap()
  File "E:\New folder\Lib\site-packages\cv2\__init__.py", line 153, in bootstrap
    native_module = importlib.import_module("cv2")
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\New folder\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ImportError: DLL load failed while importing cv2: The paging file is too small for this operation to complete.



