"""
Train a YOLO model for chess piece detection using GPU acceleration.
This script is optimized for RTX 3050 laptop GPU and implements:
1. Extended training with high epochs
2. Mixed precision training
3. Advanced learning rate scheduling
4. Checkpoint ensemble creation
5. Memory optimization techniques
"""

import os
import sys
import math
import argparse
import torch
import platform
from pathlib import Path
from datetime import datetime
import shutil
import yaml
import random
import numpy as np
from ultralytics import YOL<PERSON>

def set_seed(seed=42):
    """Set all random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def optimize_gpu():
    """Optimize GPU settings for training."""
    # Enable cuDNN auto-tuner
    torch.backends.cudnn.benchmark = True
    
    # Clear GPU cache
    torch.cuda.empty_cache()
    
    # Set TensorFloat32 precision if available (Ampere GPUs like RTX 3050)
    torch.set_float32_matmul_precision('high')
    
    # Print GPU info
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def split_dataset(input_dir, output_dir, train_ratio=0.8, seed=42):
    """
    Split the labeled dataset into training and validation sets.
    
    Args:
        input_dir: Directory containing the labeled images and annotations
        output_dir: Directory to save the split dataset
        train_ratio: Ratio of images to use for training (default: 0.8)
        seed: Random seed for reproducibility
    """
    random.seed(seed)
    
    # Create output directories
    train_img_dir = os.path.join(output_dir, 'images', 'train')
    val_img_dir = os.path.join(output_dir, 'images', 'val')
    train_label_dir = os.path.join(output_dir, 'labels', 'train')
    val_label_dir = os.path.join(output_dir, 'labels', 'val')
    
    os.makedirs(train_img_dir, exist_ok=True)
    os.makedirs(val_img_dir, exist_ok=True)
    os.makedirs(train_label_dir, exist_ok=True)
    os.makedirs(val_label_dir, exist_ok=True)
    
    # Get all image files
    image_files = []
    for ext in ['.jpg', '.jpeg', '.png']:
        image_files.extend(list(Path(input_dir).glob(f'*{ext}')))
    
    # Shuffle and split
    random.shuffle(image_files)
    split_idx = int(len(image_files) * train_ratio)
    train_files = image_files[:split_idx]
    val_files = image_files[split_idx:]
    
    print(f"Total images: {len(image_files)}")
    print(f"Training images: {len(train_files)}")
    print(f"Validation images: {len(val_files)}")
    
    # Copy files to their respective directories
    for img_path in train_files:
        # Copy image
        shutil.copy(img_path, os.path.join(train_img_dir, img_path.name))
        
        # Copy corresponding label if it exists
        label_path = os.path.join(os.path.dirname(input_dir), 'labels', f"{img_path.stem}.txt")
        if os.path.exists(label_path):
            shutil.copy(label_path, os.path.join(train_label_dir, f"{img_path.stem}.txt"))
        else:
            print(f"Warning: No label file found for {img_path.name}")
    
    for img_path in val_files:
        # Copy image
        shutil.copy(img_path, os.path.join(val_img_dir, img_path.name))
        
        # Copy corresponding label if it exists
        label_path = os.path.join(os.path.dirname(input_dir), 'labels', f"{img_path.stem}.txt")
        if os.path.exists(label_path):
            shutil.copy(label_path, os.path.join(val_label_dir, f"{img_path.stem}.txt"))
        else:
            print(f"Warning: No label file found for {img_path.name}")
    
    print(f"Dataset split complete. Files saved to {output_dir}")
    return output_dir

def create_dataset_yaml(output_dir, class_names):
    """
    Create a YAML configuration file for the dataset.
    
    Args:
        output_dir: Directory to save the YAML file
        class_names: List of class names
    
    Returns:
        Path to the created YAML file
    """
    yaml_path = os.path.join(output_dir, 'dataset.yaml')
    
    # Create dataset configuration
    dataset_config = {
        'path': output_dir,
        'train': 'images/train',
        'val': 'images/val',
        'names': {i: name for i, name in enumerate(class_names)},
        'nc': len(class_names)
    }
    
    # Write to YAML file
    with open(yaml_path, 'w') as f:
        yaml.dump(dataset_config, f, default_flow_style=False)
    
    print(f"Dataset configuration saved to {yaml_path}")
    return yaml_path

def create_ensemble(model_dir, output_path):
    """
    Create an ensemble model from multiple checkpoints.
    
    Args:
        model_dir: Directory containing model checkpoints
        output_path: Path to save the ensemble model
    """
    # Find all .pt files in the directory
    checkpoint_files = list(Path(model_dir).glob('*.pt'))
    
    if len(checkpoint_files) < 2:
        print(f"Not enough checkpoints found in {model_dir} for ensemble creation")
        return None
    
    print(f"Creating ensemble from {len(checkpoint_files)} checkpoints...")
    
    # Load models
    models = [YOLO(ckpt) for ckpt in checkpoint_files]
    
    # Create ensemble (simple averaging of weights)
    ensemble_model = models[0]
    
    # Average the weights
    for key in ensemble_model.model.state_dict():
        # Skip batch norm running stats
        if 'running_mean' in key or 'running_var' in key or 'num_batches_tracked' in key:
            continue
            
        # Average the weights
        ensemble_model.model.state_dict()[key] = sum(model.model.state_dict()[key] 
                                                    for model in models) / len(models)
    
    # Save the ensemble model
    ensemble_model.save(output_path)
    print(f"Ensemble model saved to {output_path}")
    return output_path

def train_extended(
    model_path,
    data_yaml,
    epochs=800,
    batch_size=16,
    img_size=640,
    device='0',
    workers=4,
    patience=100,
    output_dir=None,
    save_period=100
):
    """
    Train a YOLO model on the chess pieces dataset with extended epochs.
    
    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or GPU device id)
        workers: Number of worker threads
        patience: Early stopping patience
        output_dir: Directory to save results
        save_period: Save checkpoints every N epochs
    """
    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join('chess_board_detection/piece_detection/models', f'gpu_run_{timestamp}')
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Print system info
    print(f"Python version: {platform.python_version()}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    print(f"Training on: {device}")
    
    # Optimize GPU settings
    if device != 'cpu':
        optimize_gpu()
    
    # Load the model
    model = YOLO(model_path)
    
    # Train the model with extended configuration
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        imgsz=img_size,
        batch=batch_size,
        patience=patience,
        device=device,
        workers=workers,
        project=output_dir,
        name=f'chess_pieces_{timestamp}',
        exist_ok=True,
        pretrained=True,
        verbose=True,
        seed=42,
        cache=True,
        close_mosaic=epochs-50,  # Disable mosaic for final epochs
        amp=True,  # Enable mixed precision
        # Strong augmentation
        augment=True,
        mosaic=1.0,
        mixup=0.5,
        degrees=15.0,
        translate=0.2,
        scale=0.5,
        shear=2.0,
        fliplr=0.5,
        perspective=0.0005,
        # Save checkpoints periodically
        save_period=save_period
    )
    
    # Create ensemble from saved checkpoints
    checkpoints_dir = os.path.join(output_dir, f'chess_pieces_{timestamp}')
    ensemble_path = os.path.join(output_dir, f'ensemble_model_{timestamp}.pt')
    create_ensemble(checkpoints_dir, ensemble_path)
    
    # Export the model to ONNX format
    model.export(format='onnx', dynamic=True, simplify=True)
    
    print(f"Training complete. Model saved to {output_dir}")
    return results

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description="Train YOLO model on chess pieces dataset using GPU")
    parser.add_argument("--model", type=str, default="yolo11n.pt", help="Path to pre-trained model")
    parser.add_argument("--input_dir", type=str, default="chess_board_detection/piece_detection/dataset/images", 
                        help="Directory containing labeled images")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/piece_detection/dataset_split", 
                        help="Directory to save split dataset")
    parser.add_argument("--train_ratio", type=float, default=0.8, help="Ratio of images for training (default: 0.8)")
    parser.add_argument("--epochs", type=int, default=800, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16, help="Batch size")
    parser.add_argument("--img-size", type=int, default=640, help="Image size for training")
    parser.add_argument("--device", type=str, default="0", help="Device to train on ('cpu' or GPU device id)")
    parser.add_argument("--workers", type=int, default=4, help="Number of worker threads")
    parser.add_argument("--patience", type=int, default=100, help="Early stopping patience")
    parser.add_argument("--model_dir", type=str, default=None, help="Directory to save trained model")
    parser.add_argument("--save_period", type=int, default=100, help="Save checkpoints every N epochs")
    
    args = parser.parse_args()
    
    # Set random seed for reproducibility
    set_seed(42)
    
    # Chess piece class names
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]
    
    # Split dataset
    print(f"Splitting dataset from {args.input_dir} to {args.output_dir}")
    split_dataset(args.input_dir, args.output_dir, args.train_ratio)
    
    # Create dataset YAML
    data_yaml = create_dataset_yaml(args.output_dir, class_names)
    
    # Train model with extended configuration
    print(f"Training model {args.model} on dataset {data_yaml} for {args.epochs} epochs")
    train_extended(
        args.model,
        data_yaml,
        args.epochs,
        args.batch,
        args.img_size,
        args.device,
        args.workers,
        args.patience,
        args.model_dir,
        args.save_period
    )

if __name__ == "__main__":
    main()
