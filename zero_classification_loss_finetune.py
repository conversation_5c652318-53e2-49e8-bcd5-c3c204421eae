"""
Zero Classification Loss Fine-tuning for Chess Piece Detection

This script fine-tunes a YOLO model with human feedback, focusing specifically on
reducing classification loss to zero. It uses a very high weight for classification
loss and implements a feedback loop to iteratively improve the model.
"""

import os
import sys
import json
import argparse
import shutil
import cv2
import numpy as np
import yaml
import torch
import matplotlib.pyplot as plt
from datetime import datetime
from ultralytics import YOLO
from pathlib import Path
from tqdm import tqdm

# Class names for chess pieces
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

# Define colors for visualization (BGR format for OpenCV)
COLORS = {
    'white_pawn': (255, 255, 0),     # <PERSON>an
    'white_knight': (255, 0, 255),   # Magenta
    'white_bishop': (0, 255, 255),   # Yellow
    'white_rook': (0, 0, 255),       # Red
    'white_queen': (255, 0, 0),      # Blue
    'white_king': (0, 255, 0),       # Green
    'black_pawn': (128, 255, 0),     # <PERSON>an
    'black_knight': (255, 128, 255), # Light Magenta
    'black_bishop': (128, 255, 255), # Light Yellow
    'black_rook': (128, 128, 255),   # Light Red
    'black_queen': (255, 128, 128),  # Light Blue
    'black_king': (128, 255, 128),   # Light Green
}

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def collect_feedback(model_path, image_path, db_path, output_dir=None):
    """
    Collect human feedback on model predictions

    Args:
        model_path: Path to the YOLO model
        image_path: Path to the image for feedback
        db_path: Path to the feedback database
        output_dir: Directory to save visualization

    Returns:
        List of feedback items collected
    """
    # Load the feedback database or create a new one
    if os.path.exists(db_path):
        with open(db_path, 'r') as f:
            db = json.load(f)
    else:
        db = {
            "feedback_items": [],
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_items": 0
            }
        }

    # Load the model
    model = YOLO(model_path)

    # Run inference with a lower confidence threshold to catch more potential misclassifications
    results = model.predict(image_path, conf=0.25, verbose=False)

    # Get the original image
    img = cv2.imread(image_path)
    if img is None:
        print(f"Error: Could not read image {image_path}")
        return []

    # Get detection results
    result = results[0]
    boxes = result.boxes.xyxy.cpu().numpy()
    cls_ids = result.boxes.cls.cpu().numpy().astype(int)
    confs = result.boxes.conf.cpu().numpy()

    # Create a copy for visualization
    img_vis = img.copy()

    # Draw bounding boxes with detection numbers
    for i, (box, cls_id, conf) in enumerate(zip(boxes, cls_ids, confs)):
        x1, y1, x2, y2 = box.astype(int)
        color = COLORS[CLASS_NAMES[cls_id]]
        cv2.rectangle(img_vis, (x1, y1), (x2, y2), color, 2)

        # Add detection number (not class name)
        cv2.putText(img_vis, f"{i+1}", (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.9, color, 2)

    # Save visualization if output directory is provided
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
        base_name = os.path.basename(image_path)
        output_path = os.path.join(output_dir, f"detection_{base_name}")
        cv2.imwrite(output_path, img_vis)
        print(f"Visualization saved to {output_path}")

    # Display detections
    print(f"\nDetections for {os.path.basename(image_path)}:")
    for i, (box, cls_id, conf) in enumerate(zip(boxes, cls_ids, confs)):
        print(f"{i+1}. {CLASS_NAMES[cls_id]} (confidence: {conf:.2f})")

    # Collect feedback
    print("\nEnter feedback for incorrect classifications (or press Enter to skip):")
    print("Format: <detection_number> <correct_class_name> (e.g., '3 black_bishop')")
    print("Type 'done' when finished")

    feedback_items = []
    while True:
        feedback = input("> ").strip()
        if feedback.lower() == 'done' or feedback == '':
            break

        try:
            # Parse feedback
            parts = feedback.split(' ', 1)
            if len(parts) != 2:
                print("Invalid format. Please use: <detection_number> <correct_class_name>")
                continue

            idx = int(parts[0]) - 1  # Convert to 0-based index
            correct_class = parts[1].strip()

            # Validate detection index
            if idx < 0 or idx >= len(boxes):
                print(f"Invalid detection number. Please use a number between 1 and {len(boxes)}")
                continue

            # Validate class name
            if correct_class not in CLASS_NAMES:
                print(f"Invalid class name. Please use one of: {', '.join(CLASS_NAMES)}")
                continue

            # Get the detection details
            box = boxes[idx].tolist()
            predicted_class = CLASS_NAMES[cls_ids[idx]]
            confidence = float(confs[idx])

            # Create a feedback item
            feedback_item = {
                "image_path": os.path.abspath(image_path),
                "box": box,
                "predicted_class": predicted_class,
                "correct_class": correct_class,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat()
            }

            feedback_items.append(feedback_item)
            print(f"Feedback recorded: Detection {idx+1} should be {correct_class} (not {predicted_class})")

        except Exception as e:
            print(f"Error processing feedback: {e}")

    # Add feedback items to the database
    if feedback_items:
        db["feedback_items"].extend(feedback_items)
        db["metadata"]["last_updated"] = datetime.now().isoformat()
        db["metadata"]["total_items"] = len(db["feedback_items"])

        with open(db_path, 'w') as f:
            json.dump(db, f, indent=2)

        print(f"\n{len(feedback_items)} feedback items saved to {db_path}")
    else:
        print("\nNo feedback collected.")

    return feedback_items

def prepare_training_data(feedback_db_path, output_dir):
    """
    Prepare training data from feedback database with emphasis on classification

    Args:
        feedback_db_path: Path to the feedback database
        output_dir: Directory to save the training data

    Returns:
        Path to the dataset YAML file
    """
    # Load feedback database
    with open(feedback_db_path, 'r') as f:
        db = json.load(f)

    feedback_items = db["feedback_items"]
    if not feedback_items:
        print("No feedback items found in the database.")
        return None

    # Create directories
    images_dir = os.path.join(output_dir, "images", "train")
    labels_dir = os.path.join(output_dir, "labels", "train")
    val_images_dir = os.path.join(output_dir, "images", "val")
    val_labels_dir = os.path.join(output_dir, "labels", "val")

    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(labels_dir, exist_ok=True)
    os.makedirs(val_images_dir, exist_ok=True)
    os.makedirs(val_labels_dir, exist_ok=True)

    # Track processed images to avoid duplicates
    processed_images = set()

    # Process each feedback item
    for item in feedback_items:
        image_path = item["image_path"]
        if not os.path.exists(image_path):
            print(f"Warning: Image {image_path} not found, skipping")
            continue

        # Get image dimensions
        img = cv2.imread(image_path)
        if img is None:
            print(f"Warning: Could not read image {image_path}, skipping")
            continue

        h, w = img.shape[:2]

        # Generate a unique name for this image
        name_without_ext = os.path.splitext(os.path.basename(image_path))[0]
        if name_without_ext in processed_images:
            # Add a suffix to make it unique
            name_without_ext = f"{name_without_ext}_{len(processed_images)}"

        processed_images.add(name_without_ext)

        # Copy the image to the training directory
        dest_img_path = os.path.join(images_dir, f"{name_without_ext}.jpg")
        shutil.copy(image_path, dest_img_path)

        # Create a label file for this image
        label_path = os.path.join(labels_dir, f"{name_without_ext}.txt")

        # Get the corrected box and class
        box = item["box"]  # [x1, y1, x2, y2]
        correct_class = item["correct_class"]
        class_id = CLASS_NAMES.index(correct_class)

        # Convert box to YOLO format: [class_id, x_center, y_center, width, height]
        # All values normalized to [0, 1]
        x1, y1, x2, y2 = box
        x_center = (x1 + x2) / (2 * w)
        y_center = (y1 + y2) / (2 * h)
        width = (x2 - x1) / w
        height = (y2 - y1) / h

        # Write to label file
        with open(label_path, 'w') as f:
            f.write(f"{class_id} {x_center} {y_center} {width} {height}\n")

    # Create a small validation set (copy a few images from the training set)
    # This is just for monitoring training progress
    train_images = os.listdir(images_dir)
    val_count = min(len(train_images) // 5, 5)  # 20% or at most 5 images

    if val_count > 0:
        for i in range(val_count):
            img_name = train_images[i]
            shutil.copy(os.path.join(images_dir, img_name), os.path.join(val_images_dir, img_name))

            # Copy corresponding label if it exists
            label_name = os.path.splitext(img_name)[0] + '.txt'
            label_path = os.path.join(labels_dir, label_name)
            if os.path.exists(label_path):
                shutil.copy(label_path, os.path.join(val_labels_dir, label_name))

    # Create dataset YAML file
    yaml_path = os.path.join(output_dir, "dataset.yaml")
    dataset_config = {
        "path": os.path.abspath(output_dir),
        "train": "images/train",
        "val": "images/val",
        "nc": len(CLASS_NAMES),
        "names": {i: name for i, name in enumerate(CLASS_NAMES)}
    }

    with open(yaml_path, 'w') as f:
        yaml.dump(dataset_config, f, sort_keys=False)

    print(f"Training data prepared with {len(processed_images)} images")
    print(f"Dataset configuration saved to {yaml_path}")

    return yaml_path

def finetune_zero_classification_loss(
    model_path,
    data_yaml,
    output_dir=None,
    epochs=15,
    batch_size=16,
    img_size=416,
    device='0',
    workers=4,
    patience=15,
    save_period=5,
    conf_threshold=0.001,
    iou_threshold=0.7,
    cls_loss_weight=10.0,  # Initial weight for classification loss
    box_loss_weight=0.5,   # Initial weight for box loss
    dfl_loss_weight=0.5,   # Initial weight for DFL loss
    dynamic_weights=True,  # Whether to dynamically adjust weights
    target_precision=0.99, # Target precision
    target_recall=0.99,    # Target recall
    seed=42
):
    """
    Fine-tune a YOLO model with a focus on zero classification loss.

    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        output_dir: Directory to save results
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or GPU device id)
        workers: Number of worker threads
        patience: Stop training if no improvement for this many epochs
        save_period: Save checkpoints every N epochs
        conf_threshold: Confidence threshold for training
        iou_threshold: IoU threshold for NMS
        cls_loss_weight: Initial weight for classification loss
        box_loss_weight: Initial weight for box loss
        dfl_loss_weight: Initial weight for DFL loss
        dynamic_weights: Whether to dynamically adjust weights based on performance
        target_precision: Target precision value to achieve
        target_recall: Target recall value to achieve
        seed: Random seed for reproducibility
    """
    # Set random seed
    set_seed(seed)

    # Print system information
    print_system_info()
    print(f"Training on: {device}")

    if torch.cuda.is_available():
        device_info = torch.cuda.get_device_properties(0)
        print(f"GPU: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {device_info.total_memory / 1e9:.2f} GB")

    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    run_name = f'zero_cls_loss_{timestamp}'

    # Create output directory
    if output_dir is None:
        output_dir = f"runs/zero_cls_loss"

    run_dir = os.path.join(output_dir, run_name)
    os.makedirs(run_dir, exist_ok=True)

    # Save initial training configuration
    config = {
        "model_path": model_path,
        "data_yaml": data_yaml,
        "epochs": epochs,
        "batch_size": batch_size,
        "img_size": img_size,
        "device": device,
        "workers": workers,
        "patience": patience,
        "save_period": save_period,
        "conf_threshold": conf_threshold,
        "iou_threshold": iou_threshold,
        "initial_cls_loss_weight": cls_loss_weight,
        "initial_box_loss_weight": box_loss_weight,
        "initial_dfl_loss_weight": dfl_loss_weight,
        "dynamic_weights": dynamic_weights,
        "target_precision": target_precision,
        "target_recall": target_recall,
        "seed": seed,
        "timestamp": timestamp
    }

    with open(os.path.join(run_dir, 'config.json'), 'w') as f:
        json.dump(config, f, indent=2)

    # Load model
    model = YOLO(model_path)

    # If dynamic weights is enabled, we'll do multiple training runs with adjusted weights
    if dynamic_weights:
        print("\n=== Dynamic Weight Adjustment Enabled ===")
        print(f"Target Precision: {target_precision}")
        print(f"Target Recall: {target_recall}")

        # Initialize metrics tracking
        best_precision = 0
        best_recall = 0
        best_map50 = 0
        best_model_path = None

        # Track weight adjustments
        weight_history = []

        # Initial weights
        current_cls_weight = cls_loss_weight
        current_box_weight = box_loss_weight
        current_dfl_weight = dfl_loss_weight

        # Maximum number of adjustment iterations
        max_iterations = min(5, epochs // 3)  # At most 5 iterations or 1/3 of total epochs

        for iteration in range(max_iterations):
            print(f"\n=== Weight Adjustment Iteration {iteration+1}/{max_iterations} ===")
            print(f"Classification Weight: {current_cls_weight}")
            print(f"Box Weight: {current_box_weight}")
            print(f"DFL Weight: {current_dfl_weight}")

            # Calculate epochs for this iteration
            iter_epochs = max(3, epochs // max_iterations)

            # Create a subdirectory for this iteration
            iter_run_name = f"{run_name}_iter{iteration+1}"

            # Train with current weights
            iter_results = model.train(
                data=data_yaml,
                epochs=iter_epochs,
                imgsz=img_size,
                batch=batch_size,
                patience=patience,
                device=device,
                workers=workers,
                project=output_dir,
                name=iter_run_name,
                exist_ok=True,
                pretrained=False,  # We're using a pre-trained model
                verbose=True,
                seed=seed,
                cache=True,
                close_mosaic=0,  # Disable mosaic completely for fine-tuning
                amp=True,  # Enable mixed precision
                # Minimal augmentation for fine-tuning
                augment=True,
                mosaic=0.0,  # Disable mosaic
                mixup=0.0,  # Disable mixup
                degrees=0.0,  # Disable rotation
                translate=0.05,  # Minimal translation
                scale=0.1,  # Minimal scaling
                shear=0.0,  # No shear
                fliplr=0.5,  # Keep horizontal flip
                perspective=0.0,  # No perspective
                # Learning rate settings - much lower for fine-tuning
                lr0=0.0001,  # Very low initial learning rate for fine-tuning
                lrf=0.00001,  # Very low final learning rate
                # Save checkpoints
                save_period=save_period,
                # Loss weights - use current weights
                box=current_box_weight,
                cls=current_cls_weight,
                dfl=current_dfl_weight,
                # Validation settings
                val=True,
                # NMS settings
                conf=conf_threshold,
                iou=iou_threshold
            )

            # Validate the model after this iteration
            iter_model_path = os.path.join(output_dir, iter_run_name, 'weights', 'best.pt')
            iter_model = YOLO(iter_model_path)

            # Run validation
            val_results = iter_model.val(data=data_yaml, conf=0.7, iou=0.7)

            # Get metrics
            precision = val_results.box.precision
            recall = val_results.box.recall
            map50 = val_results.box.map50

            # Extract classification loss if available
            cls_loss_value = None
            try:
                train_stats = iter_results.results_dict
                for key in train_stats:
                    if 'loss_cls' in key or 'cls_loss' in key:
                        cls_loss_value = float(train_stats[key][-1])  # Get the last value
                        break
            except:
                cls_loss_value = None

            # Record weights and performance
            history_entry = {
                "iteration": iteration + 1,
                "cls_weight": current_cls_weight,
                "box_weight": current_box_weight,
                "dfl_weight": current_dfl_weight,
                "precision": float(precision),
                "recall": float(recall),
                "map50": float(map50)
            }

            # Add classification loss if available
            if cls_loss_value is not None:
                history_entry["classification_loss"] = cls_loss_value

            weight_history.append(history_entry)

            # Check if we've reached our targets
            if precision >= target_precision and recall >= target_recall:
                print(f"\n🎉 Target metrics achieved! Precision: {precision:.4f}, Recall: {recall:.4f}")
                best_model_path = iter_model_path
                best_precision = precision
                best_recall = recall
                best_map50 = map50
                break

            # Update best metrics if improved
            if map50 > best_map50:
                best_model_path = iter_model_path
                best_precision = precision
                best_recall = recall
                best_map50 = map50

            # Adjust weights based on performance
            precision_gap = target_precision - precision
            recall_gap = target_recall - recall

            # If we're in the last iteration, don't adjust weights
            if iteration == max_iterations - 1:
                break

            # Get the classification loss from the training results
            try:
                # Try to extract the classification loss from training results
                train_stats = iter_results.results_dict
                cls_loss = None

                # Look for classification loss in the results
                for key in train_stats:
                    if 'loss_cls' in key or 'cls_loss' in key:
                        cls_loss = train_stats[key][-1]  # Get the last value
                        break

                print(f"Current classification loss: {cls_loss}")

                # If we couldn't find the classification loss, use the default strategy
                if cls_loss is None:
                    raise ValueError("Classification loss not found in training results")

                # Dynamic weight adjustment strategy focused on classification loss
                # while preserving other metrics
                if cls_loss > 0.1:  # High classification loss
                    # Significant increase in classification weight
                    current_cls_weight = min(current_cls_weight * 3.0, 100.0)
                    # Keep other weights the same to maintain detection performance
                    current_box_weight = current_box_weight
                    current_dfl_weight = current_dfl_weight
                elif cls_loss > 0.05:  # Medium classification loss
                    # Moderate increase in classification weight
                    current_cls_weight = min(current_cls_weight * 2.0, 80.0)
                    # Slight reduction in other weights
                    current_box_weight = max(current_box_weight * 0.9, 0.3)
                    current_dfl_weight = max(current_dfl_weight * 0.9, 0.3)
                elif cls_loss > 0.01:  # Low classification loss
                    # Small increase in classification weight
                    current_cls_weight = min(current_cls_weight * 1.5, 60.0)
                    # Minimal reduction in other weights
                    current_box_weight = max(current_box_weight * 0.95, 0.4)
                    current_dfl_weight = max(current_dfl_weight * 0.95, 0.4)
                else:  # Very low classification loss
                    # Maintain classification weight
                    current_cls_weight = current_cls_weight
                    # Slightly increase other weights to improve detection
                    current_box_weight = min(current_box_weight * 1.05, 1.0)
                    current_dfl_weight = min(current_dfl_weight * 1.05, 1.0)

                # Check if precision or recall has degraded
                if precision < best_precision * 0.98 or recall < best_recall * 0.98:
                    print("Warning: Precision or recall has degraded. Adjusting weights to compensate.")
                    # Increase box and dfl weights to improve detection
                    current_box_weight = min(current_box_weight * 1.2, 1.0)
                    current_dfl_weight = min(current_dfl_weight * 1.2, 1.0)
                    # Still maintain high classification weight
                    current_cls_weight = max(current_cls_weight * 0.95, 10.0)

            except Exception as e:
                print(f"Error extracting classification loss: {e}")
                print("Using fallback weight adjustment strategy based on precision and recall")

                # Fallback strategy based on precision and recall
                if precision_gap > 0.05 or recall_gap > 0.05:
                    # If we're far from targets, increase classification weight significantly
                    current_cls_weight = min(current_cls_weight * 2.0, 50.0)
                    # Keep other weights relatively high to maintain detection performance
                    current_box_weight = max(current_box_weight * 0.7, 0.3)
                    current_dfl_weight = max(current_dfl_weight * 0.7, 0.3)
                elif precision_gap > 0 or recall_gap > 0:
                    # If we're close to targets, make smaller adjustments
                    current_cls_weight = min(current_cls_weight * 1.5, 50.0)
                    current_box_weight = max(current_box_weight * 0.8, 0.4)
                    current_dfl_weight = max(current_dfl_weight * 0.8, 0.4)
                else:
                    # If we've exceeded targets, make minor adjustments to balance
                    current_cls_weight = current_cls_weight * 0.95
                    current_box_weight = min(current_box_weight * 1.1, 1.0)
                    current_dfl_weight = min(current_dfl_weight * 1.1, 1.0)

            print(f"\nAdjusted weights for next iteration:")
            print(f"Classification Weight: {current_cls_weight}")
            print(f"Box Weight: {current_box_weight}")
            print(f"DFL Weight: {current_dfl_weight}")

        # Save weight history
        with open(os.path.join(run_dir, 'weight_history.json'), 'w') as f:
            json.dump(weight_history, f, indent=2)

        # Use the best model for final results
        model = YOLO(best_model_path)

        # Copy the best model to the main run directory
        shutil.copy(best_model_path, os.path.join(run_dir, 'weights', 'best.pt'))

        print(f"\nDynamic weight adjustment complete.")
        print(f"Best Precision: {best_precision:.4f}")
        print(f"Best Recall: {best_recall:.4f}")
        print(f"Best mAP50: {best_map50:.4f}")

        # Return the results object from the best model
        results = None  # Placeholder, will be updated with validation results

    else:
        # Standard training without dynamic weight adjustment
        print("\n=== Standard Training (No Dynamic Weight Adjustment) ===")

        # Train with fixed weights
        results = model.train(
            data=data_yaml,
            epochs=epochs,
            imgsz=img_size,
            batch=batch_size,
            patience=patience,
            device=device,
            workers=workers,
            project=output_dir,
            name=run_name,
            exist_ok=True,
            pretrained=False,  # We're using a pre-trained model
            verbose=True,
            seed=seed,
            cache=True,
            close_mosaic=0,  # Disable mosaic completely for fine-tuning
            amp=True,  # Enable mixed precision
            # Minimal augmentation for fine-tuning
            augment=True,
            mosaic=0.0,  # Disable mosaic
            mixup=0.0,  # Disable mixup
            degrees=0.0,  # Disable rotation
            translate=0.05,  # Minimal translation
            scale=0.1,  # Minimal scaling
            shear=0.0,  # No shear
            fliplr=0.5,  # Keep horizontal flip
            perspective=0.0,  # No perspective
            # Learning rate settings - much lower for fine-tuning
            lr0=0.0001,  # Very low initial learning rate for fine-tuning
            lrf=0.00001,  # Very low final learning rate
            # Save checkpoints
            save_period=save_period,
            # Loss weights - emphasize classification extremely
            box=box_loss_weight,
            cls=cls_loss_weight,  # Extremely high class loss gain
            dfl=dfl_loss_weight,
            # Validation settings
            val=True,
            # NMS settings
            conf=conf_threshold,
            iou=iou_threshold
        )

    # Make sure the weights directory exists
    os.makedirs(os.path.join(run_dir, 'weights'), exist_ok=True)

    # If we used dynamic weights, we already have the best model
    if dynamic_weights and best_model_path:
        # Make sure the best model is copied to the expected location
        final_best_path = os.path.join(run_dir, 'weights', 'best.pt')
        if not os.path.exists(final_best_path):
            shutil.copy(best_model_path, final_best_path)

        final_model = YOLO(final_best_path)

        # We already have the metrics from the best iteration
        precision = best_precision
        recall = best_recall
        map50 = best_map50

        # Run a final validation to get mAP
        print("\nRunning final validation...")
        metrics = final_model.val(data=data_yaml, conf=0.7, iou=0.7)
        map = metrics.box.map

    else:
        # For standard training, get the best model
        best_model_path = os.path.join(run_dir, 'weights', 'best.pt')
        final_model = YOLO(best_model_path)

        # Validate the final model
        print("\nValidating final model...")
        metrics = final_model.val(data=data_yaml, conf=0.7, iou=0.7)

        # Get validation results
        val_results = metrics.box

        # Extract metrics
        precision = val_results.precision
        recall = val_results.recall
        map50 = val_results.map50
        map = val_results.map

    # Print final metrics with color coding
    print("\n" + "="*50)
    print("FINAL MODEL PERFORMANCE:")
    print("="*50)

    # Color coding for terminal output
    GREEN = '\033[92m'  # Green for good metrics
    YELLOW = '\033[93m' # Yellow for acceptable metrics
    RED = '\033[91m'    # Red for poor metrics
    RESET = '\033[0m'   # Reset color

    # Determine color based on target
    precision_color = GREEN if precision >= target_precision else (YELLOW if precision >= target_precision * 0.95 else RED)
    recall_color = GREEN if recall >= target_recall else (YELLOW if recall >= target_recall * 0.95 else RED)
    map50_color = GREEN if map50 >= 0.95 else (YELLOW if map50 >= 0.9 else RED)
    map_color = GREEN if map >= 0.85 else (YELLOW if map >= 0.8 else RED)

    # Try to get classification loss
    cls_loss_color = ""
    cls_loss_display = ""
    if final_cls_loss is not None:
        cls_loss_color = GREEN if final_cls_loss < 0.01 else (YELLOW if final_cls_loss < 0.05 else RED)
        cls_loss_display = f"Cls Loss: {cls_loss_color}{final_cls_loss:.6f}{RESET} {'✓' if final_cls_loss < 0.01 else ''}"

    print(f"mAP50:     {map50_color}{map50:.4f}{RESET} {'✓' if map50 >= 0.95 else ''}")
    print(f"mAP50-95:  {map_color}{map:.4f}{RESET} {'✓' if map >= 0.85 else ''}")
    print(f"Precision: {precision_color}{precision:.4f}{RESET} {'✓' if precision >= target_precision else ''}")
    print(f"Recall:    {recall_color}{recall:.4f}{RESET} {'✓' if recall >= target_recall else ''}")

    # Print classification loss if available
    if cls_loss_display:
        print(cls_loss_display)

    print("="*50)

    # Check if we achieved perfect classification
    perfect_classification = precision >= target_precision and recall >= target_recall
    if perfect_classification:
        print(f"\n{GREEN}🎉 Perfect classification achieved!{RESET}")
        print(f"Precision: {precision:.4f} ≥ {target_precision} ✓")
        print(f"Recall: {recall:.4f} ≥ {target_recall} ✓")
    else:
        print(f"\n{YELLOW}Classification still needs improvement.{RESET}")
        if precision < target_precision:
            print(f"Precision: {precision:.4f} < {target_precision} ✗")
        if recall < target_recall:
            print(f"Recall: {recall:.4f} < {target_recall} ✗")

    # Try to extract final classification loss
    final_cls_loss = None
    try:
        if dynamic_weights and len(weight_history) > 0:
            # Get the classification loss from the last iteration if available
            last_entry = weight_history[-1]
            if "classification_loss" in last_entry:
                final_cls_loss = last_entry["classification_loss"]
    except:
        pass

    # Save metrics to file
    metrics_dict = {
        "map50": float(map50),
        "map": float(map),
        "precision": float(precision),
        "recall": float(recall),
        "perfect_classification": perfect_classification,
        "target_precision": target_precision,
        "target_recall": target_recall,
        "dynamic_weights_used": dynamic_weights,
        "training_completed": True
    }

    # Add classification loss if available
    if final_cls_loss is not None:
        metrics_dict["classification_loss"] = final_cls_loss

        # Update perfect classification flag based on classification loss
        if final_cls_loss < 0.01:  # Very low classification loss
            metrics_dict["zero_classification_loss_achieved"] = True
            print(f"\n🎉 Classification loss near zero achieved! Loss: {final_cls_loss:.6f}")
        else:
            metrics_dict["zero_classification_loss_achieved"] = False

    # Save metrics to the appropriate directory
    metrics_path = os.path.join(run_dir, 'final_metrics.json')
    with open(metrics_path, 'w') as f:
        json.dump(metrics_dict, f, indent=2)

    # Create performance visualizations
    try:
        # Create a bar chart of detection metrics
        plt.figure(figsize=(10, 6))
        metrics_values = [float(precision), float(recall), float(map50), float(map)]
        metrics_names = ['Precision', 'Recall', 'mAP50', 'mAP50-95']
        target_values = [target_precision, target_recall, 0.95, 0.85]  # Targets for each metric

        bars = plt.bar(metrics_names, metrics_values, color=['skyblue', 'lightgreen', 'salmon', 'purple'])

        # Add target lines
        for i, target in enumerate(target_values):
            plt.plot([i-0.4, i+0.4], [target, target], 'r--', linewidth=1)
            plt.text(i, target + 0.01, f'Target: {target:.2f}', ha='center', fontsize=8)

        # Add value labels on top of bars
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{height:.4f}', ha='center', fontsize=9)

        plt.ylim(0, 1.1)
        plt.title('Detection Performance Metrics')
        plt.tight_layout()

        # Save the figure
        plt.savefig(os.path.join(run_dir, 'detection_metrics.png'))
        plt.close()

        # If we have weight history with classification loss, create a line chart
        if dynamic_weights and len(weight_history) > 0 and any("classification_loss" in entry for entry in weight_history):
            plt.figure(figsize=(12, 8))

            # Extract data for plotting
            iterations = [entry["iteration"] for entry in weight_history]
            cls_weights = [entry["cls_weight"] for entry in weight_history]
            box_weights = [entry["box_weight"] for entry in weight_history]
            dfl_weights = [entry["dfl_weight"] for entry in weight_history]
            precisions = [entry["precision"] for entry in weight_history]
            recalls = [entry["recall"] for entry in weight_history]
            cls_losses = [entry.get("classification_loss", None) for entry in weight_history]
            cls_losses = [loss for loss in cls_losses if loss is not None]  # Filter out None values

            # Create a figure with multiple subplots
            fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15), sharex=True)

            # Plot classification loss
            if cls_losses:
                iterations_with_loss = iterations[:len(cls_losses)]
                ax1.plot(iterations_with_loss, cls_losses, 'r-o', linewidth=2, label='Classification Loss')
                ax1.set_ylabel('Loss Value')
                ax1.set_title('Classification Loss Over Iterations')
                ax1.grid(True, linestyle='--', alpha=0.7)

                # Add target line for classification loss
                ax1.axhline(y=0.01, color='g', linestyle='--', label='Target Loss (0.01)')
                ax1.legend()

                # Add value labels
                for i, loss in enumerate(cls_losses):
                    ax1.annotate(f'{loss:.4f}',
                                xy=(iterations_with_loss[i], loss),
                                xytext=(0, 10),
                                textcoords='offset points',
                                ha='center',
                                fontsize=8)

            # Plot weights
            ax2.plot(iterations, cls_weights, 'b-o', linewidth=2, label='Classification Weight')
            ax2.plot(iterations, box_weights, 'g-o', linewidth=2, label='Box Weight')
            ax2.plot(iterations, dfl_weights, 'y-o', linewidth=2, label='DFL Weight')
            ax2.set_ylabel('Weight Value')
            ax2.set_title('Loss Weights Over Iterations')
            ax2.grid(True, linestyle='--', alpha=0.7)
            ax2.legend()

            # Plot precision and recall
            ax3.plot(iterations, precisions, 'm-o', linewidth=2, label='Precision')
            ax3.plot(iterations, recalls, 'c-o', linewidth=2, label='Recall')
            ax3.set_ylabel('Metric Value')
            ax3.set_title('Precision and Recall Over Iterations')
            ax3.grid(True, linestyle='--', alpha=0.7)

            # Add target lines for precision and recall
            ax3.axhline(y=target_precision, color='m', linestyle='--', label=f'Target Precision ({target_precision})')
            ax3.axhline(y=target_recall, color='c', linestyle='--', label=f'Target Recall ({target_recall})')
            ax3.legend()

            # Add value labels
            for i, (prec, rec) in enumerate(zip(precisions, recalls)):
                ax3.annotate(f'{prec:.3f}',
                            xy=(iterations[i], prec),
                            xytext=(0, 10),
                            textcoords='offset points',
                            ha='center',
                            fontsize=8)
                ax3.annotate(f'{rec:.3f}',
                            xy=(iterations[i], rec),
                            xytext=(0, -15),
                            textcoords='offset points',
                            ha='center',
                            fontsize=8)

            # Set common x-axis label
            ax3.set_xlabel('Iteration')

            # Adjust layout
            plt.tight_layout()

            # Save the figure
            plt.savefig(os.path.join(run_dir, 'training_progress.png'))
            plt.close()

    except Exception as e:
        print(f"Warning: Could not create performance visualization: {e}")

    print(f"\nTraining complete. Final model saved to {os.path.join(run_dir, 'weights', 'best.pt')}")
    return os.path.join(run_dir, 'weights', 'best.pt'), metrics_dict

def collect_automated_feedback(
    model_path,
    images_dir,
    labels_dir,
    feedback_db,
    output_dir=None,
    distance_threshold=50,  # Maximum distance in pixels for matching
    use_center_distance=True  # Whether to use center distance instead of corner distance
):
    """
    Automatically collect feedback by comparing model predictions with existing labels

    Args:
        model_path: Path to the YOLO model
        images_dir: Directory containing images
        labels_dir: Directory containing corresponding labels in YOLO format
        feedback_db: Path to the feedback database
        output_dir: Directory to save visualizations
        distance_threshold: Maximum distance in pixels for matching boxes
        use_center_distance: Whether to use center distance instead of corner distance

    Returns:
        Number of feedback items collected
    """
    # Load the feedback database or create a new one
    if os.path.exists(feedback_db):
        with open(feedback_db, 'r') as f:
            db = json.load(f)
    else:
        db = {
            "feedback_items": [],
            "metadata": {
                "created_at": datetime.now().isoformat(),
                "last_updated": datetime.now().isoformat(),
                "total_items": 0
            }
        }

    # Load the model
    model = YOLO(model_path)

    # Get all image files
    image_files = [f for f in os.listdir(images_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

    if not image_files:
        print(f"No images found in {images_dir}")
        return 0

    # Create output directory if needed
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)

    feedback_items_count = 0

    # Process each image
    for img_file in image_files:
        img_path = os.path.join(images_dir, img_file)

        # Get corresponding label file
        label_file = os.path.splitext(img_file)[0] + '.txt'
        label_path = os.path.join(labels_dir, label_file)

        if not os.path.exists(label_path):
            print(f"No label file found for {img_file}, skipping")
            continue

        # Read the image to get dimensions
        img = cv2.imread(img_path)
        if img is None:
            print(f"Could not read image {img_path}, skipping")
            continue

        h, w = img.shape[:2]

        # Read ground truth labels
        gt_boxes = []
        gt_classes = []

        with open(label_path, 'r') as f:
            for line in f:
                parts = line.strip().split()
                if len(parts) >= 5:
                    cls_id = int(parts[0])
                    # YOLO format: [class_id, x_center, y_center, width, height]
                    x_center = float(parts[1]) * w
                    y_center = float(parts[2]) * h
                    width = float(parts[3]) * w
                    height = float(parts[4]) * h

                    # Convert to [x1, y1, x2, y2] format
                    x1 = x_center - width / 2
                    y1 = y_center - height / 2
                    x2 = x_center + width / 2
                    y2 = y_center + height / 2

                    gt_boxes.append([x1, y1, x2, y2])
                    gt_classes.append(cls_id)

        # Run model inference
        results = model.predict(img_path, conf=0.25, verbose=False)
        result = results[0]

        pred_boxes = result.boxes.xyxy.cpu().numpy()
        pred_cls_ids = result.boxes.cls.cpu().numpy().astype(int)
        pred_confs = result.boxes.conf.cpu().numpy()

        # Create a copy for visualization
        img_vis = img.copy()

        # Match predictions with ground truth and collect feedback
        feedback_items = []

        for i, (pred_box, pred_cls, pred_conf) in enumerate(zip(pred_boxes, pred_cls_ids, pred_confs)):
            # Calculate center or corners of prediction box
            if use_center_distance:
                # Use center point
                pred_center_x = (pred_box[0] + pred_box[2]) / 2
                pred_center_y = (pred_box[1] + pred_box[3]) / 2
                pred_point = (pred_center_x, pred_center_y)
            else:
                # Use all four corners and find the best match
                pred_corners = [
                    (pred_box[0], pred_box[1]),  # top-left
                    (pred_box[2], pred_box[1]),  # top-right
                    (pred_box[0], pred_box[3]),  # bottom-left
                    (pred_box[2], pred_box[3])   # bottom-right
                ]

            # Find the nearest ground truth box
            best_distance = float('inf')
            best_gt_idx = -1

            for j, gt_box in enumerate(gt_boxes):
                if use_center_distance:
                    # Calculate center of ground truth box
                    gt_center_x = (gt_box[0] + gt_box[2]) / 2
                    gt_center_y = (gt_box[1] + gt_box[3]) / 2
                    gt_point = (gt_center_x, gt_center_y)

                    # Calculate Euclidean distance between centers
                    distance = calculate_distance(pred_point, gt_point)

                    if distance < best_distance:
                        best_distance = distance
                        best_gt_idx = j
                else:
                    # Calculate corners of ground truth box
                    gt_corners = [
                        (gt_box[0], gt_box[1]),  # top-left
                        (gt_box[2], gt_box[1]),  # top-right
                        (gt_box[0], gt_box[3]),  # bottom-left
                        (gt_box[2], gt_box[3])   # bottom-right
                    ]

                    # Find minimum distance between any pair of corners
                    min_corner_distance = float('inf')
                    for pred_corner in pred_corners:
                        for gt_corner in gt_corners:
                            distance = calculate_distance(pred_corner, gt_corner)
                            min_corner_distance = min(min_corner_distance, distance)

                    if min_corner_distance < best_distance:
                        best_distance = min_corner_distance
                        best_gt_idx = j

            # If we found a nearby ground truth box within the distance threshold
            if best_gt_idx >= 0 and best_distance <= distance_threshold:
                gt_cls = gt_classes[best_gt_idx]

                # If the predicted class is different from the ground truth class
                if pred_cls != gt_cls:
                    # Create a feedback item
                    feedback_item = {
                        "image_path": os.path.abspath(img_path),
                        "box": pred_box.tolist(),
                        "predicted_class": CLASS_NAMES[pred_cls],
                        "correct_class": CLASS_NAMES[gt_cls],
                        "confidence": float(pred_conf),
                        "distance": float(best_distance),
                        "timestamp": datetime.now().isoformat()
                    }

                    feedback_items.append(feedback_item)
                    feedback_items_count += 1

                    match_type = "center" if use_center_distance else "corner"
                    print(f"Found misclassification in {img_file}: {CLASS_NAMES[pred_cls]} should be {CLASS_NAMES[gt_cls]} ({match_type} distance: {best_distance:.2f}px)")

                    # Draw on visualization
                    x1, y1, x2, y2 = pred_box.astype(int)
                    cv2.rectangle(img_vis, (x1, y1), (x2, y2), (0, 0, 255), 2)  # Red for misclassification
                    cv2.putText(img_vis, f"{CLASS_NAMES[pred_cls]}->{CLASS_NAMES[gt_cls]}",
                               (x1, y1 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 2)

                    # Draw the matching ground truth box
                    gt_x1, gt_y1, gt_x2, gt_y2 = gt_boxes[best_gt_idx].astype(int)
                    cv2.rectangle(img_vis, (gt_x1, gt_y1), (gt_x2, gt_y2), (0, 255, 0), 2)  # Green for ground truth

                    # Draw a line connecting the centers or nearest corners
                    if use_center_distance:
                        pred_center = (int((x1 + x2) / 2), int((y1 + y2) / 2))
                        gt_center = (int((gt_x1 + gt_x2) / 2), int((gt_y1 + gt_y2) / 2))
                        cv2.line(img_vis, pred_center, gt_center, (255, 0, 255), 2)  # Magenta line
                    else:
                        # This is simplified - in reality we'd need to find which corners were closest
                        pred_center = (int((x1 + x2) / 2), int((y1 + y2) / 2))
                        gt_center = (int((gt_x1 + gt_x2) / 2), int((gt_y1 + gt_y2) / 2))
                        cv2.line(img_vis, pred_center, gt_center, (255, 0, 255), 2)  # Magenta line

        # Save visualization if we found any misclassifications and output_dir is provided
        if feedback_items and output_dir:
            output_path = os.path.join(output_dir, f"auto_feedback_{img_file}")
            cv2.imwrite(output_path, img_vis)

        # Add feedback items to the database
        if feedback_items:
            db["feedback_items"].extend(feedback_items)
            db["metadata"]["last_updated"] = datetime.now().isoformat()
            db["metadata"]["total_items"] = len(db["feedback_items"])

    # Save the updated database
    if feedback_items_count > 0:
        with open(feedback_db, 'w') as f:
            json.dump(db, f, indent=2)

        print(f"\nCollected {feedback_items_count} automated feedback items")
    else:
        print("\nNo misclassifications found")

    return feedback_items_count

def calculate_distance(point1, point2):
    """
    Calculate Euclidean distance between two points

    Args:
        point1: First point as (x, y)
        point2: Second point as (x, y)

    Returns:
        Euclidean distance
    """
    return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)

def run_feedback_loop(
    model_path,
    test_images_dir,
    output_dir="runs/zero_cls_loss",
    feedback_db="feedback_database.json",
    training_data_dir=None,
    epochs=15,
    batch_size=16,
    min_feedback=3,
    cls_loss_weight=10.0,
    automated=False,
    dynamic_weights=False,
    target_precision=0.99,
    target_recall=0.99
):
    """
    Run a complete feedback loop for fine-tuning with zero classification loss

    Args:
        model_path: Path to the base model
        test_images_dir: Directory with test images
        output_dir: Directory to save results
        feedback_db: Path to the feedback database
        training_data_dir: Directory with existing training data (if provided)
        epochs: Number of training epochs
        batch_size: Batch size
        min_feedback: Minimum number of feedback items before fine-tuning
        cls_loss_weight: Initial weight for classification loss
        automated: Whether to use automated feedback from existing labels
        dynamic_weights: Whether to dynamically adjust weights based on performance
        target_precision: Target precision to achieve
        target_recall: Target recall to achieve
    """
    # Create output directories
    feedback_output_dir = os.path.join(output_dir, "feedback_visualizations")
    os.makedirs(feedback_output_dir, exist_ok=True)

    # If training_data_dir is provided and automated is True, use it for automated feedback
    if automated and training_data_dir and os.path.exists(training_data_dir):
        # Check if the directory structure is flat or has train/val subdirectories
        images_dir = os.path.join(training_data_dir, "images")
        labels_dir = os.path.join(training_data_dir, "labels")

        # If images_dir doesn't exist directly, it might be in a train subdirectory
        if not os.path.exists(images_dir):
            # Try with train subdirectory
            train_images_dir = os.path.join(training_data_dir, "images", "train")
            if os.path.exists(train_images_dir):
                images_dir = train_images_dir
            else:
                print(f"Error: Could not find images directory in {training_data_dir}")
                print(f"Tried: {images_dir} and {train_images_dir}")
                return

        # If labels_dir doesn't exist directly, it might be in a train subdirectory
        if not os.path.exists(labels_dir):
            # Try with train subdirectory
            train_labels_dir = os.path.join(training_data_dir, "labels", "train")
            if os.path.exists(train_labels_dir):
                labels_dir = train_labels_dir
            else:
                print(f"Error: Could not find labels directory in {training_data_dir}")
                print(f"Tried: {labels_dir} and {train_labels_dir}")
                return

        print(f"Using automated feedback from {training_data_dir}")
        print(f"Images directory: {images_dir}")
        print(f"Labels directory: {labels_dir}")

        # Collect automated feedback
        feedback_count = collect_automated_feedback(
            model_path,
            images_dir,
            labels_dir,
            feedback_db,
            feedback_output_dir,
            distance_threshold=50,  # 50 pixels is a reasonable default
            use_center_distance=False  # Use corner distance as requested
        )

        if feedback_count < min_feedback:
            print(f"Not enough feedback collected ({feedback_count}/{min_feedback}).")
            print("Proceeding with direct fine-tuning using the existing dataset instead.")

            # Create dataset.yaml for direct fine-tuning
            yaml_path = os.path.join(training_data_dir, "dataset.yaml")

            # Determine the correct paths for images and labels
            images_rel_path = os.path.relpath(images_dir, training_data_dir)
            labels_rel_path = os.path.relpath(labels_dir, training_data_dir)

            # Get parent directory of images_rel_path
            images_parent = os.path.dirname(images_rel_path) if os.path.dirname(images_rel_path) else images_rel_path

            # Create dataset YAML file
            dataset_config = {
                "path": os.path.abspath(training_data_dir),
                "train": images_rel_path,
                "val": images_rel_path,  # Use same data for validation
                "nc": len(CLASS_NAMES),
                "names": {i: name for i, name in enumerate(CLASS_NAMES)}
            }

            with open(yaml_path, 'w') as f:
                yaml.dump(dataset_config, f, sort_keys=False)

            print(f"Created dataset YAML file at {yaml_path}")
            print(f"Using existing training data for fine-tuning")

            # Fine-tune the model directly with existing data
            best_model_path, metrics = finetune_zero_classification_loss(
                model_path,
                yaml_path,
                output_dir=output_dir,
                epochs=epochs,
                batch_size=batch_size,
                cls_loss_weight=cls_loss_weight,
                dynamic_weights=dynamic_weights,
                target_precision=target_precision,
                target_recall=target_recall
            )

            print(f"\nFine-tuning completed with existing data. Final model: {best_model_path}")
            return best_model_path

        # Prepare training data from feedback
        feedback_training_dir = os.path.join(output_dir, "feedback_training_data")
        os.makedirs(feedback_training_dir, exist_ok=True)

        yaml_path = prepare_training_data(feedback_db, feedback_training_dir)
        if not yaml_path:
            print("Failed to prepare training data")
            return

        # Fine-tune the model
        best_model_path, metrics = finetune_zero_classification_loss(
            model_path,
            yaml_path,
            output_dir=output_dir,
            epochs=epochs,
            batch_size=batch_size,
            cls_loss_weight=cls_loss_weight
        )

        print(f"\nFine-tuning completed with automated feedback. Final model: {best_model_path}")
        return best_model_path

    # If training_data_dir is provided but not automated, use it directly for fine-tuning
    elif training_data_dir and os.path.exists(training_data_dir):
        # Check if dataset.yaml exists, if not create it
        yaml_path = os.path.join(training_data_dir, "dataset.yaml")
        if not os.path.exists(yaml_path):
            # Create dataset YAML file
            dataset_config = {
                "path": os.path.abspath(training_data_dir),
                "train": "images",
                "val": "images",
                "nc": len(CLASS_NAMES),
                "names": {i: name for i, name in enumerate(CLASS_NAMES)}
            }

            with open(yaml_path, 'w') as f:
                yaml.dump(dataset_config, f, sort_keys=False)

            print(f"Created dataset YAML file at {yaml_path}")

        print(f"Using existing training data from {training_data_dir}")

        # Fine-tune the model directly with existing data
        best_model_path, metrics = finetune_zero_classification_loss(
            model_path,
            yaml_path,
            output_dir=output_dir,
            epochs=epochs,
            batch_size=batch_size,
            cls_loss_weight=cls_loss_weight,
            dynamic_weights=dynamic_weights,
            target_precision=target_precision,
            target_recall=target_recall
        )

        print(f"\nFine-tuning completed with existing data. Final model: {best_model_path}")
        return best_model_path

    # If no training_data_dir provided or not automated, use manual feedback loop
    # Get all images in the test directory
    test_images = [os.path.join(test_images_dir, f) for f in os.listdir(test_images_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]

    if not test_images:
        print(f"No test images found in {test_images_dir}")
        return

    feedback_training_dir = os.path.join(output_dir, "feedback_training_data")
    os.makedirs(feedback_training_dir, exist_ok=True)

    # Current model to use
    current_model = model_path

    # Process each image and collect feedback
    total_feedback_items = 0

    for image_path in test_images:
        print(f"\nProcessing image: {os.path.basename(image_path)}")

        # Collect feedback for this image
        feedback_items = collect_feedback(
            current_model,
            image_path,
            feedback_db,
            feedback_output_dir
        )

        total_feedback_items += len(feedback_items)

        # Check if we have enough feedback to fine-tune
        if total_feedback_items >= min_feedback:
            print(f"\nCollected {total_feedback_items} feedback items, starting fine-tuning...")

            # Prepare training data
            yaml_path = prepare_training_data(feedback_db, feedback_training_dir)
            if not yaml_path:
                print("Failed to prepare training data")
                continue

            # Fine-tune the model
            best_model_path, metrics = finetune_zero_classification_loss(
                current_model,
                yaml_path,
                output_dir=output_dir,
                epochs=epochs,
                batch_size=batch_size,
                cls_loss_weight=cls_loss_weight,
                dynamic_weights=dynamic_weights,
                target_precision=target_precision,
                target_recall=target_recall
            )

            # Update current model
            current_model = best_model_path

            # Reset feedback count if we achieved perfect classification
            if metrics.get("perfect_classification", False):
                print("\n🎉 Perfect classification achieved! Resetting feedback database.")
                # Create a backup of the old database
                backup_path = f"{feedback_db}.bak.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy(feedback_db, backup_path)

                # Create a new empty database
                new_db = {
                    "feedback_items": [],
                    "metadata": {
                        "created_at": datetime.now().isoformat(),
                        "last_updated": datetime.now().isoformat(),
                        "total_items": 0
                    }
                }
                with open(feedback_db, 'w') as f:
                    json.dump(new_db, f, indent=2)

                total_feedback_items = 0

    print(f"\nFeedback loop completed. Final model: {current_model}")
    return current_model

def main():
    """Main function for command-line interface"""
    parser = argparse.ArgumentParser(description="Fine-tune chess piece detection model with zero classification loss")

    # Create subparsers for different modes
    subparsers = parser.add_subparsers(dest="mode", help="Operation mode")

    # Feedback collection mode
    feedback_parser = subparsers.add_parser("feedback", help="Collect feedback on a single image")
    feedback_parser.add_argument("--model", required=True, help="Path to the YOLO model")
    feedback_parser.add_argument("--image", required=True, help="Path to the image to analyze")
    feedback_parser.add_argument("--db", default="feedback_database.json", help="Path to the feedback database")
    feedback_parser.add_argument("--output", default="feedback_output", help="Directory to save output images")

    # Fine-tuning mode
    finetune_parser = subparsers.add_parser("finetune", help="Fine-tune model with collected feedback")
    finetune_parser.add_argument("--model", required=True, help="Path to the base YOLO model")
    finetune_parser.add_argument("--feedback", required=True, help="Path to the feedback database")
    finetune_parser.add_argument("--epochs", type=int, default=15, help="Number of training epochs")
    finetune_parser.add_argument("--batch", type=int, default=16, help="Batch size")
    finetune_parser.add_argument("--data-dir", default="feedback_training_data", help="Directory to store training data")
    finetune_parser.add_argument("--output-dir", default="runs/zero_cls_loss", help="Directory to save training results")
    finetune_parser.add_argument("--cls-weight", type=float, default=10.0, help="Weight for classification loss")

    # Feedback loop mode
    loop_parser = subparsers.add_parser("loop", help="Run complete feedback loop")
    loop_parser.add_argument("--model", required=True, help="Path to the base YOLO model")
    loop_parser.add_argument("--images", required=True, help="Directory with test images")
    loop_parser.add_argument("--training-data", help="Directory with existing training data (optional)")
    loop_parser.add_argument("--automated", action="store_true", help="Use automated feedback from existing labels")
    loop_parser.add_argument("--epochs", type=int, default=15, help="Number of training epochs")
    loop_parser.add_argument("--batch", type=int, default=16, help="Batch size")
    loop_parser.add_argument("--min-feedback", type=int, default=3, help="Minimum feedback items before fine-tuning")
    loop_parser.add_argument("--output-dir", default="runs/zero_cls_loss", help="Directory to save results")
    loop_parser.add_argument("--db", default="feedback_database.json", help="Path to the feedback database")
    loop_parser.add_argument("--cls-weight", type=float, default=10.0, help="Initial weight for classification loss")
    loop_parser.add_argument("--dynamic-weights", action="store_true", help="Dynamically adjust weights based on performance")
    loop_parser.add_argument("--target-precision", type=float, default=0.99, help="Target precision to achieve")
    loop_parser.add_argument("--target-recall", type=float, default=0.99, help="Target recall to achieve")

    args = parser.parse_args()

    # Execute the appropriate mode
    if args.mode == "feedback":
        collect_feedback(args.model, args.image, args.db, args.output)

    elif args.mode == "finetune":
        # Prepare training data
        yaml_path = prepare_training_data(args.feedback, args.data_dir)
        if not yaml_path:
            return

        # Fine-tune the model
        best_model_path, _ = finetune_zero_classification_loss(
            args.model,
            yaml_path,
            args.output_dir,
            args.epochs,
            args.batch,
            cls_loss_weight=args.cls_weight
        )

        print(f"\nFine-tuning complete. New model saved to {best_model_path}")

    elif args.mode == "loop":
        # Run the complete feedback loop
        final_model = run_feedback_loop(
            model_path=args.model,
            test_images_dir=args.images,
            output_dir=args.output_dir,
            feedback_db=args.db,
            training_data_dir=args.training_data,
            epochs=args.epochs,
            batch_size=args.batch,
            min_feedback=args.min_feedback,
            cls_loss_weight=args.cls_weight,
            automated=args.automated,
            dynamic_weights=args.dynamic_weights,
            target_precision=args.target_precision,
            target_recall=args.target_recall
        )

        print(f"\nFeedback loop complete. Final model saved to {final_model}")

    else:
        parser.print_help()

if __name__ == "__main__":
    main()
