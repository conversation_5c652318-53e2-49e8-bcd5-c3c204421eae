"""
Test script to evaluate the improved corner detection.
"""

import os
import argparse
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm

from models.unet import ChessBoardUNet
from inference_real import detect_chessboard
from utils.real_dataset import ChessBoardDataset
from config import DATA_DIR, MODELS_DIR, DEVICE, INPUT_SIZE


def calculate_corner_error(predicted_corners, ground_truth_corners, image_shape):
    """
    Calculate the error between predicted and ground truth corners.
    
    Args:
        predicted_corners (list): List of (x, y) predicted corner coordinates.
        ground_truth_corners (list): List of (x, y) ground truth corner coordinates.
        image_shape (tuple): (height, width) of the image.
    
    Returns:
        float: Average error as a percentage of image diagonal.
    """
    # Convert to numpy arrays
    pred = np.array(predicted_corners)
    gt = np.array(ground_truth_corners).reshape(-1, 2)
    
    # Calculate distances
    distances = np.sqrt(np.sum((pred - gt)**2, axis=1))
    
    # Calculate image diagonal
    diagonal = np.sqrt(image_shape[0]**2 + image_shape[1]**2)
    
    # Calculate error as percentage of diagonal
    error_percentage = (distances / diagonal) * 100
    
    return np.mean(error_percentage)


def evaluate_model(model_path, data_dir, output_dir=None, num_samples=None):
    """
    Evaluate the model on the test set.
    
    Args:
        model_path (str): Path to the model checkpoint.
        data_dir (str): Path to the data directory.
        output_dir (str, optional): Directory to save visualizations.
        num_samples (int, optional): Number of samples to evaluate.
    
    Returns:
        dict: Evaluation results.
    """
    # Load model
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    model.load_state_dict(torch.load(model_path, map_location=DEVICE))
    model = model.to(DEVICE)
    model.eval()
    
    # Create dataset
    dataset = ChessBoardDataset(data_dir, split='test')
    
    # Limit number of samples if specified
    if num_samples is not None:
        indices = np.random.choice(len(dataset), min(num_samples, len(dataset)), replace=False)
    else:
        indices = range(len(dataset))
    
    # Create output directory if needed
    if output_dir is not None:
        os.makedirs(output_dir, exist_ok=True)
    
    # Evaluate
    errors = []
    
    for i in tqdm(indices, desc="Evaluating"):
        sample = dataset[i]
        image_path = os.path.join(data_dir, sample['filename'])
        
        # Get ground truth corners
        gt_corners = sample['corners'].numpy().reshape(-1, 2)
        
        # Run inference
        output_path = os.path.join(output_dir, f"result_{i}.png") if output_dir else None
        results = detect_chessboard(model, image_path, output_path)
        
        # Calculate error
        error = calculate_corner_error(results['corners'], gt_corners, 
                                      (dataset.annotations[i]['image_size'][1], 
                                       dataset.annotations[i]['image_size'][0]))
        errors.append(error)
        
        # Print results
        print(f"Sample {i}, Error: {error:.2f}%")
        
        # Print corner coordinates
        print("Predicted corners:")
        for j, (x, y) in enumerate(results['corners']):
            print(f"  Corner {j+1}: ({x:.1f}, {y:.1f})")
        
        print("Ground truth corners:")
        for j, (x, y) in enumerate(gt_corners):
            print(f"  Corner {j+1}: ({x:.1f}, {y:.1f})")
        
        print()
    
    # Calculate average error
    avg_error = np.mean(errors)
    print(f"Average error: {avg_error:.2f}%")
    
    return {
        'average_error': avg_error,
        'errors': errors
    }


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Test Chess Board Corner Detection')
    parser.add_argument('--model', type=str, default='improved_model.pth',
                        help='Path to model checkpoint')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR,
                        help='Path to data directory')
    parser.add_argument('--output_dir', type=str, default='test_results',
                        help='Directory to save visualizations')
    parser.add_argument('--num_samples', type=int, default=None,
                        help='Number of samples to evaluate')
    args = parser.parse_args()
    
    # Resolve model path
    model_path = args.model
    if not os.path.isabs(model_path):
        model_path = os.path.join(MODELS_DIR, model_path)
    
    # Evaluate model
    results = evaluate_model(model_path, args.data_dir, args.output_dir, args.num_samples)
    
    # Save results
    import json
    results_path = os.path.join(args.output_dir, 'evaluation_results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f)
    
    print(f"Results saved to {results_path}")


if __name__ == "__main__":
    main()
