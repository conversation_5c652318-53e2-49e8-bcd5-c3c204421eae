# Chess Board Detection Model Deployment Guide

This guide provides instructions for deploying the chess board detection model in various environments.

## Table of Contents

1. [Model Formats](#model-formats)
2. [Deployment Options](#deployment-options)
3. [Performance Optimization](#performance-optimization)
4. [Deployment Steps](#deployment-steps)
5. [Integration Examples](#integration-examples)
6. [Troubleshooting](#troubleshooting)

## Model Formats

The chess board detection model can be exported to several formats for deployment:

### PyTorch Format (.pth)
- Standard PyTorch model format
- Best for Python-based deployments
- Requires PyTorch runtime

### Quantized PyTorch Format (.pth)
- INT8 quantized model for faster inference
- Smaller size (typically 4x smaller)
- Slightly reduced accuracy
- Requires PyTorch runtime

### ONNX Format (.onnx)
- Cross-platform format supported by many frameworks
- Can be deployed on various hardware (CPU, GPU, specialized accelerators)
- Supported by many deployment frameworks (TensorRT, OpenVINO, etc.)
- No PyTorch dependency required

### TorchScript Format (.pt)
- Optimized for mobile deployment
- Can be used with PyTorch Mobile on Android and iOS
- Smaller size and faster inference than standard PyTorch models

## Deployment Options

### Python Application
- Use the standard PyTorch model or quantized model
- Best for desktop applications or server deployments
- Highest flexibility and easiest integration

### Web Application
- Use ONNX format with ONNX Runtime Web
- Deploy model in browser using JavaScript
- No server-side inference required

### Mobile Application
- Use TorchScript format with PyTorch Mobile
- Deploy on Android or iOS devices
- Optimize for on-device inference

### Edge Devices
- Use ONNX format with appropriate runtime
- Consider quantized models for better performance
- Test on target hardware for optimal configuration

## Performance Optimization

### Model Quantization
Quantize the model to reduce size and improve inference speed:

```bash
python quantize_model.py --checkpoint path/to/model.pth --backend fbgemm
```

Options:
- `--backend fbgemm`: For x86 CPUs (desktop/server)
- `--backend qnnpack`: For ARM CPUs (mobile/edge)

### ONNX Export
Export the model to ONNX format for cross-platform deployment:

```bash
python export_model_onnx.py --checkpoint path/to/model.pth --dynamic_batch --dynamic_size
```

Options:
- `--dynamic_batch`: Allow variable batch sizes
- `--dynamic_size`: Allow variable input image sizes

### Mobile Optimization
Optimize the model for mobile deployment:

```bash
python export_model_mobile.py --checkpoint path/to/model.pth --quantize
```

Options:
- `--quantize`: Apply quantization for mobile deployment

## Deployment Steps

### 1. Choose the Best Model Format
Select the appropriate model format based on your deployment environment:
- Python application: PyTorch (.pth) or Quantized PyTorch (.pth)
- Cross-platform: ONNX (.onnx)
- Mobile: TorchScript (.pt)

### 2. Export the Model
Use the appropriate export script to convert your trained model:
- For ONNX: `export_model_onnx.py`
- For mobile: `export_model_mobile.py`
- For quantization: `quantize_model.py`

### 3. Test the Exported Model
Verify the exported model works correctly:

```bash
python deployment_inference.py --model path/to/exported/model --model_type [pytorch|quantized|onnx|torchscript] --image path/to/test/image.jpg
```

### 4. Integrate into Your Application
Use the appropriate API to integrate the model into your application:
- PyTorch API for Python applications
- ONNX Runtime for cross-platform applications
- PyTorch Mobile for mobile applications

## Integration Examples

### Python Application

```python
from deployment_inference import ChessBoardDetector

# Initialize detector
detector = ChessBoardDetector(
    model_path="path/to/model.pth",
    model_type="pytorch",  # or "quantized"
    device="cuda"  # or "cpu"
)

# Load image
import cv2
image = cv2.imread("path/to/image.jpg")

# Run detection
results = detector.detect(image)

# Visualize results
detector.visualize_detection(image, results, "output.jpg")
```

### Web Application (JavaScript with ONNX Runtime Web)

```javascript
// Install dependencies: npm install onnxruntime-web

import * as ort from 'onnxruntime-web';

async function detectChessBoard(imageElement) {
    // Load model
    const session = await ort.InferenceSession.create('path/to/model.onnx');
    
    // Preprocess image
    const tensor = preprocessImage(imageElement);
    
    // Run inference
    const feeds = { input: tensor };
    const results = await session.run(feeds);
    
    // Process results
    const segmentation = results.segmentation;
    const heatmaps = results.heatmaps;
    
    // Extract corners and visualize
    // ...
}
```

### Mobile Application (Android with PyTorch Mobile)

```java
// Add to build.gradle:
// implementation 'org.pytorch:pytorch_android:1.10.0'
// implementation 'org.pytorch:pytorch_android_torchvision:1.10.0'

import org.pytorch.IValue;
import org.pytorch.Module;
import org.pytorch.Tensor;
import org.pytorch.torchvision.TensorImageUtils;

public class ChessBoardDetector {
    private Module module;
    
    public ChessBoardDetector(Context context, String modelPath) {
        // Load model
        module = Module.load(modelPath);
    }
    
    public DetectionResult detect(Bitmap image) {
        // Preprocess image
        float[] mean = {0.485f, 0.456f, 0.406f};
        float[] std = {0.229f, 0.224f, 0.225f};
        
        Tensor inputTensor = TensorImageUtils.bitmapToFloat32Tensor(
            image, mean, std);
        
        // Run inference
        IValue[] outputs = module.forward(IValue.from(inputTensor)).toTuple();
        
        // Process results
        Tensor segmentation = outputs[0].toTensor();
        Tensor heatmaps = outputs[1].toTensor();
        
        // Extract corners and return result
        // ...
    }
}
```

## Troubleshooting

### Common Issues

#### Model Size Too Large
- Use quantization to reduce model size
- Consider pruning less important weights

#### Slow Inference
- Use quantized models for faster inference
- Ensure you're using the right backend for your hardware
- Consider using GPU acceleration if available

#### Poor Accuracy After Conversion
- Verify preprocessing is identical to training
- Check normalization values (mean and std)
- Ensure input size matches model expectations

#### Out of Memory Errors
- Reduce batch size
- Use quantized models
- Consider model pruning

#### Mobile Deployment Issues
- Ensure PyTorch Mobile version matches model version
- Check for dynamic shapes in model (may not be supported)
- Verify all operations are supported on mobile

### Getting Help

If you encounter issues not covered in this guide:
1. Check the PyTorch documentation: https://pytorch.org/docs/stable/index.html
2. For ONNX issues: https://onnx.ai/onnx/index.html
3. For mobile deployment: https://pytorch.org/mobile/home/
