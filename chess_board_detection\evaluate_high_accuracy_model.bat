@echo off
REM Evaluate the high-accuracy chess piece detection model

echo Evaluating high-accuracy model...

set MODEL=%1
if "%MODEL%"=="" (
    echo Error: Please provide the path to the model
    echo Usage: %0 path/to/model.pt
    exit /b 1
)

set DATA_YAML=chess_board_detection/piece_detection/enhanced_dataset_99plus/dataset.yaml
set OUTPUT_DIR=chess_board_detection/piece_detection/evaluation_results
set CONF_THRESHOLD=0.7
set IOU_THRESHOLD=0.7

python chess_board_detection/piece_detection/evaluate_high_accuracy.py ^
    --model %MODEL% ^
    --data_yaml %DATA_YAML% ^
    --output_dir %OUTPUT_DIR% ^
    --conf %CONF_THRESHOLD% ^
    --iou %IOU_THRESHOLD%

echo.
echo Evaluation completed. Results saved to %OUTPUT_DIR%

REM Check if the model meets the 99% target
findstr /C:"all_targets_met" %OUTPUT_DIR%\evaluation_metrics.json

echo.
echo Testing on problem cases completed.
pause
