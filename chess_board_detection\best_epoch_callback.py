import os
import json
from datetime import datetime

class BestEpochLogger:
    """
    Custom logger to track when a new best epoch is achieved.
    This provides detailed information about the metrics when a new best model is found.
    """

    def __init__(self):
        self.best_fitness = -float('inf')
        self.best_epoch = -1
        self.metrics_history = []
        self.log_file = None
        self.save_dir = None

    def _init_log_file(self, save_dir):
        """Initialize the log file with header."""
        self.save_dir = save_dir
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_file = os.path.join(save_dir, f"best_epochs_{timestamp}.txt")

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

        with open(self.log_file, 'w') as f:
            f.write("=== Best Epoch Achievement Log ===\n")
            f.write("Format: Epoch | mAP50 | mAP50-95 | Precision | Recall | Box Loss | Cls Loss | DFL Loss\n")
            f.write("-" * 80 + "\n")

    def on_train_start(self, trainer):
        """Initialize logging when training starts."""
        self._init_log_file(trainer.save_dir)
        print("\n=== Best Epoch Achievement Logging Enabled ===")
        print("Detailed logs will be saved when a new best model is found")

    def on_train_epoch_end(self, trainer):
        """Check if this epoch is the best and log if it is."""
        try:
            # Get current metrics
            metrics = trainer.metrics
            epoch = trainer.epoch
            fitness = metrics.get("fitness", 0)  # Overall fitness score

            # Check if this is a new best model
            if fitness > self.best_fitness:
                self.best_fitness = fitness
                self.best_epoch = epoch

                # Log the achievement
                achievement = {
                    "epoch": epoch,
                    "mAP50": metrics.get("metrics/mAP50", 0),
                    "mAP50-95": metrics.get("metrics/mAP50-95", 0),
                    "precision": metrics.get("metrics/precision", 0),
                    "recall": metrics.get("metrics/recall", 0)
                }

                # Try to get loss items if available
                try:
                    if hasattr(trainer, 'loss_items') and len(trainer.loss_items) > 0:
                        achievement["box_loss"] = trainer.loss_items[0] if len(trainer.loss_items) > 0 else 0
                        achievement["cls_loss"] = trainer.loss_items[1] if len(trainer.loss_items) > 1 else 0
                        achievement["dfl_loss"] = trainer.loss_items[2] if len(trainer.loss_items) > 2 else 0
                except Exception as e:
                    print(f"Note: Could not access loss items: {e}")

                self.metrics_history.append(achievement)

                # Print to console
                print("\n" + "=" * 40)
                print(f"NEW BEST MODEL at Epoch {epoch}!")
                print(f"mAP50: {achievement.get('mAP50', 0):.4f}")
                print(f"mAP50-95: {achievement.get('mAP50-95', 0):.4f}")
                print(f"Precision: {achievement.get('precision', 0):.4f}")
                print(f"Recall: {achievement.get('recall', 0):.4f}")
                print("=" * 40 + "\n")

                # Write to log file
                with open(self.log_file, 'a') as f:
                    f.write(f"Epoch {epoch:3d} | ")
                    f.write(f"mAP50: {achievement.get('mAP50', 0):.4f} | ")
                    f.write(f"mAP50-95: {achievement.get('mAP50-95', 0):.4f} | ")
                    f.write(f"Precision: {achievement.get('precision', 0):.4f} | ")
                    f.write(f"Recall: {achievement.get('recall', 0):.4f}")

                    # Add loss values if available
                    if "box_loss" in achievement:
                        f.write(f" | Box Loss: {achievement.get('box_loss', 0):.4f}")
                    if "cls_loss" in achievement:
                        f.write(f" | Cls Loss: {achievement.get('cls_loss', 0):.4f}")
                    if "dfl_loss" in achievement:
                        f.write(f" | DFL Loss: {achievement.get('dfl_loss', 0):.4f}")

                    f.write("\n")
        except Exception as e:
            print(f"Warning: Error in on_train_epoch_end callback: {e}")

    def on_train_end(self, trainer):
        """Summarize the best epochs at the end of training."""
        try:
            print("\n=== Training Complete ===")
            print(f"Best epoch: {self.best_epoch}")
            print(f"Best fitness: {self.best_fitness:.4f}")

            if self.save_dir:
                # Save history as JSON for later analysis
                history_file = os.path.join(self.save_dir, "best_epochs_history.json")
                with open(history_file, 'w') as f:
                    json.dump(self.metrics_history, f, indent=2)

                print(f"Best epoch history saved to {history_file}")
                print(f"Detailed logs saved to {self.log_file}")
        except Exception as e:
            print(f"Warning: Error in on_train_end callback: {e}")
