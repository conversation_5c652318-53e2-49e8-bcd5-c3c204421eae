"""
Utility functions for visualizing chess board detection results.
"""

import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image, ImageDraw, ImageFont

def visualize_detection_results(image, corners, normalized_board, output_path):
    """
    Visualize chess board detection results.
    
    Args:
        image: Original input image
        corners: Detected corner coordinates
        normalized_board: Normalized chess board image
        output_path: Path to save the visualization
    """
    # Convert to numpy if needed
    if isinstance(image, Image.Image):
        image = np.array(image)
        image = cv2.cvtColor(image, cv2.COLOR_RGB2BGR)
    
    if isinstance(normalized_board, Image.Image):
        normalized_board = np.array(normalized_board)
        normalized_board = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)
    
    # Create figure
    fig, axs = plt.subplots(1, 2, figsize=(12, 6))
    
    # Original image with detected corners
    axs[0].imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
    axs[0].set_title('Original Image')
    
    # Draw corners
    for i, (x, y) in enumerate(corners):
        axs[0].plot(x, y, 'ro', markersize=10)
        axs[0].text(x, y, f"{i+1}", color='white', fontsize=12)
    
    # Normalized chess board
    axs[1].imshow(cv2.cvtColor(normalized_board, cv2.COLOR_BGR2RGB))
    axs[1].set_title('Normalized Chess Board')
    
    # Remove axis ticks
    for ax in axs:
        ax.set_xticks([])
        ax.set_yticks([])
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

def visualize_piece_detection(normalized_board, detections, output_path):
    """
    Visualize chess piece detection results.
    
    Args:
        normalized_board: Normalized chess board image
        detections: List of detected pieces with coordinates and classes
        output_path: Path to save the visualization
    """
    # Convert to numpy if needed
    if isinstance(normalized_board, Image.Image):
        normalized_board = np.array(normalized_board)
        normalized_board = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)
    
    # Create a copy for drawing
    result_image = normalized_board.copy()
    
    # Draw bounding boxes
    for detection in detections:
        x1, y1, x2, y2 = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']
        
        # Determine color based on piece color
        color = (0, 0, 255) if 'white' in class_name else (255, 0, 0)
        
        # Draw bounding box
        cv2.rectangle(result_image, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
        
        # Add label
        label = f"{class_name.split('_')[1]}: {confidence:.2f}"
        cv2.putText(result_image, label, (int(x1), int(y1)-5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    # Save result
    cv2.imwrite(output_path, result_image)

def visualize_chess_position(normalized_board, chess_pieces, output_path):
    """
    Visualize chess position.
    
    Args:
        normalized_board: Normalized chess board image
        chess_pieces: Dictionary mapping chess coordinates to piece names
        output_path: Path to save the visualization
    """
    # Convert to numpy if needed
    if isinstance(normalized_board, Image.Image):
        normalized_board = np.array(normalized_board)
        normalized_board = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)
    
    # Create a copy for drawing
    result_image = normalized_board.copy()
    
    # Get board dimensions
    h, w = result_image.shape[:2]
    square_size = (w // 8, h // 8)
    
    # Draw grid
    for i in range(9):
        # Vertical lines
        cv2.line(result_image, (i * square_size[0], 0), (i * square_size[0], h), (255, 255, 255), 1)
        # Horizontal lines
        cv2.line(result_image, (0, i * square_size[1]), (w, i * square_size[1]), (255, 255, 255), 1)
    
    # Add coordinates
    for i in range(8):
        # File labels (a-h)
        cv2.putText(result_image, chr(ord('a') + i), 
                    (i * square_size[0] + square_size[0] // 2, h - 10), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        # Rank labels (1-8)
        cv2.putText(result_image, str(8 - i), 
                    (10, i * square_size[1] + square_size[1] // 2), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # Add piece labels
    for coord, piece_info in chess_pieces.items():
        file_letter = coord[0]
        rank_number = int(coord[1])
        
        file_idx = ord(file_letter) - ord('a')
        rank_idx = 8 - rank_number
        
        center_x = file_idx * square_size[0] + square_size[0] // 2
        center_y = rank_idx * square_size[1] + square_size[1] // 2
        
        piece_name = piece_info['piece']
        piece_color = 'white' if 'white' in piece_name else 'black'
        piece_type = piece_name.split('_')[1]
        
        # Add piece label
        text_color = (0, 0, 0) if piece_color == 'white' else (255, 255, 255)
        bg_color = (255, 255, 255) if piece_color == 'black' else (0, 0, 0)
        
        # Draw background circle
        cv2.circle(result_image, (center_x, center_y), 15, bg_color, -1)
        
        # Add piece label
        cv2.putText(result_image, piece_type[0].upper() if piece_color == 'white' else piece_type[0].lower(), 
                    (center_x - 5, center_y + 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, text_color, 2)
    
    # Save result
    cv2.imwrite(output_path, result_image)
