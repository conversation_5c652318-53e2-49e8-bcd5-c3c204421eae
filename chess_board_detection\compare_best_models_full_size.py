"""
Compare the best models from Phase 2 and Phase 3 using a two-stage approach on full-size images.
Tests both models on a real-world image without resizing to 256x256 first.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import json
from scipy import ndimage
from skimage.measure import regionprops

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image_full_size(image_path):
    """
    Preprocess an image for model input while preserving full size.
    The image is processed in tiles of 256x256.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Store preprocessing info
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height)
    }

    return original_image, preprocess_info

def enhance_image(image):
    """Apply basic image enhancement."""
    # Convert to float32 for processing
    image_float = image.astype(np.float32) / 255.0

    # Apply contrast stretching
    p2, p98 = np.percentile(image_float, (2, 98))
    enhanced = np.clip((image_float - p2) / (p98 - p2), 0, 1)

    # Convert back to uint8
    enhanced = (enhanced * 255).astype(np.uint8)

    return enhanced

def normalize_for_model(image, target_size=(256, 256)):
    """Normalize image for model input."""
    # Resize to target size
    image_resized = cv2.resize(image, target_size, interpolation=cv2.INTER_AREA)

    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])

    # Apply transformation
    input_tensor = transform(Image.fromarray(image_resized)).unsqueeze(0)

    return input_tensor

def process_segmentation_mask(segmentation_mask, threshold=0.5):
    """
    Process the segmentation mask to get the chess board region.
    Returns the bounding box of the largest connected component.
    """
    # Threshold the segmentation mask
    binary_mask = (segmentation_mask > threshold).astype(np.uint8)

    # Find connected components
    labeled_mask, num_features = ndimage.label(binary_mask)

    if num_features == 0:
        return None

    # Get properties of the labeled regions
    regions = regionprops(labeled_mask)

    # Find the largest region (by area)
    if not regions:
        return None

    largest_region = max(regions, key=lambda r: r.area)

    # Get bounding box (min_row, min_col, max_row, max_col)
    bbox = largest_region.bbox

    # Convert to (x, y, width, height) format
    x = bbox[1]
    y = bbox[0]
    width = bbox[3] - bbox[1]
    height = bbox[2] - bbox[0]

    return (x, y, width, height)

def detect_corners_in_region(heatmaps, region_bbox, threshold=0.3):
    """
    Detect corners within a specific region of the heatmaps.
    """
    # Extract region coordinates
    x, y, width, height = region_bbox

    # Process each heatmap
    keypoints = []

    # Handle batch dimension - heatmaps shape is [1, 4, H, W]
    for i in range(heatmaps.shape[1]):
        heatmap = heatmaps[0, i].cpu().numpy()

        # Create a mask for the region
        mask = np.zeros_like(heatmap)
        mask[y:y+height, x:x+width] = 1

        # Apply mask to heatmap
        masked_heatmap = heatmap * mask

        # Find maximum point
        max_val = np.max(masked_heatmap)

        if max_val > threshold:
            # Get coordinates of maximum
            max_idx = np.argmax(masked_heatmap)
            max_y, max_x = np.unravel_index(max_idx, masked_heatmap.shape)
            keypoints.append((max_x, max_y))
        else:
            keypoints.append(None)

    return keypoints

def map_to_original_coordinates(keypoints, original_size, model_size=(256, 256)):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    """
    mapped_keypoints = []

    original_w, original_h = original_size
    model_w, model_h = model_size

    for kp in keypoints:
        if kp is None:
            mapped_keypoints.append(None)
            continue

        x, y = kp

        # Map from model size to original size
        x_original = x * original_w / model_w
        y_original = y * original_h / model_h

        mapped_keypoints.append((int(x_original), int(y_original)))

    return mapped_keypoints

def two_stage_detection_full_size(image_path, model, model_name):
    """
    Apply two-stage detection on full-size image:
    1. Resize image to 256x256 for model input
    2. Use segmentation to identify the chess board region
    3. Use corner detection within the segmented region
    4. Map coordinates back to original image
    """
    # Preprocess image (keep original size for reference)
    original_image, preprocess_info = preprocess_image_full_size(image_path)
    enhanced_image = enhance_image(original_image)

    # Normalize and resize for model
    input_tensor = normalize_for_model(enhanced_image)

    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()
    heatmaps = torch.sigmoid(outputs['corner_heatmaps'])

    # Process segmentation to get chess board region
    region_bbox = process_segmentation_mask(segmentation[0, 0])

    if region_bbox is None:
        print(f"No chess board region detected in the image for {model_name}")
        return None

    # Detect corners within the region
    keypoints = detect_corners_in_region(heatmaps, region_bbox)

    # Map keypoints back to original image
    original_keypoints = map_to_original_coordinates(
        keypoints,
        preprocess_info['original_size']
    )

    # Create a resized version of the original image for visualization
    resized_for_vis = cv2.resize(original_image, (256, 256), interpolation=cv2.INTER_AREA)

    return {
        'model_name': model_name,
        'preprocessed_image': resized_for_vis,  # For visualization
        'segmentation': segmentation,
        'region_bbox': region_bbox,
        'keypoints': keypoints,
        'original_keypoints': original_keypoints,
        'original_image': original_image
    }

def visualize_two_stage_detection(image, segmentation, region_bbox, keypoints, output_path):
    """
    Visualize the results of two-stage detection.
    """
    # Create figure with 2x2 subplots
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))

    # Plot original image
    axs[0, 0].imshow(image)
    axs[0, 0].set_title('Preprocessed Image')
    axs[0, 0].axis('off')

    # Plot segmentation mask
    axs[0, 1].imshow(segmentation[0, 0], cmap='viridis')
    axs[0, 1].set_title('Segmentation Mask')
    axs[0, 1].axis('off')

    # Plot region and keypoints
    axs[1, 0].imshow(image)
    x, y, w, h = region_bbox
    rect = plt.Rectangle((x, y), w, h, linewidth=2, edgecolor='r', facecolor='none')
    axs[1, 0].add_patch(rect)
    axs[1, 0].set_title('Detected Region')
    axs[1, 0].axis('off')

    # Plot keypoints
    axs[1, 1].imshow(image)
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['r', 'g', 'b', 'y']

    for i, (kp, name, color) in enumerate(zip(keypoints, corner_names, colors)):
        if kp is not None:
            x, y = kp
            axs[1, 1].scatter(x, y, c=color, s=50)
            axs[1, 1].text(x+5, y+5, name, color=color, fontsize=10)

    axs[1, 1].set_title('Detected Corners')
    axs[1, 1].axis('off')

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

def visualize_original_with_keypoints(original_image, keypoints, output_path, model_name):
    """
    Visualize the original image with detected keypoints.
    """
    plt.figure(figsize=(10, 8))
    plt.imshow(original_image)

    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['r', 'g', 'b', 'y']

    for i, (kp, name, color) in enumerate(zip(keypoints, corner_names, colors)):
        if kp is not None:
            x, y = kp
            plt.scatter(x, y, c=color, s=100, marker='o')
            plt.text(x+15, y+15, name, color=color, fontsize=12, weight='bold')

    plt.title(f'Chess Board Corner Detection (Full Size) - {model_name}', fontsize=16)
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\full_size_comparison"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    os.makedirs(output_dir, exist_ok=True)

    # Process each model
    results = {}

    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} with full-size two-stage detection...")

        # Load model
        model = load_model(model_path)

        # Apply two-stage detection
        detection_results = two_stage_detection_full_size(image_path, model, model_name)

        if detection_results is None:
            continue

        # Store results
        results[model_name] = detection_results

        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_two_stage.png")
        visualize_two_stage_detection(
            detection_results['preprocessed_image'],
            detection_results['segmentation'],
            detection_results['region_bbox'],
            detection_results['keypoints'],
            output_path
        )

        # Save original image with keypoints
        original_output_path = os.path.join(output_dir, f"{model_name}_original_with_keypoints.png")
        visualize_original_with_keypoints(
            detection_results['original_image'],
            detection_results['original_keypoints'],
            original_output_path,
            model_name
        )

        print(f"Full-size two-stage detection visualization saved to: {output_path}")
        print(f"Original image with keypoints saved to: {original_output_path}")

        # Print corner coordinates
        print(f"\nCorner coordinates for {model_name} (full size):")
        corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
        for i, (name, kp) in enumerate(zip(corner_names, detection_results['original_keypoints'])):
            if kp is not None:
                print(f"{name}: {kp}")
            else:
                print(f"{name}: Not detected")

    print("\nAll processing completed!")

if __name__ == "__main__":
    main()
