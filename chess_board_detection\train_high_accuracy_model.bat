@echo off
REM Train a high-accuracy chess piece detection model targeting 99%+ accuracy

echo Creating enhanced dataset for high-accuracy training...

set INPUT_IMAGES=chess_board_detection/piece_detection/dataset/images
set INPUT_LABELS=chess_board_detection/piece_detection/dataset/labels
set OUTPUT_DIR=chess_board_detection/piece_detection/enhanced_dataset_99plus
set NUM_AUGMENTATIONS=20
set TRAIN_RATIO=0.8
set MIN_VAL_EXAMPLES=3

python chess_board_detection/piece_detection/create_enhanced_dataset.py ^
    --input_images %INPUT_IMAGES% ^
    --input_labels %INPUT_LABELS% ^
    --output_dir %OUTPUT_DIR% ^
    --num_augmentations %NUM_AUGMENTATIONS% ^
    --train_ratio %TRAIN_RATIO% ^
    --min_val_examples %MIN_VAL_EXAMPLES%

echo.
echo Training high-accuracy model...

set MODEL=yolo11n.pt
set DATA_YAML=%OUTPUT_DIR%/dataset.yaml
set MODEL_DIR=chess_board_detection/piece_detection/models/high_accuracy_yolo
set MAX_PHASE1_EPOCHS=100
set MAX_PHASE2_EPOCHS=200
set BATCH_SIZE=16
set IMG_SIZE=416
set DEVICE=0
set WORKERS=4
set PATIENCE=50
set SAVE_PERIOD=10
set CHECK_TARGETS_EVERY=5
set IMPROVEMENT_THRESHOLD=0.0005
set PRECISION_TARGET=0.99
set RECALL_TARGET=0.99
set MAP50_TARGET=0.99
set COLOR_ACCURACY_TARGET=0.99

python chess_board_detection/piece_detection/train_high_accuracy.py ^
    --model %MODEL% ^
    --data_yaml %DATA_YAML% ^
    --output_dir %MODEL_DIR% ^
    --max_phase1_epochs %MAX_PHASE1_EPOCHS% ^
    --max_phase2_epochs %MAX_PHASE2_EPOCHS% ^
    --batch %BATCH_SIZE% ^
    --img-size %IMG_SIZE% ^
    --device %DEVICE% ^
    --workers %WORKERS% ^
    --patience %PATIENCE% ^
    --save_period %SAVE_PERIOD% ^
    --check_targets_every %CHECK_TARGETS_EVERY% ^
    --improvement_threshold %IMPROVEMENT_THRESHOLD% ^
    --precision_target %PRECISION_TARGET% ^
    --recall_target %RECALL_TARGET% ^
    --map50_target %MAP50_TARGET% ^
    --color_accuracy_target %COLOR_ACCURACY_TARGET%

echo.
echo Training completed. Model saved to %MODEL_DIR%
pause
