"""
Visualize raw model outputs to better understand what the model is detecting.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
from scipy import ndimage

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """Apply basic image enhancement."""
    # Convert to float32 for processing
    image_float = image.astype(np.float32) / 255.0
    
    # Apply contrast stretching
    p2, p98 = np.percentile(image_float, (2, 98))
    enhanced = np.clip((image_float - p2) / (p98 - p2), 0, 1)
    
    # Convert back to uint8
    enhanced = (enhanced * 255).astype(np.uint8)
    
    return enhanced

def normalize_for_model(image):
    """Normalize image for model input."""
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)
    
    return input_tensor

def find_local_maxima(heatmap, threshold=0.3, min_distance=10):
    """
    Find all local maxima in a heatmap above a threshold.
    
    Args:
        heatmap: 2D numpy array
        threshold: Minimum value to be considered a maximum
        min_distance: Minimum distance between maxima
        
    Returns:
        List of (x, y, value) tuples for each maximum
    """
    # Apply maximum filter to find local maxima
    data_max = ndimage.maximum_filter(heatmap, size=min_distance)
    maxima = (heatmap == data_max) & (heatmap > threshold)
    
    # Get coordinates and values of maxima
    labeled, num_objects = ndimage.label(maxima)
    slices = ndimage.find_objects(labeled)
    
    maxima_list = []
    for dy, dx in slices:
        x_center = (dx.start + dx.stop - 1) // 2
        y_center = (dy.start + dy.stop - 1) // 2
        val = heatmap[y_center, x_center]
        maxima_list.append((x_center, y_center, val))
    
    # Sort by value, highest first
    maxima_list.sort(key=lambda x: x[2], reverse=True)
    
    return maxima_list

def visualize_raw_outputs(image_path, model, model_name, output_dir):
    """
    Run inference and visualize raw model outputs.
    """
    # Preprocess image
    preprocessed_image, preprocess_info = preprocess_image(image_path)
    enhanced_image = enhance_image(preprocessed_image)
    
    # Normalize for model
    input_tensor = normalize_for_model(enhanced_image)
    
    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation and heatmaps
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()
    heatmaps = torch.sigmoid(outputs['corner_heatmaps']).cpu().numpy()
    
    # Create output directory
    model_output_dir = os.path.join(output_dir, model_name)
    os.makedirs(model_output_dir, exist_ok=True)
    
    # Visualize preprocessed image
    plt.figure(figsize=(8, 8))
    plt.imshow(enhanced_image)
    plt.title('Preprocessed Image')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(os.path.join(model_output_dir, 'preprocessed_image.png'), dpi=150)
    plt.close()
    
    # Visualize segmentation mask
    plt.figure(figsize=(8, 8))
    plt.imshow(segmentation[0, 0], cmap='viridis')
    plt.title('Segmentation Mask')
    plt.colorbar(label='Probability')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(os.path.join(model_output_dir, 'segmentation_mask.png'), dpi=150)
    plt.close()
    
    # Visualize heatmaps with local maxima
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    
    for i, name in enumerate(corner_names):
        heatmap = heatmaps[0, i]
        
        # Find local maxima
        maxima = find_local_maxima(heatmap)
        
        # Create figure
        plt.figure(figsize=(10, 8))
        
        # Plot heatmap
        plt.imshow(heatmap, cmap='hot')
        plt.title(f'{name} Corner Heatmap')
        plt.colorbar(label='Probability')
        
        # Plot local maxima
        for j, (x, y, val) in enumerate(maxima[:5]):  # Show top 5 maxima
            plt.scatter(x, y, c='b', s=50)
            plt.text(x+5, y+5, f"{j+1}: {val:.3f}", color='white', fontsize=10, weight='bold')
        
        plt.axis('off')
        plt.tight_layout()
        plt.savefig(os.path.join(model_output_dir, f'{name.lower().replace("-", "_")}_heatmap.png'), dpi=150)
        plt.close()
    
    # Visualize combined heatmaps
    plt.figure(figsize=(10, 8))
    
    # Create RGB image from first 3 heatmaps
    rgb_heatmap = np.zeros((heatmaps.shape[2], heatmaps.shape[3], 3))
    rgb_heatmap[:, :, 0] = heatmaps[0, 0]  # Red: Top-Left
    rgb_heatmap[:, :, 1] = heatmaps[0, 1]  # Green: Top-Right
    rgb_heatmap[:, :, 2] = heatmaps[0, 2]  # Blue: Bottom-Right
    
    # Normalize to [0, 1]
    rgb_heatmap = np.clip(rgb_heatmap, 0, 1)
    
    plt.imshow(rgb_heatmap)
    plt.title('Combined Corner Heatmaps (RGB)')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(os.path.join(model_output_dir, 'combined_heatmaps.png'), dpi=150)
    plt.close()
    
    # Visualize overlay of heatmaps on image
    plt.figure(figsize=(10, 8))
    plt.imshow(enhanced_image)
    
    # Plot contours of each heatmap
    colors = ['r', 'g', 'b', 'y']
    
    for i, (name, color) in enumerate(zip(corner_names, colors)):
        heatmap = heatmaps[0, i]
        
        # Create contour at 0.5 threshold
        plt.contour(heatmap, levels=[0.5], colors=[color], linewidths=2)
        
        # Find maximum
        max_idx = np.argmax(heatmap)
        y, x = np.unravel_index(max_idx, heatmap.shape)
        
        # Plot maximum
        plt.scatter(x, y, c=color, s=100, marker='x')
        plt.text(x+5, y+5, name, color=color, fontsize=12, weight='bold')
    
    plt.title('Heatmap Overlay on Image')
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(os.path.join(model_output_dir, 'heatmap_overlay.png'), dpi=150)
    plt.close()
    
    return {
        'model_name': model_name,
        'preprocessed_image': enhanced_image,
        'segmentation': segmentation,
        'heatmaps': heatmaps,
        'output_dir': model_output_dir
    }

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\raw_outputs"
    
    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each model
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name} raw outputs...")
        
        # Load model
        model = load_model(model_path)
        
        # Visualize raw outputs
        results = visualize_raw_outputs(image_path, model, model_name, output_dir)
        
        print(f"Raw outputs visualized in: {results['output_dir']}")
    
    print("\nAll processing completed!")

if __name__ == "__main__":
    main()
