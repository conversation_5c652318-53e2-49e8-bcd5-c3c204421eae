"""
Visualize evaluation metrics for chess piece detection models.
This script creates various visualizations to help analyze model performance,
with a focus on color classification accuracy and other key metrics.
"""

import os
import sys
import argparse
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

def load_metrics(metrics_file):
    """
    Load metrics from a JSON file.
    
    Args:
        metrics_file: Path to the metrics JSON file
    
    Returns:
        Dictionary of metrics
    """
    with open(metrics_file, 'r') as f:
        return json.load(f)

def create_color_accuracy_chart(metrics, output_dir):
    """
    Create a chart showing color-specific metrics.
    
    Args:
        metrics: Dictionary of metrics
        output_dir: Directory to save the chart
    """
    # Check if color metrics are available
    if not all(k in metrics for k in ['white_precision', 'black_precision']):
        print("Color metrics not available, skipping color accuracy chart")
        return
    
    # Extract color metrics
    white_metrics = {
        'Precision': metrics['white_precision'],
        'Recall': metrics['white_recall'],
        'mAP50': metrics['white_map50'],
        'F1': metrics['white_f1']
    }
    
    black_metrics = {
        'Precision': metrics['black_precision'],
        'Recall': metrics['black_recall'],
        'mAP50': metrics['black_map50'],
        'F1': metrics['black_f1']
    }
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Set up bar positions
    bar_width = 0.35
    index = np.arange(len(white_metrics))
    
    # Create bars
    plt.bar(index, white_metrics.values(), bar_width, label='White Pieces', color='lightblue')
    plt.bar(index + bar_width, black_metrics.values(), bar_width, label='Black Pieces', color='darkblue')
    
    # Add color accuracy if available
    if 'color_accuracy' in metrics and metrics['color_accuracy'] is not None:
        plt.axhline(y=metrics['color_accuracy'], color='r', linestyle='-', 
                   label=f"Color Accuracy: {metrics['color_accuracy']:.4f}")
    
    # Add labels and title
    plt.xlabel('Metric')
    plt.ylabel('Score')
    plt.title('Color-Specific Performance Metrics')
    plt.xticks(index + bar_width / 2, white_metrics.keys())
    plt.ylim(0, 1.05)
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add value labels on bars
    for i, v in enumerate(white_metrics.values()):
        plt.text(i - 0.05, v + 0.02, f"{v:.3f}", color='black', fontweight='bold')
    
    for i, v in enumerate(black_metrics.values()):
        plt.text(i + bar_width - 0.05, v + 0.02, f"{v:.3f}", color='black', fontweight='bold')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'color_accuracy_chart.png'), dpi=150)
    plt.close()

def create_f_beta_chart(metrics, output_dir):
    """
    Create a chart showing F-beta scores.
    
    Args:
        metrics: Dictionary of metrics
        output_dir: Directory to save the chart
    """
    # Check if F-beta metrics are available
    if not all(k in metrics for k in ['f1_score', 'f2_score', 'f05_score']):
        print("F-beta metrics not available, skipping F-beta chart")
        return
    
    # Extract F-beta metrics
    f_beta_metrics = {
        'F1 (Balanced)': metrics['f1_score'],
        'F2 (Recall-focused)': metrics['f2_score'],
        'F0.5 (Precision-focused)': metrics['f05_score']
    }
    
    # Create figure
    plt.figure(figsize=(10, 6))
    
    # Create bars
    bars = plt.bar(f_beta_metrics.keys(), f_beta_metrics.values(), color=['green', 'blue', 'purple'])
    
    # Add labels and title
    plt.xlabel('F-beta Score Type')
    plt.ylabel('Score')
    plt.title('F-beta Scores (Different Precision-Recall Trade-offs)')
    plt.ylim(0, 1.05)
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add value labels on bars
    for bar in bars:
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f"{height:.3f}", ha='center', va='bottom', fontweight='bold')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'f_beta_chart.png'), dpi=150)
    plt.close()

def create_target_metrics_chart(metrics, output_dir):
    """
    Create a chart showing target metrics and actual performance.
    
    Args:
        metrics: Dictionary of metrics
        output_dir: Directory to save the chart
    """
    # Define target metrics
    target_metrics = {
        'Precision': 0.99,
        'Recall': 0.99,
        'mAP50': 0.99,
        'Color Accuracy': 0.99 if 'color_accuracy' in metrics else None
    }
    
    # Extract actual metrics
    actual_metrics = {
        'Precision': metrics['precision'],
        'Recall': metrics['recall'],
        'mAP50': metrics['map50'],
        'Color Accuracy': metrics.get('color_accuracy', None)
    }
    
    # Remove None values
    if actual_metrics['Color Accuracy'] is None:
        del target_metrics['Color Accuracy']
        del actual_metrics['Color Accuracy']
    
    # Create figure
    plt.figure(figsize=(12, 8))
    
    # Set up bar positions
    bar_width = 0.35
    index = np.arange(len(target_metrics))
    
    # Create bars
    plt.bar(index, target_metrics.values(), bar_width, label='Target', color='lightgray')
    plt.bar(index + bar_width, actual_metrics.values(), bar_width, label='Actual', color='green')
    
    # Add labels and title
    plt.xlabel('Metric')
    plt.ylabel('Score')
    plt.title('Target vs. Actual Performance Metrics')
    plt.xticks(index + bar_width / 2, target_metrics.keys())
    plt.ylim(0.9, 1.01)  # Start y-axis at 0.9 to better show differences
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Add value labels on bars
    for i, v in enumerate(target_metrics.values()):
        plt.text(i - 0.05, v + 0.002, f"{v:.3f}", color='black', fontweight='bold')
    
    for i, v in enumerate(actual_metrics.values()):
        plt.text(i + bar_width - 0.05, v + 0.002, f"{v:.3f}", color='black', fontweight='bold')
        
        # Add color to indicate if target is met
        target_val = list(target_metrics.values())[i]
        if v >= target_val:
            plt.text(i + bar_width, v - 0.02, "✓", color='green', fontsize=15, fontweight='bold')
        else:
            plt.text(i + bar_width, v - 0.02, "✗", color='red', fontsize=15, fontweight='bold')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'target_metrics_chart.png'), dpi=150)
    plt.close()

def create_class_metrics_chart(metrics, output_dir):
    """
    Create a chart showing per-class metrics.
    
    Args:
        metrics: Dictionary of metrics
        output_dir: Directory to save the chart
    """
    # Check if per-class metrics are available
    if 'per_class_metrics' not in metrics:
        print("Per-class metrics not available, skipping class metrics chart")
        return
    
    per_class_metrics = metrics['per_class_metrics']
    
    # Extract precision and recall for each class
    classes = list(per_class_metrics.keys())
    precision = [per_class_metrics[cls]['precision'] for cls in classes]
    recall = [per_class_metrics[cls]['recall'] for cls in classes]
    map50 = [per_class_metrics[cls]['map50'] for cls in classes]
    
    # Create figure
    plt.figure(figsize=(14, 10))
    
    # Set up bar positions
    bar_width = 0.25
    index = np.arange(len(classes))
    
    # Create bars
    plt.bar(index, precision, bar_width, label='Precision', color='blue')
    plt.bar(index + bar_width, recall, bar_width, label='Recall', color='green')
    plt.bar(index + 2*bar_width, map50, bar_width, label='mAP50', color='red')
    
    # Add labels and title
    plt.xlabel('Class')
    plt.ylabel('Score')
    plt.title('Per-Class Performance Metrics')
    plt.xticks(index + bar_width, classes, rotation=45, ha='right')
    plt.ylim(0, 1.05)
    plt.legend()
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'per_class_metrics_chart.png'), dpi=150)
    plt.close()

def visualize_metrics(metrics_file, output_dir=None):
    """
    Create visualizations for model evaluation metrics.
    
    Args:
        metrics_file: Path to the metrics JSON file
        output_dir: Directory to save visualizations (defaults to same directory as metrics file)
    """
    # Load metrics
    metrics = load_metrics(metrics_file)
    
    # Set output directory
    if output_dir is None:
        output_dir = os.path.dirname(metrics_file)
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Create visualizations
    create_color_accuracy_chart(metrics, output_dir)
    create_f_beta_chart(metrics, output_dir)
    create_target_metrics_chart(metrics, output_dir)
    
    # Create per-class metrics chart if available
    if 'per_class_metrics' in metrics:
        create_class_metrics_chart(metrics, output_dir)
    
    print(f"Visualizations saved to {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Visualize chess piece detection evaluation metrics")
    parser.add_argument("--metrics", type=str, required=True, help="Path to metrics JSON file")
    parser.add_argument("--output_dir", type=str, default=None, help="Directory to save visualizations")
    
    args = parser.parse_args()
    
    visualize_metrics(args.metrics, args.output_dir)

if __name__ == "__main__":
    main()
