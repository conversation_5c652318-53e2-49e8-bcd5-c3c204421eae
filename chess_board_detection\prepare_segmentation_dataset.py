"""
Prepare dataset for YOLO v11 segmentation training.
Converts corner annotations to segmentation masks for chessboard detection.
"""

import json
import os
import cv2
import numpy as np
from pathlib import Path
import shutil
from sklearn.model_selection import train_test_split

def load_annotations(annotation_file):
    """Load annotations from JSON file."""
    with open(annotation_file, 'r') as f:
        annotations = json.load(f)
    return annotations

def create_segmentation_mask(corners, image_size):
    """
    Create a segmentation mask from corner coordinates.
    
    Args:
        corners: List of 8 coordinates [x1, y1, x2, y2, x3, y3, x4, y4]
        image_size: [width, height] of the image
        
    Returns:
        Binary mask where chessboard area is 1, background is 0
    """
    width, height = image_size
    
    # Reshape corners to [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    corner_points = []
    for i in range(0, len(corners), 2):
        x, y = corners[i], corners[i+1]
        corner_points.append([int(x), int(y)])
    
    # Create mask
    mask = np.zeros((height, width), dtype=np.uint8)
    
    # Fill the polygon defined by corners
    corner_array = np.array(corner_points, dtype=np.int32)
    cv2.fillPoly(mask, [corner_array], 1)
    
    return mask

def corners_to_yolo_segmentation(corners, image_size):
    """
    Convert corner coordinates to YOLO segmentation format.
    
    Args:
        corners: List of 8 coordinates [x1, y1, x2, y2, x3, y3, x4, y4]
        image_size: [width, height] of the image
        
    Returns:
        Normalized polygon coordinates for YOLO format
    """
    width, height = image_size
    
    # Reshape corners and normalize
    normalized_coords = []
    for i in range(0, len(corners), 2):
        x, y = corners[i], corners[i+1]
        # Normalize coordinates to [0, 1]
        norm_x = x / width
        norm_y = y / height
        normalized_coords.extend([norm_x, norm_y])
    
    return normalized_coords

def prepare_yolo_dataset(image_dir, annotation_file, output_dir, train_split=0.8):
    """
    Prepare YOLO segmentation dataset.
    
    Args:
        image_dir: Directory containing images
        annotation_file: JSON file with corner annotations
        output_dir: Output directory for YOLO dataset
        train_split: Fraction of data for training
    """
    # Load annotations
    annotations = load_annotations(annotation_file)
    
    # Create output directories
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # Create YOLO directory structure
    (output_path / "images" / "train").mkdir(parents=True, exist_ok=True)
    (output_path / "images" / "val").mkdir(parents=True, exist_ok=True)
    (output_path / "labels" / "train").mkdir(parents=True, exist_ok=True)
    (output_path / "labels" / "val").mkdir(parents=True, exist_ok=True)
    
    # Split data into train/val
    train_data, val_data = train_test_split(annotations, train_size=train_split, random_state=42)
    
    print(f"Training samples: {len(train_data)}")
    print(f"Validation samples: {len(val_data)}")
    
    # Process training data
    process_split(train_data, image_dir, output_path, "train")
    
    # Process validation data
    process_split(val_data, image_dir, output_path, "val")
    
    # Create dataset.yaml file
    create_dataset_yaml(output_path)
    
    print(f"Dataset prepared in: {output_path}")

def process_split(data, image_dir, output_path, split_name):
    """Process a data split (train/val)."""
    for annotation in data:
        image_name = annotation["image"]
        corners = annotation["corners"]
        image_size = annotation["image_size"]
        
        # Copy image
        src_image = Path(image_dir) / image_name
        dst_image = output_path / "images" / split_name / image_name
        
        if src_image.exists():
            shutil.copy2(src_image, dst_image)
            
            # Create label file
            label_name = image_name.replace('.jpg', '.txt').replace('.png', '.txt')
            label_path = output_path / "labels" / split_name / label_name
            
            # Convert corners to YOLO segmentation format
            normalized_coords = corners_to_yolo_segmentation(corners, image_size)
            
            # Write label file (class_id + normalized polygon coordinates)
            with open(label_path, 'w') as f:
                # Class 0 for chessboard, followed by normalized polygon coordinates
                coords_str = ' '.join([f"{coord:.6f}" for coord in normalized_coords])
                f.write(f"0 {coords_str}\n")
        else:
            print(f"Warning: Image {src_image} not found")

def create_dataset_yaml(output_path):
    """Create dataset.yaml file for YOLO training."""
    yaml_content = f"""# Chess Board Segmentation Dataset
path: {output_path.absolute()}
train: images/train
val: images/val

# Classes
nc: 1  # number of classes
names: ['chessboard']  # class names
"""
    
    with open(output_path / "dataset.yaml", 'w') as f:
        f.write(yaml_content)

def visualize_annotations(image_dir, annotation_file, output_dir, num_samples=5):
    """Visualize some annotations to verify correctness."""
    annotations = load_annotations(annotation_file)
    
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    for i, annotation in enumerate(annotations[:num_samples]):
        image_name = annotation["image"]
        corners = annotation["corners"]
        image_size = annotation["image_size"]
        
        # Load image
        image_path = Path(image_dir) / image_name
        if not image_path.exists():
            continue
            
        image = cv2.imread(str(image_path))
        
        # Draw corners and polygon
        corner_points = []
        for j in range(0, len(corners), 2):
            x, y = int(corners[j]), int(corners[j+1])
            corner_points.append((x, y))
            cv2.circle(image, (x, y), 10, (0, 0, 255), -1)
            cv2.putText(image, str(j//2), (x+15, y+15), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)
        
        # Draw polygon
        corner_array = np.array(corner_points, dtype=np.int32)
        cv2.polylines(image, [corner_array], True, (0, 255, 0), 3)
        
        # Create segmentation mask
        mask = create_segmentation_mask(corners, image_size)
        mask_colored = cv2.applyColorMap(mask * 255, cv2.COLORMAP_JET)
        
        # Save visualization
        cv2.imwrite(str(output_path / f"annotation_{i}_{image_name}"), image)
        cv2.imwrite(str(output_path / f"mask_{i}_{image_name}"), mask_colored)

if __name__ == "__main__":
    # Configuration
    IMAGE_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real"
    ANNOTATION_FILE = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json"
    OUTPUT_DIR = "chess_board_detection/segmentation_dataset"
    
    print("Preparing YOLO segmentation dataset...")
    
    # First, visualize some annotations to verify
    print("Creating annotation visualizations...")
    visualize_annotations(IMAGE_DIR, ANNOTATION_FILE, "chess_board_detection/annotation_visualizations", num_samples=5)
    
    # Prepare the dataset
    prepare_yolo_dataset(IMAGE_DIR, ANNOTATION_FILE, OUTPUT_DIR, train_split=0.8)
    
    print("Dataset preparation complete!")
