"""
Enhanced loss functions for chess board detection (v5.1).
This version includes specialized loss components to address the challenges identified in v5:
1. Curriculum-based peak-to-second ratio loss with gradual target increase
2. Enhanced detection rate loss to ensure all corners are detected
3. Robust segmentation guidance loss to prevent negative values
4. Stabilized geometric consistency loss with dynamic weighting
5. Improved gradient handling to prevent loss explosion
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from enhanced_loss_v4 import EnhancedDiceLoss, ImprovedGeometricConsistencyLoss


class StabilizedGeometricLoss(nn.Module):
    """
    Stabilized geometric consistency loss with dynamic weighting and gradient normalization
    to prevent loss explosion during training.
    """
    def __init__(self, base_weight=0.8, max_weight=2.0):
        super(StabilizedGeometricLoss, self).__init__()
        self.base_weight = base_weight
        self.max_weight = max_weight
        self.moving_avg = None
        self.alpha = 0.9  # For moving average
        self.geometric_loss = ImprovedGeometricConsistencyLoss(weight=1.0)

    def forward(self, pred_corners, heatmap_loss=None):
        # Calculate original geometric loss
        geometric_loss = self.geometric_loss(pred_corners)

        # Update moving average
        if self.moving_avg is None:
            self.moving_avg = geometric_loss.detach()
        else:
            self.moving_avg = self.alpha * self.moving_avg + (1 - self.alpha) * geometric_loss.detach()

        # Calculate dynamic weight based on ratio to heatmap loss if provided
        dynamic_weight = self.base_weight
        if heatmap_loss is not None:
            ratio = geometric_loss.detach() / (heatmap_loss.detach() + 1e-6)

            # If geometric loss is much larger than heatmap loss, reduce its weight
            if ratio > 10.0:
                dynamic_weight = self.base_weight / (ratio / 10.0)

        # Clamp weight to reasonable range
        dynamic_weight = min(max(dynamic_weight, 0.1), self.max_weight)

        # Apply normalization to prevent explosion
        normalized_loss = geometric_loss / (self.moving_avg + 1e-6)
        normalized_loss = torch.clamp(normalized_loss, max=5.0)

        return dynamic_weight * normalized_loss


class RobustSegmentationGuidanceLoss(nn.Module):
    """
    Robust segmentation guidance loss that ensures corners are detected within the board region.
    Uses a soft boundary approach to avoid harsh penalties.
    """
    def __init__(self, weight=1.0, boundary_width=5):
        super(RobustSegmentationGuidanceLoss, self).__init__()
        self.weight = weight
        self.boundary_width = boundary_width
        self.eps = 1e-6

    def forward(self, heatmaps, segmentation):
        """
        Calculate the segmentation guidance loss.

        Args:
            heatmaps: Predicted corner heatmaps (B, 4, H, W)
            segmentation: Predicted segmentation mask (B, 1, H, W)
        """
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)

        # Create a soft boundary mask from the segmentation
        # Values close to the boundary have values close to 1, values far from the boundary have values close to 0
        boundary_mask = torch.zeros_like(segmentation)

        for i in range(batch_size):
            seg = segmentation[i, 0]

            # Dilate the segmentation mask
            dilated = F.max_pool2d(seg.unsqueeze(0), kernel_size=2*self.boundary_width+1,
                                   stride=1, padding=self.boundary_width)
            dilated = dilated.squeeze(0)

            # Erode the segmentation mask
            eroded = -F.max_pool2d(-seg.unsqueeze(0) + 1, kernel_size=2*self.boundary_width+1,
                                  stride=1, padding=self.boundary_width) + 1
            eroded = eroded.squeeze(0)

            # The boundary is the difference between dilated and eroded
            boundary = dilated - eroded
            boundary_mask[i, 0] = boundary

        # For each corner heatmap, calculate the loss
        for i in range(batch_size):
            for c in range(num_corners):
                hm = heatmaps[i, c]
                seg = segmentation[i, 0]
                boundary = boundary_mask[i, 0]

                # Find the location of the maximum value in the heatmap
                max_val, max_idx = torch.max(hm.view(-1), dim=0)
                y, x = max_idx // w, max_idx % w

                # Get the segmentation value at the peak location
                seg_at_peak = seg[y, x]
                boundary_at_peak = boundary[y, x]

                # If the peak is outside the segmentation mask, penalize based on distance to boundary
                if seg_at_peak < 0.5:
                    # Use boundary information to calculate a soft penalty
                    # If the peak is close to the boundary, the penalty is small
                    # If the peak is far from the boundary, the penalty is large
                    distance_penalty = 1.0 - boundary_at_peak
                    loss += distance_penalty * max_val

                # Also penalize if the heatmap has significant activations outside the segmentation mask
                # Weight the heatmap by the inverse of the segmentation mask
                outside_mask = 1.0 - seg
                outside_activations = torch.sum(hm * outside_mask) / torch.sum(outside_mask + self.eps)
                loss += outside_activations

        # Normalize by batch size and number of corners
        loss = loss / (batch_size * num_corners)

        # Ensure loss is non-negative
        loss = torch.clamp(loss, min=0.0)

        return self.weight * loss


class CurriculumPeakToSecondRatioLoss(nn.Module):
    """
    Improved peak-to-second ratio loss with curriculum learning approach.
    Gradually increases the target ratio and uses a smoother penalty function
    to prevent training instability while still effectively improving the ratio.
    """
    def __init__(self, weight=15.0, min_target=1.2, max_target=2.0, curriculum_epochs=30):
        super(CurriculumPeakToSecondRatioLoss, self).__init__()
        self.weight = weight
        self.min_target = min_target
        self.max_target = max_target
        self.curriculum_epochs = curriculum_epochs
        self.current_epoch = 0
        self.eps = 1e-6

    def set_epoch(self, epoch):
        """Update the current epoch for curriculum learning."""
        self.current_epoch = epoch

    def get_current_target(self):
        """Calculate the current target ratio based on curriculum progress."""
        progress = min(self.current_epoch / self.curriculum_epochs, 1.0)
        return self.min_target + progress * (self.max_target - self.min_target)

    def forward(self, heatmaps):
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)
        valid_corners = 0

        target_ratio = self.get_current_target()

        for i in range(batch_size):
            for c in range(num_corners):
                hm = heatmaps[i, c]

                # Flatten heatmap
                hm_flat = hm.view(-1)

                # Find the maximum value (primary peak)
                max_val, max_idx = torch.max(hm_flat, dim=0)

                # Skip if max_val is too small
                if max_val < 0.1:
                    continue

                # Create a mask to exclude the main peak and its immediate surroundings
                mask = torch.ones_like(hm_flat)
                y, x = max_idx // w, max_idx % w

                # Create a larger exclusion zone around the primary peak
                for dy in range(-5, 6):
                    for dx in range(-5, 6):
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < h and 0 <= nx < w:
                            mask[ny * w + nx] = 0

                # Find second peak
                masked_hm = hm_flat * mask
                second_max_val = torch.max(masked_hm)

                # Ensure second_max_val is positive
                second_max_val = torch.clamp(second_max_val, min=self.eps)

                # Calculate ratio
                ratio = max_val / second_max_val

                # Smoother penalty if ratio is below target
                if ratio < target_ratio:
                    # Quadratic penalty with safeguards
                    penalty = torch.pow(target_ratio - ratio, 2) * 0.5
                    penalty = torch.clamp(penalty, max=5.0)  # Prevent extreme values
                    loss += penalty
                    valid_corners += 1

        # Normalize loss
        if valid_corners > 0:
            loss = loss / valid_corners

        # Apply weight with safeguard
        weighted_loss = self.weight * loss
        weighted_loss = torch.clamp(weighted_loss, max=50.0)  # Prevent dominating other losses

        return weighted_loss


class EnhancedPeakToSecondRatioLoss(nn.Module):
    """
    More aggressive loss function specifically targeting the peak-to-second ratio.
    Uses exponential penalty and higher target ratio.
    """
    def __init__(self, weight=15.0, target_ratio=2.0):
        super(EnhancedPeakToSecondRatioLoss, self).__init__()
        self.weight = weight
        self.target_ratio = target_ratio
        self.eps = 1e-6

    def forward(self, heatmaps):
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)
        valid_corners = 0

        for i in range(batch_size):
            for c in range(num_corners):
                hm = heatmaps[i, c]

                # Flatten heatmap
                hm_flat = hm.view(-1)

                # Find the maximum value (primary peak)
                max_val, max_idx = torch.max(hm_flat, dim=0)

                # Skip if max_val is too small
                if max_val < 0.1:
                    continue

                # Create a mask to exclude the main peak and its immediate surroundings
                mask = torch.ones_like(hm_flat)
                y, x = max_idx // w, max_idx % w

                # Create a larger exclusion zone around the primary peak
                for dy in range(-5, 6):
                    for dx in range(-5, 6):
                        ny, nx = y + dy, x + dx
                        if 0 <= ny < h and 0 <= nx < w:
                            mask[ny * w + nx] = 0

                # Find second peak
                masked_hm = hm_flat * mask
                second_max_val = torch.max(masked_hm)

                # Ensure second_max_val is positive
                second_max_val = torch.clamp(second_max_val, min=self.eps)

                # Calculate ratio
                ratio = max_val / second_max_val

                # More aggressive penalty if ratio is below target
                if ratio < self.target_ratio:
                    # Exponential penalty for more aggressive optimization
                    penalty = torch.exp(self.target_ratio - ratio) - 1.0
                    loss += penalty
                    valid_corners += 1

        # Ensure loss is not too large
        if valid_corners > 0:
            loss = loss / valid_corners

        loss = torch.clamp(loss, max=100.0)

        return self.weight * loss


class EnhancedDetectionRateLoss(nn.Module):
    """
    More aggressive loss function that specifically targets the detection rate.
    Uses a stronger exponential penalty and recovery mechanism.
    """
    def __init__(self, weight=8.0, detection_threshold=0.5, recovery_factor=2.0):
        super(EnhancedDetectionRateLoss, self).__init__()
        self.weight = weight
        self.detection_threshold = detection_threshold
        self.recovery_factor = recovery_factor  # Factor to increase penalty for missed detections
        self.eps = 1e-6

    def forward(self, heatmaps):
        batch_size, num_corners, h, w = heatmaps.shape
        loss = torch.tensor(0.0, device=heatmaps.device)

        # Track missed corners for each image
        missed_corners_per_image = torch.zeros(batch_size, device=heatmaps.device)

        for i in range(batch_size):
            for c in range(num_corners):
                # Get the predicted heatmap
                hm = heatmaps[i, c]

                # Find the maximum value
                max_val = torch.max(hm)

                # Penalize if max_val is below detection threshold
                if max_val < self.detection_threshold:
                    # More aggressive exponential penalty
                    penalty = torch.exp(self.detection_threshold - max_val) * 2.0 - 1.0
                    loss += penalty

                    # Track missed corner
                    missed_corners_per_image[i] += 1

        # Add extra penalty for images with multiple missed corners
        # This encourages the model to recover all corners in an image
        for i in range(batch_size):
            if missed_corners_per_image[i] > 1:
                # Quadratic penalty for multiple missed corners
                recovery_penalty = self.recovery_factor * torch.pow(missed_corners_per_image[i], 2)
                loss += recovery_penalty

        # Average loss over all corners
        loss = loss / (batch_size * num_corners)

        # Ensure loss is not too large
        loss = torch.clamp(loss, max=100.0)

        return self.weight * loss


class EnhancedCornerFocusedHeatmapLossV5(nn.Module):
    """
    Enhanced loss function for corner heatmap prediction (v5).
    This improved version includes:
    1. More aggressive peak-to-second ratio loss with higher weight (15.0)
    2. Enhanced detection rate loss with recovery mechanism (8.0)
    3. Improved robust segmentation guidance (2.0)
    4. Balanced component weights for better optimization
    """
    def __init__(self,
                 separation_weight=1.0,
                 peak_separation_weight=0.8,
                 edge_suppression_weight=1.0,
                 peak_enhancement_weight=1.0,
                 peak_to_second_ratio_weight=15.0,
                 detection_rate_weight=8.0,
                 segmentation_guidance_weight=2.0,
                 use_curriculum=True):
        super(EnhancedCornerFocusedHeatmapLossV5, self).__init__()
        self.separation_weight = separation_weight
        self.peak_separation_weight = peak_separation_weight
        self.edge_suppression_weight = edge_suppression_weight
        self.peak_enhancement_weight = peak_enhancement_weight
        self.peak_to_second_ratio_weight = peak_to_second_ratio_weight
        self.detection_rate_weight = detection_rate_weight
        self.segmentation_guidance_weight = segmentation_guidance_weight
        self.use_curriculum = use_curriculum
        self.current_epoch = 0

        # Enhanced components with improved parameters
        if use_curriculum:
            self.peak_to_second_ratio_loss = CurriculumPeakToSecondRatioLoss(
                weight=1.0,
                min_target=1.2,
                max_target=2.0,
                curriculum_epochs=30
            )
        else:
            self.peak_to_second_ratio_loss = EnhancedPeakToSecondRatioLoss(weight=1.0, target_ratio=2.0)

        self.detection_rate_loss = EnhancedDetectionRateLoss(weight=1.0, detection_threshold=0.5, recovery_factor=2.0)
        self.segmentation_guidance_loss = RobustSegmentationGuidanceLoss(weight=1.0, boundary_width=5)

        self.eps = 1e-6

    def set_epoch(self, epoch):
        """Update the current epoch for curriculum learning."""
        self.current_epoch = epoch
        if self.use_curriculum:
            self.peak_to_second_ratio_loss.set_epoch(epoch)

    def forward(self, pred_heatmaps, target_heatmaps, segmentation=None):
        """
        Calculate the loss between predicted and target heatmaps.

        Args:
            pred_heatmaps: Predicted heatmaps (B, 4, H, W)
            target_heatmaps: Target heatmaps (B, 4, H, W)
            segmentation: Optional segmentation mask (B, 1, H, W)

        Returns:
            loss: Total loss
            components: Dictionary of loss components
        """
        batch_size = pred_heatmaps.size(0)

        # Base MSE loss
        mse_loss = F.mse_loss(pred_heatmaps, target_heatmaps)

        # Separation loss (penalize activations in non-corner regions)
        separation_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(batch_size):
            for c in range(4):
                # Get the target heatmap for this corner
                target_hm = target_heatmaps[i, c]

                # Get the predicted heatmap
                pred_hm = pred_heatmaps[i, c]

                # Create a mask for non-corner regions (where target is close to 0)
                non_corner_mask = (target_hm < 0.1).float()

                # Penalize activations in non-corner regions
                # Use absolute value to ensure positive loss
                activation_penalty = torch.sum(pred_hm * non_corner_mask)
                separation_loss += torch.abs(activation_penalty)

        # Ensure positive loss and normalize
        separation_loss = torch.clamp(separation_loss, min=0.0) / (batch_size * 4)

        # Peak separation loss (ensure peaks are well-separated)
        peak_separation_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(batch_size):
            # For each pair of corners
            for c1 in range(4):
                for c2 in range(c1+1, 4):
                    # Get the predicted heatmaps
                    hm1 = pred_heatmaps[i, c1]
                    hm2 = pred_heatmaps[i, c2]

                    # Penalize overlap between heatmaps
                    overlap = torch.sum(hm1 * hm2)
                    peak_separation_loss += overlap

        peak_separation_loss = peak_separation_loss / (batch_size * 6)  # 6 pairs of corners

        # Edge suppression loss (penalize activations at image edges)
        edge_suppression_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        h, w = pred_heatmaps.shape[2], pred_heatmaps.shape[3]
        edge_width = 5

        # Create edge mask
        edge_mask = torch.zeros((h, w), device=pred_heatmaps.device)
        edge_mask[:edge_width, :] = 1.0  # Top edge
        edge_mask[-edge_width:, :] = 1.0  # Bottom edge
        edge_mask[:, :edge_width] = 1.0  # Left edge
        edge_mask[:, -edge_width:] = 1.0  # Right edge

        for i in range(batch_size):
            for c in range(4):
                # Get the predicted heatmap
                pred_hm = pred_heatmaps[i, c]

                # Penalize activations at edges (ensure positive)
                edge_penalty = torch.sum(pred_hm * edge_mask)
                edge_suppression_loss += torch.abs(edge_penalty)

        # Ensure positive loss and normalize
        edge_suppression_loss = torch.clamp(edge_suppression_loss, min=0.0) / (batch_size * 4)

        # Peak enhancement loss (encourage sharp peaks)
        peak_enhancement_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        for i in range(batch_size):
            for c in range(4):
                # Get the target heatmap
                target_hm = target_heatmaps[i, c]

                # Get the predicted heatmap
                pred_hm = pred_heatmaps[i, c]

                # Create a mask for corner regions (where target is high)
                corner_mask = (target_hm > 0.5).float()

                # Penalize if predicted values in corner regions are not high enough
                peak_enhancement_loss += torch.sum((1.0 - pred_hm) * corner_mask)

        peak_enhancement_loss = peak_enhancement_loss / (batch_size * 4)

        # Enhanced peak-to-second ratio loss
        peak_to_second_ratio_loss = self.peak_to_second_ratio_loss(pred_heatmaps)

        # Enhanced detection rate loss
        detection_rate_loss = self.detection_rate_loss(pred_heatmaps)

        # Add segmentation guidance loss if segmentation is provided
        segmentation_guidance_loss = torch.tensor(0.0, device=pred_heatmaps.device)
        if segmentation is not None:
            segmentation_guidance_loss = self.segmentation_guidance_loss(pred_heatmaps, segmentation)

        # Ensure all loss components are non-negative and not too large
        separation_loss = torch.clamp(separation_loss, min=0.0, max=1000.0)
        peak_separation_loss = torch.clamp(peak_separation_loss, min=0.0, max=1000.0)
        edge_suppression_loss = torch.clamp(edge_suppression_loss, min=0.0, max=1000.0)
        peak_enhancement_loss = torch.clamp(peak_enhancement_loss, min=0.0, max=1000.0)
        peak_to_second_ratio_loss = torch.clamp(peak_to_second_ratio_loss, min=0.0, max=1000.0)
        detection_rate_loss = torch.clamp(detection_rate_loss, min=0.0, max=1000.0)
        segmentation_guidance_loss = torch.clamp(segmentation_guidance_loss, min=0.0, max=1000.0)

        # Print warning for any extremely large loss components
        if peak_to_second_ratio_loss > 100.0:
            print(f"WARNING: Large peak_to_second_ratio_loss: {peak_to_second_ratio_loss.item()}")

        # Combine all losses with weights
        total_loss = (mse_loss +
                     self.separation_weight * separation_loss +
                     self.peak_separation_weight * peak_separation_loss +
                     self.edge_suppression_weight * edge_suppression_loss +
                     self.peak_enhancement_weight * peak_enhancement_loss +
                     self.peak_to_second_ratio_weight * peak_to_second_ratio_loss +
                     self.detection_rate_weight * detection_rate_loss +
                     self.segmentation_guidance_weight * segmentation_guidance_loss)

        # Check for NaN or Inf values and replace with a safe value
        if torch.isnan(total_loss) or torch.isinf(total_loss):
            print("WARNING: NaN or Inf detected in loss calculation. Using fallback loss.")
            total_loss = mse_loss

        return total_loss, {
            'mse_loss': mse_loss.item(),
            'separation_loss': separation_loss.item(),
            'peak_separation_loss': peak_separation_loss.item(),
            'edge_suppression_loss': edge_suppression_loss.item(),
            'peak_enhancement_loss': peak_enhancement_loss.item(),
            'peak_to_second_ratio_loss': peak_to_second_ratio_loss.item(),
            'detection_rate_loss': detection_rate_loss.item(),
            'segmentation_guidance_loss': segmentation_guidance_loss.item() if segmentation is not None else 0.0
        }
