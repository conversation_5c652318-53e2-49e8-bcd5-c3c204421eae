"""
Efficient U-Net V3: Optimized architecture with reduced parameters while maintaining performance.
Key improvements:
- Lightweight attention mechanisms
- Depthwise separable convolutions
- Efficient channel dimensions
- Ghost modules for parameter reduction
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class DepthwiseSeparableConv(nn.Module):
    """Depthwise separable convolution for parameter reduction."""

    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super().__init__()
        self.depthwise = nn.Conv2d(
            in_channels, in_channels, kernel_size, stride, padding,
            groups=in_channels, bias=False
        )
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1, bias=False)
        self.bn = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU(inplace=True)

    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        x = self.bn(x)
        x = self.relu(x)
        return x

class LightweightAttention(nn.Module):
    """Lightweight attention mechanism with reduced parameters."""

    def __init__(self, in_channels, reduction=8):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        # Reduced channel attention
        self.fc = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // reduction, 1, bias=False),
            nn.ReLU(inplace=True),
            nn.Conv2d(in_channels // reduction, in_channels, 1, bias=False),
            nn.Sigmoid()
        )

        # Lightweight spatial attention
        self.spatial_conv = nn.Conv2d(2, 1, 7, padding=3, bias=False)
        self.spatial_sigmoid = nn.Sigmoid()

    def forward(self, x):
        # Channel attention
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        channel_att = avg_out + max_out
        x = x * channel_att

        # Spatial attention
        avg_out = torch.mean(x, dim=1, keepdim=True)
        max_out, _ = torch.max(x, dim=1, keepdim=True)
        spatial_input = torch.cat([avg_out, max_out], dim=1)
        spatial_att = self.spatial_sigmoid(self.spatial_conv(spatial_input))
        x = x * spatial_att

        return x

class GhostModule(nn.Module):
    """Ghost module for generating more features with fewer parameters."""

    def __init__(self, in_channels, out_channels, kernel_size=1, ratio=2, dw_size=3):
        super().__init__()
        self.out_channels = out_channels
        init_channels = math.ceil(out_channels / ratio)
        new_channels = init_channels * (ratio - 1)

        self.primary_conv = nn.Sequential(
            nn.Conv2d(in_channels, init_channels, kernel_size, 1, kernel_size//2, bias=False),
            nn.BatchNorm2d(init_channels),
            nn.ReLU(inplace=True)
        )

        self.cheap_operation = nn.Sequential(
            nn.Conv2d(init_channels, new_channels, dw_size, 1, dw_size//2, groups=init_channels, bias=False),
            nn.BatchNorm2d(new_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        x1 = self.primary_conv(x)
        x2 = self.cheap_operation(x1)
        out = torch.cat([x1, x2], dim=1)
        return out[:, :self.out_channels, :, :]

class EfficientDoubleConv(nn.Module):
    """Efficient double convolution with reduced parameters."""

    def __init__(self, in_channels, out_channels, use_ghost=True, use_attention=True):
        super().__init__()
        mid_channels = out_channels // 2

        if use_ghost:
            self.conv1 = GhostModule(in_channels, mid_channels)
            self.conv2 = GhostModule(mid_channels, out_channels)
        else:
            self.conv1 = DepthwiseSeparableConv(in_channels, mid_channels)
            self.conv2 = DepthwiseSeparableConv(mid_channels, out_channels)

        self.use_attention = use_attention
        if use_attention:
            self.attention = LightweightAttention(out_channels)

        # Lightweight residual connection
        self.residual = None
        if in_channels != out_channels:
            self.residual = nn.Conv2d(in_channels, out_channels, 1, bias=False)

    def forward(self, x):
        identity = x

        out = self.conv1(x)
        out = self.conv2(out)

        if self.use_attention:
            out = self.attention(out)

        # Residual connection
        if self.residual is not None:
            identity = self.residual(identity)

        if identity.shape == out.shape:
            out = out + identity

        return out

class EfficientDown(nn.Module):
    """Efficient downscaling."""

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            EfficientDoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)

class EfficientUp(nn.Module):
    """Efficient upscaling."""

    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()

        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = EfficientDoubleConv(in_channels, out_channels, use_ghost=False)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = EfficientDoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)

        # Handle size mismatch
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]

        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])

        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

class EfficientUNetV3(nn.Module):
    """
    Efficient U-Net V3 with reduced parameters:
    - Depthwise separable convolutions
    - Ghost modules
    - Lightweight attention
    - Optimized channel dimensions
    """

    def __init__(self, n_channels=3, n_classes=1, bilinear=True, base_channels=32):
        super(EfficientUNetV3, self).__init__()
        self.n_channels = n_channels
        self.n_classes = n_classes
        self.bilinear = bilinear

        # Reduced channel dimensions for efficiency
        c1, c2, c3, c4, c5 = base_channels, base_channels*2, base_channels*4, base_channels*8, base_channels*16

        # Encoder
        self.inc = EfficientDoubleConv(n_channels, c1)
        self.down1 = EfficientDown(c1, c2)
        self.down2 = EfficientDown(c2, c3)
        self.down3 = EfficientDown(c3, c4)

        factor = 2 if bilinear else 1
        self.down4 = EfficientDown(c4, c5 // factor)

        # Decoder
        self.up1 = EfficientUp(c5, c4 // factor, bilinear)
        self.up2 = EfficientUp(c4, c3 // factor, bilinear)
        self.up3 = EfficientUp(c3, c2 // factor, bilinear)
        self.up4 = EfficientUp(c2, c1, bilinear)

        # Output head
        self.outc = nn.Conv2d(c1, n_classes, kernel_size=1)

        # Initialize weights
        self._initialize_weights()

    def _initialize_weights(self):
        """Initialize weights for better convergence."""
        for m in self.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode='fan_out', nonlinearity='relu')
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
            elif isinstance(m, nn.BatchNorm2d):
                nn.init.constant_(m.weight, 1)
                nn.init.constant_(m.bias, 0)

    def forward(self, x):
        # Encoder
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)

        # Decoder
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)

        logits = self.outc(x)
        return logits

def get_efficient_model(model_type="efficient_v3", n_channels=3, n_classes=1, base_channels=32):
    """Get the efficient U-Net V3 model."""
    return EfficientUNetV3(
        n_channels=n_channels,
        n_classes=n_classes,
        bilinear=True,
        base_channels=base_channels
    )

class ModelSizeAnalyzer:
    """Analyze model size and complexity."""

    @staticmethod
    def count_parameters(model):
        """Count total and trainable parameters."""
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        return total_params, trainable_params

    @staticmethod
    def estimate_memory(model, input_size=(1, 3, 512, 512)):
        """Estimate memory usage."""
        # Estimate memory (rough calculation)
        param_memory = sum(p.numel() * 4 for p in model.parameters()) / (1024**2)  # MB
        return param_memory

    @staticmethod
    def compare_models():
        """Compare different model configurations."""
        configs = [
            ("V1 Standard", None, 17262977),
            ("V2 Enhanced", None, 41036150),
            ("V3 Efficient-16", 16, None),
            ("V3 Efficient-24", 24, None),
            ("V3 Efficient-32", 32, None),
            ("V3 Efficient-48", 48, None),
        ]

        print("Model Comparison:")
        print("-" * 60)
        print(f"{'Model':<20} {'Parameters':<12} {'Memory (MB)':<12} {'Efficiency'}")
        print("-" * 60)

        for name, base_channels, known_params in configs:
            if known_params:
                params = known_params
                memory = params * 4 / (1024**2)
                efficiency = "Reference"
            else:
                model = get_efficient_model(base_channels=base_channels)
                params, _ = ModelSizeAnalyzer.count_parameters(model)
                memory = ModelSizeAnalyzer.estimate_memory(model)
                efficiency = f"{params/17262977:.2f}x"

            print(f"{name:<20} {params:>11,} {memory:>8.1f} MB {efficiency:>10}")

if __name__ == "__main__":
    # Test the efficient model
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

    print("🚀 Testing Efficient U-Net V3 Models...")
    print("=" * 60)

    # Test different configurations
    base_channels_options = [16, 24, 32, 48]

    for base_channels in base_channels_options:
        print(f"\n📊 Testing V3 with base_channels={base_channels}")

        model = get_efficient_model(base_channels=base_channels)
        model = model.to(device)

        # Count parameters
        total_params, trainable_params = ModelSizeAnalyzer.count_parameters(model)

        # Test forward pass
        x = torch.randn(2, 3, 512, 512).to(device)

        with torch.no_grad():
            output = model(x)

        # Calculate efficiency metrics
        v1_params = 17262977
        v2_params = 41036150
        efficiency_vs_v1 = total_params / v1_params
        efficiency_vs_v2 = total_params / v2_params

        print(f"  Parameters: {total_params:,}")
        print(f"  Efficiency vs V1: {efficiency_vs_v1:.2f}x")
        print(f"  Efficiency vs V2: {efficiency_vs_v2:.2f}x")
        print(f"  Output shape: {output.shape}")

        # Memory estimation
        memory_mb = ModelSizeAnalyzer.estimate_memory(model)
        print(f"  Estimated memory: {memory_mb:.1f} MB")

    print("\n" + "=" * 60)
    print("📈 Model Comparison Summary:")
    ModelSizeAnalyzer.compare_models()

    print("\n🎯 Recommended Configuration:")
    print("  - V3 Efficient-32: Good balance of parameters and capacity")
    print("  - V3 Efficient-24: More efficient, suitable for mobile")
    print("  - V3 Efficient-48: Higher capacity if needed")
