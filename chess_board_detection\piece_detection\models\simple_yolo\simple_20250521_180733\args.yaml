task: detect
mode: train
model: yolo11n.pt
data: piece_detection/enhanced_dataset_99plus/dataset.yaml
epochs: 100
time: null
patience: 20
batch: 16
imgsz: 416
save: true
save_period: 5
cache: false
device: '0'
workers: 8
project: piece_detection\models\simple_yolo
name: simple_20250521_180733
exist_ok: true
pretrained: true
optimizer: AdamW
verbose: true
seed: 42
deterministic: true
single_cls: false
rect: false
cos_lr: false
close_mosaic: 10
resume: false
amp: true
fraction: 1.0
profile: false
freeze: null
multi_scale: false
overlap_mask: true
mask_ratio: 4
dropout: 0.0
val: true
split: val
save_json: false
conf: 0.001
iou: 0.7
max_det: 300
half: false
dnn: false
plots: true
source: null
vid_stride: 1
stream_buffer: false
visualize: false
augment: false
agnostic_nms: false
classes: null
retina_masks: false
embed: null
show: false
save_frames: false
save_txt: false
save_conf: false
save_crop: false
show_labels: true
show_conf: true
show_boxes: true
line_width: null
format: torchscript
keras: false
optimize: false
int8: false
dynamic: false
simplify: true
opset: null
workspace: null
nms: false
lr0: 0.001
lrf: 0.01
momentum: 0.9
weight_decay: 0.0005
warmup_epochs: 3.0
warmup_momentum: 0.8
warmup_bias_lr: 0.1
box: 7.5
cls: 0.5
dfl: 1.5
pose: 12.0
kobj: 1.0
nbs: 64
hsv_h: 0
hsv_s: 0
hsv_v: 0
degrees: 0
translate: 0
scale: 1.0
shear: 0
perspective: 0
flipud: 0
fliplr: 0
bgr: 0.0
mosaic: 0
mixup: 0
cutmix: 0.0
copy_paste: 0
copy_paste_mode: flip
auto_augment: randaugment
erasing: 0.4
cfg: null
tracker: botsort.yaml
save_dir: piece_detection\models\simple_yolo\simple_20250521_180733
