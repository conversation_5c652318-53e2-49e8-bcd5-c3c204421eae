"""
<PERSON><PERSON><PERSON> to extract and display the exact pixel coordinates of detected corners
from both models on the original test image.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import pandas as pd
from tabulate import tabulate

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for model input."""
    # Load and resize the image
    image = Image.open(image_path).convert('RGB')
    original_image = np.array(image)
    original_size = image.size  # (width, height)

    # Resize while maintaining aspect ratio
    w, h = image.size
    ratio = min(target_size[0] / w, target_size[1] / h)
    new_size = (int(w * ratio), int(h * ratio))
    image = image.resize(new_size, Image.LANCZOS)

    # Create a black canvas of target size
    canvas = Image.new('RGB', target_size, (0, 0, 0))
    # Paste the resized image in the center
    offset = ((target_size[0] - new_size[0]) // 2, (target_size[1] - new_size[1]) // 2)
    canvas.paste(image, offset)

    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    input_tensor = transform(canvas).unsqueeze(0)

    return input_tensor, original_image, offset, ratio, original_size

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)

    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']

    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)

    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []

    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))

    return segmentation, heatmaps, keypoints

def map_keypoints_to_original(keypoints, offset, ratio, original_size):
    """Map keypoints from model input space to original image space."""
    mapped_keypoints = []

    for x, y, conf in keypoints:
        # Remove offset
        x_no_offset = x - offset[0]
        y_no_offset = y - offset[1]

        # Scale back to original size
        orig_x = x_no_offset / ratio
        orig_y = y_no_offset / ratio

        # Ensure coordinates are within image bounds
        orig_x = max(0, min(orig_x, original_size[0] - 1))
        orig_y = max(0, min(orig_y, original_size[1] - 1))

        mapped_keypoints.append((orig_x, orig_y, conf))

    return mapped_keypoints

def visualize_corner_coordinates(original_image, keypoints_dict, output_path):
    """Create a visualization with corner coordinates overlaid on the original image."""
    # Create a copy of the original image for drawing
    vis_img = original_image.copy()

    # Convert to RGB if it's BGR
    if vis_img.shape[2] == 3 and vis_img[0,0,0] > vis_img[0,0,2]:  # Simple check for BGR
        vis_img = cv2.cvtColor(vis_img, cv2.COLOR_BGR2RGB)

    # Create figure
    plt.figure(figsize=(12, 10))
    plt.imshow(vis_img)

    # Define colors and markers for different models
    model_styles = {
        "Phase2_Epoch16": {"color": "red", "marker": "x", "linestyle": "-", "linewidth": 2},
        "Phase3_Epoch8": {"color": "blue", "marker": "+", "linestyle": "--", "linewidth": 2}
    }

    # Corner names
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Plot keypoints and lines for each model
    legend_elements = []

    for model_name, keypoints in keypoints_dict.items():
        style = model_styles[model_name]

        # Plot keypoints
        xs = []
        ys = []
        for i, (x, y, conf) in enumerate(keypoints):
            xs.append(x)
            ys.append(y)

            # Plot keypoint
            plt.scatter(x, y, c=style["color"], marker=style["marker"], s=100, linewidths=2)

            # Add label with coordinates and confidence
            plt.text(x+10, y+10, f"{corner_names[i]}\n({int(x)}, {int(y)})\nConf: {conf:.3f}",
                     color=style["color"], fontsize=10,
                     bbox=dict(facecolor='white', alpha=0.7))

        # Add the first point again to close the polygon
        xs.append(xs[0])
        ys.append(ys[0])

        # Plot lines connecting the keypoints
        plt.plot(xs, ys, color=style["color"], linestyle=style["linestyle"],
                 linewidth=style["linewidth"], alpha=0.7)

        # Add to legend
        legend_elements.append(plt.Line2D([0], [0], color=style["color"], linestyle=style["linestyle"],
                                         marker=style["marker"], markersize=10, linewidth=2,
                                         label=f"{model_name}"))

    # Add legend
    plt.legend(handles=legend_elements, loc='upper right')

    # Add title
    plt.title("Chess Board Corner Detection with Pixel Coordinates", fontsize=16)

    # Remove axes
    plt.axis('off')

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

    return output_path

def create_corner_coordinates_table(keypoints_dict, output_path):
    """Create a table with corner coordinates and save it as CSV and text."""
    # Corner names
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Create data for table
    table_data = []

    for model_name, keypoints in keypoints_dict.items():
        for i, (x, y, conf) in enumerate(keypoints):
            table_data.append({
                'Model': model_name,
                'Corner': corner_names[i],
                'X': int(x),
                'Y': int(y),
                'Confidence': round(conf, 4)
            })

    # Create DataFrame
    df = pd.DataFrame(table_data)

    # Save as CSV
    csv_path = output_path.replace('.png', '.csv')
    df.to_csv(csv_path, index=False)

    # Create text table
    text_table = tabulate(df, headers='keys', tablefmt='grid')

    # Save as text
    txt_path = output_path.replace('.png', '.txt')
    with open(txt_path, 'w') as f:
        f.write("Chess Board Corner Detection - Pixel Coordinates\n")
        f.write("==============================================\n\n")
        f.write(text_table)

    return csv_path, txt_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs"

    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }

    os.makedirs(output_dir, exist_ok=True)

    # Process each model
    keypoints_dict = {}

    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name}...")

        # Load model
        model = load_model(model_path)

        # Preprocess image
        input_tensor, original_image, offset, ratio, original_size = preprocess_image(image_path)

        # Detect corners
        _, _, keypoints = detect_corners(model, input_tensor)

        # Map keypoints back to original image space
        mapped_keypoints = map_keypoints_to_original(keypoints, offset, ratio, original_size)

        # Store keypoints
        keypoints_dict[model_name] = mapped_keypoints

    # Create visualization
    output_path = os.path.join(output_dir, "corner_coordinates_visualization.png")
    visualize_corner_coordinates(original_image, keypoints_dict, output_path)

    print(f"Corner coordinates visualization saved to: {output_path}")

    # Create table
    csv_path, txt_path = create_corner_coordinates_table(keypoints_dict, output_path)

    print(f"Corner coordinates table saved to: {csv_path} and {txt_path}")

    # Print the table to console
    with open(txt_path, 'r') as f:
        print(f.read())

    print("All outputs completed!")

if __name__ == "__main__":
    main()
