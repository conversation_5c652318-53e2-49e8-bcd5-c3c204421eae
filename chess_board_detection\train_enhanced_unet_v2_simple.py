"""
Simplified Enhanced U-Net V2 Training Script for initial testing.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.enhanced_unet_v2 import get_enhanced_model
from chess_board_detection.dataset.enhanced_segmentation_dataset_v2 import create_enhanced_dataloaders_v2
from chess_board_detection.losses.advanced_losses_v2 import get_loss_function

def calculate_metrics(predictions, targets, threshold=0.5):
    """Calculate basic metrics."""
    predictions = (torch.sigmoid(predictions) > threshold).float()
    targets = (targets > threshold).float()
    
    # Flatten
    pred_flat = predictions.view(-1)
    target_flat = targets.view(-1)
    
    # IoU
    intersection = (pred_flat * target_flat).sum()
    union = pred_flat.sum() + target_flat.sum() - intersection
    iou = (intersection + 1e-6) / (union + 1e-6)
    
    # Dice
    dice = (2 * intersection + 1e-6) / (pred_flat.sum() + target_flat.sum() + 1e-6)
    
    return {
        'iou': iou.item(),
        'dice': dice.item()
    }

def train_epoch_simple(model, train_loader, criterion, optimizer, scaler, device):
    """Simple training epoch."""
    model.train()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(train_loader)
    
    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device, non_blocking=True)
        masks = masks.to(device, non_blocking=True)
        
        # Add channel dimension if needed
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)
        
        optimizer.zero_grad()
        
        with autocast():
            outputs = model(images)
            
            # Handle deep supervision outputs
            if isinstance(outputs, tuple):
                main_output = outputs[0]
                loss, loss_metrics = criterion(outputs, masks)
            else:
                main_output = outputs
                loss, loss_metrics = criterion(outputs, masks)
        
        # Backward pass
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        
        # Calculate metrics
        with torch.no_grad():
            batch_metrics = calculate_metrics(main_output, masks)
        
        total_loss += loss.item()
        total_iou += batch_metrics['iou']
        total_dice += batch_metrics['dice']
        
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Dice': f'{batch_metrics["dice"]:.4f}',
            'IoU': f'{batch_metrics["iou"]:.4f}'
        })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def validate_epoch_simple(model, val_loader, criterion, device):
    """Simple validation epoch."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            
            if masks.dim() == 3:
                masks = masks.unsqueeze(1)
            
            outputs = model(images)
            
            # Handle deep supervision outputs
            if isinstance(outputs, tuple):
                main_output = outputs[0]
                loss, loss_metrics = criterion(outputs, masks)
            else:
                main_output = outputs
                loss, loss_metrics = criterion(outputs, masks)
            
            # Calculate metrics
            batch_metrics = calculate_metrics(main_output, masks)
            
            total_loss += loss.item()
            total_iou += batch_metrics['iou']
            total_dice += batch_metrics['dice']
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{batch_metrics["dice"]:.4f}',
                'IoU': f'{batch_metrics["iou"]:.4f}'
            })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def train_enhanced_unet_v2_simple():
    """Simple training function for Enhanced U-Net V2."""
    
    # Configuration
    config = {
        'dataset_dir': r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326",
        'save_dir': "chess_board_detection/enhanced_unet_v2_simple_results",
        'epochs': 30,
        'batch_size': 2,  # Small batch for 6GB GPU with 512x512
        'learning_rate': 1e-4,
        'image_size': (512, 512),
        'num_workers': 0,
    }
    
    print("Starting Enhanced U-Net V2 Simple Training...")
    print(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    # Create save directory
    save_dir = Path(config['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Create dataloaders (single fold for simplicity)
    print("Creating dataloaders...")
    train_loader, val_loader, train_dataset, val_dataset = create_enhanced_dataloaders_v2(
        config['dataset_dir'],
        fold_idx=0,
        n_folds=5,
        batch_size=config['batch_size'],
        image_size=config['image_size'],
        num_workers=config['num_workers'],
        use_mixup_cutmix=False,  # Disable for simplicity
        progressive_resize=False,  # Disable for simplicity
    )
    
    # Create model
    print("Creating Enhanced U-Net V2...")
    model = get_enhanced_model(
        model_type="enhanced_v2",
        n_channels=3,
        n_classes=1,
        deep_supervision=True
    )
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,}")
    
    # Loss function (simplified)
    criterion = get_loss_function(
        loss_type='focal_dice',  # Simpler loss
        deep_supervision=True,
        adaptive=False  # Disable for simplicity
    )
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=1e-4
    )
    
    # Scheduler
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(
        optimizer, mode='min', patience=5, factor=0.5
    )
    
    # Mixed precision scaler
    scaler = GradScaler()
    
    # Training history
    history = {
        'train_loss': [], 'val_loss': [],
        'train_dice': [], 'val_dice': [],
        'train_iou': [], 'val_iou': []
    }
    
    best_val_dice = 0
    patience_counter = 0
    patience = 10
    
    print(f"Starting training for {config['epochs']} epochs...")
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        print(f"\nEpoch {epoch+1}/{config['epochs']}")
        
        # Train
        train_loss, train_iou, train_dice = train_epoch_simple(
            model, train_loader, criterion, optimizer, scaler, device
        )
        
        # Validate
        val_loss, val_iou, val_dice = validate_epoch_simple(
            model, val_loader, criterion, device
        )
        
        # Update scheduler
        scheduler.step(val_loss)
        current_lr = optimizer.param_groups[0]['lr']
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_dice'].append(train_dice)
        history['val_dice'].append(val_dice)
        history['train_iou'].append(train_iou)
        history['val_iou'].append(val_iou)
        
        # Print results
        print(f"Train - Loss: {train_loss:.4f}, Dice: {train_dice:.4f}, IoU: {train_iou:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}, IoU: {val_iou:.4f}")
        print(f"LR: {current_lr:.6f}")
        
        # Save best model
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            torch.save(model.state_dict(), save_dir / "best_model.pth")
            print(f"New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'val_dice': val_dice,
                'history': history
            }, save_dir / f"checkpoint_epoch_{epoch+1}.pth")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"Early stopping triggered after {patience} epochs without improvement")
            break
    
    # Save final results
    torch.save(model.state_dict(), save_dir / "final_model.pth")
    
    with open(save_dir / "training_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time/3600:.2f} hours")
    print(f"Best validation Dice: {best_val_dice:.4f}")
    print(f"Results saved to: {save_dir}")
    
    return model, history, best_val_dice

if __name__ == "__main__":
    try:
        model, history, best_dice = train_enhanced_unet_v2_simple()
        print(f"\n🎉 Enhanced U-Net V2 training completed successfully!")
        print(f"🏆 Best Dice Score: {best_dice:.4f}")
        
        # Compare with V1
        print(f"\n📊 Comparison with V1:")
        print(f"V1 Best Dice: 0.9100")
        print(f"V2 Best Dice: {best_dice:.4f}")
        improvement = ((best_dice - 0.9100) / 0.9100) * 100
        print(f"Improvement: {improvement:+.2f}%")
        
    except Exception as e:
        print(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()
