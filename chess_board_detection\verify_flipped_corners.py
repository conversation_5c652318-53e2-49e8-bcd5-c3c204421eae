"""
<PERSON><PERSON><PERSON> to verify that flipped images have correctly labeled corners.
This script finds flipped images in the dataset and verifies that the corner labels are correct.
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
import argparse
from tqdm import tqdm

def load_pt_image(pt_file):
    """Load a PyTorch tensor image file and convert to numpy for visualization"""
    tensor = torch.load(pt_file)

    # Denormalize if needed
    if tensor.max() <= 1.0:
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        tensor = tensor * std + mean

    # Convert to numpy and ensure proper range
    img = tensor.permute(1, 2, 0).numpy()
    img = np.clip(img * 255, 0, 255).astype(np.uint8)
    return img

def load_pt_corners(pt_file):
    """Load PyTorch tensor corners"""
    tensor = torch.load(pt_file)
    return tensor.numpy()

def check_flipped_sample(sample_dir):
    """Check if a sample has flipped corners and verify the labels"""
    # Check if corner_orientation.json exists
    orientation_file = os.path.join(sample_dir, "corner_orientation.json")
    if not os.path.exists(orientation_file):
        return None

    # Load corner orientation
    with open(orientation_file, 'r') as f:
        orientation = json.load(f)

    # Check if flipped
    if not orientation.get("is_flipped", False):
        return None

    # This is a flipped sample, verify the corners
    pt_image = load_pt_image(os.path.join(sample_dir, "image.pt"))
    pt_corners = load_pt_corners(os.path.join(sample_dir, "corners.pt"))

    # Create verification image
    plt.figure(figsize=(10, 8))
    plt.imshow(pt_image)

    # Add corners with labels
    corner_names = orientation.get("corner_names", ["TL", "TR", "BR", "BL"])
    # Use consistent colors for each corner type
    # TL: Blue, TR: Green, BR: Red, BL: Cyan
    colors = ['blue', 'green', 'red', 'cyan']

    for i in range(0, len(pt_corners), 2):
        if i//2 < len(corner_names):
            x, y = pt_corners[i], pt_corners[i+1]
            plt.plot(x, y, 'o', color=colors[i//2], markersize=10)
            plt.text(x+5, y+5, corner_names[i//2], color='white', fontsize=14,
                    bbox=dict(facecolor='black', alpha=0.7))

    # Add title with flipped information
    plt.title(f"Flipped Sample: {os.path.basename(sample_dir)}\nOriginal Indices: {orientation.get('original_indices')}")
    plt.axis('off')

    # Save verification image
    verification_path = os.path.join(sample_dir, "flipped_verification.jpg")
    plt.savefig(verification_path)
    plt.close()

    return {
        "sample_dir": sample_dir,
        "verification_path": verification_path,
        "orientation": orientation
    }

def main():
    parser = argparse.ArgumentParser(description="Verify flipped images have correct corner labels")
    parser.add_argument("--dataset_dir", type=str, default="data/augmented/v5.2/augmented_20250518_145831",
                        help="Path to the augmented dataset directory")

    args = parser.parse_args()

    # Get all sample directories
    sample_dirs = []
    for root, dirs, files in os.walk(args.dataset_dir):
        if "sample_" in os.path.basename(root) and "image.pt" in files:
            sample_dirs.append(root)

    print(f"Found {len(sample_dirs)} samples. Checking for flipped images...")

    # Check each sample for flipped corners
    flipped_samples = []
    for sample_dir in tqdm(sample_dirs):
        result = check_flipped_sample(sample_dir)
        if result:
            flipped_samples.append(result)

    print(f"Found {len(flipped_samples)} flipped samples.")

    # Create a summary of flipped samples
    if flipped_samples:
        # Create a grid of flipped samples
        num_samples = min(16, len(flipped_samples))
        rows = int(np.ceil(np.sqrt(num_samples)))
        cols = int(np.ceil(num_samples / rows))

        plt.figure(figsize=(cols * 5, rows * 4))

        for i, sample in enumerate(flipped_samples[:num_samples]):
            plt.subplot(rows, cols, i + 1)
            img = cv2.imread(sample["verification_path"])
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            plt.imshow(img)
            plt.title(f"Sample {i+1}")
            plt.axis('off')

        plt.tight_layout()
        summary_path = os.path.join(args.dataset_dir, "flipped_samples_summary.jpg")
        plt.savefig(summary_path)
        plt.close()

        print(f"Summary of flipped samples saved to: {summary_path}")

        # Create a text report
        report_path = os.path.join(args.dataset_dir, "flipped_samples_report.txt")
        with open(report_path, 'w') as f:
            f.write(f"Flipped Samples Report\n")
            f.write(f"====================\n\n")
            f.write(f"Total samples: {len(sample_dirs)}\n")
            f.write(f"Flipped samples: {len(flipped_samples)}\n\n")

            for i, sample in enumerate(flipped_samples):
                f.write(f"Sample {i+1}: {os.path.basename(sample['sample_dir'])}\n")
                f.write(f"  - Original indices: {sample['orientation'].get('original_indices')}\n")
                f.write(f"  - Verification image: {os.path.basename(sample['verification_path'])}\n\n")

        print(f"Report of flipped samples saved to: {report_path}")

if __name__ == "__main__":
    main()
