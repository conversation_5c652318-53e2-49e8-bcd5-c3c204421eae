@echo off
echo Creating targeted augmented dataset for chess piece detection...
echo Focusing on problematic pieces: white_bishop, white_knight, white_queen, black_queen, black_bishop

python chess_board_detection/piece_detection/create_targeted_dataset.py ^
--source "chess_board_detection/piece_detection/enhanced_dataset_99plus" ^
--output "chess_board_detection/piece_detection/targeted_dataset" ^
--augmentations 15 ^
--pieces white_bishop white_knight white_queen black_queen black_bishop

echo.
echo After dataset creation, train the model with:
echo.
echo python -m ultralytics train --model yolo11n.pt --data chess_board_detection/piece_detection/targeted_dataset/dataset.yaml --epochs 100 --batch 16 --img 416 --patience 100 --lr0 0.01 --lrf 0.01 --momentum 0.937 --weight_decay 0.0005 --warmup_epochs 3.0 --warmup_momentum 0.8 --warmup_bias_lr 0.1 --mosaic 1.0 --mixup 0.5 --degrees 15.0 --translate 0.2 --scale 0.5 --shear 2.0 --fliplr 0.5 --perspective 0.0005 --hsv_h 0.015 --hsv_s 0.7 --hsv_v 0.4 --close_mosaic 50 --box 7.5 --cls 0.5 --dfl 1.5 --amp --cache --iou 0.7 --max_det 300
echo.
echo Or use our enhanced training script:
echo.
echo .\chess_board_detection\piece_detection\train_targeted_model.bat

pause
