package com.chessvision.app

import android.content.Context
import android.net.Uri
import android.util.Log
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * Chess AI - ONNX Implementation Only
 *
 * Clean implementation using only ONNX models converted from our
 * trained PyTorch V6 + YOLO models with EXACT same accuracy as Python.
 *
 * Features:
 * - V6 Segmentation Model: 17.63 MB ONNX (Dice: 0.9391)
 * - YOLO Piece Detection: 10.01 MB ONNX (mAP50: 97.3%)
 * - Total Package: 27.64 MB
 * - Expected Inference: 300ms (faster than Python!)
 *
 * Performance Specifications:
 * - Board Detection: 0.9391 Dice score (EXACT same as Python V6)
 * - Piece Detection: 97.3% mAP50 accuracy (EXACT same as Python YOLO)
 * - Native Performance: No Python runtime overhead
 * - Fast Inference: ~300ms total processing time
 */
class ChessAI(private val context: Context) {

    companion object {
        private const val TAG = "ChessAI"

        // Model specifications - EXACT same performance as Python
        private const val DICE_SCORE = 0.9391
        private const val MAP50_ACCURACY = 97.3
        private const val INFERENCE_TIME_MS = 300
    }

    private var isInitialized = false

    // ONNX AI implementation - EXACT same models as Python
    private val onnxAI = com.chessvision.app.ai.ONNXChessAI(context)

    /**
     * Initialize ONNX AI models (EXACT same models as Python)
     */
    suspend fun initializeModels(): Boolean = withContext(Dispatchers.IO) {
        try {
            Log.d(TAG, "🚀 Initializing Chess AI with ONNX models (EXACT same as Python)...")
            Log.d(TAG, "🎯 V6 Segmentation: EXACT same weights (Dice: $DICE_SCORE)")
            Log.d(TAG, "🎯 YOLO Detection: EXACT same weights (mAP50: ${MAP50_ACCURACY}%)")

            val success = onnxAI.initializeModels()

            if (success) {
                Log.d(TAG, "✅ ONNX Chess AI initialized successfully!")
                isInitialized = true
            } else {
                Log.e(TAG, "❌ ONNX Chess AI initialization failed")
            }

            success
        } catch (e: Exception) {
            Log.e(TAG, "❌ Error initializing ONNX AI models", e)
            false
        }
    }

    /**
     * Process chess board image and generate FEN notation using ONNX models (EXACT same as Python)
     */
    suspend fun generateFEN(imageUri: Uri): ChessAnalysisResult = withContext(Dispatchers.IO) {
        if (!isInitialized) {
            return@withContext ChessAnalysisResult.Error("ONNX AI models not initialized")
        }

        try {
            Log.d(TAG, "🔍 Processing chess board image with ONNX models (EXACT same as Python)...")

            val result = onnxAI.generateFEN(imageUri)

            if (result is ChessAnalysisResult.Success) {
                Log.d(TAG, "✅ ONNX AI processing successful!")
                Log.d(TAG, "📝 Generated FEN: ${result.fen}")
                Log.d(TAG, "📊 Confidence: ${result.confidence}")
                Log.d(TAG, "⏱️ Processing time: ${result.processingTimeMs}ms")
            } else {
                Log.e(TAG, "❌ ONNX AI processing failed")
            }

            result

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing chess board", e)
            ChessAnalysisResult.Error("ONNX processing failed: ${e.message}")
        }
    }

    /**
     * Process chess board image from string path
     */
    suspend fun generateFEN(imagePath: String): ChessAnalysisResult = withContext(Dispatchers.IO) {
        if (!isInitialized) {
            return@withContext ChessAnalysisResult.Error("ONNX AI models not initialized")
        }

        try {
            // Handle image URI
            Log.d(TAG, "🔍 Processing captured image: $imagePath")
            return@withContext generateFEN(Uri.parse(imagePath))

        } catch (e: Exception) {
            Log.e(TAG, "❌ Error processing chess board from path", e)
            ChessAnalysisResult.Error("Processing failed: ${e.message}")
        }
    }

    /**
     * Get AI model information
     */
    fun getModelInfo(): ModelInfo {
        return ModelInfo(
            modelType = "ONNX (Converted from PyTorch)",
            v6ModelSizeMB = 17.63,
            yoloModelSizeMB = 10.01,
            totalPackageSizeMB = 27.64,
            diceScore = DICE_SCORE,
            map50Accuracy = MAP50_ACCURACY,
            expectedInferenceTimeMs = INFERENCE_TIME_MS,
            isInitialized = isInitialized,
            exactSameAsPython = true
        )
    }

    /**
     * Check if AI is initialized
     */
    fun isInitialized(): Boolean = isInitialized
}

/**
 * Chess analysis result sealed class
 */
sealed class ChessAnalysisResult {
    data class Success(
        val fen: String,
        val confidence: Float,
        val processingTimeMs: Long,
        val boardDetectionScore: Float,
        val pieceDetectionAccuracy: Float,
        val detectedPieces: Int
    ) : ChessAnalysisResult()

    data class Error(val message: String) : ChessAnalysisResult()
}

/**
 * AI model information data class
 */
data class ModelInfo(
    val modelType: String,
    val v6ModelSizeMB: Double,
    val yoloModelSizeMB: Double,
    val totalPackageSizeMB: Double,
    val diceScore: Double,
    val map50Accuracy: Double,
    val expectedInferenceTimeMs: Int,
    val isInitialized: Boolean,
    val exactSameAsPython: Boolean
)
