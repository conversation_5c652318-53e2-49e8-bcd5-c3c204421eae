com.chessvision.app:styleable/TextAppearance = 0x7f0e0027
com.chessvision.app:styleable/SwitchCompat = 0x7f0e0026
com.chessvision.app:styleable/StateListDrawableItem = 0x7f0e0025
com.chessvision.app:styleable/StateListDrawable = 0x7f0e0024
com.chessvision.app:styleable/Spinner = 0x7f0e0023
com.chessvision.app:styleable/PopupWindowBackgroundState = 0x7f0e001f
com.chessvision.app:styleable/GradientColorItem = 0x7f0e0017
com.chessvision.app:styleable/ColorStateListItem = 0x7f0e0011
com.chessvision.app:styleable/AppCompatTheme = 0x7f0e000e
com.chessvision.app:styleable/AnimatedStateListDrawableTransition = 0x7f0e0009
com.chessvision.app:styleable/AnimatedStateListDrawableCompat = 0x7f0e0007
com.chessvision.app:styleable/ActivityChooserView = 0x7f0e0005
com.chessvision.app:styleable/ActionMenuView = 0x7f0e0003
com.chessvision.app:styleable/ActionBarLayout = 0x7f0e0001
com.chessvision.app:style/Widget.Compat.NotificationActionText = 0x7f0d0160
com.chessvision.app:style/Widget.AppCompat.TextView = 0x7f0d015b
com.chessvision.app:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0d0151
com.chessvision.app:style/Widget.AppCompat.RatingBar = 0x7f0d0150
com.chessvision.app:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0d014f
com.chessvision.app:style/Widget.AppCompat.ListView.DropDown = 0x7f0d0149
com.chessvision.app:style/Widget.AppCompat.ListView = 0x7f0d0148
com.chessvision.app:style/Widget.AppCompat.ListMenuView = 0x7f0d0146
com.chessvision.app:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0d0145
com.chessvision.app:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0d0143
com.chessvision.app:style/Widget.AppCompat.Light.PopupMenu = 0x7f0d0142
com.chessvision.app:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0d0141
com.chessvision.app:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0d0140
com.chessvision.app:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0d013f
com.chessvision.app:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0d013e
com.chessvision.app:style/Widget.AppCompat.PopupMenu = 0x7f0d014b
com.chessvision.app:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0d013b
com.chessvision.app:style/Widget.AppCompat.Light.ActionButton = 0x7f0d0139
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0d0137
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0d0133
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0d0131
com.chessvision.app:style/Widget.AppCompat.EditText = 0x7f0d012e
com.chessvision.app:styleable/Toolbar = 0x7f0e0028
com.chessvision.app:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0d012c
com.chessvision.app:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0d012a
com.chessvision.app:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0d0129
com.chessvision.app:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0d0128
com.chessvision.app:style/Widget.AppCompat.ButtonBar = 0x7f0d0127
com.chessvision.app:style/Widget.AppCompat.Button.Small = 0x7f0d0126
com.chessvision.app:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0d0124
com.chessvision.app:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0d0123
com.chessvision.app:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0d0120
com.chessvision.app:style/Widget.AppCompat.ActionMode = 0x7f0d011e
com.chessvision.app:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0d011c
com.chessvision.app:style/Widget.AppCompat.ActionBar.TabView = 0x7f0d011a
com.chessvision.app:style/Widget.AppCompat.ActionBar.TabText = 0x7f0d0119
com.chessvision.app:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0d0118
com.chessvision.app:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0d0114
com.chessvision.app:style/ThemeOverlay.AppCompat.DayNight = 0x7f0d0111
com.chessvision.app:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0d010e
com.chessvision.app:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0d012d
com.chessvision.app:style/ThemeOverlay.AppCompat = 0x7f0d010d
com.chessvision.app:style/Theme.ChessVisionApp = 0x7f0d010c
com.chessvision.app:style/Theme.AppCompat.NoActionBar = 0x7f0d010b
com.chessvision.app:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0d0107
com.chessvision.app:style/Theme.AppCompat.Light.Dialog = 0x7f0d0106
com.chessvision.app:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0d0105
com.chessvision.app:style/Theme.AppCompat.Light = 0x7f0d0104
com.chessvision.app:style/Theme.AppCompat.Dialog = 0x7f0d0100
com.chessvision.app:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0d00fe
com.chessvision.app:style/Theme.AppCompat.DayNight.Dialog = 0x7f0d00fb
com.chessvision.app:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0d00fa
com.chessvision.app:style/TextAppearance.Compat.Notification.Title = 0x7f0d00f3
com.chessvision.app:style/TextAppearance.Compat.Notification.Time = 0x7f0d00f2
com.chessvision.app:style/TextAppearance.Compat.Notification.Info = 0x7f0d00f0
com.chessvision.app:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0d00ee
com.chessvision.app:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0d00ec
com.chessvision.app:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0d00eb
com.chessvision.app:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0d00ea
com.chessvision.app:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0d00e9
com.chessvision.app:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0d00e7
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0d00e4
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0d00e0
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0d00de
com.chessvision.app:style/TextAppearance.AppCompat.Tooltip = 0x7f0d00db
com.chessvision.app:style/TextAppearance.AppCompat.Subhead = 0x7f0d00d7
com.chessvision.app:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0d00d4
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0d0135
com.chessvision.app:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0d00d1
com.chessvision.app:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0d00cf
com.chessvision.app:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0d00cb
com.chessvision.app:style/TextAppearance.AppCompat.Large = 0x7f0d00ca
com.chessvision.app:style/TextAppearance.AppCompat.Inverse = 0x7f0d00c9
com.chessvision.app:style/TextAppearance.AppCompat.Headline = 0x7f0d00c8
com.chessvision.app:style/TextAppearance.AppCompat.Display2 = 0x7f0d00c5
com.chessvision.app:style/TextAppearance.AppCompat.Caption = 0x7f0d00c3
com.chessvision.app:style/TextAppearance.AppCompat.Button = 0x7f0d00c2
com.chessvision.app:style/TextAppearance.AppCompat.Body2 = 0x7f0d00c1
com.chessvision.app:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0d00be
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0d00ba
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0d00b9
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0d00b3
com.chessvision.app:style/Platform.V25.AppCompat.Light = 0x7f0d00ac
com.chessvision.app:style/Platform.V25.AppCompat = 0x7f0d00ab
com.chessvision.app:style/Platform.V21.AppCompat = 0x7f0d00a9
com.chessvision.app:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0d00a7
com.chessvision.app:style/Platform.ThemeOverlay.AppCompat = 0x7f0d00a6
com.chessvision.app:style/FloatingDialogWindowTheme = 0x7f0d00a3
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0d00b0
com.chessvision.app:style/DialogWindowTheme = 0x7f0d00a1
com.chessvision.app:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0d00a0
com.chessvision.app:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0d009c
com.chessvision.app:style/Base.Widget.AppCompat.Spinner = 0x7f0d009b
com.chessvision.app:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0d009a
com.chessvision.app:style/Base.Widget.AppCompat.SeekBar = 0x7f0d0099
com.chessvision.app:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0d0093
com.chessvision.app:styleable/RecycleListView = 0x7f0e0021
com.chessvision.app:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0d00d3
com.chessvision.app:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0d0090
com.chessvision.app:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0d008e
com.chessvision.app:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0d008d
com.chessvision.app:style/Base.Widget.AppCompat.ListView = 0x7f0d008c
com.chessvision.app:styleable/ButtonBarLayout = 0x7f0e000f
com.chessvision.app:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0d008b
com.chessvision.app:style/Base.Widget.AppCompat.ListMenuView = 0x7f0d008a
com.chessvision.app:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0d0088
com.chessvision.app:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0d0086
com.chessvision.app:style/Base.Widget.AppCompat.ImageButton = 0x7f0d0081
com.chessvision.app:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0d007a
com.chessvision.app:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0d0079
com.chessvision.app:style/TextAppearance.AppCompat.Title = 0x7f0d00d9
com.chessvision.app:style/Base.Widget.AppCompat.ButtonBar = 0x7f0d0078
com.chessvision.app:style/Base.Widget.AppCompat.Button.Colored = 0x7f0d0076
com.chessvision.app:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0d0074
com.chessvision.app:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0d0070
com.chessvision.app:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0d006d
com.chessvision.app:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0d0075
com.chessvision.app:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0d0069
com.chessvision.app:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0d0068
com.chessvision.app:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0d0066
com.chessvision.app:style/Base.Widget.AppCompat.SearchView = 0x7f0d0097
com.chessvision.app:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0d0063
com.chessvision.app:style/Base.V7.Theme.AppCompat.Light = 0x7f0d0061
com.chessvision.app:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0d0060
com.chessvision.app:style/Base.V23.Theme.AppCompat.Light = 0x7f0d0059
com.chessvision.app:style/Base.V23.Theme.AppCompat = 0x7f0d0058
com.chessvision.app:style/Base.V22.Theme.AppCompat = 0x7f0d0056
com.chessvision.app:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0d0055
com.chessvision.app:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0d0054
com.chessvision.app:style/Base.V21.Theme.AppCompat.Light = 0x7f0d0053
com.chessvision.app:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0d0052
com.chessvision.app:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0d004f
com.chessvision.app:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0d004d
com.chessvision.app:style/Base.ThemeOverlay.AppCompat = 0x7f0d004a
com.chessvision.app:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0d0048
com.chessvision.app:styleable/MenuGroup = 0x7f0e001b
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0d00bc
com.chessvision.app:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0d0047
com.chessvision.app:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0d0046
com.chessvision.app:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0d0045
com.chessvision.app:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0d0041
com.chessvision.app:style/Base.V26.Theme.AppCompat.Light = 0x7f0d005b
com.chessvision.app:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0d0040
com.chessvision.app:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0d003f
com.chessvision.app:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0d003a
com.chessvision.app:styleable/ActionBar = 0x7f0e0000
com.chessvision.app:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0d0039
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0d0038
com.chessvision.app:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0d00d8
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0d0037
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0d0034
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0d0032
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0d0031
com.chessvision.app:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0d0098
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0d0030
com.chessvision.app:style/TextAppearance.Compat.Notification = 0x7f0d00ef
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0d002e
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0d002d
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0d002a
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0d0028
com.chessvision.app:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0d0027
com.chessvision.app:xml/file_paths = 0x7f0f0002
com.chessvision.app:style/Base.TextAppearance.AppCompat.Title = 0x7f0d0025
com.chessvision.app:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0d0024
com.chessvision.app:style/Base.V28.Theme.AppCompat.Light = 0x7f0d005e
com.chessvision.app:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0d0023
com.chessvision.app:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0d0020
com.chessvision.app:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0d001f
com.chessvision.app:style/Base.TextAppearance.AppCompat.Menu = 0x7f0d001d
com.chessvision.app:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0d0018
com.chessvision.app:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0d0016
com.chessvision.app:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0d0013
com.chessvision.app:style/TextAppearance.AppCompat.Menu = 0x7f0d00d2
com.chessvision.app:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0d0012
com.chessvision.app:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0d0011
com.chessvision.app:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0d000e
com.chessvision.app:style/Base.TextAppearance.AppCompat = 0x7f0d000c
com.chessvision.app:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0d000b
com.chessvision.app:style/Base.DialogWindowTitle.AppCompat = 0x7f0d000a
com.chessvision.app:style/Base.Animation.AppCompat.DropDownUp = 0x7f0d0008
com.chessvision.app:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0d00fc
com.chessvision.app:style/Base.AlertDialog.AppCompat = 0x7f0d0005
com.chessvision.app:style/AlertDialog.AppCompat = 0x7f0d0000
com.chessvision.app:styleable/ViewBackgroundHelper = 0x7f0e002a
com.chessvision.app:string/tooltip_description = 0x7f0c00a7
com.chessvision.app:string/template_percent = 0x7f0c00a6
com.chessvision.app:string/start_scanning = 0x7f0c00a2
com.chessvision.app:string/sound_effects = 0x7f0c00a1
com.chessvision.app:styleable/AppCompatImageView = 0x7f0e000a
com.chessvision.app:string/share_analysis = 0x7f0c00a0
com.chessvision.app:string/share = 0x7f0c009f
com.chessvision.app:string/selected = 0x7f0c009d
com.chessvision.app:string/search_menu_title = 0x7f0c009c
com.chessvision.app:string/scan_board = 0x7f0c009b
com.chessvision.app:style/Widget.AppCompat.ActionButton = 0x7f0d011b
com.chessvision.app:string/save = 0x7f0c0099
com.chessvision.app:string/retry = 0x7f0c0098
com.chessvision.app:string/recent_games = 0x7f0c0097
com.chessvision.app:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0d00ce
com.chessvision.app:string/range_end = 0x7f0c0095
com.chessvision.app:string/piece_style = 0x7f0c0091
com.chessvision.app:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0d001c
com.chessvision.app:string/piece_rook = 0x7f0c0090
com.chessvision.app:string/piece_queen = 0x7f0c008f
com.chessvision.app:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0d0022
com.chessvision.app:string/piece_king = 0x7f0c008c
com.chessvision.app:string/m3c_time_picker_pm = 0x7f0c0081
com.chessvision.app:string/m3c_time_picker_period_toggle_description = 0x7f0c0080
com.chessvision.app:string/m3c_time_picker_minute_suffix = 0x7f0c007e
com.chessvision.app:string/m3c_time_picker_minute_selection = 0x7f0c007d
com.chessvision.app:string/m3c_time_picker_minute = 0x7f0c007c
com.chessvision.app:string/m3c_time_picker_hour_text_field = 0x7f0c007b
com.chessvision.app:string/m3c_time_picker_hour_suffix = 0x7f0c007a
com.chessvision.app:string/m3c_time_picker_hour_selection = 0x7f0c0079
com.chessvision.app:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0d0156
com.chessvision.app:string/m3c_time_picker_hour_24h_suffix = 0x7f0c0078
com.chessvision.app:string/m3c_time_picker_am = 0x7f0c0076
com.chessvision.app:xml/data_extraction_rules = 0x7f0f0001
com.chessvision.app:string/m3c_suggestions_available = 0x7f0c0075
com.chessvision.app:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0d0110
com.chessvision.app:string/m3c_date_range_picker_start_headline = 0x7f0c006e
com.chessvision.app:string/m3c_date_range_picker_scroll_to_next_month = 0x7f0c006c
com.chessvision.app:string/m3c_date_range_picker_day_in_range = 0x7f0c006a
com.chessvision.app:string/m3c_date_picker_today_description = 0x7f0c0066
com.chessvision.app:string/m3c_date_picker_title = 0x7f0c0065
com.chessvision.app:string/m3c_date_picker_switch_to_year_selection = 0x7f0c0064
com.chessvision.app:string/m3c_date_picker_switch_to_next_month = 0x7f0c0062
com.chessvision.app:string/m3c_date_picker_switch_to_input_mode = 0x7f0c0061
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0d00b6
com.chessvision.app:string/m3c_date_picker_switch_to_calendar_mode = 0x7f0c005f
com.chessvision.app:string/m3c_date_picker_no_selection_description = 0x7f0c005c
com.chessvision.app:string/m3c_date_picker_headline = 0x7f0c0059
com.chessvision.app:string/m3c_date_input_title = 0x7f0c0058
com.chessvision.app:string/m3c_date_input_no_input_description = 0x7f0c0057
com.chessvision.app:string/m3c_date_input_label = 0x7f0c0056
com.chessvision.app:string/m3c_date_input_invalid_year_range = 0x7f0c0055
com.chessvision.app:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0d00f6
com.chessvision.app:string/m3c_date_input_invalid_not_allowed = 0x7f0c0054
com.chessvision.app:string/m3c_date_input_headline = 0x7f0c0051
com.chessvision.app:string/m3c_bottom_sheet_drag_handle_description = 0x7f0c004e
com.chessvision.app:string/piece_knight = 0x7f0c008d
com.chessvision.app:string/indeterminate = 0x7f0c004a
com.chessvision.app:string/in_progress = 0x7f0c0049
com.chessvision.app:string/evaluation = 0x7f0c0046
com.chessvision.app:string/error_unknown = 0x7f0c0045
com.chessvision.app:string/error_network = 0x7f0c0044
com.chessvision.app:string/error_invalid_fen = 0x7f0c0043
com.chessvision.app:styleable/LinearLayoutCompat = 0x7f0e0018
com.chessvision.app:string/error_camera_permission = 0x7f0c0041
com.chessvision.app:style/Theme.AppCompat.DialogWhenLarge = 0x7f0d0103
com.chessvision.app:string/engine_strength = 0x7f0c003f
com.chessvision.app:string/dropdown_menu = 0x7f0c003e
com.chessvision.app:string/delete = 0x7f0c003d
com.chessvision.app:string/deeper_analysis = 0x7f0c003a
com.chessvision.app:style/Platform.AppCompat.Light = 0x7f0d00a5
com.chessvision.app:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0d0095
com.chessvision.app:string/color_white = 0x7f0c0039
com.chessvision.app:string/color_black = 0x7f0c0038
com.chessvision.app:string/close_sheet = 0x7f0c0037
com.chessvision.app:string/chess_board_title = 0x7f0c0035
com.chessvision.app:string/capture = 0x7f0c0032
com.chessvision.app:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0d00ae
com.chessvision.app:style/Base.Widget.AppCompat.ProgressBar = 0x7f0d0092
com.chessvision.app:string/call_notification_screening_text = 0x7f0c002d
com.chessvision.app:styleable/LinearLayoutCompat_Layout = 0x7f0e0019
com.chessvision.app:string/call_notification_incoming_text = 0x7f0c002b
com.chessvision.app:string/call_notification_hang_up_action = 0x7f0c002a
com.chessvision.app:string/call_notification_decline_action = 0x7f0c0029
com.chessvision.app:string/call_notification_answer_video_action = 0x7f0c0028
com.chessvision.app:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0d015e
com.chessvision.app:string/best_moves = 0x7f0c0025
com.chessvision.app:string/app_name = 0x7f0c0021
com.chessvision.app:string/analyze_position = 0x7f0c001e
com.chessvision.app:string/analysis_wait_message = 0x7f0c001d
com.chessvision.app:string/about = 0x7f0c001b
com.chessvision.app:string/abc_toolbar_collapse_description = 0x7f0c001a
com.chessvision.app:string/abc_shareactionprovider_share_with_application = 0x7f0c0019
com.chessvision.app:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0d00a8
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0d0033
com.chessvision.app:string/m3c_tooltip_long_press_label = 0x7f0c0082
com.chessvision.app:string/abc_shareactionprovider_share_with = 0x7f0c0018
com.chessvision.app:string/abc_searchview_description_voice = 0x7f0c0017
com.chessvision.app:string/m3c_time_picker_hour = 0x7f0c0077
com.chessvision.app:string/abc_searchview_description_query = 0x7f0c0014
com.chessvision.app:string/abc_searchview_description_clear = 0x7f0c0013
com.chessvision.app:string/abc_search_hint = 0x7f0c0012
com.chessvision.app:string/abc_prepend_shortcut_label = 0x7f0c0011
com.chessvision.app:string/abc_menu_sym_shortcut_label = 0x7f0c0010
com.chessvision.app:string/abc_menu_meta_shortcut_label = 0x7f0c000d
com.chessvision.app:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0d014c
com.chessvision.app:string/abc_menu_function_shortcut_label = 0x7f0c000c
com.chessvision.app:string/abc_menu_space_shortcut_label = 0x7f0c000f
com.chessvision.app:string/abc_menu_delete_shortcut_label = 0x7f0c000a
com.chessvision.app:string/m3c_date_range_picker_scroll_to_previous_month = 0x7f0c006d
com.chessvision.app:string/abc_menu_alt_shortcut_label = 0x7f0c0008
com.chessvision.app:style/Base.TextAppearance.AppCompat.Button = 0x7f0d000f
com.chessvision.app:string/abc_capital_on = 0x7f0c0007
com.chessvision.app:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0d007c
com.chessvision.app:string/abc_capital_off = 0x7f0c0006
com.chessvision.app:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0d0109
com.chessvision.app:string/m3c_date_input_invalid_for_pattern = 0x7f0c0053
com.chessvision.app:string/abc_activity_chooser_view_see_all = 0x7f0c0004
com.chessvision.app:layout/select_dialog_singlechoice_material = 0x7f0a0027
com.chessvision.app:layout/select_dialog_multichoice_material = 0x7f0a0026
com.chessvision.app:string/ok = 0x7f0c0089
com.chessvision.app:layout/select_dialog_item_material = 0x7f0a0025
com.chessvision.app:layout/notification_template_part_time = 0x7f0a0024
com.chessvision.app:layout/notification_template_icon_group = 0x7f0a0022
com.chessvision.app:layout/notification_template_custom_big = 0x7f0a0021
com.chessvision.app:layout/notification_action_tombstone = 0x7f0a0020
com.chessvision.app:style/Base.Widget.AppCompat.Button.Small = 0x7f0d0077
com.chessvision.app:layout/notification_action = 0x7f0a001f
com.chessvision.app:layout/ime_secondary_split_test_activity = 0x7f0a001e
com.chessvision.app:layout/ime_base_split_test_activity = 0x7f0a001d
com.chessvision.app:layout/abc_tooltip = 0x7f0a001b
com.chessvision.app:layout/abc_select_dialog_material = 0x7f0a001a
com.chessvision.app:layout/abc_search_view = 0x7f0a0019
com.chessvision.app:style/Base.Widget.AppCompat.Button = 0x7f0d0072
com.chessvision.app:layout/abc_search_dropdown_item_icons_2line = 0x7f0a0018
com.chessvision.app:style/ThemeOverlay.AppCompat.Dark = 0x7f0d010f
com.chessvision.app:layout/abc_screen_toolbar = 0x7f0a0017
com.chessvision.app:layout/abc_screen_simple_overlay_action_mode = 0x7f0a0016
com.chessvision.app:layout/abc_screen_simple = 0x7f0a0015
com.chessvision.app:layout/abc_screen_content_include = 0x7f0a0014
com.chessvision.app:layout/abc_popup_menu_item_layout = 0x7f0a0013
com.chessvision.app:layout/abc_list_menu_item_radio = 0x7f0a0011
com.chessvision.app:layout/abc_dialog_title_material = 0x7f0a000c
com.chessvision.app:layout/abc_alert_dialog_title_material = 0x7f0a000a
com.chessvision.app:layout/abc_alert_dialog_material = 0x7f0a0009
com.chessvision.app:layout/abc_alert_dialog_button_bar_material = 0x7f0a0008
com.chessvision.app:layout/abc_activity_chooser_view = 0x7f0a0006
com.chessvision.app:style/Base.V26.Theme.AppCompat = 0x7f0d005a
com.chessvision.app:layout/abc_action_mode_close_item_material = 0x7f0a0005
com.chessvision.app:layout/abc_action_mode_bar = 0x7f0a0004
com.chessvision.app:layout/abc_action_menu_layout = 0x7f0a0003
com.chessvision.app:layout/abc_action_menu_item_layout = 0x7f0a0002
com.chessvision.app:layout/abc_action_bar_title_item = 0x7f0a0000
com.chessvision.app:interpolator/fast_out_slow_in = 0x7f090006
com.chessvision.app:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f090003
com.chessvision.app:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f090002
com.chessvision.app:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0d0019
com.chessvision.app:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f090001
com.chessvision.app:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0d012b
com.chessvision.app:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0d006a
com.chessvision.app:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f090000
com.chessvision.app:style/TextAppearance.AppCompat.Medium = 0x7f0d00d0
com.chessvision.app:integer/status_bar_notification_info_maxnum = 0x7f080004
com.chessvision.app:integer/cancel_button_image_alpha = 0x7f080002
com.chessvision.app:integer/abc_config_activityDefaultDur = 0x7f080000
com.chessvision.app:string/close_drawer = 0x7f0c0036
com.chessvision.app:string/back = 0x7f0c0023
com.chessvision.app:id/withText = 0x7f0700be
com.chessvision.app:styleable/AnimatedStateListDrawableItem = 0x7f0e0008
com.chessvision.app:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0d0112
com.chessvision.app:id/view_tree_saved_state_registry_owner = 0x7f0700bc
com.chessvision.app:id/view_tree_lifecycle_owner = 0x7f0700ba
com.chessvision.app:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0d0096
com.chessvision.app:id/useLogo = 0x7f0700b9
com.chessvision.app:style/Widget.AppCompat.ActionBar = 0x7f0d0116
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0d00b7
com.chessvision.app:id/uniform = 0x7f0700b7
com.chessvision.app:id/topPanel = 0x7f0700b5
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0d00b1
com.chessvision.app:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0d007d
com.chessvision.app:id/title_template = 0x7f0700b3
com.chessvision.app:id/titleDividerNoCustom = 0x7f0700b2
com.chessvision.app:style/Base.Widget.AppCompat.ActionMode = 0x7f0d006f
com.chessvision.app:layout/abc_action_bar_up_container = 0x7f0a0001
com.chessvision.app:id/time = 0x7f0700b0
com.chessvision.app:id/textSpacerNoTitle = 0x7f0700af
com.chessvision.app:style/Theme.AppCompat.DayNight = 0x7f0d00f9
com.chessvision.app:string/m3c_date_range_picker_title = 0x7f0c006f
com.chessvision.app:id/textSpacerNoButtons = 0x7f0700ae
com.chessvision.app:id/tag_unhandled_key_listeners = 0x7f0700aa
com.chessvision.app:id/tag_unhandled_key_event_manager = 0x7f0700a9
com.chessvision.app:id/tag_state_description = 0x7f0700a7
com.chessvision.app:id/tag_on_receive_content_mime_types = 0x7f0700a5
com.chessvision.app:id/tag_on_receive_content_listener = 0x7f0700a4
com.chessvision.app:style/Base.Theme.AppCompat.Light = 0x7f0d0043
com.chessvision.app:id/tag_on_apply_window_listener = 0x7f0700a3
com.chessvision.app:styleable/DrawerArrowToggle = 0x7f0e0013
com.chessvision.app:style/Theme.AppCompat = 0x7f0d00f7
com.chessvision.app:id/tag_accessibility_heading = 0x7f0700a1
com.chessvision.app:id/tag_accessibility_actions = 0x7f07009f
com.chessvision.app:style/Base.Widget.AppCompat.Toolbar = 0x7f0d009f
com.chessvision.app:id/src_in = 0x7f07009a
com.chessvision.app:id/src_atop = 0x7f070099
com.chessvision.app:id/split_action_bar = 0x7f070098
com.chessvision.app:id/spacer = 0x7f070097
com.chessvision.app:style/Theme.AppCompat.Dialog.Alert = 0x7f0d0101
com.chessvision.app:id/showTitle = 0x7f070096
com.chessvision.app:id/showHome = 0x7f070095
com.chessvision.app:id/showCustom = 0x7f070094
com.chessvision.app:id/search_mag_icon = 0x7f07008e
com.chessvision.app:style/Widget.AppCompat.ListPopupWindow = 0x7f0d0147
com.chessvision.app:id/search_close_btn = 0x7f07008b
com.chessvision.app:id/search_button = 0x7f07008a
com.chessvision.app:style/Base.Widget.AppCompat.EditText = 0x7f0d0080
com.chessvision.app:id/search_bar = 0x7f070089
com.chessvision.app:id/search_badge = 0x7f070088
com.chessvision.app:id/scrollView = 0x7f070087
com.chessvision.app:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0d0108
com.chessvision.app:id/scrollIndicatorUp = 0x7f070086
com.chessvision.app:string/m3c_snackbar_dismiss = 0x7f0c0074
com.chessvision.app:string/m3c_bottom_sheet_expand_description = 0x7f0c004f
com.chessvision.app:id/scrollIndicatorDown = 0x7f070085
com.chessvision.app:id/screen = 0x7f070084
com.chessvision.app:string/abc_action_mode_done = 0x7f0c0003
com.chessvision.app:id/right_side = 0x7f070083
com.chessvision.app:id/right_icon = 0x7f070082
com.chessvision.app:id/report_drawn = 0x7f070081
com.chessvision.app:id/progress_horizontal = 0x7f07007f
com.chessvision.app:string/m3c_date_range_picker_end_headline = 0x7f0c006b
com.chessvision.app:id/progress_circular = 0x7f07007e
com.chessvision.app:id/pooling_container_listener_holder_tag = 0x7f07007d
com.chessvision.app:id/parentPanel = 0x7f07007b
com.chessvision.app:string/position_camera_instruction = 0x7f0c0094
com.chessvision.app:id/off = 0x7f070079
com.chessvision.app:id/notification_main_column_container = 0x7f070078
com.chessvision.app:layout/support_simple_spinner_dropdown_item = 0x7f0a0028
com.chessvision.app:id/never = 0x7f070073
com.chessvision.app:color/switch_thumb_disabled_material_dark = 0x7f04006c
com.chessvision.app:id/none = 0x7f070074
com.chessvision.app:drawable/abc_ic_voice_search_api_material = 0x7f060027
com.chessvision.app:id/message = 0x7f070070
com.chessvision.app:id/line3 = 0x7f07006d
com.chessvision.app:string/save_game = 0x7f0c009a
com.chessvision.app:id/italic = 0x7f07006b
com.chessvision.app:id/is_pooling_container_tag = 0x7f07006a
com.chessvision.app:id/inspection_slot_table_set = 0x7f070069
com.chessvision.app:attr/seekBarStyle = 0x7f0200ce
com.chessvision.app:id/info = 0x7f070068
com.chessvision.app:id/homeAsUp = 0x7f070063
com.chessvision.app:id/home = 0x7f070062
com.chessvision.app:id/hide_ime_id = 0x7f070060
com.chessvision.app:id/fillEnd = 0x7f070059
com.chessvision.app:id/tag_transition_group = 0x7f0700a8
com.chessvision.app:dimen/abc_dialog_fixed_height_major = 0x7f05001c
com.chessvision.app:id/edit_text_id = 0x7f070054
com.chessvision.app:style/Animation.AppCompat.Dialog = 0x7f0d0002
com.chessvision.app:id/fitCenter = 0x7f07005b
com.chessvision.app:dimen/abc_button_padding_horizontal_material = 0x7f050014
com.chessvision.app:id/edit_query = 0x7f070053
com.chessvision.app:id/disableHome = 0x7f070052
com.chessvision.app:id/dialog_button = 0x7f070051
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0d00b8
com.chessvision.app:drawable/abc_ic_star_black_36dp = 0x7f060022
com.chessvision.app:attr/dividerHorizontal = 0x7f020066
com.chessvision.app:id/accessibility_custom_action_17 = 0x7f070010
com.chessvision.app:id/custom = 0x7f07004d
com.chessvision.app:id/contentPanel = 0x7f07004c
com.chessvision.app:styleable/GradientColor = 0x7f0e0016
com.chessvision.app:attr/contentInsetEnd = 0x7f020058
com.chessvision.app:id/checkbox = 0x7f070044
com.chessvision.app:attr/controlBackground = 0x7f02005e
com.chessvision.app:drawable/abc_spinner_mtrl_am_alpha = 0x7f060043
com.chessvision.app:id/center_vertical = 0x7f070043
com.chessvision.app:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0d007f
com.chessvision.app:id/always = 0x7f07003c
com.chessvision.app:id/actions = 0x7f070038
com.chessvision.app:attr/fontProviderQuery = 0x7f020082
com.chessvision.app:attr/searchIcon = 0x7f0200cc
com.chessvision.app:id/action_text = 0x7f070037
com.chessvision.app:id/action_mode_bar_stub = 0x7f070035
com.chessvision.app:style/FloatingDialogTheme = 0x7f0d00a2
com.chessvision.app:dimen/abc_text_size_display_3_material = 0x7f050042
com.chessvision.app:id/action_mode_bar = 0x7f070034
com.chessvision.app:dimen/abc_text_size_headline_material = 0x7f050044
com.chessvision.app:id/action_menu_presenter = 0x7f070033
com.chessvision.app:string/range_start = 0x7f0c0096
com.chessvision.app:string/default_popup_window_title = 0x7f0c003c
com.chessvision.app:id/action_menu_divider = 0x7f070032
com.chessvision.app:id/action_container = 0x7f07002e
com.chessvision.app:id/action_bar_title = 0x7f07002d
com.chessvision.app:id/action_bar_container = 0x7f070029
com.chessvision.app:id/tabMode = 0x7f07009e
com.chessvision.app:id/accessibility_custom_action_9 = 0x7f070026
com.chessvision.app:id/accessibility_custom_action_8 = 0x7f070025
com.chessvision.app:drawable/ic_call_decline = 0x7f060069
com.chessvision.app:id/accessibility_custom_action_7 = 0x7f070024
com.chessvision.app:attr/overlapAnchor = 0x7f0200b5
com.chessvision.app:id/accessibility_custom_action_6 = 0x7f070023
com.chessvision.app:id/accessibility_custom_action_5 = 0x7f070022
com.chessvision.app:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.chessvision.app:attr/textAppearanceSmallPopupMenu = 0x7f0200f0
com.chessvision.app:id/accessibility_custom_action_4 = 0x7f070021
com.chessvision.app:color/abc_primary_text_material_dark = 0x7f04000a
com.chessvision.app:drawable/abc_list_focused_holo = 0x7f06002c
com.chessvision.app:id/accessibility_custom_action_3 = 0x7f07001e
com.chessvision.app:style/Widget.AppCompat.ListView.Menu = 0x7f0d014a
com.chessvision.app:id/accessibility_custom_action_29 = 0x7f07001d
com.chessvision.app:attr/contentInsetLeft = 0x7f02005a
com.chessvision.app:id/accessibility_custom_action_27 = 0x7f07001b
com.chessvision.app:style/Widget.AppCompat.Light.SearchView = 0x7f0d0144
com.chessvision.app:id/tag_accessibility_clickable_spans = 0x7f0700a0
com.chessvision.app:attr/backgroundStacked = 0x7f020033
com.chessvision.app:id/accessibility_custom_action_26 = 0x7f07001a
com.chessvision.app:id/accessibility_custom_action_25 = 0x7f070019
com.chessvision.app:color/background_material_dark = 0x7f04001e
com.chessvision.app:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f06003b
com.chessvision.app:id/accessibility_custom_action_24 = 0x7f070018
com.chessvision.app:styleable/ActionMenuItemView = 0x7f0e0002
com.chessvision.app:id/accessibility_custom_action_23 = 0x7f070017
com.chessvision.app:dimen/abc_text_size_display_2_material = 0x7f050041
com.chessvision.app:id/accessibility_custom_action_21 = 0x7f070015
com.chessvision.app:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0d006b
com.chessvision.app:id/accessibility_custom_action_2 = 0x7f070013
com.chessvision.app:style/TextAppearance.Compat.Notification.Line2 = 0x7f0d00f1
com.chessvision.app:id/accessibility_custom_action_18 = 0x7f070011
com.chessvision.app:id/accessibility_custom_action_12 = 0x7f07000b
com.chessvision.app:styleable/AppCompatTextHelper = 0x7f0e000c
com.chessvision.app:id/accessibility_custom_action_11 = 0x7f07000a
com.chessvision.app:style/Widget.AppCompat.Button = 0x7f0d0121
com.chessvision.app:string/analyzing_board = 0x7f0c001f
com.chessvision.app:id/accessibility_custom_action_1 = 0x7f070008
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0d00df
com.chessvision.app:id/accessibility_custom_action_0 = 0x7f070007
com.chessvision.app:id/accessibility_action_clickable_span = 0x7f070006
com.chessvision.app:style/Base.Widget.AppCompat.PopupWindow = 0x7f0d0091
com.chessvision.app:id/alertTitle = 0x7f07003b
com.chessvision.app:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f090004
com.chessvision.app:id/META = 0x7f070003
com.chessvision.app:style/Base.Theme.AppCompat.CompactMenu = 0x7f0d003d
com.chessvision.app:id/FUNCTION = 0x7f070002
com.chessvision.app:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0d009e
com.chessvision.app:drawable/wq = 0x7f060080
com.chessvision.app:id/activity_chooser_view_content = 0x7f070039
com.chessvision.app:id/search_plate = 0x7f07008f
com.chessvision.app:color/abc_tint_default = 0x7f040013
com.chessvision.app:drawable/tooltip_frame_light = 0x7f06007b
com.chessvision.app:id/unchecked = 0x7f0700b6
com.chessvision.app:drawable/tooltip_frame_dark = 0x7f06007a
com.chessvision.app:attr/drawableLeftCompat = 0x7f02006b
com.chessvision.app:drawable/notify_panel_notification_icon_bg = 0x7f060079
com.chessvision.app:drawable/notification_template_icon_bg = 0x7f060076
com.chessvision.app:style/Widget.AppCompat.ImageButton = 0x7f0d012f
com.chessvision.app:id/fillCenter = 0x7f070058
com.chessvision.app:drawable/notification_icon_background = 0x7f060074
com.chessvision.app:drawable/ic_launcher_foreground = 0x7f06006c
com.chessvision.app:string/abc_activitychooserview_choose_application = 0x7f0c0005
com.chessvision.app:drawable/ic_launcher_background = 0x7f06006b
com.chessvision.app:color/button_material_light = 0x7f040027
com.chessvision.app:drawable/ic_call_answer_video_low = 0x7f060068
com.chessvision.app:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0d00da
com.chessvision.app:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f090005
com.chessvision.app:drawable/chessboard_background = 0x7f060064
com.chessvision.app:string/on = 0x7f0c008a
com.chessvision.app:dimen/abc_dropdownitem_text_padding_left = 0x7f05002a
com.chessvision.app:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f060063
com.chessvision.app:drawable/btn_radio_on_mtrl = 0x7f060062
com.chessvision.app:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f060061
com.chessvision.app:id/action_mode_close_button = 0x7f070036
com.chessvision.app:drawable/btn_radio_off_mtrl = 0x7f060060
com.chessvision.app:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f06005f
com.chessvision.app:drawable/btn_checkbox_unchecked_mtrl = 0x7f06005e
com.chessvision.app:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0d013c
com.chessvision.app:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f06005d
com.chessvision.app:style/TextAppearance.AppCompat.Widget.Button = 0x7f0d00e5
com.chessvision.app:string/tab = 0x7f0c00a5
com.chessvision.app:attr/actionModeStyle = 0x7f02001b
com.chessvision.app:drawable/btn_checkbox_checked_mtrl = 0x7f06005c
com.chessvision.app:string/navigation_menu = 0x7f0c0084
com.chessvision.app:layout/abc_list_menu_item_icon = 0x7f0a000f
com.chessvision.app:drawable/bp = 0x7f060059
com.chessvision.app:attr/imageButtonStyle = 0x7f020091
com.chessvision.app:drawable/bn = 0x7f060058
com.chessvision.app:drawable/bb = 0x7f060056
com.chessvision.app:drawable/abc_vector_test = 0x7f060055
com.chessvision.app:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f060053
com.chessvision.app:drawable/abc_textfield_default_mtrl_alpha = 0x7f060051
com.chessvision.app:drawable/abc_text_select_handle_right_mtrl_dark = 0x7f06004e
com.chessvision.app:id/consume_window_insets_tag = 0x7f07004a
com.chessvision.app:drawable/abc_text_select_handle_left_mtrl_dark = 0x7f06004a
com.chessvision.app:drawable/abc_tab_indicator_mtrl_alpha = 0x7f060048
com.chessvision.app:drawable/abc_tab_indicator_material = 0x7f060047
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0d00e1
com.chessvision.app:attr/switchTextAppearance = 0x7f0200e7
com.chessvision.app:drawable/abc_switch_track_mtrl_alpha = 0x7f060046
com.chessvision.app:id/accessibility_custom_action_20 = 0x7f070014
com.chessvision.app:drawable/abc_textfield_search_material = 0x7f060054
com.chessvision.app:drawable/abc_switch_thumb_material = 0x7f060045
com.chessvision.app:drawable/abc_spinner_textfield_background_material = 0x7f060044
com.chessvision.app:drawable/abc_seekbar_track_material = 0x7f060042
com.chessvision.app:drawable/abc_seekbar_tick_mark_material = 0x7f060041
com.chessvision.app:drawable/abc_scrubber_track_mtrl_alpha = 0x7f06003f
com.chessvision.app:style/Base.Widget.AppCompat.PopupMenu = 0x7f0d008f
com.chessvision.app:dimen/abc_control_corner_material = 0x7f050018
com.chessvision.app:id/fitStart = 0x7f07005d
com.chessvision.app:style/Base.TextAppearance.AppCompat.Large = 0x7f0d0017
com.chessvision.app:layout/abc_list_menu_item_layout = 0x7f0a0010
com.chessvision.app:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f06003d
com.chessvision.app:dimen/abc_floating_window_z = 0x7f05002f
com.chessvision.app:drawable/abc_popup_background_mtrl_mult = 0x7f060037
com.chessvision.app:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f060036
com.chessvision.app:drawable/abc_list_selector_disabled_holo_dark = 0x7f060032
com.chessvision.app:bool/abc_action_bar_embed_tabs = 0x7f030000
com.chessvision.app:drawable/abc_list_selector_background_transition_holo_dark = 0x7f060030
com.chessvision.app:style/ThemeOverlay.AppCompat.Dialog = 0x7f0d0113
com.chessvision.app:color/abc_search_url_text_selected = 0x7f04000f
com.chessvision.app:id/notification_main_column = 0x7f070077
com.chessvision.app:drawable/abc_list_pressed_holo_light = 0x7f06002f
com.chessvision.app:dimen/abc_text_size_menu_header_material = 0x7f050047
com.chessvision.app:drawable/abc_list_longpressed_holo = 0x7f06002d
com.chessvision.app:id/accessibility_custom_action_19 = 0x7f070012
com.chessvision.app:drawable/abc_list_divider_mtrl_alpha = 0x7f06002b
com.chessvision.app:attr/splitTrack = 0x7f0200da
com.chessvision.app:drawable/abc_item_background_holo_light = 0x7f060029
com.chessvision.app:attr/actionBarTabTextStyle = 0x7f020008
com.chessvision.app:drawable/abc_ic_star_half_black_48dp = 0x7f060026
com.chessvision.app:attr/editTextColor = 0x7f020076
com.chessvision.app:drawable/abc_ic_star_half_black_36dp = 0x7f060025
com.chessvision.app:style/Widget.AppCompat.Button.Borderless = 0x7f0d0122
com.chessvision.app:dimen/abc_dialog_min_width_major = 0x7f050022
com.chessvision.app:drawable/abc_ic_star_half_black_16dp = 0x7f060024
com.chessvision.app:drawable/abc_ic_star_black_16dp = 0x7f060021
com.chessvision.app:string/abc_menu_enter_shortcut_label = 0x7f0c000b
com.chessvision.app:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f06001f
com.chessvision.app:color/material_blue_grey_900 = 0x7f040035
com.chessvision.app:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f06001e
com.chessvision.app:drawable/bk = 0x7f060057
com.chessvision.app:drawable/abc_ic_menu_overflow_material = 0x7f06001c
com.chessvision.app:dimen/abc_button_inset_vertical_material = 0x7f050013
com.chessvision.app:drawable/abc_btn_check_material_anim = 0x7f060004
com.chessvision.app:drawable/abc_ic_go_search_api_material = 0x7f060019
com.chessvision.app:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f060018
com.chessvision.app:attr/autoSizeTextType = 0x7f020030
com.chessvision.app:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f05000b
com.chessvision.app:drawable/abc_ic_clear_material = 0x7f060017
com.chessvision.app:drawable/abc_cab_background_top_material = 0x7f060010
com.chessvision.app:id/collapseActionView = 0x7f070047
com.chessvision.app:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f06000e
com.chessvision.app:drawable/abc_btn_colored_material = 0x7f060007
com.chessvision.app:string/camera_permission_required = 0x7f0c0030
com.chessvision.app:attr/menu = 0x7f0200ae
com.chessvision.app:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f060006
com.chessvision.app:string/auto_analysis = 0x7f0c0022
com.chessvision.app:dimen/hint_alpha_material_light = 0x7f05005b
com.chessvision.app:drawable/abc_btn_check_material = 0x7f060003
com.chessvision.app:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0d0085
com.chessvision.app:color/md_theme_light_onSurfaceVariant = 0x7f04004e
com.chessvision.app:dimen/tooltip_y_offset_touch = 0x7f050074
com.chessvision.app:dimen/tooltip_y_offset_non_touch = 0x7f050073
com.chessvision.app:string/call_notification_ongoing_text = 0x7f0c002c
com.chessvision.app:dimen/tooltip_precise_anchor_threshold = 0x7f050071
com.chessvision.app:dimen/tooltip_margin = 0x7f05006f
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0d0036
com.chessvision.app:id/action_bar_root = 0x7f07002a
com.chessvision.app:string/position_analysis = 0x7f0c0093
com.chessvision.app:dimen/tooltip_corner_radius = 0x7f05006d
com.chessvision.app:dimen/notification_top_pad = 0x7f05006b
com.chessvision.app:anim/abc_slide_out_bottom = 0x7f010008
com.chessvision.app:dimen/notification_small_icon_size_as_large = 0x7f050069
com.chessvision.app:dimen/notification_small_icon_background_padding = 0x7f050068
com.chessvision.app:drawable/abc_list_pressed_holo_dark = 0x7f06002e
com.chessvision.app:id/accessibility_custom_action_31 = 0x7f070020
com.chessvision.app:style/TextAppearance.AppCompat.Small = 0x7f0d00d5
com.chessvision.app:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0d004e
com.chessvision.app:attr/colorSwitchThumbNormal = 0x7f020055
com.chessvision.app:dimen/notification_large_icon_width = 0x7f050063
com.chessvision.app:string/m3c_date_picker_scroll_to_earlier_years = 0x7f0c005d
com.chessvision.app:id/tag_screen_reader_focusable = 0x7f0700a6
com.chessvision.app:id/CTRL = 0x7f070001
com.chessvision.app:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0d000d
com.chessvision.app:color/material_blue_grey_950 = 0x7f040036
com.chessvision.app:dimen/notification_content_margin_start = 0x7f050061
com.chessvision.app:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0d0062
com.chessvision.app:dimen/notification_action_icon_size = 0x7f05005e
com.chessvision.app:dimen/hint_pressed_alpha_material_dark = 0x7f05005c
com.chessvision.app:dimen/highlight_alpha_material_light = 0x7f050059
com.chessvision.app:style/Base.TextAppearance.AppCompat.Medium = 0x7f0d001b
com.chessvision.app:string/camera_permission_rationale = 0x7f0c002f
com.chessvision.app:dimen/disabled_alpha_material_dark = 0x7f050055
com.chessvision.app:style/Widget.AppCompat.ProgressBar = 0x7f0d014e
com.chessvision.app:drawable/wn = 0x7f06007e
com.chessvision.app:drawable/notification_bg = 0x7f06006e
com.chessvision.app:dimen/compat_notification_large_icon_max_width = 0x7f050054
com.chessvision.app:color/material_grey_800 = 0x7f04003d
com.chessvision.app:anim/abc_tooltip_exit = 0x7f01000b
com.chessvision.app:dimen/compat_notification_large_icon_max_height = 0x7f050053
com.chessvision.app:dimen/compat_control_corner_material = 0x7f050052
com.chessvision.app:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0d0049
com.chessvision.app:dimen/compat_button_inset_vertical_material = 0x7f05004f
com.chessvision.app:dimen/abc_text_size_subtitle_material_toolbar = 0x7f05004b
com.chessvision.app:dimen/abc_text_size_medium_material = 0x7f050046
com.chessvision.app:dimen/abc_text_size_large_material = 0x7f050045
com.chessvision.app:mipmap/ic_launcher_round = 0x7f0b0001
com.chessvision.app:attr/listItemLayout = 0x7f02009f
com.chessvision.app:attr/measureWithLargestChild = 0x7f0200ad
com.chessvision.app:drawable/abc_ratingbar_small_material = 0x7f06003a
com.chessvision.app:layout/notification_template_part_chronometer = 0x7f0a0023
com.chessvision.app:id/search_src_text = 0x7f070090
com.chessvision.app:dimen/abc_text_size_caption_material = 0x7f05003f
com.chessvision.app:dimen/abc_text_size_body_1_material = 0x7f05003c
com.chessvision.app:color/androidx_core_ripple_material_light = 0x7f04001a
com.chessvision.app:dimen/abc_switch_padding = 0x7f05003b
com.chessvision.app:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f06001b
com.chessvision.app:dimen/abc_select_dialog_padding_start_material = 0x7f05003a
com.chessvision.app:dimen/abc_seekbar_track_progress_height_material = 0x7f050039
com.chessvision.app:style/AlertDialog.AppCompat.Light = 0x7f0d0001
com.chessvision.app:dimen/abc_seekbar_track_background_height_material = 0x7f050038
com.chessvision.app:dimen/abc_search_view_preferred_width = 0x7f050037
com.chessvision.app:styleable/MenuView = 0x7f0e001d
com.chessvision.app:id/fitEnd = 0x7f07005c
com.chessvision.app:attr/autoSizeMaxTextSize = 0x7f02002c
com.chessvision.app:dimen/abc_search_view_preferred_height = 0x7f050036
com.chessvision.app:dimen/abc_progress_bar_height_material = 0x7f050035
com.chessvision.app:dimen/compat_button_inset_horizontal_material = 0x7f05004e
com.chessvision.app:dimen/abc_list_item_padding_horizontal_material = 0x7f050033
com.chessvision.app:dimen/abc_dialog_fixed_height_minor = 0x7f05001d
com.chessvision.app:dimen/abc_list_item_height_large_material = 0x7f050030
com.chessvision.app:dimen/abc_edit_text_inset_top_material = 0x7f05002e
com.chessvision.app:drawable/ic_call_decline_low = 0x7f06006a
com.chessvision.app:dimen/abc_edit_text_inset_horizontal_material = 0x7f05002d
com.chessvision.app:attr/arrowHeadLength = 0x7f020029
com.chessvision.app:dimen/abc_dropdownitem_text_padding_right = 0x7f05002b
com.chessvision.app:string/settings_title = 0x7f0c009e
com.chessvision.app:id/compose_view_saveable_id_tag = 0x7f070049
com.chessvision.app:dimen/abc_dropdownitem_icon_width = 0x7f050029
com.chessvision.app:id/search_voice_btn = 0x7f070091
com.chessvision.app:dimen/abc_dialog_title_divider_material = 0x7f050026
com.chessvision.app:attr/textColorAlertDialogListItem = 0x7f0200f1
com.chessvision.app:attr/actionBarWidgetTheme = 0x7f02000a
com.chessvision.app:dimen/abc_dialog_padding_top_material = 0x7f050025
com.chessvision.app:attr/autoSizePresetSizes = 0x7f02002e
com.chessvision.app:attr/listPopupWindowStyle = 0x7f0200a2
com.chessvision.app:id/blocking = 0x7f070040
com.chessvision.app:id/action_bar_spinner = 0x7f07002b
com.chessvision.app:attr/multiChoiceItemLayout = 0x7f0200af
com.chessvision.app:drawable/abc_btn_borderless_material = 0x7f060002
com.chessvision.app:attr/color = 0x7f02004b
com.chessvision.app:dimen/abc_list_item_height_small_material = 0x7f050032
com.chessvision.app:attr/windowActionBarOverlay = 0x7f020114
com.chessvision.app:dimen/abc_dialog_padding_material = 0x7f050024
com.chessvision.app:drawable/abc_dialog_material_background = 0x7f060013
com.chessvision.app:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.chessvision.app:dimen/abc_dialog_min_width_minor = 0x7f050023
com.chessvision.app:color/call_notification_decline_color = 0x7f040029
com.chessvision.app:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f050020
com.chessvision.app:string/m3c_date_range_input_invalid_range_input = 0x7f0c0068
com.chessvision.app:string/androidx_startup = 0x7f0c0020
com.chessvision.app:drawable/wk = 0x7f06007d
com.chessvision.app:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0d013a
com.chessvision.app:dimen/abc_dialog_fixed_width_minor = 0x7f05001f
com.chessvision.app:style/Base.Theme.AppCompat.Dialog = 0x7f0d003e
com.chessvision.app:dimen/abc_dialog_fixed_width_major = 0x7f05001e
com.chessvision.app:dimen/abc_disabled_alpha_material_light = 0x7f050028
com.chessvision.app:id/listMode = 0x7f07006e
com.chessvision.app:dimen/abc_dialog_corner_radius_material = 0x7f05001b
com.chessvision.app:dimen/abc_control_inset_material = 0x7f050019
com.chessvision.app:color/md_theme_light_onPrimary = 0x7f040049
com.chessvision.app:dimen/abc_alert_dialog_button_bar_height = 0x7f050010
com.chessvision.app:string/error_fen_detection = 0x7f0c0042
com.chessvision.app:id/androidx_compose_ui_view_composition_context = 0x7f07003d
com.chessvision.app:dimen/abc_action_button_min_height_material = 0x7f05000d
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0d0138
com.chessvision.app:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f05000c
com.chessvision.app:id/normal = 0x7f070075
com.chessvision.app:style/Widget.AppCompat.ActionBar.Solid = 0x7f0d0117
com.chessvision.app:style/Base.Animation.AppCompat.Tooltip = 0x7f0d0009
com.chessvision.app:dimen/abc_action_bar_stacked_max_height = 0x7f050009
com.chessvision.app:drawable/abc_seekbar_thumb_material = 0x7f060040
com.chessvision.app:dimen/abc_action_bar_overflow_padding_start_material = 0x7f050008
com.chessvision.app:attr/fontProviderAuthority = 0x7f02007d
com.chessvision.app:dimen/abc_text_size_title_material = 0x7f05004c
com.chessvision.app:dimen/abc_action_bar_overflow_padding_end_material = 0x7f050007
com.chessvision.app:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f050006
com.chessvision.app:drawable/abc_btn_radio_material = 0x7f060009
com.chessvision.app:dimen/abc_action_bar_default_padding_end_material = 0x7f050003
com.chessvision.app:style/Base.Theme.AppCompat = 0x7f0d003c
com.chessvision.app:dimen/abc_action_bar_default_height_material = 0x7f050002
com.chessvision.app:dimen/abc_action_bar_content_inset_with_nav = 0x7f050001
com.chessvision.app:attr/colorPrimary = 0x7f020053
com.chessvision.app:color/vector_tint_theme_color = 0x7f040075
com.chessvision.app:style/TextAppearance.AppCompat.Display1 = 0x7f0d00c4
com.chessvision.app:color/foreground_material_light = 0x7f040031
com.chessvision.app:color/vector_tint_color = 0x7f040074
com.chessvision.app:id/buttonPanel = 0x7f070042
com.chessvision.app:string/piece_bishop = 0x7f0c008b
com.chessvision.app:color/tooltip_background_dark = 0x7f040072
com.chessvision.app:color/switch_thumb_normal_material_dark = 0x7f040070
com.chessvision.app:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f060016
com.chessvision.app:color/switch_thumb_material_dark = 0x7f04006e
com.chessvision.app:attr/listChoiceIndicatorSingleAnimated = 0x7f02009d
com.chessvision.app:color/md_theme_light_onSecondaryContainer = 0x7f04004c
com.chessvision.app:color/switch_thumb_disabled_material_light = 0x7f04006d
com.chessvision.app:string/abc_menu_shift_shortcut_label = 0x7f0c000e
com.chessvision.app:id/notification_background = 0x7f070076
com.chessvision.app:drawable/abc_control_background_material = 0x7f060012
com.chessvision.app:string/camera_permission_description = 0x7f0c002e
com.chessvision.app:id/view_tree_view_model_store_owner = 0x7f0700bd
com.chessvision.app:color/secondary_text_disabled_material_light = 0x7f04006b
com.chessvision.app:attr/subMenuArrow = 0x7f0200dd
com.chessvision.app:color/secondary_text_disabled_material_dark = 0x7f04006a
com.chessvision.app:color/secondary_text_default_material_light = 0x7f040069
com.chessvision.app:styleable/SearchView = 0x7f0e0022
com.chessvision.app:drawable/abc_ratingbar_material = 0x7f060039
com.chessvision.app:dimen/abc_action_button_min_width_overflow_material = 0x7f05000f
com.chessvision.app:color/md_theme_light_inverseSurface = 0x7f040045
com.chessvision.app:color/secondary_text_default_material_dark = 0x7f040068
com.chessvision.app:dimen/abc_list_item_height_material = 0x7f050031
com.chessvision.app:color/primary_text_disabled_material_light = 0x7f040065
com.chessvision.app:color/primary_text_disabled_material_dark = 0x7f040064
com.chessvision.app:color/primary_text_default_material_light = 0x7f040063
com.chessvision.app:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.chessvision.app:id/hide_in_inspector_tag = 0x7f070061
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0d0035
com.chessvision.app:string/m3c_bottom_sheet_pane_title = 0x7f0c0050
com.chessvision.app:mipmap/ic_launcher = 0x7f0b0000
com.chessvision.app:dimen/abc_text_size_menu_material = 0x7f050048
com.chessvision.app:color/md_theme_light_onTertiaryContainer = 0x7f040050
com.chessvision.app:color/primary_material_light = 0x7f040061
com.chessvision.app:style/Widget.AppCompat.SearchView = 0x7f0d0153
com.chessvision.app:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0d00f5
com.chessvision.app:dimen/abc_text_size_small_material = 0x7f050049
com.chessvision.app:color/primary_material_dark = 0x7f040060
com.chessvision.app:string/m3c_bottom_sheet_collapse_description = 0x7f0c004c
com.chessvision.app:color/primary_dark_material_light = 0x7f04005f
com.chessvision.app:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0d011d
com.chessvision.app:attr/buttonBarButtonStyle = 0x7f020038
com.chessvision.app:attr/backgroundTint = 0x7f020034
com.chessvision.app:drawable/abc_cab_background_top_mtrl_alpha = 0x7f060011
com.chessvision.app:color/notification_icon_bg_color = 0x7f04005d
com.chessvision.app:style/Base.TextAppearance.AppCompat.Headline = 0x7f0d0015
com.chessvision.app:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f06001a
com.chessvision.app:color/notification_action_color_filter = 0x7f04005c
com.chessvision.app:color/md_theme_light_tertiaryContainer = 0x7f04005b
com.chessvision.app:color/md_theme_light_surfaceVariant = 0x7f040059
com.chessvision.app:style/Platform.V21.AppCompat.Light = 0x7f0d00aa
com.chessvision.app:color/md_theme_light_surface = 0x7f040058
com.chessvision.app:string/abc_action_menu_overflow_description = 0x7f0c0002
com.chessvision.app:color/md_theme_light_secondaryContainer = 0x7f040057
com.chessvision.app:layout/abc_activity_chooser_view_list_item = 0x7f0a0007
com.chessvision.app:attr/windowMinWidthMinor = 0x7f02011b
com.chessvision.app:dimen/tooltip_precise_anchor_extra_offset = 0x7f050070
com.chessvision.app:attr/logo = 0x7f0200aa
com.chessvision.app:color/md_theme_light_secondary = 0x7f040056
com.chessvision.app:id/tag_accessibility_pane_title = 0x7f0700a2
com.chessvision.app:attr/listDividerAlertDialog = 0x7f02009e
com.chessvision.app:id/action_bar_subtitle = 0x7f07002c
com.chessvision.app:color/md_theme_light_scrim = 0x7f040055
com.chessvision.app:attr/paddingBottomNoButtons = 0x7f0200b6
com.chessvision.app:color/md_theme_light_outlineVariant = 0x7f040052
com.chessvision.app:anim/abc_popup_exit = 0x7f010004
com.chessvision.app:color/md_theme_light_onBackground = 0x7f040046
com.chessvision.app:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0d00e8
com.chessvision.app:string/cancel = 0x7f0c0031
com.chessvision.app:drawable/abc_list_selector_background_transition_holo_light = 0x7f060031
com.chessvision.app:styleable/Capability = 0x7f0e0010
com.chessvision.app:attr/numericModifiers = 0x7f0200b4
com.chessvision.app:color/md_theme_light_inversePrimary = 0x7f040044
com.chessvision.app:attr/editTextBackground = 0x7f020075
com.chessvision.app:color/md_theme_light_error = 0x7f040041
com.chessvision.app:color/switch_thumb_normal_material_light = 0x7f040071
com.chessvision.app:drawable/abc_list_selector_holo_light = 0x7f060035
com.chessvision.app:color/highlighted_text_material_dark = 0x7f040032
com.chessvision.app:color/material_grey_900 = 0x7f04003f
com.chessvision.app:attr/titleMarginStart = 0x7f020102
com.chessvision.app:string/no_recent_games = 0x7f0c0086
com.chessvision.app:string/m3c_search_bar_search = 0x7f0c0073
com.chessvision.app:color/material_grey_850 = 0x7f04003e
com.chessvision.app:drawable/abc_text_select_handle_middle_mtrl_light = 0x7f06004d
com.chessvision.app:color/material_grey_600 = 0x7f04003c
com.chessvision.app:color/material_grey_100 = 0x7f040039
com.chessvision.app:integer/config_tooltipAnimTime = 0x7f080003
com.chessvision.app:color/md_theme_light_onSurface = 0x7f04004d
com.chessvision.app:drawable/abc_item_background_holo_dark = 0x7f060028
com.chessvision.app:color/md_theme_light_primaryContainer = 0x7f040054
com.chessvision.app:id/fillStart = 0x7f07005a
com.chessvision.app:styleable/ViewStubCompat = 0x7f0e002b
com.chessvision.app:id/action_bar = 0x7f070027
com.chessvision.app:attr/alphabeticModifiers = 0x7f020028
com.chessvision.app:style/Widget.AppCompat.SeekBar = 0x7f0d0155
com.chessvision.app:color/material_blue_grey_800 = 0x7f040034
com.chessvision.app:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0d0042
com.chessvision.app:string/loading = 0x7f0c004b
com.chessvision.app:color/highlighted_text_material_light = 0x7f040033
com.chessvision.app:attr/lStar = 0x7f020097
com.chessvision.app:attr/alertDialogTheme = 0x7f020025
com.chessvision.app:attr/commitIcon = 0x7f020056
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0d00b5
com.chessvision.app:dimen/abc_disabled_alpha_material_dark = 0x7f050027
com.chessvision.app:dimen/abc_dialog_list_padding_top_no_title = 0x7f050021
com.chessvision.app:color/tooltip_background_light = 0x7f040073
com.chessvision.app:style/Base.Widget.AppCompat.RatingBar = 0x7f0d0094
com.chessvision.app:color/foreground_material_dark = 0x7f040030
com.chessvision.app:id/beginning = 0x7f07003f
com.chessvision.app:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0d013d
com.chessvision.app:color/abc_input_method_navigation_guard = 0x7f040007
com.chessvision.app:color/error_color_material_dark = 0x7f04002e
com.chessvision.app:drawable/abc_list_selector_disabled_holo_light = 0x7f060033
com.chessvision.app:drawable/ic_call_answer_video = 0x7f060067
com.chessvision.app:color/background_material_light = 0x7f04001f
com.chessvision.app:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0d001e
com.chessvision.app:attr/navigationMode = 0x7f0200b2
com.chessvision.app:attr/actionModeWebSearchDrawable = 0x7f02001c
com.chessvision.app:color/dim_foreground_material_light = 0x7f04002d
com.chessvision.app:color/dim_foreground_disabled_material_dark = 0x7f04002a
com.chessvision.app:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.chessvision.app:color/md_theme_light_primary = 0x7f040053
com.chessvision.app:drawable/notification_bg_low_pressed = 0x7f060071
com.chessvision.app:string/not_selected = 0x7f0c0087
com.chessvision.app:attr/implementationMode = 0x7f020092
com.chessvision.app:attr/paddingEnd = 0x7f0200b7
com.chessvision.app:color/bright_foreground_material_light = 0x7f040025
com.chessvision.app:attr/checkedTextViewStyle = 0x7f020046
com.chessvision.app:attr/actionBarStyle = 0x7f020005
com.chessvision.app:color/bright_foreground_material_dark = 0x7f040024
com.chessvision.app:color/bright_foreground_inverse_material_light = 0x7f040023
com.chessvision.app:color/bright_foreground_inverse_material_dark = 0x7f040022
com.chessvision.app:color/bright_foreground_disabled_material_light = 0x7f040021
com.chessvision.app:attr/actionModeCopyDrawable = 0x7f020013
com.chessvision.app:drawable/wb = 0x7f06007c
com.chessvision.app:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0d0159
com.chessvision.app:attr/actionOverflowMenuStyle = 0x7f02001e
com.chessvision.app:drawable/abc_ratingbar_indicator_material = 0x7f060038
com.chessvision.app:attr/actionModeSplitBackground = 0x7f02001a
com.chessvision.app:color/background_floating_material_light = 0x7f04001d
com.chessvision.app:string/abc_searchview_description_submit = 0x7f0c0016
com.chessvision.app:attr/subtitleTextColor = 0x7f0200e1
com.chessvision.app:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.chessvision.app:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0d00cd
com.chessvision.app:string/m3c_dropdown_menu_collapsed = 0x7f0c0071
com.chessvision.app:attr/windowMinWidthMajor = 0x7f02011a
com.chessvision.app:color/abc_tint_switch_track = 0x7f040017
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0d002b
com.chessvision.app:color/abc_tint_btn_checkable = 0x7f040012
com.chessvision.app:style/Theme.AppCompat.Light.NoActionBar = 0x7f0d010a
com.chessvision.app:attr/selectableItemBackgroundBorderless = 0x7f0200d0
com.chessvision.app:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0d0071
com.chessvision.app:color/abc_tint_seek_thumb = 0x7f040015
com.chessvision.app:string/m3c_date_picker_navigate_to_year_description = 0x7f0c005b
com.chessvision.app:id/multiply = 0x7f070072
com.chessvision.app:id/compatible = 0x7f070048
com.chessvision.app:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0d0064
com.chessvision.app:style/Animation.AppCompat.Tooltip = 0x7f0d0004
com.chessvision.app:color/bright_foreground_disabled_material_dark = 0x7f040020
com.chessvision.app:dimen/compat_button_padding_vertical_material = 0x7f050051
com.chessvision.app:attr/toolbarNavigationButtonStyle = 0x7f020108
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0d00af
com.chessvision.app:attr/trackTintMode = 0x7f02010f
com.chessvision.app:attr/tooltipForegroundColor = 0x7f02010a
com.chessvision.app:attr/actionOverflowButtonStyle = 0x7f02001d
com.chessvision.app:styleable/MenuItem = 0x7f0e001c
com.chessvision.app:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.chessvision.app:color/abc_search_url_text_normal = 0x7f04000d
com.chessvision.app:color/abc_search_url_text = 0x7f04000c
com.chessvision.app:style/Widget.AppCompat.Toolbar = 0x7f0d015d
com.chessvision.app:drawable/abc_btn_radio_material_anim = 0x7f06000a
com.chessvision.app:id/accessibility_custom_action_30 = 0x7f07001f
com.chessvision.app:color/dim_foreground_material_dark = 0x7f04002c
com.chessvision.app:id/async = 0x7f07003e
com.chessvision.app:color/abc_hint_foreground_material_dark = 0x7f040005
com.chessvision.app:attr/contentInsetRight = 0x7f02005b
com.chessvision.app:color/androidx_core_secondary_text_default_material_light = 0x7f04001b
com.chessvision.app:string/home_title = 0x7f0c0048
com.chessvision.app:attr/popupWindowStyle = 0x7f0200bf
com.chessvision.app:color/abc_btn_colored_text_material = 0x7f040003
com.chessvision.app:color/abc_btn_colored_borderless_text_material = 0x7f040002
com.chessvision.app:color/error_color_material_light = 0x7f04002f
com.chessvision.app:dimen/abc_text_size_body_2_material = 0x7f05003d
com.chessvision.app:color/abc_hint_foreground_material_light = 0x7f040006
com.chessvision.app:string/analysis_text = 0x7f0c001c
com.chessvision.app:color/abc_search_url_text_pressed = 0x7f04000e
com.chessvision.app:dimen/abc_button_inset_horizontal_material = 0x7f050012
com.chessvision.app:attr/displayOptions = 0x7f020064
com.chessvision.app:color/md_theme_light_inverseOnSurface = 0x7f040043
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0d00bb
com.chessvision.app:color/abc_background_cache_hint_selector_material_light = 0x7f040001
com.chessvision.app:id/radio = 0x7f070080
com.chessvision.app:color/md_theme_light_onError = 0x7f040047
com.chessvision.app:id/SYM = 0x7f070005
com.chessvision.app:color/button_material_dark = 0x7f040026
com.chessvision.app:bool/abc_allow_stacked_button_bar = 0x7f030001
com.chessvision.app:dimen/notification_subtext_size = 0x7f05006a
com.chessvision.app:id/text = 0x7f0700ac
com.chessvision.app:attr/progressBarStyle = 0x7f0200c2
com.chessvision.app:dimen/abc_text_size_subhead_material = 0x7f05004a
com.chessvision.app:drawable/notification_bg_normal_pressed = 0x7f060073
com.chessvision.app:color/md_theme_light_onTertiary = 0x7f04004f
com.chessvision.app:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.chessvision.app:attr/windowFixedHeightMinor = 0x7f020117
com.chessvision.app:attr/spinnerStyle = 0x7f0200d9
com.chessvision.app:style/Base.Animation.AppCompat.Dialog = 0x7f0d0007
com.chessvision.app:color/md_theme_light_background = 0x7f040040
com.chessvision.app:drawable/br = 0x7f06005b
com.chessvision.app:dimen/abc_control_padding_material = 0x7f05001a
com.chessvision.app:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0d0102
com.chessvision.app:color/abc_background_cache_hint_selector_material_dark = 0x7f040000
com.chessvision.app:attr/listChoiceBackgroundIndicator = 0x7f02009b
com.chessvision.app:attr/windowActionModeOverlay = 0x7f020115
com.chessvision.app:attr/windowActionBar = 0x7f020113
com.chessvision.app:drawable/notification_bg_low_normal = 0x7f060070
com.chessvision.app:attr/trackTint = 0x7f02010e
com.chessvision.app:attr/listPreferredItemPaddingRight = 0x7f0200a8
com.chessvision.app:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0d004b
com.chessvision.app:drawable/abc_textfield_activated_mtrl_alpha = 0x7f060050
com.chessvision.app:attr/track = 0x7f02010d
com.chessvision.app:attr/nestedScrollViewStyle = 0x7f0200b3
com.chessvision.app:attr/titleMarginTop = 0x7f020103
com.chessvision.app:drawable/ic_call_answer = 0x7f060065
com.chessvision.app:id/on = 0x7f07007a
com.chessvision.app:attr/toolbarStyle = 0x7f020109
com.chessvision.app:style/Base.AlertDialog.AppCompat.Light = 0x7f0d0006
com.chessvision.app:color/md_theme_light_onErrorContainer = 0x7f040048
com.chessvision.app:attr/titleTextStyle = 0x7f020107
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar = 0x7f0d0130
com.chessvision.app:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0d004c
com.chessvision.app:id/ALT = 0x7f070000
com.chessvision.app:attr/queryHint = 0x7f0200c4
com.chessvision.app:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f06001d
com.chessvision.app:id/search_edit_frame = 0x7f07008c
com.chessvision.app:attr/titleTextAppearance = 0x7f020105
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0d002f
com.chessvision.app:attr/ratingBarStyleSmall = 0x7f0200c9
com.chessvision.app:style/Base.V28.Theme.AppCompat = 0x7f0d005d
com.chessvision.app:color/accent_material_dark = 0x7f040018
com.chessvision.app:attr/buttonStyle = 0x7f020041
com.chessvision.app:attr/titleMargins = 0x7f020104
com.chessvision.app:attr/titleMarginEnd = 0x7f020101
com.chessvision.app:id/wrap_content = 0x7f0700bf
com.chessvision.app:dimen/abc_text_size_display_1_material = 0x7f050040
com.chessvision.app:attr/alertDialogCenterButtons = 0x7f020023
com.chessvision.app:id/checked = 0x7f070045
com.chessvision.app:attr/autoCompleteTextViewStyle = 0x7f02002b
com.chessvision.app:attr/titleMarginBottom = 0x7f020100
com.chessvision.app:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0d00bd
com.chessvision.app:attr/title = 0x7f0200fe
com.chessvision.app:attr/tintMode = 0x7f0200fd
com.chessvision.app:attr/tint = 0x7f0200fc
com.chessvision.app:string/m3c_date_picker_headline_description = 0x7f0c005a
com.chessvision.app:id/title = 0x7f0700b1
com.chessvision.app:attr/thickness = 0x7f0200f5
com.chessvision.app:style/TextAppearance.AppCompat.Display3 = 0x7f0d00c6
com.chessvision.app:string/m3c_date_picker_scroll_to_later_years = 0x7f0c005e
com.chessvision.app:id/shortcut = 0x7f070093
com.chessvision.app:attr/tickMark = 0x7f0200f9
com.chessvision.app:attr/background = 0x7f020031
com.chessvision.app:attr/windowFixedHeightMajor = 0x7f020116
com.chessvision.app:attr/thumbTint = 0x7f0200f7
com.chessvision.app:attr/thumbTextPadding = 0x7f0200f6
com.chessvision.app:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0d00e6
com.chessvision.app:attr/theme = 0x7f0200f4
com.chessvision.app:dimen/abc_button_padding_vertical_material = 0x7f050015
com.chessvision.app:attr/spinBars = 0x7f0200d7
com.chessvision.app:string/call_notification_answer_action = 0x7f0c0027
com.chessvision.app:color/abc_color_highlight_material = 0x7f040004
com.chessvision.app:attr/titleMargin = 0x7f0200ff
com.chessvision.app:attr/textLocale = 0x7f0200f3
com.chessvision.app:styleable/FontFamily = 0x7f0e0014
com.chessvision.app:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f06000c
com.chessvision.app:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0d0014
com.chessvision.app:color/ripple_material_dark = 0x7f040066
com.chessvision.app:attr/textAppearanceSearchResultSubtitle = 0x7f0200ee
com.chessvision.app:attr/tooltipFrameBackground = 0x7f02010b
com.chessvision.app:attr/navigationContentDescription = 0x7f0200b0
com.chessvision.app:id/select_dialog_listview = 0x7f070092
com.chessvision.app:attr/textAppearancePopupMenuHeader = 0x7f0200ed
com.chessvision.app:attr/actionModePasteDrawable = 0x7f020016
com.chessvision.app:attr/activityChooserViewStyle = 0x7f020021
com.chessvision.app:attr/tickMarkTint = 0x7f0200fa
com.chessvision.app:style/Base.V21.Theme.AppCompat = 0x7f0d0051
com.chessvision.app:string/abc_searchview_description_search = 0x7f0c0015
com.chessvision.app:dimen/notification_action_text_size = 0x7f05005f
com.chessvision.app:attr/showTitle = 0x7f0200d5
com.chessvision.app:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0d00d6
com.chessvision.app:layout/abc_cascading_menu_item_layout = 0x7f0a000b
com.chessvision.app:id/submenuarrow = 0x7f07009c
com.chessvision.app:attr/textAppearanceListItemSecondary = 0x7f0200eb
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0d00b2
com.chessvision.app:attr/textAppearanceListItem = 0x7f0200ea
com.chessvision.app:id/customPanel = 0x7f07004e
com.chessvision.app:dimen/tooltip_horizontal_padding = 0x7f05006e
com.chessvision.app:color/abc_tint_spinner = 0x7f040016
com.chessvision.app:dimen/abc_config_prefDialogWidth = 0x7f050017
com.chessvision.app:style/Base.Widget.AppCompat.TextView = 0x7f0d009d
com.chessvision.app:string/abc_action_bar_home_description = 0x7f0c0000
com.chessvision.app:attr/subtitleTextStyle = 0x7f0200e2
com.chessvision.app:attr/srcCompat = 0x7f0200db
com.chessvision.app:attr/switchStyle = 0x7f0200e6
com.chessvision.app:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0d0154
com.chessvision.app:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0d00b4
com.chessvision.app:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.chessvision.app:attr/switchPadding = 0x7f0200e5
com.chessvision.app:attr/switchMinWidth = 0x7f0200e4
com.chessvision.app:attr/suggestionRowLayout = 0x7f0200e3
com.chessvision.app:string/m3c_date_range_input_title = 0x7f0c0069
com.chessvision.app:attr/searchViewStyle = 0x7f0200cd
com.chessvision.app:styleable/CompoundButton = 0x7f0e0012
com.chessvision.app:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0d00ff
com.chessvision.app:attr/subtitle = 0x7f0200df
com.chessvision.app:attr/iconifiedByDefault = 0x7f020090
com.chessvision.app:color/md_theme_light_errorContainer = 0x7f040042
com.chessvision.app:styleable/ListPopupWindow = 0x7f0e001a
com.chessvision.app:color/md_theme_light_onSecondary = 0x7f04004b
com.chessvision.app:attr/singleChoiceItemLayout = 0x7f0200d6
com.chessvision.app:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0d0073
com.chessvision.app:attr/submitBackground = 0x7f0200de
com.chessvision.app:color/md_theme_light_outline = 0x7f040051
com.chessvision.app:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f06000b
com.chessvision.app:attr/thumbTintMode = 0x7f0200f8
com.chessvision.app:attr/subtitleTextAppearance = 0x7f0200e0
com.chessvision.app:attr/drawableTint = 0x7f02006f
com.chessvision.app:attr/spinnerDropDownItemStyle = 0x7f0200d8
com.chessvision.app:id/tag_window_insets_animation_callback = 0x7f0700ab
com.chessvision.app:id/accessibility_custom_action_10 = 0x7f070009
com.chessvision.app:attr/showText = 0x7f0200d4
com.chessvision.app:attr/showAsAction = 0x7f0200d2
com.chessvision.app:drawable/abc_list_divider_material = 0x7f06002a
com.chessvision.app:drawable/abc_ic_ab_back_material = 0x7f060015
com.chessvision.app:attr/scaleType = 0x7f0200ca
com.chessvision.app:styleable/FontFamilyFont = 0x7f0e0015
com.chessvision.app:id/search_go_btn = 0x7f07008d
com.chessvision.app:attr/tickMarkTintMode = 0x7f0200fb
com.chessvision.app:attr/textAllCaps = 0x7f0200e8
com.chessvision.app:style/Base.TextAppearance.AppCompat.Small = 0x7f0d0021
com.chessvision.app:attr/alpha = 0x7f020027
com.chessvision.app:attr/ratingBarStyleIndicator = 0x7f0200c8
com.chessvision.app:color/accent_material_light = 0x7f040019
com.chessvision.app:drawable/abc_text_select_handle_middle_mtrl_dark = 0x7f06004c
com.chessvision.app:dimen/abc_cascading_menus_min_smallest_width = 0x7f050016
com.chessvision.app:string/capture_board_title = 0x7f0c0034
com.chessvision.app:integer/abc_config_activityShortDur = 0x7f080001
com.chessvision.app:attr/radioButtonStyle = 0x7f0200c6
com.chessvision.app:dimen/notification_right_side_padding_top = 0x7f050067
com.chessvision.app:attr/actionLayout = 0x7f02000d
com.chessvision.app:attr/actionModeCutDrawable = 0x7f020014
com.chessvision.app:attr/homeLayout = 0x7f02008c
com.chessvision.app:styleable/PreviewView = 0x7f0e0020
com.chessvision.app:color/abc_secondary_text_material_light = 0x7f040011
com.chessvision.app:string/best_move = 0x7f0c0024
com.chessvision.app:color/background_floating_material_dark = 0x7f04001c
com.chessvision.app:attr/queryBackground = 0x7f0200c3
com.chessvision.app:dimen/notification_big_circle_margin = 0x7f050060
com.chessvision.app:attr/popupTheme = 0x7f0200be
com.chessvision.app:drawable/notification_action_background = 0x7f06006d
com.chessvision.app:attr/panelMenuListWidth = 0x7f0200bc
com.chessvision.app:id/accessibility_custom_action_16 = 0x7f07000f
com.chessvision.app:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0d005c
com.chessvision.app:color/primary_text_default_material_dark = 0x7f040062
com.chessvision.app:string/m3c_time_picker_minute_text_field = 0x7f0c007f
com.chessvision.app:attr/textAppearanceLargePopupMenu = 0x7f0200e9
com.chessvision.app:string/m3c_date_picker_year_picker_pane_title = 0x7f0c0067
com.chessvision.app:color/material_grey_300 = 0x7f04003a
com.chessvision.app:attr/panelMenuListTheme = 0x7f0200bb
com.chessvision.app:style/Widget.AppCompat.Spinner.DropDown = 0x7f0d0158
com.chessvision.app:id/group_divider = 0x7f07005f
com.chessvision.app:id/add = 0x7f07003a
com.chessvision.app:attr/panelBackground = 0x7f0200ba
com.chessvision.app:attr/buttonGravity = 0x7f02003e
com.chessvision.app:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0d0089
com.chessvision.app:attr/actionDropDownStyle = 0x7f02000c
com.chessvision.app:attr/paddingStart = 0x7f0200b8
com.chessvision.app:attr/navigationIcon = 0x7f0200b1
com.chessvision.app:color/primary_dark_material_dark = 0x7f04005e
com.chessvision.app:drawable/abc_text_cursor_material = 0x7f060049
com.chessvision.app:attr/buttonStyleSmall = 0x7f020042
com.chessvision.app:attr/listPreferredItemPaddingLeft = 0x7f0200a7
com.chessvision.app:attr/fontProviderPackage = 0x7f020081
com.chessvision.app:drawable/notification_bg_low = 0x7f06006f
com.chessvision.app:dimen/disabled_alpha_material_light = 0x7f050056
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0d0136
com.chessvision.app:attr/buttonTintMode = 0x7f020044
com.chessvision.app:attr/actionBarTabBarStyle = 0x7f020006
com.chessvision.app:attr/listPreferredItemPaddingStart = 0x7f0200a9
com.chessvision.app:attr/listMenuViewStyle = 0x7f0200a1
com.chessvision.app:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0d0087
com.chessvision.app:attr/listLayout = 0x7f0200a0
com.chessvision.app:string/m3c_bottom_sheet_dismiss_description = 0x7f0c004d
com.chessvision.app:attr/dialogTheme = 0x7f020063
com.chessvision.app:attr/colorAccent = 0x7f02004c
com.chessvision.app:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f060052
com.chessvision.app:attr/gapBetweenBars = 0x7f020087
com.chessvision.app:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0d007e
com.chessvision.app:color/switch_thumb_material_light = 0x7f04006f
com.chessvision.app:attr/colorButtonNormal = 0x7f02004e
com.chessvision.app:id/action_context_bar = 0x7f07002f
com.chessvision.app:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0d0050
com.chessvision.app:drawable/notification_template_icon_low_bg = 0x7f060077
com.chessvision.app:attr/layout = 0x7f020099
com.chessvision.app:attr/height = 0x7f020089
com.chessvision.app:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f060000
com.chessvision.app:attr/fontWeight = 0x7f020086
com.chessvision.app:dimen/notification_top_pad_large_text = 0x7f05006c
com.chessvision.app:attr/lastBaselineToBottomHeight = 0x7f020098
com.chessvision.app:drawable/ic_call_answer_low = 0x7f060066
com.chessvision.app:attr/ttcIndex = 0x7f020110
com.chessvision.app:attr/closeItemLayout = 0x7f020048
com.chessvision.app:string/abc_action_bar_up_description = 0x7f0c0001
com.chessvision.app:attr/fontProviderFetchTimeout = 0x7f020080
com.chessvision.app:drawable/abc_text_select_handle_right_mtrl_light = 0x7f06004f
com.chessvision.app:dimen/abc_text_size_display_4_material = 0x7f050043
com.chessvision.app:attr/dividerVertical = 0x7f020068
com.chessvision.app:attr/divider = 0x7f020065
com.chessvision.app:id/up = 0x7f0700b8
com.chessvision.app:id/forever = 0x7f07005e
com.chessvision.app:dimen/tooltip_vertical_padding = 0x7f050072
com.chessvision.app:attr/fontFamily = 0x7f02007c
com.chessvision.app:styleable/View = 0x7f0e0029
com.chessvision.app:attr/actionModeFindDrawable = 0x7f020015
com.chessvision.app:id/wrapped_composition_tag = 0x7f0700c0
com.chessvision.app:attr/firstBaselineToTopHeight = 0x7f02007a
com.chessvision.app:style/Platform.Widget.AppCompat.Spinner = 0x7f0d00ad
com.chessvision.app:attr/queryPatterns = 0x7f0200c5
com.chessvision.app:id/accessibility_custom_action_28 = 0x7f07001c
com.chessvision.app:attr/expandActivityOverflowButtonDrawable = 0x7f020079
com.chessvision.app:attr/editTextStyle = 0x7f020077
com.chessvision.app:string/board_theme = 0x7f0c0026
com.chessvision.app:attr/colorControlNormal = 0x7f020051
com.chessvision.app:attr/listPreferredItemHeight = 0x7f0200a3
com.chessvision.app:attr/fontProviderCerts = 0x7f02007e
com.chessvision.app:string/error = 0x7f0c0040
com.chessvision.app:dimen/abc_edit_text_inset_bottom_material = 0x7f05002c
com.chessvision.app:id/submit_area = 0x7f07009d
com.chessvision.app:attr/homeAsUpIndicator = 0x7f02008b
com.chessvision.app:attr/dropdownListPreferredItemHeight = 0x7f020074
com.chessvision.app:attr/actionModeCloseDrawable = 0x7f020012
com.chessvision.app:attr/dividerPadding = 0x7f020067
com.chessvision.app:style/Widget.AppCompat.Button.Colored = 0x7f0d0125
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0d00dd
com.chessvision.app:dimen/abc_action_button_min_width_material = 0x7f05000e
com.chessvision.app:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0700bb
com.chessvision.app:color/abc_secondary_text_material_dark = 0x7f040010
com.chessvision.app:attr/drawerArrowStyle = 0x7f020072
com.chessvision.app:color/dim_foreground_disabled_material_light = 0x7f04002b
com.chessvision.app:attr/defaultQueryHint = 0x7f020060
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0d0134
com.chessvision.app:string/tooltip_label = 0x7f0c00a8
com.chessvision.app:attr/indeterminateProgressStyle = 0x7f020093
com.chessvision.app:id/middle = 0x7f070071
com.chessvision.app:drawable/abc_cab_background_internal_bg = 0x7f06000f
com.chessvision.app:style/Widget.AppCompat.Spinner = 0x7f0d0157
com.chessvision.app:attr/drawableTopCompat = 0x7f020071
com.chessvision.app:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0d001a
com.chessvision.app:attr/lineHeight = 0x7f02009a
com.chessvision.app:drawable/notification_tile_bg = 0x7f060078
com.chessvision.app:attr/textAppearanceListItemSmall = 0x7f0200ec
com.chessvision.app:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0d0132
com.chessvision.app:attr/listPreferredItemHeightSmall = 0x7f0200a5
com.chessvision.app:dimen/notification_main_column_padding_top = 0x7f050064
com.chessvision.app:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0d007b
com.chessvision.app:attr/drawableTintMode = 0x7f020070
com.chessvision.app:drawable/abc_btn_default_mtrl_shape = 0x7f060008
com.chessvision.app:attr/drawableSize = 0x7f02006d
com.chessvision.app:attr/actionMenuTextAppearance = 0x7f02000e
com.chessvision.app:style/TextAppearance.AppCompat.Body1 = 0x7f0d00c0
com.chessvision.app:attr/barLength = 0x7f020036
com.chessvision.app:attr/autoSizeStepGranularity = 0x7f02002f
com.chessvision.app:attr/customNavigationLayout = 0x7f02005f
com.chessvision.app:drawable/abc_action_bar_item_background_material = 0x7f060001
com.chessvision.app:attr/drawableRightCompat = 0x7f02006c
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0d00e2
com.chessvision.app:string/off = 0x7f0c0088
com.chessvision.app:attr/listChoiceIndicatorMultipleAnimated = 0x7f02009c
com.chessvision.app:string/switch_role = 0x7f0c00a4
com.chessvision.app:attr/colorControlHighlight = 0x7f020050
com.chessvision.app:attr/listPreferredItemPaddingEnd = 0x7f0200a6
com.chessvision.app:string/new_game = 0x7f0c0085
com.chessvision.app:color/material_deep_teal_200 = 0x7f040037
com.chessvision.app:id/action_divider = 0x7f070030
com.chessvision.app:attr/drawableEndCompat = 0x7f02006a
com.chessvision.app:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f06003e
com.chessvision.app:styleable/AppCompatSeekBar = 0x7f0e000b
com.chessvision.app:attr/contentDescription = 0x7f020057
com.chessvision.app:attr/isLightTheme = 0x7f020095
com.chessvision.app:color/material_deep_teal_500 = 0x7f040038
com.chessvision.app:attr/icon = 0x7f02008d
com.chessvision.app:layout/abc_popup_menu_header_item_layout = 0x7f0a0012
com.chessvision.app:id/expand_activities_button = 0x7f070056
com.chessvision.app:string/capture_board_subtitle = 0x7f0c0033
com.chessvision.app:attr/logoDescription = 0x7f0200ab
com.chessvision.app:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0d00ed
com.chessvision.app:attr/contentInsetStartWithNavigation = 0x7f02005d
com.chessvision.app:style/Base.V7.Theme.AppCompat = 0x7f0d005f
com.chessvision.app:attr/dialogCornerRadius = 0x7f020061
com.chessvision.app:id/text2 = 0x7f0700ad
com.chessvision.app:drawable/abc_edit_text_material = 0x7f060014
com.chessvision.app:attr/colorControlActivated = 0x7f02004f
com.chessvision.app:id/image = 0x7f070067
com.chessvision.app:style/Widget.AppCompat.Spinner.Underlined = 0x7f0d015a
com.chessvision.app:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0d00fd
com.chessvision.app:attr/contentInsetStart = 0x7f02005c
com.chessvision.app:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0d015c
com.chessvision.app:style/TextAppearance.AppCompat = 0x7f0d00bf
com.chessvision.app:id/accessibility_custom_action_22 = 0x7f070016
com.chessvision.app:styleable/PopupWindow = 0x7f0e001e
com.chessvision.app:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0d0026
com.chessvision.app:id/chronometer = 0x7f070046
com.chessvision.app:style/Widget.AppCompat.ActivityChooserView = 0x7f0d011f
com.chessvision.app:id/bottom = 0x7f070041
com.chessvision.app:styleable/AppCompatTextView = 0x7f0e000d
com.chessvision.app:id/SHIFT = 0x7f070004
com.chessvision.app:attr/actionModeShareDrawable = 0x7f020019
com.chessvision.app:dimen/abc_action_bar_elevation_material = 0x7f050005
com.chessvision.app:color/ripple_material_light = 0x7f040067
com.chessvision.app:attr/borderlessButtonStyle = 0x7f020037
com.chessvision.app:id/ifRoom = 0x7f070066
com.chessvision.app:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0d003b
com.chessvision.app:drawable/abc_ic_search_api_material = 0x7f060020
com.chessvision.app:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0d00cc
com.chessvision.app:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0d006e
com.chessvision.app:string/m3c_date_picker_switch_to_previous_month = 0x7f0c0063
com.chessvision.app:attr/colorPrimaryDark = 0x7f020054
com.chessvision.app:id/accessibility_custom_action_15 = 0x7f07000e
com.chessvision.app:color/md_theme_light_tertiary = 0x7f04005a
com.chessvision.app:attr/colorError = 0x7f020052
com.chessvision.app:style/TextAppearance.AppCompat.Display4 = 0x7f0d00c7
com.chessvision.app:attr/fontVariationSettings = 0x7f020085
com.chessvision.app:style/Base.V22.Theme.AppCompat.Light = 0x7f0d0057
com.chessvision.app:attr/elevation = 0x7f020078
com.chessvision.app:id/src_over = 0x7f07009b
com.chessvision.app:attr/colorBackgroundFloating = 0x7f02004d
com.chessvision.app:attr/dialogPreferredPadding = 0x7f020062
com.chessvision.app:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.chessvision.app:anim/abc_fade_in = 0x7f010000
com.chessvision.app:dimen/abc_action_bar_content_inset_material = 0x7f050000
com.chessvision.app:color/abc_primary_text_material_light = 0x7f04000b
com.chessvision.app:drawable/notification_oversize_large_icon_bg = 0x7f060075
com.chessvision.app:anim/abc_slide_out_top = 0x7f010009
com.chessvision.app:attr/buttonPanelSideLayout = 0x7f020040
com.chessvision.app:id/expanded_menu = 0x7f070057
com.chessvision.app:drawable/bq = 0x7f06005a
com.chessvision.app:attr/actionModeBackground = 0x7f020010
com.chessvision.app:attr/voiceIcon = 0x7f020112
com.chessvision.app:attr/buttonBarStyle = 0x7f02003c
com.chessvision.app:id/performance = 0x7f07007c
com.chessvision.app:attr/iconTint = 0x7f02008e
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0d002c
com.chessvision.app:string/grant_camera_permission = 0x7f0c0047
com.chessvision.app:attr/buttonBarPositiveButtonStyle = 0x7f02003b
com.chessvision.app:string/default_error_message = 0x7f0c003b
com.chessvision.app:attr/closeIcon = 0x7f020047
com.chessvision.app:style/Theme.AppCompat.CompactMenu = 0x7f0d00f8
com.chessvision.app:style/Base.V7.Widget.AppCompat.EditText = 0x7f0d0065
com.chessvision.app:attr/ratingBarStyle = 0x7f0200c7
com.chessvision.app:attr/buttonTint = 0x7f020043
com.chessvision.app:attr/actionMenuTextColor = 0x7f02000f
com.chessvision.app:style/Widget.AppCompat.RatingBar.Small = 0x7f0d0152
com.chessvision.app:style/Platform.AppCompat = 0x7f0d00a4
com.chessvision.app:id/accessibility_custom_action_14 = 0x7f07000d
com.chessvision.app:style/Base.TextAppearance.AppCompat.Caption = 0x7f0d0010
com.chessvision.app:drawable/abc_list_selector_holo_dark = 0x7f060034
com.chessvision.app:attr/buttonBarNeutralButtonStyle = 0x7f02003a
com.chessvision.app:id/accessibility_custom_action_13 = 0x7f07000c
com.chessvision.app:id/line1 = 0x7f07006c
com.chessvision.app:dimen/notification_media_narrow_margin = 0x7f050065
com.chessvision.app:id/default_activity_button = 0x7f070050
com.chessvision.app:style/Base.Widget.AppCompat.ActionButton = 0x7f0d006c
com.chessvision.app:attr/backgroundTintMode = 0x7f020035
com.chessvision.app:attr/font = 0x7f02007b
com.chessvision.app:attr/checkboxStyle = 0x7f020045
com.chessvision.app:layout/abc_expanded_menu_layout = 0x7f0a000d
com.chessvision.app:dimen/abc_action_bar_default_padding_start_material = 0x7f050004
com.chessvision.app:style/Animation.AppCompat.DropDownUp = 0x7f0d0003
com.chessvision.app:anim/abc_fade_out = 0x7f010001
com.chessvision.app:string/abc_menu_ctrl_shortcut_label = 0x7f0c0009
com.chessvision.app:attr/backgroundSplit = 0x7f020032
com.chessvision.app:styleable/AlertDialog = 0x7f0e0006
com.chessvision.app:color/abc_primary_text_disable_only_material_light = 0x7f040009
com.chessvision.app:attr/drawableBottomCompat = 0x7f020069
com.chessvision.app:id/icon_group = 0x7f070065
com.chessvision.app:xml/backup_rules = 0x7f0f0000
com.chessvision.app:attr/actionProviderClass = 0x7f02001f
com.chessvision.app:drawable/notification_bg_normal = 0x7f060072
com.chessvision.app:attr/fontProviderSystemFontFamily = 0x7f020083
com.chessvision.app:attr/dropDownListViewStyle = 0x7f020073
com.chessvision.app:attr/arrowShaftLength = 0x7f02002a
com.chessvision.app:attr/textColorSearchUrl = 0x7f0200f2
com.chessvision.app:id/content = 0x7f07004b
com.chessvision.app:string/m3c_dialog = 0x7f0c0070
com.chessvision.app:attr/titleTextColor = 0x7f020106
com.chessvision.app:attr/popupMenuStyle = 0x7f0200bd
com.chessvision.app:attr/allowStacking = 0x7f020026
com.chessvision.app:attr/fontStyle = 0x7f020084
com.chessvision.app:attr/alertDialogButtonGroupStyle = 0x7f020022
com.chessvision.app:dimen/abc_panel_menu_list_width = 0x7f050034
com.chessvision.app:attr/actionBarDivider = 0x7f020000
com.chessvision.app:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0d0083
com.chessvision.app:attr/actionButtonStyle = 0x7f02000b
com.chessvision.app:attr/windowFixedWidthMinor = 0x7f020119
com.chessvision.app:styleable/ActionMode = 0x7f0e0004
com.chessvision.app:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f060005
com.chessvision.app:attr/autoSizeMinTextSize = 0x7f02002d
com.chessvision.app:dimen/hint_alpha_material_dark = 0x7f05005a
com.chessvision.app:attr/textAppearanceSearchResultTitle = 0x7f0200ef
com.chessvision.app:attr/actionViewClass = 0x7f020020
com.chessvision.app:attr/buttonBarNegativeButtonStyle = 0x7f020039
com.chessvision.app:color/md_theme_light_onPrimaryContainer = 0x7f04004a
com.chessvision.app:id/list_item = 0x7f07006f
com.chessvision.app:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f06000d
com.chessvision.app:attr/preserveIconSpacing = 0x7f0200c0
com.chessvision.app:layout/abc_list_menu_item_checkbox = 0x7f0a000e
com.chessvision.app:attr/shortcutMatchRequired = 0x7f0200d1
com.chessvision.app:anim/abc_tooltip_enter = 0x7f01000a
com.chessvision.app:attr/actionBarPopupTheme = 0x7f020002
com.chessvision.app:drawable/wr = 0x7f060081
com.chessvision.app:string/play_vs_ai = 0x7f0c0092
com.chessvision.app:dimen/abc_alert_dialog_button_dimen = 0x7f050011
com.chessvision.app:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0d0044
com.chessvision.app:attr/buttonCompat = 0x7f02003d
com.chessvision.app:style/Widget.AppCompat.PopupWindow = 0x7f0d014d
com.chessvision.app:bool/abc_config_actionMenuItemAllCaps = 0x7f030002
com.chessvision.app:dimen/highlight_alpha_material_dark = 0x7f050058
com.chessvision.app:attr/fontProviderFetchStrategy = 0x7f02007f
com.chessvision.app:id/action_bar_activity_content = 0x7f070028
com.chessvision.app:attr/viewInflaterClass = 0x7f020111
com.chessvision.app:id/action_image = 0x7f070031
com.chessvision.app:attr/actionModeCloseButtonStyle = 0x7f020011
com.chessvision.app:dimen/notification_right_icon_size = 0x7f050066
com.chessvision.app:string/m3c_date_input_headline_description = 0x7f0c0052
com.chessvision.app:attr/tooltipText = 0x7f02010c
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0d00e3
com.chessvision.app:anim/abc_popup_enter = 0x7f010003
com.chessvision.app:attr/contentInsetEndWithActions = 0x7f020059
com.chessvision.app:drawable/wp = 0x7f06007f
com.chessvision.app:drawable/abc_text_select_handle_left_mtrl_light = 0x7f06004b
com.chessvision.app:attr/actionBarTheme = 0x7f020009
com.chessvision.app:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.chessvision.app:attr/buttonIconDimen = 0x7f02003f
com.chessvision.app:attr/actionBarItemBackground = 0x7f020001
com.chessvision.app:id/decor_content_parent = 0x7f07004f
com.chessvision.app:color/abc_tint_edittext = 0x7f040014
com.chessvision.app:dimen/notification_large_icon_height = 0x7f050062
com.chessvision.app:attr/actionBarSize = 0x7f020003
com.chessvision.app:attr/actionBarTabStyle = 0x7f020007
com.chessvision.app:attr/alertDialogStyle = 0x7f020024
com.chessvision.app:attr/itemPadding = 0x7f020096
com.chessvision.app:dimen/abc_text_size_title_material_toolbar = 0x7f05004d
com.chessvision.app:attr/hideOnContentScroll = 0x7f02008a
com.chessvision.app:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.chessvision.app:attr/actionBarSplitStyle = 0x7f020004
com.chessvision.app:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0d0029
com.chessvision.app:dimen/hint_pressed_alpha_material_light = 0x7f05005d
com.chessvision.app:layout/custom_dialog = 0x7f0a001c
com.chessvision.app:color/material_grey_50 = 0x7f04003b
com.chessvision.app:attr/progressBarPadding = 0x7f0200c1
com.chessvision.app:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0d00f4
com.chessvision.app:string/status_bar_notification_info_overflow = 0x7f0c00a3
com.chessvision.app:attr/windowFixedWidthMajor = 0x7f020118
com.chessvision.app:attr/paddingTopNoTitle = 0x7f0200b9
com.chessvision.app:attr/actionModeSelectAllDrawable = 0x7f020018
com.chessvision.app:style/Base.Widget.AppCompat.ActionBar = 0x7f0d0067
com.chessvision.app:dimen/compat_button_padding_horizontal_material = 0x7f050050
com.chessvision.app:style/Widget.Compat.NotificationActionContainer = 0x7f0d015f
com.chessvision.app:id/icon = 0x7f070064
com.chessvision.app:anim/abc_slide_in_top = 0x7f010007
com.chessvision.app:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.chessvision.app:style/ThemeOverlay.AppCompat.Light = 0x7f0d0115
com.chessvision.app:attr/collapseIcon = 0x7f02004a
com.chessvision.app:attr/actionModePopupWindowStyle = 0x7f020017
com.chessvision.app:string/m3c_tooltip_pane_description = 0x7f0c0083
com.chessvision.app:string/m3c_dropdown_menu_expanded = 0x7f0c0072
com.chessvision.app:attr/iconTintMode = 0x7f02008f
com.chessvision.app:string/piece_pawn = 0x7f0c008e
com.chessvision.app:id/top = 0x7f0700b4
com.chessvision.app:attr/state_above_anchor = 0x7f0200dc
com.chessvision.app:attr/searchHintIcon = 0x7f0200cb
com.chessvision.app:drawable/abc_ic_star_black_48dp = 0x7f060023
com.chessvision.app:dimen/highlight_alpha_material_colored = 0x7f050057
com.chessvision.app:attr/maxButtonHeight = 0x7f0200ac
com.chessvision.app:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.chessvision.app:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0d0082
com.chessvision.app:attr/selectableItemBackground = 0x7f0200cf
com.chessvision.app:attr/initialActivityCount = 0x7f020094
com.chessvision.app:color/abc_primary_text_disable_only_material_dark = 0x7f040008
com.chessvision.app:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0d00dc
com.chessvision.app:attr/windowNoTitle = 0x7f02011c
com.chessvision.app:id/end = 0x7f070055
com.chessvision.app:attr/listPreferredItemHeightLarge = 0x7f0200a4
com.chessvision.app:dimen/abc_action_bar_stacked_tab_max_width = 0x7f05000a
com.chessvision.app:color/call_notification_answer_color = 0x7f040028
com.chessvision.app:attr/goIcon = 0x7f020088
com.chessvision.app:attr/drawableStartCompat = 0x7f02006e
com.chessvision.app:string/m3c_date_picker_switch_to_day_selection = 0x7f0c0060
com.chessvision.app:attr/collapseContentDescription = 0x7f020049
com.chessvision.app:anim/abc_slide_in_bottom = 0x7f010006
com.chessvision.app:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0d0084
com.chessvision.app:attr/showDividers = 0x7f0200d3
com.chessvision.app:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f06003c
com.chessvision.app:dimen/abc_text_size_button_material = 0x7f05003e
com.chessvision.app:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
