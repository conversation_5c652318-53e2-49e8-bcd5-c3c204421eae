package com.chessvision.app.ui.theme

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.clickable
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlin.math.sin

// Material 3 Expressive Animation Specifications
object ChessExpressiveAnimations {

    // Spring specifications for different interaction types
    val gentleSpring = spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessLow
    )

    val responsiveSpring = spring<Float>(
        dampingRatio = Spring.DampingRatioLowBouncy,
        stiffness = Spring.StiffnessMedium
    )

    val snappySpring = spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessHigh
    )

    // Piece movement animation with physics
    val pieceMovement = spring<Float>(
        dampingRatio = Spring.DampingRatioMediumBouncy,
        stiffness = Spring.StiffnessMediumLow
    )

    // UI entrance animations
    val slideInFromBottom = slideInVertically(
        initialOffsetY = { it },
        animationSpec = tween(600, easing = EaseOutCubic)
    ) + fadeIn(animationSpec = tween(600))

    val slideInFromTop = slideInVertically(
        initialOffsetY = { -it },
        animationSpec = tween(600, easing = EaseOutCubic)
    ) + fadeIn(animationSpec = tween(600))

    val slideInFromLeft = slideInHorizontally(
        initialOffsetX = { -it },
        animationSpec = tween(500, easing = EaseOutCubic)
    ) + fadeIn(animationSpec = tween(500))

    val slideInFromRight = slideInHorizontally(
        initialOffsetX = { it },
        animationSpec = tween(500, easing = EaseOutCubic)
    ) + fadeIn(animationSpec = tween(500))

    // Scale animations for buttons and cards
    val scaleIn = scaleIn(
        initialScale = 0.8f,
        animationSpec = tween(400, easing = EaseOutBack)
    ) + fadeIn(animationSpec = tween(400))

    val scaleOut = scaleOut(
        targetScale = 0.8f,
        animationSpec = tween(300, easing = EaseInBack)
    ) + fadeOut(animationSpec = tween(300))

    // Staggered animations for lists
    fun staggeredSlideIn(index: Int, delayPerItem: Int = 100) = slideInVertically(
        initialOffsetY = { it },
        animationSpec = tween(
            durationMillis = 600,
            delayMillis = index * delayPerItem,
            easing = EaseOutCubic
        )
    ) + fadeIn(
        animationSpec = tween(
            durationMillis = 600,
            delayMillis = index * delayPerItem
        )
    )
}

// Adaptive layout utilities for different screen sizes
@Composable
fun rememberAdaptiveLayout(): AdaptiveLayoutInfo {
    val configuration = LocalConfiguration.current

    return remember(configuration.screenWidthDp, configuration.screenHeightDp) {
        AdaptiveLayoutInfo(
            screenWidth = configuration.screenWidthDp.dp,
            screenHeight = configuration.screenHeightDp.dp,
            isCompact = configuration.screenWidthDp < 600,
            isMedium = configuration.screenWidthDp in 600..839,
            isExpanded = configuration.screenWidthDp >= 840,
            isLandscape = configuration.screenWidthDp > configuration.screenHeightDp
        )
    }
}

data class AdaptiveLayoutInfo(
    val screenWidth: Dp,
    val screenHeight: Dp,
    val isCompact: Boolean,
    val isMedium: Boolean,
    val isExpanded: Boolean,
    val isLandscape: Boolean
) {
    // Adaptive spacing based on screen size
    val spacing = when {
        isExpanded -> 24.dp
        isMedium -> 20.dp
        else -> 16.dp
    }

    val cardPadding = when {
        isExpanded -> 20.dp
        isMedium -> 16.dp
        else -> 12.dp
    }

    val boardSize = when {
        isExpanded -> minOf(screenWidth * 0.6f, screenHeight * 0.7f)
        isMedium -> minOf(screenWidth * 0.7f, screenHeight * 0.8f)
        else -> minOf(screenWidth * 0.9f, screenHeight * 0.6f)
    }
}

// Expressive hover and press animations
@Composable
fun Modifier.expressiveClickable(
    enabled: Boolean = true,
    onClick: () -> Unit
): Modifier {
    var isPressed by remember { mutableStateOf(false) }

    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = ChessExpressiveAnimations.responsiveSpring,
        label = "press_scale"
    )

    return this
        .graphicsLayer {
            scaleX = scale
            scaleY = scale
        }
        .then(
            if (enabled) {
                Modifier.clickable {
                    onClick()
                }
            } else Modifier
        )
}

// Breathing animation for loading states
@Composable
fun Modifier.breathingAnimation(
    enabled: Boolean = true,
    minAlpha: Float = 0.6f,
    maxAlpha: Float = 1f,
    duration: Int = 2000
): Modifier {
    val infiniteTransition = rememberInfiniteTransition(label = "breathing")

    val alpha by infiniteTransition.animateFloat(
        initialValue = minAlpha,
        targetValue = maxAlpha,
        animationSpec = infiniteRepeatable(
            animation = tween(duration, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "breathing_alpha"
    )

    return if (enabled) {
        this.graphicsLayer { this.alpha = alpha }
    } else this
}

// Shimmer effect for loading cards
@Composable
fun Modifier.shimmerEffect(
    enabled: Boolean = true,
    duration: Int = 1500
): Modifier {
    val infiniteTransition = rememberInfiniteTransition(label = "shimmer")

    val offset by infiniteTransition.animateFloat(
        initialValue = -1f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(duration, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "shimmer_offset"
    )

    return if (enabled) {
        this.graphicsLayer {
            translationX = offset * size.width
        }
    } else this
}

// Floating animation for chess pieces
@Composable
fun Modifier.floatingAnimation(
    enabled: Boolean = true,
    amplitude: Float = 2f,
    duration: Int = 3000
): Modifier {
    val infiniteTransition = rememberInfiniteTransition(label = "floating")

    val offsetY by infiniteTransition.animateFloat(
        initialValue = -amplitude,
        targetValue = amplitude,
        animationSpec = infiniteRepeatable(
            animation = tween(duration, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "floating_offset"
    )

    return if (enabled) {
        this.graphicsLayer {
            translationY = offsetY
        }
    } else this
}
