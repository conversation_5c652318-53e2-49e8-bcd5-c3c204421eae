import os
import argparse
import subprocess
import json
import shutil
from datetime import datetime

def run_command(command):
    """Run a command and return its output"""
    print(f"Running: {' '.join(command)}")
    result = subprocess.run(command, capture_output=True, text=True)
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return None
    return result.stdout

def create_model_version_record(model_path, feedback_db_path, metrics=None):
    """Create a record of this model version"""
    # Create models directory if it doesn't exist
    models_dir = "model_versions"
    os.makedirs(models_dir, exist_ok=True)
    
    # Create a record file if it doesn't exist
    record_path = os.path.join(models_dir, "version_history.json")
    if os.path.exists(record_path):
        with open(record_path, 'r') as f:
            records = json.load(f)
    else:
        records = {
            "versions": [],
            "current_version": 0
        }
    
    # Get feedback count
    feedback_count = 0
    if os.path.exists(feedback_db_path):
        with open(feedback_db_path, 'r') as f:
            db = json.load(f)
            feedback_count = len(db.get("feedback_items", []))
    
    # Create a new version record
    version = len(records["versions"]) + 1
    timestamp = datetime.now().isoformat()
    
    new_model_filename = f"model_v{version}.pt"
    new_model_path = os.path.join(models_dir, new_model_filename)
    
    # Copy the model file
    shutil.copy(model_path, new_model_path)
    
    version_record = {
        "version": version,
        "model_path": new_model_path,
        "base_model": model_path,
        "created_at": timestamp,
        "feedback_count": feedback_count,
        "metrics": metrics or {}
    }
    
    records["versions"].append(version_record)
    records["current_version"] = version
    
    # Save the updated records
    with open(record_path, 'w') as f:
        json.dump(records, f, indent=2)
    
    print(f"Created model version {version} at {new_model_path}")
    return new_model_path

def get_current_model():
    """Get the path to the current model version"""
    record_path = os.path.join("model_versions", "version_history.json")
    if not os.path.exists(record_path):
        return None
    
    with open(record_path, 'r') as f:
        records = json.load(f)
    
    current_version = records.get("current_version", 0)
    if current_version == 0:
        return None
    
    for version in records.get("versions", []):
        if version.get("version") == current_version:
            model_path = version.get("model_path")
            if os.path.exists(model_path):
                return model_path
    
    return None

def run_feedback_loop(base_model, image_dir, test_dir, epochs=10, batch_size=16, min_feedback=5):
    """Run the complete feedback loop"""
    # Set up paths
    feedback_db = "feedback_database.json"
    training_data_dir = "feedback_training_data"
    output_dir = "runs/finetune"
    
    # Get the current model if it exists, otherwise use the base model
    current_model = get_current_model() or base_model
    print(f"Using model: {current_model}")
    
    # Process each image and collect feedback
    image_files = [os.path.join(image_dir, f) for f in os.listdir(image_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    for image_file in image_files:
        print(f"\nProcessing image: {image_file}")
        
        # Run the feedback collection tool
        command = [
            "python", "collect_feedback.py",
            "--model", current_model,
            "--image", image_file,
            "--db", feedback_db,
            "--output", "feedback_output"
        ]
        run_command(command)
    
    # Check if we have enough feedback to fine-tune
    if os.path.exists(feedback_db):
        with open(feedback_db, 'r') as f:
            db = json.load(f)
            feedback_count = len(db.get("feedback_items", []))
        
        print(f"\nCollected {feedback_count} feedback items")
        
        if feedback_count >= min_feedback:
            print(f"Sufficient feedback ({feedback_count} items) collected for fine-tuning")
            
            # Fine-tune the model
            command = [
                "python", "finetune_model.py",
                "--model", current_model,
                "--feedback", feedback_db,
                "--epochs", str(epochs),
                "--batch", str(batch_size),
                "--data-dir", training_data_dir,
                "--output-dir", output_dir
            ]
            
            if test_dir:
                command.extend(["--test-dir", test_dir])
            
            output = run_command(command)
            
            # Extract the path to the best model
            best_model_path = None
            for line in output.split('\n'):
                if "Fine-tuning complete. New model saved to" in line:
                    best_model_path = line.split("New model saved to")[1].strip()
                    break
            
            if best_model_path and os.path.exists(best_model_path):
                # Extract metrics if available
                metrics = {}
                in_metrics_section = False
                for line in output.split('\n'):
                    if "Evaluation Results:" in line:
                        in_metrics_section = True
                        continue
                    
                    if in_metrics_section and ":" in line:
                        key, value = line.split(":", 1)
                        metrics[key.strip()] = float(value.strip())
                
                # Create a new model version
                new_model_path = create_model_version_record(best_model_path, feedback_db, metrics)
                print(f"Model fine-tuning complete. New model version created at {new_model_path}")
                
                # Clear the feedback database after successful fine-tuning
                if os.path.exists(feedback_db):
                    # Backup the old database
                    backup_path = f"{feedback_db}.{datetime.now().strftime('%Y%m%d_%H%M%S')}.bak"
                    shutil.copy(feedback_db, backup_path)
                    
                    # Create a new empty database
                    empty_db = {
                        "feedback_items": [],
                        "metadata": {
                            "created_at": datetime.now().isoformat(),
                            "last_updated": datetime.now().isoformat(),
                            "total_items": 0
                        }
                    }
                    with open(feedback_db, 'w') as f:
                        json.dump(empty_db, f, indent=2)
                    
                    print(f"Feedback database cleared. Backup saved to {backup_path}")
            else:
                print("Error: Could not find the fine-tuned model")
        else:
            print(f"Not enough feedback for fine-tuning. Need at least {min_feedback}, have {feedback_count}")
    else:
        print("No feedback database found")

def main():
    parser = argparse.ArgumentParser(description="Run the chess piece detection feedback loop")
    parser.add_argument("--base-model", required=True, help="Path to the base YOLO model")
    parser.add_argument("--image-dir", required=True, help="Directory containing images to process")
    parser.add_argument("--test-dir", help="Directory with test images for evaluation")
    parser.add_argument("--epochs", type=int, default=10, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16, help="Batch size")
    parser.add_argument("--min-feedback", type=int, default=5, help="Minimum feedback items required for fine-tuning")
    
    args = parser.parse_args()
    
    run_feedback_loop(
        args.base_model,
        args.image_dir,
        args.test_dir,
        args.epochs,
        args.batch,
        args.min_feedback
    )

if __name__ == "__main__":
    main()
