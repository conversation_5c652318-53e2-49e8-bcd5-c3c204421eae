import os
import json
import argparse
import shutil
import cv2
import numpy as np
from ultralytics import YOLO
from datetime import datetime
import torch

# Class names
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

def non_max_suppression(boxes, scores, classes, iou_threshold=0.5):
    """Apply non-maximum suppression to eliminate duplicate detections"""
    # Convert to numpy arrays if they're not already
    boxes = np.array(boxes)
    scores = np.array(scores)
    classes = np.array(classes)
    
    # Sort by confidence score
    indices = np.argsort(scores)[::-1]
    boxes = boxes[indices]
    scores = scores[indices]
    classes = classes[indices]
    
    keep = []
    while len(indices) > 0:
        # Keep the detection with highest confidence
        keep.append(indices[0])
        
        # Calculate IoU with remaining boxes
        ious = []
        for i in range(1, len(indices)):
            iou = calculate_iou(boxes[0], boxes[i])
            ious.append(iou)
        
        # Keep only boxes with IoU less than threshold
        mask = np.array(ious) < iou_threshold
        indices = indices[1:][mask]
        boxes = boxes[1:][mask]
        scores = scores[1:][mask]
        classes = classes[1:][mask]
    
    return keep

def calculate_iou(box1, box2):
    """Calculate IoU between two boxes"""
    # Box format: [x1, y1, x2, y2]
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i < x1_i or y2_i < y1_i:
        return 0.0
    
    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)
    
    # Calculate union area
    box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
    box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = box1_area + box2_area - intersection_area
    
    return intersection_area / union_area

def run_inference_with_nms(model_path, image_path, conf_threshold=0.5, iou_threshold=0.7):
    """Run inference with non-maximum suppression to eliminate duplicates"""
    # Load model
    model = YOLO(model_path)
    
    # Run inference
    results = model.predict(image_path, conf=conf_threshold)[0]
    
    # Get the original image
    img = cv2.imread(image_path)
    
    # Get detection data
    boxes = results.boxes.xyxy.cpu().numpy()
    cls_ids = results.boxes.cls.cpu().numpy().astype(int)
    confs = results.boxes.conf.cpu().numpy()
    
    # Apply non-maximum suppression
    keep_indices = non_max_suppression(boxes, confs, cls_ids, iou_threshold)
    
    # Keep only the selected detections
    filtered_boxes = boxes[keep_indices]
    filtered_cls_ids = cls_ids[keep_indices]
    filtered_confs = confs[keep_indices]
    
    return img, filtered_boxes, filtered_cls_ids, filtered_confs

def prepare_training_data(feedback_db_path, output_dir):
    """Prepare training data from feedback database"""
    # Load feedback database
    with open(feedback_db_path, 'r') as f:
        db = json.load(f)
    
    feedback_items = db["feedback_items"]
    if not feedback_items:
        print("No feedback items found in the database.")
        return None
    
    # Create directories
    images_dir = os.path.join(output_dir, "images")
    labels_dir = os.path.join(output_dir, "labels")
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(labels_dir, exist_ok=True)
    
    # Process each feedback item
    processed_images = set()
    for item in feedback_items:
        image_path = item["image_path"]
        if not os.path.exists(image_path):
            print(f"Warning: Image {image_path} not found, skipping")
            continue
        
        # Get image dimensions
        img = cv2.imread(image_path)
        if img is None:
            print(f"Warning: Could not read image {image_path}, skipping")
            continue
        
        h, w = img.shape[:2]
        
        # Create a unique name for this image
        base_name = os.path.basename(image_path)
        name_without_ext = os.path.splitext(base_name)[0]
        
        # If we've already processed this image, use a different name
        if name_without_ext in processed_images:
            name_without_ext = f"{name_without_ext}_{len(processed_images)}"
        
        processed_images.add(name_without_ext)
        
        # Copy the image to the training directory
        dest_img_path = os.path.join(images_dir, f"{name_without_ext}.jpg")
        shutil.copy(image_path, dest_img_path)
        
        # Create a label file for this image
        label_path = os.path.join(labels_dir, f"{name_without_ext}.txt")
        
        # Get the corrected box and class
        box = item["box"]  # [x1, y1, x2, y2]
        correct_class = item["correct_class"]
        class_id = CLASS_NAMES.index(correct_class)
        
        # Convert box to YOLO format: [class_id, x_center, y_center, width, height]
        # All values normalized to [0, 1]
        x1, y1, x2, y2 = box
        x_center = (x1 + x2) / (2 * w)
        y_center = (y1 + y2) / (2 * h)
        width = (x2 - x1) / w
        height = (y2 - y1) / h
        
        # Write the label to the file
        with open(label_path, 'a') as f:
            f.write(f"{class_id} {x_center} {y_center} {width} {height}\n")
    
    # Create a dataset YAML file
    yaml_path = os.path.join(output_dir, "dataset.yaml")
    with open(yaml_path, 'w') as f:
        f.write(f"path: {output_dir}\n")
        f.write("train: images\n")
        f.write("val: images\n")
        f.write(f"nc: {len(CLASS_NAMES)}\n")
        f.write(f"names: {CLASS_NAMES}\n")
    
    print(f"Prepared training data with {len(processed_images)} images in {output_dir}")
    return yaml_path

def finetune_model(model_path, yaml_path, epochs=15, batch_size=16, output_dir="runs/finetune"):
    """Fine-tune the model on the feedback data"""
    # Load the model
    model = YOLO(model_path)
    
    # Create a timestamp for this training run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Fine-tune the model
    results = model.train(
        data=yaml_path,
        epochs=epochs,
        batch=batch_size,
        imgsz=416,
        patience=5,  # Early stopping patience
        project=output_dir,
        name=f"feedback_finetune_{timestamp}",
        exist_ok=True,
        pretrained=True,
        resume=False
    )
    
    # Return the path to the best model
    best_model_path = os.path.join(output_dir, f"feedback_finetune_{timestamp}", "weights", "best.pt")
    if os.path.exists(best_model_path):
        print(f"Fine-tuning complete. Best model saved to {best_model_path}")
        return best_model_path
    else:
        print("Warning: Best model not found after training")
        return None

def evaluate_model(model_path, test_images_dir, iou_threshold=0.7):
    """Evaluate the fine-tuned model on test images"""
    model = YOLO(model_path)
    
    # Get all images in the test directory
    test_images = [os.path.join(test_images_dir, f) for f in os.listdir(test_images_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    if not test_images:
        print(f"No test images found in {test_images_dir}")
        return
    
    # Run validation with higher IoU threshold
    results = model.val(data=test_images, imgsz=416, iou=iou_threshold)
    
    print("\nEvaluation Results:")
    print(f"mAP50: {results.box.map50:.4f}")
    print(f"mAP50-95: {results.box.map:.4f}")
    print(f"Precision: {results.box.precision:.4f}")
    print(f"Recall: {results.box.recall:.4f}")
    
    return results

def main():
    parser = argparse.ArgumentParser(description="Fine-tune chess piece detection model with feedback")
    parser.add_argument("--model", required=True, help="Path to the base YOLO model")
    parser.add_argument("--feedback", required=True, help="Path to the feedback database")
    parser.add_argument("--epochs", type=int, default=15, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=16, help="Batch size")
    parser.add_argument("--iou", type=float, default=0.7, help="IoU threshold for NMS")
    parser.add_argument("--conf", type=float, default=0.5, help="Confidence threshold")
    parser.add_argument("--data-dir", default="feedback_training_data", help="Directory to store training data")
    parser.add_argument("--output-dir", default="runs/finetune", help="Directory to save training results")
    parser.add_argument("--test-dir", help="Directory with test images for evaluation")
    
    args = parser.parse_args()
    
    # Prepare training data
    yaml_path = prepare_training_data(args.feedback, args.data_dir)
    if not yaml_path:
        return
    
    # Fine-tune the model
    best_model_path = finetune_model(args.model, yaml_path, args.epochs, args.batch, args.output_dir)
    if not best_model_path:
        return
    
    # Evaluate the model if test directory is provided
    if args.test_dir and os.path.exists(args.test_dir):
        evaluate_model(best_model_path, args.test_dir, args.iou)
    
    print(f"\nFine-tuning complete. New model saved to {best_model_path}")
    print("Use this model for future detections to improve accuracy.")

if __name__ == "__main__":
    main()
