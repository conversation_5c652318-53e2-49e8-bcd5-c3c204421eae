"""
Dataset class for handling real chess board images.
"""

import os
import json
import numpy as np
import cv2
import torch
from torch.utils.data import Dataset, DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2
import matplotlib.pyplot as plt

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import INPUT_SIZE, BATCH_SIZE, HEATMAP_SIGMA


def generate_gaussian_heatmap(height, width, center_x, center_y, sigma=HEATMAP_SIGMA):
    """
    Generate a Gaussian heatmap centered at (center_x, center_y) with improved focus on corners.

    Args:
        height (int): Height of the heatmap.
        width (int): Width of the heatmap.
        center_x (float): X-coordinate of the center.
        center_y (float): Y-coordinate of the center.
        sigma (float): Standard deviation of the Gaussian.

    Returns:
        numpy.ndarray: Gaussian heatmap.
    """
    # Create coordinate grids
    x = np.arange(0, width, 1, float)
    y = np.arange(0, height, 1, float)
    y = y[:, np.newaxis]

    # Center coordinates
    x0 = center_x
    y0 = center_y

    # Calculate distance from center
    distance_squared = ((x - x0) ** 2 + (y - y0) ** 2)

    # Create Gaussian heatmap with sharper falloff
    heatmap = np.exp(-distance_squared / (2 * sigma ** 2))

    # Apply threshold to make it more focused (optional)
    # This helps ensure the heatmap is more concentrated at the corner
    heatmap[heatmap < 0.01] = 0

    # Normalize to ensure max value is 1
    if np.max(heatmap) > 0:
        heatmap = heatmap / np.max(heatmap)

    return heatmap


class RealChessBoardDataset(Dataset):
    """
    Dataset for real chess board images.
    """
    def __init__(self, data_dir, annotation_file, transform=None, split='train'):
        """
        Args:
            data_dir (str): Directory with all the images.
            annotation_file (str): Path to the annotation file.
            transform (callable, optional): Optional transform to be applied on a sample.
            split (str): 'train', 'val', or 'test'.
        """
        self.data_dir = data_dir
        self.transform = transform
        self.split = split

        # Load annotations
        with open(annotation_file, 'r') as f:
            self.annotations = json.load(f)

        # Split the dataset
        if split == 'train':
            self.indices = list(range(int(len(self.annotations) * 0.8)))
        elif split == 'val':
            self.indices = list(range(int(len(self.annotations) * 0.8), len(self.annotations)))
        else:  # test
            self.indices = list(range(len(self.annotations)))

    def __len__(self):
        return len(self.indices)

    def __getitem__(self, idx):
        """
        Get a sample from the dataset.
        """
        idx = self.indices[idx]
        annotation = self.annotations[idx]

        # Load image
        image_path = os.path.join(self.data_dir, annotation['image'])
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Get corners
        corners = np.array(annotation['corners'], dtype=np.float32)  # [x1, y1, x2, y2, x3, y3, x4, y4]

        # Create mask (polygon filled with ones)
        mask = np.zeros((image.shape[0], image.shape[1]), dtype=np.float32)
        corners_points = corners.reshape(-1, 2)
        cv2.fillPoly(mask, [corners_points.astype(np.int32)], 1)

        # Store original dimensions
        orig_height, orig_width = image.shape[:2]
        orig_img_shape = image.shape

        # Resize image and mask to INPUT_SIZE
        image = cv2.resize(image, (INPUT_SIZE[1], INPUT_SIZE[0]))
        mask = cv2.resize(mask, (INPUT_SIZE[1], INPUT_SIZE[0]))

        # Calculate scale factors
        scale_x = INPUT_SIZE[1] / orig_width
        scale_y = INPUT_SIZE[0] / orig_height

        # Scale the corner coordinates to match INPUT_SIZE
        scaled_corners = np.zeros_like(corners)
        for i in range(0, len(corners), 2):
            scaled_corners[i] = corners[i] * scale_x
            scaled_corners[i+1] = corners[i+1] * scale_y

        # Use the scaled corners for the rest of the processing
        corners = scaled_corners

        # Create corner heatmaps at INPUT_SIZE dimensions
        corner_heatmaps = np.zeros((4, INPUT_SIZE[0], INPUT_SIZE[1]), dtype=np.float32)

        for i in range(4):
            # Use the already scaled corner coordinates
            x, y = corners[i*2], corners[i*2+1]
            corner_heatmaps[i] = generate_gaussian_heatmap(INPUT_SIZE[0], INPUT_SIZE[1], x, y)

        # Apply transformations
        if self.transform:
            # For albumentations, we need to format keypoints as [x, y, visibility]
            keypoints_list = []
            for i in range(0, len(corners), 2):
                keypoints_list.append([corners[i], corners[i+1], 1])  # 1 for visible

            # Store original image dimensions for reference
            orig_h, orig_w = image.shape[:2]

            # Apply transform directly - we've removed rotations and other transforms
            # that could cause corners to go outside the image
            transformed = self.transform(
                image=image,
                mask=mask,
                keypoints=keypoints_list
            )

            # Check if all keypoints are within image boundaries
            all_visible = True
            img_h, img_w = transformed['image'].shape[1:] if isinstance(transformed['image'], torch.Tensor) else transformed['image'].shape[:2]

            # Also check if we have all 4 keypoints
            if len(transformed['keypoints']) != 4:
                all_visible = False
                print(f"Warning: Transform lost keypoints. Expected 4, got {len(transformed['keypoints'])}.")
                # If we don't have all keypoints, use the original keypoints
                transformed['keypoints'] = keypoints_list
            else:
                # Check if any keypoints are outside the image boundaries
                for kp in transformed['keypoints']:
                    x, y = kp[0], kp[1]
                    if x < 0 or y < 0 or x >= img_w or y >= img_h:
                        all_visible = False
                        print(f"Warning: Keypoint ({x:.1f}, {y:.1f}) outside image boundaries ({img_w}x{img_h}).")
                        break

            # Handle heatmaps and corner order properly during transformations
            # First, detect what transformations were applied by comparing keypoints
            orig_keypoints = keypoints_list
            transformed_keypoints = transformed['keypoints']

            # Check for horizontal flip
            horizontal_flip = False
            vertical_flip = False

            # If we have all 4 keypoints, we can detect flips by analyzing their positions
            if len(transformed_keypoints) == 4:
                # Original corners: TL, TR, BR, BL
                # After horizontal flip: TR, TL, BL, BR
                # After vertical flip: BL, BR, TR, TL
                # After both flips: BR, BL, TL, TR

                # Get original corners in x,y format
                orig_corners = np.array([(kp[0], kp[1]) for kp in orig_keypoints])

                # Get transformed corners in x,y format
                trans_corners = np.array([(kp[0], kp[1]) for kp in transformed_keypoints])

                # Calculate center of original corners
                orig_center_x = np.mean(orig_corners[:, 0])
                orig_center_y = np.mean(orig_corners[:, 1])

                # Check if corners have moved from left to right or top to bottom
                # This is a simplified approach - for more complex transforms, we'd need more sophisticated detection

                # Check for horizontal flip by comparing left/right positions
                orig_left_side = [i for i, (x, y) in enumerate(orig_corners) if x < orig_center_x]
                trans_left_side = [i for i, (x, y) in enumerate(trans_corners) if x < orig_center_x]

                if set(orig_left_side) != set(trans_left_side):
                    horizontal_flip = True

                # Check for vertical flip by comparing top/bottom positions
                orig_top_side = [i for i, (x, y) in enumerate(orig_corners) if y < orig_center_y]
                trans_top_side = [i for i, (x, y) in enumerate(trans_corners) if y < orig_center_y]

                if set(orig_top_side) != set(trans_top_side):
                    vertical_flip = True

            # Instead of transforming the heatmaps, regenerate them based on the transformed keypoints
            # This ensures perfect alignment between keypoints and heatmaps

            # Get the transformed image dimensions
            img_h, img_w = transformed['image'].shape[1:] if isinstance(transformed['image'], torch.Tensor) else transformed['image'].shape[:2]

            # Create new heatmaps based on transformed keypoints
            transformed_heatmaps = np.zeros((4, img_h, img_w), dtype=np.float32)

            # Generate new heatmaps for each transformed keypoint
            for i in range(len(transformed['keypoints'])):
                if i < 4:  # Ensure we only process up to 4 keypoints
                    x, y = transformed['keypoints'][i][0], transformed['keypoints'][i][1]

                    # Only generate heatmap if keypoint is within image boundaries
                    if 0 <= x < img_w and 0 <= y < img_h:
                        transformed_heatmaps[i] = generate_gaussian_heatmap(img_h, img_w, x, y, sigma=HEATMAP_SIGMA)

            # If we have fewer than 4 keypoints, the remaining heatmaps will be zeros

            image = transformed['image']
            mask = transformed['mask']
            corner_heatmaps = transformed_heatmaps  # Use our manually transformed heatmaps

            # Convert keypoints back to flat array
            corners = []
            for kp in transformed['keypoints']:
                corners.extend([kp[0], kp[1]])
            corners = np.array(corners, dtype=np.float32)

            # Ensure corners has the correct shape (8,) for 4 corners
            if len(corners) != 8:
                # If we have fewer than 4 corners after transformation,
                # use the original corners scaled to the new image size
                # This preserves the corner information instead of using dummy zeros
                if len(corners) == 0:
                    # Use original corners scaled to the new dimensions
                    orig_corners = np.array(annotation['corners'], dtype=np.float32)
                    # Scale corners to match the transformed image dimensions
                    img_h, img_w = image.shape[:2] if isinstance(image, np.ndarray) else (image.shape[1], image.shape[2])
                    orig_h, orig_w = orig_img_shape[:2] if 'orig_img_shape' in locals() else (INPUT_SIZE[0], INPUT_SIZE[1])

                    scale_x = img_w / orig_w
                    scale_y = img_h / orig_h

                    scaled_corners = np.zeros(8, dtype=np.float32)
                    for i in range(0, 8, 2):
                        scaled_corners[i] = orig_corners[i] * scale_x
                        scaled_corners[i+1] = orig_corners[i+1] * scale_y

                    corners = scaled_corners
                else:
                    # We have some corners but not all 4, pad only what's missing
                    padded_corners = np.zeros(8, dtype=np.float32)
                    padded_corners[:len(corners)] = corners
                    corners = padded_corners

            # Convert heatmaps to tensor
            if not isinstance(corner_heatmaps, torch.Tensor):
                corner_heatmaps = torch.from_numpy(corner_heatmaps).float()
        else:
            # Convert to tensors
            image = torch.from_numpy(image.transpose((2, 0, 1))).float() / 255.0
            mask = torch.from_numpy(mask).float().unsqueeze(0)
            corner_heatmaps = torch.from_numpy(corner_heatmaps).float()

        return {
            'image': image,
            'mask': mask,
            'corner_heatmaps': corner_heatmaps,
            'corners': torch.from_numpy(corners),
            'filename': os.path.basename(image_path)
        }


def get_transforms(phase):
    """
    Get transformations for different phases.

    Args:
        phase (str): 'train', 'val', or 'test'.

    Returns:
        albumentations.Compose: Composition of transforms.
    """
    if phase == 'train':
        return A.Compose([
            # Note: We're not resizing here because we've already resized the heatmaps
            # and scaled the coordinates accordingly
            A.HorizontalFlip(p=0.5),
            A.VerticalFlip(p=0.5),

            # Color and noise augmentations
            A.RandomBrightnessContrast(p=0.5),
            A.GaussNoise(p=0.3),
            A.Blur(blur_limit=3, p=0.2),  # Add blur to simulate different image qualities

            # Ensure consistent size with padding
            A.PadIfNeeded(
                min_height=INPUT_SIZE[0],
                min_width=INPUT_SIZE[1],
                border_mode=cv2.BORDER_CONSTANT,
                p=1.0
            ),

            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
    else:
        return A.Compose([
            # Note: We're not resizing here because we've already resized the heatmaps
            # and scaled the coordinates accordingly
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def create_annotation_file(data_dir, output_file):
    """
    Create an annotation file from manually labeled corner points.
    This is a helper function to create the initial annotation file.

    Args:
        data_dir (str): Directory with all the images.
        output_file (str): Path to save the annotation file.
    """
    annotations = []

    # Get all image files
    image_files = [f for f in os.listdir(data_dir) if f.endswith('.jpg') or f.endswith('.png')]

    for image_file in image_files:
        image_path = os.path.join(data_dir, image_file)
        image = cv2.imread(image_path)
        height, width = image.shape[:2]

        # Display the image for manual annotation
        plt.figure(figsize=(10, 8))
        plt.imshow(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        plt.title(f"Click on the 4 corners of the chess board in {image_file}\n"
                 "Order: top-left, top-right, bottom-right, bottom-left")
        plt.axis('on')

        # Get 4 corner points from user clicks
        corners = plt.ginput(4, timeout=0)
        plt.close()

        # Flatten corners to [x1, y1, x2, y2, x3, y3, x4, y4]
        corners_flat = []
        for corner in corners:
            corners_flat.extend([corner[0], corner[1]])

        # Add to annotations
        annotations.append({
            'image': image_file,
            'corners': corners_flat,
            'image_size': [width, height]
        })

        print(f"Annotated {image_file}")

    # Save annotations
    with open(output_file, 'w') as f:
        json.dump(annotations, f, indent=2)

    print(f"Saved annotations to {output_file}")


def get_data_loaders(data_dir, annotation_file):
    """
    Get data loaders for training, validation, and testing.

    Args:
        data_dir (str): Directory with all the images.
        annotation_file (str): Path to the annotation file.

    Returns:
        dict: Dictionary with data loaders for 'train', 'val', and 'test'.
    """
    train_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        split='train',
        transform=get_transforms('train')
    )

    val_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        split='val',
        transform=get_transforms('val')
    )

    test_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        split='test',
        transform=get_transforms('test')
    )

    return {
        'train': DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=0),
        'val': DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=0),
        'test': DataLoader(test_dataset, batch_size=1, shuffle=False, num_workers=0)
    }
