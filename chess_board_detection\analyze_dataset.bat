@echo off
REM Analyze class distribution in a YOLO dataset

set DATASET_PATH=%1
if "%DATASET_PATH%"=="" (
    set DATASET_PATH=chess_board_detection/piece_detection/enhanced_dataset_99plus
)

set OUTPUT_DIR=%2
if "%OUTPUT_DIR%"=="" (
    set OUTPUT_DIR=chess_board_detection/piece_detection/dataset_analysis
)

echo Analyzing dataset at %DATASET_PATH%...

python chess_board_detection/piece_detection/analyze_dataset.py ^
    %DATASET_PATH% ^
    --output_dir %OUTPUT_DIR%

echo.
echo Analysis completed. Results saved to %OUTPUT_DIR%
echo.
echo Opening results...

start "" "%OUTPUT_DIR%\class_distribution.png"

pause
