"""
Breakthrough U-Net V6 Training: Target 0.95+ Dice to exceed V5's 0.9341 performance and approach 0.99.
Advanced training with 5-scale fusion, unified attention, and progressive refinement.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.breakthrough_unet_v6_simple import get_breakthrough_v6_model
from chess_board_detection.dataset.augmented_segmentation_dataset import create_augmented_dataloaders

class AdvancedV6Loss(nn.Module):
    """Advanced loss function optimized for V6 breakthrough performance."""

    def __init__(self, alpha=0.25, gamma=2.0, dice_weight=2.5, focal_weight=1.0, smooth=1e-6):
        super(AdvancedV6Loss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.dice_weight = dice_weight
        self.focal_weight = focal_weight
        self.smooth = smooth

    def focal_loss(self, inputs, targets):
        """Enhanced focal loss for V6."""
        bce_loss = nn.functional.binary_cross_entropy_with_logits(inputs, targets, reduction='none')
        pt = torch.exp(-bce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * bce_loss
        return focal_loss.mean()

    def dice_loss(self, inputs, targets):
        """Enhanced dice loss for V6."""
        inputs = torch.sigmoid(inputs)
        inputs = inputs.view(-1)
        targets = targets.view(-1)

        intersection = (inputs * targets).sum()
        dice = (2. * intersection + self.smooth) / (inputs.sum() + targets.sum() + self.smooth)
        return 1 - dice

    def tversky_loss(self, inputs, targets, alpha=0.3, beta=0.7):
        """Tversky loss for better boundary detection."""
        inputs = torch.sigmoid(inputs)
        inputs = inputs.view(-1)
        targets = targets.view(-1)

        tp = (inputs * targets).sum()
        fp = (inputs * (1 - targets)).sum()
        fn = ((1 - inputs) * targets).sum()

        tversky = (tp + self.smooth) / (tp + alpha * fp + beta * fn + self.smooth)
        return 1 - tversky

    def forward(self, inputs, targets):
        focal = self.focal_loss(inputs, targets)
        dice = self.dice_loss(inputs, targets)
        tversky = self.tversky_loss(inputs, targets)

        total_loss = self.focal_weight * focal + self.dice_weight * dice + 0.5 * tversky

        return total_loss, {
            'focal': focal.item(),
            'dice': dice.item(),
            'tversky': tversky.item(),
            'total': total_loss.item()
        }

def calculate_v6_metrics(predictions, targets, threshold=0.5):
    """Calculate comprehensive metrics for V6 assessment."""
    with torch.no_grad():
        predictions = torch.sigmoid(predictions)
        pred_binary = (predictions > threshold).float()
        targets_binary = (targets > threshold).float()

        # Flatten
        pred_flat = pred_binary.view(-1)
        target_flat = targets_binary.view(-1)

        # Basic metrics
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum() - intersection

        iou = (intersection + 1e-6) / (union + 1e-6)
        dice = (2 * intersection + 1e-6) / (pred_flat.sum() + target_flat.sum() + 1e-6)

        # Advanced metrics
        tp = intersection
        fp = pred_flat.sum() - intersection
        fn = target_flat.sum() - intersection
        tn = len(pred_flat) - tp - fp - fn

        precision = (tp + 1e-6) / (tp + fp + 1e-6)
        recall = (tp + 1e-6) / (tp + fn + 1e-6)
        f1 = 2 * (precision * recall) / (precision + recall + 1e-6)

        # Specificity and sensitivity
        specificity = (tn + 1e-6) / (tn + fp + 1e-6)
        sensitivity = recall

        return {
            'iou': torch.clamp(iou, 0, 1).item(),
            'dice': torch.clamp(dice, 0, 1).item(),
            'precision': torch.clamp(precision, 0, 1).item(),
            'recall': torch.clamp(recall, 0, 1).item(),
            'f1': torch.clamp(f1, 0, 1).item(),
            'specificity': torch.clamp(specificity, 0, 1).item(),
            'sensitivity': torch.clamp(sensitivity, 0, 1).item()
        }

def train_epoch_v6(model, train_loader, criterion, optimizer, scaler, device, accumulation_steps=2):
    """Advanced training epoch for V6 breakthrough performance."""
    model.train()
    total_loss = 0
    total_metrics = {}
    num_batches = len(train_loader)

    optimizer.zero_grad()

    pbar = tqdm(train_loader, desc="🚀 V6 Breakthrough Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device, non_blocking=True)
        masks = masks.to(device, non_blocking=True)

        if masks.dim() == 3:
            masks = masks.unsqueeze(1)

        with autocast():
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            loss = loss / accumulation_steps

        # Backward pass
        scaler.scale(loss).backward()

        # Gradient accumulation
        if (batch_idx + 1) % accumulation_steps == 0:
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()

        # Calculate metrics
        batch_metrics = calculate_v6_metrics(outputs, masks)

        # Accumulate metrics
        total_loss += loss.item() * accumulation_steps
        for key, value in {**loss_metrics, **batch_metrics}.items():
            if key not in total_metrics:
                total_metrics[key] = 0
            total_metrics[key] += value

        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{loss.item() * accumulation_steps:.4f}',
            'Dice': f'{batch_metrics["dice"]:.4f}',
            'IoU': f'{batch_metrics["iou"]:.4f}',
            'F1': f'{batch_metrics["f1"]:.4f}'
        })

    # Average metrics
    avg_loss = total_loss / num_batches
    avg_metrics = {key: value / num_batches for key, value in total_metrics.items()}

    return avg_loss, avg_metrics

def validate_epoch_v6(model, val_loader, criterion, device):
    """Advanced validation epoch for V6 breakthrough assessment."""
    model.eval()
    total_loss = 0
    total_metrics = {}
    num_batches = len(val_loader)

    with torch.no_grad():
        pbar = tqdm(val_loader, desc="🎯 V6 Breakthrough Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)

            if masks.dim() == 3:
                masks = masks.unsqueeze(1)

            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)

            batch_metrics = calculate_v6_metrics(outputs, masks)

            # Accumulate
            total_loss += loss.item()
            for key, value in {**loss_metrics, **batch_metrics}.items():
                if key not in total_metrics:
                    total_metrics[key] = 0
                total_metrics[key] += value

            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{batch_metrics["dice"]:.4f}',
                'IoU': f'{batch_metrics["iou"]:.4f}',
                'F1': f'{batch_metrics["f1"]:.4f}'
            })

    avg_loss = total_loss / num_batches
    avg_metrics = {key: value / num_batches for key, value in total_metrics.items()}

    return avg_loss, avg_metrics

def train_breakthrough_v6():
    """Train V6 model to achieve 0.95+ Dice and target 0.99 performance."""

    # V6 Configuration for breakthrough performance
    config = {
        'dataset_dir': r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326",
        'save_dir': "chess_board_detection/breakthrough_v6_results",
        'epochs': 100,
        'batch_size': 4,  # Optimized for 6GB GPU
        'learning_rate': 1e-3,  # Optimal LR for V6 breakthrough
        'base_channels': 32,  # V6-32 for optimal performance
        'accumulation_steps': 2,  # Effective batch size = 8
        'num_workers': 0,
        'v5_baseline': 0.9341,  # V5 performance to exceed
        'target_dice': 0.95,  # Breakthrough target
        'stretch_target': 0.99,  # Stretch goal
    }

    print("🚀 BREAKTHROUGH U-NET V6 TRAINING")
    print("=" * 60)
    print("🎯 TARGET: Exceed V5's 0.9341 Dice and achieve 0.95+ with target 0.99!")
    print(f"🏆 V5 Baseline: {config['v5_baseline']:.4f} Dice")
    print(f"🚀 V6 Target: {config['target_dice']:.4f} Dice")
    print(f"🌟 V6 Stretch: {config['stretch_target']:.4f} Dice")
    print(f"Configuration: {json.dumps(config, indent=2)}")

    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    # Create save directory
    save_dir = Path(config['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)

    # Create dataloaders
    print("Creating enhanced dataloaders...")
    train_loader, val_loader = create_augmented_dataloaders(
        config['dataset_dir'],
        batch_size=config['batch_size'],
        train_split=0.8,
        num_workers=config['num_workers']
    )

    # Create V6 breakthrough model
    print("Creating Breakthrough U-Net V6...")
    model = get_breakthrough_v6_model(base_channels=config['base_channels'])
    model = model.to(device)

    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    v5_params = 4290977
    efficiency = total_params / v5_params

    print(f"V6 Model parameters: {total_params:,}")
    print(f"Efficiency vs V5: {efficiency:.2f}x ({(efficiency-1)*100:+.1f}% change)")
    print(f"🎯 Target: Achieve 0.95+ Dice with {total_params:,} params!")

    # Advanced loss function for V6
    criterion = AdvancedV6Loss(alpha=0.25, gamma=2.0, dice_weight=2.5, focal_weight=1.0)

    # Advanced optimizer for V6
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=1e-4,
        betas=(0.9, 0.999),
        eps=1e-8
    )

    # Advanced scheduler for V6
    scheduler = optim.lr_scheduler.OneCycleLR(
        optimizer,
        max_lr=config['learning_rate'],
        epochs=config['epochs'],
        steps_per_epoch=len(train_loader),
        pct_start=0.1,
        anneal_strategy='cos'
    )

    # Mixed precision
    scaler = GradScaler()

    # Training variables
    best_val_dice = 0
    v5_exceeded = False
    breakthrough_achieved = False
    stretch_achieved = False
    patience_counter = 0
    patience = 25

    print(f"🚀 Starting V6 breakthrough training for {config['epochs']} epochs...")
    start_time = time.time()

    return model, config, save_dir, train_loader, val_loader, device, criterion, optimizer, scheduler, scaler, best_val_dice, v5_exceeded, breakthrough_achieved, stretch_achieved, patience_counter, patience, start_time

if __name__ == "__main__":
    try:
        model, config, save_dir, train_loader, val_loader, device, criterion, optimizer, scheduler, scaler, best_val_dice, v5_exceeded, breakthrough_achieved, stretch_achieved, patience_counter, patience, start_time = train_breakthrough_v6()

        # Training history
        history = {
            'train_loss': [], 'val_loss': [],
            'train_dice': [], 'val_dice': [],
            'train_iou': [], 'val_iou': [],
            'train_f1': [], 'val_f1': []
        }

        for epoch in range(config['epochs']):
            print(f"\n🔥 Epoch {epoch+1}/{config['epochs']}")

            # Train
            train_loss, train_metrics = train_epoch_v6(
                model, train_loader, criterion, optimizer, scaler, device, config['accumulation_steps']
            )

            # Validate
            val_loss, val_metrics = validate_epoch_v6(
                model, val_loader, criterion, device
            )

            # Update scheduler
            scheduler.step()
            current_lr = optimizer.param_groups[0]['lr']

            # Save history
            history['train_loss'].append(train_loss)
            history['val_loss'].append(val_loss)
            history['train_dice'].append(train_metrics.get('dice', 0))
            history['val_dice'].append(val_metrics.get('dice', 0))
            history['train_iou'].append(train_metrics.get('iou', 0))
            history['val_iou'].append(val_metrics.get('iou', 0))
            history['train_f1'].append(train_metrics.get('f1', 0))
            history['val_f1'].append(val_metrics.get('f1', 0))

            # Print results
            val_dice = val_metrics.get('dice', 0)
            val_iou = val_metrics.get('iou', 0)
            val_f1 = val_metrics.get('f1', 0)

            print(f"Train - Loss: {train_loss:.4f}, Dice: {train_metrics.get('dice', 0):.4f}, IoU: {train_metrics.get('iou', 0):.4f}, F1: {train_metrics.get('f1', 0):.4f}")
            print(f"Val   - Loss: {val_loss:.4f}, Dice: {val_dice:.4f}, IoU: {val_iou:.4f}, F1: {val_f1:.4f}")
            print(f"LR: {current_lr:.6f}")

            # Check for V5 exceeded
            if val_dice > config['v5_baseline'] and not v5_exceeded:
                v5_exceeded = True
                print(f"🎉 V5 EXCEEDED! Val Dice: {val_dice:.4f} > {config['v5_baseline']:.4f}")
                print(f"🏆 V6 SURPASSES V5 PERFORMANCE!")

            # Check for breakthrough
            if val_dice >= config['target_dice'] and not breakthrough_achieved:
                breakthrough_achieved = True
                print(f"🎉 BREAKTHROUGH ACHIEVED! Val Dice: {val_dice:.4f} >= {config['target_dice']:.4f}")
                print(f"🏆 V6 ACHIEVES 0.95+ DICE TARGET!")

            # Check for stretch goal
            if val_dice >= config['stretch_target'] and not stretch_achieved:
                stretch_achieved = True
                print(f"🌟 STRETCH GOAL ACHIEVED! Val Dice: {val_dice:.4f} >= {config['stretch_target']:.4f}")
                print(f"🏆 V6 ACHIEVES 0.99 DICE STRETCH TARGET!")

            # Save best model
            if val_dice > best_val_dice:
                best_val_dice = val_dice
                patience_counter = 0
                torch.save(model.state_dict(), save_dir / "best_model.pth")

                if stretch_achieved:
                    print(f"🌟 NEW STRETCH RECORD! Val Dice: {val_dice:.4f}")
                elif breakthrough_achieved:
                    print(f"🏆 NEW BREAKTHROUGH RECORD! Val Dice: {val_dice:.4f}")
                elif v5_exceeded:
                    print(f"🚀 NEW V6 RECORD! Val Dice: {val_dice:.4f}")
                else:
                    print(f"✅ New best model saved! Val Dice: {val_dice:.4f}")
            else:
                patience_counter += 1

            # Save checkpoint every 20 epochs
            if (epoch + 1) % 20 == 0:
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'val_dice': val_dice,
                    'history': history,
                    'v5_exceeded': v5_exceeded,
                    'breakthrough_achieved': breakthrough_achieved,
                    'stretch_achieved': stretch_achieved
                }, save_dir / f"checkpoint_epoch_{epoch+1}.pth")

            # Early stopping (only if stretch goal achieved)
            if stretch_achieved and patience_counter >= patience:
                print(f"Early stopping: Stretch goal achieved and {patience} epochs without improvement")
                break

        # Save final results
        torch.save(model.state_dict(), save_dir / "final_model.pth")

        with open(save_dir / "training_history.json", 'w') as f:
            json.dump(history, f, indent=2)

        training_time = time.time() - start_time
        print(f"\n🎉 V6 Training completed in {training_time/3600:.2f} hours")
        print(f"🏆 Best validation Dice: {best_val_dice:.4f}")

        # Final analysis
        print(f"\n{'='*80}")
        print(f"🚀 BREAKTHROUGH U-NET V6 TRAINING COMPLETED!")
        print(f"{'='*80}")

        if stretch_achieved:
            print(f"🌟 STRETCH GOAL ACHIEVED! 🌟")
            print(f"🏆 V6 ACHIEVED 0.99 DICE TARGET!")
            print(f"🎯 Best Dice: {best_val_dice:.4f} >= {config['stretch_target']:.4f}")
        elif breakthrough_achieved:
            print(f"🎉 BREAKTHROUGH ACHIEVED! 🎉")
            print(f"🏆 V6 ACHIEVED 0.95+ DICE TARGET!")
            print(f"🎯 Best Dice: {best_val_dice:.4f} >= {config['target_dice']:.4f}")
        elif v5_exceeded:
            print(f"🚀 V5 EXCEEDED! V6 SURPASSES V5!")
            print(f"🏆 V6: {best_val_dice:.4f} > V5: {config['v5_baseline']:.4f}")
            print(f"🎯 Improvement: {((best_val_dice - config['v5_baseline']) / config['v5_baseline'] * 100):+.2f}%")
        else:
            print(f"🎯 Best Dice: {best_val_dice:.4f}")
            if best_val_dice > 0.93:
                print(f"🔥 Excellent performance! Very close to breakthrough.")
            elif best_val_dice > 0.90:
                print(f"✅ Strong performance! Consider longer training.")

        # Final comparison
        v5_dice = config['v5_baseline']
        improvement = ((best_val_dice - v5_dice) / v5_dice) * 100

        print(f"\n📊 FINAL V5 vs V6 COMPARISON:")
        print(f"V5: {v5_dice:.4f} Dice (4.3M params)")
        print(f"V6: {best_val_dice:.4f} Dice ({sum(p.numel() for p in model.parameters() if p.requires_grad)/1e6:.1f}M params)")
        print(f"Improvement: {improvement:+.2f}%")

        if stretch_achieved:
            print(f"\n🌟 MISSION ACCOMPLISHED: V6 stretch goal achieved!")
        elif breakthrough_achieved:
            print(f"\n🏆 MISSION ACCOMPLISHED: V6 breakthrough achieved!")
        elif v5_exceeded:
            print(f"\n🚀 SUCCESS: V6 exceeds V5 performance!")
        else:
            print(f"\n🎯 PROGRESS: Strong V6 performance, consider optimization!")

    except Exception as e:
        print(f"❌ V6 training failed with error: {e}")
        import traceback
        traceback.print_exc()
