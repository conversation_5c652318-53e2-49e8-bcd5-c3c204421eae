"""
Enhanced U-Net V2 Training Script with all improvements:
- Enhanced architecture with attention and multi-scale features
- Advanced data augmentations and progressive resizing
- Sophisticated loss functions and deep supervision
- Cross-validation and ensemble training
- Mixed precision training and gradient accumulation
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.cuda.amp import GradScaler, autocast
import numpy as np
from tqdm import tqdm
import time
import json
from pathlib import Path
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.enhanced_unet_v2 import get_enhanced_model
from chess_board_detection.dataset.enhanced_segmentation_dataset_v2 import create_enhanced_dataloaders_v2
from chess_board_detection.losses.advanced_losses_v2 import get_loss_function

class AdvancedMetrics:
    """Advanced metrics calculation for segmentation."""
    
    @staticmethod
    def calculate_metrics(predictions, targets, threshold=0.5):
        """Calculate comprehensive metrics."""
        predictions = (torch.sigmoid(predictions) > threshold).float()
        targets = (targets > threshold).float()
        
        # Flatten for calculation
        pred_flat = predictions.view(-1)
        target_flat = targets.view(-1)
        
        # Basic metrics
        intersection = (pred_flat * target_flat).sum()
        union = pred_flat.sum() + target_flat.sum() - intersection
        
        # IoU
        iou = (intersection + 1e-6) / (union + 1e-6)
        
        # Dice
        dice = (2 * intersection + 1e-6) / (pred_flat.sum() + target_flat.sum() + 1e-6)
        
        # Precision, Recall, F1
        tp = intersection
        fp = pred_flat.sum() - intersection
        fn = target_flat.sum() - intersection
        
        precision = (tp + 1e-6) / (tp + fp + 1e-6)
        recall = (tp + 1e-6) / (tp + fn + 1e-6)
        f1 = 2 * (precision * recall) / (precision + recall + 1e-6)
        
        # Specificity
        tn = (1 - pred_flat).sum() - fn
        specificity = (tn + 1e-6) / (tn + fp + 1e-6)
        
        return {
            'iou': iou.item(),
            'dice': dice.item(),
            'precision': precision.item(),
            'recall': recall.item(),
            'f1': f1.item(),
            'specificity': specificity.item()
        }

class CosineAnnealingWarmRestarts:
    """Cosine annealing with warm restarts scheduler."""
    
    def __init__(self, optimizer, T_0, T_mult=1, eta_min=0, last_epoch=-1):
        self.optimizer = optimizer
        self.T_0 = T_0
        self.T_mult = T_mult
        self.eta_min = eta_min
        self.T_cur = last_epoch
        self.T_i = T_0
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
    
    def step(self):
        self.T_cur += 1
        if self.T_cur >= self.T_i:
            self.T_cur = 0
            self.T_i *= self.T_mult
        
        for param_group, base_lr in zip(self.optimizer.param_groups, self.base_lrs):
            param_group['lr'] = self.eta_min + (base_lr - self.eta_min) * (
                1 + np.cos(np.pi * self.T_cur / self.T_i)) / 2

def train_epoch_v2(model, train_loader, criterion, optimizer, scaler, device, 
                   accumulation_steps=1, max_grad_norm=1.0):
    """Enhanced training epoch with mixed precision and gradient accumulation."""
    model.train()
    total_loss = 0
    total_metrics = {}
    num_batches = len(train_loader)
    
    optimizer.zero_grad()
    
    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device, non_blocking=True)
        masks = masks.to(device, non_blocking=True)
        
        # Add channel dimension if needed
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)
        
        with autocast():
            outputs = model(images)
            loss, loss_metrics = criterion(outputs, masks)
            loss = loss / accumulation_steps
        
        # Backward pass with mixed precision
        scaler.scale(loss).backward()
        
        # Gradient accumulation
        if (batch_idx + 1) % accumulation_steps == 0:
            # Gradient clipping
            scaler.unscale_(optimizer)
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_grad_norm)
            
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad()
        
        # Calculate metrics
        with torch.no_grad():
            if isinstance(outputs, tuple):
                main_output = outputs[0]
            else:
                main_output = outputs
            
            batch_metrics = AdvancedMetrics.calculate_metrics(main_output, masks)
        
        # Accumulate metrics
        total_loss += loss.item() * accumulation_steps
        for key, value in loss_metrics.items():
            if key not in total_metrics:
                total_metrics[key] = 0
            total_metrics[key] += value
        
        for key, value in batch_metrics.items():
            if key not in total_metrics:
                total_metrics[key] = 0
            total_metrics[key] += value
        
        # Update progress bar
        pbar.set_postfix({
            'Loss': f'{loss.item() * accumulation_steps:.4f}',
            'Dice': f'{batch_metrics["dice"]:.4f}',
            'IoU': f'{batch_metrics["iou"]:.4f}'
        })
    
    # Average metrics
    avg_loss = total_loss / num_batches
    avg_metrics = {key: value / num_batches for key, value in total_metrics.items()}
    
    return avg_loss, avg_metrics

def validate_epoch_v2(model, val_loader, criterion, device, use_tta=False):
    """Enhanced validation with optional test-time augmentation."""
    model.eval()
    total_loss = 0
    total_metrics = {}
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device, non_blocking=True)
            masks = masks.to(device, non_blocking=True)
            
            if masks.dim() == 3:
                masks = masks.unsqueeze(1)
            
            if use_tta:
                # Test-time augmentation (simplified)
                outputs_list = []
                
                # Original
                outputs = model(images)
                if isinstance(outputs, tuple):
                    outputs = outputs[0]
                outputs_list.append(outputs)
                
                # Horizontal flip
                outputs_flip = model(torch.flip(images, dims=[3]))
                if isinstance(outputs_flip, tuple):
                    outputs_flip = outputs_flip[0]
                outputs_list.append(torch.flip(outputs_flip, dims=[3]))
                
                # Average predictions
                outputs = torch.mean(torch.stack(outputs_list), dim=0)
            else:
                outputs = model(images)
                if isinstance(outputs, tuple):
                    main_output = outputs[0]
                else:
                    main_output = outputs
                outputs = main_output
            
            loss, loss_metrics = criterion(outputs, masks)
            
            # Calculate metrics
            batch_metrics = AdvancedMetrics.calculate_metrics(outputs, masks)
            
            # Accumulate
            total_loss += loss.item()
            for key, value in loss_metrics.items():
                if key not in total_metrics:
                    total_metrics[key] = 0
                total_metrics[key] += value
            
            for key, value in batch_metrics.items():
                if key not in total_metrics:
                    total_metrics[key] = 0
                total_metrics[key] += value
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Dice': f'{batch_metrics["dice"]:.4f}',
                'IoU': f'{batch_metrics["iou"]:.4f}'
            })
    
    avg_loss = total_loss / num_batches
    avg_metrics = {key: value / num_batches for key, value in total_metrics.items()}
    
    return avg_loss, avg_metrics

def train_fold(fold_idx, config):
    """Train a single fold with all V2 enhancements."""
    print(f"\n{'='*50}")
    print(f"Training Fold {fold_idx + 1}/{config['n_folds']}")
    print(f"{'='*50}")
    
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create save directory for this fold
    fold_save_dir = Path(config['save_dir']) / f"fold_{fold_idx}"
    fold_save_dir.mkdir(parents=True, exist_ok=True)
    
    # Model
    model = get_enhanced_model(
        model_type="enhanced_v2",
        n_channels=3,
        n_classes=1,
        deep_supervision=config['deep_supervision']
    )
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,}")
    
    # Loss function
    criterion = get_loss_function(
        loss_type=config['loss_type'],
        deep_supervision=config['deep_supervision'],
        adaptive=config['adaptive_loss']
    )
    
    # Optimizer
    optimizer = optim.AdamW(
        model.parameters(),
        lr=config['learning_rate'],
        weight_decay=config['weight_decay'],
        betas=(0.9, 0.999)
    )
    
    # Scheduler
    scheduler = CosineAnnealingWarmRestarts(
        optimizer,
        T_0=config['scheduler_t0'],
        T_mult=config['scheduler_tmult'],
        eta_min=config['learning_rate'] * 0.01
    )
    
    # Mixed precision scaler
    scaler = GradScaler()
    
    # Training history
    history = {
        'train_loss': [], 'val_loss': [],
        'train_dice': [], 'val_dice': [],
        'train_iou': [], 'val_iou': [],
        'learning_rates': []
    }
    
    best_val_dice = 0
    patience_counter = 0
    
    print(f"Starting training for {config['epochs']} epochs...")
    start_time = time.time()
    
    for epoch in range(config['epochs']):
        print(f"\nEpoch {epoch+1}/{config['epochs']}")
        
        # Update adaptive loss
        if hasattr(criterion, 'update_epoch'):
            criterion.update_epoch(epoch, config['epochs'])
        
        # Create dataloaders for this epoch (progressive resizing)
        train_loader, val_loader, train_dataset, val_dataset = create_enhanced_dataloaders_v2(
            config['dataset_dir'],
            fold_idx=fold_idx,
            n_folds=config['n_folds'],
            batch_size=config['batch_size'],
            image_size=config['image_size'],
            num_workers=config['num_workers'],
            use_mixup_cutmix=config['use_mixup_cutmix'],
            progressive_resize=config['progressive_resize'],
            current_epoch=epoch,
            max_epochs=config['epochs']
        )
        
        # Update dataset epoch for progressive resizing
        if hasattr(train_dataset, 'update_epoch'):
            train_dataset.update_epoch(epoch)
        
        # Train
        train_loss, train_metrics = train_epoch_v2(
            model, train_loader, criterion, optimizer, scaler, device,
            config['accumulation_steps'], config['max_grad_norm']
        )
        
        # Validate
        val_loss, val_metrics = validate_epoch_v2(
            model, val_loader, criterion, device, config['use_tta']
        )
        
        # Update scheduler
        scheduler.step()
        current_lr = optimizer.param_groups[0]['lr']
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_dice'].append(train_metrics.get('dice', 0))
        history['val_dice'].append(val_metrics.get('dice', 0))
        history['train_iou'].append(train_metrics.get('iou', 0))
        history['val_iou'].append(val_metrics.get('iou', 0))
        history['learning_rates'].append(current_lr)
        
        # Print results
        print(f"Train - Loss: {train_loss:.4f}, Dice: {train_metrics.get('dice', 0):.4f}, IoU: {train_metrics.get('iou', 0):.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, Dice: {val_metrics.get('dice', 0):.4f}, IoU: {val_metrics.get('iou', 0):.4f}")
        print(f"LR: {current_lr:.6f}")
        
        # Save best model
        val_dice = val_metrics.get('dice', 0)
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            torch.save(model.state_dict(), fold_save_dir / "best_model.pth")
            print(f"New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Save checkpoint
        if (epoch + 1) % 10 == 0:
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scaler_state_dict': scaler.state_dict(),
                'val_dice': val_dice,
                'history': history
            }, fold_save_dir / f"checkpoint_epoch_{epoch+1}.pth")
        
        # Early stopping
        if patience_counter >= config['patience']:
            print(f"Early stopping triggered after {config['patience']} epochs without improvement")
            break
    
    # Save final results
    torch.save(model.state_dict(), fold_save_dir / "final_model.pth")
    
    with open(fold_save_dir / "training_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    training_time = time.time() - start_time
    print(f"\nFold {fold_idx + 1} completed in {training_time/3600:.2f} hours")
    print(f"Best validation Dice: {best_val_dice:.4f}")
    
    return best_val_dice, history

def main():
    """Main training function with cross-validation."""
    
    # Configuration
    config = {
        # Data
        'dataset_dir': r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326",
        'save_dir': "chess_board_detection/enhanced_unet_v2_results",
        
        # Training
        'n_folds': 5,
        'epochs': 100,
        'batch_size': 4,  # Reduced for 6GB GPU with 512x512 images
        'learning_rate': 1e-4,
        'weight_decay': 1e-4,
        'accumulation_steps': 4,  # Effective batch size = 4 * 4 = 16
        'max_grad_norm': 1.0,
        'patience': 20,
        
        # Model
        'image_size': (512, 512),
        'deep_supervision': True,
        'loss_type': 'combo',
        'adaptive_loss': True,
        
        # Augmentation
        'use_mixup_cutmix': True,
        'progressive_resize': True,
        'use_tta': False,  # Disable for training speed
        
        # Scheduler
        'scheduler_t0': 10,
        'scheduler_tmult': 2,
        
        # System
        'num_workers': 0,
    }
    
    print("Starting Enhanced U-Net V2 Training with Cross-Validation")
    print(f"Configuration: {json.dumps(config, indent=2)}")
    
    # Create main save directory
    save_dir = Path(config['save_dir'])
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Save configuration
    with open(save_dir / "config.json", 'w') as f:
        json.dump(config, f, indent=2)
    
    # Cross-validation training
    fold_results = []
    
    for fold_idx in range(config['n_folds']):
        try:
            best_dice, history = train_fold(fold_idx, config)
            fold_results.append({
                'fold': fold_idx,
                'best_dice': best_dice,
                'history': history
            })
        except Exception as e:
            print(f"Error in fold {fold_idx}: {e}")
            continue
    
    # Summary
    if fold_results:
        dice_scores = [result['best_dice'] for result in fold_results]
        mean_dice = np.mean(dice_scores)
        std_dice = np.std(dice_scores)
        
        print(f"\n{'='*50}")
        print(f"CROSS-VALIDATION RESULTS")
        print(f"{'='*50}")
        print(f"Mean Dice: {mean_dice:.4f} ± {std_dice:.4f}")
        print(f"Individual folds: {[f'{d:.4f}' for d in dice_scores]}")
        print(f"Best fold: {max(dice_scores):.4f}")
        
        # Save summary
        summary = {
            'mean_dice': mean_dice,
            'std_dice': std_dice,
            'fold_scores': dice_scores,
            'config': config
        }
        
        with open(save_dir / "cv_summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Results saved to: {save_dir}")
    
    print("\nEnhanced U-Net V2 training completed!")

if __name__ == "__main__":
    main()
