"""
Enhanced U-Net model architecture for chess board detection.
This version includes specific improvements for corner detection.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F


class DoubleConv(nn.Module):
    """
    Double convolution block used in U-Net.
    (Conv2d -> BatchNorm -> ReLU) * 2
    """
    def __init__(self, in_channels, out_channels, mid_channels=None):
        super().__init__()
        if not mid_channels:
            mid_channels = out_channels
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, out_channels, kernel_size=3, padding=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )

    def forward(self, x):
        return self.double_conv(x)


class Down(nn.Module):
    """
    Downscaling with maxpool then double conv.
    """
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            DoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)


class Up(nn.Module):
    """
    Upscaling then double conv.
    """
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()

        # if bilinear, use the normal convolutions to reduce the number of channels
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
            self.conv = DoubleConv(in_channels, out_channels, in_channels // 2)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
            self.conv = DoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        # input is CHW
        diffY = x2.size()[2] - x1.size()[2]
        diffX = x2.size()[3] - x1.size()[3]

        x1 = F.pad(x1, [diffX // 2, diffX - diffX // 2,
                        diffY // 2, diffY - diffY // 2])
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)


class OutConv(nn.Module):
    """
    Final convolution layer.
    """
    def __init__(self, in_channels, out_channels):
        super(OutConv, self).__init__()
        self.conv = nn.Conv2d(in_channels, out_channels, kernel_size=1)

    def forward(self, x):
        return self.conv(x)


class AttentionBlock(nn.Module):
    """
    Attention block to focus on specific features.
    """
    def __init__(self, F_g, F_l, F_int):
        super(AttentionBlock, self).__init__()
        self.W_g = nn.Sequential(
            nn.Conv2d(F_g, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )
        
        self.W_x = nn.Sequential(
            nn.Conv2d(F_l, F_int, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(F_int)
        )
        
        self.psi = nn.Sequential(
            nn.Conv2d(F_int, 1, kernel_size=1, stride=1, padding=0, bias=True),
            nn.BatchNorm2d(1),
            nn.Sigmoid()
        )
        
        self.relu = nn.ReLU(inplace=True)
        
    def forward(self, g, x):
        g1 = self.W_g(g)
        x1 = self.W_x(x)
        psi = self.relu(g1 + x1)
        psi = self.psi(psi)
        
        return x * psi


class CornerDetectionHead(nn.Module):
    """
    Enhanced corner detection head with attention mechanism.
    """
    def __init__(self, in_channels, mid_channels=64):
        super(CornerDetectionHead, self).__init__()
        
        # Attention mechanism to focus on corner regions
        self.attention = AttentionBlock(in_channels, in_channels, mid_channels // 2)
        
        # Corner detection convolutions
        self.corner_conv = nn.Sequential(
            nn.Conv2d(in_channels, mid_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(mid_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(mid_channels, mid_channels // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(mid_channels // 2),
            nn.ReLU(inplace=True)
        )
        
        # Separate convolution paths for each corner
        self.corner_specific = nn.ModuleList([
            nn.Sequential(
                nn.Conv2d(mid_channels // 2, mid_channels // 4, kernel_size=3, padding=1),
                nn.BatchNorm2d(mid_channels // 4),
                nn.ReLU(inplace=True),
                nn.Conv2d(mid_channels // 4, 1, kernel_size=1)
            ) for _ in range(4)
        ])
        
    def forward(self, x, encoder_features=None):
        # Apply attention if encoder features are provided
        if encoder_features is not None:
            x = self.attention(x, encoder_features)
            
        # Common processing
        x = self.corner_conv(x)
        
        # Separate processing for each corner
        corner_outputs = []
        for corner_conv in self.corner_specific:
            corner_outputs.append(corner_conv(x))
            
        # Concatenate outputs
        return torch.cat(corner_outputs, dim=1)


class EnhancedChessBoardUNet(nn.Module):
    """
    Enhanced U-Net for chess board detection with improved corner detection.
    """
    def __init__(self, n_channels=3, bilinear=True):
        super(EnhancedChessBoardUNet, self).__init__()
        self.n_channels = n_channels
        self.bilinear = bilinear
        
        # Encoder
        self.inc = DoubleConv(n_channels, 64)
        self.down1 = Down(64, 128)
        self.down2 = Down(128, 256)
        self.down3 = Down(256, 512)
        factor = 2 if bilinear else 1
        self.down4 = Down(512, 1024 // factor)
        
        # Decoder
        self.up1 = Up(1024, 512 // factor, bilinear)
        self.up2 = Up(512, 256 // factor, bilinear)
        self.up3 = Up(256, 128 // factor, bilinear)
        self.up4 = Up(128, 64, bilinear)
        
        # Segmentation head
        self.outc = OutConv(64, 1)
        
        # Enhanced corner detection head
        self.corner_head = CornerDetectionHead(64)
        
        # Edge suppression module
        self.edge_suppression = nn.Sequential(
            nn.Conv2d(64, 32, kernel_size=3, padding=1),
            nn.BatchNorm2d(32),
            nn.ReLU(inplace=True),
            nn.Conv2d(32, 4, kernel_size=1)  # One channel per edge
        )
        
    def forward(self, x):
        # Encoder path
        x1 = self.inc(x)
        x2 = self.down1(x1)
        x3 = self.down2(x2)
        x4 = self.down3(x3)
        x5 = self.down4(x4)
        
        # Decoder path
        x = self.up1(x5, x4)
        x = self.up2(x, x3)
        x = self.up3(x, x2)
        x = self.up4(x, x1)
        
        # Segmentation output
        segmentation = self.outc(x)
        
        # Corner heatmap output with attention from encoder features
        corner_heatmaps = self.corner_head(x, x1)
        
        # Edge suppression mask
        edge_mask = self.edge_suppression(x)
        
        return {
            'segmentation': segmentation,
            'corner_heatmaps': corner_heatmaps,
            'edge_mask': edge_mask
        }