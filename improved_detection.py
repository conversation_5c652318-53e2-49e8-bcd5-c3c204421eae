import os
import cv2
import numpy as np
from ultralytics import YOLO
import argparse

# Class names
CLASS_NAMES = [
    'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
    'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
]

# Define colors for each class (BGR format for OpenCV)
COLORS = {
    'white_pawn': (255, 255, 0),     # <PERSON>an
    'white_knight': (255, 0, 255),   # Magenta
    'white_bishop': (0, 255, 255),   # Yellow
    'white_rook': (0, 0, 255),       # Red
    'white_queen': (255, 0, 0),      # Blue
    'white_king': (0, 255, 0),       # <PERSON>
    'black_pawn': (128, 255, 0),     # Light Cyan
    'black_knight': (255, 128, 255), # Light Magenta
    'black_bishop': (128, 255, 255), # Light Yellow
    'black_rook': (128, 128, 255),   # Light Red
    'black_queen': (255, 128, 128),  # Light Blue
    'black_king': (128, 255, 128),   # Light Green
}

def calculate_iou(box1, box2):
    """Calculate IoU between two boxes"""
    # Box format: [x1, y1, x2, y2]
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i < x1_i or y2_i < y1_i:
        return 0.0
    
    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)
    
    # Calculate union area
    box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
    box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = box1_area + box2_area - intersection_area
    
    return intersection_area / union_area

def non_max_suppression(boxes, scores, classes, iou_threshold=0.5):
    """Apply non-maximum suppression to eliminate duplicate detections"""
    # Convert to numpy arrays if they're not already
    boxes = np.array(boxes)
    scores = np.array(scores)
    classes = np.array(classes)
    
    # Sort by confidence score
    indices = np.argsort(scores)[::-1]
    boxes = boxes[indices]
    scores = scores[indices]
    classes = classes[indices]
    
    keep = []
    while len(indices) > 0:
        # Keep the detection with highest confidence
        keep.append(indices[0])
        
        # Calculate IoU with remaining boxes
        ious = []
        for i in range(1, len(indices)):
            iou = calculate_iou(boxes[0], boxes[i])
            ious.append(iou)
        
        # Keep only boxes with IoU less than threshold
        mask = np.array(ious) < iou_threshold
        indices = indices[1:][mask]
        boxes = boxes[1:][mask]
        scores = scores[1:][mask]
        classes = classes[1:][mask]
    
    return keep

def run_inference(model_path, image_path, output_dir, conf_threshold=0.5, iou_threshold=0.7):
    """Run inference on an image and save the results"""
    # Load model
    model = YOLO(model_path)
    
    # Run inference
    results = model.predict(image_path, conf=conf_threshold)[0]
    
    # Get the original image
    img = cv2.imread(image_path)
    
    # Get detection data
    boxes = results.boxes.xyxy.cpu().numpy()
    cls_ids = results.boxes.cls.cpu().numpy().astype(int)
    confs = results.boxes.conf.cpu().numpy()
    
    # Apply non-maximum suppression
    keep_indices = non_max_suppression(boxes, confs, cls_ids, iou_threshold)
    
    # Keep only the selected detections
    filtered_boxes = boxes[keep_indices]
    filtered_cls_ids = cls_ids[keep_indices]
    filtered_confs = confs[keep_indices]
    
    # Create a copy of the image for drawing
    img_with_boxes = img.copy()
    
    # Draw bounding boxes and labels
    for i, (box, cls_id, conf) in enumerate(zip(filtered_boxes, filtered_cls_ids, filtered_confs)):
        x1, y1, x2, y2 = box.astype(int)
        color = COLORS[CLASS_NAMES[cls_id]]
        
        # Draw box
        cv2.rectangle(img_with_boxes, (x1, y1), (x2, y2), color, 2)
        
        # Add label with detection number
        label = f"{i+1}: {CLASS_NAMES[cls_id]} ({conf:.2f})"
        cv2.putText(img_with_boxes, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
    
    # Save the image with detections
    os.makedirs(output_dir, exist_ok=True)
    base_name = os.path.basename(image_path)
    output_path = os.path.join(output_dir, f"detection_{base_name}")
    cv2.imwrite(output_path, img_with_boxes)
    
    # Create a text file with detection information
    txt_path = os.path.join(output_dir, f"detection_{os.path.splitext(base_name)[0]}.txt")
    with open(txt_path, 'w') as f:
        f.write(f"Detections for {base_name}:\n\n")
        for i, (box, cls_id, conf) in enumerate(zip(filtered_boxes, filtered_cls_ids, filtered_confs)):
            x1, y1, x2, y2 = box.astype(int)
            f.write(f"{i+1}. {CLASS_NAMES[cls_id]} (confidence: {conf:.2f}) at position ({x1}, {y1}, {x2}, {y2})\n")
    
    print(f"Processed {base_name}")
    print(f"Detection image saved to: {output_path}")
    print(f"Detection info saved to: {txt_path}")
    print(f"Found {len(filtered_boxes)} pieces after filtering (original: {len(boxes)})")
    
    return output_path, txt_path

def main():
    parser = argparse.ArgumentParser(description="Generate detection images with improved filtering")
    parser.add_argument("--model", required=True, help="Path to the YOLO model")
    parser.add_argument("--image-dir", required=True, help="Directory containing images to process")
    parser.add_argument("--output-dir", default="improved_detections", help="Directory to save output images")
    parser.add_argument("--conf", type=float, default=0.5, help="Confidence threshold")
    parser.add_argument("--iou", type=float, default=0.7, help="IoU threshold for NMS")
    
    args = parser.parse_args()
    
    # Get all images in the directory
    image_files = [os.path.join(args.image_dir, f) for f in os.listdir(args.image_dir)
                  if f.lower().endswith(('.jpg', '.jpeg', '.png'))]
    
    for image_file in image_files:
        run_inference(args.model, image_file, args.output_dir, args.conf, args.iou)

if __name__ == "__main__":
    main()
