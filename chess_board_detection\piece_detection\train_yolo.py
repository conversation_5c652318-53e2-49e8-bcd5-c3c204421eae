"""
Train a YOLO model for chess piece detection.
This script uses the Ultralytics YOLOv8 implementation.
"""

import os
import sys
import argparse
from ultralytics import YOLO
import yaml
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def train_yolo(data_yaml, model_size='n', epochs=100, batch_size=16, img_size=640, 
               patience=20, output_dir=None, pretrained=True):
    """
    Train a YOLO model for chess piece detection.
    
    Args:
        data_yaml: Path to the data.yaml file
        model_size: YOLO model size ('n', 's', 'm', 'l', 'x')
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size
        patience: Early stopping patience
        output_dir: Directory to save the output model
        pretrained: Whether to use pretrained weights
    
    Returns:
        Path to the trained model
    """
    # Create output directory
    if output_dir is None:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_dir = f"chess_board_detection/piece_detection/models/yolov8{model_size}_{timestamp}"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Load data.yaml to get class names
    with open(data_yaml, 'r') as f:
        data_config = yaml.safe_load(f)
    
    class_names = data_config.get('names', [])
    print(f"Training YOLO model for {len(class_names)} classes: {class_names}")
    
    # Create model
    model = YOLO(f"yolov8{model_size}.pt" if pretrained else f"yolov8{model_size}.yaml")
    
    # Train model
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        batch=batch_size,
        imgsz=img_size,
        patience=patience,
        project=output_dir,
        name="train",
        exist_ok=True,
        pretrained=pretrained,
        verbose=True
    )
    
    # Plot training results
    plot_results(results, output_dir)
    
    # Return path to best model
    best_model_path = os.path.join(output_dir, "train", "weights", "best.pt")
    return best_model_path

def plot_results(results, output_dir):
    """
    Plot training results.
    
    Args:
        results: Training results
        output_dir: Directory to save the plots
    """
    # Create plots directory
    plots_dir = os.path.join(output_dir, "plots")
    os.makedirs(plots_dir, exist_ok=True)
    
    # Plot metrics
    metrics = ['box_loss', 'cls_loss', 'dfl_loss', 'precision', 'recall', 'mAP50', 'mAP50-95']
    
    for metric in metrics:
        if metric in results.results_dict:
            plt.figure(figsize=(10, 6))
            
            # Get training and validation values
            train_values = results.results_dict.get(f'train/{metric}', [])
            val_values = results.results_dict.get(f'val/{metric}', [])
            
            # Plot training values
            if train_values:
                plt.plot(train_values, label=f'Train {metric}')
            
            # Plot validation values
            if val_values:
                plt.plot(val_values, label=f'Val {metric}')
            
            plt.xlabel('Epoch')
            plt.ylabel(metric)
            plt.title(f'{metric} vs. Epoch')
            plt.legend()
            plt.grid(True)
            
            # Save plot
            plt.savefig(os.path.join(plots_dir, f'{metric}.png'), dpi=150, bbox_inches='tight')
            plt.close()
    
    # Plot combined metrics
    plt.figure(figsize=(12, 8))
    
    # Plot mAP metrics
    if 'val/mAP50' in results.results_dict:
        plt.plot(results.results_dict['val/mAP50'], label='mAP50')
    
    if 'val/mAP50-95' in results.results_dict:
        plt.plot(results.results_dict['val/mAP50-95'], label='mAP50-95')
    
    plt.xlabel('Epoch')
    plt.ylabel('mAP')
    plt.title('mAP vs. Epoch')
    plt.legend()
    plt.grid(True)
    
    # Save plot
    plt.savefig(os.path.join(plots_dir, 'mAP.png'), dpi=150, bbox_inches='tight')
    plt.close()
    
    # Plot loss metrics
    plt.figure(figsize=(12, 8))
    
    # Plot loss metrics
    for loss in ['box_loss', 'cls_loss', 'dfl_loss']:
        if f'val/{loss}' in results.results_dict:
            plt.plot(results.results_dict[f'val/{loss}'], label=loss)
    
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.title('Validation Loss vs. Epoch')
    plt.legend()
    plt.grid(True)
    
    # Save plot
    plt.savefig(os.path.join(plots_dir, 'val_loss.png'), dpi=150, bbox_inches='tight')
    plt.close()

def validate_yolo(model_path, data_yaml):
    """
    Validate a trained YOLO model.
    
    Args:
        model_path: Path to the trained model
        data_yaml: Path to the data.yaml file
    """
    # Load model
    model = YOLO(model_path)
    
    # Validate model
    metrics = model.val(data=data_yaml)
    
    print("\nValidation Results:")
    print(f"mAP50: {metrics.box.map50:.4f}")
    print(f"mAP50-95: {metrics.box.map:.4f}")
    print(f"Precision: {metrics.box.precision:.4f}")
    print(f"Recall: {metrics.box.recall:.4f}")
    
    return metrics

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train YOLO for chess piece detection')
    parser.add_argument('--data', type=str, required=True,
                        help='Path to data.yaml file')
    parser.add_argument('--model_size', type=str, default='n', choices=['n', 's', 'm', 'l', 'x'],
                        help='YOLO model size (n, s, m, l, x)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=16,
                        help='Batch size')
    parser.add_argument('--img_size', type=int, default=640,
                        help='Image size')
    parser.add_argument('--patience', type=int, default=20,
                        help='Early stopping patience')
    parser.add_argument('--output_dir', type=str, default=None,
                        help='Directory to save the output model')
    parser.add_argument('--no_pretrained', action='store_true',
                        help='Do not use pretrained weights')
    args = parser.parse_args()
    
    # Train model
    model_path = train_yolo(
        args.data,
        model_size=args.model_size,
        epochs=args.epochs,
        batch_size=args.batch_size,
        img_size=args.img_size,
        patience=args.patience,
        output_dir=args.output_dir,
        pretrained=not args.no_pretrained
    )
    
    # Validate model
    validate_yolo(model_path, args.data)
    
    print(f"\nTraining completed. Model saved to {model_path}")

if __name__ == "__main__":
    main()
