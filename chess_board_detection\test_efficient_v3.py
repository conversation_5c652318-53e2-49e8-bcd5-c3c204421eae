"""
Simple test script for Efficient U-Net V3.
"""

import torch
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.efficient_unet_v3 import get_efficient_model, ModelSizeAnalyzer

def test_efficient_models():
    """Test different efficient model configurations."""
    
    print("🚀 Testing Efficient U-Net V3 Models...")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test different configurations
    configs = [
        ("V3-16", 16),
        ("V3-24", 24), 
        ("V3-32", 32),
        ("V3-48", 48)
    ]
    
    results = []
    
    for name, base_channels in configs:
        print(f"\n📊 Testing {name} (base_channels={base_channels})")
        
        try:
            # Create model
            model = get_efficient_model(base_channels=base_channels)
            
            # Count parameters
            total_params, trainable_params = ModelSizeAnalyzer.count_parameters(model)
            
            # Test forward pass on CPU first
            model_cpu = model.cpu()
            x_cpu = torch.randn(1, 3, 256, 256)  # Smaller size for testing
            
            with torch.no_grad():
                output_cpu = model_cpu(x_cpu)
            
            # Test on GPU if available
            if torch.cuda.is_available():
                model_gpu = model.to(device)
                x_gpu = torch.randn(1, 3, 256, 256).to(device)
                
                with torch.no_grad():
                    output_gpu = model_gpu(x_gpu)
                
                print(f"  ✅ GPU test passed: {output_gpu.shape}")
            
            # Calculate efficiency metrics
            v1_params = 17262977
            v2_params = 41036150
            efficiency_vs_v1 = total_params / v1_params
            efficiency_vs_v2 = total_params / v2_params
            
            # Memory estimation
            memory_mb = ModelSizeAnalyzer.estimate_memory(model)
            
            result = {
                'name': name,
                'params': total_params,
                'efficiency_v1': efficiency_vs_v1,
                'efficiency_v2': efficiency_vs_v2,
                'memory_mb': memory_mb,
                'output_shape': output_cpu.shape
            }
            results.append(result)
            
            print(f"  Parameters: {total_params:,}")
            print(f"  Efficiency vs V1: {efficiency_vs_v1:.2f}x")
            print(f"  Efficiency vs V2: {efficiency_vs_v2:.2f}x")
            print(f"  Memory: {memory_mb:.1f} MB")
            print(f"  Output shape: {output_cpu.shape}")
            print(f"  ✅ Test passed!")
            
        except Exception as e:
            print(f"  ❌ Test failed: {e}")
            continue
    
    # Summary table
    print("\n" + "=" * 80)
    print("📈 EFFICIENT U-NET V3 COMPARISON SUMMARY")
    print("=" * 80)
    print(f"{'Model':<10} {'Parameters':<12} {'vs V1':<8} {'vs V2':<8} {'Memory':<10} {'Status'}")
    print("-" * 80)
    
    # Reference models
    print(f"{'V1':<10} {17262977:>11,} {'1.00x':<8} {'0.42x':<8} {'65.8 MB':<10} {'Reference'}")
    print(f"{'V2':<10} {41036150:>11,} {'2.38x':<8} {'1.00x':<8} {'156.5 MB':<10} {'Reference'}")
    
    # V3 models
    for result in results:
        print(f"{result['name']:<10} {result['params']:>11,} {result['efficiency_v1']:<7.2f}x {result['efficiency_v2']:<7.2f}x {result['memory_mb']:<7.1f} MB {'✅ Good'}")
    
    # Recommendations
    print("\n🎯 RECOMMENDATIONS:")
    print("-" * 50)
    
    for result in results:
        if result['efficiency_v1'] < 0.5:  # Less than 50% of V1 parameters
            status = "🟢 Highly Efficient"
        elif result['efficiency_v1'] < 1.0:  # Less than V1 parameters
            status = "🟡 Efficient"
        else:
            status = "🔴 Less Efficient"
        
        print(f"{result['name']}: {status}")
        
        if result['name'] == 'V3-32':
            print(f"  👑 RECOMMENDED: Good balance of efficiency and capacity")
        elif result['name'] == 'V3-24':
            print(f"  📱 MOBILE: Best for mobile deployment")
        elif result['name'] == 'V3-48':
            print(f"  🚀 PERFORMANCE: Higher capacity for complex cases")
    
    return results

if __name__ == "__main__":
    results = test_efficient_models()
    
    print(f"\n🎉 Testing completed! Found {len(results)} working configurations.")
    
    # Find best configuration
    if results:
        best_efficiency = min(results, key=lambda x: x['efficiency_v1'])
        best_balance = None
        
        for result in results:
            if 0.3 <= result['efficiency_v1'] <= 0.7:  # Good balance range
                if best_balance is None or result['params'] > best_balance['params']:
                    best_balance = result
        
        print(f"\n🏆 BEST CONFIGURATIONS:")
        print(f"Most Efficient: {best_efficiency['name']} ({best_efficiency['params']:,} params)")
        if best_balance:
            print(f"Best Balance: {best_balance['name']} ({best_balance['params']:,} params)")
        
        print(f"\n💡 NEXT STEP: Train the recommended model to compare performance!")
