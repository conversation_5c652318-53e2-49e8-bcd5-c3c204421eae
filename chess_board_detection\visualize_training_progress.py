"""
<PERSON><PERSON><PERSON> to visualize the training progress of heatmaps.
This helps understand how the model's corner detection improves over time.
"""

import os
import argparse
import json
import numpy as np
import torch
import matplotlib.pyplot as plt
from tqdm import tqdm

from models.unet import ChessBoardUNet
from utils.real_dataset import ChessBoardDataset
from config import DATA_DIR, MODELS_DIR, DEVICE


def visualize_training_progress(model_dir, checkpoint_pattern, dataset, sample_idx, output_dir):
    """
    Visualize the training progress of heatmaps for a specific sample.
    
    Args:
        model_dir (str): Directory containing model checkpoints.
        checkpoint_pattern (str): Pattern for checkpoint filenames.
        dataset: Dataset containing the sample.
        sample_idx (int): Index of the sample to visualize.
        output_dir (str): Directory to save visualizations.
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Get sample
    sample = dataset[sample_idx]
    image = sample['image'].unsqueeze(0)
    gt_corners = sample['corners'].numpy().reshape(-1, 2)
    
    # Get list of checkpoints
    checkpoints = []
    for filename in os.listdir(model_dir):
        if checkpoint_pattern in filename and filename.endswith('.pth'):
            try:
                epoch = int(filename.split('_')[-1].split('.')[0])
                checkpoints.append((epoch, os.path.join(model_dir, filename)))
            except ValueError:
                continue
    
    # Sort checkpoints by epoch
    checkpoints.sort(key=lambda x: x[0])
    
    # Process each checkpoint
    all_heatmaps = []
    epochs = []
    
    for epoch, checkpoint_path in tqdm(checkpoints, desc="Processing checkpoints"):
        # Load model
        model = ChessBoardUNet(n_channels=3, bilinear=True)
        checkpoint = torch.load(checkpoint_path, map_location=DEVICE)
        
        # Handle different checkpoint formats
        if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(DEVICE)
        model.eval()
        
        # Run inference
        with torch.no_grad():
            image_gpu = image.to(DEVICE)
            outputs = model(image_gpu)
        
        # Get corner heatmaps
        corner_heatmaps = outputs['corner_heatmaps'][0].cpu().numpy()
        all_heatmaps.append(corner_heatmaps)
        epochs.append(epoch)
    
    # Create visualization
    num_checkpoints = len(checkpoints)
    if num_checkpoints == 0:
        print("No checkpoints found!")
        return
    
    # Determine grid size
    grid_size = int(np.ceil(np.sqrt(num_checkpoints)))
    
    # Create figure for each corner
    for corner_idx in range(4):
        plt.figure(figsize=(15, 15))
        
        for i, (epoch, heatmaps) in enumerate(zip(epochs, all_heatmaps)):
            if i >= grid_size * grid_size:
                break
                
            plt.subplot(grid_size, grid_size, i + 1)
            plt.imshow(heatmaps[corner_idx], cmap='jet')
            plt.title(f'Epoch {epoch}')
            plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, f'corner_{corner_idx+1}_progress.png'))
        plt.close()
    
    # Create a visualization of all corners at selected epochs
    if num_checkpoints > 0:
        # Select epochs to visualize (first, 25%, 50%, 75%, last)
        selected_indices = [0]
        if num_checkpoints > 4:
            selected_indices.extend([
                num_checkpoints // 4,
                num_checkpoints // 2,
                3 * num_checkpoints // 4
            ])
        selected_indices.append(num_checkpoints - 1)
        selected_indices = sorted(set(selected_indices))
        
        plt.figure(figsize=(15, 15))
        
        for i, idx in enumerate(selected_indices):
            epoch = epochs[idx]
            heatmaps = all_heatmaps[idx]
            
            # Create a combined heatmap with different colors for each corner
            combined_heatmap = np.zeros((*heatmaps[0].shape, 3))
            colors = [(1, 0, 0), (0, 1, 0), (0, 0, 1), (1, 1, 0)]  # R, G, B, Y
            
            for corner_idx in range(4):
                heatmap = heatmaps[corner_idx]
                heatmap = heatmap / np.max(heatmap) if np.max(heatmap) > 0 else heatmap
                for c in range(3):
                    combined_heatmap[:, :, c] += heatmap * colors[corner_idx][c]
            
            # Normalize
            combined_heatmap = np.clip(combined_heatmap, 0, 1)
            
            plt.subplot(len(selected_indices), 2, 2*i + 1)
            plt.imshow(sample['image'].permute(1, 2, 0).numpy())
            plt.imshow(combined_heatmap, alpha=0.6)
            plt.title(f'Epoch {epoch} - Combined Heatmaps')
            plt.axis('off')
            
            # Show individual heatmaps side by side
            plt.subplot(len(selected_indices), 2, 2*i + 2)
            
            # Create a 2x2 grid of heatmaps
            combined_img = np.zeros((*heatmaps[0].shape, 3))
            
            # Top-left: corner 1 (red)
            heatmap = heatmaps[0] / np.max(heatmaps[0]) if np.max(heatmaps[0]) > 0 else heatmaps[0]
            combined_img[:heatmaps[0].shape[0]//2, :heatmaps[0].shape[1]//2, 0] = heatmap[:heatmaps[0].shape[0]//2, :heatmaps[0].shape[1]//2]
            
            # Top-right: corner 2 (green)
            heatmap = heatmaps[1] / np.max(heatmaps[1]) if np.max(heatmaps[1]) > 0 else heatmaps[1]
            combined_img[:heatmaps[1].shape[0]//2, heatmaps[1].shape[1]//2:, 1] = heatmap[:heatmaps[1].shape[0]//2, heatmaps[1].shape[1]//2:]
            
            # Bottom-right: corner 3 (blue)
            heatmap = heatmaps[2] / np.max(heatmaps[2]) if np.max(heatmaps[2]) > 0 else heatmaps[2]
            combined_img[heatmaps[2].shape[0]//2:, heatmaps[2].shape[1]//2:, 2] = heatmap[heatmaps[2].shape[0]//2:, heatmaps[2].shape[1]//2:]
            
            # Bottom-left: corner 4 (yellow = red + green)
            heatmap = heatmaps[3] / np.max(heatmaps[3]) if np.max(heatmaps[3]) > 0 else heatmaps[3]
            combined_img[heatmaps[3].shape[0]//2:, :heatmaps[3].shape[1]//2, 0] = heatmap[heatmaps[3].shape[0]//2:, :heatmaps[3].shape[1]//2]
            combined_img[heatmaps[3].shape[0]//2:, :heatmaps[3].shape[1]//2, 1] = heatmap[heatmaps[3].shape[0]//2:, :heatmaps[3].shape[1]//2]
            
            plt.imshow(combined_img)
            plt.title(f'Epoch {epoch} - Individual Heatmaps')
            plt.axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'combined_progress.png'))
        plt.close()
    
    print(f"Visualizations saved to {output_dir}")


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Visualize Training Progress')
    parser.add_argument('--model_dir', type=str, default=MODELS_DIR,
                        help='Directory containing model checkpoints')
    parser.add_argument('--checkpoint_pattern', type=str, default='checkpoint_epoch',
                        help='Pattern for checkpoint filenames')
    parser.add_argument('--data_dir', type=str, default=os.path.join(DATA_DIR, 'real'),
                        help='Path to data directory')
    parser.add_argument('--annotation_file', type=str, default=os.path.join(DATA_DIR, 'real_annotations.json'),
                        help='Path to annotation file')
    parser.add_argument('--sample_idx', type=int, default=0,
                        help='Index of the sample to visualize')
    parser.add_argument('--output_dir', type=str, default='training_progress',
                        help='Directory to save visualizations')
    args = parser.parse_args()
    
    # Create dataset
    dataset = ChessBoardDataset(
        data_dir=args.data_dir,
        annotation_file=args.annotation_file,
        split='test'
    )
    
    # Visualize training progress
    visualize_training_progress(
        args.model_dir,
        args.checkpoint_pattern,
        dataset,
        args.sample_idx,
        args.output_dir
    )


if __name__ == "__main__":
    main()
