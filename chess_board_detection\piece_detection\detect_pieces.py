"""
Detect chess pieces and generate a chess position.
This script:
1. Detects the chess board using the distilled segmentation model
2. Extracts and normalizes the chess board region
3. Detects chess pieces within the normalized board using YOLO
4. Maps detected pieces to chess board coordinates (A1-H8)
5. Generates chess position notation (FEN)
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
from ultralytics import YOLO
import time

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import models and utilities
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel
from chess_board_detection.piece_detection.prepare_dataset import (
    preprocess_image, normalize_for_model, find_corners_from_segmentation,
    sort_corners, extract_and_normalize_board
)

def load_segmentation_model(model_path):
    """Load the distilled segmentation model."""
    model = TinySegmentationModel(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def detect_board(segmentation_model, image_path, output_size=(640, 640)):
    """
    Detect the chess board and extract a normalized view.

    Args:
        segmentation_model: The segmentation model
        image_path: Path to the input image
        output_size: Size of the output normalized board image

    Returns:
        normalized_board: Normalized chess board image
        corners: Detected corner coordinates
        preprocess_info: Preprocessing information
    """
    # Preprocess image
    preprocessed_image, preprocess_info = preprocess_image(image_path)

    # Normalize for model
    input_tensor = normalize_for_model(preprocessed_image)

    # Run inference
    with torch.no_grad():
        outputs = segmentation_model(input_tensor)

    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]

    # Find corners from segmentation
    keypoints = find_corners_from_segmentation(segmentation)

    if keypoints is None:
        print(f"No chess board detected in {image_path}")
        return None, None, None

    # Extract and normalize board
    normalized_board = extract_and_normalize_board(preprocessed_image, keypoints, output_size)

    return normalized_board, keypoints, preprocess_info

def detect_pieces(yolo_model, normalized_board, confidence=0.25):
    """
    Detect chess pieces in the normalized board image.

    Args:
        yolo_model: The YOLO model
        normalized_board: Normalized chess board image
        confidence: Detection confidence threshold

    Returns:
        detections: List of detected pieces with coordinates and classes
    """
    # Convert to BGR for YOLO
    normalized_board_bgr = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)

    # Run inference
    results = yolo_model(normalized_board_bgr, conf=confidence)

    # Extract detections
    detections = []

    for result in results:
        boxes = result.boxes

        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes.xyxy[i].tolist()
            confidence = boxes.conf[i].item()
            class_id = int(boxes.cls[i].item())
            class_name = result.names[class_id]

            # Calculate center point
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            detections.append({
                'class_id': class_id,
                'class_name': class_name,
                'confidence': confidence,
                'bbox': (x1, y1, x2, y2),
                'center': (center_x, center_y)
            })

    return detections

def map_to_chess_coordinates(detections, board_size=(640, 640)):
    """
    Map detected pieces to chess board coordinates (A1-H8).

    Args:
        detections: List of detected pieces with coordinates
        board_size: Size of the normalized board image

    Returns:
        chess_pieces: Dictionary mapping chess coordinates to piece names
    """
    chess_pieces = {}

    # Calculate square size
    square_width = board_size[0] / 8
    square_height = board_size[1] / 8

    for detection in detections:
        center_x, center_y = detection['center']

        # Calculate chess coordinates
        file_idx = int(center_x / square_width)
        rank_idx = int(center_y / square_height)

        # Convert to chess notation (A1-H8)
        file_letter = chr(ord('a') + file_idx)
        rank_number = 8 - rank_idx

        # Create chess coordinate
        chess_coord = f"{file_letter}{rank_number}"

        # Map piece name
        piece_name = detection['class_name']

        # Store in dictionary (keep highest confidence piece if multiple detections in same square)
        if chess_coord not in chess_pieces or detection['confidence'] > chess_pieces[chess_coord]['confidence']:
            chess_pieces[chess_coord] = {
                'piece': piece_name,
                'confidence': detection['confidence']
            }

    return chess_pieces

def generate_fen(chess_pieces):
    """
    Generate Forsyth-Edwards Notation (FEN) from detected pieces.

    Args:
        chess_pieces: Dictionary mapping chess coordinates to piece names

    Returns:
        fen: FEN string representing the chess position
    """
    # Define piece symbols
    piece_symbols = {
        'w_pawn': 'P',
        'w_knight': 'N',
        'w_bishop': 'B',
        'w_rook': 'R',
        'w_queen': 'Q',
        'w_king': 'K',
        'b_pawn': 'p',
        'b_knight': 'n',
        'b_bishop': 'b',
        'b_rook': 'r',
        'b_queen': 'q',
        'b_king': 'k'
    }

    # Generate board representation
    board = []

    for rank in range(8, 0, -1):
        rank_str = ""
        empty_count = 0

        for file_idx in range(8):
            file_letter = chr(ord('a') + file_idx)
            chess_coord = f"{file_letter}{rank}"

            if chess_coord in chess_pieces:
                # Add empty squares count if any
                if empty_count > 0:
                    rank_str += str(empty_count)
                    empty_count = 0

                # Add piece symbol
                piece_name = chess_pieces[chess_coord]['piece']
                rank_str += piece_symbols.get(piece_name, '?')
            else:
                # Increment empty squares count
                empty_count += 1

        # Add remaining empty squares count if any
        if empty_count > 0:
            rank_str += str(empty_count)

        board.append(rank_str)

    # Join ranks with slashes
    fen = "/".join(board)

    # Add other FEN components (active color, castling, en passant, etc.)
    # For simplicity, we'll use default values
    fen += " w - - 0 1"

    return fen

def visualize_detection(image_path, normalized_board, detections, chess_pieces, fen, output_path):
    """
    Visualize the chess board detection and piece detection results.

    Args:
        image_path: Path to the input image
        normalized_board: Normalized chess board image
        detections: List of detected pieces
        chess_pieces: Dictionary mapping chess coordinates to piece names
        fen: FEN string representing the chess position
        output_path: Path to save the visualization
    """
    # Create figure with 2x2 subplots
    fig, axs = plt.subplots(2, 2, figsize=(15, 12))

    # Plot original image
    original_image = cv2.imread(image_path)
    original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
    axs[0, 0].imshow(original_image)
    axs[0, 0].set_title('Original Image')
    axs[0, 0].axis('off')

    # Plot normalized board
    axs[0, 1].imshow(normalized_board)
    axs[0, 1].set_title('Normalized Chess Board')
    axs[0, 1].axis('off')

    # Plot detected pieces
    axs[1, 0].imshow(normalized_board)

    # Draw bounding boxes
    for detection in detections:
        x1, y1, x2, y2 = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']

        # Determine color based on piece color
        color = 'r' if class_name.startswith('w_') else 'b'

        # Draw bounding box
        rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, linewidth=2, edgecolor=color, facecolor='none')
        axs[1, 0].add_patch(rect)

        # Add label
        axs[1, 0].text(x1, y1-5, f"{class_name}: {confidence:.2f}", color=color, fontsize=8)

    axs[1, 0].set_title(f'Detected Pieces ({len(detections)})')
    axs[1, 0].axis('off')

    # Plot chess position
    axs[1, 1].imshow(normalized_board)

    # Draw grid
    board_size = normalized_board.shape[:2]
    square_size = (board_size[1] / 8, board_size[0] / 8)

    for i in range(9):
        # Vertical lines
        axs[1, 1].axvline(x=i*square_size[0], color='w', linewidth=1)
        # Horizontal lines
        axs[1, 1].axhline(y=i*square_size[1], color='w', linewidth=1)

    # Add coordinates
    for i in range(8):
        # File labels (a-h)
        axs[1, 1].text((i+0.5)*square_size[0], board_size[0]-10, chr(ord('a')+i),
                      ha='center', va='bottom', color='w', fontsize=10)
        # Rank labels (1-8)
        axs[1, 1].text(10, (i+0.5)*square_size[1], str(8-i),
                      ha='left', va='center', color='w', fontsize=10)

    # Add piece labels
    for coord, piece_info in chess_pieces.items():
        file_letter = coord[0]
        rank_number = int(coord[1])

        file_idx = ord(file_letter) - ord('a')
        rank_idx = 8 - rank_number

        center_x = (file_idx + 0.5) * square_size[0]
        center_y = (rank_idx + 0.5) * square_size[1]

        piece_name = piece_info['piece']
        piece_color = 'white' if piece_name.startswith('w_') else 'black'
        piece_type = piece_name.split('_')[1]

        # Add piece label
        axs[1, 1].text(center_x, center_y, piece_type[0].upper() if piece_color == 'white' else piece_type[0].lower(),
                      ha='center', va='center', color='w' if piece_color == 'black' else 'k',
                      fontsize=16, fontweight='bold',
                      bbox=dict(facecolor='k' if piece_color == 'white' else 'w', alpha=0.7, boxstyle='round'))

    axs[1, 1].set_title(f'Chess Position (FEN):\n{fen}')
    axs[1, 1].axis('off')

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

def detect_chess_position(segmentation_model, yolo_model, image_path, output_dir):
    """
    Detect chess position in an image.

    Args:
        segmentation_model: The segmentation model
        yolo_model: The YOLO model
        image_path: Path to the input image
        output_dir: Directory to save the output

    Returns:
        fen: FEN string representing the chess position
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Start timing
    start_time = time.time()

    # Detect board
    normalized_board, corners, preprocess_info = detect_board(segmentation_model, image_path)

    if normalized_board is None:
        print(f"No chess board detected in {image_path}")
        return None

    # Detect pieces
    detections = detect_pieces(yolo_model, normalized_board)

    # Map to chess coordinates
    chess_pieces = map_to_chess_coordinates(detections)

    # Generate FEN
    fen = generate_fen(chess_pieces)

    # Calculate total time
    total_time = time.time() - start_time

    # Create output path
    base_name = os.path.splitext(os.path.basename(image_path))[0]
    output_path = os.path.join(output_dir, f"{base_name}_position.png")

    # Visualize detection
    visualize_detection(image_path, normalized_board, detections, chess_pieces, fen, output_path)

    # Save FEN to file
    fen_path = os.path.join(output_dir, f"{base_name}_position.fen")
    with open(fen_path, 'w') as f:
        f.write(fen)

    # Print results
    print(f"Chess position detected in {total_time:.3f}s")
    print(f"FEN: {fen}")
    print(f"Visualization saved to {output_path}")
    print(f"FEN saved to {fen_path}")

    return fen

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Detect chess position in an image')
    parser.add_argument('--segmentation_model', type=str, default='chess_board_detection/models/segmentation_only/tiny_20250519_091307/best_model_dice.pth',
                        help='Path to segmentation model')
    parser.add_argument('--yolo_model', type=str, required=True,
                        help='Path to YOLO model')
    parser.add_argument('--input', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/outputs/piece_detection',
                        help='Directory to save the output')
    parser.add_argument('--confidence', type=float, default=0.25,
                        help='Detection confidence threshold')
    args = parser.parse_args()

    # Load models
    print(f"Loading segmentation model from {args.segmentation_model}")
    segmentation_model = load_segmentation_model(args.segmentation_model)

    print(f"Loading YOLO model from {args.yolo_model}")
    yolo_model = YOLO(args.yolo_model)

    # Detect chess position
    detect_chess_position(segmentation_model, yolo_model, args.input, args.output_dir)

if __name__ == "__main__":
    main()
