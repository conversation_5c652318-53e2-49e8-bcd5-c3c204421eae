"""
Enhanced U-Net model for chess board detection (v5.2).
This version includes specialized components for improved corner detection:
1. Peak Competition Module - Enhances primary peaks while suppressing secondary peaks
2. Cross-Attention Mechanism - Integrates segmentation and heatmap features
3. Balanced architecture for improved peak-to-second ratio
4. Enhanced regularization and normalization
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .enhanced_unet_v5 import EnhancedChessBoardUNetV5
from .improved_enhanced_unet_v5 import ImprovedEnhancedChessBoardUNetV5


class PeakCompetitionModule(nn.Module):
    """
    Module that enhances primary peaks while actively suppressing secondary peaks.
    This directly addresses the peak-to-second ratio challenge.
    """
    def __init__(self, channels=4, competition_strength=3.0):
        super().__init__()
        self.channels = channels
        self.competition_strength = competition_strength
        self.conv = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn = nn.BatchNorm2d(channels)

    def forward(self, x):
        # Skip peak competition during tracing to avoid TracerWarning
        if torch._C._get_tracing_state() is not None:
            return self.bn(self.conv(x))

        # Find peaks and their values
        batch_size, channels, h, w = x.shape
        x_flat = x.view(batch_size, channels, -1)

        # Ensure we have at least one element to find peaks
        if x_flat.shape[2] == 0:
            return self.bn(self.conv(x))

        # Find top-k peaks
        k = min(5, x_flat.shape[2])
        values, indices = torch.topk(x_flat, k=k, dim=2)

        # Create competition mask
        enhanced = x.clone()

        # Use try-except to handle potential errors during peak processing
        try:
            for b in range(batch_size):
                for c in range(channels):
                    # Skip if no peaks found
                    if values.shape[2] == 0:
                        continue

                    # Get primary peak location
                    primary_idx = indices[b, c, 0].item()
                    primary_y, primary_x = primary_idx // w, primary_idx % w

                    # Create distance-based suppression for other peaks
                    for k in range(1, min(values.shape[2], 5)):
                        secondary_idx = indices[b, c, k].item()
                        secondary_y, secondary_x = secondary_idx // w, secondary_idx % w

                        # Calculate suppression factor based on relative value
                        value_ratio = values[b, c, 0] / (values[b, c, k] + 1e-6)
                        suppression = self.competition_strength * (value_ratio - 1.0)

                        # Apply suppression
                        enhanced[b, c, secondary_y, secondary_x] /= (1.0 + suppression)
        except Exception as e:
            # If any error occurs, just return the convolved input without peak competition
            print(f"Warning: Error in peak competition: {e}. Skipping peak competition.")

        # Apply convolution and normalization
        return self.bn(self.conv(enhanced))


class CrossAttentionModule(nn.Module):
    """
    Lightweight feature fusion module that integrates segmentation and heatmap features.
    This allows the segmentation information to guide the corner detection without
    using memory-intensive attention mechanisms.
    """
    def __init__(self, seg_channels, heatmap_channels):
        super().__init__()
        # Project segmentation features to match heatmap channels
        self.seg_proj = nn.Conv2d(seg_channels, heatmap_channels, kernel_size=1)

        # Feature fusion network
        self.fusion = nn.Sequential(
            # Concatenate features (channels will be 2*heatmap_channels)
            nn.Conv2d(heatmap_channels * 2, heatmap_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(heatmap_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(heatmap_channels, heatmap_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(heatmap_channels),
            nn.ReLU(inplace=True)
        )

        # Channel attention to focus on important features
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),  # Global average pooling
            nn.Conv2d(heatmap_channels, heatmap_channels // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(heatmap_channels // 4, heatmap_channels, kernel_size=1),
            nn.Sigmoid()
        )

        # Weighting parameter for residual connection
        self.gamma = nn.Parameter(torch.zeros(1))

    def forward(self, seg_features, heatmap_features):
        # Project segmentation features to match heatmap dimensions
        seg_proj = self.seg_proj(seg_features)

        # Concatenate features along channel dimension
        combined = torch.cat([seg_proj, heatmap_features], dim=1)

        # Apply fusion convolutions
        fused = self.fusion(combined)

        # Apply channel attention
        attention = self.channel_attention(fused)

        # Apply attention and add residual connection
        enhanced = fused * attention

        # Return weighted sum with original features (residual connection)
        return self.gamma * enhanced + heatmap_features


class EnhancedChessBoardUNetV5_2(nn.Module):
    """
    Enhanced U-Net for chess board detection with v5.2 improvements:
    - Peak Competition Module
    - Cross-Attention Mechanism
    - Balanced architecture for improved peak-to-second ratio
    - Enhanced regularization and normalization
    """
    def __init__(self, n_channels=3, dropout_rate=0.3, use_batch_norm=True):
        super(EnhancedChessBoardUNetV5_2, self).__init__()

        # Base model from v5.1
        self.base_model = ImprovedEnhancedChessBoardUNetV5(
            n_channels=n_channels,
            dropout_rate=dropout_rate,
            use_batch_norm=use_batch_norm
        )

        # New peak competition module
        self.peak_competition = PeakCompetitionModule(channels=4)

        # Cross-attention mechanism
        self.cross_attention = CrossAttentionModule(seg_channels=1, heatmap_channels=4)

        # Additional refinement layers
        self.final_refinement = nn.Sequential(
            nn.Conv2d(4, 8, kernel_size=3, padding=1),
            nn.BatchNorm2d(8),
            nn.ReLU(inplace=True),
            nn.Conv2d(8, 4, kernel_size=1)
        )

        # Track current epoch for curriculum learning
        self.current_epoch = 0

    def set_epoch(self, epoch):
        """Update the current epoch for curriculum learning."""
        self.current_epoch = epoch
        if hasattr(self.base_model, 'set_epoch'):
            self.base_model.set_epoch(epoch)

    def forward(self, x):
        # Get base outputs from v5.1 model
        outputs = self.base_model(x)

        # Extract segmentation and heatmap outputs
        segmentation = outputs['segmentation']
        heatmaps = outputs['corner_heatmaps']

        # Apply peak competition module
        enhanced_heatmaps = self.peak_competition(heatmaps)

        # Apply cross-attention mechanism
        attended_heatmaps = self.cross_attention(segmentation, enhanced_heatmaps)

        # Apply final refinement
        refined_heatmaps = self.final_refinement(attended_heatmaps)

        # Residual connection
        final_heatmaps = heatmaps + refined_heatmaps

        # Update outputs
        outputs['corner_heatmaps'] = final_heatmaps

        return outputs
