"""
Data augmentation utilities for chess board detection.
These augmentations are designed to improve corner detection by creating more varied training examples.
"""

import numpy as np
import cv2
import albumentations as A
from albumentations.pytorch import ToTensorV2
import torch


def get_corner_augmentation_transforms(input_size=(256, 256), p=0.5):
    """
    Get augmentation transforms specifically designed to improve corner detection.
    
    Args:
        input_size (tuple): Target size for the image.
        p (float): Probability of applying each augmentation.
        
    Returns:
        train_transform: Transforms for training.
        val_transform: Transforms for validation.
    """
    # Training transforms with corner-focused augmentations
    train_transform = A.<PERSON>se([
        # Resize to target size
        A.Resize(height=input_size[0], width=input_size[1]),
        
        # Basic augmentations
        A.RandomRotate90(p=p),
        <PERSON><PERSON>lip(p=p),
        
        # Color augmentations
        A.OneOf([
            A.RandomBrightnessContrast(p=1.0),
            <PERSON><PERSON>(p=1.0),
            <PERSON><PERSON>(p=1.0),
        ], p=p),
        
        # Blur and noise
        A.One<PERSON>([
            <PERSON><PERSON>(blur_limit=3, p=1.0),
            <PERSON><PERSON>(var_limit=(10.0, 50.0), p=1.0),
        ], p=p*0.5),  # Lower probability for these
        
        # Perspective and distortion transforms (important for corner detection)
        A.OneOf([
            A.ElasticTransform(alpha=1, sigma=50, alpha_affine=50, p=1.0),
            A.GridDistortion(num_steps=5, distort_limit=0.3, p=1.0),
            A.OpticalDistortion(distort_limit=0.3, shift_limit=0.5, p=1.0),
        ], p=p*0.7),  # Higher probability for these
        
        # Perspective transform (crucial for corner detection)
        A.Perspective(scale=(0.05, 0.1), p=p*0.8),  # Higher probability
        
        # Normalization and conversion to tensor
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
    
    # Validation transforms (only resize and normalize)
    val_transform = A.Compose([
        A.Resize(height=input_size[0], width=input_size[1]),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
    
    return train_transform, val_transform


def get_corner_focused_transforms(input_size=(256, 256), p=0.5):
    """
    Get transforms specifically focused on improving corner detection.
    These transforms include more aggressive perspective and distortion.
    
    Args:
        input_size (tuple): Target size for the image.
        p (float): Probability of applying each augmentation.
        
    Returns:
        train_transform: Transforms for training.
        val_transform: Transforms for validation.
    """
    # Training transforms with corner-focused augmentations
    train_transform = A.Compose([
        # Resize to target size
        A.Resize(height=input_size[0], width=input_size[1]),
        
        # Basic augmentations
        A.RandomRotate90(p=p),
        A.Flip(p=p),
        
        # Color augmentations
        A.OneOf([
            A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=1.0),
            A.RandomGamma(gamma_limit=(80, 120), p=1.0),
            A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=1.0),
        ], p=p),
        
        # Blur and noise
        A.OneOf([
            A.GaussianBlur(blur_limit=3, p=1.0),
            A.GaussNoise(var_limit=(10.0, 50.0), p=1.0),
            A.ISONoise(color_shift=(0.01, 0.05), intensity=(0.1, 0.5), p=1.0),
        ], p=p*0.5),
        
        # Perspective and distortion transforms (important for corner detection)
        A.OneOf([
            # More aggressive perspective transform
            A.Perspective(scale=(0.05, 0.2), p=1.0),
            # Grid distortion to simulate different viewing angles
            A.GridDistortion(num_steps=5, distort_limit=0.3, p=1.0),
            # Optical distortion to simulate lens effects
            A.OpticalDistortion(distort_limit=0.3, shift_limit=0.5, p=1.0),
        ], p=p*0.8),  # Higher probability
        
        # Normalization and conversion to tensor
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
    
    # Validation transforms (only resize and normalize)
    val_transform = A.Compose([
        A.Resize(height=input_size[0], width=input_size[1]),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2()
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
    
    return train_transform, val_transform


def generate_corner_heatmaps(corners, shape, sigma=5):
    """
    Generate corner heatmaps from corner coordinates.
    
    Args:
        corners (list): List of corner coordinates (x, y).
        shape (tuple): Shape of the heatmap (H, W).
        sigma (float): Sigma for Gaussian kernel.
        
    Returns:
        heatmaps: Corner heatmaps (4, H, W).
    """
    heatmaps = np.zeros((4, shape[0], shape[1]), dtype=np.float32)
    
    for i, (x, y) in enumerate(corners):
        # Skip if corner is outside the image
        if x < 0 or y < 0 or x >= shape[1] or y >= shape[0]:
            continue
        
        # Create a single channel heatmap
        heatmap = np.zeros(shape, dtype=np.float32)
        
        # Convert to int
        x, y = int(x), int(y)
        
        # Set the peak
        heatmap[y, x] = 1.0
        
        # Apply Gaussian blur
        heatmap = cv2.GaussianBlur(heatmap, (0, 0), sigma)
        
        # Normalize to [0, 1]
        if np.max(heatmap) > 0:
            heatmap = heatmap / np.max(heatmap)
        
        # Add to heatmaps
        heatmaps[i] = heatmap
    
    return heatmaps


def generate_enhanced_corner_heatmaps(corners, shape, sigma=5, edge_suppression=True):
    """
    Generate enhanced corner heatmaps with edge suppression.
    
    Args:
        corners (list): List of corner coordinates (x, y).
        shape (tuple): Shape of the heatmap (H, W).
        sigma (float): Sigma for Gaussian kernel.
        edge_suppression (bool): Whether to suppress activations along edges.
        
    Returns:
        heatmaps: Corner heatmaps (4, H, W).
    """
    heatmaps = np.zeros((4, shape[0], shape[1]), dtype=np.float32)
    
    # Create edge suppression mask if needed
    if edge_suppression:
        # Create masks for the four edges
        edge_masks = np.zeros((4, shape[0], shape[1]), dtype=np.float32)
        
        # Define edge width
        edge_width = 3
        
        # Top edge
        edge_masks[0, :edge_width, :] = 1.0
        # Right edge
        edge_masks[1, :, -edge_width:] = 1.0
        # Bottom edge
        edge_masks[2, -edge_width:, :] = 1.0
        # Left edge
        edge_masks[3, :, :edge_width] = 1.0
    
    for i, (x, y) in enumerate(corners):
        # Skip if corner is outside the image
        if x < 0 or y < 0 or x >= shape[1] or y >= shape[0]:
            continue
        
        # Create a single channel heatmap
        heatmap = np.zeros(shape, dtype=np.float32)
        
        # Convert to int
        x, y = int(x), int(y)
        
        # Set the peak
        heatmap[y, x] = 1.0
        
        # Apply Gaussian blur
        heatmap = cv2.GaussianBlur(heatmap, (0, 0), sigma)
        
        # Apply edge suppression if needed
        if edge_suppression:
            # Define corner region size
            corner_size = min(shape) // 8
            
            # Create corner mask
            corner_mask = np.zeros(shape, dtype=np.float32)
            
            # Set corner region based on which corner this is
            if i == 0:  # Top-left
                corner_mask[:corner_size, :corner_size] = 1.0
            elif i == 1:  # Top-right
                corner_mask[:corner_size, -corner_size:] = 1.0
            elif i == 2:  # Bottom-right
                corner_mask[-corner_size:, -corner_size:] = 1.0
            elif i == 3:  # Bottom-left
                corner_mask[-corner_size:, :corner_size] = 1.0
            
            # Suppress activations outside the corner region
            heatmap = heatmap * corner_mask
        
        # Normalize to [0, 1]
        if np.max(heatmap) > 0:
            heatmap = heatmap / np.max(heatmap)
        
        # Add to heatmaps
        heatmaps[i] = heatmap
    
    return heatmaps
