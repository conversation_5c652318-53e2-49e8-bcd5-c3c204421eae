"""
Train U-Net for chess board segmentation.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
from tqdm import tqdm
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.chessboard_segmentation_unet import get_model
from chess_board_detection.dataset.segmentation_dataset import create_dataloaders

class DiceLoss(nn.Module):
    """Dice loss for segmentation."""
    
    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth
    
    def forward(self, predictions, targets):
        predictions = torch.sigmoid(predictions)
        
        # Flatten
        predictions = predictions.view(-1)
        targets = targets.view(-1)
        
        intersection = (predictions * targets).sum()
        dice = (2. * intersection + self.smooth) / (predictions.sum() + targets.sum() + self.smooth)
        
        return 1 - dice

class CombinedLoss(nn.Module):
    """Combined BCE and Dice loss."""
    
    def __init__(self, bce_weight=0.5, dice_weight=0.5):
        super(CombinedLoss, self).__init__()
        self.bce_weight = bce_weight
        self.dice_weight = dice_weight
        self.bce_loss = nn.BCEWithLogitsLoss()
        self.dice_loss = DiceLoss()
    
    def forward(self, predictions, targets):
        bce = self.bce_loss(predictions, targets)
        dice = self.dice_loss(predictions, targets)
        return self.bce_weight * bce + self.dice_weight * dice

def calculate_iou(predictions, targets, threshold=0.5):
    """Calculate IoU metric."""
    predictions = torch.sigmoid(predictions) > threshold
    targets = targets > threshold
    
    intersection = (predictions & targets).float().sum()
    union = (predictions | targets).float().sum()
    
    if union == 0:
        return 1.0 if intersection == 0 else 0.0
    
    return (intersection / union).item()

def calculate_dice(predictions, targets, threshold=0.5):
    """Calculate Dice coefficient."""
    predictions = torch.sigmoid(predictions) > threshold
    targets = targets > threshold
    
    intersection = (predictions & targets).float().sum()
    total = predictions.float().sum() + targets.float().sum()
    
    if total == 0:
        return 1.0 if intersection == 0 else 0.0
    
    return (2 * intersection / total).item()

def train_epoch(model, train_loader, criterion, optimizer, device):
    """Train for one epoch."""
    model.train()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(train_loader)
    
    pbar = tqdm(train_loader, desc="Training")
    for batch_idx, (images, masks) in enumerate(pbar):
        images = images.to(device)
        masks = masks.to(device)
        
        optimizer.zero_grad()
        
        outputs = model(images)
        loss = criterion(outputs, masks)
        
        loss.backward()
        optimizer.step()
        
        # Calculate metrics
        with torch.no_grad():
            iou = calculate_iou(outputs, masks)
            dice = calculate_dice(outputs, masks)
        
        total_loss += loss.item()
        total_iou += iou
        total_dice += dice
        
        pbar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'IoU': f'{iou:.4f}',
            'Dice': f'{dice:.4f}'
        })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def validate_epoch(model, val_loader, criterion, device):
    """Validate for one epoch."""
    model.eval()
    total_loss = 0
    total_iou = 0
    total_dice = 0
    num_batches = len(val_loader)
    
    with torch.no_grad():
        pbar = tqdm(val_loader, desc="Validation")
        for batch_idx, (images, masks) in enumerate(pbar):
            images = images.to(device)
            masks = masks.to(device)
            
            outputs = model(images)
            loss = criterion(outputs, masks)
            
            # Calculate metrics
            iou = calculate_iou(outputs, masks)
            dice = calculate_dice(outputs, masks)
            
            total_loss += loss.item()
            total_iou += iou
            total_dice += dice
            
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'IoU': f'{iou:.4f}',
                'Dice': f'{dice:.4f}'
            })
    
    return total_loss / num_batches, total_iou / num_batches, total_dice / num_batches

def save_checkpoint(model, optimizer, epoch, loss, save_path):
    """Save model checkpoint."""
    torch.save({
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'loss': loss,
    }, save_path)

def plot_training_history(history, save_path):
    """Plot training history."""
    epochs = range(1, len(history['train_loss']) + 1)
    
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Loss
    axes[0].plot(epochs, history['train_loss'], 'b-', label='Train Loss')
    axes[0].plot(epochs, history['val_loss'], 'r-', label='Val Loss')
    axes[0].set_title('Loss')
    axes[0].set_xlabel('Epoch')
    axes[0].set_ylabel('Loss')
    axes[0].legend()
    axes[0].grid(True)
    
    # IoU
    axes[1].plot(epochs, history['train_iou'], 'b-', label='Train IoU')
    axes[1].plot(epochs, history['val_iou'], 'r-', label='Val IoU')
    axes[1].set_title('IoU')
    axes[1].set_xlabel('Epoch')
    axes[1].set_ylabel('IoU')
    axes[1].legend()
    axes[1].grid(True)
    
    # Dice
    axes[2].plot(epochs, history['train_dice'], 'b-', label='Train Dice')
    axes[2].plot(epochs, history['val_dice'], 'r-', label='Val Dice')
    axes[2].set_title('Dice Coefficient')
    axes[2].set_xlabel('Epoch')
    axes[2].set_ylabel('Dice')
    axes[2].legend()
    axes[2].grid(True)
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=150, bbox_inches='tight')
    plt.close()

def train_model(
    model_type="standard",
    epochs=100,
    batch_size=8,
    learning_rate=1e-4,
    image_size=(256, 256),
    save_dir="chess_board_detection/unet_segmentation_results"
):
    """
    Train the U-Net segmentation model.
    """
    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create save directory
    save_dir = Path(save_dir)
    save_dir.mkdir(parents=True, exist_ok=True)
    
    # Data paths
    IMAGE_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real"
    ANNOTATION_FILE = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json"
    
    # Create dataloaders
    print("Creating dataloaders...")
    train_loader, val_loader = create_dataloaders(
        IMAGE_DIR, ANNOTATION_FILE, 
        batch_size=batch_size, 
        image_size=image_size,
        train_split=0.8,
        num_workers=4
    )
    
    # Create model
    print(f"Creating {model_type} U-Net model...")
    model = get_model(model_type=model_type, n_channels=3, n_classes=1)
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Model parameters: {total_params:,}")
    
    # Loss and optimizer
    criterion = CombinedLoss(bce_weight=0.5, dice_weight=0.5)
    optimizer = optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=1e-4)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=10, factor=0.5)
    
    # Training history
    history = {
        'train_loss': [], 'val_loss': [],
        'train_iou': [], 'val_iou': [],
        'train_dice': [], 'val_dice': []
    }
    
    best_val_dice = 0
    patience = 20
    patience_counter = 0
    
    print(f"Starting training for {epochs} epochs...")
    start_time = time.time()
    
    for epoch in range(epochs):
        print(f"\nEpoch {epoch+1}/{epochs}")
        
        # Train
        train_loss, train_iou, train_dice = train_epoch(model, train_loader, criterion, optimizer, device)
        
        # Validate
        val_loss, val_iou, val_dice = validate_epoch(model, val_loader, criterion, device)
        
        # Update scheduler
        scheduler.step(val_loss)
        
        # Save history
        history['train_loss'].append(train_loss)
        history['val_loss'].append(val_loss)
        history['train_iou'].append(train_iou)
        history['val_iou'].append(val_iou)
        history['train_dice'].append(train_dice)
        history['val_dice'].append(val_dice)
        
        # Print epoch results
        print(f"Train - Loss: {train_loss:.4f}, IoU: {train_iou:.4f}, Dice: {train_dice:.4f}")
        print(f"Val   - Loss: {val_loss:.4f}, IoU: {val_iou:.4f}, Dice: {val_dice:.4f}")
        print(f"LR: {optimizer.param_groups[0]['lr']:.6f}")
        
        # Save best model
        if val_dice > best_val_dice:
            best_val_dice = val_dice
            patience_counter = 0
            save_checkpoint(model, optimizer, epoch, val_loss, save_dir / "best_model.pth")
            print(f"New best model saved! Val Dice: {val_dice:.4f}")
        else:
            patience_counter += 1
        
        # Save checkpoint every 10 epochs
        if (epoch + 1) % 10 == 0:
            save_checkpoint(model, optimizer, epoch, val_loss, save_dir / f"checkpoint_epoch_{epoch+1}.pth")
        
        # Early stopping
        if patience_counter >= patience:
            print(f"Early stopping triggered after {patience} epochs without improvement")
            break
    
    # Save final model and history
    save_checkpoint(model, optimizer, epoch, val_loss, save_dir / "final_model.pth")
    
    with open(save_dir / "training_history.json", 'w') as f:
        json.dump(history, f, indent=2)
    
    # Plot training history
    plot_training_history(history, save_dir / "training_history.png")
    
    training_time = time.time() - start_time
    print(f"\nTraining completed in {training_time/3600:.2f} hours")
    print(f"Best validation Dice: {best_val_dice:.4f}")
    print(f"Results saved to: {save_dir}")
    
    return model, history

if __name__ == "__main__":
    # Train standard U-Net
    print("Training Standard U-Net...")
    model, history = train_model(
        model_type="standard",
        epochs=100,
        batch_size=8,
        learning_rate=1e-4,
        image_size=(256, 256),
        save_dir="chess_board_detection/unet_standard_results"
    )
