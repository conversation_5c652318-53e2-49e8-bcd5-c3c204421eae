@echo off
echo Training simple YOLO model for chess piece detection (basic version)...
echo Max epochs: 100 (no phase system)
echo Target metrics: mAP50=0.99, Precision=0.99, Recall=0.99
echo After 100 epochs, you will be asked if you want to continue for 10 more epochs
echo Using absolute paths for all files

python "C:/Users/<USER>/OneDrive/Desktop/a1 v1/chess_board_detection/train_simple_basic.py" --model "C:/Users/<USER>/OneDrive/Desktop/a1 v1/yolo11n.pt" --dataset "C:/Users/<USER>/OneDrive/Desktop/a1 v1/chess_board_detection/piece_detection/enhanced_dataset_99plus/dataset.yaml" --epochs 100 --patience 20 --target-map50 0.99 --target-precision 0.99 --target-recall 0.99

pause
