# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# ============================================================================
# CHESS VISION APP - OPTIMIZED PROGUARD RULES
# ============================================================================

# Keep main application class
-keep public class com.chessvision.app.MainActivity
-keep public class com.chessvision.app.ChessVisionApplication

# ============================================================================
# ONNX RUNTIME - CRITICAL FOR AI MODELS
# ============================================================================

# Keep all ONNX Runtime classes and methods
-keep class ai.onnxruntime.** { *; }
-keep interface ai.onnxruntime.** { *; }
-keep enum ai.onnxruntime.** { *; }

# Keep native methods for ONNX Runtime JNI
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep ONNX Runtime providers
-keep class ai.onnxruntime.providers.** { *; }

# Keep ONNX tensor and session classes
-keep class ai.onnxruntime.OnnxTensor { *; }
-keep class ai.onnxruntime.OrtSession { *; }
-keep class ai.onnxruntime.OrtEnvironment { *; }
-keep class ai.onnxruntime.OnnxValue { *; }

# ============================================================================
# ANDROID CAMERA X - ESSENTIAL FOR CHESS BOARD CAPTURE
# ============================================================================

# Keep Camera X classes
-keep class androidx.camera.** { *; }
-keep interface androidx.camera.** { *; }

# Keep camera lifecycle and use cases
-keep class androidx.camera.lifecycle.** { *; }
-keep class androidx.camera.core.** { *; }

# ============================================================================
# JETPACK COMPOSE - UI FRAMEWORK
# ============================================================================

# Keep Compose runtime classes
-keep class androidx.compose.runtime.** { *; }
-keep class androidx.compose.ui.** { *; }
-keep class androidx.compose.material3.** { *; }

# Keep Compose compiler generated classes
-keep class **.*ComposableSingletons* { *; }
-keep class **.*LiveLiterals* { *; }

# ============================================================================
# KOTLIN COROUTINES - ASYNC OPERATIONS
# ============================================================================

# Keep coroutines classes
-keep class kotlinx.coroutines.** { *; }
-keep class kotlin.coroutines.** { *; }

# ============================================================================
# CHESS VISION APP SPECIFIC CLASSES
# ============================================================================

# Keep our AI processing classes
-keep class com.chessvision.app.ai.** { *; }

# Keep chess board and piece classes
-keep class com.chessvision.app.ChessBoard** { *; }
-keep class com.chessvision.app.ChessPiece** { *; }

# Keep data classes and enums
-keep class com.chessvision.app.ChessAnalysisResult** { *; }
-keep class com.chessvision.app.PieceType { *; }
-keep class com.chessvision.app.PieceColor { *; }

# ============================================================================
# GENERAL ANDROID OPTIMIZATIONS
# ============================================================================

# Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Keep Parcelable implementations
-keep class * implements android.os.Parcelable {
    public static final ** CREATOR;
}

# Keep Serializable classes
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# ============================================================================
# PERFORMANCE OPTIMIZATIONS
# ============================================================================

# Enable aggressive optimizations
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5
-allowaccessmodification
-dontpreverify

# Remove unused code more aggressively
-dontwarn **
-ignorewarnings

# ============================================================================
# SIZE OPTIMIZATIONS
# ============================================================================

# Remove debug information in release
-keepattributes !SourceFile,!LineNumberTable

# Merge classes and interfaces aggressively
-mergeinterfacesaggressively

# Note: Resource shrinking is handled by Gradle, not ProGuard

# ============================================================================
# CHESS AI SPECIFIC OPTIMIZATIONS
# ============================================================================

# Keep FEN notation methods
-keep class com.chessvision.app.** {
    *** *fen*(...);
    *** *FEN*(...);
}

# Keep chess position methods
-keep class com.chessvision.app.** {
    *** *position*(...);
    *** *Position*(...);
}

# Keep model loading methods
-keep class com.chessvision.app.** {
    *** *model*(...);
    *** *Model*(...);
    *** *onnx*(...);
    *** *ONNX*(...);
}
