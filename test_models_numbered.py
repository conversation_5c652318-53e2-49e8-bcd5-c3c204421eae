"""
Test Models with Numbered Detections Script for Chess Piece Detection

This script tests three models on a single test image with numbered detections:
1. Original model (before fine-tuning)
2. Best mAP model (Epoch 107)
3. Best Loss model (Epoch 111)

The script creates:
1. An image with numbered detections (no labels or legend)
2. A text file with detection details for each model
"""

import os
import cv2
import numpy as np
import torch
from ultralytics import YOLO
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, Patch

# Configuration
CONFIG = {
    # Models to test
    "models": [
        {"path": "runs/detect/train/weights/best.pt", "name": "Original Best Model (Epoch 86)", "coco_classes": False},
        {"path": "runs/detect/simple_continue/continue_epoch107/weights/best.pt", "name": "Epoch 107 (Best mAP)", "coco_classes": False},
        {"path": "runs/detect/simple_continue/continue_epoch111/weights/best.pt", "name": "Epoch 111 (Best Loss)", "coco_classes": False}
    ],

    # Test image
    "test_image": r"chess_board_detection\data\real\9.jpg",

    # Output directory for visualizations
    "output_dir": "numbered_detection_comparison",

    # Class names
    "class_names": [
        "white_pawn", "white_knight", "white_bishop", "white_rook",
        "white_queen", "white_king", "black_pawn", "black_knight",
        "black_bishop", "black_rook", "black_queen", "black_king"
    ]
}

# Define colors for each class (white pieces in light colors, black pieces in dark colors)
COLORS = {
    'white_pawn': (200, 200, 200),    # Light gray
    'white_knight': (173, 216, 230),  # Light blue
    'white_bishop': (152, 251, 152),  # Light green
    'white_rook': (255, 182, 193),    # Light pink
    'white_queen': (255, 255, 224),   # Light yellow
    'white_king': (255, 228, 196),    # Light orange
    'black_pawn': (100, 100, 100),    # Dark gray
    'black_knight': (70, 130, 180),   # Dark blue
    'black_bishop': (34, 139, 34),    # Dark green
    'black_rook': (178, 34, 34),      # Dark red
    'black_queen': (218, 165, 32),    # Dark yellow/gold
    'black_king': (139, 69, 19)       # Dark brown
}

def box_iou(box1, box2):
    """Calculate IoU between two boxes."""
    # Box coordinates
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2

    # Calculate intersection area
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)

    if x2_i < x1_i or y2_i < y1_i:
        return 0.0  # No intersection

    intersection_area = (x2_i - x1_i) * (y2_i - y1_i)

    # Calculate union area
    box1_area = (x2_1 - x1_1) * (y2_1 - y1_1)
    box2_area = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = box1_area + box2_area - intersection_area

    # Calculate IoU
    iou = intersection_area / union_area

    return iou

def remove_duplicate_detections(boxes, scores, class_ids, iou_threshold=0.5, area_overlap_threshold=0.8):
    """
    Remove duplicate detections by keeping only the highest confidence detection
    when bounding boxes overlap significantly.

    This function prioritizes:
    1. Highest confidence when there's significant overlap
    2. Checks for both IoU and area containment (one box mostly inside another)
    """
    if len(boxes) == 0:
        return boxes, scores, class_ids

    # Convert to numpy arrays if they're not already
    boxes = boxes.cpu().numpy() if hasattr(boxes, 'cpu') else boxes
    scores = scores.cpu().numpy() if hasattr(scores, 'cpu') else scores
    class_ids = class_ids.cpu().numpy() if hasattr(class_ids, 'cpu') else class_ids

    # Sort by confidence (highest first)
    indices = np.argsort(scores)[::-1]
    boxes = boxes[indices]
    scores = scores[indices]
    class_ids = class_ids[indices]

    # Initialize list of indices to keep
    keep_indices = []

    # Initialize list of indices to remove
    remove_indices = []

    # Calculate areas for all boxes
    areas = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])

    for i in range(len(boxes)):
        # Skip if this box was already marked for removal
        if i in remove_indices:
            continue

        # Add current box to keep list
        keep_indices.append(i)

        # Compare with all remaining boxes
        for j in range(i + 1, len(boxes)):
            # Skip if this box was already marked for removal
            if j in remove_indices:
                continue

            # Calculate IoU
            iou = box_iou(boxes[i], boxes[j])

            # Calculate intersection area for containment check
            x1 = max(boxes[i][0], boxes[j][0])
            y1 = max(boxes[i][1], boxes[j][1])
            x2 = min(boxes[i][2], boxes[j][2])
            y2 = min(boxes[i][3], boxes[j][3])

            if x2 > x1 and y2 > y1:  # There is an intersection
                intersection_area = (x2 - x1) * (y2 - y1)

                # Check if one box is mostly contained within the other
                area_ratio_i_in_j = intersection_area / areas[i]
                area_ratio_j_in_i = intersection_area / areas[j]

                # Remove box j if:
                # 1. IoU is above threshold, OR
                # 2. Box j is mostly contained within box i, OR
                # 3. Box i is mostly contained within box j (but i has higher confidence)
                if (iou > iou_threshold or
                    area_ratio_i_in_j > area_overlap_threshold or
                    area_ratio_j_in_i > area_overlap_threshold):
                    remove_indices.append(j)

    # Keep only the selected boxes
    filtered_boxes = boxes[keep_indices]
    filtered_scores = scores[keep_indices]
    filtered_class_ids = class_ids[keep_indices]

    return filtered_boxes, filtered_scores, filtered_class_ids

def test_model(model_path, model_name, img_path, use_coco_classes=False):
    """Test a model on a single image and return the results."""
    print(f"\nTesting model: {model_name}")
    print(f"Model path: {model_path}")

    # Load model
    model = YOLO(model_path)

    # Read and preprocess image to 416x416 (same as training)
    img = cv2.imread(img_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img_resized = cv2.resize(img, (416, 416))

    # Run inference with specific size and higher IoU threshold to reduce duplicates
    print(f"Running inference on {img_path} (resized to 416x416)...")
    results = model(img_resized, imgsz=416, iou=0.7)[0]

    # Get detections
    boxes = results.boxes.xyxy
    scores = results.boxes.conf
    class_ids = results.boxes.cls

    # Remove duplicate detections
    print("Removing duplicate detections...")
    filtered_boxes, filtered_scores, filtered_class_ids = remove_duplicate_detections(
        boxes, scores, class_ids, iou_threshold=0.45, area_overlap_threshold=0.7
    )

    # Process results
    classes = []
    confidences = []

    for i in range(len(filtered_boxes)):
        cls_id = int(filtered_class_ids[i])
        conf = filtered_scores[i]

        # Use appropriate class names based on model type
        if use_coco_classes:
            if cls_id < len(CONFIG["coco_class_names"]):
                class_name = CONFIG["coco_class_names"][cls_id]
            else:
                class_name = f"unknown_{cls_id}"
        else:
            if cls_id < len(CONFIG["class_names"]):
                class_name = CONFIG["class_names"][cls_id]
            else:
                class_name = f"unknown_{cls_id}"

        classes.append(class_name)
        confidences.append(conf)

    print(f"Detected {len(filtered_boxes)} objects after removing duplicates")

    # Create a new Results object with filtered detections
    # Instead of modifying the results object, we'll just return the filtered data
    filtered_boxes_tensor = torch.tensor(filtered_boxes) if len(filtered_boxes) > 0 else torch.zeros((0, 4))
    filtered_scores_tensor = torch.tensor(filtered_scores) if len(filtered_scores) > 0 else torch.zeros(0)
    filtered_class_ids_tensor = torch.tensor(filtered_class_ids) if len(filtered_class_ids) > 0 else torch.zeros(0)

    # Create a simple dictionary to hold the filtered results
    filtered_results = {
        'boxes': filtered_boxes_tensor,
        'scores': filtered_scores_tensor,
        'class_ids': filtered_class_ids_tensor
    }

    return filtered_results, classes, confidences

def create_visualization(img_path, results_list, model_names, classes_list, confidences_list):
    """Create a visualization comparing the results of multiple models with numbered detections."""
    # Read and resize the image to 416x416 (same as used for inference)
    img = cv2.imread(img_path)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    img_resized = cv2.resize(img, (416, 416))

    # Create figure with subplots
    fig, axes = plt.subplots(1, len(results_list), figsize=(7*len(results_list), 10))

    # Process each model's results
    for i, (results, model_name, classes, confidences) in enumerate(zip(results_list, model_names, classes_list, confidences_list)):
        # Create a copy of the resized image for this model
        img_copy = img_resized.copy()

        # Get boxes from our filtered results dictionary
        boxes = results['boxes']

        # Draw boxes on the image
        for j in range(len(boxes)):
            if isinstance(boxes, torch.Tensor):
                x1, y1, x2, y2 = boxes[j].cpu().numpy().astype(int)
            else:
                x1, y1, x2, y2 = boxes[j].astype(int)

            class_name = classes[j]  # Use the class name from the classes list
            conf = confidences[j]

            # Get color for the class
            if class_name in COLORS:
                color = COLORS[class_name]
            else:
                # Use a default color for COCO classes or unknown classes
                color = (0, 255, 0)  # Green

            # Draw bounding box
            cv2.rectangle(img_copy, (x1, y1), (x2, y2), color, 2)

            # Draw only the number (no text label)
            cv2.putText(img_copy, f"{j+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 3)  # Black outline
            cv2.putText(img_copy, f"{j+1}", (x1, y1 - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 1)  # White text

        # Display the image
        axes[i].imshow(img_copy)
        axes[i].set_title(f"{model_name}: {len(boxes)} detections", fontsize=16)
        axes[i].axis('off')

    plt.tight_layout()
    return fig

def create_detection_text_file(output_path, model_names, classes_list, confidences_list, boxes_list):
    """Create a text file with detection details for each model."""
    with open(output_path, 'w') as f:
        f.write("Chess Piece Detection - Model Comparison\n")
        f.write("=" * 50 + "\n\n")

        for model_name, classes, confidences, boxes in zip(model_names, classes_list, confidences_list, boxes_list):
            f.write(f"{model_name} Detections:\n")
            f.write("-" * 50 + "\n")
            
            for i, (cls, conf) in enumerate(zip(classes, confidences)):
                if isinstance(boxes[i], torch.Tensor):
                    x1, y1, x2, y2 = boxes[i].cpu().numpy().astype(int)
                else:
                    x1, y1, x2, y2 = boxes[i].astype(int)
                    
                f.write(f"Detection #{i+1}: {cls} (Confidence: {conf:.4f})\n")
                f.write(f"   Bounding Box: [{x1}, {y1}, {x2}, {y2}]\n")
            
            f.write(f"\nTotal detections: {len(classes)}\n")
            avg_conf = sum(confidences) / len(confidences) if confidences else 0
            f.write(f"Average confidence: {avg_conf:.4f}\n\n")
            f.write("=" * 50 + "\n\n")

def main():
    """Main function."""
    print("Chess Piece Detection - Model Comparison with Numbered Detections")
    print("=" * 50)

    # Create output directory
    os.makedirs(CONFIG["output_dir"], exist_ok=True)

    # Test each model
    results_list = []
    classes_list = []
    confidences_list = []
    model_names = []
    boxes_list = []

    for model_info in CONFIG["models"]:
        results, classes, confidences = test_model(
            model_info["path"],
            model_info["name"],
            CONFIG["test_image"],
            model_info.get("coco_classes", False)
        )
        results_list.append(results)
        classes_list.append(classes)
        confidences_list.append(confidences)
        model_names.append(model_info["name"])
        boxes_list.append(results['boxes'])

    # Create visualization
    fig = create_visualization(
        CONFIG["test_image"],
        results_list,
        model_names,
        classes_list,
        confidences_list
    )

    # Save visualization
    output_image_path = os.path.join(CONFIG["output_dir"], "numbered_detection_comparison.png")
    fig.savefig(output_image_path, dpi=300, bbox_inches='tight')
    plt.close(fig)

    # Create and save text file with detection details
    output_text_path = os.path.join(CONFIG["output_dir"], "detection_details.txt")
    create_detection_text_file(output_text_path, model_names, classes_list, confidences_list, boxes_list)

    # Print comparison summary
    print("\nModel Comparison Summary:")
    print("=" * 50)
    print(f"{'Model':<25} {'Detections':<15} {'Avg Confidence':<15}")
    print("-" * 55)
    for model_name, confidences in zip(model_names, confidences_list):
        avg_conf = sum(confidences) / len(confidences) if confidences else 0
        print(f"{model_name:<25} {len(confidences):<15} {avg_conf:.4f}")

    print("\nComparison completed!")
    print(f"Results saved to:")
    print(f"- Image: {output_image_path}")
    print(f"- Detection details: {output_text_path}")

if __name__ == "__main__":
    main()
