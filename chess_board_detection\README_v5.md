# Chess Board Detection v5

This document explains the v5 implementation of the chess board detection model, which addresses the challenges identified in v4 training.

## Key Improvements in v5

### 1. Peak Sharpening Module

The v5 model includes a dedicated peak sharpening module to improve the peak-to-second ratio, which was a persistent challenge in v4:

```python
class PeakSharpeningModule(nn.Module):
    """
    Module that enhances the primary peak in each heatmap while suppressing secondary peaks.
    This addresses the persistent challenge with peak-to-second ratio.
    """
    def __init__(self, kernel_size=5, channels=4):
        super(PeakSharpeningModule, self).__init__()
        
        # Convolutional layers to identify and enhance primary peaks
        self.peak_detector = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=kernel_size, padding=kernel_size//2, groups=channels),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Attention mechanism to focus on primary peaks
        self.attention = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.<PERSON>g<PERSON><PERSON>()
        )
        
        # Suppression mechanism for secondary peaks
        self.suppressor = nn.Sequential(
            nn.Conv2d(channels*2, channels, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
        )
```

### 2. Detection Recovery Mechanism

To address the declining detection rate observed in v4, v5 includes a detection recovery mechanism:

```python
class DetectionRecoveryModule(nn.Module):
    """
    Module that ensures all corners are detected by recovering missed corners.
    This addresses the challenge with detection rate.
    """
    def __init__(self, channels=4, features=32):
        super(DetectionRecoveryModule, self).__init__()
        
        # Multi-scale detection
        self.multi_scale = nn.ModuleList([
            nn.Conv2d(channels, channels, kernel_size=k, padding=k//2, groups=channels)
            for k in [3, 5, 7]
        ])
        
        # Feature fusion
        self.fusion = nn.Sequential(
            nn.Conv2d(channels * 3, features, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(features, channels, kernel_size=3, padding=1),
            nn.Sigmoid()
        )
```

### 3. Geometric Refinement Module

To improve geometric consistency, v5 includes a dedicated geometric refinement module:

```python
class GeometricRefinementModule(nn.Module):
    """
    Module that refines corner positions to ensure geometric consistency.
    This addresses the challenge with geometric loss.
    """
    def __init__(self, channels=4, features=64):
        super(GeometricRefinementModule, self).__init__()
        
        # Feature extraction
        self.features = nn.Sequential(
            nn.Conv2d(channels, features, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=3, padding=1),
            nn.ReLU()
        )
        
        # Geometric relationship modeling
        self.geometric = nn.Sequential(
            nn.Conv2d(features, features, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=3, padding=1),
            nn.ReLU()
        )
```

### 4. Robust Segmentation Guidance Loss

To address the negative segmentation guidance loss observed in v4, v5 includes a robust version:

```python
class RobustSegmentationGuidanceLoss(nn.Module):
    """
    Robust version of the segmentation guidance loss that prevents negative values.
    """
    def __init__(self, weight=1.0, boundary_width=5):
        super(RobustSegmentationGuidanceLoss, self).__init__()
        self.weight = weight
        self.boundary_width = boundary_width
        self.eps = 1e-6
    
    def forward(self, segmentation, heatmaps):
        # ...
        
        # Ensure loss is non-negative
        loss = torch.clamp(loss, min=0.0)
```

### 5. Enhanced Peak-to-Second Ratio Loss

To specifically target the peak-to-second ratio, v5 includes an enhanced loss function:

```python
class EnhancedPeakToSecondRatioLoss(nn.Module):
    """
    Enhanced loss function that specifically targets the peak-to-second ratio.
    """
    def __init__(self, weight=8.0, target_ratio=1.5):
        super(EnhancedPeakToSecondRatioLoss, self).__init__()
        self.weight = weight
        self.target_ratio = target_ratio
        self.eps = 1e-6
    
    def forward(self, heatmaps):
        # ...
        
        # Penalize if ratio is below target
        if ratio < self.target_ratio:
            # Quadratic penalty for more aggressive optimization
            penalty = torch.pow(self.target_ratio - ratio, 2)
```

### 6. Expanded Validation Set

To address the validation instability observed in v4, v5 includes an expanded validation set:

```python
def create_expanded_validation_set(data_dir, annotation_file, fold_idx=0, n_folds=5):
    """
    Create an expanded validation set using k-fold cross-validation.
    """
    # ...
    
    # Create multiple validation datasets with different augmentations
    val_datasets = []
    
    # Original validation set
    val_dataset_orig = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_validation_augmentation()
    )
    val_dataset_orig = torch.utils.data.Subset(val_dataset_orig, val_indices)
    val_datasets.append(val_dataset_orig)
    
    # Create additional validation sets with different augmentations
    for i in range(3):  # Create 3 additional validation sets
        # ...
```

### 7. Two-Phase Training Approach

To balance different optimization objectives, v5 implements a two-phase training approach:

```python
# Phase 1: Focus on peak-to-second ratio and detection rate
criterion_heatmap_phase1 = EnhancedCornerFocusedHeatmapLossV5(
    separation_weight=0.6,
    peak_separation_weight=0.5,
    edge_suppression_weight=0.7,
    peak_enhancement_weight=0.5,
    peak_to_second_ratio_weight=10.0,  # Higher weight for peak-to-second ratio
    detection_rate_weight=8.0,         # Higher weight for detection rate
    segmentation_guidance_weight=1.0
)

# Phase 2: Fine-tune with balanced weights
criterion_heatmap_phase2 = EnhancedCornerFocusedHeatmapLossV5(
    separation_weight=0.6,
    peak_separation_weight=0.5,
    edge_suppression_weight=0.7,
    peak_enhancement_weight=0.5,
    peak_to_second_ratio_weight=8.0,   # Slightly reduced from phase 1
    detection_rate_weight=5.0,         # Slightly reduced from phase 1
    segmentation_guidance_weight=1.0
)
```

## Usage

### Training

To train the v5 model:

```bash
python chess_board_detection/train_v5.py --data_dir /path/to/data --annotation_file /path/to/annotations.json --output_dir /path/to/output --continue_from_v4
```

Options:
- `--continue_from_v4`: Continue training from v4 checkpoint
- `--skip_phase1`: Skip phase 1 and go directly to phase 2
- `--fold_idx`: Index of the fold to use as validation (default: 0)
- `--n_folds`: Number of folds for cross-validation (default: 5)
- `--lr_phase1`: Learning rate for phase 1 (default: 0.001)
- `--lr_phase2`: Learning rate for phase 2 (default: 0.0005)
- `--epochs_phase1`: Number of epochs for phase 1 (default: 40)
- `--epochs_phase2`: Number of epochs for phase 2 (default: 80)
- `--dropout_rate`: Dropout rate (default: 0.2)
- `--weight_decay`: Weight decay (default: 1e-5)
- `--cpu`: Use CPU instead of GPU

### Inference

To run inference with the v5 model:

```bash
python chess_board_detection/inference_enhanced_v5.py --image_path /path/to/image.jpg --model_path /path/to/model.pth --use_post_processing --use_segmentation_guidance
```

Options:
- `--output_path`: Path to save output visualization
- `--use_post_processing`: Use post-processing
- `--use_segmentation_guidance`: Use segmentation guidance
- `--cpu`: Use CPU instead of GPU

## Expected Improvements

Based on the analysis of v4 training, the v5 model is expected to show improvements in:

1. **Peak-to-Second Ratio**: The peak sharpening module and enhanced loss function should improve this metric, which was resistant to improvement in v4.

2. **Detection Rate**: The detection recovery mechanism should address the declining detection rate observed in later epochs of v4.

3. **Geometric Consistency**: The geometric refinement module should improve the geometric loss, which was inconsistent in v4.

4. **Validation Stability**: The expanded validation set should provide more stable and reliable metrics compared to v4.

5. **Segmentation Guidance**: The robust segmentation guidance loss should prevent the negative values observed in v4.

## Comparison with v4

| Aspect | v4 | v5 |
|--------|----|----|
| **Peak-to-Second Ratio** | Struggled to meet target (1.0074 in training, 1.0026 in validation) | Enhanced with dedicated module and loss function |
| **Detection Rate** | Declined in later epochs (0.7917 in training, 0.8500 in validation) | Improved with detection recovery mechanism |
| **Geometric Loss** | Inconsistent (0.2883 in training, 0.8620 in validation) | Stabilized with geometric refinement module |
| **Validation Stability** | Extreme fluctuations due to small validation set | Improved with expanded validation set |
| **Segmentation Guidance** | Negative values in later epochs | Fixed with robust loss function |

## Conclusion

The v5 model represents a significant improvement over v4, addressing the specific challenges identified during v4 training. The specialized modules and loss functions, combined with the two-phase training approach and expanded validation set, should result in a more robust and reliable model for chess board detection.
