"""
Test chess piece detection on a real chess board image.
This script:
1. Detects the chess board using the distilled segmentation model
2. Extracts and normalizes the chess board region
3. Detects chess pieces within the normalized board using YOLO
4. Visualizes the results

The script supports three approaches:
- Standard approach: Detect board with segmentation model, then detect all pieces with YOLO
- Two-stage approach: Detect board, then classify each square individually
- Confusion matrix analysis: Generate a confusion matrix to analyze detection errors
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
from ultralytics import YOLO
import time
import json
from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import models and utilities
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2 as ImprovedCornerDetectionModel
from chess_board_detection.piece_detection.prepare_dataset import (
    preprocess_image, normalize_for_model, find_corners_from_segmentation,
    sort_corners, extract_and_normalize_board
)
from chess_board_detection.piece_detection.two_stage_detection import TwoStageChessDetection
from chess_board_detection.piece_detection.analyze_results import generate_confusion_matrix, visualize_confusion_matrix

def load_segmentation_model(model_path):
    """Load the distilled segmentation model."""
    model = TinySegmentationModel(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def load_corner_detection_model(model_path):
    """Load the corner detection model."""
    model = ImprovedCornerDetectionModel()
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def detect_board(segmentation_model, image_path, output_size=(640, 640)):
    """
    Detect the chess board and extract a normalized view.

    Args:
        segmentation_model: The segmentation model
        image_path: Path to the input image
        output_size: Size of the output normalized board image

    Returns:
        normalized_board: Normalized chess board image
        corners: Detected corner coordinates
        preprocess_info: Preprocessing information
    """
    # Preprocess image
    preprocessed_image, preprocess_info = preprocess_image(image_path)

    # Normalize for model
    input_tensor = normalize_for_model(preprocessed_image)

    # Run inference
    with torch.no_grad():
        outputs = segmentation_model(input_tensor)

    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]

    # Find corners from segmentation
    keypoints = find_corners_from_segmentation(segmentation)

    if keypoints is None:
        print(f"No chess board detected in {image_path}")
        return None, None, None

    # Extract and normalize board
    normalized_board = extract_and_normalize_board(preprocessed_image, keypoints, output_size)

    return normalized_board, keypoints, preprocess_info

def detect_pieces(yolo_model, normalized_board, confidence=0.25):
    """
    Detect chess pieces in the normalized board image.

    Args:
        yolo_model: The YOLO model
        normalized_board: Normalized chess board image
        confidence: Detection confidence threshold

    Returns:
        detections: List of detected pieces with coordinates and classes
    """
    # Convert to BGR for YOLO
    normalized_board_bgr = cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR)

    # Run inference
    results = yolo_model(normalized_board_bgr, conf=confidence)

    # Extract detections
    detections = []

    for result in results:
        boxes = result.boxes

        for i in range(len(boxes)):
            x1, y1, x2, y2 = boxes.xyxy[i].tolist()
            confidence = boxes.conf[i].item()
            class_id = int(boxes.cls[i].item())
            class_name = result.names[class_id]

            # Calculate center point
            center_x = (x1 + x2) / 2
            center_y = (y1 + y2) / 2

            detections.append({
                'class_id': class_id,
                'class_name': class_name,
                'confidence': confidence,
                'bbox': (x1, y1, x2, y2),
                'center': (center_x, center_y)
            })

    return detections

def map_to_chess_coordinates(detections, board_size=(640, 640)):
    """
    Map detected pieces to chess board coordinates (A1-H8).

    Args:
        detections: List of detected pieces with coordinates
        board_size: Size of the normalized board image

    Returns:
        chess_pieces: Dictionary mapping chess coordinates to piece names
    """
    chess_pieces = {}

    # Calculate square size
    square_width = board_size[0] / 8
    square_height = board_size[1] / 8

    for detection in detections:
        center_x, center_y = detection['center']

        # Calculate chess coordinates
        file_idx = int(center_x / square_width)
        rank_idx = int(center_y / square_height)

        # Convert to chess notation (A1-H8)
        file_letter = chr(ord('a') + file_idx)
        rank_number = 8 - rank_idx

        # Create chess coordinate
        chess_coord = f"{file_letter}{rank_number}"

        # Map piece name
        piece_name = detection['class_name']

        # Store in dictionary (keep highest confidence piece if multiple detections in same square)
        if chess_coord not in chess_pieces or detection['confidence'] > chess_pieces[chess_coord]['confidence']:
            chess_pieces[chess_coord] = {
                'piece': piece_name,
                'confidence': detection['confidence']
            }

    return chess_pieces

def visualize_detection(image_path, normalized_board, detections, chess_pieces, output_path):
    """
    Visualize the chess board detection and piece detection results.

    Args:
        image_path: Path to the input image
        normalized_board: Normalized chess board image
        detections: List of detected pieces
        chess_pieces: Dictionary mapping chess coordinates to piece names
        output_path: Path to save the visualization
    """
    # Create figure with 2x2 subplots
    fig, axs = plt.subplots(2, 2, figsize=(15, 12))

    # Plot original image
    original_image = cv2.imread(image_path)
    original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
    axs[0, 0].imshow(original_image)
    axs[0, 0].set_title('Original Image')
    axs[0, 0].axis('off')

    # Plot normalized board
    axs[0, 1].imshow(normalized_board)
    axs[0, 1].set_title('Normalized Chess Board')
    axs[0, 1].axis('off')

    # Plot detected pieces
    axs[1, 0].imshow(normalized_board)

    # Draw bounding boxes
    for detection in detections:
        x1, y1, x2, y2 = detection['bbox']
        class_name = detection['class_name']
        confidence = detection['confidence']

        # Determine color based on piece color
        color = 'r' if 'white' in class_name else 'b'

        # Draw bounding box
        rect = plt.Rectangle((x1, y1), x2-x1, y2-y1, linewidth=2, edgecolor=color, facecolor='none')
        axs[1, 0].add_patch(rect)

        # Add label
        axs[1, 0].text(x1, y1-5, f"{class_name.split('_')[1]}: {confidence:.2f}", color=color, fontsize=8)

    axs[1, 0].set_title(f'Detected Pieces ({len(detections)})')
    axs[1, 0].axis('off')

    # Plot chess position
    axs[1, 1].imshow(normalized_board)

    # Draw grid
    board_size = normalized_board.shape[:2]
    square_size = (board_size[1] / 8, board_size[0] / 8)

    for i in range(9):
        # Vertical lines
        axs[1, 1].axvline(x=i*square_size[0], color='w', linewidth=1)
        # Horizontal lines
        axs[1, 1].axhline(y=i*square_size[1], color='w', linewidth=1)

    # Add coordinates
    for i in range(8):
        # File labels (a-h)
        axs[1, 1].text((i+0.5)*square_size[0], board_size[0]-10, chr(ord('a')+i),
                      ha='center', va='bottom', color='w', fontsize=10)
        # Rank labels (1-8)
        axs[1, 1].text(10, (i+0.5)*square_size[1], str(8-i),
                      ha='left', va='center', color='w', fontsize=10)

    # Add piece labels
    for coord, piece_info in chess_pieces.items():
        file_letter = coord[0]
        rank_number = int(coord[1])

        file_idx = ord(file_letter) - ord('a')
        rank_idx = 8 - rank_number

        center_x = (file_idx + 0.5) * square_size[0]
        center_y = (rank_idx + 0.5) * square_size[1]

        piece_name = piece_info['piece']
        piece_color = 'white' if 'white' in piece_name else 'black'
        piece_type = piece_name.split('_')[1]

        # Add piece label
        axs[1, 1].text(center_x, center_y, piece_type[0].upper() if piece_color == 'white' else piece_type[0].lower(),
                      ha='center', va='center', color='w' if piece_color == 'black' else 'k',
                      fontsize=16, fontweight='bold',
                      bbox=dict(facecolor='k' if piece_color == 'white' else 'w', alpha=0.7, boxstyle='round'))

    axs[1, 1].set_title(f'Chess Position')
    axs[1, 1].axis('off')

    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

def test_piece_detection(segmentation_model, yolo_model, image_path, output_dir, approach='standard', corner_model=None, ground_truth_file=None):
    """
    Test chess piece detection on a real chess board image.

    Args:
        segmentation_model: The segmentation model
        yolo_model: The YOLO model
        image_path: Path to the input image
        output_dir: Directory to save the output
        approach: Detection approach ('standard', 'two_stage', or 'confusion_matrix')
        corner_model: The corner detection model (required for two_stage approach)
        ground_truth_file: Path to ground truth annotations (required for confusion_matrix approach)
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    # Start timing
    start_time = time.time()

    base_name = os.path.splitext(os.path.basename(image_path))[0]

    if approach == 'standard':
        # Standard approach: Detect board with segmentation, then detect all pieces with YOLO
        normalized_board, corners, preprocess_info = detect_board(segmentation_model, image_path)

        if normalized_board is None:
            print(f"No chess board detected in {image_path}")
            return

        # Detect pieces
        detections = detect_pieces(yolo_model, normalized_board)

        # Map to chess coordinates
        chess_pieces = map_to_chess_coordinates(detections)

        # Calculate total time
        total_time = time.time() - start_time

        # Create output path
        output_path = os.path.join(output_dir, f"{base_name}_detection.png")

        # Visualize detection
        visualize_detection(image_path, normalized_board, detections, chess_pieces, output_path)

        # Print results
        print(f"Chess board and pieces detected in {total_time:.3f}s")
        print(f"Detected {len(detections)} pieces")
        print(f"Visualization saved to {output_path}")

        # Print chess pieces
        print("\nDetected pieces:")
        for coord, piece_info in sorted(chess_pieces.items()):
            print(f"{coord}: {piece_info['piece']} ({piece_info['confidence']:.2f})")

    elif approach == 'two_stage':
        # Two-stage approach: Detect board, then classify each square individually
        if corner_model is None:
            print("Corner detection model is required for two-stage approach")
            return

        # Initialize two-stage detector
        detector = TwoStageChessDetection(
            board_model_path=None,  # We'll pass the model directly
            piece_model_path=None   # We'll pass the model directly
        )

        # Set models directly
        detector.board_model = corner_model
        detector.piece_classifier.model = yolo_model

        # Load image
        image = cv2.imread(image_path)
        if image is None:
            print(f"Could not load image from {image_path}")
            return

        # Detect board
        print("Detecting chess board...")
        corners, normalized_board, homography = detector.detect_board(image)

        # Detect pieces
        print("Detecting chess pieces...")
        piece_map, square_images = detector.detect_pieces(normalized_board)

        # Visualize results
        output_path = os.path.join(output_dir, f"{base_name}_two_stage.png")
        print(f"Saving results to {output_path}")
        detector.visualize_results(image, corners, normalized_board, piece_map, square_images, output_path)

        # Calculate total time
        total_time = time.time() - start_time

        # Print results
        print(f"Two-stage detection completed in {total_time:.3f}s")
        print(f"Detected {len(piece_map)} pieces")
        print(f"Visualization saved to {output_path}")

        # Print detected pieces
        print("\nDetected pieces:")
        for coord, info in sorted(piece_map.items()):
            print(f"{coord}: {info['piece']} (confidence: {info['confidence']:.4f})")

    elif approach == 'confusion_matrix':
        # Confusion matrix analysis: Generate a confusion matrix to analyze detection errors
        if ground_truth_file is None:
            print("Ground truth file is required for confusion matrix analysis")
            return

        # Load ground truth
        try:
            with open(ground_truth_file, 'r') as f:
                ground_truth_data = json.load(f)
        except Exception as e:
            print(f"Error loading ground truth file: {e}")
            return

        # Find ground truth for this image
        image_basename = os.path.basename(image_path)
        ground_truth = None

        for item in ground_truth_data:
            if os.path.basename(item.get('image_path', '')) == image_basename:
                ground_truth = {}
                for piece in item.get('pieces', []):
                    coord = piece.get('coord', '')
                    piece_type = piece.get('type', '')
                    color = piece.get('color', '')

                    if coord and piece_type and color:
                        class_name = f"{color}_{piece_type}"
                        ground_truth[coord] = class_name
                break

        if ground_truth is None:
            print(f"No ground truth found for {image_path}")
            return

        # Detect board
        normalized_board, corners, preprocess_info = detect_board(segmentation_model, image_path)

        if normalized_board is None:
            print(f"No chess board detected in {image_path}")
            return

        # Detect pieces
        detections = detect_pieces(yolo_model, normalized_board)

        # Map to chess coordinates
        chess_pieces = map_to_chess_coordinates(detections)

        # Convert to format expected by confusion matrix function
        detections_dict = {}
        for coord, info in chess_pieces.items():
            detections_dict[coord] = {
                'piece': info['piece'],
                'confidence': info['confidence']
            }

        # Define class names
        class_names = [
            'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
            'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
        ]

        # Generate confusion matrix
        cm, y_true, y_pred, all_classes = generate_confusion_matrix(ground_truth, detections_dict, class_names)

        # Visualize confusion matrix
        output_cm_path = os.path.join(output_dir, f"{base_name}_confusion_matrix.png")
        visualize_confusion_matrix(cm, all_classes, output_cm_path)

        # Also visualize standard detection
        output_path = os.path.join(output_dir, f"{base_name}_detection.png")
        visualize_detection(image_path, normalized_board, detections, chess_pieces, output_path)

        # Calculate total time
        total_time = time.time() - start_time

        # Print results
        print(f"Confusion matrix analysis completed in {total_time:.3f}s")
        print(f"Confusion matrix saved to {output_cm_path}")
        print(f"Detection visualization saved to {output_path}")

        # Calculate accuracy
        accuracy = np.sum(np.diag(cm)) / np.sum(cm)
        print(f"\nOverall accuracy: {accuracy:.4f}")

        # Calculate per-class metrics
        print("\nPer-class metrics:")
        for i, class_name in enumerate(all_classes):
            if np.sum(cm[i, :]) > 0:
                precision = cm[i, i] / np.sum(cm[:, i]) if np.sum(cm[:, i]) > 0 else 0
                recall = cm[i, i] / np.sum(cm[i, :])
                f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
                print(f"{class_name}: Precision={precision:.4f}, Recall={recall:.4f}, F1={f1:.4f}")

    else:
        print(f"Unknown approach: {approach}")
        print("Supported approaches: 'standard', 'two_stage', 'confusion_matrix'")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Test chess piece detection')
    parser.add_argument('--segmentation_model', type=str, default='chess_board_detection/models/segmentation_only/tiny_20250519_091307/best_model_dice.pth',
                        help='Path to segmentation model')
    parser.add_argument('--corner_model', type=str, default='chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_combined.pth',
                        help='Path to corner detection model')
    parser.add_argument('--yolo_model', type=str, required=True,
                        help='Path to YOLO model')
    parser.add_argument('--input', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/outputs/piece_detection_test',
                        help='Directory to save the output')
    parser.add_argument('--confidence', type=float, default=0.25,
                        help='Detection confidence threshold')
    parser.add_argument('--approach', type=str, default='standard',
                        choices=['standard', 'two_stage', 'confusion_matrix'],
                        help='Detection approach')
    parser.add_argument('--ground_truth', type=str, default=None,
                        help='Path to ground truth annotations (required for confusion_matrix approach)')
    args = parser.parse_args()

    # Load models
    print(f"Loading segmentation model from {args.segmentation_model}")
    segmentation_model = load_segmentation_model(args.segmentation_model)

    corner_model = None
    if args.approach == 'two_stage':
        print(f"Loading corner detection model from {args.corner_model}")
        corner_model = load_corner_detection_model(args.corner_model)

    print(f"Loading YOLO model from {args.yolo_model}")
    yolo_model = YOLO(args.yolo_model)

    # Test piece detection
    test_piece_detection(
        segmentation_model=segmentation_model,
        yolo_model=yolo_model,
        image_path=args.input,
        output_dir=args.output_dir,
        approach=args.approach,
        corner_model=corner_model,
        ground_truth_file=args.ground_truth
    )

if __name__ == "__main__":
    main()
