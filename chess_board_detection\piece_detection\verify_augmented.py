"""
Verify the augmented dataset by visualizing images and annotations.
"""

import os
import cv2
import random
import numpy as np
import argparse
from pathlib import Path

def visualize_annotations(img_path, label_path, output_path=None):
    """Visualize annotations on an image."""
    # Read image
    img = cv2.imread(img_path)
    if img is None:
        print(f"Error: Could not read image {img_path}")
        return
    
    # Convert to RGB for visualization
    img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    h, w = img_rgb.shape[:2]
    
    print(f"Image: {os.path.basename(img_path)}")
    print(f"Size: {w}x{h}")
    
    # Class names for visualization
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]
    
    # Colors for different classes (BGR format)
    colors = [
        (0, 255, 0),    # white_pawn: green
        (0, 255, 255),  # white_knight: yellow
        (255, 0, 0),    # white_bishop: blue
        (255, 0, 255),  # white_rook: magenta
        (0, 0, 255),    # white_queen: red
        (255, 255, 0),  # white_king: cyan
        (0, 200, 0),    # black_pawn: dark green
        (0, 200, 200),  # black_knight: dark yellow
        (200, 0, 0),    # black_bishop: dark blue
        (200, 0, 200),  # black_rook: dark magenta
        (0, 0, 200),    # black_queen: dark red
        (200, 200, 0),  # black_king: dark cyan
    ]
    
    # Read annotations
    print("Annotations:")
    with open(label_path, 'r') as f:
        for line in f:
            parts = line.strip().split()
            if len(parts) >= 5:
                class_id, x_center, y_center, width, height = map(float, parts)
                class_id = int(class_id)
                
                # Convert YOLO format to pixel coordinates
                x1 = int((x_center - width/2) * w)
                y1 = int((y_center - height/2) * h)
                x2 = int((x_center + width/2) * w)
                y2 = int((y_center + height/2) * h)
                
                # Ensure coordinates are within image bounds
                x1 = max(0, min(x1, w-1))
                y1 = max(0, min(y1, h-1))
                x2 = max(0, min(x2, w-1))
                y2 = max(0, min(y2, h-1))
                
                # Print annotation info
                class_name = class_names[class_id] if class_id < len(class_names) else f"Unknown-{class_id}"
                print(f"Class {class_id} ({class_name}): ({x1}, {y1}, {x2}, {y2})")
                
                # Draw bounding box
                color = colors[class_id % len(colors)]
                cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
                
                # Draw label
                label = f"{class_name}"
                (label_width, label_height), _ = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 1)
                cv2.rectangle(img, (x1, y1-label_height-5), (x1+label_width, y1), color, -1)
                cv2.putText(img, label, (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    # Save or display the image
    if output_path:
        cv2.imwrite(output_path, img)
        print(f"Annotated image saved to {output_path}")
    
    return img

def verify_dataset(dataset_dir, num_samples=5, output_dir=None):
    """Verify multiple samples from the dataset."""
    # Create output directory if needed
    if output_dir:
        os.makedirs(output_dir, exist_ok=True)
    
    # Get training and validation image directories
    train_img_dir = os.path.join(dataset_dir, 'images', 'train')
    val_img_dir = os.path.join(dataset_dir, 'images', 'val')
    train_label_dir = os.path.join(dataset_dir, 'labels', 'train')
    val_label_dir = os.path.join(dataset_dir, 'labels', 'val')
    
    # Verify training samples
    print(f"\nVerifying {num_samples} training samples:")
    train_img_files = list(Path(train_img_dir).glob('*.jpg'))
    train_samples = random.sample(train_img_files, min(num_samples, len(train_img_files)))
    
    for img_path in train_samples:
        label_path = os.path.join(train_label_dir, f"{img_path.stem}.txt")
        if not os.path.exists(label_path):
            print(f"Warning: No label file found for {img_path.name}")
            continue
        
        if output_dir:
            output_path = os.path.join(output_dir, f"train_{img_path.stem}.jpg")
        else:
            output_path = None
        
        visualize_annotations(str(img_path), label_path, output_path)
        print()
    
    # Verify validation samples
    print(f"\nVerifying {num_samples} validation samples:")
    val_img_files = list(Path(val_img_dir).glob('*.jpg'))
    val_samples = random.sample(val_img_files, min(num_samples, len(val_img_files)))
    
    for img_path in val_samples:
        label_path = os.path.join(val_label_dir, f"{img_path.stem}.txt")
        if not os.path.exists(label_path):
            print(f"Warning: No label file found for {img_path.name}")
            continue
        
        if output_dir:
            output_path = os.path.join(output_dir, f"val_{img_path.stem}.jpg")
        else:
            output_path = None
        
        visualize_annotations(str(img_path), label_path, output_path)
        print()
    
    # Print dataset statistics
    print("\nDataset Statistics:")
    print(f"Training images: {len(list(Path(train_img_dir).glob('*.jpg')))}")
    print(f"Validation images: {len(list(Path(val_img_dir).glob('*.jpg')))}")
    
    # Check image sizes
    train_img = cv2.imread(str(train_img_files[0]))
    val_img = cv2.imread(str(val_img_files[0]))
    print(f"Training image size: {train_img.shape[1]}x{train_img.shape[0]}")
    print(f"Validation image size: {val_img.shape[1]}x{val_img.shape[0]}")

def main():
    parser = argparse.ArgumentParser(description="Verify augmented dataset")
    parser.add_argument("--dataset_dir", type=str, default="chess_board_detection/piece_detection/augmented_dataset_416x416", 
                        help="Directory containing the augmented dataset")
    parser.add_argument("--num_samples", type=int, default=3, 
                        help="Number of samples to verify from each split")
    parser.add_argument("--output_dir", type=str, default="chess_board_detection/piece_detection/verification", 
                        help="Directory to save visualized samples")
    
    args = parser.parse_args()
    verify_dataset(args.dataset_dir, args.num_samples, args.output_dir)

if __name__ == "__main__":
    main()
