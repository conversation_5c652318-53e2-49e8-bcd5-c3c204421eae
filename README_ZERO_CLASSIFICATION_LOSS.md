# Zero Classification Loss Fine-tuning for Chess Piece Detection

This tool fine-tunes a YOLO model with human or automated feedback, focusing specifically on reducing classification loss to zero while maintaining high detection performance metrics. It uses an advanced dynamic weight adjustment strategy that directly targets classification loss while preserving mAP50, precision, and recall.

## Features

- **Zero Classification Loss Focus**: Specifically designed to drive classification loss to zero without degrading other metrics
- **Classification-focused fine-tuning**: Uses extremely high weights for classification loss to prioritize correct piece identification
- **Performance-preserving weight adjustment**: Dynamically adjusts loss weights based on classification loss while monitoring other metrics
- **Automated feedback**: Automatically compares model predictions with existing labels using nearest corner matching
- **Human feedback loop**: Collects manual feedback on misclassifications and uses it to fine-tune the model
- **Interactive feedback collection**: Simple command-line interface for providing feedback on model predictions
- **Automatic training data preparation**: Converts feedback into training data for fine-tuning
- **Multiple operation modes**: Supports feedback collection, fine-tuning, and complete feedback loop
- **Comprehensive visualization**: Generates detailed charts tracking classification loss, weights, and performance metrics

## Requirements

- Python 3.8+
- PyTorch 2.0+
- Ultralytics YOLO v8+
- OpenCV
- NumPy
- Matplotlib

## Usage

### 1. Collecting Feedback on a Single Image

```bash
python zero_classification_loss_finetune.py feedback --model "path/to/model.pt" --image "path/to/image.jpg"
```

This will:
- Run inference on the image
- Display the detections with numbers
- Allow you to provide feedback on incorrect classifications
- Save the feedback to the database

### 2. Fine-tuning with Collected Feedback

```bash
python zero_classification_loss_finetune.py finetune --model "path/to/model.pt" --feedback "feedback_database.json"
```

This will:
- Prepare training data from the feedback
- Fine-tune the model with a focus on classification accuracy
- Save the fine-tuned model

### 3. Running a Complete Feedback Loop

#### Manual Feedback Loop

```bash
python zero_classification_loss_finetune.py loop --model "path/to/model.pt" --images "path/to/test/images"
```

This will:
- Process each image in the test directory
- Collect manual feedback on misclassifications
- Fine-tune the model when enough feedback is collected
- Continue the loop until all images are processed

#### Automated Feedback Loop

```bash
python zero_classification_loss_finetune.py loop --model "path/to/model.pt" --images "path/to/test/images" --training-data "path/to/training/data" --automated
```

This will:
- Compare model predictions with existing labels in the training data
- Automatically collect feedback on misclassifications
- Fine-tune the model with the collected feedback
- Produce a model with improved classification accuracy

Alternatively, you can use the provided batch file:

```bash
run_zero_classification_finetune.bat
```

## Command-line Arguments

### Feedback Collection Mode

```
--model       Path to the YOLO model
--image       Path to the image to analyze
--db          Path to the feedback database (default: feedback_database.json)
--output      Directory to save output images (default: feedback_output)
```

### Fine-tuning Mode

```
--model       Path to the base YOLO model
--feedback    Path to the feedback database
--epochs      Number of training epochs (default: 15)
--batch       Batch size (default: 16)
--data-dir    Directory to store training data (default: feedback_training_data)
--output-dir  Directory to save training results (default: runs/zero_cls_loss)
--cls-weight  Weight for classification loss (default: 10.0)
```

### Feedback Loop Mode

```
--model             Path to the base YOLO model
--images            Directory with test images
--training-data     Directory with existing training data (optional)
--automated         Use automated feedback from existing labels (flag)
--epochs            Number of training epochs (default: 15)
--batch             Batch size (default: 16)
--min-feedback      Minimum feedback items before fine-tuning (default: 3)
--output-dir        Directory to save results (default: runs/zero_cls_loss)
--db                Path to the feedback database (default: feedback_database.json)
--cls-weight        Initial weight for classification loss (default: 10.0)
--dynamic-weights   Enable dynamic weight adjustment (flag)
--target-precision  Target precision to achieve (default: 0.99)
--target-recall     Target recall to achieve (default: 0.99)
```

## How It Works

### Manual Feedback Mode

1. **Manual Feedback Collection**:
   - The model makes predictions on an image
   - You identify misclassifications and provide the correct class
   - The feedback is stored in a JSON database

2. **Training Data Preparation**:
   - Feedback is converted to YOLO format training data
   - Images and labels are organized in a dataset structure
   - A dataset YAML file is created

3. **Classification-Focused Fine-tuning**:
   - The model is fine-tuned with a very high weight for classification loss
   - Minimal augmentation is used to focus on the specific corrections
   - Very low learning rates are used for stable fine-tuning

4. **Feedback Loop**:
   - The process repeats until perfect classification is achieved
   - When perfect classification is reached, the feedback database is reset

### Automated Feedback Mode

1. **Automated Feedback Collection**:
   - The model makes predictions on images from the training data
   - Predictions are matched with existing labels using nearest corner distance
   - The system finds the closest ground truth box for each prediction (within 50px threshold)
   - Misclassifications (nearby position but wrong class) are identified automatically
   - The feedback is stored in a JSON database

2. **Training Data Preparation**:
   - Automated feedback is converted to YOLO format training data
   - Images and labels are organized in a dataset structure
   - A dataset YAML file is created

3. **Classification-Focused Fine-tuning**:
   - The model is fine-tuned with a very high weight for classification loss
   - Minimal augmentation is used to focus on the specific corrections
   - Very low learning rates are used for stable fine-tuning

4. **Evaluation**:
   - The fine-tuned model is evaluated on validation data
   - If perfect classification is achieved, the process is complete

### Dynamic Weight Adjustment Mode

1. **Initial Training**:
   - The model is trained with initial loss weights
   - Classification loss and performance metrics (precision, recall, mAP50) are measured

2. **Classification Loss-Based Weight Adjustment**:
   - The system directly analyzes the classification loss value
   - Loss weights are automatically adjusted based on classification loss:
     - High loss (>0.1): Classification weight is tripled (up to 100), other weights maintained
     - Medium loss (>0.05): Classification weight is doubled (up to 80), other weights reduced by 10%
     - Low loss (>0.01): Classification weight is increased by 50% (up to 60), other weights reduced by 5%
     - Very low loss (<0.01): Classification weight maintained, other weights slightly increased

3. **Performance Monitoring**:
   - The system continuously monitors precision and recall
   - If performance degrades (>2% drop), weights are adjusted to compensate:
     - Box and DFL weights are increased by 20%
     - Classification weight is slightly reduced while keeping it high

4. **Iterative Optimization**:
   - The process repeats for multiple iterations
   - Each iteration uses the adjusted weights from the previous round
   - All weight adjustments, classification loss, and performance metrics are tracked

5. **Comprehensive Visualization**:
   - Classification loss is tracked and visualized over iterations
   - Weight changes are plotted to show the optimization strategy
   - Performance metrics are tracked to ensure they remain high

6. **Best Model Selection**:
   - The best performing model across all iterations is selected
   - The process stops early if classification loss drops below 0.01 while maintaining target metrics

## Tips for Best Results

1. **Focus on Misclassifications**: Only provide feedback for pieces that are incorrectly classified, not for detection issues.
2. **Provide Multiple Examples**: Include feedback for the same piece type in different positions and lighting conditions.
3. **Start with the Most Common Errors**: Focus first on the most frequently misclassified pieces.
4. **Use Clear Images**: For initial feedback, use clear images with good lighting and minimal occlusion.
5. **Gradually Increase Difficulty**: Once basic classification is working well, introduce more challenging images.

## Customization

You can adjust the following parameters to customize the fine-tuning process:

- **Classification Loss Weight**: Increase for more focus on classification (default: 10.0)
- **Epochs**: More epochs for more thorough training (default: 15)
- **Minimum Feedback**: Number of feedback items needed before fine-tuning (default: 3)
- **Batch Size**: Adjust based on your GPU memory (default: 16)
