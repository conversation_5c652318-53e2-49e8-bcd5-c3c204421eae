"""
Dataset class for chess board segmentation using the existing augmented dataset.
Uses only the image.pt and mask.pt files from the augmented dataset.
"""

import os
import torch
from torch.utils.data import Dataset, DataLoader
import numpy as np
from pathlib import Path
import glob
from sklearn.model_selection import train_test_split

class AugmentedSegmentationDataset(Dataset):
    """
    Dataset for chess board segmentation using pre-augmented data.
    """
    
    def __init__(self, dataset_dir, sample_folders=None, transform=None):
        """
        Args:
            dataset_dir: Directory containing augmented samples
            sample_folders: List of sample folder names to use (if None, uses all)
            transform: Optional transform to apply
        """
        self.dataset_dir = Path(dataset_dir)
        self.transform = transform
        
        # Find all sample folders
        if sample_folders is None:
            self.sample_folders = sorted([
                d for d in self.dataset_dir.iterdir() 
                if d.is_dir() and d.name.startswith('sample_')
            ])
        else:
            self.sample_folders = [self.dataset_dir / folder for folder in sample_folders]
        
        # Filter to only include folders with both image.pt and mask.pt
        valid_folders = []
        for folder in self.sample_folders:
            image_path = folder / "image.pt"
            mask_path = folder / "mask.pt"
            if image_path.exists() and mask_path.exists():
                valid_folders.append(folder)
        
        self.sample_folders = valid_folders
        print(f"Found {len(self.sample_folders)} valid samples")
    
    def __len__(self):
        return len(self.sample_folders)
    
    def __getitem__(self, idx):
        folder = self.sample_folders[idx]
        
        # Load image and mask tensors
        image_path = folder / "image.pt"
        mask_path = folder / "mask.pt"
        
        try:
            # Load tensors
            image = torch.load(image_path, map_location='cpu')
            mask = torch.load(mask_path, map_location='cpu')
            
            # Ensure correct data types and shapes
            image = image.float()
            mask = mask.float()
            
            # Ensure image is in CHW format
            if image.dim() == 3 and image.shape[0] != 3:
                # If image is HWC, convert to CHW
                if image.shape[2] == 3:
                    image = image.permute(2, 0, 1)
            
            # Ensure mask is 2D (HW)
            if mask.dim() == 3:
                mask = mask.squeeze()
            
            # Normalize image to [0, 1] if needed
            if image.max() > 1.0:
                image = image / 255.0
            
            # Ensure mask is binary [0, 1]
            if mask.max() > 1.0:
                mask = mask / 255.0
            
            # Apply transforms if provided
            if self.transform:
                # Convert to numpy for albumentations
                image_np = image.permute(1, 2, 0).numpy()
                mask_np = mask.numpy()
                
                transformed = self.transform(image=image_np, mask=mask_np)
                image = transformed['image']
                mask = transformed['mask']
            
            return image, mask
            
        except Exception as e:
            print(f"Error loading sample {folder.name}: {e}")
            # Return a dummy sample
            return torch.zeros(3, 256, 256), torch.zeros(256, 256)

def create_augmented_dataloaders(
    dataset_dir,
    batch_size=8,
    train_split=0.8,
    num_workers=0,
    transform_train=None,
    transform_val=None
):
    """
    Create training and validation dataloaders from augmented dataset.
    
    Args:
        dataset_dir: Directory containing augmented samples
        batch_size: Batch size for training
        train_split: Fraction of data for training
        num_workers: Number of worker processes
        transform_train: Transform for training data
        transform_val: Transform for validation data
    """
    dataset_path = Path(dataset_dir)
    
    # Get all sample folders
    all_folders = sorted([
        d.name for d in dataset_path.iterdir() 
        if d.is_dir() and d.name.startswith('sample_')
    ])
    
    print(f"Found {len(all_folders)} total sample folders")
    
    # Split into train/val
    train_folders, val_folders = train_test_split(
        all_folders, 
        train_size=train_split, 
        random_state=42
    )
    
    print(f"Train samples: {len(train_folders)}")
    print(f"Val samples: {len(val_folders)}")
    
    # Create datasets
    train_dataset = AugmentedSegmentationDataset(
        dataset_dir, train_folders, transform_train
    )
    
    val_dataset = AugmentedSegmentationDataset(
        dataset_dir, val_folders, transform_val
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True if torch.cuda.is_available() else False
    )
    
    return train_loader, val_loader

def visualize_augmented_sample(dataset, idx=0, save_path=None):
    """Visualize a sample from the augmented dataset."""
    import matplotlib.pyplot as plt
    
    image, mask = dataset[idx]
    
    # Convert tensor to numpy for visualization
    if isinstance(image, torch.Tensor):
        if image.shape[0] == 3:  # CHW format
            image = image.permute(1, 2, 0).numpy()
        else:
            image = image.numpy()
    
    if isinstance(mask, torch.Tensor):
        mask = mask.numpy()
    
    # Ensure image is in [0, 1] range
    image = np.clip(image, 0, 1)
    
    # Create visualization
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(image)
    axes[0].set_title('Augmented Image')
    axes[0].axis('off')
    
    axes[1].imshow(mask, cmap='gray')
    axes[1].set_title('Segmentation Mask')
    axes[1].axis('off')
    
    # Overlay
    overlay = image.copy()
    if len(overlay.shape) == 3:
        overlay[:, :, 0] = np.where(mask > 0.5, 1.0, overlay[:, :, 0])
    axes[2].imshow(overlay)
    axes[2].set_title('Overlay')
    axes[2].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"Visualization saved to {save_path}")
    else:
        plt.show()
    
    plt.close()

def test_dataset_loading():
    """Test loading the augmented dataset."""
    DATASET_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326"
    
    print("Testing augmented dataset loading...")
    
    # Create dataset
    dataset = AugmentedSegmentationDataset(DATASET_DIR)
    print(f"Dataset size: {len(dataset)}")
    
    if len(dataset) > 0:
        # Test loading a sample
        try:
            image, mask = dataset[0]
            print(f"Sample 0 - Image shape: {image.shape}, Mask shape: {mask.shape}")
            print(f"Image dtype: {image.dtype}, range: [{image.min():.3f}, {image.max():.3f}]")
            print(f"Mask dtype: {mask.dtype}, range: [{mask.min():.3f}, {mask.max():.3f}]")
            
            # Visualize
            os.makedirs("chess_board_detection/augmented_dataset_test", exist_ok=True)
            visualize_augmented_sample(
                dataset, 
                idx=0, 
                save_path="chess_board_detection/augmented_dataset_test/sample_0.png"
            )
            
            # Test dataloader
            train_loader, val_loader = create_augmented_dataloaders(
                DATASET_DIR, 
                batch_size=4, 
                train_split=0.8
            )
            
            # Test batch loading
            for batch_idx, (images, masks) in enumerate(train_loader):
                print(f"Batch {batch_idx}: Images {images.shape}, Masks {masks.shape}")
                if batch_idx == 0:
                    break
            
            print("Dataset loading test successful!")
            return True
            
        except Exception as e:
            print(f"Error testing dataset: {e}")
            return False
    else:
        print("No samples found in dataset")
        return False

if __name__ == "__main__":
    test_dataset_loading()
