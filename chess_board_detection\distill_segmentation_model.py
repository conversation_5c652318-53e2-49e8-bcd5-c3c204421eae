"""
Knowledge distillation script for training a smaller segmentation-only model.
Uses the best model (Phase 2 Epoch 16) as the teacher.
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt
from datetime import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models and utilities
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from chess_board_detection.models.segmentation_only_model import SegmentationOnlyModel, TinySegmentationModel
from chess_board_detection.utils.augmented_dataset import AugmentedDataset, create_augmented_dataloaders

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

def load_teacher_model(model_path):
    """Load the teacher model (Phase 2 Epoch 16)."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=device))
    model.eval()  # Set to evaluation mode
    return model

def create_student_model(model_type='small'):
    """Create a student model for segmentation only."""
    if model_type == 'tiny':
        return TinySegmentationModel(n_channels=3)
    else:
        return SegmentationOnlyModel(n_channels=3)

def get_data_loaders(data_dir, batch_size=8):
    """Create data loaders for training and validation."""
    # Use the existing function to create dataloaders from augmented data
    train_loader, val_loader = create_augmented_dataloaders(
        augmented_data_dir=data_dir,
        batch_size=batch_size,
        val_split=0.2,
        filter_valid=True,
        shuffle=True
    )

    return train_loader, val_loader

class DistillationLoss(nn.Module):
    """
    Loss function for knowledge distillation.
    Combines:
    1. Hard loss (BCE) between student predictions and ground truth
    2. Soft loss (KL divergence) between student and teacher predictions
    """
    def __init__(self, temperature=2.0, alpha=0.5):
        super(DistillationLoss, self).__init__()
        self.temperature = temperature
        self.alpha = alpha  # Weight for soft loss
        self.bce_loss = nn.BCEWithLogitsLoss()
        self.kl_loss = nn.KLDivLoss(reduction='batchmean')

    def forward(self, student_logits, teacher_probs, targets):
        # Hard loss: BCE between student predictions and ground truth
        hard_loss = self.bce_loss(student_logits, targets)

        # Soft loss: KL divergence between student and teacher predictions
        # Apply temperature scaling
        soft_student = torch.sigmoid(student_logits / self.temperature)
        soft_teacher = teacher_probs.detach()  # Detach to prevent gradient flow to teacher

        # Apply log to student predictions for KL divergence
        log_soft_student = torch.log(soft_student + 1e-8)  # Add small epsilon to prevent log(0)

        soft_loss = self.kl_loss(log_soft_student, soft_teacher)

        # Combine losses
        total_loss = (1 - self.alpha) * hard_loss + self.alpha * soft_loss * (self.temperature ** 2)

        return total_loss, hard_loss, soft_loss

def train_epoch(teacher_model, student_model, train_loader, optimizer, criterion, device):
    """Train for one epoch."""
    student_model.train()
    teacher_model.eval()

    running_loss = 0.0
    running_hard_loss = 0.0
    running_soft_loss = 0.0

    for batch in tqdm(train_loader, desc="Training"):
        # Get data
        images = batch['image'].to(device)
        masks = batch['mask'].to(device)

        # Ensure masks have the right shape (B, 1, H, W)
        if masks.dim() == 3:
            masks = masks.unsqueeze(1)

        # Zero the parameter gradients
        optimizer.zero_grad()

        # Forward pass through teacher model (no gradient)
        with torch.no_grad():
            teacher_outputs = teacher_model(images)
            teacher_segmentation = torch.sigmoid(teacher_outputs['segmentation'])

        # Forward pass through student model
        student_outputs = student_model(images)
        student_segmentation = student_outputs['segmentation']

        # Calculate loss
        loss, hard_loss, soft_loss = criterion(
            student_segmentation,
            teacher_segmentation,
            masks
        )

        # Backward pass and optimize
        loss.backward()
        optimizer.step()

        # Update statistics
        running_loss += loss.item()
        running_hard_loss += hard_loss.item()
        running_soft_loss += soft_loss.item()

    # Calculate average losses
    epoch_loss = running_loss / len(train_loader)
    epoch_hard_loss = running_hard_loss / len(train_loader)
    epoch_soft_loss = running_soft_loss / len(train_loader)

    return epoch_loss, epoch_hard_loss, epoch_soft_loss

def validate(student_model, val_loader, device):
    """Validate the model."""
    student_model.eval()

    val_iou = 0.0
    val_dice = 0.0

    with torch.no_grad():
        for batch in tqdm(val_loader, desc="Validation"):
            # Get data
            images = batch['image'].to(device)
            masks = batch['mask'].to(device)

            # Ensure masks have the right shape (B, 1, H, W)
            if masks.dim() == 3:
                masks = masks.unsqueeze(1)

            # Forward pass
            outputs = student_model(images)
            preds = torch.sigmoid(outputs['segmentation']) > 0.5

            # Calculate metrics
            iou = calculate_iou(preds, masks > 0.5)
            dice = calculate_dice(preds, masks > 0.5)

            val_iou += iou
            val_dice += dice

    # Calculate average metrics
    val_iou /= len(val_loader)
    val_dice /= len(val_loader)

    return val_iou, val_dice

def calculate_iou(preds, targets):
    """Calculate IoU (Intersection over Union)."""
    intersection = (preds & targets).float().sum((1, 2, 3))
    union = (preds | targets).float().sum((1, 2, 3))
    iou = (intersection + 1e-6) / (union + 1e-6)
    return iou.mean().item()

def calculate_dice(preds, targets):
    """Calculate Dice coefficient."""
    intersection = (preds & targets).float().sum((1, 2, 3))
    union = preds.float().sum((1, 2, 3)) + targets.float().sum((1, 2, 3))
    dice = (2 * intersection + 1e-6) / (union + 1e-6)
    return dice.mean().item()

def save_model(model, path):
    """Save model to disk."""
    torch.save(model.state_dict(), path)

def plot_training_history(history, output_dir):
    """Plot training history."""
    # Create figure with multiple subplots
    fig, axs = plt.subplots(2, 2, figsize=(15, 10))

    # Plot loss
    axs[0, 0].plot(history['train_loss'], label='Train Loss')
    axs[0, 0].set_title('Training Loss')
    axs[0, 0].set_xlabel('Epoch')
    axs[0, 0].set_ylabel('Loss')
    axs[0, 0].legend()

    # Plot hard and soft loss
    axs[0, 1].plot(history['hard_loss'], label='Hard Loss')
    axs[0, 1].plot(history['soft_loss'], label='Soft Loss')
    axs[0, 1].set_title('Hard vs Soft Loss')
    axs[0, 1].set_xlabel('Epoch')
    axs[0, 1].set_ylabel('Loss')
    axs[0, 1].legend()

    # Plot IoU
    axs[1, 0].plot(history['val_iou'], label='Validation IoU')
    axs[1, 0].set_title('Validation IoU')
    axs[1, 0].set_xlabel('Epoch')
    axs[1, 0].set_ylabel('IoU')
    axs[1, 0].legend()

    # Plot Dice
    axs[1, 1].plot(history['val_dice'], label='Validation Dice')
    axs[1, 1].set_title('Validation Dice')
    axs[1, 1].set_xlabel('Epoch')
    axs[1, 1].set_ylabel('Dice')
    axs[1, 1].legend()

    # Save figure
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'training_history.png'))
    plt.close()

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Knowledge distillation for segmentation model')
    parser.add_argument('--teacher_model', type=str, default='chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth',
                        help='Path to teacher model')
    parser.add_argument('--data_dir', type=str, default='chess_board_detection/data/augmented/v5.2/augmented_20250518_095549',
                        help='Path to data directory')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/models/segmentation_only',
                        help='Path to output directory')
    parser.add_argument('--model_type', type=str, default='small', choices=['small', 'tiny'],
                        help='Type of student model to use')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--temperature', type=float, default=2.0, help='Temperature for distillation')
    parser.add_argument('--alpha', type=float, default=0.5, help='Weight for soft loss')
    args = parser.parse_args()

    # Create output directory
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    output_dir = os.path.join(args.output_dir, f'{args.model_type}_{timestamp}')
    os.makedirs(output_dir, exist_ok=True)

    # Load teacher model
    print(f"Loading teacher model from {args.teacher_model}")
    teacher_model = load_teacher_model(args.teacher_model).to(device)

    # Create student model
    print(f"Creating {args.model_type} student model")
    student_model = create_student_model(args.model_type).to(device)

    # Print model sizes
    teacher_params = sum(p.numel() for p in teacher_model.parameters() if p.requires_grad)
    student_params = sum(p.numel() for p in student_model.parameters() if p.requires_grad)
    print(f"Teacher model parameters: {teacher_params:,}")
    print(f"Student model parameters: {student_params:,}")
    print(f"Size reduction: {teacher_params / student_params:.2f}x")

    # Get data loaders
    print(f"Loading data from {args.data_dir}")
    train_loader, val_loader = get_data_loaders(args.data_dir, args.batch_size)

    # Create optimizer and loss function
    optimizer = optim.Adam(student_model.parameters(), lr=args.lr)
    criterion = DistillationLoss(temperature=args.temperature, alpha=args.alpha)

    # Training history
    history = {
        'train_loss': [],
        'hard_loss': [],
        'soft_loss': [],
        'val_iou': [],
        'val_dice': []
    }

    # Best model tracking
    best_iou = 0.0
    best_dice = 0.0

    # Train the model
    print(f"Starting training for {args.epochs} epochs")
    for epoch in range(args.epochs):
        print(f"Epoch {epoch+1}/{args.epochs}")

        # Train
        train_loss, hard_loss, soft_loss = train_epoch(
            teacher_model, student_model, train_loader, optimizer, criterion, device
        )

        # Validate
        val_iou, val_dice = validate(student_model, val_loader, device)

        # Update history
        history['train_loss'].append(train_loss)
        history['hard_loss'].append(hard_loss)
        history['soft_loss'].append(soft_loss)
        history['val_iou'].append(val_iou)
        history['val_dice'].append(val_dice)

        # Print metrics
        print(f"Train Loss: {train_loss:.4f}, Hard Loss: {hard_loss:.4f}, Soft Loss: {soft_loss:.4f}")
        print(f"Val IoU: {val_iou:.4f}, Val Dice: {val_dice:.4f}")

        # Save best models
        if val_iou > best_iou:
            best_iou = val_iou
            save_model(student_model, os.path.join(output_dir, 'best_model_iou.pth'))
            print(f"Saved new best IoU model: {val_iou:.4f}")

        if val_dice > best_dice:
            best_dice = val_dice
            save_model(student_model, os.path.join(output_dir, 'best_model_dice.pth'))
            print(f"Saved new best Dice model: {val_dice:.4f}")

        # Save latest model
        save_model(student_model, os.path.join(output_dir, 'latest_model.pth'))

    # Save final model
    save_model(student_model, os.path.join(output_dir, 'final_model.pth'))

    # Plot training history
    plot_training_history(history, output_dir)

    print(f"Training completed. Models saved to {output_dir}")
    print(f"Best IoU: {best_iou:.4f}, Best Dice: {best_dice:.4f}")

if __name__ == "__main__":
    main()
