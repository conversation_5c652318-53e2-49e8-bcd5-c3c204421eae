PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> cd "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection"
>> python train_v5_2.py --cpu
E:\New folder\Lib\site-packages\albumentations\__init__.py:28: UserWarning: A new version of Albumentations is available: '2.0.7' (you have '2.0.6'). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.
  check_for_updates()
2025-05-18 20:45:34.802013: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-05-18 20:45:39.183612: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
CUDA not available. Using CPU.
Memory limits: 3GB maximum usage
Using device: cpu
Using pre-augmented dataset from: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326
Loaded 85 augmented samples from C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326
Created dataloaders with 68 training samples and 17 validation samples
Train dataset size: 68
Validation dataset size: 17
Initializing v5.2 model...
Model moved to cpu
Model parameters: 17874848
Trainable parameters: 17874848

=== Phase 1: Balanced foundation with smooth convergence path ===
Saving outputs to v5.2(3rd attempt) folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Skipping model graph visualization in TensorBoard to conserve memory
Generating data augmentation visualizations...
Augmentation visualization directory created at C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(3rd attempt)\augmentations
Generated augmentation visualizations in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(3rd attempt)\augmentations
Epoch 1/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:11<00:00,  7.73s/it] 
train Loss: 219.4647, Seg Loss: 0.5772, Heatmap Loss: 145.6863, Geometric Loss: 0.8950
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 8.9599 🟢 (448.0% of target 2.0)
  avg_peak_to_mean_ratio: 60.6764
  avg_peak_to_second_ratio: 1.1303
  detection_rate: 0.9081 🟡 (90.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0014
  p2s_ratio_max: 1.5582
  p2s_ratio_std: 0.1545
  secondary_peak_distance: 68.4468
  primary_peak_sharpness: 1.9199
  primary_peak_isolation: 3.9513

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1324
  Top-Right: 1.1215
  Bottom-Right: 1.1759
  Bottom-Left: 1.0913

Secondary Peak Relative Position: x=6.74, y=-5.68
train Heatmap Components:
  mse_loss: 1.5647
  separation_loss: 2.2544
  peak_separation_loss: 63.6814
  edge_suppression_loss: -0.0209
  peak_enhancement_loss: 3.5060
  peak_to_second_ratio_loss: 1.0971
  avg_peak_to_second_ratio: 1.0983
  detection_rate_loss: 0.0520
  segmentation_guidance_loss: 0.1902
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.35s/it] 
val Loss: 3827.6776, Seg Loss: 0.7690, Heatmap Loss: 2551.0611, Geometric Loss: 0.7916
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 0.6776 🔴 (33.9% of target 2.0)
  avg_peak_to_mean_ratio: 7.8220
  avg_peak_to_second_ratio: 1.5370
  detection_rate: 0.5882 🔴 (58.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0000
  p2s_ratio_max: 8.7481
  p2s_ratio_std: 1.9363
  secondary_peak_distance: 69.6071
  primary_peak_sharpness: 0.1380
  primary_peak_isolation: 4.1602

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.0523
  Top-Right: 2.9904
  Bottom-Right: 1.0506
  Bottom-Left: 1.0547

Secondary Peak Relative Position: x=9.70, y=7.37
val Heatmap Components:
  mse_loss: 0.3315
  separation_loss: 0.4160
  peak_separation_loss: 576.2809
  edge_suppression_loss: -0.0088
  peak_enhancement_loss: 1.7326
  peak_to_second_ratio_loss: 1.3476
  avg_peak_to_second_ratio: 1.0520
  detection_rate_loss: 0.2025
  segmentation_guidance_loss: 0.0015
Current learning rate: 0.000224
New best model saved with loss: 3827.6776
New best model saved with peak-to-second ratio: 1.5370
New best model saved with detection rate: 0.5882
New best model saved with combined score: 0.6946 (loss: 3827.6776, p2s: 1.5370, detection: 0.5882)
Early stopping: New best score: 0.6946

Epoch 2/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:10<00:00,  7.68s/it] 
train Loss: 1314.2365, Seg Loss: 0.5431, Heatmap Loss: 875.5878, Geometric Loss: 0.7790
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.0715 🟢 (203.6% of target 2.0)
  avg_peak_to_mean_ratio: 8.8234
  avg_peak_to_second_ratio: 1.1365
  detection_rate: 0.9044 🟡 (90.4% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0018
  p2s_ratio_max: 1.7654
  p2s_ratio_std: 0.2015
  secondary_peak_distance: 72.6393
  primary_peak_sharpness: 0.8617
  primary_peak_isolation: 4.4091

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1214
  Top-Right: 1.2181
  Bottom-Right: 1.1094
  Bottom-Left: 1.0968

Secondary Peak Relative Position: x=-0.83, y=0.29
train Heatmap Components:
  mse_loss: 1.1207
  separation_loss: 1.7729
  peak_separation_loss: 198.1674
  edge_suppression_loss: -0.0145
  peak_enhancement_loss: 1.7395
  peak_to_second_ratio_loss: 1.2882
  avg_peak_to_second_ratio: 1.1070
  detection_rate_loss: 0.0655
  segmentation_guidance_loss: 0.1593
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.17s/it] 
val Loss: 2720.2864, Seg Loss: 0.6659, Heatmap Loss: 1812.8588, Geometric Loss: 0.8313
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5115 🟢 (125.6% of target 2.0)
  avg_peak_to_mean_ratio: 16.0758
  avg_peak_to_second_ratio: 1.1097
  detection_rate: 0.9559 🟡 (95.6% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0029
  p2s_ratio_max: 1.7011
  p2s_ratio_std: 0.1825
  secondary_peak_distance: 72.0087
  primary_peak_sharpness: 0.6258
  primary_peak_isolation: 6.2828

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1724
  Top-Right: 1.1195
  Bottom-Right: 1.1014
  Bottom-Left: 1.0457

Secondary Peak Relative Position: x=-1.89, y=-18.91
val Heatmap Components:
  mse_loss: 0.6600
  separation_loss: 2.0707
  peak_separation_loss: 423.6535
  edge_suppression_loss: -0.0149
  peak_enhancement_loss: 1.8878
  peak_to_second_ratio_loss: 1.4242
  avg_peak_to_second_ratio: 1.0857
  detection_rate_loss: 0.0138
  segmentation_guidance_loss: 0.0213
Current learning rate: 0.000368
New best model saved with loss: 2720.2864
New best model saved with detection rate: 0.9559
Early stopping: No improvement for 1/15 epochs. Best: 0.6946, Current: 0.6494


Epoch 3/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:09<00:00,  7.63s/it] 
train Loss: 1287.7640, Seg Loss: 0.4719, Heatmap Loss: 857.9660, Geometric Loss: 0.8578
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.7309 🟢 (186.5% of target 2.0)
  avg_peak_to_mean_ratio: 18.5485
  avg_peak_to_second_ratio: 1.1596
  detection_rate: 0.8860 🔴 (88.6% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0002
  p2s_ratio_max: 1.8950
  p2s_ratio_std: 0.2407
  secondary_peak_distance: 82.7542
  primary_peak_sharpness: 0.9536
  primary_peak_isolation: 4.7263

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2518
  Top-Right: 1.1630
  Bottom-Right: 1.1432
  Bottom-Left: 1.0802

Secondary Peak Relative Position: x=-22.49, y=-3.87
train Heatmap Components:
  mse_loss: 0.8296
  separation_loss: 3.5001
  peak_separation_loss: 190.1544
  edge_suppression_loss: -0.0166
  peak_enhancement_loss: 1.5870
  peak_to_second_ratio_loss: 1.4236
  avg_peak_to_second_ratio: 1.1006
  detection_rate_loss: 0.0721
  segmentation_guidance_loss: 0.1340
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.09s/it] 
val Loss: 1997.9101, Seg Loss: 0.5549, Heatmap Loss: 1331.3395, Geometric Loss: 0.8649
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.7413 🔴 (87.1% of target 2.0)
  avg_peak_to_mean_ratio: 4.2133
  avg_peak_to_second_ratio: 1.3004
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0030
  p2s_ratio_max: 2.9189
  p2s_ratio_std: 0.5268
  secondary_peak_distance: 93.2381
  primary_peak_sharpness: 0.4272
  primary_peak_isolation: 14.9911

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7131
  Top-Right: 1.1861
  Bottom-Right: 1.2588
  Bottom-Left: 1.0436

Secondary Peak Relative Position: x=-28.37, y=-1.72
val Heatmap Components:
  mse_loss: 0.6947
  separation_loss: 3.8894
  peak_separation_loss: 298.3365
  edge_suppression_loss: -0.0124
  peak_enhancement_loss: 2.0561
  peak_to_second_ratio_loss: 1.2209
  avg_peak_to_second_ratio: 1.1554
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000512
New best model saved with loss: 1997.9101
New best model saved with detection rate: 1.0000
New best model saved with combined score: 0.7112 (loss: 1997.9101, p2s: 1.3004, detection: 1.0000)
Early stopping: New best score: 0.7112

Epoch 4/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.37s/it] 
train Loss: 526.4836, Seg Loss: 0.5525, Heatmap Loss: 350.4128, Geometric Loss: 0.7799
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.0948 🟢 (154.7% of target 2.0)
  avg_peak_to_mean_ratio: 9.6167
  avg_peak_to_second_ratio: 1.2404
  detection_rate: 0.9081 🟡 (90.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0007
  p2s_ratio_max: 1.9552
  p2s_ratio_std: 0.2861
  secondary_peak_distance: 121.3065
  primary_peak_sharpness: 0.8867
  primary_peak_isolation: 3.8918

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4280
  Top-Right: 1.1200
  Bottom-Right: 1.2968
  Bottom-Left: 1.1167

Secondary Peak Relative Position: x=-32.23, y=-5.33
train Heatmap Components:
  mse_loss: 0.6422
  separation_loss: 3.5479
  peak_separation_loss: 81.1883
  edge_suppression_loss: -0.0159
  peak_enhancement_loss: 1.5881
  peak_to_second_ratio_loss: 1.2456
  avg_peak_to_second_ratio: 1.1798
  detection_rate_loss: 0.0827
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.12s/it] 
val Loss: 136.6012, Seg Loss: 0.5728, Heatmap Loss: 90.4792, Geometric Loss: 0.7739
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.9235 🟡 (96.2% of target 2.0)
  avg_peak_to_mean_ratio: 21.2443
  avg_peak_to_second_ratio: 1.2475
  detection_rate: 0.9706 🟡 (97.1% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0124
  p2s_ratio_max: 1.8379
  p2s_ratio_std: 0.2761
  secondary_peak_distance: 104.9641
  primary_peak_sharpness: 0.5364
  primary_peak_isolation: 5.0127

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4081
  Top-Right: 1.2055
  Bottom-Right: 1.2280
  Bottom-Left: 1.1486

Secondary Peak Relative Position: x=-12.35, y=-5.22
val Heatmap Components:
  mse_loss: 0.1941
  separation_loss: 1.7389
  peak_separation_loss: 21.4330
  edge_suppression_loss: -0.0116
  peak_enhancement_loss: 1.5282
  peak_to_second_ratio_loss: 1.1005
  avg_peak_to_second_ratio: 1.2162
  detection_rate_loss: 0.0015
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000656
New best model saved with loss: 136.6012
Early stopping: No improvement for 1/15 epochs. Best: 0.7112, Current: 0.6907

Epoch 5/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:07<00:00,  7.52s/it] 
train Loss: 293.3599, Seg Loss: 0.4476, Heatmap Loss: 195.0509, Geometric Loss: 0.8397
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.4881 🟢 (224.4% of target 2.0)
  avg_peak_to_mean_ratio: 24.4218
  avg_peak_to_second_ratio: 1.2989
  detection_rate: 0.9081 🟡 (90.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0003
  p2s_ratio_max: 2.0466
  p2s_ratio_std: 0.3262
  secondary_peak_distance: 139.9070
  primary_peak_sharpness: 1.0359
  primary_peak_isolation: 4.8291

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4739
  Top-Right: 1.1434
  Bottom-Right: 1.3528
  Bottom-Left: 1.2256

Secondary Peak Relative Position: x=-43.12, y=-13.81
train Heatmap Components:
  mse_loss: 0.3925
  separation_loss: 5.4652
  peak_separation_loss: 46.7904
  edge_suppression_loss: -0.0110
  peak_enhancement_loss: 1.3824
  peak_to_second_ratio_loss: 1.1821
  avg_peak_to_second_ratio: 1.2272
  detection_rate_loss: 0.0685
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 2461.8164, Seg Loss: 0.4492, Heatmap Loss: 1640.6619, Geometric Loss: 0.9360
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.0792 🟢 (204.0% of target 2.0)
  avg_peak_to_mean_ratio: 18.2195
  avg_peak_to_second_ratio: 1.1426
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0030
  p2s_ratio_max: 1.5052
  p2s_ratio_std: 0.1535
  secondary_peak_distance: 100.9259
  primary_peak_sharpness: 1.2487
  primary_peak_isolation: 3.2217

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2198
  Top-Right: 1.0785
  Bottom-Right: 1.1343
  Bottom-Left: 1.1377

Secondary Peak Relative Position: x=-37.00, y=29.81
val Heatmap Components:
  mse_loss: 0.7437
  separation_loss: 6.9173
  peak_separation_loss: 396.7418
  edge_suppression_loss: 0.0142
  peak_enhancement_loss: 1.5717
  peak_to_second_ratio_loss: 1.5828
  avg_peak_to_second_ratio: 1.1180
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000800

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=1.50, geometric_weight=0.40
Increasing heatmap weight to improve peak-to-second ratio
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 1.50 -> 1.70, geometric_weight: 0.40 -> 0.50

=== Learning Rate Tuning ===
Slow peak-to-second ratio improvement (change: -0.1214)
Increasing learning rate to accelerate peak-to-second ratio improvement
Adjusted learning rate: 0.000800 -> 0.000960
Early stopping: No improvement for 2/15 epochs. Best: 0.7112, Current: 0.5174
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 5 to prevent memory issues
Model evaluated at epoch 5
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 6/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:04<00:00,  7.29s/it] 
train Loss: 632.6946, Seg Loss: 0.4082, Heatmap Loss: 371.7222, Geometric Loss: 0.7172
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5896 🟢 (129.5% of target 2.0)
  avg_peak_to_mean_ratio: 48.4302
  avg_peak_to_second_ratio: 1.2696
  detection_rate: 0.8529 🔴 (85.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0012
  p2s_ratio_max: 1.9784
  p2s_ratio_std: 0.2901
  secondary_peak_distance: 148.1493
  primary_peak_sharpness: 0.7623
  primary_peak_isolation: 3.8457

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4123
  Top-Right: 1.1794
  Bottom-Right: 1.2828
  Bottom-Left: 1.2040

Secondary Peak Relative Position: x=-25.62, y=-8.41
train Heatmap Components:
  mse_loss: 0.2587
  separation_loss: 3.0685
  peak_separation_loss: 85.5694
  edge_suppression_loss: -0.0123
  peak_enhancement_loss: 1.3396
  peak_to_second_ratio_loss: 1.3171
  avg_peak_to_second_ratio: 1.2208
  detection_rate_loss: 0.0875
  segmentation_guidance_loss: 0.1684
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.07s/it] 
val Loss: 992.5124, Seg Loss: 0.4031, Heatmap Loss: 583.4310, Geometric Loss: 0.5531
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.0298 🟢 (151.5% of target 2.0)
  avg_peak_to_mean_ratio: 11.9487
  avg_peak_to_second_ratio: 1.3590
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0318
  p2s_ratio_max: 1.9909
  p2s_ratio_std: 0.2900
  secondary_peak_distance: 168.8979
  primary_peak_sharpness: 0.7499
  primary_peak_isolation: 3.9693

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.3289
  Top-Right: 1.3746
  Bottom-Right: 1.4789
  Bottom-Left: 1.2536

Secondary Peak Relative Position: x=-11.53, y=-22.91
val Heatmap Components:
  mse_loss: 0.2051
  separation_loss: 1.4299
  peak_separation_loss: 128.7516
  edge_suppression_loss: -0.0029
  peak_enhancement_loss: 1.2757
  peak_to_second_ratio_loss: 0.8709
  avg_peak_to_second_ratio: 1.2765
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 1.7905
Current learning rate: 0.000800
Early stopping: No improvement for 3/15 epochs. Best: 0.7112, Current: 0.5775

Epoch 7/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:03<00:00,  7.29s/it] 
train Loss: 345.1004, Seg Loss: 0.4162, Heatmap Loss: 303.9763, Geometric Loss: 0.7917
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.7881 🟢 (139.4% of target 2.0)
  avg_peak_to_mean_ratio: 19.3635
  avg_peak_to_second_ratio: 1.3994
  detection_rate: 0.8603 🔴 (86.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0018
  p2s_ratio_max: 2.4181
  p2s_ratio_std: 0.4323
  secondary_peak_distance: 166.9324
  primary_peak_sharpness: 0.5473
  primary_peak_isolation: 7.3406

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.6793
  Top-Right: 1.1192
  Bottom-Right: 1.4835
  Bottom-Left: 1.3157

Secondary Peak Relative Position: x=-15.63, y=0.13
train Heatmap Components:
  mse_loss: 0.2673
  separation_loss: 2.3237
  peak_separation_loss: 64.7312
  edge_suppression_loss: -0.0141
  peak_enhancement_loss: 1.3783
  peak_to_second_ratio_loss: 1.1207
  avg_peak_to_second_ratio: 1.3042
  detection_rate_loss: 0.0947
  segmentation_guidance_loss: 0.2214
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.10s/it] 
val Loss: 445.3094, Seg Loss: 0.3835, Heatmap Loss: 455.2162, Geometric Loss: 0.7113
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.8189 🟡 (90.9% of target 2.0)
  avg_peak_to_mean_ratio: 71.9658
  avg_peak_to_second_ratio: 1.8292
  detection_rate: 0.9412 🟡 (94.1% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0132
  p2s_ratio_max: 3.3082
  p2s_ratio_std: 0.8866
  secondary_peak_distance: 144.5871
  primary_peak_sharpness: 0.2255
  primary_peak_isolation: 3.0326

Per-Corner Peak-to-Second Ratios:
  Top-Left: 3.1017
  Top-Right: 1.0829
  Bottom-Right: 1.8401
  Bottom-Left: 1.2922

Secondary Peak Relative Position: x=-49.10, y=54.12
val Heatmap Components:
  mse_loss: 0.1716
  separation_loss: 1.4003
  peak_separation_loss: 100.0000
  edge_suppression_loss: -0.0086
  peak_enhancement_loss: 1.2608
  peak_to_second_ratio_loss: 0.8229
  avg_peak_to_second_ratio: 1.3757
  detection_rate_loss: 0.0017
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000750
New best model saved with peak-to-second ratio: 1.8292
Early stopping: No improvement for 4/15 epochs. Best: 0.7112, Current: 0.6882

Epoch 8/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:03<00:00,  7.27s/it] 
train Loss: 37.3529, Seg Loss: 0.4305, Heatmap Loss: 75.7846, Geometric Loss: 0.8629
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.3241 🟢 (166.2% of target 2.0)
  avg_peak_to_mean_ratio: 218.0157
  avg_peak_to_second_ratio: 1.6276
  detection_rate: 0.9191 🟡 (91.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0436
  p2s_ratio_max: 3.0731
  p2s_ratio_std: 0.5919
  secondary_peak_distance: 201.1410
  primary_peak_sharpness: 0.6306
  primary_peak_isolation: 11.1438

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0830
  Top-Right: 1.3448
  Bottom-Right: 1.5421
  Bottom-Left: 1.5404

Secondary Peak Relative Position: x=-37.78, y=47.98
train Heatmap Components:
  mse_loss: 0.2151
  separation_loss: 4.0659
  peak_separation_loss: 16.6381
  edge_suppression_loss: -0.0151
  peak_enhancement_loss: 1.2609
  peak_to_second_ratio_loss: 0.7264
  avg_peak_to_second_ratio: 1.4920
  detection_rate_loss: 0.0510
  segmentation_guidance_loss: 0.1712
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.10s/it] 
val Loss: 2.6735, Seg Loss: 0.3960, Heatmap Loss: 8.1068, Geometric Loss: 0.6891
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1072 🟢 (105.4% of target 2.0)
  avg_peak_to_mean_ratio: 5.5103
  avg_peak_to_second_ratio: 1.9176
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3948
  p2s_ratio_max: 2.5004
  p2s_ratio_std: 0.3510
  secondary_peak_distance: 225.9281
  primary_peak_sharpness: 0.4192
  primary_peak_isolation: 17.6286

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3151
  Top-Right: 1.8825
  Bottom-Right: 1.6407
  Bottom-Left: 1.8320

Secondary Peak Relative Position: x=-62.76, y=65.53
val Heatmap Components:
  mse_loss: 0.1000
  separation_loss: 4.6722
  peak_separation_loss: 1.5196
  edge_suppression_loss: -0.0149
  peak_enhancement_loss: 1.1996
  peak_to_second_ratio_loss: 0.0793
  avg_peak_to_second_ratio: 1.7440
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000600
New best model saved with loss: 2.6735
New best model saved with peak-to-second ratio: 1.9176
New best model saved with combined score: 0.8500 (loss: 2.6735, p2s: 1.9176, detection: 1.0000)
Early stopping: New best score: 0.8500

Epoch 9/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:03<00:00,  7.28s/it] 
train Loss: 11.5889, Seg Loss: 0.3850, Heatmap Loss: 55.2075, Geometric Loss: 0.8878
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3425 🟢 (117.1% of target 2.0)
  avg_peak_to_mean_ratio: 49.5663
  avg_peak_to_second_ratio: 1.6724
  detection_rate: 0.8787 🔴 (87.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0021
  p2s_ratio_max: 3.3144
  p2s_ratio_std: 0.6839
  secondary_peak_distance: 196.6965
  primary_peak_sharpness: 0.5018
  primary_peak_isolation: 13.0100

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9401
  Top-Right: 1.4138
  Bottom-Right: 1.9048
  Bottom-Left: 1.4310

Secondary Peak Relative Position: x=-39.96, y=3.44
train Heatmap Components:
  mse_loss: 0.1536
  separation_loss: 3.6610
  peak_separation_loss: 12.1413
  edge_suppression_loss: -0.0241
  peak_enhancement_loss: 1.4413
  peak_to_second_ratio_loss: 0.8494
  avg_peak_to_second_ratio: 1.5227
  detection_rate_loss: 0.0894
  segmentation_guidance_loss: 0.1110
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.09s/it] 
val Loss: 32.5414, Seg Loss: 0.4168, Heatmap Loss: 28.0899, Geometric Loss: 0.5345
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.8109 🟡 (90.5% of target 2.0)
  avg_peak_to_mean_ratio: 15.8267
  avg_peak_to_second_ratio: 1.6904
  detection_rate: 0.9118 🟡 (91.2% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0289
  p2s_ratio_max: 4.8702
  p2s_ratio_std: 0.9749
  secondary_peak_distance: 198.8228
  primary_peak_sharpness: 0.3463
  primary_peak_isolation: 27.6668

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.5457
  Top-Right: 1.9703
  Bottom-Right: 2.1012
  Bottom-Left: 1.1445

Secondary Peak Relative Position: x=-60.17, y=-16.13
val Heatmap Components:
  mse_loss: 0.1020
  separation_loss: 1.4967
  peak_separation_loss: 6.0261
  edge_suppression_loss: -0.0187
  peak_enhancement_loss: 1.2647
  peak_to_second_ratio_loss: 0.8541
  avg_peak_to_second_ratio: 1.4830
  detection_rate_loss: 0.0243
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000720
Early stopping: No improvement for 1/15 epochs. Best: 0.8500, Current: 0.6519

Epoch 10/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:03<00:00,  7.29s/it] 
train Loss: 22.0629, Seg Loss: 0.3748, Heatmap Loss: 32.1669, Geometric Loss: 0.8170
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.3237 🟢 (166.2% of target 2.0)
  avg_peak_to_mean_ratio: 73.0415
  avg_peak_to_second_ratio: 1.6654
  detection_rate: 0.9265 🟡 (92.6% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0608
  p2s_ratio_max: 2.5074
  p2s_ratio_std: 0.4375
  secondary_peak_distance: 218.3606
  primary_peak_sharpness: 0.8161
  primary_peak_isolation: 7.3418

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7867
  Top-Right: 1.3703
  Bottom-Right: 1.8904
  Bottom-Left: 1.6142

Secondary Peak Relative Position: x=-35.18, y=14.62
train Heatmap Components:
  mse_loss: 0.1472
  separation_loss: 5.2497
  peak_separation_loss: 6.0948
  edge_suppression_loss: -0.0226
  peak_enhancement_loss: 1.2652
  peak_to_second_ratio_loss: 0.5685
  avg_peak_to_second_ratio: 1.5605
  detection_rate_loss: 0.0537
  segmentation_guidance_loss: 0.0110
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.10s/it] 
val Loss: 9.0011, Seg Loss: 0.4061, Heatmap Loss: 19.9301, Geometric Loss: 0.6941
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5465 🟢 (127.3% of target 2.0)
  avg_peak_to_mean_ratio: 43.5626
  avg_peak_to_second_ratio: 1.8670
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3994
  p2s_ratio_max: 2.7929
  p2s_ratio_std: 0.4015
  secondary_peak_distance: 231.7411
  primary_peak_sharpness: 0.6565
  primary_peak_isolation: 16.5533

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8658
  Top-Right: 2.3004
  Bottom-Right: 1.7321
  Bottom-Left: 1.5698

Secondary Peak Relative Position: x=-88.28, y=19.24
val Heatmap Components:
  mse_loss: 0.1079
  separation_loss: 4.6722
  peak_separation_loss: 1.7827
  edge_suppression_loss: -0.0203
  peak_enhancement_loss: 1.1975
  peak_to_second_ratio_loss: 0.1890
  avg_peak_to_second_ratio: 1.8188
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000666

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=0.30, geometric_weight=1.98
Keeping current heatmap weight
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 0.30 -> 0.50, geometric_weight: 1.98 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 2/15 epochs. Best: 0.8500, Current: 0.7000
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 10 to prevent memory issues
Model evaluated at epoch 10
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 11/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:03<00:00,  7.25s/it] 
train Loss: 22.8490, Seg Loss: 0.3999, Heatmap Loss: 140.5098, Geometric Loss: 0.8647
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.6805 🟢 (134.0% of target 2.0)
  avg_peak_to_mean_ratio: 24.9070
  avg_peak_to_second_ratio: 1.7512
  detection_rate: 0.8971 🔴 (89.7% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0886
  p2s_ratio_max: 2.8390
  p2s_ratio_std: 0.4934
  secondary_peak_distance: 181.4773
  primary_peak_sharpness: 0.5664
  primary_peak_isolation: 6.0342

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1120
  Top-Right: 1.5838
  Bottom-Right: 1.8205
  Bottom-Left: 1.4885

Secondary Peak Relative Position: x=1.33, y=28.11
train Heatmap Components:
  mse_loss: 0.1471
  separation_loss: 5.8278
  peak_separation_loss: 41.8807
  edge_suppression_loss: -0.0157
  peak_enhancement_loss: 1.1379
  peak_to_second_ratio_loss: 0.5671
  avg_peak_to_second_ratio: 1.5546
  detection_rate_loss: 0.0521
  segmentation_guidance_loss: 0.5493
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.09s/it] 
val Loss: 4.4868, Seg Loss: 0.4187, Heatmap Loss: 24.8388, Geometric Loss: 0.6487
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1001 🟢 (105.0% of target 2.0)
  avg_peak_to_mean_ratio: 8.5206
  avg_peak_to_second_ratio: 1.8911
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3217
  p2s_ratio_max: 2.6934
  p2s_ratio_std: 0.4233
  secondary_peak_distance: 217.9985
  primary_peak_sharpness: 0.2499
  primary_peak_isolation: 6.8761

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.4630
  Top-Right: 1.5079
  Bottom-Right: 1.9838
  Bottom-Left: 1.6099

Secondary Peak Relative Position: x=5.91, y=1.09
val Heatmap Components:
  mse_loss: 0.0898
  separation_loss: 4.6722
  peak_separation_loss: 4.1650
  edge_suppression_loss: -0.0068
  peak_enhancement_loss: 1.0165
  peak_to_second_ratio_loss: 0.0930
  avg_peak_to_second_ratio: 1.6328
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000532
Early stopping: No improvement for 3/15 epochs. Best: 0.8500, Current: 0.7483

Epoch 12/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.36s/it] 
train Loss: 17.9745, Seg Loss: 0.3715, Heatmap Loss: 158.7656, Geometric Loss: 0.8272
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3610 🟢 (118.1% of target 2.0)
  avg_peak_to_mean_ratio: 20.6656
  avg_peak_to_second_ratio: 1.8793
  detection_rate: 0.8971 🔴 (89.7% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0207
  p2s_ratio_max: 3.2579
  p2s_ratio_std: 0.6296
  secondary_peak_distance: 204.8005
  primary_peak_sharpness: 0.4723
  primary_peak_isolation: 7.4938

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2712
  Top-Right: 1.6144
  Bottom-Right: 1.7795
  Bottom-Left: 1.8521

Secondary Peak Relative Position: x=17.20, y=24.10
train Heatmap Components:
  mse_loss: 0.1330
  separation_loss: 5.2688
  peak_separation_loss: 38.7218
  edge_suppression_loss: -0.0149
  peak_enhancement_loss: 1.1582
  peak_to_second_ratio_loss: 0.5777
  avg_peak_to_second_ratio: 1.6161
  detection_rate_loss: 0.0657
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.15s/it] 
val Loss: 3.8745, Seg Loss: 0.4065, Heatmap Loss: 18.4508, Geometric Loss: 0.6664
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.8899 🟡 (94.5% of target 2.0)
  avg_peak_to_mean_ratio: 49.5498
  avg_peak_to_second_ratio: 2.0993
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1632
  p2s_ratio_max: 3.1146
  p2s_ratio_std: 0.6653
  secondary_peak_distance: 230.5750
  primary_peak_sharpness: 0.2997
  primary_peak_isolation: 20.5020

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.9428
  Top-Right: 1.7832
  Bottom-Right: 2.3284
  Bottom-Left: 1.3428

Secondary Peak Relative Position: x=-26.68, y=61.57
val Heatmap Components:
  mse_loss: 0.0907
  separation_loss: 4.6722
  peak_separation_loss: 1.9444
  edge_suppression_loss: -0.0080
  peak_enhancement_loss: 1.0367
  peak_to_second_ratio_loss: 0.2768
  avg_peak_to_second_ratio: 1.7005
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000639
New best model saved with peak-to-second ratio: 2.0993
Early stopping: No improvement for 4/15 epochs. Best: 0.8500, Current: 0.7826

Epoch 13/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.36s/it] 
train Loss: 9.1886, Seg Loss: 0.3635, Heatmap Loss: 71.8052, Geometric Loss: 0.8663
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.6181 🟢 (180.9% of target 2.0)
  avg_peak_to_mean_ratio: 61.4665
  avg_peak_to_second_ratio: 1.9866
  detection_rate: 0.9485 🟡 (94.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1210
  p2s_ratio_max: 3.1190
  p2s_ratio_std: 0.5529
  secondary_peak_distance: 202.2505
  primary_peak_sharpness: 0.5627
  primary_peak_isolation: 5.7780

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3714
  Top-Right: 1.6239
  Bottom-Right: 1.8847
  Bottom-Left: 2.0663

Secondary Peak Relative Position: x=-26.41, y=50.79
train Heatmap Components:
  mse_loss: 0.1159
  separation_loss: 6.3867
  peak_separation_loss: 17.2778
  edge_suppression_loss: -0.0106
  peak_enhancement_loss: 0.9890
  peak_to_second_ratio_loss: 0.3573
  avg_peak_to_second_ratio: 1.7195
  detection_rate_loss: 0.0437
  segmentation_guidance_loss: 0.2963
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.11s/it] 
val Loss: 3.7796, Seg Loss: 0.4128, Heatmap Loss: 20.4720, Geometric Loss: 0.6288
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.4900 🟢 (124.5% of target 2.0)
  avg_peak_to_mean_ratio: 31.7874
  avg_peak_to_second_ratio: 2.0301
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.4048
  p2s_ratio_max: 2.6230
  p2s_ratio_std: 0.4534
  secondary_peak_distance: 238.8349
  primary_peak_sharpness: 0.3096
  primary_peak_isolation: 5.8448

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.5232
  Top-Right: 1.7658
  Bottom-Right: 2.3012
  Bottom-Left: 1.5301

Secondary Peak Relative Position: x=-13.41, y=40.29
val Heatmap Components:
  mse_loss: 0.0744
  separation_loss: 4.6722
  peak_separation_loss: 1.4837
  edge_suppression_loss: -0.0079
  peak_enhancement_loss: 0.9887
  peak_to_second_ratio_loss: 0.0851
  avg_peak_to_second_ratio: 1.7384
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000553
Early stopping: No improvement for 5/15 epochs. Best: 0.8500, Current: 0.7879

Epoch 14/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.40s/it] 
train Loss: 34.3116, Seg Loss: 0.3683, Heatmap Loss: 66.3027, Geometric Loss: 0.8369
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3386 🟢 (116.9% of target 2.0)
  avg_peak_to_mean_ratio: 24.7338
  avg_peak_to_second_ratio: 1.9011
  detection_rate: 0.9301 🟡 (93.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0838
  p2s_ratio_max: 2.6753
  p2s_ratio_std: 0.4636
  secondary_peak_distance: 213.3487
  primary_peak_sharpness: 0.3088
  primary_peak_isolation: 3.6479

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9858
  Top-Right: 1.6132
  Bottom-Right: 1.9669
  Bottom-Left: 2.0383

Secondary Peak Relative Position: x=-8.79, y=25.37
train Heatmap Components:
  mse_loss: 0.0942
  separation_loss: 5.8082
  peak_separation_loss: 23.4003
  edge_suppression_loss: -0.0094
  peak_enhancement_loss: 0.9699
  peak_to_second_ratio_loss: 0.4143
  avg_peak_to_second_ratio: 1.7145
  detection_rate_loss: 0.0700
  segmentation_guidance_loss: 0.0889
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.10s/it] 
val Loss: 4.6145, Seg Loss: 0.4081, Heatmap Loss: 28.6779, Geometric Loss: 0.6387
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3300 🟢 (116.5% of target 2.0)
  avg_peak_to_mean_ratio: 30.1707
  avg_peak_to_second_ratio: 1.7854
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.4966
  p2s_ratio_max: 2.3613
  p2s_ratio_std: 0.2733
  secondary_peak_distance: 239.1321
  primary_peak_sharpness: 0.3676
  primary_peak_isolation: 4.0138

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.6147
  Top-Right: 1.5924
  Bottom-Right: 2.1456
  Bottom-Left: 1.7889

Secondary Peak Relative Position: x=-18.69, y=51.07
val Heatmap Components:
  mse_loss: 0.0577
  separation_loss: 4.6722
  peak_separation_loss: 5.6993
  edge_suppression_loss: -0.0068
  peak_enhancement_loss: 0.9078
  peak_to_second_ratio_loss: 0.0178
  avg_peak_to_second_ratio: 1.7326
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000443
Early stopping: No improvement for 6/15 epochs. Best: 0.8500, Current: 0.7370

Epoch 15/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:12<00:00,  7.80s/it] 
train Loss: 19.4662, Seg Loss: 0.3568, Heatmap Loss: 182.4121, Geometric Loss: 0.8583
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.9230 🟢 (196.1% of target 2.0)
  avg_peak_to_mean_ratio: 61.8937
  avg_peak_to_second_ratio: 1.8665
  detection_rate: 0.9265 🟡 (92.6% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1286
  p2s_ratio_max: 2.7015
  p2s_ratio_std: 0.4380
  secondary_peak_distance: 208.9374
  primary_peak_sharpness: 0.5963
  primary_peak_isolation: 5.5392

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8035
  Top-Right: 1.5649
  Bottom-Right: 1.8537
  Bottom-Left: 2.2438

Secondary Peak Relative Position: x=-51.74, y=51.23
train Heatmap Components:
  mse_loss: 0.1073
  separation_loss: 7.9070
  peak_separation_loss: 50.3121
  edge_suppression_loss: -0.0052
  peak_enhancement_loss: 0.8555
  peak_to_second_ratio_loss: 0.4163
  avg_peak_to_second_ratio: 1.7232
  detection_rate_loss: 0.0537
  segmentation_guidance_loss: 0.0511
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 3.6836, Seg Loss: 0.4045, Heatmap Loss: 23.1488, Geometric Loss: 0.6187
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.1342 🟢 (156.7% of target 2.0)
  avg_peak_to_mean_ratio: 25.9051
  avg_peak_to_second_ratio: 2.0614
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6580
  p2s_ratio_max: 2.5512
  p2s_ratio_std: 0.3099
  secondary_peak_distance: 243.2939
  primary_peak_sharpness: 0.3966
  primary_peak_isolation: 6.4725

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3297
  Top-Right: 1.7894
  Bottom-Right: 2.3238
  Bottom-Left: 1.8027

Secondary Peak Relative Position: x=-70.09, y=106.63
val Heatmap Components:
  mse_loss: 0.0546
  separation_loss: 4.6722
  peak_separation_loss: 0.7712
  edge_suppression_loss: 0.0021
  peak_enhancement_loss: 0.7422
  peak_to_second_ratio_loss: 0.0000
  avg_peak_to_second_ratio: 1.8191
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000354

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=0.09, geometric_weight=1.68
Decreasing heatmap weight to balance with other metrics
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 0.09 -> 0.50, geometric_weight: 1.68 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 7/15 epochs. Best: 0.8500, Current: 0.7933
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 15 to prevent memory issues
Model evaluated at epoch 15
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 16/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.40s/it] 
train Loss: 7.8846, Seg Loss: 0.3687, Heatmap Loss: 51.7671, Geometric Loss: 0.8164
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.1262 🟢 (206.3% of target 2.0)
  avg_peak_to_mean_ratio: 108.3280
  avg_peak_to_second_ratio: 1.9722
  detection_rate: 0.9191 🟡 (91.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0829
  p2s_ratio_max: 2.8174
  p2s_ratio_std: 0.4687
  secondary_peak_distance: 230.3708
  primary_peak_sharpness: 0.5134
  primary_peak_isolation: 5.0441

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0543
  Top-Right: 1.6292
  Bottom-Right: 2.2270
  Bottom-Left: 1.9782

Secondary Peak Relative Position: x=-43.48, y=78.53
train Heatmap Components:
  mse_loss: 0.1057
  separation_loss: 6.3869
  peak_separation_loss: 29.2676
  edge_suppression_loss: -0.0026
  peak_enhancement_loss: 0.8788
  peak_to_second_ratio_loss: 0.3941
  avg_peak_to_second_ratio: 1.7714
  detection_rate_loss: 0.0745
  segmentation_guidance_loss: 0.1272
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.17s/it] 
val Loss: 3.8330, Seg Loss: 0.4091, Heatmap Loss: 23.9376, Geometric Loss: 0.6573
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.6878 🟢 (184.4% of target 2.0)
  avg_peak_to_mean_ratio: 26.5509
  avg_peak_to_second_ratio: 2.0492
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6653
  p2s_ratio_max: 2.6512
  p2s_ratio_std: 0.3305
  secondary_peak_distance: 223.0006
  primary_peak_sharpness: 0.4740
  primary_peak_isolation: 8.3677

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9760
  Top-Right: 1.9199
  Bottom-Right: 2.5507
  Bottom-Left: 1.7502

Secondary Peak Relative Position: x=-14.18, y=56.62
val Heatmap Components:
  mse_loss: 0.0620
  separation_loss: 4.6722
  peak_separation_loss: 0.6748
  edge_suppression_loss: -0.0000
  peak_enhancement_loss: 0.7597
  peak_to_second_ratio_loss: 0.0071
  avg_peak_to_second_ratio: 1.8547
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000425
Early stopping: No improvement for 8/15 epochs. Best: 0.8500, Current: 0.7849

Epoch 17/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.41s/it] 
train Loss: 19.4429, Seg Loss: 0.3575, Heatmap Loss: 48.4620, Geometric Loss: 0.8801
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.5257 🟢 (176.3% of target 2.0)
  avg_peak_to_mean_ratio: 43.9621
  avg_peak_to_second_ratio: 1.8290
  detection_rate: 0.9044 🟡 (90.4% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1376
  p2s_ratio_max: 2.6182
  p2s_ratio_std: 0.4128
  secondary_peak_distance: 201.2601
  primary_peak_sharpness: 0.4302
  primary_peak_isolation: 4.9506

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9091
  Top-Right: 1.5655
  Bottom-Right: 2.0078
  Bottom-Left: 1.8336

Secondary Peak Relative Position: x=-15.73, y=48.98
train Heatmap Components:
  mse_loss: 0.1091
  separation_loss: 6.3136
  peak_separation_loss: 17.4453
  edge_suppression_loss: -0.0027
  peak_enhancement_loss: 0.8237
  peak_to_second_ratio_loss: 0.4738
  avg_peak_to_second_ratio: 1.7072
  detection_rate_loss: 0.0731
  segmentation_guidance_loss: 0.0366
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.19s/it] 
val Loss: 12.2972, Seg Loss: 0.3911, Heatmap Loss: 23.5335, Geometric Loss: 0.6067
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.2442 🟢 (162.2% of target 2.0)
  avg_peak_to_mean_ratio: 29.8186
  avg_peak_to_second_ratio: 1.9650
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.7257
  p2s_ratio_max: 2.1995
  p2s_ratio_std: 0.1294
  secondary_peak_distance: 233.4175
  primary_peak_sharpness: 0.3823
  primary_peak_isolation: 3.0000

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0456
  Top-Right: 1.9301
  Bottom-Right: 2.0083
  Bottom-Left: 1.8758

Secondary Peak Relative Position: x=-53.26, y=90.49
val Heatmap Components:
  mse_loss: 0.0540
  separation_loss: 4.6722
  peak_separation_loss: 0.6634
  edge_suppression_loss: 0.0003
  peak_enhancement_loss: 0.7444
  peak_to_second_ratio_loss: 0.0085
  avg_peak_to_second_ratio: 1.8126
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000340
Early stopping: No improvement for 9/15 epochs. Best: 0.8500, Current: 0.7000

Epoch 18/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.45s/it] 
train Loss: 22.6112, Seg Loss: 0.3776, Heatmap Loss: 64.8410, Geometric Loss: 0.8293
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.1455 🟢 (207.3% of target 2.0)
  avg_peak_to_mean_ratio: 129.2821
  avg_peak_to_second_ratio: 1.9214
  detection_rate: 0.9044 🟡 (90.4% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1607
  p2s_ratio_max: 2.7913
  p2s_ratio_std: 0.4526
  secondary_peak_distance: 202.2318
  primary_peak_sharpness: 0.5272
  primary_peak_isolation: 4.8431

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0655
  Top-Right: 1.7006
  Bottom-Right: 1.8409
  Bottom-Left: 2.0785

Secondary Peak Relative Position: x=-20.04, y=33.20
train Heatmap Components:
  mse_loss: 0.0873
  separation_loss: 7.9248
  peak_separation_loss: 26.6532
  edge_suppression_loss: -0.0049
  peak_enhancement_loss: 0.9101
  peak_to_second_ratio_loss: 0.5068
  avg_peak_to_second_ratio: 1.7725
  detection_rate_loss: 0.0646
  segmentation_guidance_loss: 0.1903
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.15s/it] 
val Loss: 15.2534, Seg Loss: 0.3714, Heatmap Loss: 24.1375, Geometric Loss: 0.6347
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.7958 🟢 (189.8% of target 2.0)
  avg_peak_to_mean_ratio: 68.5500
  avg_peak_to_second_ratio: 2.0600
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6081
  p2s_ratio_max: 2.3809
  p2s_ratio_std: 0.2494
  secondary_peak_distance: 206.4591
  primary_peak_sharpness: 0.4545
  primary_peak_isolation: 4.7206

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2044
  Top-Right: 1.7990
  Bottom-Right: 2.2012
  Bottom-Left: 2.0354

Secondary Peak Relative Position: x=-4.84, y=62.49
val Heatmap Components:
  mse_loss: 0.0472
  separation_loss: 4.6722
  peak_separation_loss: 0.9918
  edge_suppression_loss: -0.0009
  peak_enhancement_loss: 0.7824
  peak_to_second_ratio_loss: 0.0162
  avg_peak_to_second_ratio: 1.8186
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000408
Early stopping: No improvement for 10/15 epochs. Best: 0.8500, Current: 0.7000

Epoch 19/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.40s/it] 
train Loss: 8.4421, Seg Loss: 0.3332, Heatmap Loss: 42.3998, Geometric Loss: 0.9034
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.3858 🟢 (219.3% of target 2.0)
  avg_peak_to_mean_ratio: 67.3079
  avg_peak_to_second_ratio: 1.9731
  detection_rate: 0.9191 🟡 (91.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0712
  p2s_ratio_max: 3.0367
  p2s_ratio_std: 0.5078
  secondary_peak_distance: 188.0687
  primary_peak_sharpness: 0.6434
  primary_peak_isolation: 10.4838

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0820
  Top-Right: 1.6672
  Bottom-Right: 2.1396
  Bottom-Left: 2.0037

Secondary Peak Relative Position: x=2.23, y=27.12
train Heatmap Components:
  mse_loss: 0.0811
  separation_loss: 7.9226
  peak_separation_loss: 5.2933
  edge_suppression_loss: -0.0065
  peak_enhancement_loss: 0.9582
  peak_to_second_ratio_loss: 0.5046
  avg_peak_to_second_ratio: 1.7937
  detection_rate_loss: 0.0604
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.12s/it] 
val Loss: 3.7026, Seg Loss: 0.4078, Heatmap Loss: 24.0766, Geometric Loss: 0.5657
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.9074 🟢 (195.4% of target 2.0)
  avg_peak_to_mean_ratio: 77.5337
  avg_peak_to_second_ratio: 2.1657
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6024
  p2s_ratio_max: 2.6327
  p2s_ratio_std: 0.2899
  secondary_peak_distance: 192.6921
  primary_peak_sharpness: 0.4707
  primary_peak_isolation: 15.9265

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.4188
  Top-Right: 1.9793
  Bottom-Right: 2.3543
  Bottom-Left: 1.9106

Secondary Peak Relative Position: x=25.93, y=38.99
val Heatmap Components:
  mse_loss: 0.0463
  separation_loss: 4.6722
  peak_separation_loss: 0.7925
  edge_suppression_loss: -0.0034
  peak_enhancement_loss: 0.8604
  peak_to_second_ratio_loss: 0.0611
  avg_peak_to_second_ratio: 1.9049
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000400
New best model saved with peak-to-second ratio: 2.1657
Early stopping: No improvement for 11/15 epochs. Best: 0.8500, Current: 0.7923

Epoch 20/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:07<00:00,  7.47s/it] 
train Loss: 6.3053, Seg Loss: 0.3457, Heatmap Loss: 43.4975, Geometric Loss: 0.8603
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.5911 🟢 (179.6% of target 2.0)
  avg_peak_to_mean_ratio: 138.7247
  avg_peak_to_second_ratio: 2.0030
  detection_rate: 0.9118 🟡 (91.2% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2100
  p2s_ratio_max: 3.0717
  p2s_ratio_std: 0.4573
  secondary_peak_distance: 182.7429
  primary_peak_sharpness: 0.4883
  primary_peak_isolation: 10.8947

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1647
  Top-Right: 1.7286
  Bottom-Right: 2.0998
  Bottom-Left: 2.0190

Secondary Peak Relative Position: x=14.48, y=22.84
train Heatmap Components:
  mse_loss: 0.0738
  separation_loss: 8.3914
  peak_separation_loss: 3.6025
  edge_suppression_loss: -0.0074
  peak_enhancement_loss: 0.9896
  peak_to_second_ratio_loss: 0.4044
  avg_peak_to_second_ratio: 1.8382
  detection_rate_loss: 0.0666
  segmentation_guidance_loss: 0.0247
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.15s/it] 
val Loss: 3.7081, Seg Loss: 0.3997, Heatmap Loss: 24.0615, Geometric Loss: 0.5698
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.3222 🟢 (166.1% of target 2.0)
  avg_peak_to_mean_ratio: 94.6674
  avg_peak_to_second_ratio: 2.0370
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.7163
  p2s_ratio_max: 2.6569
  p2s_ratio_std: 0.2460
  secondary_peak_distance: 225.4723
  primary_peak_sharpness: 0.4432
  primary_peak_isolation: 20.1030

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9752
  Top-Right: 1.9101
  Bottom-Right: 2.3455
  Bottom-Left: 1.9172

Secondary Peak Relative Position: x=-15.62, y=56.85
val Heatmap Components:
  mse_loss: 0.0480
  separation_loss: 4.6722
  peak_separation_loss: 0.7729
  edge_suppression_loss: -0.0037
  peak_enhancement_loss: 0.9100
  peak_to_second_ratio_loss: 0.0085
  avg_peak_to_second_ratio: 1.9732
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000400

=== Automatic Hyperparameter Tuning ===
Decreasing heatmap weight to balance with other metrics
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 0.09 -> 0.50, geometric_weight: 1.81 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 12/15 epochs. Best: 0.8500, Current: 0.7920
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 20 to prevent memory issues
Model evaluated at epoch 20
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Training complete in 46m 59s
Best val loss: 2.6735
Best peak-to-second ratio: 2.1657
Skipping final model visualization in TensorBoard to conserve memory

=== Creating Checkpoint Ensemble ===
Added best combined model to ensemble
Added best peak-to-second ratio model to ensemble
Added best detection rate model to ensemble
Added best loss model to ensemble
Added final model to ensemble
Skipping ensemble visualization to conserve memory...
Ensemble model evaluated
Ensemble output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])
Ensemble model saved to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v5.2(3rd attempt)\ensemble_model_phase1.pth
Warning: Could not create checkpoint ensemble: name 'ensemble_vis_path' is not defined

=== Generating Phase 1 Analysis Report ===
Warning: Could not generate analysis report: name 'sys' is not defined

=== Phase 2: Proportional improvement with targeted convergence ===
Saving outputs to v5.2(3rd attempt) folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Skipping model graph visualization in TensorBoard to conserve memory
Generating data augmentation visualizations...
Augmentation visualization directory created at C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(3rd attempt)\augmentations
Generated augmentation visualizations in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(3rd attempt)\augmentations
Epoch 1/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.43s/it] 
train Loss: 81.9503, Seg Loss: 0.3894, Heatmap Loss: 54.1441, Geometric Loss: 0.8621
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.1258 🟢 (156.3% of target 2.0)
  avg_peak_to_mean_ratio: 17.8432
  avg_peak_to_second_ratio: 1.6121
  detection_rate: 0.9081 🟡 (90.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0387
  p2s_ratio_max: 2.2796
  p2s_ratio_std: 0.3552
  secondary_peak_distance: 187.7309
  primary_peak_sharpness: 0.4635
  primary_peak_isolation: 3.5860

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8244
  Top-Right: 1.4941
  Bottom-Right: 1.5028
  Bottom-Left: 1.6272

Secondary Peak Relative Position: x=22.30, y=10.89
train Heatmap Components:
  mse_loss: 0.2317
  separation_loss: 8.0035
  peak_separation_loss: 25.0560
  edge_suppression_loss: -0.0100
  peak_enhancement_loss: 1.1598
  peak_to_second_ratio_loss: 0.2271
  avg_peak_to_second_ratio: 1.5733
  detection_rate_loss: 0.0716
  segmentation_guidance_loss: 0.5239
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.18s/it] 
val Loss: 25.4554, Seg Loss: 0.3700, Heatmap Loss: 16.5714, Geometric Loss: 0.5709
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.6405 🟢 (132.0% of target 2.0)
  avg_peak_to_mean_ratio: 31.2844
  avg_peak_to_second_ratio: 1.6607
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3687
  p2s_ratio_max: 2.1051
  p2s_ratio_std: 0.2114
  secondary_peak_distance: 231.1614
  primary_peak_sharpness: 0.3838
  primary_peak_isolation: 3.0147

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.5745
  Top-Right: 1.8289
  Bottom-Right: 1.6713
  Bottom-Left: 1.5681

Secondary Peak Relative Position: x=-11.28, y=8.65
val Heatmap Components:
  mse_loss: 0.1245
  separation_loss: 4.6722
  peak_separation_loss: 3.1062
  edge_suppression_loss: -0.0046
  peak_enhancement_loss: 0.9096
  peak_to_second_ratio_loss: 0.0025
  avg_peak_to_second_ratio: 1.6226
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000200
New best model saved with loss: 25.4554
New best model saved with peak-to-second ratio: 1.6607
New best model saved with detection rate: 1.0000
New best model saved with combined score: 0.8113 (loss: 25.4554, p2s: 1.6607, detection: 1.0000)
Early stopping: New best score: 0.8113

Epoch 2/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.42s/it] 
train Loss: 368.8078, Seg Loss: 0.3884, Heatmap Loss: 245.3922, Geometric Loss: 0.8278
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3695 🟢 (118.5% of target 2.0)
  avg_peak_to_mean_ratio: 63.5122
  avg_peak_to_second_ratio: 1.5669
  detection_rate: 0.9301 🟡 (93.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0137
  p2s_ratio_max: 2.1374
  p2s_ratio_std: 0.3235
  secondary_peak_distance: 192.5102
  primary_peak_sharpness: 0.4647
  primary_peak_isolation: 3.0057

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.5697
  Top-Right: 1.3643
  Bottom-Right: 1.7226
  Bottom-Left: 1.6112

Secondary Peak Relative Position: x=-45.61, y=-40.69
train Heatmap Components:
  mse_loss: 0.1231
  separation_loss: 7.3670
  peak_separation_loss: 60.0931
  edge_suppression_loss: 0.0014
  peak_enhancement_loss: 0.8590
  peak_to_second_ratio_loss: 0.2118
  avg_peak_to_second_ratio: 1.4374
  detection_rate_loss: 0.0637
  segmentation_guidance_loss: 0.2227
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.17s/it] 
val Loss: 22.3756, Seg Loss: 0.3840, Heatmap Loss: 14.4980, Geometric Loss: 0.6116
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1691 🟢 (108.5% of target 2.0)
  avg_peak_to_mean_ratio: 19.0024
  avg_peak_to_second_ratio: 1.7387
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.4015
  p2s_ratio_max: 2.1896
  p2s_ratio_std: 0.2383
  secondary_peak_distance: 230.2782
  primary_peak_sharpness: 0.3737
  primary_peak_isolation: 3.0000

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7527
  Top-Right: 1.8554
  Bottom-Right: 1.8532
  Bottom-Left: 1.4933

Secondary Peak Relative Position: x=-17.54, y=15.62
val Heatmap Components:
  mse_loss: 0.0916
  separation_loss: 4.6722
  peak_separation_loss: 0.8791
  edge_suppression_loss: 0.0035
  peak_enhancement_loss: 0.7319
  peak_to_second_ratio_loss: 0.0067
  avg_peak_to_second_ratio: 1.6253
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000350
New best model saved with loss: 22.3756
New best model saved with peak-to-second ratio: 1.7387
New best model saved with combined score: 0.8330 (loss: 22.3756, p2s: 1.7387, detection: 1.0000)
Early stopping: New best score: 0.8330

Epoch 3/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:11<00:00,  7.74s/it] 
train Loss: 202.9643, Seg Loss: 0.3518, Heatmap Loss: 134.8495, Geometric Loss: 0.8459
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.9536 🟡 (97.7% of target 2.0)
  avg_peak_to_mean_ratio: 28.2644
  avg_peak_to_second_ratio: 1.6320
  detection_rate: 0.9007 🟡 (90.1% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0299
  p2s_ratio_max: 2.4108
  p2s_ratio_std: 0.4002
  secondary_peak_distance: 220.5487
  primary_peak_sharpness: 0.4427
  primary_peak_isolation: 7.1224

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.6263
  Top-Right: 1.4631
  Bottom-Right: 1.6049
  Bottom-Left: 1.8338

Secondary Peak Relative Position: x=0.91, y=9.70
train Heatmap Components:
  mse_loss: 0.1032
  separation_loss: 7.2461
  peak_separation_loss: 34.5453
  edge_suppression_loss: -0.0052
  peak_enhancement_loss: 1.0250
  peak_to_second_ratio_loss: 0.2898
  avg_peak_to_second_ratio: 1.5184
  detection_rate_loss: 0.1002
  segmentation_guidance_loss: 0.2325
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:12<00:00,  2.49s/it] 
val Loss: 34.6898, Seg Loss: 0.3970, Heatmap Loss: 22.6970, Geometric Loss: 0.6180
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.5993 🔴 (80.0% of target 2.0)
  avg_peak_to_mean_ratio: 18.2443
  avg_peak_to_second_ratio: 1.6560
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1806
  p2s_ratio_max: 2.2328
  p2s_ratio_std: 0.3509
  secondary_peak_distance: 253.4342
  primary_peak_sharpness: 0.3956
  primary_peak_isolation: 3.6884

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7664
  Top-Right: 1.8967
  Bottom-Right: 1.6950
  Bottom-Left: 1.2661

Secondary Peak Relative Position: x=-40.96, y=76.51
val Heatmap Components:
  mse_loss: 0.0644
  separation_loss: 4.6722
  peak_separation_loss: 3.4477
  edge_suppression_loss: -0.0052
  peak_enhancement_loss: 0.9242
  peak_to_second_ratio_loss: 0.0587
  avg_peak_to_second_ratio: 1.5719
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500
Early stopping: No improvement for 1/15 epochs. Best: 0.8330, Current: 0.7275

Epoch 4/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:20<00:00,  8.28s/it] 
train Loss: 90.6883, Seg Loss: 0.3680, Heatmap Loss: 59.9861, Geometric Loss: 0.8527
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1288 🟢 (106.4% of target 2.0)
  avg_peak_to_mean_ratio: 28.4118
  avg_peak_to_second_ratio: 1.7511
  detection_rate: 0.8971 🔴 (89.7% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0547
  p2s_ratio_max: 2.9301
  p2s_ratio_std: 0.5461
  secondary_peak_distance: 223.7000
  primary_peak_sharpness: 0.4491
  primary_peak_isolation: 5.0951

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9947
  Top-Right: 1.3362
  Bottom-Right: 1.9388
  Bottom-Left: 1.7347

Secondary Peak Relative Position: x=-19.06, y=38.76
train Heatmap Components:
  mse_loss: 0.0856
  separation_loss: 6.8458
  peak_separation_loss: 13.3840
  edge_suppression_loss: -0.0023
  peak_enhancement_loss: 0.9988
  peak_to_second_ratio_loss: 0.2553
  avg_peak_to_second_ratio: 1.5606
  detection_rate_loss: 0.0760
  segmentation_guidance_loss: 0.2154
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:20<00:00,  4.02s/it] 
val Loss: 28.1476, Seg Loss: 0.4029, Heatmap Loss: 18.3327, Geometric Loss: 0.6141
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.9134 🟡 (95.7% of target 2.0)
  avg_peak_to_mean_ratio: 29.9158
  avg_peak_to_second_ratio: 1.7030
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1094
  p2s_ratio_max: 2.2321
  p2s_ratio_std: 0.3690
  secondary_peak_distance: 246.4374
  primary_peak_sharpness: 0.4659
  primary_peak_isolation: 3.2417

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1479
  Top-Right: 1.5986
  Bottom-Right: 1.8196
  Bottom-Left: 1.2461

Secondary Peak Relative Position: x=-17.60, y=65.01
val Heatmap Components:
  mse_loss: 0.0498
  separation_loss: 4.6722
  peak_separation_loss: 2.1683
  edge_suppression_loss: -0.0012
  peak_enhancement_loss: 0.8480
  peak_to_second_ratio_loss: 0.1094
  avg_peak_to_second_ratio: 1.5636
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000412
Early stopping: No improvement for 2/15 epochs. Best: 0.8330, Current: 0.7844

Epoch 5/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:14<00:00,  7.91s/it] 
train Loss: 65.5570, Seg Loss: 0.3719, Heatmap Loss: 43.2280, Geometric Loss: 0.8578
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5142 🟢 (125.7% of target 2.0)
  avg_peak_to_mean_ratio: 82.7356
  avg_peak_to_second_ratio: 1.8886
  detection_rate: 0.9081 🟡 (90.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0372
  p2s_ratio_max: 2.8258
  p2s_ratio_std: 0.5019
  secondary_peak_distance: 208.9449
  primary_peak_sharpness: 0.5216
  primary_peak_isolation: 4.3310

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0168
  Top-Right: 1.4824
  Bottom-Right: 1.8363
  Bottom-Left: 2.2191

Secondary Peak Relative Position: x=0.58, y=39.49
train Heatmap Components:
  mse_loss: 0.0799
  separation_loss: 8.0919
  peak_separation_loss: 6.4591
  edge_suppression_loss: -0.0037
  peak_enhancement_loss: 1.0286
  peak_to_second_ratio_loss: 0.2534
  avg_peak_to_second_ratio: 1.6524
  detection_rate_loss: 0.0824
  segmentation_guidance_loss: 0.1684
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.13s/it] 
val Loss: 34.4177, Seg Loss: 0.4069, Heatmap Loss: 22.5125, Geometric Loss: 0.6050
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1502 🟢 (107.5% of target 2.0)
  avg_peak_to_mean_ratio: 32.3909
  avg_peak_to_second_ratio: 1.9033
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5143
  p2s_ratio_max: 2.2864
  p2s_ratio_std: 0.2251
  secondary_peak_distance: 234.7356
  primary_peak_sharpness: 0.3952
  primary_peak_isolation: 3.0984

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0087
  Top-Right: 1.8236
  Bottom-Right: 2.0300
  Bottom-Left: 1.7508

Secondary Peak Relative Position: x=13.91, y=50.24
val Heatmap Components:
  mse_loss: 0.0600
  separation_loss: 4.6722
  peak_separation_loss: 0.8578
  edge_suppression_loss: -0.0018
  peak_enhancement_loss: 0.9187
  peak_to_second_ratio_loss: 0.0078
  avg_peak_to_second_ratio: 1.7324
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000396
New best model saved with peak-to-second ratio: 1.9033

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=1.50, geometric_weight=0.40
Keeping current heatmap weight
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 1.50 -> 1.50, geometric_weight: 0.40 -> 0.50

=== Learning Rate Tuning ===
Loss plateau detected (change: 0.0078)
Reducing learning rate for fine-tuning
Adjusted learning rate: 0.000396 -> 0.000277
Early stopping: No improvement for 3/15 epochs. Best: 0.8330, Current: 0.7693
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 5 to prevent memory issues
Model evaluated at epoch 5
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 6/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:04<00:00,  7.34s/it] 
train Loss: 71.0860, Seg Loss: 0.3610, Heatmap Loss: 46.8578, Geometric Loss: 0.8764
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1957 🟢 (109.8% of target 2.0)
  avg_peak_to_mean_ratio: 40.5658
  avg_peak_to_second_ratio: 1.7583
  detection_rate: 0.9375 🟡 (93.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0833
  p2s_ratio_max: 2.5026
  p2s_ratio_std: 0.3745
  secondary_peak_distance: 195.1622
  primary_peak_sharpness: 0.5069
  primary_peak_isolation: 5.1958

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8095
  Top-Right: 1.5205
  Bottom-Right: 1.7264
  Bottom-Left: 1.9770

Secondary Peak Relative Position: x=14.90, y=46.03
train Heatmap Components:
  mse_loss: 0.0708
  separation_loss: 8.0421
  peak_separation_loss: 9.0176
  edge_suppression_loss: -0.0031
  peak_enhancement_loss: 0.9838
  peak_to_second_ratio_loss: 0.2051
  avg_peak_to_second_ratio: 1.6146
  detection_rate_loss: 0.0628
  segmentation_guidance_loss: 0.0123
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.21s/it] 
val Loss: 42.9731, Seg Loss: 0.4031, Heatmap Loss: 28.1873, Geometric Loss: 0.5783
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.6952 🔴 (84.8% of target 2.0)
  avg_peak_to_mean_ratio: 42.9472
  avg_peak_to_second_ratio: 1.5585
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2315
  p2s_ratio_max: 1.9875
  p2s_ratio_std: 0.2139
  secondary_peak_distance: 246.4393
  primary_peak_sharpness: 0.4365
  primary_peak_isolation: 3.0584

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4599
  Top-Right: 1.7310
  Bottom-Right: 1.4612
  Bottom-Left: 1.5819

Secondary Peak Relative Position: x=-1.84, y=50.03
val Heatmap Components:
  mse_loss: 0.0429
  separation_loss: 4.6722
  peak_separation_loss: 5.8693
  edge_suppression_loss: -0.0012
  peak_enhancement_loss: 0.8604
  peak_to_second_ratio_loss: 0.1305
  avg_peak_to_second_ratio: 1.5245
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000333
Early stopping: No improvement for 4/15 epochs. Best: 0.8330, Current: 0.6448

Epoch 7/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.36s/it] 
train Loss: 61.7759, Seg Loss: 0.3467, Heatmap Loss: 59.9611, Geometric Loss: 0.8351
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3716 🟢 (118.6% of target 2.0)
  avg_peak_to_mean_ratio: 199.8940
  avg_peak_to_second_ratio: 1.8073
  detection_rate: 0.9118 🟡 (91.2% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0558
  p2s_ratio_max: 2.8673
  p2s_ratio_std: 0.4835
  secondary_peak_distance: 191.7945
  primary_peak_sharpness: 0.5023
  primary_peak_isolation: 4.6611

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2292
  Top-Right: 1.5186
  Bottom-Right: 1.6726
  Bottom-Left: 1.8090

Secondary Peak Relative Position: x=25.26, y=46.54
train Heatmap Components:
  mse_loss: 0.0649
  separation_loss: 7.8917
  peak_separation_loss: 16.1062
  edge_suppression_loss: -0.0024
  peak_enhancement_loss: 0.9698
  peak_to_second_ratio_loss: 0.2491
  avg_peak_to_second_ratio: 1.6542
  detection_rate_loss: 0.0772
  segmentation_guidance_loss: 0.0463
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.20s/it] 
val Loss: 15.6092, Seg Loss: 0.4120, Heatmap Loss: 23.7226, Geometric Loss: 0.6069
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.2504 🟢 (112.5% of target 2.0)
  avg_peak_to_mean_ratio: 185.9010
  avg_peak_to_second_ratio: 1.8365
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.4414
  p2s_ratio_max: 2.1988
  p2s_ratio_std: 0.2110
  secondary_peak_distance: 217.1478
  primary_peak_sharpness: 0.5207
  primary_peak_isolation: 3.3292

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8965
  Top-Right: 1.9401
  Bottom-Right: 1.5504
  Bottom-Left: 1.9588

Secondary Peak Relative Position: x=44.15, y=19.69
val Heatmap Components:
  mse_loss: 0.0312
  separation_loss: 4.6722
  peak_separation_loss: 1.3709
  edge_suppression_loss: 0.0023
  peak_enhancement_loss: 0.8402
  peak_to_second_ratio_loss: 0.0212
  avg_peak_to_second_ratio: 1.8096
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000282
New best model saved with loss: 15.6092
New best model saved with combined score: 0.8500 (loss: 15.6092, p2s: 1.8365, detection: 1.0000)
Early stopping: New best score: 0.8500

Epoch 8/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:07<00:00,  7.49s/it] 
train Loss: 11.9247, Seg Loss: 0.3640, Heatmap Loss: 42.3986, Geometric Loss: 0.8184
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1947 🟢 (109.7% of target 2.0)
  avg_peak_to_mean_ratio: 48.9995
  avg_peak_to_second_ratio: 1.8130
  detection_rate: 0.9154 🟡 (91.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0614
  p2s_ratio_max: 2.6678
  p2s_ratio_std: 0.4388
  secondary_peak_distance: 192.2703
  primary_peak_sharpness: 0.4864
  primary_peak_isolation: 6.4252

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9439
  Top-Right: 1.5201
  Bottom-Right: 1.8269
  Bottom-Left: 1.9609

Secondary Peak Relative Position: x=29.11, y=26.93
train Heatmap Components:
  mse_loss: 0.0746
  separation_loss: 8.1351
  peak_separation_loss: 3.1560
  edge_suppression_loss: -0.0056
  peak_enhancement_loss: 1.0657
  peak_to_second_ratio_loss: 0.2580
  avg_peak_to_second_ratio: 1.5881
  detection_rate_loss: 0.0757
  segmentation_guidance_loss: 0.0685
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.19s/it] 
val Loss: 3.8794, Seg Loss: 0.3976, Heatmap Loss: 23.9033, Geometric Loss: 0.6472
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.0199 🟢 (101.0% of target 2.0)
  avg_peak_to_mean_ratio: 177.9726
  avg_peak_to_second_ratio: 2.0873
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.7463
  p2s_ratio_max: 2.5255
  p2s_ratio_std: 0.2529
  secondary_peak_distance: 240.4941
  primary_peak_sharpness: 0.4347
  primary_peak_isolation: 3.0237

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9015
  Top-Right: 1.9063
  Bottom-Right: 2.4124
  Bottom-Left: 2.1288

Secondary Peak Relative Position: x=11.71, y=40.49
val Heatmap Components:
  mse_loss: 0.0302
  separation_loss: 4.6722
  peak_separation_loss: 0.7124
  edge_suppression_loss: -0.0020
  peak_enhancement_loss: 0.8696
  peak_to_second_ratio_loss: 0.0000
  avg_peak_to_second_ratio: 1.7879
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000226
New best model saved with loss: 3.8794
New best model saved with peak-to-second ratio: 2.0873
Early stopping: No improvement for 1/15 epochs. Best: 0.8500, Current: 0.8500

Epoch 9/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:04<00:00,  7.33s/it] 
train Loss: 5.8307, Seg Loss: 0.3584, Heatmap Loss: 37.2953, Geometric Loss: 0.8738
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3963 🟢 (119.8% of target 2.0)
  avg_peak_to_mean_ratio: 58.0995
  avg_peak_to_second_ratio: 2.0190
  detection_rate: 0.9044 🟡 (90.4% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0341
  p2s_ratio_max: 2.7953
  p2s_ratio_std: 0.4800
  secondary_peak_distance: 199.8476
  primary_peak_sharpness: 0.4422
  primary_peak_isolation: 3.0175

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1802
  Top-Right: 1.6045
  Bottom-Right: 2.1082
  Bottom-Left: 2.1833

Secondary Peak Relative Position: x=21.30, y=22.22
train Heatmap Components:
  mse_loss: 0.0769
  separation_loss: 7.1231
  peak_separation_loss: 2.9571
  edge_suppression_loss: -0.0084
  peak_enhancement_loss: 1.0874
  peak_to_second_ratio_loss: 0.2511
  avg_peak_to_second_ratio: 1.6903
  detection_rate_loss: 0.0940
  segmentation_guidance_loss: 0.0648
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.21s/it] 
val Loss: 4.0440, Seg Loss: 0.3804, Heatmap Loss: 24.2267, Geometric Loss: 0.6064
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1839 🟢 (109.2% of target 2.0)
  avg_peak_to_mean_ratio: 30.6621
  avg_peak_to_second_ratio: 2.3237
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.8667
  p2s_ratio_max: 2.9526
  p2s_ratio_std: 0.3056
  secondary_peak_distance: 226.1446
  primary_peak_sharpness: 0.4348
  primary_peak_isolation: 3.0000

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1939
  Top-Right: 2.0140
  Bottom-Right: 2.7164
  Bottom-Left: 2.3705

Secondary Peak Relative Position: x=23.72, y=11.32
val Heatmap Components:
  mse_loss: 0.0449
  separation_loss: 4.6722
  peak_separation_loss: 0.7778
  edge_suppression_loss: -0.0064
  peak_enhancement_loss: 0.9632
  peak_to_second_ratio_loss: 0.0177
  avg_peak_to_second_ratio: 1.8528
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000181
New best model saved with peak-to-second ratio: 2.3237
Early stopping: No improvement for 2/15 epochs. Best: 0.8500, Current: 0.8436

Epoch 10/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.38s/it] 
train Loss: 6.3031, Seg Loss: 0.3394, Heatmap Loss: 44.4893, Geometric Loss: 0.8627
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5367 🟢 (126.8% of target 2.0)
  avg_peak_to_mean_ratio: 62.6728
  avg_peak_to_second_ratio: 2.0066
  detection_rate: 0.9265 🟡 (92.6% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1326
  p2s_ratio_max: 2.8241
  p2s_ratio_std: 0.4579
  secondary_peak_distance: 201.7482
  primary_peak_sharpness: 0.5729
  primary_peak_isolation: 3.5608

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2579
  Top-Right: 1.5591
  Bottom-Right: 2.0927
  Bottom-Left: 2.1165

Secondary Peak Relative Position: x=27.91, y=50.68
train Heatmap Components:
  mse_loss: 0.0592
  separation_loss: 8.5280
  peak_separation_loss: 2.5944
  edge_suppression_loss: -0.0054
  peak_enhancement_loss: 1.0436
  peak_to_second_ratio_loss: 0.2110
  avg_peak_to_second_ratio: 1.7538
  detection_rate_loss: 0.0695
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.24s/it] 
val Loss: 3.6867, Seg Loss: 0.3806, Heatmap Loss: 24.1033, Geometric Loss: 0.5932
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.2328 🟢 (111.6% of target 2.0)
  avg_peak_to_mean_ratio: 53.8487
  avg_peak_to_second_ratio: 2.0192
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6492
  p2s_ratio_max: 2.3578
  p2s_ratio_std: 0.2033
  secondary_peak_distance: 203.9920
  primary_peak_sharpness: 0.5830
  primary_peak_isolation: 3.1461

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0248
  Top-Right: 1.7606
  Bottom-Right: 2.1942
  Bottom-Left: 2.0973

Secondary Peak Relative Position: x=51.59, y=9.62
val Heatmap Components:
  mse_loss: 0.0345
  separation_loss: 4.6722
  peak_separation_loss: 1.6797
  edge_suppression_loss: -0.0015
  peak_enhancement_loss: 0.8779
  peak_to_second_ratio_loss: 0.0000
  avg_peak_to_second_ratio: 1.8651
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000144
New best model saved with loss: 3.6867

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=0.09, geometric_weight=1.68
Decreasing heatmap weight to balance with other metrics
Keeping current geometric weight
Adjusted weights: heatmap_weight: 0.09 -> 0.50, geometric_weight: 1.68 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 3/15 epochs. Best: 0.8500, Current: 0.8500
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 10 to prevent memory issues
Model evaluated at epoch 10
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 11/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:18<00:00,  8.15s/it] 
train Loss: 5.9829, Seg Loss: 0.3720, Heatmap Loss: 42.6405, Geometric Loss: 0.8438
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5684 🟢 (128.4% of target 2.0)
  avg_peak_to_mean_ratio: 83.4969
  avg_peak_to_second_ratio: 1.8318
  detection_rate: 0.8934 🔴 (89.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1344
  p2s_ratio_max: 2.5737
  p2s_ratio_std: 0.3879
  secondary_peak_distance: 196.8051
  primary_peak_sharpness: 0.6171
  primary_peak_isolation: 10.2441

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9409
  Top-Right: 1.5368
  Bottom-Right: 1.9435
  Bottom-Left: 1.9058

Secondary Peak Relative Position: x=40.82, y=29.85
train Heatmap Components:
  mse_loss: 0.0548
  separation_loss: 8.0702
  peak_separation_loss: 4.0968
  edge_suppression_loss: -0.0027
  peak_enhancement_loss: 1.0125
  peak_to_second_ratio_loss: 0.2685
  avg_peak_to_second_ratio: 1.7032
  detection_rate_loss: 0.0786
  segmentation_guidance_loss: 0.2423
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.16s/it] 
val Loss: 6.4699, Seg Loss: 0.3770, Heatmap Loss: 24.0051, Geometric Loss: 0.6078
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3572 🟢 (117.9% of target 2.0)
  avg_peak_to_mean_ratio: 43.6102
  avg_peak_to_second_ratio: 1.7723
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5563
  p2s_ratio_max: 2.0747
  p2s_ratio_std: 0.1547
  secondary_peak_distance: 232.8446
  primary_peak_sharpness: 0.6705
  primary_peak_isolation: 43.7545

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.6387
  Top-Right: 1.6924
  Bottom-Right: 1.8513
  Bottom-Left: 1.9067

Secondary Peak Relative Position: x=32.99, y=30.76
val Heatmap Components:
  mse_loss: 0.0299
  separation_loss: 4.6722
  peak_separation_loss: 1.0229
  edge_suppression_loss: 0.0009
  peak_enhancement_loss: 0.8154
  peak_to_second_ratio_loss: 0.0000
  avg_peak_to_second_ratio: 1.7515
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000116
Early stopping: No improvement for 4/15 epochs. Best: 0.8500, Current: 0.7291


Epoch 12/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.37s/it] 
train Loss: 6.5187, Seg Loss: 0.3475, Heatmap Loss: 47.6519, Geometric Loss: 0.8696
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8089 🟢 (140.4% of target 2.0)
  avg_peak_to_mean_ratio: 64.4234
  avg_peak_to_second_ratio: 1.8656
  detection_rate: 0.9301 🟡 (93.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1500
  p2s_ratio_max: 2.5999
  p2s_ratio_std: 0.3851
  secondary_peak_distance: 197.2067
  primary_peak_sharpness: 0.7162
  primary_peak_isolation: 8.0118

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9616
  Top-Right: 1.5653
  Bottom-Right: 2.0469
  Bottom-Left: 1.8885

Secondary Peak Relative Position: x=28.12, y=55.34
train Heatmap Components:
  mse_loss: 0.0493
  separation_loss: 9.0273
  peak_separation_loss: 2.6148
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
  secondary_peak_distance: 197.2067
  primary_peak_sharpness: 0.7162
  primary_peak_isolation: 8.0118

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9616
  Top-Right: 1.5653
  Bottom-Right: 2.0469
  Bottom-Left: 1.8885

Secondary Peak Relative Position: x=28.12, y=55.34
train Heatmap Components:
  mse_loss: 0.0493
  separation_loss: 9.0273
  peak_separation_loss: 2.6148
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
  Bottom-Right: 2.0469
  Bottom-Left: 1.8885

Secondary Peak Relative Position: x=28.12, y=55.34
train Heatmap Components:
  mse_loss: 0.0493
  separation_loss: 9.0273
  peak_separation_loss: 2.6148
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
train Heatmap Components:
  mse_loss: 0.0493
  separation_loss: 9.0273
  peak_separation_loss: 2.6148
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
  mse_loss: 0.0493
  separation_loss: 9.0273
  peak_separation_loss: 2.6148
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
  peak_separation_loss: 2.6148
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.9142
  peak_to_second_ratio_loss: 0.2195
  avg_peak_to_second_ratio: 1.7353
  detection_rate_loss: 0.0575
  segmentation_guidance_loss: 0.0371
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.18s/it] 
val Loss: 4.5923, Seg Loss: 0.3752, Heatmap Loss: 24.4499, Geometric Loss: 0.5875
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.4726 🟢 (123.6% of target 2.0)
  avg_peak_to_mean_ratio: 36.9827
  avg_peak_to_second_ratio: 1.7088
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5294
  p2s_ratio_max: 2.0000
  p2s_ratio_std: 0.1391
  secondary_peak_distance: 248.0472
  primary_peak_sharpness: 0.7343
  primary_peak_isolation: 25.7625

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.6183
  Top-Right: 1.7540
  Bottom-Right: 1.6239
  Bottom-Left: 1.8391

Secondary Peak Relative Position: x=10.66, y=36.72
val Heatmap Components:
  mse_loss: 0.0272
  separation_loss: 4.6722
  peak_separation_loss: 0.7386
  edge_suppression_loss: 0.0026
  peak_enhancement_loss: 0.7757
  peak_to_second_ratio_loss: 0.0179
  avg_peak_to_second_ratio: 1.6923
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000139
Early stopping: No improvement for 5/15 epochs. Best: 0.8500, Current: 0.7878


Epoch 13/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.38s/it]
train Loss: 6.1212, Seg Loss: 0.3553, Heatmap Loss: 36.4845, Geometric Loss: 0.8440
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.7939 🟢 (139.7% of target 2.0)
  avg_peak_to_mean_ratio: 189.0991
  avg_peak_to_second_ratio: 1.9487
  detection_rate: 0.8934 🔴 (89.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0391
  p2s_ratio_max: 3.0169
  p2s_ratio_std: 0.5128
  secondary_peak_distance: 211.0794
  primary_peak_sharpness: 0.6817
  primary_peak_isolation: 5.2766

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2487
  Top-Right: 1.5361
  Bottom-Right: 1.9813
  Bottom-Left: 2.0289

Secondary Peak Relative Position: x=28.76, y=55.37
train Heatmap Components:
  mse_loss: 0.0543
  separation_loss: 6.7945
  peak_separation_loss: 6.0576
  edge_suppression_loss: -0.0013
  peak_enhancement_loss: 0.9686
  peak_to_second_ratio_loss: 0.3185
  avg_peak_to_second_ratio: 1.7340
  detection_rate_loss: 0.0914
  segmentation_guidance_loss: 0.0445
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.20s/it] 
val Loss: 3.9736, Seg Loss: 0.3779, Heatmap Loss: 24.2145, Geometric Loss: 0.6024
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.6353 🟢 (131.8% of target 2.0)
  avg_peak_to_mean_ratio: 49.0388
  avg_peak_to_second_ratio: 1.8432
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6116
  p2s_ratio_max: 2.1061
  p2s_ratio_std: 0.1465
  secondary_peak_distance: 245.7357
  primary_peak_sharpness: 0.7605
  primary_peak_isolation: 12.8340

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9041
  Top-Right: 1.8850
  Bottom-Right: 1.6557
  Bottom-Left: 1.9279

Secondary Peak Relative Position: x=25.54, y=40.57
val Heatmap Components:
  mse_loss: 0.0292
  separation_loss: 4.6722
  peak_separation_loss: 0.6618
  edge_suppression_loss: 0.0027
  peak_enhancement_loss: 0.7763
  peak_to_second_ratio_loss: 0.0033
  avg_peak_to_second_ratio: 1.7863
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000166
Early stopping: No improvement for 6/15 epochs. Best: 0.8500, Current: 0.8383

Epoch 14/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.37s/it] 
train Loss: 7.2218, Seg Loss: 0.3435, Heatmap Loss: 42.6925, Geometric Loss: 0.8446
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8946 🟢 (144.7% of target 2.0)
  avg_peak_to_mean_ratio: 63.5567
  avg_peak_to_second_ratio: 1.9787
  detection_rate: 0.9485 🟡 (94.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3657
  p2s_ratio_max: 2.5841
  p2s_ratio_std: 0.3272
  secondary_peak_distance: 228.4039
  primary_peak_sharpness: 0.6808
  primary_peak_isolation: 3.1319

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1127
  Top-Right: 1.7344
  Bottom-Right: 2.0157
  Bottom-Left: 2.0519

Secondary Peak Relative Position: x=-2.74, y=97.10
train Heatmap Components:
  mse_loss: 0.0514
  separation_loss: 8.1352
  peak_separation_loss: 3.2606
  edge_suppression_loss: 0.0003
  peak_enhancement_loss: 0.8888
  peak_to_second_ratio_loss: 0.1564
  avg_peak_to_second_ratio: 1.7740
  detection_rate_loss: 0.0480
  segmentation_guidance_loss: 0.0239
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 3.7704, Seg Loss: 0.3863, Heatmap Loss: 23.8985, Geometric Loss: 0.6134
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.4910 🟢 (124.6% of target 2.0)
  avg_peak_to_mean_ratio: 52.3678
  avg_peak_to_second_ratio: 1.8740
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6683
  p2s_ratio_max: 2.0899
  p2s_ratio_std: 0.0990
  secondary_peak_distance: 262.0971
  primary_peak_sharpness: 0.6756
  primary_peak_isolation: 3.2108

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8700
  Top-Right: 1.8895
  Bottom-Right: 1.8839
  Bottom-Left: 1.8527

Secondary Peak Relative Position: x=-0.50, y=46.15
val Heatmap Components:
  mse_loss: 0.0256
  separation_loss: 4.6722
  peak_separation_loss: 0.6552
  edge_suppression_loss: 0.0037
  peak_enhancement_loss: 0.7398
  peak_to_second_ratio_loss: 0.0060
  avg_peak_to_second_ratio: 1.7908
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000200
Early stopping: No improvement for 7/15 epochs. Best: 0.8500, Current: 0.8466


Epoch 15/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.40s/it] 
train Loss: 6.1685, Seg Loss: 0.3448, Heatmap Loss: 40.6413, Geometric Loss: 0.8508
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.7110 🟢 (135.5% of target 2.0)
  avg_peak_to_mean_ratio: 93.2458
  avg_peak_to_second_ratio: 1.9490
  detection_rate: 0.9228 🟡 (92.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1730
  p2s_ratio_max: 2.6455
  p2s_ratio_std: 0.3998
  secondary_peak_distance: 244.7779
  primary_peak_sharpness: 0.6339
  primary_peak_isolation: 3.1137

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1395
  Top-Right: 1.7116
  Bottom-Right: 1.9017
  Bottom-Left: 2.0431

Secondary Peak Relative Position: x=-20.30, y=93.28
train Heatmap Components:
  mse_loss: 0.0428
  separation_loss: 7.8853
  peak_separation_loss: 1.7847
  edge_suppression_loss: 0.0008
  peak_enhancement_loss: 0.8757
  peak_to_second_ratio_loss: 0.2560
  avg_peak_to_second_ratio: 1.7343
  detection_rate_loss: 0.0676
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 4.2361, Seg Loss: 0.3781, Heatmap Loss: 23.6139, Geometric Loss: 0.6133
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.4855 🟢 (124.3% of target 2.0)
  avg_peak_to_mean_ratio: 32.1276
  avg_peak_to_second_ratio: 1.8509
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5222
  p2s_ratio_max: 2.1532
  p2s_ratio_std: 0.1881
  secondary_peak_distance: 256.3176
  primary_peak_sharpness: 0.6583
  primary_peak_isolation: 4.4605

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8755
  Top-Right: 1.9680
  Bottom-Right: 1.9731
  Bottom-Left: 1.5871

Secondary Peak Relative Position: x=-0.50, y=40.37
val Heatmap Components:
  mse_loss: 0.0269
  separation_loss: 4.6722
  peak_separation_loss: 0.7222
  edge_suppression_loss: 0.0031
  peak_enhancement_loss: 0.7559
  peak_to_second_ratio_loss: 0.0343
  avg_peak_to_second_ratio: 1.7715
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000198

=== Automatic Hyperparameter Tuning ===
Keeping current heatmap weight
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 0.09 -> 0.50, geometric_weight: 1.92 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 8/15 epochs. Best: 0.8500, Current: 0.8276
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 15 to prevent memory issues
Model evaluated at epoch 15
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 16/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:07<00:00,  7.50s/it] 
train Loss: 5.5844, Seg Loss: 0.3655, Heatmap Loss: 39.9433, Geometric Loss: 0.8379
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8048 🟢 (140.2% of target 2.0)
  avg_peak_to_mean_ratio: 72.1099
  avg_peak_to_second_ratio: 2.0357
  detection_rate: 0.9154 🟡 (91.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0858
  p2s_ratio_max: 2.8988
  p2s_ratio_std: 0.4676
  secondary_peak_distance: 226.1351
  primary_peak_sharpness: 0.6515
  primary_peak_isolation: 4.7337

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1472
  Top-Right: 1.7012
  Bottom-Right: 2.0498
  Bottom-Left: 2.2447

Secondary Peak Relative Position: x=-2.92, y=68.76
train Heatmap Components:
  mse_loss: 0.0451
  separation_loss: 7.7336
  peak_separation_loss: 1.3407
  edge_suppression_loss: -0.0007
  peak_enhancement_loss: 0.9282
  peak_to_second_ratio_loss: 0.2777
  avg_peak_to_second_ratio: 1.8236
  detection_rate_loss: 0.0690
  segmentation_guidance_loss: 0.1499
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:12<00:00,  2.50s/it] 
val Loss: 3.5841, Seg Loss: 0.3691, Heatmap Loss: 24.0349, Geometric Loss: 0.6281
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.4826 🟢 (124.1% of target 2.0)
  avg_peak_to_mean_ratio: 39.8471
  avg_peak_to_second_ratio: 1.9827
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5890
  p2s_ratio_max: 2.6210
  p2s_ratio_std: 0.3365
  secondary_peak_distance: 262.3472
  primary_peak_sharpness: 0.6351
  primary_peak_isolation: 6.7315

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8858
  Top-Right: 1.8643
  Bottom-Right: 2.4945
  Bottom-Left: 1.6861

Secondary Peak Relative Position: x=-7.97, y=53.78
val Heatmap Components:
  mse_loss: 0.0248
  separation_loss: 4.6722
  peak_separation_loss: 0.6977
  edge_suppression_loss: 0.0027
  peak_enhancement_loss: 0.7714
  peak_to_second_ratio_loss: 0.0257
  avg_peak_to_second_ratio: 1.8291
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000158
New best model saved with loss: 3.5841
Early stopping: No improvement for 9/15 epochs. Best: 0.8500, Current: 0.8500

Epoch 17/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:08<00:00,  7.58s/it] 
train Loss: 5.9015, Seg Loss: 0.3274, Heatmap Loss: 34.1090, Geometric Loss: 0.8485
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8131 🟢 (140.7% of target 2.0)
  avg_peak_to_mean_ratio: 119.3669
  avg_peak_to_second_ratio: 2.1041
  detection_rate: 0.9118 🟡 (91.2% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1880
  p2s_ratio_max: 3.0484
  p2s_ratio_std: 0.5148
  secondary_peak_distance: 239.0526
  primary_peak_sharpness: 0.5720
  primary_peak_isolation: 8.9907

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2257
  Top-Right: 1.7309
  Bottom-Right: 2.3730
  Bottom-Left: 2.0866

Secondary Peak Relative Position: x=-45.21, y=51.46
train Heatmap Components:
  mse_loss: 0.0469
  separation_loss: 6.5435
  peak_separation_loss: 2.1013
  edge_suppression_loss: -0.0002
  peak_enhancement_loss: 0.9322
  peak_to_second_ratio_loss: 0.2787
  avg_peak_to_second_ratio: 1.7616
  detection_rate_loss: 0.0666
  segmentation_guidance_loss: 0.0421
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 3.7134, Seg Loss: 0.3815, Heatmap Loss: 24.2578, Geometric Loss: 0.6248
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.5334 🟢 (126.7% of target 2.0)
  avg_peak_to_mean_ratio: 40.2215
  avg_peak_to_second_ratio: 2.2400
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.6456
  p2s_ratio_max: 3.2751
  p2s_ratio_std: 0.5013
  secondary_peak_distance: 262.0983
  primary_peak_sharpness: 0.5826
  primary_peak_isolation: 10.6496

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9773
  Top-Right: 2.1884
  Bottom-Right: 2.9919
  Bottom-Left: 1.8026

Secondary Peak Relative Position: x=-52.79, y=97.82
val Heatmap Components:
  mse_loss: 0.0310
  separation_loss: 4.6722
  peak_separation_loss: 0.7598
  edge_suppression_loss: 0.0014
  peak_enhancement_loss: 0.7724
  peak_to_second_ratio_loss: 0.0618
  avg_peak_to_second_ratio: 1.8305
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000127
Early stopping: No improvement for 10/15 epochs. Best: 0.8500, Current: 0.8446

Epoch 18/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.36s/it] 
train Loss: 4.9488, Seg Loss: 0.3472, Heatmap Loss: 33.2737, Geometric Loss: 0.8344
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.7865 🟢 (139.3% of target 2.0)
  avg_peak_to_mean_ratio: 131.0688
  avg_peak_to_second_ratio: 2.1659
  detection_rate: 0.8824 🔴 (88.2% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1050
  p2s_ratio_max: 3.5718
  p2s_ratio_std: 0.6798
  secondary_peak_distance: 243.4702
  primary_peak_sharpness: 0.4953
  primary_peak_isolation: 10.8228

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.5111
  Top-Right: 1.7917
  Bottom-Right: 2.3544
  Bottom-Left: 2.0065

Secondary Peak Relative Position: x=-64.15, y=23.49
train Heatmap Components:
  mse_loss: 0.0557
  separation_loss: 6.2112
  peak_separation_loss: 2.4016
  edge_suppression_loss: -0.0033
  peak_enhancement_loss: 0.9961
  peak_to_second_ratio_loss: 0.4354
  avg_peak_to_second_ratio: 1.7419
  detection_rate_loss: 0.1032
  segmentation_guidance_loss: 0.1456
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.23s/it] 
val Loss: 4.0000, Seg Loss: 0.3763, Heatmap Loss: 24.4400, Geometric Loss: 0.6411
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8633 🟢 (143.2% of target 2.0)
  avg_peak_to_mean_ratio: 65.4349
  avg_peak_to_second_ratio: 2.4448
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.8264
  p2s_ratio_max: 3.4019
  p2s_ratio_std: 0.4905
  secondary_peak_distance: 245.5367
  primary_peak_sharpness: 0.6292
  primary_peak_isolation: 3.1241

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8990
  Top-Right: 2.3754
  Bottom-Right: 3.1300
  Bottom-Left: 2.3747

Secondary Peak Relative Position: x=-39.63, y=52.57
val Heatmap Components:
  mse_loss: 0.0367
  separation_loss: 4.6722
  peak_separation_loss: 0.7647
  edge_suppression_loss: -0.0007
  peak_enhancement_loss: 0.8428
  peak_to_second_ratio_loss: 0.0047
  avg_peak_to_second_ratio: 1.8429
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000101
New best model saved with peak-to-second ratio: 2.4448
Early stopping: No improvement for 11/15 epochs. Best: 0.8500, Current: 0.8326

Epoch 19/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.39s/it] 
train Loss: 10.6978, Seg Loss: 0.3440, Heatmap Loss: 49.9981, Geometric Loss: 0.9094
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.0740 🟢 (153.7% of target 2.0)
  avg_peak_to_mean_ratio: 126.0927
  avg_peak_to_second_ratio: 2.1571
  detection_rate: 0.9301 🟡 (93.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2164
  p2s_ratio_max: 2.8993
  p2s_ratio_std: 0.4653
  secondary_peak_distance: 224.7196
  primary_peak_sharpness: 0.6934
  primary_peak_isolation: 12.8714

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2853
  Top-Right: 1.7965
  Bottom-Right: 2.2465
  Bottom-Left: 2.3000

Secondary Peak Relative Position: x=-30.02, y=55.86
train Heatmap Components:
  mse_loss: 0.0361
  separation_loss: 9.5280
  peak_separation_loss: 1.0384
  edge_suppression_loss: -0.0003
  peak_enhancement_loss: 0.8675
  peak_to_second_ratio_loss: 0.2338
  avg_peak_to_second_ratio: 1.8247
  detection_rate_loss: 0.0527
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.18s/it] 
val Loss: 3.7644, Seg Loss: 0.3786, Heatmap Loss: 23.9625, Geometric Loss: 0.5619
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.7885 🟢 (139.4% of target 2.0)
  avg_peak_to_mean_ratio: 112.9285
  avg_peak_to_second_ratio: 2.1132
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.7811
  p2s_ratio_max: 2.3771
  p2s_ratio_std: 0.1683
  secondary_peak_distance: 251.1502
  primary_peak_sharpness: 0.6792
  primary_peak_isolation: 51.8291

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0273
  Top-Right: 1.9901
  Bottom-Right: 2.2428
  Bottom-Left: 2.1928

Secondary Peak Relative Position: x=8.79, y=37.96
val Heatmap Components:
  mse_loss: 0.0239
  separation_loss: 4.6722
  peak_separation_loss: 0.7565
  edge_suppression_loss: 0.0017
  peak_enhancement_loss: 0.7916
  peak_to_second_ratio_loss: 0.0166
  avg_peak_to_second_ratio: 1.8520
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000122
Early stopping: No improvement for 12/15 epochs. Best: 0.8500, Current: 0.8425

Epoch 20/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.46s/it] 
train Loss: 5.9897, Seg Loss: 0.3310, Heatmap Loss: 39.8055, Geometric Loss: 0.8834
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.9870 🟢 (149.4% of target 2.0)
  avg_peak_to_mean_ratio: 66.6826
  avg_peak_to_second_ratio: 2.1191
  detection_rate: 0.9191 🟡 (91.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1157
  p2s_ratio_max: 3.0931
  p2s_ratio_std: 0.5277
  secondary_peak_distance: 237.1966
  primary_peak_sharpness: 0.6482
  primary_peak_isolation: 13.1789

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3470
  Top-Right: 1.8690
  Bottom-Right: 2.0890
  Bottom-Left: 2.1716

Secondary Peak Relative Position: x=-9.88, y=52.37
train Heatmap Components:
  mse_loss: 0.0413
  separation_loss: 7.7457
  peak_separation_loss: 1.5768
  edge_suppression_loss: -0.0003
  peak_enhancement_loss: 0.8752
  peak_to_second_ratio_loss: 0.3232
  avg_peak_to_second_ratio: 1.7884
  detection_rate_loss: 0.0710
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.23s/it] 
val Loss: 3.6270, Seg Loss: 0.3758, Heatmap Loss: 23.5401, Geometric Loss: 0.5431
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8196 🟢 (141.0% of target 2.0)
  avg_peak_to_mean_ratio: 91.3916
  avg_peak_to_second_ratio: 2.1055
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.8793
  p2s_ratio_max: 2.3540
  p2s_ratio_std: 0.1490
  secondary_peak_distance: 253.3561
  primary_peak_sharpness: 0.6822
  primary_peak_isolation: 52.2422

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9833
  Top-Right: 2.1353
  Bottom-Right: 2.0688
  Bottom-Left: 2.2345

Secondary Peak Relative Position: x=8.81, y=40.18
val Heatmap Components:
  mse_loss: 0.0254
  separation_loss: 4.6722
  peak_separation_loss: 0.7876
  edge_suppression_loss: 0.0015
  peak_enhancement_loss: 0.7904
  peak_to_second_ratio_loss: 0.0305
  avg_peak_to_second_ratio: 1.8662
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000146

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=0.09, geometric_weight=1.89
Decreasing heatmap weight to balance with other metrics
Keeping current geometric weight
Adjusted weights: heatmap_weight: 0.09 -> 0.50, geometric_weight: 1.89 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 13/15 epochs. Best: 0.8500, Current: 0.8482
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 20 to prevent memory issues
Model evaluated at epoch 20
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 21/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.41s/it] 
train Loss: 5.8639, Seg Loss: 0.3290, Heatmap Loss: 41.9988, Geometric Loss: 0.8435
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.1560 🟢 (157.8% of target 2.0)
  avg_peak_to_mean_ratio: 75.9622
  avg_peak_to_second_ratio: 2.1434
  detection_rate: 0.9007 🟡 (90.1% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1355
  p2s_ratio_max: 2.9200
  p2s_ratio_std: 0.4999
  secondary_peak_distance: 221.6394
  primary_peak_sharpness: 0.6643
  primary_peak_isolation: 10.6263

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3254
  Top-Right: 1.7938
  Bottom-Right: 2.2099
  Bottom-Left: 2.2446

Secondary Peak Relative Position: x=-20.20, y=64.07
train Heatmap Components:
  mse_loss: 0.0424
  separation_loss: 8.1177
  peak_separation_loss: 1.3501
  edge_suppression_loss: -0.0016
  peak_enhancement_loss: 0.9132
  peak_to_second_ratio_loss: 0.3399
  avg_peak_to_second_ratio: 1.8616
  detection_rate_loss: 0.0778
  segmentation_guidance_loss: 0.0680
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.15s/it] 
val Loss: 3.6854, Seg Loss: 0.3775, Heatmap Loss: 23.6204, Geometric Loss: 0.5730
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.8654 🟢 (143.3% of target 2.0)
  avg_peak_to_mean_ratio: 115.6368
  avg_peak_to_second_ratio: 2.0298
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.7653
  p2s_ratio_max: 2.3401
  p2s_ratio_std: 0.1567
  secondary_peak_distance: 261.0235
  primary_peak_sharpness: 0.7006
  primary_peak_isolation: 56.1070

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0880
  Top-Right: 2.1655
  Bottom-Right: 1.9160
  Bottom-Left: 1.9496

Secondary Peak Relative Position: x=-0.50, y=45.07
val Heatmap Components:
  mse_loss: 0.0297
  separation_loss: 4.6722
  peak_separation_loss: 0.7794
  edge_suppression_loss: 0.0005
  peak_enhancement_loss: 0.8212
  peak_to_second_ratio_loss: 0.0165
  avg_peak_to_second_ratio: 1.8813
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000175
Early stopping: No improvement for 14/15 epochs. Best: 0.8500, Current: 0.8458

Epoch 22/40
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.36s/it] 
train Loss: 6.0944, Seg Loss: 0.3443, Heatmap Loss: 45.1483, Geometric Loss: 0.8604
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.2407 🟢 (162.0% of target 2.0)
  avg_peak_to_mean_ratio: 156.7045
  avg_peak_to_second_ratio: 2.0965
  detection_rate: 0.9375 🟡 (93.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2930
  p2s_ratio_max: 2.8703
  p2s_ratio_std: 0.4369
  secondary_peak_distance: 232.9334
  primary_peak_sharpness: 0.7108
  primary_peak_isolation: 8.2546

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2315
  Top-Right: 1.8496
  Bottom-Right: 2.1989
  Bottom-Left: 2.1059

Secondary Peak Relative Position: x=-34.85, y=71.51
train Heatmap Components:
  mse_loss: 0.0403
  separation_loss: 8.7996
  peak_separation_loss: 1.3215
  edge_suppression_loss: 0.0022
  peak_enhancement_loss: 0.7736
  peak_to_second_ratio_loss: 0.2720
  avg_peak_to_second_ratio: 1.8387
  detection_rate_loss: 0.0511
  segmentation_guidance_loss: 0.2103
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 3.6377, Seg Loss: 0.3682, Heatmap Loss: 23.9146, Geometric Loss: 0.5741
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.0438 🟢 (152.2% of target 2.0)
  avg_peak_to_mean_ratio: 76.0893
  avg_peak_to_second_ratio: 2.0956
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.7550
  p2s_ratio_max: 2.6408
  p2s_ratio_std: 0.2997
  secondary_peak_distance: 258.0118
  primary_peak_sharpness: 0.6760
  primary_peak_isolation: 40.3307

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9994
  Top-Right: 1.9851
  Bottom-Right: 2.5552
  Bottom-Left: 1.8427

Secondary Peak Relative Position: x=3.22, y=43.60
val Heatmap Components:
  mse_loss: 0.0238
  separation_loss: 4.6722
  peak_separation_loss: 0.7386
  edge_suppression_loss: 0.0039
  peak_enhancement_loss: 0.7459
  peak_to_second_ratio_loss: 0.0628
  avg_peak_to_second_ratio: 1.8931
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000196
Early stopping: No improvement for 15/15 epochs. Best: 0.8500, Current: 0.8478
Early stopping triggered after 22 epochs!


=== Early Stopping Triggered! ===
Training stopped after 22 epochs due to no improvement in combined score.
Best combined score: 0.8500
Training complete in 52m 26s
Best val loss: 3.5841
Best peak-to-second ratio: 2.4448
Skipping final model visualization in TensorBoard to conserve memory

=== Creating Checkpoint Ensemble ===
Added best combined model to ensemble
Added best peak-to-second ratio model to ensemble
Added best detection rate model to ensemble
Added best loss model to ensemble
Added final model to ensemble
Skipping ensemble visualization to conserve memory...
Ensemble model evaluated
Ensemble output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])
Ensemble model saved to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v5.2(3rd attempt)\ensemble_model_phase2.pth
Warning: Could not create checkpoint ensemble: name 'ensemble_vis_path' is not defined

=== Generating Phase 2 Analysis Report ===
Warning: Could not generate analysis report: name 'sys' is not defined

=== Phase 3: Final convergence and harmonization ===
Saving outputs to v5.2(3rd attempt) folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Skipping model graph visualization in TensorBoard to conserve memory
Generating data augmentation visualizations...
Augmentation visualization directory created at C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(3rd attempt)\augmentations
Generated augmentation visualizations in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(3rd attempt)\augmentations
Epoch 1/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.45s/it] 
train Loss: 36.4753, Seg Loss: 0.3326, Heatmap Loss: 23.6471, Geometric Loss: 0.8400
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.3570 🟢 (117.8% of target 2.0)
  avg_peak_to_mean_ratio: 64.2043
  avg_peak_to_second_ratio: 1.8529
  detection_rate: 0.8897 🔴 (89.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0587
  p2s_ratio_max: 2.8247
  p2s_ratio_std: 0.4764
  secondary_peak_distance: 217.5350
  primary_peak_sharpness: 0.4137
  primary_peak_isolation: 3.2091

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1972
  Top-Right: 1.4287
  Bottom-Right: 1.8787
  Bottom-Left: 1.9068

Secondary Peak Relative Position: x=-22.32, y=40.63
train Heatmap Components:
  mse_loss: 0.1205
  separation_loss: 6.5396
  peak_separation_loss: 25.4093
  edge_suppression_loss: 0.0043
  peak_enhancement_loss: 0.8038
  peak_to_second_ratio_loss: 0.1546
  avg_peak_to_second_ratio: 1.4925
  detection_rate_loss: 0.0852
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.01s/it] 
val Loss: 41.2212, Seg Loss: 0.3727, Heatmap Loss: 26.9125, Geometric Loss: 0.5998
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1885 🟢 (109.4% of target 2.0)
  avg_peak_to_mean_ratio: 44.8468
  avg_peak_to_second_ratio: 1.8656
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5430
  p2s_ratio_max: 2.2921
  p2s_ratio_std: 0.2600
  secondary_peak_distance: 247.3245
  primary_peak_sharpness: 0.4283
  primary_peak_isolation: 3.0000

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1420
  Top-Right: 1.6689
  Bottom-Right: 2.0569
  Bottom-Left: 1.5947

Secondary Peak Relative Position: x=-26.51, y=51.84
val Heatmap Components:
  mse_loss: 0.0513
  separation_loss: 4.6722
  peak_separation_loss: 6.2631
  edge_suppression_loss: 0.0119
  Top-Left: 2.1420
  Top-Right: 1.6689
  Bottom-Right: 2.0569
  Bottom-Left: 1.5947

Secondary Peak Relative Position: x=-26.51, y=51.84
val Heatmap Components:
  mse_loss: 0.0513
  separation_loss: 4.6722
  peak_separation_loss: 6.2631
  edge_suppression_loss: 0.0119
Secondary Peak Relative Position: x=-26.51, y=51.84
val Heatmap Components:
  mse_loss: 0.0513
  separation_loss: 4.6722
  peak_separation_loss: 6.2631
  edge_suppression_loss: 0.0119
  mse_loss: 0.0513
  separation_loss: 4.6722
  peak_separation_loss: 6.2631
  edge_suppression_loss: 0.0119
  edge_suppression_loss: 0.0119
  peak_enhancement_loss: 0.6263
  peak_to_second_ratio_loss: 0.0421
  avg_peak_to_second_ratio: 1.4685
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1716
Current learning rate: 0.000105
New best model saved with loss: 41.2212
New best model saved with peak-to-second ratio: 1.8656
New best model saved with detection rate: 1.0000
New best model saved with combined score: 0.8500 (loss: 41.2212, p2s: 1.8656, detection: 1.0000)
Early stopping: New best score: 0.8500

Epoch 2/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:07<00:00,  7.48s/it] 
train Loss: 56.3735, Seg Loss: 0.3330, Heatmap Loss: 36.9109, Geometric Loss: 0.8426
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1519 🟢 (107.6% of target 2.0)
  avg_peak_to_mean_ratio: 53.7075
  avg_peak_to_second_ratio: 1.8042
  detection_rate: 0.9081 🟡 (90.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1500
  p2s_ratio_max: 2.4366
  p2s_ratio_std: 0.3652
  secondary_peak_distance: 226.4588
  primary_peak_sharpness: 0.4185
  primary_peak_isolation: 3.0133

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8620
  Top-Right: 1.5195
  Bottom-Right: 1.8491
  Bottom-Left: 1.9862

Secondary Peak Relative Position: x=-18.72, y=9.83
train Heatmap Components:
  mse_loss: 0.0816
  separation_loss: 7.7838
  peak_separation_loss: 4.5580
  edge_suppression_loss: 0.0042
  peak_enhancement_loss: 0.8340
  peak_to_second_ratio_loss: 0.1610
  avg_peak_to_second_ratio: 1.5267
  detection_rate_loss: 0.0887
  segmentation_guidance_loss: 0.0250
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.12s/it] 
val Loss: 35.1515, Seg Loss: 0.3724, Heatmap Loss: 22.8588, Geometric Loss: 0.6137
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.9896 🟡 (99.5% of target 2.0)
  avg_peak_to_mean_ratio: 38.8838
  avg_peak_to_second_ratio: 1.8055
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.5113
  p2s_ratio_max: 2.2215
  p2s_ratio_std: 0.2283
  secondary_peak_distance: 249.2289
  primary_peak_sharpness: 0.4286
  primary_peak_isolation: 3.0024

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8099
  Top-Right: 1.7491
  Bottom-Right: 2.1207
  Bottom-Left: 1.5424

Secondary Peak Relative Position: x=-8.19, y=40.74
val Heatmap Components:
  mse_loss: 0.0344
  separation_loss: 4.6722
  peak_separation_loss: 0.5948
  edge_suppression_loss: 0.0089
  peak_enhancement_loss: 0.6561
  peak_to_second_ratio_loss: 0.0065
  avg_peak_to_second_ratio: 1.5216
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000200
New best model saved with loss: 35.1515
Early stopping: No improvement for 1/10 epochs. Best: 0.8500, Current: 0.8500

Epoch 3/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:05<00:00,  7.36s/it] 
train Loss: 75.4811, Seg Loss: 0.3481, Heatmap Loss: 49.6126, Geometric Loss: 0.8925
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.1529 🟢 (107.6% of target 2.0)
  avg_peak_to_mean_ratio: 58.4369
  avg_peak_to_second_ratio: 1.8745
  detection_rate: 0.9449 🟡 (94.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1731
  p2s_ratio_max: 2.4005
  p2s_ratio_std: 0.3391
  secondary_peak_distance: 221.4877
  primary_peak_sharpness: 0.4689
  primary_peak_isolation: 3.9264

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8799
  Top-Right: 1.7323
  Bottom-Right: 1.9160
  Bottom-Left: 1.9698

Secondary Peak Relative Position: x=-20.61, y=26.25
train Heatmap Components:
  mse_loss: 0.0511
  separation_loss: 9.7016
  peak_separation_loss: 2.3966
  edge_suppression_loss: 0.0028
  peak_enhancement_loss: 0.8186
  peak_to_second_ratio_loss: 0.1090
  avg_peak_to_second_ratio: 1.6296
  detection_rate_loss: 0.0521
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.02s/it] 
val Loss: 36.3335, Seg Loss: 0.3695, Heatmap Loss: 23.6755, Geometric Loss: 0.5634
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.7588 🔴 (87.9% of target 2.0)
  avg_peak_to_mean_ratio: 37.6963
  avg_peak_to_second_ratio: 1.8155
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.4326
  p2s_ratio_max: 2.2626
  p2s_ratio_std: 0.2754
  secondary_peak_distance: 254.7874
  primary_peak_sharpness: 0.3548
  primary_peak_isolation: 3.0072

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7898

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7898
Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7898
  Top-Left: 1.7898
  Top-Right: 2.0210
  Bottom-Right: 1.9881
  Bottom-Left: 1.4632

Secondary Peak Relative Position: x=-4.96, y=40.49
val Heatmap Components:
  mse_loss: 0.0232
  separation_loss: 4.6722
  peak_separation_loss: 0.6585
  edge_suppression_loss: 0.0042
  peak_enhancement_loss: 0.7104
  peak_to_second_ratio_loss: 0.0000
  avg_peak_to_second_ratio: 1.5705
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000164
Early stopping: No improvement for 2/10 epochs. Best: 0.8500, Current: 0.8450

Epoch 4/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.43s/it] 
train Loss: 58.5127, Seg Loss: 0.3223, Heatmap Loss: 38.3605, Geometric Loss: 0.8120
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.9064 🟡 (95.3% of target 2.0)
  avg_peak_to_mean_ratio: 86.9950
  avg_peak_to_second_ratio: 1.9751
  detection_rate: 0.9228 🟡 (92.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0768
  p2s_ratio_max: 2.8654
  p2s_ratio_std: 0.4989
  secondary_peak_distance: 233.4603
  primary_peak_sharpness: 0.3546
  primary_peak_isolation: 4.5179

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3287
  Top-Right: 1.6423
  Bottom-Right: 1.8427
  Bottom-Left: 2.0869

Secondary Peak Relative Position: x=-22.38, y=32.34
train Heatmap Components:
  mse_loss: 0.0502
  separation_loss: 7.5169
  peak_separation_loss: 3.8190
  edge_suppression_loss: -0.0014
  peak_enhancement_loss: 0.9107
  peak_to_second_ratio_loss: 0.1731
  avg_peak_to_second_ratio: 1.6152
  detection_rate_loss: 0.0739
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.01s/it] 
val Loss: 36.2229, Seg Loss: 0.3860, Heatmap Loss: 23.5578, Geometric Loss: 0.6253
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.6920 🔴 (84.6% of target 2.0)
  avg_peak_to_mean_ratio: 29.8624
  avg_peak_to_second_ratio: 1.9323
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3393
  p2s_ratio_max: 2.5462
  p2s_ratio_std: 0.3968
  secondary_peak_distance: 262.7862
  primary_peak_sharpness: 0.3408
  primary_peak_isolation: 3.0869

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1171
  Top-Right: 2.3198
  Bottom-Right: 1.8959
  Bottom-Left: 1.3963

Secondary Peak Relative Position: x=-6.38, y=64.59
val Heatmap Components:
  mse_loss: 0.0225
  separation_loss: 4.6722
  peak_separation_loss: 0.9788
  edge_suppression_loss: 0.0029
  peak_enhancement_loss: 0.7227
  peak_to_second_ratio_loss: 0.0000
  avg_peak_to_second_ratio: 1.5658
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000142
New best model saved with peak-to-second ratio: 1.9323
Early stopping: No improvement for 3/10 epochs. Best: 0.8500, Current: 0.8454

Epoch 5/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.41s/it] 
train Loss: 55.8511, Seg Loss: 0.3405, Heatmap Loss: 36.5479, Geometric Loss: 0.8608
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.8408 🟡 (92.0% of target 2.0)
  avg_peak_to_mean_ratio: 70.0649
  avg_peak_to_second_ratio: 1.9033
  detection_rate: 0.9044 🟡 (90.4% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1193
  p2s_ratio_max: 2.6864
  p2s_ratio_std: 0.4441
  secondary_peak_distance: 231.8683
  primary_peak_sharpness: 0.3497
  primary_peak_isolation: 5.2119

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1539
  Top-Right: 1.5692
  Bottom-Right: 1.7877
  Bottom-Left: 2.1022

Secondary Peak Relative Position: x=8.00, y=27.29
train Heatmap Components:
  mse_loss: 0.0540
  separation_loss: 7.2263
  peak_separation_loss: 3.4579
  edge_suppression_loss: 0.0001
  peak_enhancement_loss: 0.8501
  peak_to_second_ratio_loss: 0.1906
  avg_peak_to_second_ratio: 1.6230
  detection_rate_loss: 0.0939
  segmentation_guidance_loss: 0.0124
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.01s/it] 
val Loss: 36.6494, Seg Loss: 0.4035, Heatmap Loss: 23.8403, Geometric Loss: 0.6069
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.5709 🔴 (78.5% of target 2.0)
  avg_peak_to_mean_ratio: 41.0342
  avg_peak_to_second_ratio: 1.8326
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.4367
  p2s_ratio_max: 2.4184
  p2s_ratio_std: 0.3436
  secondary_peak_distance: 276.7003
  primary_peak_sharpness: 0.3559
  primary_peak_isolation: 3.0841

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.7103
  Top-Right: 2.3066
  Bottom-Right: 1.8524
  Bottom-Left: 1.4611

Secondary Peak Relative Position: x=-1.56, y=62.22
val Heatmap Components:
  mse_loss: 0.0215
  separation_loss: 4.6722
  peak_separation_loss: 0.7141
  edge_suppression_loss: 0.0021
  peak_enhancement_loss: 0.7577
  peak_to_second_ratio_loss: 0.0900
  avg_peak_to_second_ratio: 1.6869
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000113

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=1.50, geometric_weight=0.80
Keeping current heatmap weight
Keeping current geometric weight
Adjusted weights: heatmap_weight: 1.50 -> 1.50, geometric_weight: 0.80 -> 0.80

=== Learning Rate Tuning ===
Loss plateau detected (change: 0.0087)
Reducing learning rate for fine-tuning
Adjusted learning rate: 0.000113 -> 0.000079
Early stopping: No improvement for 4/10 epochs. Best: 0.8500, Current: 0.8436
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 5 to prevent memory issues
Model evaluated at epoch 5
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 6/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.43s/it] 
train Loss: 61.8219, Seg Loss: 0.3459, Heatmap Loss: 40.5279, Geometric Loss: 0.8551
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.7820 🔴 (89.1% of target 2.0)
  avg_peak_to_mean_ratio: 203.9884
  avg_peak_to_second_ratio: 1.9258
  detection_rate: 0.9375 🟡 (93.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1996
  p2s_ratio_max: 2.6333
  p2s_ratio_std: 0.3857
  secondary_peak_distance: 236.0543
  primary_peak_sharpness: 0.3595
  primary_peak_isolation: 3.1657

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0592
  Top-Right: 1.7111
  Bottom-Right: 1.9265
  Bottom-Left: 2.0063

Secondary Peak Relative Position: x=-9.33, y=44.28
train Heatmap Components:
  mse_loss: 0.0381
  separation_loss: 7.9989
  peak_separation_loss: 2.8758
  edge_suppression_loss: 0.0011
  peak_enhancement_loss: 0.8252
  peak_to_second_ratio_loss: 0.1288
  avg_peak_to_second_ratio: 1.6356
  detection_rate_loss: 0.0555
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.07s/it] 
val Loss: 35.8597, Seg Loss: 0.3916, Heatmap Loss: 23.3238, Geometric Loss: 0.6030
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.4605 🔴 (73.0% of target 2.0)
  avg_peak_to_mean_ratio: 58.3021
  avg_peak_to_second_ratio: 1.8284
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2845
  p2s_ratio_max: 2.3497
  p2s_ratio_std: 0.3641
  secondary_peak_distance: 276.5471
  primary_peak_sharpness: 0.3482
  primary_peak_isolation: 3.9788

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8431
  Top-Right: 2.2024
  Bottom-Right: 1.9355
  Bottom-Left: 1.3327

Secondary Peak Relative Position: x=-5.56, y=62.66
val Heatmap Components:
  mse_loss: 0.0198
  separation_loss: 4.6722
  peak_separation_loss: 0.7042
  edge_suppression_loss: 0.0024
  peak_enhancement_loss: 0.7664
  peak_to_second_ratio_loss: 0.0386
  avg_peak_to_second_ratio: 1.6002
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000064
Early stopping: No improvement for 5/10 epochs. Best: 0.8500, Current: 0.8470

Epoch 7/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:09<00:00,  7.63s/it] 
train Loss: 37.9061, Seg Loss: 0.3413, Heatmap Loss: 34.2571, Geometric Loss: 0.8041
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.6604 🔴 (83.0% of target 2.0)
  avg_peak_to_mean_ratio: 123.8493
  avg_peak_to_second_ratio: 1.9166
  detection_rate: 0.8971 🔴 (89.7% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0281
  p2s_ratio_max: 2.6963
  p2s_ratio_std: 0.4580
  secondary_peak_distance: 243.0383
  primary_peak_sharpness: 0.3264
  primary_peak_isolation: 6.4801

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1550
  Top-Right: 1.5346
  Bottom-Right: 1.9071
  Bottom-Left: 2.0697

Secondary Peak Relative Position: x=-15.30, y=49.70
train Heatmap Components:
  mse_loss: 0.0397
  separation_loss: 6.8201
  peak_separation_loss: 3.4808
  edge_suppression_loss: -0.0005
  peak_enhancement_loss: 0.8650
  peak_to_second_ratio_loss: 0.2301
  avg_peak_to_second_ratio: 1.5833
  detection_rate_loss: 0.0862
  segmentation_guidance_loss: 0.0059
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.04s/it] 
val Loss: 17.8998, Seg Loss: 0.4105, Heatmap Loss: 23.2097, Geometric Loss: 0.6614
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.4751 🔴 (73.8% of target 2.0)
  avg_peak_to_mean_ratio: 59.9651
  avg_peak_to_second_ratio: 1.8780
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3369
  p2s_ratio_max: 2.3553
  p2s_ratio_std: 0.3494
  secondary_peak_distance: 279.2261
  primary_peak_sharpness: 0.3447
  primary_peak_isolation: 3.1137

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9049
  Top-Right: 2.1978
  Bottom-Right: 2.0297
  Bottom-Left: 1.3798

Secondary Peak Relative Position: x=-0.85, y=63.50
val Heatmap Components:
  mse_loss: 0.0196
  separation_loss: 4.6722
  peak_separation_loss: 0.7108
  edge_suppression_loss: 0.0011
  peak_enhancement_loss: 0.7987
  peak_to_second_ratio_loss: 0.0407
  avg_peak_to_second_ratio: 1.6369
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000076
New best model saved with loss: 17.8998
Early stopping: No improvement for 6/10 epochs. Best: 0.8500, Current: 0.8500

Epoch 8/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:08<00:00,  7.56s/it] 
train Loss: 15.5018, Seg Loss: 0.3430, Heatmap Loss: 46.6196, Geometric Loss: 0.8855
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.7780 🔴 (88.9% of target 2.0)
  avg_peak_to_mean_ratio: 74.5738
  avg_peak_to_second_ratio: 1.9609
  detection_rate: 0.9228 🟡 (92.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0920
  p2s_ratio_max: 2.7304
  p2s_ratio_std: 0.4417
  secondary_peak_distance: 244.9236
  primary_peak_sharpness: 0.3723
  primary_peak_isolation: 4.1889

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.1169
  Top-Right: 1.6988
  Bottom-Right: 1.8802
  Bottom-Left: 2.1476

Secondary Peak Relative Position: x=-28.45, y=55.56
train Heatmap Components:
  mse_loss: 0.0335
  separation_loss: 9.3485
  peak_separation_loss: 2.4146
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.8120
  peak_to_second_ratio_loss: 0.1766
  avg_peak_to_second_ratio: 1.6701
  detection_rate_loss: 0.0629
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.07s/it] 
val Loss: 3.5868, Seg Loss: 0.3767, Heatmap Loss: 23.3152, Geometric Loss: 0.6019
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.5818 🔴 (79.1% of target 2.0)
  avg_peak_to_mean_ratio: 120.7515
  avg_peak_to_second_ratio: 1.8646
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2470
  p2s_ratio_max: 2.6845
  p2s_ratio_std: 0.4523
  secondary_peak_distance: 255.8049
  primary_peak_sharpness: 0.3734
  primary_peak_isolation: 3.0817

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.9107
  Top-Right: 2.4792
  Bottom-Right: 1.7489
  Bottom-Left: 1.3197

Secondary Peak Relative Position: x=-3.38, y=38.53
val Heatmap Components:
  mse_loss: 0.0181
  separation_loss: 4.6722
  peak_separation_loss: 0.7222
  edge_suppression_loss: 0.0025
  peak_enhancement_loss: 0.7233
  peak_to_second_ratio_loss: 0.1219
  avg_peak_to_second_ratio: 1.6521
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000091
New best model saved with loss: 3.5868
Early stopping: No improvement for 7/10 epochs. Best: 0.8500, Current: 0.8500


Epoch 9/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:07<00:00,  7.49s/it] 
train Loss: 19.1656, Seg Loss: 0.3455, Heatmap Loss: 36.1962, Geometric Loss: 0.8821
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.0411 🟢 (102.1% of target 2.0)
  avg_peak_to_mean_ratio: 84.0507
  avg_peak_to_second_ratio: 2.0701
  detection_rate: 0.9228 🟡 (92.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0987
  p2s_ratio_max: 2.9013
  p2s_ratio_std: 0.4817
  secondary_peak_distance: 244.5685
  primary_peak_sharpness: 0.4116
  primary_peak_isolation: 3.6025

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.3329
  Top-Right: 1.9173
  Bottom-Right: 1.9417
  Bottom-Left: 2.0886

Secondary Peak Relative Position: x=-33.03, y=43.80
train Heatmap Components:
  mse_loss: 0.0335
  separation_loss: 7.2865
  peak_separation_loss: 0.8844
  edge_suppression_loss: 0.0004
  peak_enhancement_loss: 0.8031
  peak_to_second_ratio_loss: 0.1787
  avg_peak_to_second_ratio: 1.6963
  detection_rate_loss: 0.0650
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.04s/it] 
val Loss: 20.5881, Seg Loss: 0.3706, Heatmap Loss: 23.3263, Geometric Loss: 0.5691
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.7172 🔴 (85.9% of target 2.0)
  avg_peak_to_mean_ratio: 45.0070
  avg_peak_to_second_ratio: 1.8682
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.2599
  p2s_ratio_max: 2.6175
  p2s_ratio_std: 0.4617
  secondary_peak_distance: 242.2034
  primary_peak_sharpness: 0.4164
  primary_peak_isolation: 3.1113

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0636
  Top-Right: 2.4541
  Bottom-Right: 1.6238
  Bottom-Left: 1.3311

Secondary Peak Relative Position: x=-1.26, y=25.84
val Heatmap Components:
  mse_loss: 0.0179
  separation_loss: 4.6722
  peak_separation_loss: 0.7255
  edge_suppression_loss: 0.0022
  peak_enhancement_loss: 0.7074
  peak_to_second_ratio_loss: 0.1451
  avg_peak_to_second_ratio: 1.5878
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000073
Early stopping: No improvement for 8/10 epochs. Best: 0.8500, Current: 0.7000

Epoch 10/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:06<00:00,  7.43s/it] 
train Loss: 20.3462, Seg Loss: 0.3423, Heatmap Loss: 42.6406, Geometric Loss: 0.8418
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.9297 🟡 (96.5% of target 2.0)
  avg_peak_to_mean_ratio: 43.3540
  avg_peak_to_second_ratio: 2.0504
  detection_rate: 0.9228 🟡 (92.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1639
  p2s_ratio_max: 2.8959
  p2s_ratio_std: 0.4634
  secondary_peak_distance: 239.9336
  primary_peak_sharpness: 0.4007
  primary_peak_isolation: 3.7488

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.2499
  Top-Right: 1.8708
  Bottom-Right: 1.9861
  Bottom-Left: 2.0948

Secondary Peak Relative Position: x=-43.11, y=43.45
train Heatmap Components:
  mse_loss: 0.0316
  separation_loss: 8.3206
  peak_separation_loss: 1.1001
  edge_suppression_loss: 0.0000
  peak_enhancement_loss: 0.8137
  peak_to_second_ratio_loss: 0.1866
  avg_peak_to_second_ratio: 1.6727
  detection_rate_loss: 0.0637
  segmentation_guidance_loss: 0.1576
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.04s/it] 
val Loss: 6.3571, Seg Loss: 0.3702, Heatmap Loss: 23.8854, Geometric Loss: 0.5929
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.6909 🔴 (84.5% of target 2.0)
  avg_peak_to_mean_ratio: 30.8007
  avg_peak_to_second_ratio: 1.8992
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3251
  p2s_ratio_max: 2.6511
  p2s_ratio_std: 0.4160
  secondary_peak_distance: 254.7399
  primary_peak_sharpness: 0.4150
  primary_peak_isolation: 3.1700

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8787
  Top-Right: 2.4417
  Bottom-Right: 1.8717
  Bottom-Left: 1.4045

Secondary Peak Relative Position: x=-3.78, y=37.07
val Heatmap Components:
  mse_loss: 0.0177
  separation_loss: 4.6722
  peak_separation_loss: 0.7026
  edge_suppression_loss: 0.0021
  peak_enhancement_loss: 0.7134
  peak_to_second_ratio_loss: 0.0786
  avg_peak_to_second_ratio: 1.6142
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000059

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=0.14, geometric_weight=2.48
Keeping current heatmap weight
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 0.14 -> 0.50, geometric_weight: 2.48 -> 1.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 9/10 epochs. Best: 0.8500, Current: 0.7341
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 10 to prevent memory issues
Model evaluated at epoch 10
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 11/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:08<00:00,  7.53s/it] 
train Loss: 6.8790, Seg Loss: 0.3569, Heatmap Loss: 36.4603, Geometric Loss: 0.8420
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.8295 🟡 (91.5% of target 2.0)
  avg_peak_to_mean_ratio: 60.1015
  avg_peak_to_second_ratio: 2.0338
  detection_rate: 0.9228 🟡 (92.3% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.1468
  p2s_ratio_max: 2.8733
  p2s_ratio_std: 0.4617
  secondary_peak_distance: 252.4463
  primary_peak_sharpness: 0.3972
  primary_peak_isolation: 3.1374

Per-Corner Peak-to-Second Ratios:
  Top-Left: 2.0502
  Top-Right: 1.9722
  Bottom-Right: 2.0904
  Bottom-Left: 2.0224

Secondary Peak Relative Position: x=-40.44, y=53.51
train Heatmap Components:
  mse_loss: 0.0332
  separation_loss: 7.0882
  peak_separation_loss: 1.3043
  edge_suppression_loss: 0.0001
  peak_enhancement_loss: 0.8341
  peak_to_second_ratio_loss: 0.2041
  avg_peak_to_second_ratio: 1.6484
  detection_rate_loss: 0.0613
  segmentation_guidance_loss: 0.0788
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.14s/it] 
val Loss: 4.4059, Seg Loss: 0.3678, Heatmap Loss: 24.0505, Geometric Loss: 0.6102
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 1.6412 🔴 (82.1% of target 2.0)
  avg_peak_to_mean_ratio: 48.5222
  avg_peak_to_second_ratio: 1.9764
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.3532
  p2s_ratio_max: 2.7835
  p2s_ratio_std: 0.4517
  secondary_peak_distance: 257.9079
  primary_peak_sharpness: 0.3997
  primary_peak_isolation: 3.2288

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.8575
  Top-Right: 2.5759
  Bottom-Right: 2.0277
  Bottom-Left: 1.4443

Secondary Peak Relative Position: x=-3.79, y=40.22
val Heatmap Components:
  mse_loss: 0.0164
  separation_loss: 4.6722
  peak_separation_loss: 0.6977
  edge_suppression_loss: 0.0022
  peak_enhancement_loss: 0.7108
  peak_to_second_ratio_loss: 0.0811
  avg_peak_to_second_ratio: 1.6488
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000070
New best model saved with peak-to-second ratio: 1.9764
Early stopping: No improvement for 10/10 epochs. Best: 0.8500, Current: 0.8157
Early stopping triggered after 11 epochs!


=== Early Stopping Triggered! ===
Training stopped after 11 epochs due to no improvement in combined score.
Best combined score: 0.8500
Training complete in 25m 50s
Best val loss: 3.5868
Best peak-to-second ratio: 1.9764
Skipping final model visualization in TensorBoard to conserve memory

=== Creating Checkpoint Ensemble ===
Added best combined model to ensemble
Added best peak-to-second ratio model to ensemble
Added best detection rate model to ensemble
Added best loss model to ensemble
Added final model to ensemble
Skipping ensemble visualization to conserve memory...
Ensemble model evaluated
Ensemble output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])
Ensemble model saved to C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v5.2(3rd attempt)\ensemble_model_phase3.pth
Warning: Could not create checkpoint ensemble: name 'ensemble_vis_path' is not defined

=== Generating Phase 3 Analysis Report ===
Warning: Could not generate analysis report: name 'sys' is not defined

=== Generating Ensemble Inference Example ===
Warning: Could not generate ensemble inference example: name 'sys' is not defined

Training completed!
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection> 
