{"logs": [{"outputFile": "com.chessvision.app-mergeDebugResources-51:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bca7edd7dea9a9293306bd31b1a5bbbe\\transformed\\appcompat-1.1.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,829,906,997,1089,1185,1280,1381,1474,1569,1665,1756,1846,1934,2044,2148,2254,2365,2469,2587,2750,2856", "endColumns": "118,109,106,85,103,119,77,76,90,91,95,94,100,92,94,95,90,89,87,109,103,105,110,103,117,162,105,88", "endOffsets": "219,329,436,522,626,746,824,901,992,1084,1180,1275,1376,1469,1564,1660,1751,1841,1929,2039,2143,2249,2360,2464,2582,2745,2851,2940"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,829,906,997,1089,1185,1280,1381,1474,1569,1665,1756,1846,1934,2044,2148,2254,2365,2469,2587,2750,10954", "endColumns": "118,109,106,85,103,119,77,76,90,91,95,94,100,92,94,95,90,89,87,109,103,105,110,103,117,162,105,88", "endOffsets": "219,329,436,522,626,746,824,901,992,1084,1180,1275,1376,1469,1564,1660,1751,1841,1929,2039,2143,2249,2360,2464,2582,2745,2851,11038"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5b68b955b795828701719ac184f2bfb\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "29,30,31,32,33,34,35,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2856,2959,3061,3164,3269,3370,3472,11113", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "2954,3056,3159,3264,3365,3467,3586,11209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4713635adcc2bac18369e016a04ea54e\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "36,37,38,39,40,41,42,99,100,101,102,103,104,106,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3591,3688,3775,3867,3967,4053,4130,10479,10567,10654,10724,10794,10872,11043,11214,11297,11364", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "3683,3770,3862,3962,4048,4125,4223,10562,10649,10719,10789,10867,10949,11108,11292,11359,11478"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2559031d7e9fe48b3590293cf5c6448f\\transformed\\material3-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,177,294,407,526,620,720,837,980,1106,1257,1342,1447,1543,1638,1754,1884,1994,2137,2275,2406,2598,2724,2853,2988,3118,3215,3311,3428,3550,3655,3760,3863,4005,4155,4262,4371,4446,4550,4652,4746,4837,4942,5022,5107,5208,5314,5407,5508,5595,5703,5802,5905,6029,6109,6212", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "172,289,402,521,615,715,832,975,1101,1252,1337,1442,1538,1633,1749,1879,1989,2132,2270,2401,2593,2719,2848,2983,3113,3210,3306,3423,3545,3650,3755,3858,4000,4150,4257,4366,4441,4545,4647,4741,4832,4937,5017,5102,5203,5309,5402,5503,5590,5698,5797,5900,6024,6104,6207,6301"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4228,4350,4467,4580,4699,4793,4893,5010,5153,5279,5430,5515,5620,5716,5811,5927,6057,6167,6310,6448,6579,6771,6897,7026,7161,7291,7388,7484,7601,7723,7828,7933,8036,8178,8328,8435,8544,8619,8723,8825,8919,9010,9115,9195,9280,9381,9487,9580,9681,9768,9876,9975,10078,10202,10282,10385", "endColumns": "121,116,112,118,93,99,116,142,125,150,84,104,95,94,115,129,109,142,137,130,191,125,128,134,129,96,95,116,121,104,104,102,141,149,106,108,74,103,101,93,90,104,79,84,100,105,92,100,86,107,98,102,123,79,102,93", "endOffsets": "4345,4462,4575,4694,4788,4888,5005,5148,5274,5425,5510,5615,5711,5806,5922,6052,6162,6305,6443,6574,6766,6892,7021,7156,7286,7383,7479,7596,7718,7823,7928,8031,8173,8323,8430,8539,8614,8718,8820,8914,9005,9110,9190,9275,9376,9482,9575,9676,9763,9871,9970,10073,10197,10277,10380,10474"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ead5545371d24235bc023f33a0e98494\\transformed\\foundation-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,87", "endOffsets": "135,223"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "11483,11568", "endColumns": "84,87", "endOffsets": "11563,11651"}}]}]}