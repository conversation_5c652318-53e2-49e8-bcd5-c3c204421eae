<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res"><file name="ic_launcher_background" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\values\colors.xml" qualifiers=""><color name="md_theme_light_primary">#8B4513</color><color name="md_theme_light_onPrimary">#FFFFFF</color><color name="md_theme_light_primaryContainer">#DEB887</color><color name="md_theme_light_onPrimaryContainer">#2D1810</color><color name="md_theme_light_secondary">#6B4423</color><color name="md_theme_light_onSecondary">#FFFFFF</color><color name="md_theme_light_secondaryContainer">#F5DEB3</color><color name="md_theme_light_onSecondaryContainer">#241A0E</color><color name="md_theme_light_tertiary">#4A5D23</color><color name="md_theme_light_onTertiary">#FFFFFF</color><color name="md_theme_light_tertiaryContainer">#CBD896</color><color name="md_theme_light_onTertiaryContainer">#141F00</color><color name="md_theme_light_error">#BA1A1A</color><color name="md_theme_light_onError">#FFFFFF</color><color name="md_theme_light_errorContainer">#FFDAD6</color><color name="md_theme_light_onErrorContainer">#410002</color><color name="md_theme_light_background">#FFFBFF</color><color name="md_theme_light_onBackground">#1F1B16</color><color name="md_theme_light_surface">#FFFBFF</color><color name="md_theme_light_onSurface">#1F1B16</color><color name="md_theme_light_surfaceVariant">#F0E0CF</color><color name="md_theme_light_onSurfaceVariant">#4F4539</color><color name="md_theme_light_outline">#817567</color><color name="md_theme_light_outlineVariant">#D3C4B4</color><color name="md_theme_light_scrim">#000000</color><color name="md_theme_light_inverseSurface">#34302A</color><color name="md_theme_light_inverseOnSurface">#F9EFE7</color><color name="md_theme_light_inversePrimary">#FFB59D</color></file><file path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Chess Vision AI</string><string name="home_title">Chess Vision AI</string><string name="capture_board_title">Capture Chess Board</string><string name="capture_board_subtitle">Point camera at chess board to detect position</string><string name="scan_board">Scan Board</string><string name="new_game">New Game</string><string name="recent_games">Recent Games</string><string name="no_recent_games">No recent games</string><string name="start_scanning">Start by scanning a chess board</string><string name="camera_permission_required">Camera Permission Required</string><string name="camera_permission_rationale">Camera access is needed to capture chess board images for position detection. Please grant permission to continue.</string><string name="camera_permission_description">To detect chess positions, we need access to your camera. This allows you to capture images of physical chess boards.</string><string name="grant_camera_permission">Grant Camera Permission</string><string name="position_camera_instruction">Position Camera Over Chess Board</string><string name="analyzing_board">Analyzing chess board...</string><string name="analysis_wait_message">This may take a few seconds</string><string name="capture">Capture</string><string name="chess_board_title">Chess Board</string><string name="play_vs_ai">Play vs AI</string><string name="analyze_position">Analyze Position</string><string name="save_game">Save Game</string><string name="evaluation">Evaluation: %1$s</string><string name="best_move">Best: %1$s</string><string name="position_analysis">Position Analysis</string><string name="best_moves">Best Moves:</string><string name="analysis_text">Analysis:</string><string name="deeper_analysis">Deeper</string><string name="share_analysis">Share</string><string name="settings_title">Settings</string><string name="engine_strength">Engine Strength</string><string name="board_theme">Board Theme</string><string name="piece_style">Piece Style</string><string name="auto_analysis">Auto Analysis</string><string name="sound_effects">Sound Effects</string><string name="about">About</string><string name="back">Back</string><string name="cancel">Cancel</string><string name="ok">OK</string><string name="save">Save</string><string name="delete">Delete</string><string name="share">Share</string><string name="loading">Loading...</string><string name="error">Error</string><string name="retry">Retry</string><string name="error_camera_permission">Camera permission is required to scan chess boards</string><string name="error_fen_detection">Failed to detect chess position</string><string name="error_invalid_fen">Invalid chess position detected</string><string name="error_network">Network error occurred</string><string name="error_unknown">An unexpected error occurred</string><string name="piece_pawn">Pawn</string><string name="piece_rook">Rook</string><string name="piece_knight">Knight</string><string name="piece_bishop">Bishop</string><string name="piece_queen">Queen</string><string name="piece_king">King</string><string name="color_white">White</string><string name="color_black">Black</string></file><file path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.ChessVisionApp" parent="android:Theme.Material.NoActionBar">
        
        <item name="android:colorPrimary">#1A1A2E</item>

        
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>

        
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>

        
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>

        
        <item name="android:windowFullscreen">false</item>
        <item name="android:fitsSystemWindows">false</item>

        
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="file_paths" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\xml\file_paths.xml" qualifiers="" type="xml"/><file name="bb" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\bb.png" qualifiers="" type="drawable"/><file name="bk" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\bk.png" qualifiers="" type="drawable"/><file name="bn" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\bn.png" qualifiers="" type="drawable"/><file name="bp" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\bp.png" qualifiers="" type="drawable"/><file name="bq" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\bq.png" qualifiers="" type="drawable"/><file name="br" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\br.png" qualifiers="" type="drawable"/><file name="wb" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\wb.png" qualifiers="" type="drawable"/><file name="wk" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\wk.png" qualifiers="" type="drawable"/><file name="wn" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\wn.png" qualifiers="" type="drawable"/><file name="wp" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\wp.png" qualifiers="" type="drawable"/><file name="wq" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\wq.png" qualifiers="" type="drawable"/><file name="wr" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\wr.png" qualifiers="" type="drawable"/><file name="chessboard_background" path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\main\res\drawable\chessboard_background.png" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>