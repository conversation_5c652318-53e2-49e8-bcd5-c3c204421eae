"""
Verify GPU setup for YOLO training.
This script checks if your GPU is properly configured for training YOLO models.
"""

import os
import sys
import torch
import platform
from ultralytics import YOLO
import time

def print_separator():
    print("\n" + "="*50 + "\n")

def check_gpu():
    """Check if GPU is available and properly configured."""
    print_separator()
    print("CHECKING GPU CONFIGURATION")
    print_separator()
    
    # Check PyTorch installation
    print(f"PyTorch version: {torch.__version__}")
    print(f"Python version: {platform.python_version()}")
    
    # Check CUDA availability
    cuda_available = torch.cuda.is_available()
    print(f"CUDA available: {cuda_available}")
    
    if not cuda_available:
        print("WARNING: CUDA is not available. Training will be slow on CPU.")
        print("Please install PyTorch with CUDA support:")
        print("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121")
        return False
    
    # Check CUDA version
    print(f"CUDA version: {torch.version.cuda}")
    
    # Check GPU information
    device_count = torch.cuda.device_count()
    print(f"GPU device count: {device_count}")
    
    if device_count == 0:
        print("WARNING: No GPU devices found despite CUDA being available.")
        return False
    
    # Print GPU information
    for i in range(device_count):
        print(f"\nGPU {i}: {torch.cuda.get_device_name(i)}")
        print(f"GPU {i} memory: {torch.cuda.get_device_properties(i).total_memory / 1e9:.2f} GB")
    
    # Check cuDNN
    print(f"\ncuDNN enabled: {torch.backends.cudnn.enabled}")
    if torch.backends.cudnn.enabled:
        print(f"cuDNN version: {torch.backends.cudnn.version()}")
    
    return True

def benchmark_gpu():
    """Run a simple benchmark to test GPU performance."""
    if not torch.cuda.is_available():
        print("Cannot run benchmark: CUDA is not available")
        return
    
    print_separator()
    print("RUNNING GPU BENCHMARK")
    print_separator()
    
    # Create a large tensor and measure transfer time
    print("Testing CPU to GPU transfer speed...")
    x = torch.randn(1000, 1000)
    
    start_time = time.time()
    x_gpu = x.cuda()
    torch.cuda.synchronize()
    transfer_time = time.time() - start_time
    
    print(f"Transfer time for 1000x1000 tensor: {transfer_time*1000:.2f} ms")
    
    # Matrix multiplication benchmark
    print("\nTesting matrix multiplication performance...")
    a = torch.randn(2000, 2000, device='cuda')
    b = torch.randn(2000, 2000, device='cuda')
    
    # Warmup
    for _ in range(5):
        c = torch.matmul(a, b)
    
    # Benchmark
    torch.cuda.synchronize()
    start_time = time.time()
    for _ in range(10):
        c = torch.matmul(a, b)
    torch.cuda.synchronize()
    matmul_time = (time.time() - start_time) / 10
    
    print(f"Matrix multiplication time (2000x2000): {matmul_time*1000:.2f} ms")
    
    # Convolution benchmark (relevant for YOLO)
    print("\nTesting convolution performance (relevant for YOLO)...")
    input_tensor = torch.randn(16, 3, 640, 640, device='cuda')
    conv = torch.nn.Conv2d(3, 64, kernel_size=3, padding=1).cuda()
    
    # Warmup
    for _ in range(3):
        output = conv(input_tensor)
    
    # Benchmark
    torch.cuda.synchronize()
    start_time = time.time()
    for _ in range(5):
        output = conv(input_tensor)
    torch.cuda.synchronize()
    conv_time = (time.time() - start_time) / 5
    
    print(f"Convolution time (16x3x640x640 -> 16x64x640x640): {conv_time*1000:.2f} ms")
    
    # Estimate YOLO inference time
    print("\nEstimating YOLO inference time...")
    try:
        # Try to load a small YOLO model
        model = YOLO("yolo11n.pt")
        model.to('cuda')
        
        # Create a dummy input
        dummy_input = torch.randn(1, 3, 640, 640, device='cuda')
        
        # Warmup
        for _ in range(3):
            with torch.no_grad():
                model(dummy_input)
        
        # Benchmark
        torch.cuda.synchronize()
        start_time = time.time()
        for _ in range(10):
            with torch.no_grad():
                model(dummy_input)
        torch.cuda.synchronize()
        inference_time = (time.time() - start_time) / 10
        
        print(f"YOLO11n inference time (1x3x640x640): {inference_time*1000:.2f} ms")
        print(f"Estimated FPS: {1/inference_time:.2f}")
    except Exception as e:
        print(f"Could not load YOLO model for benchmark: {e}")

def check_ultralytics():
    """Check Ultralytics installation."""
    print_separator()
    print("CHECKING ULTRALYTICS INSTALLATION")
    print_separator()
    
    try:
        from ultralytics import __version__ as ultralytics_version
        print(f"Ultralytics version: {ultralytics_version}")
        
        # Check if YOLO11n model is available
        try:
            model = YOLO("yolo11n.pt")
            print(f"Successfully loaded YOLO11n model")
            print(f"Model task: {model.task}")
            print(f"Model architecture: {model.model.yaml_file}")
            print(f"Number of parameters: {sum(p.numel() for p in model.parameters())/1e6:.2f}M")
        except Exception as e:
            print(f"Could not load YOLO11n model: {e}")
            print("You may need to download it first.")
    
    except ImportError:
        print("Ultralytics is not installed. Please install it:")
        print("pip install ultralytics")
        return False
    
    return True

def main():
    """Run all checks."""
    print("\nGPU VERIFICATION FOR YOLO TRAINING")
    print("==================================\n")
    
    # Check system information
    print(f"System: {platform.system()} {platform.release()}")
    print(f"Python: {platform.python_version()}")
    print(f"Directory: {os.getcwd()}")
    
    # Check GPU configuration
    gpu_ok = check_gpu()
    
    # Check Ultralytics installation
    ultralytics_ok = check_ultralytics()
    
    # Run benchmark if GPU is available
    if gpu_ok:
        benchmark_gpu()
    
    # Print summary
    print_separator()
    print("VERIFICATION SUMMARY")
    print_separator()
    
    if gpu_ok and ultralytics_ok:
        print("✅ Your system is properly configured for GPU training!")
        print("\nYou can start training with:")
        print("python chess_board_detection/piece_detection/train_gpu.py --model yolo11n.pt --epochs 800 --batch 16 --device 0")
    else:
        print("❌ There are issues with your GPU configuration.")
        if not gpu_ok:
            print("- GPU is not properly configured")
        if not ultralytics_ok:
            print("- Ultralytics is not properly installed")
        print("\nPlease fix these issues before training.")

if __name__ == "__main__":
    main()
