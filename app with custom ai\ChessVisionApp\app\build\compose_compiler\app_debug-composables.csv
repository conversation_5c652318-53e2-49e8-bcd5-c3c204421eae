package,name,composable,skippable,restartable,readonly,inline,isLambda,hasDefaults,defaultsGroup,groups,calls,
com.chessvision.app.CameraScreen,CameraScreen,1,1,1,0,0,0,0,0,2,1,
com.chessvision.app.rememberCameraStateManager,rememberCameraStateManager,1,0,0,0,0,0,0,0,1,1,
com.chessvision.app.CameraViewScreen,CameraViewScreen,1,1,1,0,0,0,0,0,1,9,
com.chessvision.app.ChessBoardGuide,ChessBoardGuide,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.CornerGuide,CornerGuide,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.PhotoPreviewScreen,PhotoPreviewScreen,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.CapturedImagePreview,CapturedImagePreview,1,1,1,0,0,0,0,0,1,2,
