package,name,composable,skippable,restartable,readonly,inline,isLambda,hasDefaults,defaultsGroup,groups,calls,
com.chessvision.app.CameraScreen,CameraScreen,1,1,1,0,0,0,0,0,2,1,
com.chessvision.app.rememberCameraStateManager,rememberCameraStateManager,1,0,0,0,0,0,0,0,1,1,
com.chessvision.app.CameraViewScreen,CameraViewScreen,1,1,1,0,0,0,0,0,1,9,
com.chessvision.app.ChessBoardGuide,ChessBoardGuide,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.CornerGuide,CornerGuide,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.PhotoPreviewScreen,PhotoPreviewScreen,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.CapturedImagePreview,CapturedImagePreview,1,1,1,0,0,0,0,0,1,2,
com.chessvision.app.ChessBoard,ChessBoard,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.ChessBoardControls,ChessBoardControls,1,0,1,0,0,0,0,0,1,3,
com.chessvision.app.ControlButton,ControlButton,1,1,1,0,0,0,0,0,1,6,
com.chessvision.app.rememberChessBoardState,rememberChessBoardState,1,0,0,0,0,0,0,0,1,1,
com.chessvision.app.InteractiveChessBoard,InteractiveChessBoard,1,1,1,0,0,0,1,0,2,3,
com.chessvision.app.ChessBoardGrid,ChessBoardGrid,1,0,1,0,0,0,0,0,1,3,
com.chessvision.app.ChessSquare,ChessSquare,1,1,1,0,0,0,0,0,1,5,
com.chessvision.app.FENDisplay,FENDisplay,1,1,1,0,0,0,0,0,1,6,
com.chessvision.app.MainScreen,MainScreen,1,0,1,0,0,0,0,0,5,20,
com.chessvision.app.MainHomeScreen,MainHomeScreen,1,0,1,0,0,0,0,0,1,1,
com.chessvision.app.ChessAppHeader,ChessAppHeader,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.QuickActionsSection,QuickActionsSection,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.ChessActionCard,ChessActionCard,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.FeaturesSection,FeaturesSection,1,0,1,0,0,0,0,0,1,1,
com.chessvision.app.FeatureItem,FeatureItem,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.ChessBoardScreen,ChessBoardScreen,1,1,1,0,0,0,0,0,1,4,
com.chessvision.app.ModernControlButton,ModernControlButton,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.MainScreenPreview,MainScreenPreview,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.PieceTray,PieceTray,1,0,1,0,0,0,0,0,1,2,
com.chessvision.app.PieceTrayItem,PieceTrayItem,1,0,1,0,0,0,0,0,1,6,
com.chessvision.app.ui.theme.rememberAdaptiveLayout,rememberAdaptiveLayout,1,0,0,0,0,0,0,0,1,2,
com.chessvision.app.ui.theme.expressiveClickable,expressiveClickable,1,0,0,0,0,0,0,0,1,4,
com.chessvision.app.ui.theme.breathingAnimation,breathingAnimation,1,0,0,0,0,0,0,0,1,3,
com.chessvision.app.ui.theme.shimmerEffect,shimmerEffect,1,0,0,0,0,0,0,0,1,3,
com.chessvision.app.ui.theme.floatingAnimation,floatingAnimation,1,0,0,0,0,0,0,0,1,3,
com.chessvision.app.ui.theme.ChessVisionAppTheme,ChessVisionAppTheme,1,1,1,0,0,0,1,0,4,5,
