package,name,composable,skippable,restartable,readonly,inline,isLambda,hasDefaults,defaultsGroup,groups,calls,
com.chessvision.app.MainScreen,MainScreen,1,1,1,0,0,0,0,0,5,20,
com.chessvision.app.MainHomeScreen,MainHomeScreen,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.ChessAppHeader,ChessAppHeader,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.QuickActionsSection,QuickActionsSection,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.ChessActionCard,ChessActionCard,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.FeaturesSection,FeaturesSection,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.FeatureItem,FeatureItem,1,1,1,0,0,0,0,0,1,1,
com.chessvision.app.ChessBoardScreen,ChessBoardScreen,1,1,1,0,0,0,0,0,1,4,
com.chessvision.app.ModernControlButton,ModernControlButton,1,1,1,0,0,0,0,0,1,3,
com.chessvision.app.MainScreenPreview,MainScreenPreview,1,1,1,0,0,0,0,0,1,1,
