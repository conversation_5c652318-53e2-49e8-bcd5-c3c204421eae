package com.chessvision.app.ui.theme

import androidx.compose.material3.Typography
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color

// Material 3 Expressive Typography - Enhanced font hierarchy with dynamic scaling
val ChessFontFamily = FontFamily.Default

// Material 3 Expressive Typography with enhanced hierarchy, dynamic scaling, and rich text treatments
val Typography = Typography(
    bodyLarge = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.5.sp
    ),
    bodyMedium = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.25.sp
    ),
    bodySmall = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Normal,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp
    ),
    titleLarge = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 22.sp,
        lineHeight = 28.sp,
        letterSpacing = 0.sp,
        // Enhanced with subtle shadow for depth
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.1f),
            offset = Offset(0f, 1f),
            blurRadius = 2f
        )
    ),
    titleMedium = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 16.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.15.sp
    ),
    titleSmall = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp
    ),
    labelLarge = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp
    ),
    labelMedium = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    ),
    labelSmall = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp
    ),
    headlineLarge = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 32.sp,
        lineHeight = 40.sp,
        letterSpacing = 0.sp,
        // Enhanced with expressive shadow
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.15f),
            offset = Offset(0f, 2f),
            blurRadius = 4f
        )
    ),
    headlineMedium = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 28.sp,
        lineHeight = 36.sp,
        letterSpacing = 0.sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.12f),
            offset = Offset(0f, 1.5f),
            blurRadius = 3f
        )
    ),
    headlineSmall = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 24.sp,
        lineHeight = 32.sp,
        letterSpacing = 0.sp
    ),
    // Material 3 Expressive Display styles - Enhanced for chess app with dramatic effects
    displayLarge = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.ExtraBold,
        fontSize = 64.sp, // Larger for more impact
        lineHeight = 72.sp,
        letterSpacing = (-0.5).sp, // Tighter for modern look
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.2f),
            offset = Offset(0f, 4f),
            blurRadius = 8f
        )
    ),
    displayMedium = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.ExtraBold,
        fontSize = 52.sp, // Enhanced size
        lineHeight = 60.sp,
        letterSpacing = (-0.25).sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.18f),
            offset = Offset(0f, 3f),
            blurRadius = 6f
        )
    ),
    displaySmall = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 40.sp, // Larger for better hierarchy
        lineHeight = 48.sp,
        letterSpacing = 0.sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.15f),
            offset = Offset(0f, 2f),
            blurRadius = 4f
        )
    )
)

// Material 3 Expressive custom text styles for chess-specific elements with enhanced visual hierarchy
val ChessExpressiveTypography = object {
    val chessTitle = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Black,
        fontSize = 28.sp,
        lineHeight = 36.sp,
        letterSpacing = (-0.5).sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.2f),
            offset = Offset(0f, 2f),
            blurRadius = 4f
        )
    )

    val pieceLabel = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.SemiBold,
        fontSize = 13.sp,
        lineHeight = 18.sp,
        letterSpacing = 0.25.sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.08f),
            offset = Offset(0f, 0.5f),
            blurRadius = 1f
        )
    )

    val fenDisplay = TextStyle(
        fontFamily = FontFamily.Monospace, // Monospace for FEN
        fontWeight = FontWeight.Medium,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.1f),
            offset = Offset(0f, 1f),
            blurRadius = 2f
        )
    )

    val boardCoordinate = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 10.sp,
        lineHeight = 14.sp,
        letterSpacing = 0.5.sp
    )

    // New expressive styles for enhanced UI
    val heroTitle = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.ExtraBold,
        fontSize = 36.sp,
        lineHeight = 44.sp,
        letterSpacing = (-0.75).sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.25f),
            offset = Offset(0f, 3f),
            blurRadius = 6f
        )
    )

    val cardTitle = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Bold,
        fontSize = 18.sp,
        lineHeight = 24.sp,
        letterSpacing = 0.sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.12f),
            offset = Offset(0f, 1f),
            blurRadius = 2f
        )
    )

    val buttonLabel = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.SemiBold,
        fontSize = 14.sp,
        lineHeight = 20.sp,
        letterSpacing = 0.1.sp
    )

    val statusText = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 12.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.4.sp
    )

    val expressiveCaption = TextStyle(
        fontFamily = ChessFontFamily,
        fontWeight = FontWeight.Medium,
        fontSize = 11.sp,
        lineHeight = 16.sp,
        letterSpacing = 0.5.sp,
        shadow = Shadow(
            color = Color.Black.copy(alpha = 0.06f),
            offset = Offset(0f, 0.5f),
            blurRadius = 1f
        )
    )
}
