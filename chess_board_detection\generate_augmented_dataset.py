"""
<PERSON><PERSON><PERSON> to generate and save augmented data for training.
This creates a pre-augmented dataset that can be used for training,
allowing inspection of the augmented data before training begins.
"""

import os
import json
import numpy as np
import torch
import cv2
import matplotlib.pyplot as plt
from tqdm import tqdm
import albumentations as A
from albumentations.pytorch import ToTensorV2
import shutil
from datetime import datetime

from utils.real_dataset import RealChessBoardDataset
from config import DATA_DIR, INPUT_SIZE

def get_enhanced_training_augmentation():
    """
    Enhanced training augmentation that ensures the full chess board remains visible.
    Uses black padding and avoids rotations that cause issues with corner detection.
    """
    return <PERSON><PERSON>([
        # Basic flips only (no rotations)
        A.<PERSON>(p=0.5),
        <PERSON><PERSON>lip(p=0.5),

        # Color augmentations
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.7),
        <PERSON><PERSON>at<PERSON>Value(hue_shift_limit=15, sat_shift_limit=25, val_shift_limit=15, p=0.5),

        # Noise and blur
        A.<PERSON>([
            <PERSON><PERSON>(p=1.0),
            <PERSON><PERSON>(blur_limit=5, p=1.0),
            A.Blur(blur_limit=3, p=1.0)
        ], p=0.5),

        # Ensure consistent size with padding
        A.PadIfNeeded(
            min_height=INPUT_SIZE[0],
            min_width=INPUT_SIZE[1],
            border_mode=cv2.BORDER_CONSTANT,
            p=1.0
        ),

        # Normalization and conversion to tensor
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))

def detect_corner_orientation(corners):
    """
    Detect the orientation of corners and return the correct corner order.

    Args:
        corners: Array of corner coordinates [x1, y1, x2, y2, x3, y3, x4, y4]
                where the order is expected to be [TL, TR, BR, BL]

    Returns:
        corner_indices: List of indices that map the current corners to the correct TL, TR, BR, BL order
        is_flipped: Boolean indicating if the corners have been flipped
    """
    # Convert corners to a more usable format
    corner_points = []
    for i in range(0, len(corners), 2):
        if i+1 < len(corners):
            corner_points.append((corners[i], corners[i+1]))

    # If we don't have 4 corners, return the default order
    if len(corner_points) != 4:
        return [0, 1, 2, 3], False

    # Calculate the center of the corners
    center_x = sum(p[0] for p in corner_points) / 4
    center_y = sum(p[1] for p in corner_points) / 4

    # Classify corners based on their position relative to the center
    classified_corners = []
    for i, (x, y) in enumerate(corner_points):
        # Determine quadrant (TL, TR, BR, BL)
        if x <= center_x and y <= center_y:
            corner_type = "TL"
        elif x > center_x and y <= center_y:
            corner_type = "TR"
        elif x > center_x and y > center_y:
            corner_type = "BR"
        else:  # x <= center_x and y > center_y
            corner_type = "BL"

        classified_corners.append((i, corner_type))

    # Create a mapping from the expected order [TL, TR, BR, BL] to the actual order
    corner_map = {"TL": 0, "TR": 1, "BR": 2, "BL": 3}
    corner_indices = [-1, -1, -1, -1]

    for idx, corner_type in classified_corners:
        corner_indices[corner_map[corner_type]] = idx

    # Check if any corners are missing from the mapping
    if -1 in corner_indices:
        # Fall back to the original order if we couldn't map all corners
        return [0, 1, 2, 3], False

    # Determine if the corners have been flipped
    original_order = [0, 1, 2, 3]
    is_flipped = corner_indices != original_order

    # Convert to boolean for JSON serialization
    is_flipped = any(corner_indices[i] != original_order[i] for i in range(4))

    return corner_indices, is_flipped

def save_augmented_sample(sample, output_dir, index, aug_index):
    """
    Save an augmented sample to disk with enhanced visualizations.

    Args:
        sample: Dictionary containing 'image', 'mask', 'corner_heatmaps', and 'corners'
        output_dir: Directory to save the sample
        index: Original sample index
        aug_index: Augmentation index
    """
    # Create sample directory
    sample_dir = os.path.join(output_dir, f"sample_{index+1}_aug_{aug_index+1}")
    os.makedirs(sample_dir, exist_ok=True)

    # Save image
    image = sample['image']
    if isinstance(image, torch.Tensor):
        # Save normalized tensor
        torch.save(image, os.path.join(sample_dir, "image.pt"))

        # Also save a visualization (denormalized)
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        vis_image = image * std + mean
        vis_image = vis_image.permute(1, 2, 0).numpy()
        vis_image = np.clip(vis_image * 255, 0, 255).astype(np.uint8)
        cv2.imwrite(os.path.join(sample_dir, "image.jpg"), cv2.cvtColor(vis_image, cv2.COLOR_RGB2BGR))

    # Save mask
    mask = sample['mask']
    if isinstance(mask, torch.Tensor):
        torch.save(mask, os.path.join(sample_dir, "mask.pt"))
        # Also save visualization
        vis_mask = mask.squeeze().numpy()
        vis_mask = (vis_mask * 255).astype(np.uint8)
        cv2.imwrite(os.path.join(sample_dir, "mask.jpg"), vis_mask)

    # Save corners
    corners = sample['corners']
    if isinstance(corners, torch.Tensor):
        # Detect corner orientation
        corner_indices, is_flipped = detect_corner_orientation(corners.numpy())

        # If corners are flipped, reorder them to match the correct orientation
        if is_flipped:
            # Create a new tensor with reordered corners
            reordered_corners = torch.zeros_like(corners)
            for i, idx in enumerate(corner_indices):
                reordered_corners[i*2] = corners[idx*2]
                reordered_corners[i*2+1] = corners[idx*2+1]

            # Replace the original corners with the reordered ones
            corners = reordered_corners

            # Also update the sample's corners for later use
            sample['corners'] = corners

        torch.save(corners, os.path.join(sample_dir, "corners.pt"))

        # Save information about the corner orientation
        with open(os.path.join(sample_dir, "corner_orientation.json"), 'w') as f:
            json.dump({
                "original_indices": corner_indices,
                "is_flipped": bool(is_flipped),
                "corner_names": ["TL", "TR", "BR", "BL"]
            }, f, indent=4)

    # Save corner heatmaps
    heatmaps = sample['corner_heatmaps']
    if isinstance(heatmaps, torch.Tensor):
        # If corners were flipped, we need to reorder the heatmaps too
        if 'is_flipped' in locals() and is_flipped:
            # Create a new tensor with reordered heatmaps
            reordered_heatmaps = torch.zeros_like(heatmaps)
            for i, idx in enumerate(corner_indices):
                reordered_heatmaps[i] = heatmaps[idx]

            # Replace the original heatmaps with the reordered ones
            heatmaps = reordered_heatmaps

            # Also update the sample's heatmaps for later use
            sample['corner_heatmaps'] = heatmaps

        torch.save(heatmaps, os.path.join(sample_dir, "corner_heatmaps.pt"))

        # Save individual heatmap visualizations
        corner_names = ['TL', 'TR', 'BR', 'BL']
        # Use consistent colormaps for each corner type
        corner_cmaps = ['Blues', 'Greens', 'Reds', 'cool']  # TL, TR, BR, BL

        for i in range(min(4, heatmaps.shape[0])):
            heatmap = heatmaps[i].numpy()

            plt.figure(figsize=(5, 5))
            # Create dark background
            dark_bg = np.zeros((heatmap.shape[0], heatmap.shape[1], 3))
            plt.imshow(dark_bg)
            plt.imshow(heatmap, alpha=0.7, cmap=corner_cmaps[i])
            plt.title(f'Heatmap {corner_names[i]} (Dark Background)')
            plt.colorbar()
            plt.savefig(os.path.join(sample_dir, f"heatmap_{corner_names[i]}_dark_bg.jpg"))
            plt.close()

            # 2. Overlaid on original image

            plt.figure(figsize=(5, 5))
            plt.imshow(vis_image)
            plt.imshow(heatmap, alpha=0.5, cmap=corner_cmaps[i])
            plt.title(f'Heatmap {corner_names[i]} (Overlaid)')
            plt.colorbar()
            plt.savefig(os.path.join(sample_dir, f"heatmap_{corner_names[i]}_overlaid.jpg"))
            plt.close()

        # 3. Combined visualization with all 4 heatmaps on blue background
        plt.figure(figsize=(8, 8))
        # Create blue background
        blue_bg = np.zeros((heatmaps.shape[1], heatmaps.shape[2], 3))
        blue_bg[:, :, 2] = 1.0  # Set blue channel to 1
        plt.imshow(blue_bg)

        # Add each heatmap with a different color
        # Colors for each corner type: TL: Blue, TR: Green, BR: Red, BL: Cyan
        colors = ['blue', 'green', 'red', 'cyan']
        for i in range(min(4, heatmaps.shape[0])):
            heatmap = heatmaps[i].numpy()
            colored_heatmap = np.zeros((heatmap.shape[0], heatmap.shape[1], 4))

            # Set RGB based on corner color
            if colors[i] == 'red':
                colored_heatmap[:, :, 0] = heatmap  # R
            elif colors[i] == 'green':
                colored_heatmap[:, :, 1] = heatmap  # G
            elif colors[i] == 'blue':
                colored_heatmap[:, :, 2] = heatmap  # B
            elif colors[i] == 'cyan':
                colored_heatmap[:, :, 1] = heatmap  # G
                colored_heatmap[:, :, 2] = heatmap  # B

            # Set alpha channel
            colored_heatmap[:, :, 3] = heatmap * 0.7

            plt.imshow(colored_heatmap)

            # Add corner point if available
            if isinstance(corners, torch.Tensor) and corners.numel() >= 8:
                x, y = corners[i*2].item(), corners[i*2+1].item()
                plt.plot(x, y, 'o', color=colors[i], markersize=8)
                plt.text(x+5, y+5, corner_names[i], color='white', fontsize=12,
                         bbox=dict(facecolor='black', alpha=0.7))

        plt.title('All Heatmaps (Blue Background)')
        plt.savefig(os.path.join(sample_dir, "all_heatmaps_blue_bg.jpg"))
        plt.close()

        # 4. Combined visualization with all 4 heatmaps overlaid on original image
        plt.figure(figsize=(8, 8))
        plt.imshow(vis_image)

        # Add each heatmap with a different color and transparency
        # Use consistent colormaps for each corner type
        corner_cmaps = ['Blues', 'Greens', 'Reds', 'cool']  # TL, TR, BR, BL
        for i in range(min(4, heatmaps.shape[0])):
            heatmap = heatmaps[i].numpy()
            plt.imshow(heatmap, alpha=0.3, cmap=corner_cmaps[i], vmin=0, vmax=1)

            # Add corner point if available
            if isinstance(corners, torch.Tensor) and corners.numel() >= 8:
                x, y = corners[i*2].item(), corners[i*2+1].item()
                plt.plot(x, y, 'o', color=colors[i], markersize=8)
                plt.text(x+5, y+5, corner_names[i], color='white', fontsize=12,
                         bbox=dict(facecolor='black', alpha=0.7))

        plt.title('All Heatmaps (Overlaid)')
        plt.savefig(os.path.join(sample_dir, "all_heatmaps_overlaid.jpg"))
        plt.close()

        # 5. Comprehensive visualization: original image + mask + heatmaps + keypoints
        plt.figure(figsize=(10, 10))

        # Create a composite image
        # Start with original image
        composite = vis_image.copy()

        # Add segmentation mask as a semi-transparent overlay
        mask_overlay = np.zeros_like(composite, dtype=np.uint8)
        mask_rgb = np.stack([vis_mask, vis_mask, vis_mask], axis=2)
        mask_overlay = np.where(mask_rgb > 0, [0, 255, 0], [0, 0, 0]).astype(np.uint8)  # Green mask
        composite = cv2.addWeighted(composite, 1.0, mask_overlay, 0.3, 0)

        # Create a heatmap overlay
        heatmap_overlay = np.zeros_like(composite, dtype=np.float32)

        # Add each heatmap with a different color
        for i in range(min(4, heatmaps.shape[0])):
            heatmap = heatmaps[i].numpy()

            # Normalize heatmap
            if np.max(heatmap) > 0:
                heatmap = heatmap / np.max(heatmap)

            # Create colored heatmap with consistent colors
            if i == 0:  # TL - Blue
                heatmap_overlay[:, :, 2] += heatmap * 255
            elif i == 1:  # TR - Green
                heatmap_overlay[:, :, 1] += heatmap * 255
            elif i == 2:  # BR - Red
                heatmap_overlay[:, :, 0] += heatmap * 255
            elif i == 3:  # BL - Cyan
                heatmap_overlay[:, :, 1] += heatmap * 255
                heatmap_overlay[:, :, 2] += heatmap * 255

        # Clip values to valid range
        heatmap_overlay = np.clip(heatmap_overlay, 0, 255).astype(np.uint8)

        # Add heatmap overlay to composite
        composite = cv2.addWeighted(composite, 0.7, heatmap_overlay, 0.3, 0)

        # Add keypoints
        if isinstance(corners, torch.Tensor) and corners.numel() >= 8:
            # Create a copy for drawing
            keypoint_overlay = composite.copy()

            # Draw keypoints with the correct labels and colors
            for i in range(4):
                x, y = int(corners[i*2].item()), int(corners[i*2+1].item())

                # If corners were reordered due to flipping, use the original indices to get the correct color
                if 'corner_indices' in locals() and is_flipped:
                    # Use the original corner index to determine the color
                    # This ensures the color matches the corner label (TL, TR, BR, BL)
                    # TL: Blue (0, 0, 255), TR: Green (0, 255, 0), BR: Red (255, 0, 0), BL: Cyan (0, 255, 255)
                    color_index = i  # Default to current index
                else:
                    # No reordering, use the current index
                    color_index = i

                # BGR format for OpenCV
                colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 255, 255)]
                color = colors[color_index]

                cv2.circle(keypoint_overlay, (x, y), 5, color, -1)
                cv2.putText(keypoint_overlay, corner_names[i], (x+5, y+5),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 2)

            # Add keypoint overlay to composite
            composite = keypoint_overlay

        # Save the composite visualization
        cv2.imwrite(os.path.join(sample_dir, "comprehensive_visualization.jpg"),
                   cv2.cvtColor(composite, cv2.COLOR_RGB2BGR))

        # Also create a matplotlib version for better visualization
        plt.imshow(composite)
        plt.title('Comprehensive Visualization')
        plt.axis('off')
        plt.savefig(os.path.join(sample_dir, "comprehensive_visualization_plt.jpg"))
        plt.close()

    # Save metadata
    metadata = {
        "original_sample_index": index,
        "augmentation_index": aug_index,
        "image_shape": list(image.shape) if isinstance(image, torch.Tensor) else list(image.shape),
        "corners": corners.tolist() if isinstance(corners, torch.Tensor) else corners.tolist() if isinstance(corners, np.ndarray) else corners,
    }

    # Add corner orientation information if available
    if 'is_flipped' in locals():
        metadata["corner_orientation"] = {
            "is_flipped": bool(is_flipped),
            "corner_names": ["TL", "TR", "BR", "BL"],
            "original_indices": corner_indices
        }

    with open(os.path.join(sample_dir, "metadata.json"), 'w') as f:
        json.dump(metadata, f, indent=4)

    return sample_dir

def generate_augmented_dataset(num_samples=None, num_augmentations=5, version="v5.2", exclude_images=None):
    """
    Generate and save an augmented dataset.

    Args:
        num_samples: Number of original samples to use (None for all)
        num_augmentations: Number of augmentations per sample
        version: Version string for the output directory
        exclude_images: List of image filenames to exclude (default: ["6.jpg"])

    Returns:
        Path to the generated dataset
    """
    # Set default exclude list if None
    if exclude_images is None:
        exclude_images = ["6.jpg"]  # Default to excluding 6.jpg

    # Load dataset
    data_dir = os.path.join(DATA_DIR, 'real')
    annotation_file = os.path.join(DATA_DIR, 'real_annotations.json')

    # Load annotations to filter out excluded images
    with open(annotation_file, 'r') as f:
        annotations = json.load(f)

    # Filter out excluded images
    filtered_annotations = [ann for ann in annotations if ann['image'] not in exclude_images]
    print(f"Excluding images: {exclude_images}")
    print(f"Original annotation count: {len(annotations)}, Filtered count: {len(filtered_annotations)}")

    # Save filtered annotations to a temporary file
    temp_annotation_file = os.path.join(DATA_DIR, 'temp_annotations.json')
    with open(temp_annotation_file, 'w') as f:
        json.dump(filtered_annotations, f, indent=2)

    # Create dataset with augmentation
    dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=temp_annotation_file,
        transform=get_enhanced_training_augmentation()
    )

    # Create output directory
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join('data', 'augmented', version, f"augmented_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    # Create a dataset without augmentation for original samples
    no_aug_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=temp_annotation_file,
        transform=None
    )

    # Determine number of samples to process
    if num_samples is None:
        num_samples = len(dataset)
    else:
        num_samples = min(num_samples, len(dataset))

    # Create a summary file
    summary_file = os.path.join(output_dir, "dataset_summary.txt")
    with open(summary_file, 'w') as f:
        f.write(f"Augmented Dataset Summary\n")
        f.write(f"========================\n\n")
        f.write(f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Version: {version}\n")
        f.write(f"Original samples: {num_samples}\n")
        f.write(f"Augmentations per sample: {num_augmentations}\n")
        f.write(f"Total augmented samples: {num_samples * num_augmentations}\n\n")
        f.write(f"Sample Details:\n")

    # Copy the original annotation file for reference
    shutil.copy(annotation_file, os.path.join(output_dir, "original_annotations.json"))

    # Create a new annotation file for the augmented dataset
    augmented_annotations = {}

    # Process each sample
    for i in tqdm(range(num_samples), desc="Generating augmented samples"):
        # Get original sample without augmentation
        original_sample = no_aug_dataset[i]

        # Save original sample
        original_dir = save_augmented_sample(original_sample, output_dir, i, -1)

        # Log to summary file
        with open(summary_file, 'a') as f:
            f.write(f"\nSample {i+1} (Original):\n")
            f.write(f"  - Saved to: {os.path.relpath(original_dir, output_dir)}\n")

        # Generate augmentations
        for j in range(num_augmentations):
            # Get augmented sample
            augmented_sample = dataset[i]

            # Save augmented sample
            aug_dir = save_augmented_sample(augmented_sample, output_dir, i, j)

            # Check if all corners are within image boundaries
            corners = augmented_sample['corners']
            if isinstance(corners, torch.Tensor):
                corners = corners.numpy()

            image = augmented_sample['image']
            if isinstance(image, torch.Tensor):
                img_h, img_w = image.shape[1:3]
            else:
                img_h, img_w = image.shape[:2]

            all_corners_valid = True
            for k in range(4):
                x, y = corners[k*2], corners[k*2+1]
                if not (0 <= x < img_w and 0 <= y < img_h):
                    all_corners_valid = False
                    break

            # Log to summary file
            status = "valid" if all_corners_valid else "invalid"
            with open(summary_file, 'a') as f:
                f.write(f"  - Augmentation {j+1}: {status}, saved to: {os.path.relpath(aug_dir, output_dir)}\n")

            # Add to augmented annotations
            sample_id = f"sample_{i+1}_aug_{j+1}"

            # Include corner orientation information if available
            annotation_entry = {
                "corners": corners.tolist() if isinstance(corners, np.ndarray) else corners,
                "valid": all_corners_valid
            }

            # Check if we have corner orientation information from the detection
            if 'is_flipped' in locals():
                annotation_entry["corner_orientation"] = {
                    "is_flipped": bool(is_flipped),
                    "corner_names": ["TL", "TR", "BR", "BL"],
                    "original_indices": corner_indices
                }

            augmented_annotations[sample_id] = annotation_entry

    # Save augmented annotations
    with open(os.path.join(output_dir, "augmented_annotations.json"), 'w') as f:
        json.dump(augmented_annotations, f, indent=4)

    print(f"Augmented dataset generated at: {output_dir}")
    print(f"Total samples: {num_samples * (num_augmentations + 1)}")

    # Clean up temporary file
    if os.path.exists(temp_annotation_file):
        os.remove(temp_annotation_file)
        print(f"Removed temporary annotation file: {temp_annotation_file}")

    return output_dir

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Generate augmented dataset for training")
    parser.add_argument("--num_samples", type=int, default=None, help="Number of original samples to use (default: all)")
    parser.add_argument("--num_augmentations", type=int, default=5, help="Number of augmentations per sample (default: 5)")
    parser.add_argument("--version", type=str, default="v5.2", help="Version string for the output directory (default: v5.2)")
    parser.add_argument("--exclude", type=str, nargs='+', default=["6.jpg"], help="List of image filenames to exclude (default: 6.jpg)")

    args = parser.parse_args()

    generate_augmented_dataset(args.num_samples, args.num_augmentations, args.version, args.exclude)
