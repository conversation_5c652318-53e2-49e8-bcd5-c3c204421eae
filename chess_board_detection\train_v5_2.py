"""
Training script for the v5.2 chess board detection model.
This version implements the balanced improvement strategy with three phases:
1. Phase 1 (20 epochs): Balanced foundation with smooth convergence path
2. Phase 2 (40 epochs): Proportional improvement with targeted convergence
3. Phase 3 (20 epochs): Final convergence and harmonization

Key improvements:
1. Peak Competition Module for enhanced primary peaks and suppressed secondary peaks
2. Cross-Attention Mechanism for better segmentation-heatmap integration
3. Balanced Peak-to-Second Ratio Loss with adaptive targets
4. Multi-Loss Convergence Scheduler for smooth convergence of all components
5. Metric-Specific Learning Rate Adjustment for targeted improvement
6. Loss Component Balancer for preventing any single loss from dominating
7. Detailed metrics tracking for peak-to-second ratio
"""

import os
import argparse
import time
import json
import inspect
import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
import torchvision
from torch.utils.data import DataLoader, ConcatDataset
from tqdm import tqdm
from sklearn.model_selection import KFold
from sklearn.metrics import confusion_matrix
import albumentations as A
from albumentations.pytorch import ToTensorV2
import cv2
from matplotlib.colors import LinearSegmentedColormap
from torch.utils.tensorboard import SummaryWriter
import subprocess
from collections import defaultdict

# Import our improved modules
from ensemble_v5_2 import DynamicEnsembleModel
from hyperparameter_optimizer_v5_2 import BayesianHyperparameterOptimizer

from config import MODELS_DIR, DATA_DIR, DEVICE, INPUT_SIZE
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from enhanced_loss_v5_2 import (
    EnhancedCornerFocusedHeatmapLossV5_2,
    MultiLossConvergenceScheduler,
    MetricSpecificLROptimizer,
    LossComponentBalancer
)
from enhanced_loss_v5 import RobustSegmentationGuidanceLoss, StabilizedGeometricLoss
from enhanced_loss_v4 import EnhancedDiceLoss
from utils.real_dataset import RealChessBoardDataset
from utils.augmented_dataset import AugmentedDataset, create_augmented_dataloaders
from utils.metrics import calculate_corner_confidence


# Visualization functions for heatmaps and keypoint detection
def visualize_heatmaps_and_keypoints(images, heatmaps, segmentations, target_heatmaps=None,
                                    save_path=None, max_samples=4, figsize=(15, 10), dpi=100):
    """
    Visualize input images, predicted heatmaps, segmentations, and detected keypoints.

    Args:
        images: Input images (B, C, H, W)
        heatmaps: Predicted heatmaps (B, 4, H, W)
        segmentations: Predicted segmentation masks (B, 1, H, W)
        target_heatmaps: Optional target heatmaps (B, 4, H, W)
        save_path: Path to save the visualization
        max_samples: Maximum number of samples to visualize
        figsize: Figure size
    """
    # Create a custom colormap for heatmaps (blue to red)
    colors = [(0, 0, 1), (0, 1, 1), (0, 1, 0), (1, 1, 0), (1, 0, 0)]
    cmap_name = 'blue_to_red'
    cm = LinearSegmentedColormap.from_list(cmap_name, colors, N=100)

    # Determine number of samples to visualize
    batch_size = min(images.size(0), max_samples)

    # Determine number of rows and columns in the figure
    if target_heatmaps is not None:
        n_rows = 4  # Input, Segmentation, Predicted Heatmaps, Target Heatmaps
    else:
        n_rows = 3  # Input, Segmentation, Predicted Heatmaps

    n_cols = batch_size

    # Create figure
    plt.figure(figsize=figsize)

    # Denormalize images for visualization
    mean = torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1).to(images.device)
    std = torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1).to(images.device)
    denorm_images = images * std + mean

    # Visualize each sample
    for i in range(batch_size):
        # Get sample data
        img = denorm_images[i].permute(1, 2, 0).cpu().detach().numpy()
        img = np.clip(img, 0, 1)  # Clip to valid range

        seg = segmentations[i, 0].cpu().detach().numpy()

        # Plot input image
        plt.subplot(n_rows, n_cols, i + 1)
        plt.imshow(img)
        plt.title(f'Sample {i+1}')
        plt.axis('off')

        # Plot segmentation mask
        plt.subplot(n_rows, n_cols, i + 1 + n_cols)
        plt.imshow(seg, cmap='gray')
        plt.title(f'Segmentation {i+1}')
        plt.axis('off')

        # Create combined heatmap visualization
        combined_heatmap = np.zeros((heatmaps.size(2), heatmaps.size(3), 3))

        # Detect keypoints from heatmaps
        keypoints = []
        for c in range(4):
            hm = heatmaps[i, c].cpu().detach().numpy()

            # Find peak location
            peak_idx = np.argmax(hm)
            y, x = peak_idx // hm.shape[1], peak_idx % hm.shape[1]

            # Only consider peaks above threshold
            if hm[y, x] > 0.3:
                keypoints.append((x, y))

                # Add this heatmap to the combined visualization with a different color for each corner
                color_idx = c / 3.0  # Normalize to [0, 1]
                color = plt.cm.jet(color_idx)[:3]  # Get RGB from colormap

                # Normalize heatmap for visualization
                norm_hm = hm / (np.max(hm) + 1e-8)

                # Add to combined heatmap
                for ch in range(3):
                    combined_heatmap[:, :, ch] += norm_hm * color[ch]
            else:
                keypoints.append(None)

        # Normalize combined heatmap
        if np.max(combined_heatmap) > 0:
            combined_heatmap = combined_heatmap / np.max(combined_heatmap)

        # Plot predicted heatmaps with keypoints
        plt.subplot(n_rows, n_cols, i + 1 + 2*n_cols)
        plt.imshow(img)  # Show image as background
        plt.imshow(combined_heatmap, alpha=0.7)  # Overlay heatmap with transparency

        # Plot detected keypoints
        corner_colors = ['r', 'g', 'b', 'y']  # Colors for each corner
        corner_names = ['TL', 'TR', 'BR', 'BL']  # Names for each corner

        for j, kp in enumerate(keypoints):
            if kp is not None:
                plt.plot(kp[0], kp[1], 'o', color=corner_colors[j], markersize=8)
                plt.text(kp[0]+5, kp[1]+5, corner_names[j], color=corner_colors[j],
                        fontsize=12, fontweight='bold')

        plt.title(f'Pred Heatmaps & Keypoints {i+1}')
        plt.axis('off')

        # Plot target heatmaps if provided
        if target_heatmaps is not None:
            # Create combined target heatmap visualization
            combined_target_hm = np.zeros((target_heatmaps.size(2), target_heatmaps.size(3), 3))

            # Detect keypoints from target heatmaps
            target_keypoints = []
            for c in range(4):
                hm = target_heatmaps[i, c].cpu().detach().numpy()

                # Find peak location
                peak_idx = np.argmax(hm)
                y, x = peak_idx // hm.shape[1], peak_idx % hm.shape[1]

                # Only consider peaks above threshold
                if hm[y, x] > 0.3:
                    target_keypoints.append((x, y))

                    # Add this heatmap to the combined visualization
                    color_idx = c / 3.0  # Normalize to [0, 1]
                    color = plt.cm.jet(color_idx)[:3]  # Get RGB from colormap

                    # Normalize heatmap for visualization
                    norm_hm = hm / (np.max(hm) + 1e-8)

                    # Add to combined heatmap
                    for ch in range(3):
                        combined_target_hm[:, :, ch] += norm_hm * color[ch]
                else:
                    target_keypoints.append(None)

            # Normalize combined target heatmap
            if np.max(combined_target_hm) > 0:
                combined_target_hm = combined_target_hm / np.max(combined_target_hm)

            # Plot target heatmaps with keypoints
            plt.subplot(n_rows, n_cols, i + 1 + 3*n_cols)
            plt.imshow(img)  # Show image as background
            plt.imshow(combined_target_hm, alpha=0.7)  # Overlay heatmap with transparency

            # Plot target keypoints
            for j, kp in enumerate(target_keypoints):
                if kp is not None:
                    plt.plot(kp[0], kp[1], 'o', color=corner_colors[j], markersize=8)
                    plt.text(kp[0]+5, kp[1]+5, corner_names[j], color=corner_colors[j],
                            fontsize=12, fontweight='bold')

            plt.title(f'Target Heatmaps & Keypoints {i+1}')
            plt.axis('off')

    plt.tight_layout()

    # Save figure if save_path is provided
    if save_path:
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight')
        plt.close()
    else:
        plt.show()


def visualize_peak_to_second_ratio(heatmaps, save_path=None, max_samples=4, figsize=(15, 10), dpi=100):
    """
    Visualize the peak-to-second ratio for each corner heatmap.

    Args:
        heatmaps: Predicted heatmaps (B, 4, H, W)
        save_path: Path to save the visualization
        max_samples: Maximum number of samples to visualize
        figsize: Figure size
    """
    # Determine number of samples to visualize
    batch_size = min(heatmaps.size(0), max_samples)

    # Create figure
    plt.figure(figsize=figsize)

    # Corner names
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Visualize each sample
    for i in range(batch_size):
        for c in range(4):
            # Get heatmap
            hm = heatmaps[i, c].cpu().detach().numpy()

            # Find primary peak
            primary_idx = np.argmax(hm)
            primary_y, primary_x = primary_idx // hm.shape[1], primary_idx % hm.shape[1]
            primary_val = hm[primary_y, primary_x]

            # Create a mask to exclude the primary peak and its surroundings
            mask = np.ones_like(hm)
            radius = 5
            y_min, y_max = max(0, primary_y - radius), min(hm.shape[0], primary_y + radius + 1)
            x_min, x_max = max(0, primary_x - radius), min(hm.shape[1], primary_x + radius + 1)
            mask[y_min:y_max, x_min:x_max] = 0

            # Find secondary peak
            masked_hm = hm * mask
            secondary_idx = np.argmax(masked_hm)
            secondary_y, secondary_x = secondary_idx // hm.shape[1], secondary_idx % hm.shape[1]
            secondary_val = hm[secondary_y, secondary_x]

            # Calculate peak-to-second ratio
            if secondary_val > 0:
                p2s_ratio = primary_val / secondary_val
            else:
                p2s_ratio = float('inf')

            # Plot heatmap with primary and secondary peaks
            plt.subplot(batch_size, 4, i*4 + c + 1)
            plt.imshow(hm, cmap='jet')

            # Plot primary peak
            plt.plot(primary_x, primary_y, 'ro', markersize=10)
            plt.text(primary_x+5, primary_y+5, f'P: {primary_val:.2f}', color='white',
                    fontsize=10, fontweight='bold', bbox=dict(facecolor='red', alpha=0.5))

            # Plot secondary peak
            plt.plot(secondary_x, secondary_y, 'bo', markersize=8)
            plt.text(secondary_x+5, secondary_y+5, f'S: {secondary_val:.2f}', color='white',
                    fontsize=10, fontweight='bold', bbox=dict(facecolor='blue', alpha=0.5))

            # Add peak-to-second ratio to title
            plt.title(f'Sample {i+1}, {corner_names[c]}\nP2S Ratio: {p2s_ratio:.2f}')
            plt.axis('off')

    plt.tight_layout()

    # Save figure if save_path is provided
    if save_path:
        plt.savefig(save_path, dpi=dpi, bbox_inches='tight')
        plt.close()
    else:
        plt.show()


def calculate_corner_confusion_matrix(pred_heatmaps, target_heatmaps, threshold=0.5):
    """
    Calculate confusion matrix for corner types.

    This function analyzes which corner types are most challenging for the model
    by comparing predicted and target corner locations.

    Args:
        pred_heatmaps: Predicted heatmaps (B, 4, H, W)
        target_heatmaps: Target heatmaps (B, 4, H, W)
        threshold: Detection threshold

    Returns:
        conf_matrix: 4x4 confusion matrix for corner types
        corner_accuracies: Accuracy for each corner type
        misclassification_rates: Rate at which each corner is misclassified as another
    """
    batch_size = pred_heatmaps.size(0)
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Initialize confusion matrix
    conf_matrix = np.zeros((4, 4), dtype=np.int32)

    # Initialize detection counters
    true_positives = np.zeros(4)
    false_positives = np.zeros(4)
    false_negatives = np.zeros(4)

    # Process each sample in the batch
    for b in range(batch_size):
        # For each corner type
        for true_corner in range(4):
            # Get target heatmap for this corner
            target_hm = target_heatmaps[b, true_corner].cpu().detach().numpy()

            # Find target peak location
            target_peak_idx = np.argmax(target_hm)
            target_y, target_x = target_peak_idx // target_hm.shape[1], target_peak_idx % target_hm.shape[1]
            target_val = target_hm[target_y, target_x]

            # Skip if target peak is below threshold (no corner in ground truth)
            if target_val < threshold:
                continue

            # Check which predicted corner is closest to this target corner
            best_match = -1
            min_distance = float('inf')

            for pred_corner in range(4):
                # Get predicted heatmap for this corner
                pred_hm = pred_heatmaps[b, pred_corner].cpu().detach().numpy()

                # Find predicted peak location
                pred_peak_idx = np.argmax(pred_hm)
                pred_y, pred_x = pred_peak_idx // pred_hm.shape[1], pred_peak_idx % pred_hm.shape[1]
                pred_val = pred_hm[pred_y, pred_x]

                # Skip if predicted peak is below threshold
                if pred_val < threshold:
                    continue

                # Calculate distance between predicted and target peaks
                distance = np.sqrt((pred_y - target_y)**2 + (pred_x - target_x)**2)

                # Update best match if this is closer
                if distance < min_distance:
                    min_distance = distance
                    best_match = pred_corner

            # Update confusion matrix
            if best_match != -1 and min_distance < 20:  # Consider as a match if within 20 pixels
                conf_matrix[true_corner, best_match] += 1

                # Update detection counters
                if true_corner == best_match:
                    true_positives[true_corner] += 1
                else:
                    false_positives[best_match] += 1
                    false_negatives[true_corner] += 1
            else:
                # No match found - false negative
                false_negatives[true_corner] += 1

    # Calculate accuracy for each corner type
    corner_accuracies = np.zeros(4)
    for i in range(4):
        if conf_matrix[i].sum() > 0:
            corner_accuracies[i] = conf_matrix[i, i] / conf_matrix[i].sum()

    # Calculate misclassification rates
    misclassification_rates = np.zeros((4, 4))
    for i in range(4):
        for j in range(4):
            if i != j and conf_matrix[i].sum() > 0:
                misclassification_rates[i, j] = conf_matrix[i, j] / conf_matrix[i].sum()

    return conf_matrix, corner_accuracies, misclassification_rates


def visualize_data_augmentations(dataset, num_samples=2, num_augmentations=3, save_path=None, figsize=(10, 8), max_image_size=(256, 256)):
    """
    Visualize data augmentations applied to samples from the dataset.

    Args:
        dataset: Dataset with augmentation transform
        num_samples: Number of original samples to visualize
        num_augmentations: Number of augmentations to apply to each sample
        save_path: Path to save the visualization
        figsize: Figure size
        max_image_size: Maximum size of images to visualize (width, height)
    """
    # Create figure
    plt.figure(figsize=figsize)

    # Get augmentation transform from dataset
    transform = dataset.transform if hasattr(dataset, 'transform') else None
    if transform is None:
        print("Warning: Dataset does not have a transform attribute")
        return

    # Get samples from dataset
    samples = []
    for i in range(min(num_samples, len(dataset))):
        try:
            sample = dataset[i]
            if isinstance(sample, dict) and 'image' in sample and 'mask' in sample:
                # Get original image and mask before transformation
                img_path = sample.get('image_path', f'Sample {i+1}')
                if isinstance(img_path, str) and os.path.exists(img_path):
                    orig_img = cv2.imread(img_path)
                    if orig_img is not None:
                        # Resize image to limit memory usage
                        h, w = orig_img.shape[:2]
                        max_w, max_h = max_image_size
                        if w > max_w or h > max_h:
                            scale = min(max_w / w, max_h / h)
                            new_w, new_h = int(w * scale), int(h * scale)
                            orig_img = cv2.resize(orig_img, (new_w, new_h), interpolation=cv2.INTER_AREA)

                        orig_img = cv2.cvtColor(orig_img, cv2.COLOR_BGR2RGB)

                        # Convert mask to numpy if it's a tensor
                        if isinstance(sample['mask'], torch.Tensor):
                            mask_np = sample['mask'].numpy()
                        else:
                            mask_np = sample['mask']

                        # Resize mask to match image
                        if mask_np.shape[:2] != orig_img.shape[:2]:
                            mask_np = cv2.resize(mask_np, (orig_img.shape[1], orig_img.shape[0]),
                                               interpolation=cv2.INTER_NEAREST)

                        samples.append((orig_img, mask_np, img_path))
        except Exception as e:
            print(f"Warning: Could not get sample {i}: {e}")

    if not samples:
        print("Warning: No valid samples found in dataset")
        return

    # Visualize each sample and its augmentations
    for i, (orig_img, orig_mask, img_path) in enumerate(samples):
        # Plot original image
        plt.subplot(num_samples, num_augmentations + 1, i * (num_augmentations + 1) + 1)
        plt.imshow(orig_img)
        plt.title(f"Original {i+1}")
        plt.axis('off')

        # Apply augmentations
        for j in range(num_augmentations):
            try:
                # Apply augmentation
                augmented = transform(image=orig_img, mask=orig_mask)
                aug_img = augmented['image']
                aug_mask = augmented['mask']

                # Convert tensor to numpy if needed
                if isinstance(aug_img, torch.Tensor):
                    # Denormalize
                    mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
                    std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
                    aug_img = aug_img * std + mean
                    aug_img = aug_img.permute(1, 2, 0).numpy()
                    aug_img = np.clip(aug_img, 0, 1)

                if isinstance(aug_mask, torch.Tensor):
                    aug_mask = aug_mask.numpy()

                # Plot augmented image
                plt.subplot(num_samples, num_augmentations + 1, i * (num_augmentations + 1) + j + 2)
                plt.imshow(aug_img)
                plt.title(f"Aug {j+1}")
                plt.axis('off')
            except Exception as e:
                print(f"Warning: Could not apply augmentation {j} to sample {i}: {e}")

    plt.tight_layout()

    # Save figure if save_path is provided
    if save_path:
        plt.savefig(save_path, dpi=100, bbox_inches='tight')
        plt.close()
    else:
        plt.show()


def visualize_corner_confusion_matrix(conf_matrix, corner_accuracies, save_path=None, figsize=(10, 8)):
    """
    Visualize the confusion matrix for corner types.

    Args:
        conf_matrix: 4x4 confusion matrix for corner types
        corner_accuracies: Accuracy for each corner type
        save_path: Path to save the visualization
        figsize: Figure size
    """
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']

    # Create figure
    plt.figure(figsize=figsize)

    # Plot confusion matrix
    plt.imshow(conf_matrix, interpolation='nearest', cmap=plt.cm.Blues)
    plt.title('Corner Type Confusion Matrix')
    plt.colorbar()

    # Add labels
    tick_marks = np.arange(4)
    plt.xticks(tick_marks, corner_names, rotation=45)
    plt.yticks(tick_marks, corner_names)

    # Add text annotations
    thresh = conf_matrix.max() / 2.0
    for i in range(4):
        for j in range(4):
            plt.text(j, i, format(conf_matrix[i, j], 'd'),
                    ha="center", va="center",
                    color="white" if conf_matrix[i, j] > thresh else "black")

    # Add accuracy information
    for i in range(4):
        plt.text(4.1, i, f"{corner_accuracies[i]:.2f}",
                ha="center", va="center",
                color="black", fontweight='bold')
    plt.text(4.1, -0.5, "Accuracy",
            ha="center", va="center",
            color="black", fontweight='bold')

    plt.tight_layout()
    plt.ylabel('True Corner')
    plt.xlabel('Predicted Corner')

    # Save figure if save_path is provided
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    else:
        plt.show()


# Enhanced data augmentation
def get_enhanced_training_augmentation():
    """
    Enhanced training augmentation with more aggressive transformations
    to improve model generalization.
    """
    return A.Compose([
        # Spatial augmentations
        A.RandomResizedCrop(size=(256, 256), scale=(0.7, 1.0), p=1.0),
        A.Affine(
            scale=(0.8, 1.2),
            translate_percent=(0.12, 0.12),
            rotate=(-45, 45),
            shear=(-10, 10),
            p=0.9
        ),
        A.Perspective(scale=(0.05, 0.2), p=0.8),
        A.GridDistortion(num_steps=5, distort_limit=0.3, p=0.6),
        A.ElasticTransform(alpha=1, sigma=50, p=0.4),

        # Color augmentations
        A.RandomBrightnessContrast(brightness_limit=0.4, contrast_limit=0.4, p=0.9),
        A.HueSaturationValue(hue_shift_limit=20, sat_shift_limit=30, val_shift_limit=20, p=0.7),
        A.CLAHE(clip_limit=4.0, p=0.5),
        A.RandomGamma(gamma_limit=(80, 120), p=0.6),

        # Noise and blur
        A.OneOf([
            A.GaussNoise(p=1.0),
            A.GaussianBlur(blur_limit=7, p=1.0),
            A.MotionBlur(blur_limit=7, p=1.0),
            A.Blur(blur_limit=5, p=1.0)
        ], p=0.7),

        # Normalization
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def get_enhanced_validation_augmentation():
    """
    Enhanced validation augmentation with light transformations
    to better evaluate model robustness.
    """
    return A.Compose([
        A.Resize(height=256, width=256),
        A.Affine(
            scale=(0.9, 1.1),
            translate_percent=(0.05, 0.05),
            rotate=(-15, 15),
            p=0.5
        ),
        A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=0.5),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


# Custom collate function to handle different batch sizes
def custom_collate_fn(batch):
    """
    Custom collate function to handle batches with different sizes.
    """
    # Filter out None values
    batch = [item for item in batch if item is not None]
    if len(batch) == 0:
        return None

    # Create a batch dictionary
    batch_dict = {}
    for key in batch[0].keys():
        batch_dict[key] = [item[key] for item in batch]

    # Stack tensors with error handling
    for key in batch_dict.keys():
        if key == 'corners':
            # Special handling for corners - ensure all are tensors with shape [8]
            try:
                # Convert all corners to tensors with shape [8]
                processed_corners = []
                for item in batch_dict[key]:
                    if isinstance(item, torch.Tensor):
                        # Ensure tensor has shape [8]
                        if item.shape != torch.Size([8]):
                            # Check if we have any valid corner data
                            if item.numel() > 0:
                                # Use the available corners and pad only what's missing
                                padded = torch.zeros(8, dtype=torch.float32)
                                padded[:min(8, item.numel())] = item.flatten()[:min(8, item.numel())]
                                processed_corners.append(padded)
                            else:
                                # If we have no corners at all, this is likely an issue with the dataset
                                # We'll use the mean of other corners in the batch as a fallback
                                # This is better than using zeros which would mislead the model
                                processed_corners.append(None)  # Temporarily mark as None to fill later
                        else:
                            processed_corners.append(item)
                    elif isinstance(item, np.ndarray):
                        # Convert numpy array to tensor and ensure shape [8]
                        tensor = torch.from_numpy(item.flatten()).float()
                        if tensor.shape != torch.Size([8]):
                            if tensor.numel() > 0:
                                padded = torch.zeros(8, dtype=torch.float32)
                                padded[:min(8, tensor.numel())] = tensor[:min(8, tensor.numel())]
                                processed_corners.append(padded)
                            else:
                                processed_corners.append(None)  # Temporarily mark as None to fill later
                        else:
                            processed_corners.append(tensor)
                    else:
                        # Mark as None to fill later
                        processed_corners.append(None)

                # Replace None values with the mean of valid corners in the batch
                valid_corners = [c for c in processed_corners if c is not None]
                if valid_corners:
                    # Calculate mean of valid corners
                    mean_corners = torch.mean(torch.stack(valid_corners), dim=0)
                    # Replace None values with mean corners
                    for i in range(len(processed_corners)):
                        if processed_corners[i] is None:
                            processed_corners[i] = mean_corners
                else:
                    # If no valid corners in the batch, use a default pattern
                    # This creates a simple square in the center of the image
                    default_corners = torch.tensor([
                        64.0, 64.0,    # Top-left
                        192.0, 64.0,   # Top-right
                        64.0, 192.0,   # Bottom-left
                        192.0, 192.0   # Bottom-right
                    ], dtype=torch.float32)

                    # Replace all None values with default corners
                    for i in range(len(processed_corners)):
                        if processed_corners[i] is None:
                            processed_corners[i] = default_corners

                # Now all corners should be tensors with shape [8]
                batch_dict[key] = torch.stack(processed_corners)
            except Exception as e:
                # If all else fails, use a default pattern for all corners in the batch
                default_corners = torch.tensor([
                    64.0, 64.0,    # Top-left
                    192.0, 64.0,   # Top-right
                    64.0, 192.0,   # Bottom-left
                    192.0, 192.0   # Bottom-right
                ], dtype=torch.float32)

                batch_dict[key] = default_corners.repeat(len(batch), 1)
        elif key in ['heatmaps', 'corner_heatmaps']:
            # Special handling for heatmaps
            try:
                # Ensure all heatmaps are tensors with shape [4, H, W]
                processed_heatmaps = []
                for item_idx, item in enumerate(batch_dict[key]):
                    if isinstance(item, torch.Tensor):
                        # Check if tensor has correct shape (should be [4, H, W])
                        if len(item.shape) == 3 and item.shape[0] == 4:
                            processed_heatmaps.append(item)
                        else:
                            # If we have corners for this item, generate heatmaps from them
                            if 'corners' in batch_dict and item_idx < len(batch_dict['corners']):
                                corners = batch_dict['corners'][item_idx]
                                if isinstance(corners, torch.Tensor) and corners.numel() == 8:
                                    # Generate Gaussian heatmaps from corners
                                    heatmap = torch.zeros((4, 256, 256), dtype=torch.float32)
                                    for i in range(4):
                                        x, y = corners[i*2], corners[i*2+1]
                                        # Create Gaussian peak
                                        x_grid = torch.arange(0, 256, dtype=torch.float32)
                                        y_grid = torch.arange(0, 256, dtype=torch.float32).unsqueeze(1)
                                        sigma = 5.0
                                        heatmap[i] = torch.exp(-((x_grid - x)**2 + (y_grid - y)**2) / (2 * sigma**2))
                                    processed_heatmaps.append(heatmap)
                                else:
                                    # If corners are invalid, use mean of other heatmaps
                                    processed_heatmaps.append(None)
                            else:
                                # If no corners available, mark for later replacement
                                processed_heatmaps.append(None)
                    else:
                        # Mark for later replacement
                        processed_heatmaps.append(None)

                # Replace None values with the mean of valid heatmaps
                valid_heatmaps = [h for h in processed_heatmaps if h is not None]
                if valid_heatmaps:
                    mean_heatmap = torch.mean(torch.stack(valid_heatmaps), dim=0)
                    for i in range(len(processed_heatmaps)):
                        if processed_heatmaps[i] is None:
                            processed_heatmaps[i] = mean_heatmap
                else:
                    # If no valid heatmaps, create basic heatmaps from default corners
                    default_corners = torch.tensor([
                        64.0, 64.0,    # Top-left
                        192.0, 64.0,   # Top-right
                        192.0, 192.0,  # Bottom-right
                        64.0, 192.0    # Bottom-left
                    ], dtype=torch.float32)

                    default_heatmap = torch.zeros((4, 256, 256), dtype=torch.float32)
                    for i in range(4):
                        x, y = default_corners[i*2], default_corners[i*2+1]
                        # Create Gaussian peak
                        x_grid = torch.arange(0, 256, dtype=torch.float32)
                        y_grid = torch.arange(0, 256, dtype=torch.float32).unsqueeze(1)
                        sigma = 5.0
                        default_heatmap[i] = torch.exp(-((x_grid - x)**2 + (y_grid - y)**2) / (2 * sigma**2))

                    for i in range(len(processed_heatmaps)):
                        if processed_heatmaps[i] is None:
                            processed_heatmaps[i] = default_heatmap

                # Now all heatmaps should be tensors with shape [4, H, W]
                batch_dict[key] = torch.stack(processed_heatmaps)
            except Exception as e:
                # If all else fails, create basic heatmaps from default corners
                default_corners = torch.tensor([
                    64.0, 64.0,    # Top-left
                    192.0, 64.0,   # Top-right
                    192.0, 192.0,  # Bottom-right
                    64.0, 192.0    # Bottom-left
                ], dtype=torch.float32)

                default_heatmap = torch.zeros((4, 256, 256), dtype=torch.float32)
                for i in range(4):
                    x, y = default_corners[i*2], default_corners[i*2+1]
                    # Create Gaussian peak
                    x_grid = torch.arange(0, 256, dtype=torch.float32)
                    y_grid = torch.arange(0, 256, dtype=torch.float32).unsqueeze(1)
                    sigma = 5.0
                    default_heatmap[i] = torch.exp(-((x_grid - x)**2 + (y_grid - y)**2) / (2 * sigma**2))

                batch_dict[key] = default_heatmap.unsqueeze(0).repeat(len(batch), 1, 1, 1)
        elif isinstance(batch_dict[key][0], torch.Tensor):
            try:
                # Check if all tensors have the same shape
                shapes = [t.shape for t in batch_dict[key]]
                if all(s == shapes[0] for s in shapes):
                    batch_dict[key] = torch.stack(batch_dict[key])
                else:
                    # If tensors have different shapes, keep them as a list
                    print(f"Warning: Tensors with key '{key}' have different shapes: {shapes}. Keeping as list.")
            except Exception as e:
                print(f"Warning: Could not stack tensors with key '{key}': {e}. Keeping as list.")

    return batch_dict


def create_expanded_validation_set(data_dir, annotation_file, fold_idx=0, n_folds=5):
    """
    Create an expanded validation set using k-fold cross-validation with enhanced augmentations.

    Args:
        data_dir: Directory containing the images
        annotation_file: Path to the annotation file
        fold_idx: Index of the fold to use as validation
        n_folds: Number of folds

    Returns:
        train_dataset: Training dataset
        val_dataset: Validation dataset
    """
    # Load dataset
    full_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_enhanced_training_augmentation()
    )

    # Create indices for k-fold cross-validation
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

    # Get train and validation indices
    indices = list(range(len(full_dataset)))
    splits = list(kf.split(indices))
    train_indices, val_indices = splits[fold_idx]

    # Create train and validation datasets
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)

    # Create multiple validation datasets with different augmentations
    val_datasets = []

    # Original validation set
    val_dataset_orig = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_enhanced_validation_augmentation()
    )
    val_dataset_orig = torch.utils.data.Subset(val_dataset_orig, val_indices)
    val_datasets.append(val_dataset_orig)

    # Create additional validation sets with different augmentations
    for i in range(3):  # Create 3 additional validation sets
        # Create custom augmentation for this validation set
        custom_aug = A.Compose([
            A.RandomResizedCrop(size=(256, 256), scale=(0.8, 1.0), p=1.0),
            A.Affine(
                scale=(0.9 + 0.1*i, 1.1 + 0.1*i),
                translate_percent=(0.05*i, 0.05*i),
                rotate=(-15*i, 15*i),
                shear=(-5*i, 5*i),
                p=0.8
            ),
            A.RandomBrightnessContrast(brightness_limit=0.1*i, contrast_limit=0.1*i, p=0.7),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))

        val_dataset_aug = RealChessBoardDataset(
            data_dir=data_dir,
            annotation_file=annotation_file,
            transform=custom_aug
        )
        val_dataset_aug = torch.utils.data.Subset(val_dataset_aug, val_indices)
        val_datasets.append(val_dataset_aug)

    # Combine validation datasets
    val_dataset = ConcatDataset(val_datasets)

    return train_dataset, val_dataset


def train_model_balanced(model, dataloaders, criterion_seg, criterion_heatmap, criterion_geometric,
                        optimizer, scheduler, output_dir, num_epochs=20, heatmap_weight=1.5,
                        geometric_weight=0.8, save_interval=5, phase_name='phase1',
                        auto_tune_hyperparams=True, tune_interval=5,
                        early_stopping_patience=10, max_checkpoints_to_keep=5):
    """
    Balanced training function with advanced metrics tracking and dynamic weight adjustment.

    Args:
        model: Model to train
        dataloaders: Dictionary of dataloaders ('train', 'val')
        criterion_seg: Loss function for segmentation
        criterion_heatmap: Loss function for heatmaps
        criterion_geometric: Loss function for geometric consistency
        optimizer: Optimizer
        scheduler: Learning rate scheduler
        output_dir: Output directory
        num_epochs: Number of epochs
        heatmap_weight: Weight for heatmap loss
        geometric_weight: Weight for geometric loss
        save_interval: Interval to save model checkpoints
        phase_name: Name of the training phase
        auto_tune_hyperparams: Whether to automatically tune hyperparameters
        tune_interval: Interval (in epochs) for hyperparameter tuning
        early_stopping_patience: Number of epochs to wait for improvement before stopping
        max_checkpoints_to_keep: Maximum number of regular checkpoints to keep (best models are always kept)
    """
    since = time.time()

    # Create directories for outputs
    checkpoints_dir = os.path.join(output_dir, 'checkpoints', 'v5.2(3rd attempt)')
    logs_dir = os.path.join(output_dir, 'logs', 'v5.2(3rd attempt)')

    # Create main visualization directory
    vis_dir = os.path.join(output_dir, 'visualizations', 'v5.2(3rd attempt)')

    # Create subdirectories for different visualization types
    vis_heatmaps_dir = os.path.join(vis_dir, 'heatmaps')
    vis_p2s_dir = os.path.join(vis_dir, 'peak_to_second_ratio')
    vis_confusion_dir = os.path.join(vis_dir, 'confusion_matrix')
    vis_losses_dir = os.path.join(vis_dir, 'losses')
    vis_metrics_dir = os.path.join(vis_dir, 'metrics')
    vis_per_corner_dir = os.path.join(vis_dir, 'per_corner_metrics')
    vis_augmentation_dir = os.path.join(vis_dir, 'augmentations')

    tensorboard_dir = os.path.join(output_dir, 'tensorboard', 'v5.2(3rd attempt)', phase_name)

    # Create all directories
    os.makedirs(checkpoints_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)
    os.makedirs(vis_heatmaps_dir, exist_ok=True)
    os.makedirs(vis_p2s_dir, exist_ok=True)
    os.makedirs(vis_confusion_dir, exist_ok=True)
    os.makedirs(vis_losses_dir, exist_ok=True)
    os.makedirs(vis_metrics_dir, exist_ok=True)
    os.makedirs(vis_per_corner_dir, exist_ok=True)
    os.makedirs(vis_augmentation_dir, exist_ok=True)
    os.makedirs(tensorboard_dir, exist_ok=True)

    print(f"Saving outputs to v5.2(3rd attempt) folders in {output_dir}")

    # Initialize TensorBoard writer
    writer = SummaryWriter(tensorboard_dir)

    # Skip adding model graph to TensorBoard to avoid memory issues
    print("Skipping model graph visualization in TensorBoard to conserve memory")

    # Create augmentation visualizations
    print("Generating data augmentation visualizations...")

    # Create the directory structure
    os.makedirs(vis_augmentation_dir, exist_ok=True)
    print(f"Augmentation visualization directory created at {vis_augmentation_dir}")

    # Visualize augmentations for a few samples
    try:
        # Get a few samples from the training dataset
        train_loader = dataloaders['train']
        samples = []
        for i, batch in enumerate(train_loader):
            if i >= 2:  # Get 2 batches
                break
            samples.append(batch)

        # Create visualization grid
        for batch_idx, batch in enumerate(samples):
            if batch is None:
                continue

            # Get batch data
            images = batch['image']
            masks = batch['mask']
            heatmaps = batch['corner_heatmaps'] if 'corner_heatmaps' in batch else batch.get('heatmaps', None)

            # Skip if any data is missing
            if images is None or masks is None or heatmaps is None:
                continue

            # Create visualization for each sample in the batch
            for i in range(min(4, len(images))):  # Visualize up to 4 samples per batch
                # Get sample data
                image = images[i].cpu().numpy().transpose(1, 2, 0)
                mask = masks[i].cpu().numpy().squeeze()

                # Denormalize image
                mean = np.array([0.485, 0.456, 0.406])
                std = np.array([0.229, 0.224, 0.225])
                image = std * image + mean
                image = np.clip(image, 0, 1)

                # Create figure with subplots
                fig, axs = plt.subplots(2, 3, figsize=(15, 10))

                # Plot image
                axs[0, 0].imshow(image)
                axs[0, 0].set_title('Image')
                axs[0, 0].axis('off')

                # Plot mask
                axs[0, 1].imshow(mask, cmap='gray')
                axs[0, 1].set_title('Mask')
                axs[0, 1].axis('off')

                # Plot combined image and mask
                axs[0, 2].imshow(image)
                axs[0, 2].imshow(mask, alpha=0.5, cmap='jet')
                axs[0, 2].set_title('Image + Mask')
                axs[0, 2].axis('off')

                # Plot heatmaps
                if isinstance(heatmaps, torch.Tensor) and heatmaps.dim() >= 4:
                    for j in range(min(3, heatmaps.shape[1])):
                        heatmap = heatmaps[i, j].cpu().numpy()
                        axs[1, j].imshow(heatmap, cmap='jet')
                        axs[1, j].set_title(f'Heatmap {j+1}')
                        axs[1, j].axis('off')

                # Save figure
                plt.tight_layout()
                plt.savefig(os.path.join(vis_augmentation_dir, f'aug_batch{batch_idx}_sample{i}.png'))
                plt.close(fig)

        print(f"Generated augmentation visualizations in {vis_augmentation_dir}")
    except Exception as e:
        print(f"Error generating augmentation visualizations: {e}")
        # Continue with training even if visualization fails

    # Initialize best model and metrics
    best_model_wts = model.state_dict()
    best_loss = float('inf')
    best_p2s_ratio = 0.0
    best_detection_rate = 0.0
    best_combined_score = 0.0

    # Define weights for combined metric score
    metric_weights = {
        'val_loss': 0.3,           # Lower is better
        'peak_to_second_ratio': 0.5,  # Higher is better
        'detection_rate': 0.2       # Higher is better
    }

    # Initialize early stopping variables
    early_stopping_counter = 0
    early_stopping_best_score = -float('inf')
    early_stopping_flag = False

    # Initialize checkpoint pruning variables
    checkpoint_files = []

    # Initialize history
    history = {
        'train_loss': [],
        'val_loss': [],
        'train_seg_loss': [],
        'val_seg_loss': [],
        'train_heatmap_loss': [],
        'val_heatmap_loss': [],
        'train_geometric_loss': [],
        'val_geometric_loss': [],
        'train_heatmap_components': [],
        'val_heatmap_components': [],
        'train_corner_confidence': [],
        'val_corner_confidence': [],
        'learning_rates': [],
        'heatmap_weights': [],
        'geometric_weights': []
    }

    # Initialize hyperparameter tuning state
    tuning_state = {
        'heatmap_weight': heatmap_weight,
        'geometric_weight': geometric_weight,
        'best_weights': {
            'heatmap_weight': heatmap_weight,
            'geometric_weight': geometric_weight
        },
        'best_metrics': {
            'val_loss': float('inf'),
            'peak_to_second_ratio': 0.0,
            'detection_rate': 0.0
        },
        'exploration_directions': {
            'heatmap_weight': 0,  # 0: no change, 1: increase, -1: decrease
            'geometric_weight': 0
        },
        'step_sizes': {
            'heatmap_weight': 0.2,
            'geometric_weight': 0.1
        },
        'last_tuned_epoch': 0
    }

    # Define target metrics for balanced optimization
    target_metrics = {
        'peak_to_second_ratio': 1.8,
        'detection_rate': 1.0,
        'peak_to_mean_ratio': 10.0,
        'avg_peak_value': 2.0,
        'segmentation_loss': 0.15,
        'geometric_loss': 1.0,
        'mse_loss': 0.6
    }

    # Initialize loss component balancer
    loss_balancer = LossComponentBalancer(
        loss_components=['total_loss', 'segmentation_loss', 'heatmap_loss', 'geometric_loss'],
        target_ratios={
            'segmentation_loss/total_loss': 0.1,
            'heatmap_loss/total_loss': 0.6,
            'geometric_loss/total_loss': 0.3
        }
    )

    # Training loop
    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)

        # Update epoch for curriculum learning in model and loss function
        if hasattr(model, 'set_epoch'):
            model.set_epoch(epoch)
        if hasattr(criterion_heatmap, 'set_epoch'):
            criterion_heatmap.set_epoch(epoch)

        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode

            running_loss = 0.0
            running_seg_loss = 0.0
            running_heatmap_loss = 0.0
            running_geometric_loss = 0.0
            running_heatmap_components = {
                'mse_loss': 0.0,
                'separation_loss': 0.0,
                'peak_separation_loss': 0.0,
                'edge_suppression_loss': 0.0,
                'peak_enhancement_loss': 0.0,
                'peak_to_second_ratio_loss': 0.0,
                'avg_peak_to_second_ratio': 0.0,
                'detection_rate_loss': 0.0,
                'segmentation_guidance_loss': 0.0
            }
            running_corner_confidence = {
                'avg_peak_value': 0.0,
                'avg_peak_to_mean_ratio': 0.0,
                'avg_peak_to_second_ratio': 0.0,
                'detection_rate': 0.0,
                # New v5.2 detailed metrics
                'per_corner_p2s_ratio': [0.0, 0.0, 0.0, 0.0],  # Per-corner type P2S ratio
                'p2s_ratio_min': 0.0,                          # Minimum P2S ratio
                'p2s_ratio_max': 0.0,                          # Maximum P2S ratio
                'p2s_ratio_std': 0.0,                          # Standard deviation of P2S ratios
                'secondary_peak_distance': 0.0,                # Average distance between primary and secondary peaks
                'secondary_peak_relative_position': [0.0, 0.0], # Average x,y offset of secondary peak from primary
                'primary_peak_sharpness': 0.0,                 # Gradient magnitude around primary peak
                'primary_peak_isolation': 0.0                  # Distance to nearest significant activation
            }

            # Iterate over data
            for batch_idx, batch in enumerate(tqdm(dataloaders[phase])):
                # Skip None batches
                if batch is None:
                    print("Warning: Received None batch. Skipping.")
                    continue

                # Get inputs
                try:
                    inputs = batch['image'].to(DEVICE)
                    masks = batch['mask'].to(DEVICE)

                    # Get heatmaps (different datasets might have different keys)
                    heatmap_key = 'heatmaps' if 'heatmaps' in batch else 'corner_heatmaps'

                    # Ensure heatmaps is a tensor
                    if isinstance(batch[heatmap_key], list):
                        # Generate heatmaps from corners if available
                        if 'corners' in batch and isinstance(batch['corners'], torch.Tensor):
                            corners = batch['corners']
                            # Create heatmaps from corners
                            heatmaps = torch.zeros((inputs.shape[0], 4, inputs.shape[2], inputs.shape[3]), dtype=torch.float32)
                            for b in range(inputs.shape[0]):
                                for i in range(4):
                                    x, y = corners[b, i*2], corners[b, i*2+1]
                                    # Create Gaussian peak
                                    x_grid = torch.arange(0, inputs.shape[3], dtype=torch.float32)
                                    y_grid = torch.arange(0, inputs.shape[2], dtype=torch.float32).unsqueeze(1)
                                    sigma = 5.0
                                    heatmaps[b, i] = torch.exp(-((x_grid - x)**2 + (y_grid - y)**2) / (2 * sigma**2))
                            heatmaps = heatmaps.to(DEVICE)
                        else:
                            # Create empty heatmaps
                            heatmaps = torch.zeros((inputs.shape[0], 4, inputs.shape[2], inputs.shape[3]), dtype=torch.float32).to(DEVICE)
                    else:
                        heatmaps = batch[heatmap_key].to(DEVICE)

                    # Handle corners - we don't need to move them to device for training
                    # Just check if they exist and are valid
                    if 'corners' in batch:
                        if isinstance(batch['corners'], list):
                            # Process corners list into tensor
                            corners_list = batch['corners']
                            corners = torch.zeros((len(corners_list), 8), dtype=torch.float32)
                            for i, c in enumerate(corners_list):
                                if isinstance(c, torch.Tensor):
                                    corners[i, :c.numel()] = c.flatten()[:8]
                                elif isinstance(c, np.ndarray):
                                    corners[i, :c.size] = torch.from_numpy(c.flatten()[:8])
                            corners = corners.to(DEVICE)
                        elif isinstance(batch['corners'], torch.Tensor):
                            # It's a tensor, we can move it to device if needed for future use
                            corners = batch['corners'].to(DEVICE)
                except Exception as e:
                    print(f"Error processing batch: {e}")
                    continue

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    # Log memory before forward pass (only on first batch of each epoch)
                    if batch_idx == 0:
                        # Memory tracking removed
                        pass

                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    heatmap_outputs = outputs['corner_heatmaps']

                    # Log memory after forward pass (only on first batch of each epoch)
                    if batch_idx == 0:
                        # Memory tracking removed
                        pass

                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    # Pass segmentation to heatmap loss for segmentation-guided corner detection
                    heatmap_loss, heatmap_components = criterion_heatmap(heatmap_outputs, heatmaps, seg_outputs)

                    # Pass heatmap loss to stabilized geometric loss if supported
                    if hasattr(criterion_geometric, 'forward') and len(inspect.signature(criterion_geometric.forward).parameters) > 1:
                        geometric_loss = criterion_geometric(heatmap_outputs, heatmap_loss)
                    else:
                        geometric_loss = criterion_geometric(heatmap_outputs)

                    # Update heatmap and geometric weights if using loss balancer
                    if epoch > 5:  # Allow a few epochs for initial stabilization
                        loss_weights = loss_balancer.update({
                            'total_loss': seg_loss.item() + heatmap_loss.item() + geometric_loss.item(),
                            'segmentation_loss': seg_loss.item(),
                            'heatmap_loss': heatmap_loss.item(),
                            'geometric_loss': geometric_loss.item()
                        })
                        heatmap_weight = loss_weights['heatmap_loss']
                        geometric_weight = loss_weights['geometric_loss']

                    # Combined loss with custom weights
                    loss = seg_loss + heatmap_weight * heatmap_loss + geometric_weight * geometric_loss

                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        # Log memory before backward pass (only on first batch of each epoch)
                        if batch_idx == 0:
                            # Memory tracking removed
                            pass

                        loss.backward()

                        # Log memory after backward pass (only on first batch of each epoch)
                        if batch_idx == 0:
                            # Memory tracking removed
                            pass

                        # Apply gradient clipping
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()

                        # Log memory after optimizer step (only on first batch of each epoch)
                        if batch_idx == 0:
                            # Memory tracking removed
                            pass

                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_heatmap_loss += heatmap_loss.item() * inputs.size(0)
                running_geometric_loss += geometric_loss.item() * inputs.size(0)

                # Track heatmap loss components
                for key, value in heatmap_components.items():
                    running_heatmap_components[key] += value * inputs.size(0)

                # Calculate and track corner confidence metrics
                confidence_metrics = calculate_corner_confidence(heatmap_outputs)
                for key, value in confidence_metrics.items():
                    if isinstance(value, (int, float)):
                        running_corner_confidence[key] += value * inputs.size(0)
                    elif isinstance(value, list):
                        # For list values like per_corner_p2s_ratio, add element-wise
                        for i in range(len(value)):
                            running_corner_confidence[key][i] += value[i] * inputs.size(0)

            # Calculate epoch losses
            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_heatmap_loss = running_heatmap_loss / len(dataloaders[phase].dataset)
            epoch_geometric_loss = running_geometric_loss / len(dataloaders[phase].dataset)

            # Calculate average heatmap components
            epoch_heatmap_components = {
                key: value / len(dataloaders[phase].dataset)
                for key, value in running_heatmap_components.items()
            }

            # Calculate average corner confidence metrics
            epoch_corner_confidence = {}
            for key, value in running_corner_confidence.items():
                if isinstance(value, (int, float)):
                    epoch_corner_confidence[key] = value / len(dataloaders[phase].dataset)
                elif isinstance(value, list):
                    # For list values, normalize each element
                    epoch_corner_confidence[key] = [
                        element / len(dataloaders[phase].dataset) for element in value
                    ]

            # Print epoch losses
            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, '
                  f'Heatmap Loss: {epoch_heatmap_loss:.4f}, Geometric Loss: {epoch_geometric_loss:.4f}')

            # Print corner confidence metrics with more prominence
            print(f'=== {phase} Corner Confidence Metrics ===')

            # First print the main metrics with targets
            main_metrics = ['avg_peak_value', 'avg_peak_to_mean_ratio', 'avg_peak_to_second_ratio', 'detection_rate']
            print("Main Metrics:")
            for key in main_metrics:
                value = epoch_corner_confidence[key]
                # Add color indicators for metrics compared to targets
                if key in target_metrics:
                    target = target_metrics[key]
                    if value >= target:
                        status = "🟢"  # Green dot for meeting/exceeding target
                    elif value >= 0.9 * target:
                        status = "🟡"  # Yellow dot for close to target
                    else:
                        status = "🔴"  # Red dot for far from target

                    # Calculate percentage of target
                    pct = (value / target) * 100
                    print(f"  {key}: {value:.4f} {status} ({pct:.1f}% of target {target})")
                else:
                    print(f"  {key}: {value:.4f}")

            # Then print the detailed peak-to-second ratio metrics
            print("\nDetailed Peak-to-Second Ratio Metrics:")
            detailed_metrics = [
                'p2s_ratio_min', 'p2s_ratio_max', 'p2s_ratio_std',
                'secondary_peak_distance', 'primary_peak_sharpness', 'primary_peak_isolation'
            ]
            for key in detailed_metrics:
                if key in epoch_corner_confidence:
                    print(f"  {key}: {epoch_corner_confidence[key]:.4f}")

            # Print per-corner P2S ratios
            if 'per_corner_p2s_ratio' in epoch_corner_confidence:
                print("\nPer-Corner Peak-to-Second Ratios:")
                corner_names = ["Top-Left", "Top-Right", "Bottom-Right", "Bottom-Left"]
                for i, ratio in enumerate(epoch_corner_confidence['per_corner_p2s_ratio']):
                    print(f"  {corner_names[i]}: {ratio:.4f}")

            # Print secondary peak relative position if available
            if 'secondary_peak_relative_position' in epoch_corner_confidence:
                pos = epoch_corner_confidence['secondary_peak_relative_position']
                print(f"\nSecondary Peak Relative Position: x={pos[0]:.2f}, y={pos[1]:.2f}")

            # Print heatmap components
            print(f'{phase} Heatmap Components:')
            for key, value in epoch_heatmap_components.items():
                print(f'  {key}: {value:.4f}')

            # Save history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_heatmap_loss'].append(epoch_heatmap_loss)
            history[f'{phase}_geometric_loss'].append(epoch_geometric_loss)
            history[f'{phase}_heatmap_components'].append(epoch_heatmap_components)
            history[f'{phase}_corner_confidence'].append(epoch_corner_confidence)

            # Log metrics to TensorBoard
            step = epoch + 1
            writer.add_scalar(f'{phase}/total_loss', epoch_loss, step)
            writer.add_scalar(f'{phase}/segmentation_loss', epoch_seg_loss, step)
            writer.add_scalar(f'{phase}/heatmap_loss', epoch_heatmap_loss, step)
            writer.add_scalar(f'{phase}/geometric_loss', epoch_geometric_loss, step)

            # Log heatmap components
            for key, value in epoch_heatmap_components.items():
                writer.add_scalar(f'{phase}/heatmap_components/{key}', value, step)

            # Log corner confidence metrics
            for key, value in epoch_corner_confidence.items():
                if isinstance(value, (int, float)):  # Skip list values
                    writer.add_scalar(f'{phase}/corner_confidence/{key}', value, step)

            # Calculate and visualize confusion matrix for validation
            if phase == 'val':
                # Get a batch of validation data for confusion matrix
                val_iter = iter(dataloaders['val'])
                try:
                    batch = next(val_iter)
                    while batch is None:  # Skip None batches
                        batch = next(val_iter)

                    # Get inputs and targets
                    inputs = batch['image'].to(DEVICE)
                    heatmap_key = 'heatmaps' if 'heatmaps' in batch else 'corner_heatmaps'

                    # Handle the case where heatmaps might be a list
                    if isinstance(batch[heatmap_key], list):
                        # Generate heatmaps from corners if available
                        if 'corners' in batch and isinstance(batch['corners'], torch.Tensor):
                            corners = batch['corners']
                            # Forward pass to get output shape
                            with torch.no_grad():
                                outputs = model(inputs)
                                pred_heatmaps = outputs['corner_heatmaps']
                                # Create target heatmaps from corners
                                target_heatmaps = torch.zeros_like(pred_heatmaps)
                                for b in range(inputs.shape[0]):
                                    for i in range(4):
                                        x, y = corners[b, i*2], corners[b, i*2+1]
                                        # Create Gaussian peak
                                        x_grid = torch.arange(0, target_heatmaps.shape[3], dtype=torch.float32, device=DEVICE)
                                        y_grid = torch.arange(0, target_heatmaps.shape[2], dtype=torch.float32, device=DEVICE).unsqueeze(1)
                                        sigma = 5.0
                                        target_heatmaps[b, i] = torch.exp(-((x_grid - x)**2 + (y_grid - y)**2) / (2 * sigma**2))
                        else:
                            # Forward pass to get output shape
                            with torch.no_grad():
                                outputs = model(inputs)
                                pred_heatmaps = outputs['corner_heatmaps']
                                # Create empty target heatmaps with same shape
                                target_heatmaps = torch.zeros_like(pred_heatmaps)
                    else:
                        target_heatmaps = batch[heatmap_key].to(DEVICE)

                    # Forward pass
                    with torch.no_grad():
                        outputs = model(inputs)
                        pred_heatmaps = outputs['corner_heatmaps']

                    # Log memory before confusion matrix calculation
                    # Memory tracking removed
                    pass

                    # Calculate confusion matrix
                    conf_matrix, corner_accuracies, _ = calculate_corner_confusion_matrix(
                        pred_heatmaps, target_heatmaps, threshold=0.3
                    )

                    # Log memory after confusion matrix calculation
                    # Memory tracking removed
                    pass

                    # Visualize and save confusion matrix
                    if epoch % save_interval == 0 or epoch == num_epochs - 1:
                        conf_matrix_path = os.path.join(vis_confusion_dir, f'confusion_matrix_epoch_{epoch+1}_{phase_name}.png')
                        visualize_corner_confusion_matrix(
                            conf_matrix, corner_accuracies, save_path=conf_matrix_path
                        )

                    # Log corner accuracies to TensorBoard
                    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
                    for i, acc in enumerate(corner_accuracies):
                        writer.add_scalar(f'corner_accuracy/{corner_names[i]}', acc, step)

                    # Add confusion matrix to TensorBoard
                    try:
                        fig = plt.figure(figsize=(8, 8))
                        plt.imshow(conf_matrix, interpolation='nearest', cmap=plt.cm.Blues)
                        plt.title('Corner Type Confusion Matrix')
                        plt.colorbar()
                        tick_marks = np.arange(4)
                        plt.xticks(tick_marks, corner_names, rotation=45)
                        plt.yticks(tick_marks, corner_names)
                        plt.tight_layout()
                        writer.add_figure('confusion_matrix', fig, step)
                        plt.close(fig)
                    except Exception as e:
                        print(f"Warning: Could not add confusion matrix to TensorBoard: {e}")

                except (StopIteration, Exception) as e:
                    print(f"Warning: Could not calculate confusion matrix: {e}")

            # Save best model based on validation loss
            if phase == 'val':
                # Update learning rate scheduler with validation losses
                if scheduler is not None:
                    scheduler.step({
                        'total_loss': epoch_loss,
                        'segmentation_loss': epoch_seg_loss,
                        'heatmap_loss': epoch_heatmap_loss,
                        'geometric_loss': epoch_geometric_loss
                    })
                    # Record current learning rate
                    current_lr = optimizer.param_groups[0]['lr']
                    history['learning_rates'].append(current_lr)
                    writer.add_scalar('learning_rate', current_lr, step)
                    print(f'Current learning rate: {current_lr:.6f}')

                # Get current metrics
                p2s_ratio = epoch_corner_confidence['avg_peak_to_second_ratio']
                detection_rate = epoch_corner_confidence['detection_rate']

                # Save best model based on validation loss
                if epoch_loss < best_loss:
                    best_loss = epoch_loss
                    torch.save(model.state_dict().copy(), os.path.join(checkpoints_dir, f'best_model_loss_{phase_name}.pth'))
                    print(f'New best model saved with loss: {best_loss:.4f}')

                # Save best model based on peak-to-second ratio
                if p2s_ratio > best_p2s_ratio:
                    best_p2s_ratio = p2s_ratio
                    torch.save(model.state_dict().copy(), os.path.join(checkpoints_dir, f'best_model_p2s_{phase_name}.pth'))
                    print(f'New best model saved with peak-to-second ratio: {best_p2s_ratio:.4f}')

                # Save best model based on detection rate
                if detection_rate > best_detection_rate:
                    best_detection_rate = detection_rate
                    torch.save(model.state_dict().copy(), os.path.join(checkpoints_dir, f'best_model_detection_{phase_name}.pth'))
                    print(f'New best model saved with detection rate: {best_detection_rate:.4f}')

                # Calculate combined score using multiple metrics
                # Normalize metrics to 0-1 range
                normalized_loss = min(1.0, epoch_loss / (best_loss * 2)) if best_loss > 0 else 0.5
                normalized_p2s = min(1.0, p2s_ratio / target_metrics['peak_to_second_ratio'])
                normalized_detection = min(1.0, detection_rate / target_metrics['detection_rate'])

                # Invert loss so higher is better
                normalized_loss = 1.0 - normalized_loss

                # Calculate weighted score
                combined_score = (
                    metric_weights['val_loss'] * normalized_loss +
                    metric_weights['peak_to_second_ratio'] * normalized_p2s +
                    metric_weights['detection_rate'] * normalized_detection
                )

                # Save best model based on combined score
                if combined_score > best_combined_score:
                    best_combined_score = combined_score
                    best_model_wts = model.state_dict().copy()
                    torch.save(best_model_wts, os.path.join(checkpoints_dir, f'best_model_combined_{phase_name}.pth'))
                    print(f'New best model saved with combined score: {best_combined_score:.4f} '
                          f'(loss: {epoch_loss:.4f}, p2s: {p2s_ratio:.4f}, detection: {detection_rate:.4f})')

                # Automatic hyperparameter tuning
                if auto_tune_hyperparams and (epoch + 1) % tune_interval == 0:
                    print("\n=== Automatic Hyperparameter Tuning ===")

                    # Record current metrics for tuning
                    current_metrics = {
                        'val_loss': epoch_loss,
                        'peak_to_second_ratio': p2s_ratio,
                        'detection_rate': epoch_corner_confidence['detection_rate']
                    }

                    # Update best metrics if improved
                    metrics_improved = False
                    if current_metrics['val_loss'] < tuning_state['best_metrics']['val_loss']:
                        tuning_state['best_metrics']['val_loss'] = current_metrics['val_loss']
                        metrics_improved = True

                    if current_metrics['peak_to_second_ratio'] > tuning_state['best_metrics']['peak_to_second_ratio']:
                        tuning_state['best_metrics']['peak_to_second_ratio'] = current_metrics['peak_to_second_ratio']
                        metrics_improved = True

                    if current_metrics['detection_rate'] > tuning_state['best_metrics']['detection_rate']:
                        tuning_state['best_metrics']['detection_rate'] = current_metrics['detection_rate']
                        metrics_improved = True

                    # If metrics improved with current weights, update best weights
                    if metrics_improved:
                        tuning_state['best_weights']['heatmap_weight'] = heatmap_weight
                        tuning_state['best_weights']['geometric_weight'] = geometric_weight
                        print(f"New best weights: heatmap_weight={heatmap_weight:.2f}, geometric_weight={geometric_weight:.2f}")

                    # Determine exploration directions based on metrics
                    # Focus on improving peak-to-second ratio
                    if p2s_ratio < target_metrics['peak_to_second_ratio'] * 0.9:  # Less than 90% of target
                        # Increase heatmap weight to focus more on corner detection
                        tuning_state['exploration_directions']['heatmap_weight'] = 1
                        print(f"Increasing heatmap weight to improve peak-to-second ratio")
                    elif p2s_ratio > target_metrics['peak_to_second_ratio'] * 1.1:  # More than 110% of target
                        # Decrease heatmap weight to avoid overfitting on corners
                        tuning_state['exploration_directions']['heatmap_weight'] = -1
                        print(f"Decreasing heatmap weight to balance with other metrics")
                    else:
                        # Keep current heatmap weight
                        tuning_state['exploration_directions']['heatmap_weight'] = 0
                        print(f"Keeping current heatmap weight")

                    # Adjust geometric weight based on detection rate
                    if epoch_corner_confidence['detection_rate'] < 0.9:  # Less than 90% detection rate
                        # Decrease geometric weight to focus more on detection
                        tuning_state['exploration_directions']['geometric_weight'] = -1
                        print(f"Decreasing geometric weight to improve detection rate")
                    elif epoch_loss > best_loss * 1.1:  # Loss is 10% worse than best
                        # Increase geometric weight to improve overall loss
                        tuning_state['exploration_directions']['geometric_weight'] = 1
                        print(f"Increasing geometric weight to improve overall loss")
                    else:
                        # Keep current geometric weight
                        tuning_state['exploration_directions']['geometric_weight'] = 0
                        print(f"Keeping current geometric weight")

                    # Apply weight adjustments
                    old_heatmap_weight = heatmap_weight
                    old_geometric_weight = geometric_weight

                    # Update heatmap weight
                    heatmap_weight += tuning_state['exploration_directions']['heatmap_weight'] * tuning_state['step_sizes']['heatmap_weight']
                    heatmap_weight = max(0.5, min(3.0, heatmap_weight))  # Clamp to reasonable range

                    # Update geometric weight
                    geometric_weight += tuning_state['exploration_directions']['geometric_weight'] * tuning_state['step_sizes']['geometric_weight']
                    geometric_weight = max(0.1, min(1.5, geometric_weight))  # Clamp to reasonable range

                    # Update tuning state
                    tuning_state['heatmap_weight'] = heatmap_weight
                    tuning_state['geometric_weight'] = geometric_weight
                    tuning_state['last_tuned_epoch'] = epoch

                    # Log weight changes
                    print(f"Adjusted weights: heatmap_weight: {old_heatmap_weight:.2f} -> {heatmap_weight:.2f}, "
                          f"geometric_weight: {old_geometric_weight:.2f} -> {geometric_weight:.2f}")

                    # Add to TensorBoard
                    writer.add_scalar('hyperparams/heatmap_weight', heatmap_weight, epoch + 1)
                    writer.add_scalar('hyperparams/geometric_weight', geometric_weight, epoch + 1)

                    # Learning rate tuning based on metrics
                    print("\n=== Learning Rate Tuning ===")

                    # Get current learning rate
                    current_lr = optimizer.param_groups[0]['lr']

                    # Determine if we need to adjust learning rate
                    lr_adjustment_needed = False

                    # Check if we're stuck in a plateau
                    if len(history['val_loss']) >= 3:
                        recent_losses = history['val_loss'][-3:]
                        loss_change = abs(recent_losses[0] - recent_losses[-1]) / (recent_losses[0] + 1e-8)

                        if loss_change < 0.01:  # Less than 1% change in loss
                            print(f"Loss plateau detected (change: {loss_change:.4f})")
                            lr_adjustment_needed = True

                    # Check if peak-to-second ratio is improving too slowly
                    if len(history['val_corner_confidence']) >= 3:
                        recent_p2s = [conf['avg_peak_to_second_ratio'] for conf in history['val_corner_confidence'][-3:]]
                        p2s_change = (recent_p2s[-1] - recent_p2s[0]) / (recent_p2s[0] + 1e-8)

                        if p2s_change < 0.02 and p2s_ratio < target_metrics['peak_to_second_ratio'] * 0.9:
                            print(f"Slow peak-to-second ratio improvement (change: {p2s_change:.4f})")
                            lr_adjustment_needed = True

                    # Apply learning rate adjustment if needed
                    if lr_adjustment_needed:
                        # Determine adjustment factor based on current performance
                        if p2s_ratio < target_metrics['peak_to_second_ratio'] * 0.7:
                            # Far from target, try increasing learning rate
                            if early_stopping_counter < early_stopping_patience // 2:
                                adjustment_factor = 1.2  # Increase by 20%
                                print(f"Increasing learning rate to accelerate peak-to-second ratio improvement")
                            else:
                                # Getting close to early stopping, reduce learning rate
                                adjustment_factor = 0.5  # Reduce by 50%
                                print(f"Reducing learning rate to escape local minimum")
                        else:
                            # Close to target, reduce learning rate for fine-tuning
                            adjustment_factor = 0.7  # Reduce by 30%
                            print(f"Reducing learning rate for fine-tuning")

                        # Apply adjustment
                        new_lr = current_lr * adjustment_factor

                        # Ensure learning rate stays within reasonable bounds
                        min_lr = 1e-6
                        max_lr = 1e-2
                        new_lr = max(min_lr, min(max_lr, new_lr))

                        # Update optimizer learning rate
                        for param_group in optimizer.param_groups:
                            param_group['lr'] = new_lr

                        print(f"Adjusted learning rate: {current_lr:.6f} -> {new_lr:.6f}")

                        # Add to TensorBoard
                        writer.add_scalar('learning_rate/tuned', new_lr, epoch + 1)

                # Record current weights in history
                history['heatmap_weights'].append(heatmap_weight)
                history['geometric_weights'].append(geometric_weight)

                # Early stopping based on combined score
                # Calculate early stopping score (higher is better)
                early_stopping_score = combined_score

                if early_stopping_score > early_stopping_best_score:
                    early_stopping_best_score = early_stopping_score
                    early_stopping_counter = 0
                    print(f"Early stopping: New best score: {early_stopping_best_score:.4f}")
                else:
                    early_stopping_counter += 1
                    print(f"Early stopping: No improvement for {early_stopping_counter}/{early_stopping_patience} epochs. "
                          f"Best: {early_stopping_best_score:.4f}, Current: {early_stopping_score:.4f}")

                    if early_stopping_counter >= early_stopping_patience:
                        early_stopping_flag = True
                        print(f"Early stopping triggered after {epoch+1} epochs!")
                        # Don't break here, let the loop finish the current epoch

        # Save checkpoint at specified intervals
        if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
            # Create checkpoint filename
            checkpoint_filename = os.path.join(checkpoints_dir, f'checkpoint_epoch_{epoch+1}_{phase_name}.pth')

            # Save checkpoint
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'loss': epoch_loss,
                'history': history
            }, checkpoint_filename)

            # Add to checkpoint files list
            checkpoint_files.append(checkpoint_filename)

            # Prune checkpoints if we have too many
            if len(checkpoint_files) > max_checkpoints_to_keep:
                # Sort checkpoints by creation time (oldest first)
                checkpoint_files.sort(key=lambda x: os.path.getctime(x))

                # Get list of checkpoints to remove (keep the newest ones)
                checkpoints_to_remove = checkpoint_files[:-max_checkpoints_to_keep]

                # Remove old checkpoints
                for old_checkpoint in checkpoints_to_remove:
                    try:
                        # Don't remove best model checkpoints
                        if ('best_model' not in old_checkpoint and
                            'ensemble' not in old_checkpoint):
                            os.remove(old_checkpoint)
                            print(f"Pruned old checkpoint: {os.path.basename(old_checkpoint)}")
                    except Exception as e:
                        print(f"Warning: Could not remove old checkpoint {old_checkpoint}: {e}")

                # Update checkpoint files list
                checkpoint_files = checkpoint_files[-max_checkpoints_to_keep:]

            # Save history to JSON
            with open(os.path.join(logs_dir, f'history_epoch_{epoch+1}_{phase_name}.json'), 'w') as f:
                # Convert numpy values to Python types for JSON serialization
                json_history = {}
                for key, value in history.items():
                    if isinstance(value[0], dict):
                        json_history[key] = [{k: (v if isinstance(v, list) else float(v)) for k, v in d.items()} for d in value]
                    else:
                        json_history[key] = [float(v) for v in value]

                json.dump(json_history, f, indent=4)

            # Skip memory-intensive visualizations
            print("Skipping heatmap and keypoint visualizations to conserve memory...")

            # Just log that we're skipping this step
            print(f"Visualization skipped for epoch {epoch+1} to prevent memory issues")

            # Record metrics instead of visualizing
            model.eval()
            with torch.no_grad():
                try:
                    # Get a batch of validation data
                    val_iter = iter(dataloaders['val'])
                    batch = next(val_iter)
                    while batch is None:  # Skip None batches
                        batch = next(val_iter)

                    # Get inputs
                    inputs = batch['image'].to(DEVICE)

                    # Forward pass
                    outputs = model(inputs)

                    # Log metrics instead of visualizing
                    print(f"Model evaluated at epoch {epoch+1}")
                    print(f"Output shapes: segmentation={outputs['segmentation'].shape}, heatmaps={outputs['corner_heatmaps'].shape}")

                except StopIteration:
                    print("Warning: Could not evaluate model (no validation data available)")
                except Exception as e:
                    print(f"Warning: Error evaluating model: {e}")

            # Plot losses
            plt.figure(figsize=(15, 5))

            # Plot total loss
            plt.subplot(1, 3, 1)
            plt.plot(history['train_loss'], label='Train')
            plt.plot(history['val_loss'], label='Validation')
            plt.title('Total Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot segmentation loss
            plt.subplot(1, 3, 2)
            plt.plot(history['train_seg_loss'], label='Train')
            plt.plot(history['val_seg_loss'], label='Validation')
            plt.title('Segmentation Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            # Plot heatmap loss
            plt.subplot(1, 3, 3)
            plt.plot(history['train_heatmap_loss'], label='Train')
            plt.plot(history['val_heatmap_loss'], label='Validation')
            plt.title('Heatmap Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()

            plt.tight_layout()
            plt.savefig(os.path.join(vis_losses_dir, f'losses_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot main corner confidence metrics
            plt.figure(figsize=(15, 10))
            plt.suptitle('Main Confidence Metrics', fontsize=16)

            main_metrics = ['avg_peak_value', 'avg_peak_to_mean_ratio', 'avg_peak_to_second_ratio', 'detection_rate']
            for i, key in enumerate(main_metrics):
                plt.subplot(2, 2, i+1)
                plt.plot(
                    [conf[key] for conf in history['train_corner_confidence']],
                    label='Train'
                )
                plt.plot(
                    [conf[key] for conf in history['val_corner_confidence']],
                    label='Validation'
                )
                # Add target line if available
                if key in target_metrics:
                    plt.axhline(y=target_metrics[key], color='r', linestyle='--',
                               label=f'Target: {target_metrics[key]}')
                plt.title(key)
                plt.xlabel('Epoch')
                plt.ylabel('Value')
                plt.legend()

            plt.tight_layout(rect=[0, 0, 1, 0.95])  # Adjust for suptitle
            plt.savefig(os.path.join(vis_metrics_dir, f'main_metrics_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot detailed peak-to-second ratio metrics
            plt.figure(figsize=(15, 12))
            plt.suptitle('Detailed Peak-to-Second Ratio Metrics', fontsize=16)

            detailed_metrics = [
                'p2s_ratio_min', 'p2s_ratio_max', 'p2s_ratio_std',
                'secondary_peak_distance', 'primary_peak_sharpness', 'primary_peak_isolation'
            ]

            for i, key in enumerate(detailed_metrics):
                plt.subplot(3, 2, i+1)
                # Check if the metric exists in the history
                if key in history['train_corner_confidence'][0]:
                    plt.plot(
                        [conf[key] for conf in history['train_corner_confidence']],
                        label='Train'
                    )
                    plt.plot(
                        [conf[key] for conf in history['val_corner_confidence']],
                        label='Validation'
                    )
                    plt.title(key)
                    plt.xlabel('Epoch')
                    plt.ylabel('Value')
                    plt.legend()

            plt.tight_layout(rect=[0, 0, 1, 0.95])  # Adjust for suptitle
            plt.savefig(os.path.join(vis_p2s_dir, f'detailed_p2s_metrics_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot per-corner peak-to-second ratios
            plt.figure(figsize=(10, 8))
            plt.suptitle('Per-Corner Peak-to-Second Ratios', fontsize=16)

            if 'per_corner_p2s_ratio' in history['train_corner_confidence'][0]:
                corner_names = ["Top-Left", "Top-Right", "Bottom-Right", "Bottom-Left"]
                for i in range(4):
                    plt.subplot(2, 2, i+1)
                    plt.plot(
                        [conf['per_corner_p2s_ratio'][i] for conf in history['train_corner_confidence']],
                        label='Train'
                    )
                    plt.plot(
                        [conf['per_corner_p2s_ratio'][i] for conf in history['val_corner_confidence']],
                        label='Validation'
                    )
                    # Add target line
                    if 'peak_to_second_ratio' in target_metrics:
                        plt.axhline(y=target_metrics['peak_to_second_ratio'], color='r', linestyle='--',
                                   label=f'Target: {target_metrics["peak_to_second_ratio"]}')
                    plt.title(f'{corner_names[i]} Corner')
                    plt.xlabel('Epoch')
                    plt.ylabel('P2S Ratio')
                    plt.legend()

            plt.tight_layout(rect=[0, 0, 1, 0.95])  # Adjust for suptitle
            plt.savefig(os.path.join(vis_per_corner_dir, f'per_corner_p2s_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

            # Plot learning rate
            plt.figure(figsize=(8, 6))
            if scheduler is not None:
                plt.plot(history['learning_rates'])
                plt.title('Learning Rate')
                plt.xlabel('Epoch')
                plt.ylabel('Learning Rate')
                plt.savefig(os.path.join(vis_metrics_dir, f'learning_rate_epoch_{epoch+1}_{phase_name}.png'))
                plt.close()

            # Plot hyperparameter weights
            plt.figure(figsize=(10, 6))
            plt.plot(history['heatmap_weights'], label='Heatmap Weight')
            plt.plot(history['geometric_weights'], label='Geometric Weight')
            plt.title('Hyperparameter Weights')
            plt.xlabel('Epoch')
            plt.ylabel('Weight Value')
            plt.legend()
            plt.grid(True)
            plt.savefig(os.path.join(vis_metrics_dir, f'hyperparameter_weights_epoch_{epoch+1}_{phase_name}.png'))
            plt.close()

        print()

        # Check if early stopping is triggered
        if early_stopping_flag:
            print(f"\n=== Early Stopping Triggered! ===")
            print(f"Training stopped after {epoch+1} epochs due to no improvement in combined score.")
            print(f"Best combined score: {early_stopping_best_score:.4f}")
            break

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')
    print(f'Best peak-to-second ratio: {best_p2s_ratio:.4f}')

    # Skip adding final model visualizations to TensorBoard
    print("Skipping final model visualization in TensorBoard to conserve memory")

    # Close TensorBoard writer
    writer.close()

    # Load best model weights
    model.load_state_dict(best_model_wts)

    # Create checkpoint ensemble
    if num_epochs >= 10:  # Only create ensemble if we have enough epochs
        print("\n=== Creating Checkpoint Ensemble ===")
        try:
            # Load different checkpoint models
            ensemble_models = []

            # Add best model based on combined score
            combined_model_path = os.path.join(checkpoints_dir, f'best_model_combined_{phase_name}.pth')
            if os.path.exists(combined_model_path):
                combined_model = EnhancedChessBoardUNetV5_2(
                    n_channels=3,
                    dropout_rate=0.3,
                    use_batch_norm=True
                ).to(DEVICE)
                combined_model.load_state_dict(torch.load(combined_model_path, map_location=DEVICE))
                combined_model.eval()
                ensemble_models.append(('combined', combined_model))
                print(f"Added best combined model to ensemble")

            # Add best model based on peak-to-second ratio
            p2s_model_path = os.path.join(checkpoints_dir, f'best_model_p2s_{phase_name}.pth')
            if os.path.exists(p2s_model_path) and p2s_model_path != combined_model_path:
                p2s_model = EnhancedChessBoardUNetV5_2(
                    n_channels=3,
                    dropout_rate=0.3,
                    use_batch_norm=True
                ).to(DEVICE)
                p2s_model.load_state_dict(torch.load(p2s_model_path, map_location=DEVICE))
                p2s_model.eval()
                ensemble_models.append(('p2s', p2s_model))
                print(f"Added best peak-to-second ratio model to ensemble")

            # Add best model based on detection rate
            detection_model_path = os.path.join(checkpoints_dir, f'best_model_detection_{phase_name}.pth')
            if os.path.exists(detection_model_path) and detection_model_path != combined_model_path and detection_model_path != p2s_model_path:
                detection_model = EnhancedChessBoardUNetV5_2(
                    n_channels=3,
                    dropout_rate=0.3,
                    use_batch_norm=True
                ).to(DEVICE)
                detection_model.load_state_dict(torch.load(detection_model_path, map_location=DEVICE))
                detection_model.eval()
                ensemble_models.append(('detection', detection_model))
                print(f"Added best detection rate model to ensemble")

            # Add best model based on loss
            loss_model_path = os.path.join(checkpoints_dir, f'best_model_loss_{phase_name}.pth')
            if os.path.exists(loss_model_path) and loss_model_path != combined_model_path and loss_model_path != p2s_model_path and loss_model_path != detection_model_path:
                loss_model = EnhancedChessBoardUNetV5_2(
                    n_channels=3,
                    dropout_rate=0.3,
                    use_batch_norm=True
                ).to(DEVICE)
                loss_model.load_state_dict(torch.load(loss_model_path, map_location=DEVICE))
                loss_model.eval()
                ensemble_models.append(('loss', loss_model))
                print(f"Added best loss model to ensemble")

            # Add final model
            ensemble_models.append(('final', model))
            print(f"Added final model to ensemble")

            # If we have at least 2 models in the ensemble
            if len(ensemble_models) >= 2:
                # Create ensemble prediction function
                def ensemble_predict(x):
                    with torch.no_grad():
                        # Get predictions from all models
                        all_segmentations = []
                        all_heatmaps = []

                        for name, model in ensemble_models:
                            outputs = model(x)
                            all_segmentations.append(outputs['segmentation'])
                            all_heatmaps.append(outputs['corner_heatmaps'])

                        # Average predictions
                        avg_segmentation = torch.mean(torch.stack(all_segmentations), dim=0)
                        avg_heatmaps = torch.mean(torch.stack(all_heatmaps), dim=0)

                        return {
                            'segmentation': avg_segmentation,
                            'corner_heatmaps': avg_heatmaps
                        }

                # Skip memory-intensive ensemble visualization
                print("Skipping ensemble visualization to conserve memory...")

                # Create a validation batch for testing the ensemble
                val_iter = iter(dataloaders['val'])
                batch = next(val_iter)
                while batch is None:  # Skip None batches
                    batch = next(val_iter)

                # Check if batch has valid image data
                if isinstance(batch, dict) and 'image' in batch and isinstance(batch['image'], torch.Tensor):
                    inputs = batch['image'].to(DEVICE)

                    # Get ensemble predictions
                    ensemble_outputs = ensemble_predict(inputs)

                    # Log ensemble output shapes instead of visualizing
                    print(f"Ensemble model evaluated")
                    print(f"Ensemble output shapes: segmentation={ensemble_outputs['segmentation'].shape}, heatmaps={ensemble_outputs['corner_heatmaps'].shape}")

                    # Create directory for ensemble outputs
                    os.makedirs(os.path.join(vis_dir, 'ensemble'), exist_ok=True)

                else:
                    print("Warning: Invalid batch format for ensemble evaluation")
                    raise Exception("Invalid batch format")

                # Save ensemble model
                ensemble_model_path = os.path.join(checkpoints_dir, f'ensemble_model_{phase_name}.pth')
                torch.save({
                    'model_paths': [model_path for _, model_path in zip(['combined', 'p2s', 'detection', 'loss', 'final'],
                                                                       [combined_model_path, p2s_model_path, detection_model_path, loss_model_path, 'final'])],
                    'model_weights': [1.0, 1.0, 1.0, 1.0, 1.0]  # Equal weights for now
                }, ensemble_model_path)

                print(f"Ensemble model saved to {ensemble_model_path}")
                print(f"Ensemble visualization saved to {ensemble_vis_path}")
        except Exception as e:
            print(f"Warning: Could not create checkpoint ensemble: {e}")

    return model, history


# Memory tracking functions removed

def main():
    """
    Main function to train the v5.2 model with balanced improvement strategy.
    """
    # Set memory limits to prevent crashes
    import gc
    import os
    import psutil

    # Set memory limits (3GB max)
    max_memory_gb = 3

    # Enable garbage collection
    gc.enable()

    # Set PyTorch memory management
    torch.cuda.empty_cache()

    # Set environment variables to limit memory usage
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = f'max_split_size_mb:{max_memory_gb*1024//2}'

    # Log initial memory usage
    # Memory tracking removed

    # Parse arguments
    parser = argparse.ArgumentParser(description='Train v5.2 chess board detection model with balanced improvement strategy')
    parser.add_argument('--data_dir', type=str, default=os.path.join(DATA_DIR, 'real'),
                        help='Data directory for regular training')
    parser.add_argument('--annotation_file', type=str, default=os.path.join(DATA_DIR, 'real_annotations.json'),
                        help='Annotation file for regular training')
    parser.add_argument('--augmented_data_dir', type=str,
                        default=r'C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326',
                        help='Directory containing pre-augmented data')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--epochs_phase1', type=int, default=20, help='Number of epochs for phase 1')
    parser.add_argument('--epochs_phase2', type=int, default=40, help='Number of epochs for phase 2')
    parser.add_argument('--epochs_phase3', type=int, default=20, help='Number of epochs for phase 3')
    parser.add_argument('--lr_phase1', type=float, default=0.0008, help='Learning rate for phase 1')
    parser.add_argument('--lr_phase2', type=float, default=0.0005, help='Learning rate for phase 2')
    parser.add_argument('--lr_phase3', type=float, default=0.0002, help='Learning rate for phase 3')
    parser.add_argument('--dropout_rate', type=float, default=0.3, help='Dropout rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--batch_size', type=int, default=4, help='Batch size')
    parser.add_argument('--save_interval', type=int, default=5, help='Interval to save model checkpoints')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--continue_from_v5_1', action='store_true', help='Continue training from v5.1 checkpoint')
    parser.add_argument('--continue_from_phase1', action='store_true', help='Continue training from phase 1 checkpoint')
    parser.add_argument('--continue_from_phase2', action='store_true', help='Continue training from phase 2 checkpoint')
    parser.add_argument('--skip_phase1', action='store_true', help='Skip phase 1 and go directly to phase 2')
    parser.add_argument('--skip_phase2', action='store_true', help='Skip phase 2 and go directly to phase 3')
    parser.add_argument('--fold_idx', type=int, default=0, help='Index of the fold to use as validation')
    parser.add_argument('--n_folds', type=int, default=5, help='Number of folds for cross-validation')
    parser.add_argument('--max_memory_gb', type=float, default=max_memory_gb, help='Maximum memory usage in GB')
    parser.add_argument('--val_split', type=float, default=0.2, help='Validation split ratio for pre-augmented data')
    parser.add_argument('--heatmap_weight', type=float, default=1.5, help='Weight for heatmap loss')
    parser.add_argument('--geometric_weight', type=float, default=0.4, help='Weight for geometric loss')
    parser.add_argument('--edge_suppression_weight', type=float, default=0.7, help='Weight for edge suppression loss')
    parser.add_argument('--peak_enhancement_weight', type=float, default=0.5, help='Weight for peak enhancement loss')
    parser.add_argument('--separation_weight', type=float, default=0.6, help='Weight for separation loss')
    parser.add_argument('--peak_separation_weight', type=float, default=0.5, help='Weight for peak separation loss')

    # Advanced optimization parameters
    parser.add_argument('--use_bayesian_optimization', action='store_true', help='Use Bayesian hyperparameter optimization')
    parser.add_argument('--use_ensemble', action='store_true', help='Use ensemble model for inference')
    parser.add_argument('--optimization_trials', type=int, default=10, help='Number of optimization trials')
    parser.add_argument('--ensemble_checkpoints', type=int, default=5, help='Number of checkpoints to use in ensemble')
    args = parser.parse_args()

    # Print memory limits
    print(f"Memory limits: {args.max_memory_gb}GB maximum usage")

    # Initialize hyperparameter optimizer if requested
    if args.use_bayesian_optimization:
        print("Initializing Bayesian hyperparameter optimizer...")

        # Define parameter space for optimization
        param_space = {
            'heatmap_weight': (0.5, 2.0),
            'geometric_weight': (0.1, 1.0),
            'edge_suppression_weight': (0.3, 1.0),
            'peak_enhancement_weight': (0.2, 1.0),
            'separation_weight': (0.3, 1.0),
            'peak_separation_weight': (0.2, 1.0),
            'learning_rate': (0.0001, 0.001),
            'weight_decay': (1e-5, 1e-3)
        }

        # Define objective metrics
        objective_metrics = [
            'peak_to_second_ratio',
            'detection_rate',
            'segmentation_iou',
            'geometric_consistency'
        ]

        # Create optimizer
        history_path = os.path.join(args.output_dir, 'hyperparameter_history.json')
        hyperparameter_optimizer = BayesianHyperparameterOptimizer(
            param_space=param_space,
            objective_metrics=objective_metrics,
            history_path=history_path,
            exploration_weight=0.2,
            n_initial_points=3,
            meta_learning=True
        )

        # If we have previous optimization results, use them
        if os.path.exists(history_path):
            print(f"Loaded hyperparameter optimization history from {history_path}")
            best_params = hyperparameter_optimizer.get_best_params()
            print(f"Best parameters from previous runs: {best_params}")

            # Update args with best parameters
            for param, value in best_params.items():
                if hasattr(args, param):
                    setattr(args, param, value)
                    print(f"Updated {param} to {value}")
    else:
        hyperparameter_optimizer = None

    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")

    # Create dataloaders
    # Memory tracking removed

    if args.augmented_data_dir:
        # Use pre-augmented dataset
        print(f"Using pre-augmented dataset from: {args.augmented_data_dir}")
        train_loader, val_loader = create_augmented_dataloaders(
            augmented_data_dir=args.augmented_data_dir,
            batch_size=args.batch_size,
            val_split=args.val_split,
            filter_valid=True,
            shuffle=True
        )
    else:
        # Use regular dataset with on-the-fly augmentation
        print("Creating expanded validation set with enhanced augmentations...")
        train_dataset, val_dataset = create_expanded_validation_set(
            data_dir=args.data_dir,
            annotation_file=args.annotation_file,
            fold_idx=args.fold_idx,
            n_folds=args.n_folds
        )

        # Create data loaders with custom collate function
        train_loader = DataLoader(
            train_dataset,
            batch_size=args.batch_size,
            shuffle=True,
            num_workers=0,  # Use 0 workers to avoid multiprocessing issues
            collate_fn=custom_collate_fn
        )

        val_loader = DataLoader(
            val_dataset,
            batch_size=args.batch_size,
            shuffle=False,
            num_workers=0,  # Use 0 workers to avoid multiprocessing issues
            collate_fn=custom_collate_fn
        )

    # Memory tracking removed

    dataloaders = {
        'train': train_loader,
        'val': val_loader
    }

    # Print dataset sizes
    print(f"Train dataset size: {len(train_loader.dataset)}")
    print(f"Validation dataset size: {len(val_loader.dataset)}")

    # Initialize v5.2 model
    print("Initializing v5.2 model...")
    # Memory tracking removed
    model = EnhancedChessBoardUNetV5_2(
        n_channels=3,
        dropout_rate=args.dropout_rate,
        use_batch_norm=True
    )
    # Memory tracking removed

    # Load from checkpoint if requested
    if args.continue_from_v5_1:
        v5_1_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v5.1')
        best_model_path = os.path.join(v5_1_checkpoints_dir, 'best_model_phase2.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v5.1 best model: {best_model_path}")
            # Create a temporary model to load the state dict
            from models.improved_enhanced_unet_v5 import ImprovedEnhancedChessBoardUNetV5
            temp_model = ImprovedEnhancedChessBoardUNetV5(n_channels=3, dropout_rate=args.dropout_rate)
            temp_model.load_state_dict(torch.load(best_model_path, map_location=device))

            # Copy the base model weights
            model.base_model.load_state_dict(temp_model.state_dict())
            print("Loaded v5.1 best model into base model")
        else:
            print(f"Warning: v5.1 best model not found at {best_model_path}")

    # Continue from phase 1 if requested
    elif args.continue_from_phase1:
        v5_2_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v5.2')
        best_model_path = os.path.join(v5_2_checkpoints_dir, 'best_model_p2s_phase1.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v5.2 phase 1 best model: {best_model_path}")
            model.load_state_dict(torch.load(best_model_path, map_location=device))
            print("Loaded v5.2 phase 1 best model")
        else:
            print(f"Warning: v5.2 phase 1 best model not found at {best_model_path}")

    # Continue from phase 2 if requested
    elif args.continue_from_phase2:
        v5_2_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v5.2')
        best_model_path = os.path.join(v5_2_checkpoints_dir, 'best_model_p2s_phase2.pth')

        if os.path.exists(best_model_path):
            print(f"Loading v5.2 phase 2 best model: {best_model_path}")
            model.load_state_dict(torch.load(best_model_path, map_location=device))
            print("Loaded v5.2 phase 2 best model")
        else:
            print(f"Warning: v5.2 phase 2 best model not found at {best_model_path}")

    model = model.to(device)
    print(f"Model moved to {device}")

    # Define loss functions
    criterion_seg = EnhancedDiceLoss()
    criterion_geometric = StabilizedGeometricLoss(base_weight=0.8, max_weight=2.0)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Define target metrics for balanced optimization
    target_metrics = {
        'peak_to_second_ratio': 1.8,
        'detection_rate': 1.0,
        'peak_to_mean_ratio': 10.0,
        'avg_peak_value': 2.0,
        'segmentation_loss': 0.15,
        'geometric_loss': 1.0,
        'mse_loss': 0.6
    }

    # Phase 1: Balanced foundation with smooth convergence path
    if not args.skip_phase1 and not args.continue_from_phase1 and not args.continue_from_phase2:
        print("\n=== Phase 1: Balanced foundation with smooth convergence path ===")

        # Phase 1 configuration
        criterion_heatmap_phase1 = EnhancedCornerFocusedHeatmapLossV5_2(
            separation_weight=args.separation_weight,
            peak_separation_weight=args.peak_separation_weight,
            edge_suppression_weight=args.edge_suppression_weight,
            peak_enhancement_weight=args.peak_enhancement_weight,
            peak_to_second_ratio_weight=10.0,
            detection_rate_weight=5.0,
            segmentation_guidance_weight=1.5
        )

        # Initialize weight manager with target metrics
        criterion_heatmap_phase1.initialize_weight_manager(target_metrics)

        # Create base optimizer
        base_optimizer_phase1 = optim.AdamW(
            model.parameters(),
            lr=args.lr_phase1,
            weight_decay=args.weight_decay,
            amsgrad=True
        )

        # Wrap with metric-specific optimizer
        optimizer_phase1 = MetricSpecificLROptimizer(
            model=model,
            base_optimizer=base_optimizer_phase1,
            base_lr=args.lr_phase1
        )

        # Initialize multi-loss convergence scheduler
        scheduler_phase1 = MultiLossConvergenceScheduler(
            optimizer=optimizer_phase1,
            total_epochs=args.epochs_phase1,
            initial_lr=args.lr_phase1,
            min_lr=args.lr_phase1 * 0.1,
            warmup_epochs=5,
            target_losses={
                'total_loss': 100.0,
                'segmentation_loss': 0.15,
                'heatmap_loss': 50.0,
                'geometric_loss': 1.0
            },
            smoothing_factor=0.8,
            convergence_shape='exponential'
        )

        # Train phase 1
        # Memory tracking removed
        model, history_phase1 = train_model_balanced(
            model=model,
            dataloaders=dataloaders,
            criterion_seg=criterion_seg,
            criterion_heatmap=criterion_heatmap_phase1,
            criterion_geometric=criterion_geometric,
            optimizer=optimizer_phase1,
            scheduler=scheduler_phase1,
            output_dir=args.output_dir,
            num_epochs=args.epochs_phase1,
            heatmap_weight=args.heatmap_weight,
            geometric_weight=args.geometric_weight,
            save_interval=args.save_interval,
            phase_name='phase1',
            auto_tune_hyperparams=True,
            tune_interval=5,
            early_stopping_patience=15,
            max_checkpoints_to_keep=5
        )
        # Memory tracking removed

        # Save history to JSON for analysis
        history_path = os.path.join(args.output_dir, 'logs', 'v5.2(3rd attempt)', f'history_final_phase1.json')
        with open(history_path, 'w') as f:
            # Convert numpy values to Python types for JSON serialization
            json_history = {}
            for key, value in history_phase1.items():
                if isinstance(value[0], dict):
                    json_history[key] = [{k: (v if isinstance(v, list) else float(v)) for k, v in d.items()} for d in value]
                else:
                    json_history[key] = [float(v) for v in value]
            json.dump(json_history, f, indent=4)

        # Generate analysis report
        print("\n=== Generating Phase 1 Analysis Report ===")
        try:
            report_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generate_analysis_report.py')
            subprocess.run([
                sys.executable,
                report_script,
                '--history_path', history_path,
                '--output_dir', args.output_dir,
                '--phase_name', 'phase1'
            ], check=True)
            print("Phase 1 analysis report generated successfully")
        except Exception as e:
            print(f"Warning: Could not generate analysis report: {e}")

    # Phase 2: Proportional improvement with targeted convergence
    if not args.skip_phase2 and not args.continue_from_phase2:
        print("\n=== Phase 2: Proportional improvement with targeted convergence ===")

        # Phase 2 configuration
        criterion_heatmap_phase2 = EnhancedCornerFocusedHeatmapLossV5_2(
            separation_weight=args.separation_weight,
            peak_separation_weight=args.peak_separation_weight,
            edge_suppression_weight=args.edge_suppression_weight,
            peak_enhancement_weight=args.peak_enhancement_weight,
            peak_to_second_ratio_weight=12.0,  # Increased focus on peak-to-second ratio
            detection_rate_weight=4.0,
            segmentation_guidance_weight=1.2
        )

        # Initialize weight manager with target metrics
        criterion_heatmap_phase2.initialize_weight_manager(target_metrics)

        # Create base optimizer
        base_optimizer_phase2 = optim.AdamW(
            model.parameters(),
            lr=args.lr_phase2,
            weight_decay=args.weight_decay,
            amsgrad=True
        )

        # Wrap with metric-specific optimizer
        optimizer_phase2 = MetricSpecificLROptimizer(
            model=model,
            base_optimizer=base_optimizer_phase2,
            base_lr=args.lr_phase2
        )

        # Initialize multi-loss convergence scheduler
        scheduler_phase2 = MultiLossConvergenceScheduler(
            optimizer=optimizer_phase2,
            total_epochs=args.epochs_phase2,
            initial_lr=args.lr_phase2,
            min_lr=args.lr_phase2 * 0.1,
            warmup_epochs=3,
            target_losses={
                'total_loss': 50.0,
                'segmentation_loss': 0.1,
                'heatmap_loss': 25.0,
                'geometric_loss': 0.5
            },
            smoothing_factor=0.8,
            convergence_shape='sigmoid'
        )

        # Train phase 2
        model, history_phase2 = train_model_balanced(
            model=model,
            dataloaders=dataloaders,
            criterion_seg=criterion_seg,
            criterion_heatmap=criterion_heatmap_phase2,
            criterion_geometric=criterion_geometric,
            optimizer=optimizer_phase2,
            scheduler=scheduler_phase2,
            output_dir=args.output_dir,
            num_epochs=args.epochs_phase2,
            heatmap_weight=args.heatmap_weight,
            geometric_weight=args.geometric_weight,
            save_interval=args.save_interval,
            phase_name='phase2',
            auto_tune_hyperparams=True,
            tune_interval=5,
            early_stopping_patience=15,
            max_checkpoints_to_keep=5
        )

        # Save history to JSON for analysis
        history_path = os.path.join(args.output_dir, 'logs', 'v5.2(3rd attempt)', f'history_final_phase2.json')
        with open(history_path, 'w') as f:
            # Convert numpy values to Python types for JSON serialization
            json_history = {}
            for key, value in history_phase2.items():
                if isinstance(value[0], dict):
                    json_history[key] = [{k: (v if isinstance(v, list) else float(v)) for k, v in d.items()} for d in value]
                else:
                    json_history[key] = [float(v) for v in value]
            json.dump(json_history, f, indent=4)

        # Generate analysis report
        print("\n=== Generating Phase 2 Analysis Report ===")
        try:
            report_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generate_analysis_report.py')
            subprocess.run([
                sys.executable,
                report_script,
                '--history_path', history_path,
                '--output_dir', args.output_dir,
                '--phase_name', 'phase2'
            ], check=True)
            print("Phase 2 analysis report generated successfully")
        except Exception as e:
            print(f"Warning: Could not generate analysis report: {e}")

    # Phase 3: Final convergence and harmonization
    print("\n=== Phase 3: Final convergence and harmonization ===")

    # Phase 3 configuration
    criterion_heatmap_phase3 = EnhancedCornerFocusedHeatmapLossV5_2(
        separation_weight=0.6,
        peak_separation_weight=0.5,
        edge_suppression_weight=0.6,
        peak_enhancement_weight=0.6,
        peak_to_second_ratio_weight=15.0,  # Maximum focus on peak-to-second ratio
        detection_rate_weight=3.0,
        segmentation_guidance_weight=1.0
    )

    # Initialize weight manager with target metrics
    criterion_heatmap_phase3.initialize_weight_manager(target_metrics)

    # Create base optimizer
    base_optimizer_phase3 = optim.AdamW(
        model.parameters(),
        lr=args.lr_phase3,
        weight_decay=args.weight_decay,
        amsgrad=True
    )

    # Wrap with metric-specific optimizer
    optimizer_phase3 = MetricSpecificLROptimizer(
        model=model,
        base_optimizer=base_optimizer_phase3,
        base_lr=args.lr_phase3
    )

    # Initialize multi-loss convergence scheduler
    scheduler_phase3 = MultiLossConvergenceScheduler(
        optimizer=optimizer_phase3,
        total_epochs=args.epochs_phase3,
        initial_lr=args.lr_phase3,
        min_lr=args.lr_phase3 * 0.05,
        warmup_epochs=2,
        target_losses={
            'total_loss': 20.0,
            'segmentation_loss': 0.05,
            'heatmap_loss': 10.0,
            'geometric_loss': 0.2
        },
        smoothing_factor=0.9,
        convergence_shape='exponential'
    )

    # Train phase 3
    model, history_phase3 = train_model_balanced(
        model=model,
        dataloaders=dataloaders,
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap_phase3,
        criterion_geometric=criterion_geometric,
        optimizer=optimizer_phase3,
        scheduler=scheduler_phase3,
        output_dir=args.output_dir,
        num_epochs=args.epochs_phase3,
        heatmap_weight=1.5,
        geometric_weight=0.8,
        save_interval=args.save_interval,
        phase_name='phase3',
        auto_tune_hyperparams=True,
        tune_interval=5,
        early_stopping_patience=10,
        max_checkpoints_to_keep=5
    )

    # Save history to JSON for analysis
    history_path = os.path.join(args.output_dir, 'logs', 'v5.2(3rd attempt)', f'history_final_phase3.json')
    with open(history_path, 'w') as f:
        # Convert numpy values to Python types for JSON serialization
        json_history = {}
        for key, value in history_phase3.items():
            if isinstance(value[0], dict):
                json_history[key] = [{k: (v if isinstance(v, list) else float(v)) for k, v in d.items()} for d in value]
            else:
                json_history[key] = [float(v) for v in value]
        json.dump(json_history, f, indent=4)

    # Generate analysis report
    print("\n=== Generating Phase 3 Analysis Report ===")
    try:
        report_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'generate_analysis_report.py')
        subprocess.run([
            sys.executable,
            report_script,
            '--history_path', history_path,
            '--output_dir', args.output_dir,
            '--phase_name', 'phase3'
        ], check=True)
        print("Phase 3 analysis report generated successfully")
    except Exception as e:
        print(f"Warning: Could not generate analysis report: {e}")

    # Generate ensemble inference script
    print("\n=== Generating Ensemble Inference Example ===")
    try:
        # Find ensemble model path
        ensemble_model_path = os.path.join(args.output_dir, 'checkpoints', 'v5.2(3rd attempt)', f'ensemble_model_phase3.pth')
        if os.path.exists(ensemble_model_path):
            # Find a sample image
            sample_images = [f for f in os.listdir(args.data_dir) if f.endswith(('.jpg', '.png', '.jpeg'))]
            if sample_images:
                sample_image_path = os.path.join(args.data_dir, sample_images[0])

                # Run ensemble inference
                inference_script = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'ensemble_inference.py')
                output_dir = os.path.join(args.output_dir, 'ensemble_results')
                os.makedirs(output_dir, exist_ok=True)

                subprocess.run([
                    sys.executable,
                    inference_script,
                    '--ensemble_path', ensemble_model_path,
                    '--image_path', sample_image_path,
                    '--output_dir', output_dir
                ], check=True)
                print(f"Ensemble inference example generated in {output_dir}")
            else:
                print("No sample images found for ensemble inference example")
        else:
            print(f"Ensemble model not found at {ensemble_model_path}")
    except Exception as e:
        print(f"Warning: Could not generate ensemble inference example: {e}")

    # Create dynamic ensemble model if requested
    if args.use_ensemble:
        print("\n=== Creating Dynamic Ensemble Model ===")

        # Find best checkpoints
        checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v5.2(3rd attempt)')
        checkpoint_files = []

        # Look for best checkpoints from each phase
        for phase in ['phase1', 'phase2', 'phase3']:
            best_p2s_path = os.path.join(checkpoints_dir, f'best_model_p2s_{phase}.pth')
            if os.path.exists(best_p2s_path):
                checkpoint_files.append(best_p2s_path)

            best_dr_path = os.path.join(checkpoints_dir, f'best_model_dr_{phase}.pth')
            if os.path.exists(best_dr_path):
                checkpoint_files.append(best_dr_path)

        # Add regular checkpoints
        for phase in ['phase1', 'phase2', 'phase3']:
            phase_epochs = getattr(args, f'epochs_{phase}', 0)
            for epoch in range(phase_epochs, 0, -5):
                checkpoint_path = os.path.join(checkpoints_dir, f'model_{phase}_epoch_{epoch}.pth')
                if os.path.exists(checkpoint_path):
                    checkpoint_files.append(checkpoint_path)
                    if len(checkpoint_files) >= args.ensemble_checkpoints:
                        break

        if len(checkpoint_files) > 1:
            print(f"Creating ensemble with {len(checkpoint_files)} models:")
            for i, path in enumerate(checkpoint_files):
                print(f"  {i+1}. {os.path.basename(path)}")

            # Create ensemble model
            ensemble_model = DynamicEnsembleModel(
                model_paths=checkpoint_files,
                device=device
            )

            # Save ensemble model configuration
            ensemble_config_path = os.path.join(args.output_dir, 'models', 'v5.2(3rd attempt)', 'dynamic_ensemble_config.json')
            os.makedirs(os.path.dirname(ensemble_config_path), exist_ok=True)

            with open(ensemble_config_path, 'w') as f:
                json.dump({
                    'model_paths': checkpoint_files,
                    'creation_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'weights': ensemble_model.model_weights,
                    'specialties': ensemble_model.model_specialties
                }, f, indent=2)

            print(f"Dynamic ensemble model configuration saved to {ensemble_config_path}")

            # Visualize ensemble weights
            weights_vis_path = os.path.join(args.output_dir, 'visualizations', 'v5.2(3rd attempt)', 'dynamic_ensemble_weights.png')
            ensemble_model.visualize_weights(save_path=weights_vis_path)
            print(f"Ensemble weights visualization saved to {weights_vis_path}")
        else:
            print("Not enough checkpoints found to create a dynamic ensemble model")

    # Update hyperparameter optimization history if used
    if args.use_bayesian_optimization and hyperparameter_optimizer is not None:
        print("\n=== Updating Hyperparameter Optimization History ===")

        # Collect metrics from training
        optimization_metrics = {}

        # Get peak-to-second ratio from best model
        if 'best_p2s_ratio' in locals():
            optimization_metrics['peak_to_second_ratio'] = best_p2s_ratio

        # Get detection rate from best model
        if 'best_detection_rate' in locals():
            optimization_metrics['detection_rate'] = best_detection_rate

        # Get segmentation IoU from best model
        if 'best_seg_iou' in locals():
            optimization_metrics['segmentation_iou'] = best_seg_iou

        # Get geometric consistency from best model
        if 'best_geometric_loss' in locals():
            # Convert loss to metric (higher is better)
            optimization_metrics['geometric_consistency'] = 1.0 / (1.0 + best_geometric_loss)

        # Collect parameters used
        optimization_params = {
            'heatmap_weight': args.heatmap_weight,
            'geometric_weight': args.geometric_weight,
            'edge_suppression_weight': args.edge_suppression_weight,
            'peak_enhancement_weight': args.peak_enhancement_weight,
            'separation_weight': args.separation_weight,
            'peak_separation_weight': args.peak_separation_weight,
            'learning_rate': args.lr_phase1,
            'weight_decay': args.weight_decay
        }

        # Update optimizer with results
        hyperparameter_optimizer.update(optimization_params, optimization_metrics)
        print(f"Updated hyperparameter optimization history with metrics: {optimization_metrics}")

        # Get suggestion for next run
        next_params = hyperparameter_optimizer.suggest(1)[0]
        print(f"Suggested parameters for next run: {next_params}")

    print("\nTraining completed!")


if __name__ == "__main__":
    main()
