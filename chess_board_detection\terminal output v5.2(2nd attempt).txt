PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> cd chess_board_detection; python train_v5_2.py --cpu
E:\New folder\Lib\site-packages\albumentations\__init__.py:28: UserWarning: A new version of Albumentations is available: '2.0.7' (you have '2.0.6'). Upgrade using: pip install -U albumentations. To disable automatic update checks, set the environment variable NO_ALBUMENTATIONS_UPDATE to 1.
  check_for_updates()
2025-05-18 17:49:25.634517: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
2025-05-18 17:49:28.529375: I tensorflow/core/util/port.cc:153] oneDNN custom operations are on. You may see slightly different numerical results due to floating-point round-off errors from different computation orders. To turn them off, set the environment variable `TF_ENABLE_ONEDNN_OPTS=0`.
CUDA not available. Using CPU.
Memory limits: 3GB maximum usage
Using device: cpu
Using pre-augmented dataset from: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326
Loaded 85 augmented samples from C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\augmented\v5.2\augmented_20250518_153326
Created dataloaders with 68 training samples and 17 validation samples
Train dataset size: 68
Validation dataset size: 17
Initializing v5.2 model...
Model moved to cpu
Model parameters: 17874848
Trainable parameters: 17874848

=== Phase 1: Balanced foundation with smooth convergence path ===
Saving outputs to v5.2(2nd attempt) folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Skipping model graph visualization in TensorBoard to conserve memory
Generating data augmentation visualizations...
Augmentation visualization directory created at C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(2nd attempt)\augmentations
Generated augmentation visualizations in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\visualizations\v5.2(2nd attempt)\augmentations
Epoch 1/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:14<00:00,  7.93s/it] 
train Loss: 97.9081, Seg Loss: 0.5067, Heatmap Loss: 64.7008, Geometric Loss: 0.8755
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 9.0771 🟢 (453.9% of target 2.0)
  avg_peak_to_mean_ratio: 47.6202
  avg_peak_to_second_ratio: 1.1317
  detection_rate: 0.9853 🟡 (98.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0080
  p2s_ratio_max: 1.5464
  p2s_ratio_std: 0.1475
  secondary_peak_distance: 82.4522
  primary_peak_sharpness: 1.7868
  primary_peak_isolation: 3.1388

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1350
  Top-Right: 1.0856
  Bottom-Right: 1.1388
  Bottom-Left: 1.1672

Secondary Peak Relative Position: x=-5.49, y=0.03
train Heatmap Components:
  mse_loss: 1.1725
  separation_loss: 3.9751
  peak_separation_loss: 28.1315
  edge_suppression_loss: 0.0103
  peak_enhancement_loss: 1.2804
  peak_to_second_ratio_loss: 1.0591
  avg_peak_to_second_ratio: 1.1069
  detection_rate_loss: 0.0027
  segmentation_guidance_loss: 0.8672
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:12<00:00,  2.54s/it]
val Loss: 8195.7264, Seg Loss: 0.4993, Heatmap Loss: 5463.2887, Geometric Loss: 0.7357
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 0.4312 🔴 (21.6% of target 2.0)
  avg_peak_to_mean_ratio: 2.8593
  avg_peak_to_second_ratio: 1.1481
  detection_rate: 0.2941 🔴 (29.4% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0084
  p2s_ratio_max: 1.5418
  p2s_ratio_std: 0.1643
  secondary_peak_distance: 92.6423
  primary_peak_sharpness: 0.0884
  primary_peak_isolation: 4.1256

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.3135
  Top-Right: 1.0715
  Bottom-Right: 1.0493
  Bottom-Left: 1.1581

Secondary Peak Relative Position: x=7.65, y=-1.63
val Heatmap Components:
  mse_loss: 0.0880
  separation_loss: 0.6933
  peak_separation_loss: 1147.1419
  edge_suppression_loss: 0.0050
  peak_enhancement_loss: 0.9857
  peak_to_second_ratio_loss: 1.1144
  avg_peak_to_second_ratio: 1.0936
  detection_rate_loss: 0.1376
  segmentation_guidance_loss: 0.3544
Current learning rate: 0.000224
New best model saved with loss: 8195.7264
New best model saved with peak-to-second ratio: 1.1481
New best model saved with detection rate: 0.2941
New best model saved with combined score: 0.5277 (loss: 8195.7264, p2s: 1.1481, detection: 0.2941)
Early stopping: New best score: 0.5277

Epoch 2/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:19<00:00,  8.23s/it] 
train Loss: 143.6432, Seg Loss: 0.4605, Heatmap Loss: 95.2251, Geometric Loss: 0.8626
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 6.5554 🟢 (327.8% of target 2.0)
  avg_peak_to_mean_ratio: 125.4621
  avg_peak_to_second_ratio: 1.1795
  detection_rate: 0.9853 🟡 (98.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0085
  p2s_ratio_max: 1.7736
  p2s_ratio_std: 0.2110
  secondary_peak_distance: 84.0343
  primary_peak_sharpness: 1.3142
  primary_peak_isolation: 3.4183

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1919
  Top-Right: 1.1109
  Bottom-Right: 1.1866
  Bottom-Left: 1.2285

Secondary Peak Relative Position: x=-6.90, y=4.31
train Heatmap Components:
  mse_loss: 0.7152
  separation_loss: 4.6973
  peak_separation_loss: 20.1838
  edge_suppression_loss: 0.0100
  peak_enhancement_loss: 1.0495
  peak_to_second_ratio_loss: 1.1402
  avg_peak_to_second_ratio: 1.1188
  detection_rate_loss: 0.0081
  segmentation_guidance_loss: 0.5522
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.30s/it] 
val Loss: 140.1252, Seg Loss: 0.5250, Heatmap Loss: 92.8842, Geometric Loss: 0.6846
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 2.2202 🟢 (111.0% of target 2.0)
  avg_peak_to_mean_ratio: 20.7731
  avg_peak_to_second_ratio: 1.1434
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0035
  p2s_ratio_max: 1.5608
  p2s_ratio_std: 0.1495
  secondary_peak_distance: 79.1729
  primary_peak_sharpness: 0.4872
  primary_peak_isolation: 3.8291

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1912
  Top-Right: 1.0807
  Bottom-Right: 1.1540
  Bottom-Left: 1.1478

Secondary Peak Relative Position: x=-5.16, y=18.18
val Heatmap Components:
  mse_loss: 0.2111
  separation_loss: 0.6148
  peak_separation_loss: 21.0474
  edge_suppression_loss: 0.0093
  peak_enhancement_loss: 1.0303
  peak_to_second_ratio_loss: 1.2839
  avg_peak_to_second_ratio: 1.0861
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000368
New best model saved with loss: 140.1252
New best model saved with detection rate: 1.0000
New best model saved with combined score: 0.6676 (loss: 140.1252, p2s: 1.1434, detection: 1.0000)
Early stopping: New best score: 0.6676

Epoch 3/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:17<00:00,  8.11s/it] 
train Loss: 82.2882, Seg Loss: 0.5029, Heatmap Loss: 54.3014, Geometric Loss: 0.8331
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 6.8451 🟢 (342.3% of target 2.0)
  avg_peak_to_mean_ratio: 168.7961
  avg_peak_to_second_ratio: 1.2333
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0123
  p2s_ratio_max: 1.8646
  p2s_ratio_std: 0.2357
  secondary_peak_distance: 82.6405
  primary_peak_sharpness: 1.4427
  primary_peak_isolation: 3.3526

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2356
  Top-Right: 1.1675
  Bottom-Right: 1.2598
  Bottom-Left: 1.2705

Secondary Peak Relative Position: x=-6.60, y=2.69
train Heatmap Components:
  mse_loss: 0.4567
  separation_loss: 6.1140
  peak_separation_loss: 12.9109
  edge_suppression_loss: 0.0058
  peak_enhancement_loss: 0.9349
  peak_to_second_ratio_loss: 1.1683
  avg_peak_to_second_ratio: 1.1419
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.26s/it] 
val Loss: 37.0699, Seg Loss: 0.4291, Heatmap Loss: 24.2199, Geometric Loss: 0.7776
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.0395 🟢 (202.0% of target 2.0)
  avg_peak_to_mean_ratio: 438.7000
  avg_peak_to_second_ratio: 1.2251
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0089
  p2s_ratio_max: 1.7248
  p2s_ratio_std: 0.2243
  secondary_peak_distance: 49.6087
  primary_peak_sharpness: 1.0467
  primary_peak_isolation: 3.4728

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.1726
  Top-Right: 1.0927
  Bottom-Right: 1.3289
  Bottom-Left: 1.3063

Secondary Peak Relative Position: x=-7.50, y=-0.50
val Heatmap Components:
  mse_loss: 0.2446
  separation_loss: 3.5707
  peak_separation_loss: 5.9755
  edge_suppression_loss: -0.0063
  peak_enhancement_loss: 1.0755
  peak_to_second_ratio_loss: 1.2131
  avg_peak_to_second_ratio: 1.1180
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000512
New best model saved with loss: 37.0699
New best model saved with peak-to-second ratio: 1.2251
New best model saved with combined score: 0.6903 (loss: 37.0699, p2s: 1.2251, detection: 1.0000)
Early stopping: New best score: 0.6903

Epoch 4/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:14<00:00,  7.89s/it] 
train Loss: 128.5569, Seg Loss: 0.4555, Heatmap Loss: 85.1869, Geometric Loss: 0.8024
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 5.5376 🟢 (276.9% of target 2.0)
  avg_peak_to_mean_ratio: 281.6426
  avg_peak_to_second_ratio: 1.2265
  detection_rate: 0.9853 🟡 (98.5% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0111
  p2s_ratio_max: 1.8477
  p2s_ratio_std: 0.2463
  secondary_peak_distance: 76.3161
  primary_peak_sharpness: 1.4079
  primary_peak_isolation: 3.2180

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2537
  Top-Right: 1.1645
  Bottom-Right: 1.2419
  Bottom-Left: 1.2460

Secondary Peak Relative Position: x=0.82, y=0.19
train Heatmap Components:
  mse_loss: 0.2368
  separation_loss: 9.5793
  peak_separation_loss: 20.8341
  edge_suppression_loss: -0.0021
  peak_enhancement_loss: 1.0077
  peak_to_second_ratio_loss: 1.4515
  avg_peak_to_second_ratio: 1.1071
  detection_rate_loss: 0.0053
  segmentation_guidance_loss: 0.0524
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.13s/it] 
val Loss: 536.4909, Seg Loss: 0.4722, Heatmap Loss: 357.1442, Geometric Loss: 0.7560
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.8665 🟢 (193.3% of target 2.0)
  avg_peak_to_mean_ratio: 6.5034
  avg_peak_to_second_ratio: 1.2856
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0231
  p2s_ratio_max: 1.9601
  p2s_ratio_std: 0.2904
  secondary_peak_distance: 81.7362
  primary_peak_sharpness: 0.8631
  primary_peak_isolation: 4.1340

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.3964
  Top-Right: 1.2373
  Bottom-Right: 1.2306
  Bottom-Left: 1.2779

Secondary Peak Relative Position: x=-2.93, y=-8.01
val Heatmap Components:
  mse_loss: 0.2064
  separation_loss: 5.4870
  peak_separation_loss: 86.1062
  edge_suppression_loss: -0.0033
  peak_enhancement_loss: 1.1582
  peak_to_second_ratio_loss: 1.2887
  avg_peak_to_second_ratio: 1.1518
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000656
New best model saved with peak-to-second ratio: 1.2856
Early stopping: No improvement for 1/15 epochs. Best: 0.6903, Current: 0.5571

Epoch 5/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:11<00:00,  7.76s/it] 
train Loss: 68.8449, Seg Loss: 0.5003, Heatmap Loss: 45.3350, Geometric Loss: 0.8553
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 8.1186 🟢 (405.9% of target 2.0)
  avg_peak_to_mean_ratio: 35.0251
  avg_peak_to_second_ratio: 1.2384
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0098
  p2s_ratio_max: 1.7621
  p2s_ratio_std: 0.2378
  secondary_peak_distance: 82.8881
  primary_peak_sharpness: 1.9440
  primary_peak_isolation: 3.9909

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.3229
  Top-Right: 1.1207
  Bottom-Right: 1.2588
  Bottom-Left: 1.2512

Secondary Peak Relative Position: x=-8.16, y=9.73
train Heatmap Components:
  mse_loss: 0.2586
  separation_loss: 12.6868
  peak_separation_loss: 10.3329
  edge_suppression_loss: -0.0044
  peak_enhancement_loss: 1.2627
  peak_to_second_ratio_loss: 1.3701
  avg_peak_to_second_ratio: 1.1536
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 1.1910
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.37s/it] 
val Loss: 1465.5291, Seg Loss: 0.4572, Heatmap Loss: 976.5064, Geometric Loss: 0.7810
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.7546 🟢 (187.7% of target 2.0)
  avg_peak_to_mean_ratio: 21.3150
  avg_peak_to_second_ratio: 1.3439
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0054
  p2s_ratio_max: 2.5493
  p2s_ratio_std: 0.4608
  secondary_peak_distance: 103.0461
  primary_peak_sharpness: 0.9433
  primary_peak_isolation: 8.9210

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.5694
  Top-Right: 1.1271
  Bottom-Right: 1.3508
  Bottom-Left: 1.3282

Secondary Peak Relative Position: x=-1.35, y=1.82
val Heatmap Components:
  mse_loss: 0.3267
  separation_loss: 8.1909
  peak_separation_loss: 237.6094
  edge_suppression_loss: -0.0006
  peak_enhancement_loss: 1.3252
  peak_to_second_ratio_loss: 1.1407
  avg_peak_to_second_ratio: 1.2265
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000800
New best model saved with peak-to-second ratio: 1.3439

=== Automatic Hyperparameter Tuning ===
New best weights: heatmap_weight=1.50, geometric_weight=0.40
Increasing heatmap weight to improve peak-to-second ratio
Increasing geometric weight to improve overall loss
Adjusted weights: heatmap_weight: 1.50 -> 1.70, geometric_weight: 0.40 -> 0.50

=== Learning Rate Tuning ===
Early stopping: No improvement for 2/15 epochs. Best: 0.6903, Current: 0.5733
Skipping heatmap and keypoint visualizations to conserve memory...
Visualization skipped for epoch 5 to prevent memory issues
Model evaluated at epoch 5
Output shapes: segmentation=torch.Size([4, 1, 256, 256]), heatmaps=torch.Size([4, 4, 256, 256])

Epoch 6/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:22<00:00,  8.37s/it] 
train Loss: 403.2558, Seg Loss: 0.4681, Heatmap Loss: 236.6963, Geometric Loss: 0.8079
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 4.8294 🟢 (241.5% of target 2.0)
  avg_peak_to_mean_ratio: 31.7186
  avg_peak_to_second_ratio: 1.2243
  detection_rate: 0.9816 🟡 (98.2% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0099
  p2s_ratio_max: 1.8571
  p2s_ratio_std: 0.2441
  secondary_peak_distance: 100.4937
  primary_peak_sharpness: 1.2558
  primary_peak_isolation: 4.6338

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2785
  Top-Right: 1.1312
  Bottom-Right: 1.2194
  Bottom-Left: 1.2680

Secondary Peak Relative Position: x=-14.96, y=-1.15
train Heatmap Components:
  mse_loss: 0.1817
  separation_loss: 11.3053
  peak_separation_loss: 53.1103
  edge_suppression_loss: 0.0037
  peak_enhancement_loss: 1.1201
  peak_to_second_ratio_loss: 1.5734
  avg_peak_to_second_ratio: 1.1411
  detection_rate_loss: 0.0048
  segmentation_guidance_loss: 0.8774
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:11<00:00,  2.36s/it] 
val Loss: 277.7924, Seg Loss: 0.4606, Heatmap Loss: 162.8915, Geometric Loss: 0.8325
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 9.1761 🟢 (458.8% of target 2.0)
  avg_peak_to_mean_ratio: 30.5516
  avg_peak_to_second_ratio: 1.2381
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0164
  p2s_ratio_max: 1.8392
  p2s_ratio_std: 0.2505
  secondary_peak_distance: 82.2739
  primary_peak_sharpness: 1.2721
  primary_peak_isolation: 3.9148

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.2802
  Top-Right: 1.1912
  Bottom-Right: 1.2098
  Bottom-Left: 1.2712

Secondary Peak Relative Position: x=-4.19, y=-14.91
val Heatmap Components:
  mse_loss: 2.0462
  separation_loss: 11.9206
  peak_separation_loss: 36.5539
  edge_suppression_loss: 0.0177
  peak_enhancement_loss: 0.9597
  peak_to_second_ratio_loss: 1.6172
  avg_peak_to_second_ratio: 1.1386
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000800
Early stopping: No improvement for 3/15 epochs. Best: 0.6903, Current: 0.5439


Epoch 7/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:14<00:00,  7.93s/it] 
train Loss: 560.7997, Seg Loss: 0.4295, Heatmap Loss: 461.2247, Geometric Loss: 0.7988
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 5.8580 🟢 (292.9% of target 2.0)
  avg_peak_to_mean_ratio: 43.3868
  avg_peak_to_second_ratio: 1.4179
  detection_rate: 0.9779 🟡 (97.8% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0249
  p2s_ratio_max: 3.0116
  p2s_ratio_std: 0.5820
  secondary_peak_distance: 98.8754
  primary_peak_sharpness: 1.4265
  primary_peak_isolation: 6.8920

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4941
  Top-Right: 1.2476
  Bottom-Right: 1.4061
  Bottom-Left: 1.5238

Secondary Peak Relative Position: x=-7.42, y=-3.77
train Heatmap Components:
  mse_loss: 0.2585
  separation_loss: 12.0524
  peak_separation_loss: 104.4967
  edge_suppression_loss: 0.0093
  peak_enhancement_loss: 0.9730
  peak_to_second_ratio_loss: 1.5547
  avg_peak_to_second_ratio: 1.1955
  detection_rate_loss: 0.0034
  segmentation_guidance_loss: 0.7029
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:14<00:00,  2.87s/it] 
val Loss: 150.5972, Seg Loss: 0.5066, Heatmap Loss: 147.1179, Geometric Loss: 0.9008
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 3.8901 🟢 (194.5% of target 2.0)
  avg_peak_to_mean_ratio: 23.5710
  avg_peak_to_second_ratio: 1.3446
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0177
  p2s_ratio_max: 1.8987
  p2s_ratio_std: 0.2823
  secondary_peak_distance: 106.6255
  primary_peak_sharpness: 0.9294
  primary_peak_isolation: 5.4683

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.4024
  Top-Right: 1.3090
  Bottom-Right: 1.2104
  Bottom-Left: 1.4566

Secondary Peak Relative Position: x=0.26, y=24.49
val Heatmap Components:
  mse_loss: 0.1132
  separation_loss: 17.1425
  peak_separation_loss: 31.5327
  edge_suppression_loss: 0.0017
  peak_enhancement_loss: 0.8408
  peak_to_second_ratio_loss: 1.0972
  avg_peak_to_second_ratio: 1.2749
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0744
Current learning rate: 0.000795
New best model saved with peak-to-second ratio: 1.3446
Early stopping: No improvement for 4/15 epochs. Best: 0.6903, Current: 0.5735

Epoch 8/20
----------
100%|███████████████████████████████████████████████████████████████████████████████| 17/17 [02:10<00:00,  7.66s/it] 
train Loss: 77.3463, Seg Loss: 0.4326, Heatmap Loss: 153.9397, Geometric Loss: 0.7875
=== train Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 6.9437 🟢 (347.2% of target 2.0)
  avg_peak_to_mean_ratio: 48.8096
  avg_peak_to_second_ratio: 1.4239
  detection_rate: 0.9890 🟡 (98.9% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0105
  p2s_ratio_max: 2.2484
  p2s_ratio_std: 0.4025
  secondary_peak_distance: 120.8560
  primary_peak_sharpness: 1.7278
  primary_peak_isolation: 7.8936

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.5437
  Top-Right: 1.2133
  Bottom-Right: 1.4208
  Bottom-Left: 1.5179

Secondary Peak Relative Position: x=-18.63, y=-10.26
train Heatmap Components:
  mse_loss: 0.1774
  separation_loss: 15.5238
  peak_separation_loss: 37.7357
  edge_suppression_loss: 0.0110
  peak_enhancement_loss: 0.9074
  peak_to_second_ratio_loss: 1.2207
  avg_peak_to_second_ratio: 1.2822
  detection_rate_loss: 0.0027
  segmentation_guidance_loss: 0.2642
100%|█████████████████████████████████████████████████████████████████████████████████| 5/5 [00:10<00:00,  2.11s/it] 
val Loss: 60.0411, Seg Loss: 0.3890, Heatmap Loss: 74.1168, Geometric Loss: 0.8286
=== val Corner Confidence Metrics ===
Main Metrics:
  avg_peak_value: 5.0740 🟢 (253.7% of target 2.0)
  avg_peak_to_mean_ratio: 22.4858
  avg_peak_to_second_ratio: 1.3799
  detection_rate: 1.0000 🟢 (100.0% of target 1.0)

Detailed Peak-to-Second Ratio Metrics:
  p2s_ratio_min: 1.0226
  p2s_ratio_max: 2.3830
  p2s_ratio_std: 0.3902
  secondary_peak_distance: 120.8859
  primary_peak_sharpness: 1.2796
  primary_peak_isolation: 3.9310

Per-Corner Peak-to-Second Ratios:
  Top-Left: 1.3794
  Top-Right: 1.3827
  Bottom-Right: 1.3609
  Bottom-Left: 1.3967

Secondary Peak Relative Position: x=-3.88, y=-16.06
val Heatmap Components:
  mse_loss: 0.1507
  separation_loss: 18.0948
  peak_separation_loss: 3.2288
  edge_suppression_loss: 0.0037
  peak_enhancement_loss: 0.8841
  peak_to_second_ratio_loss: 1.2451
  avg_peak_to_second_ratio: 1.2700
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.9089
Current learning rate: 0.000636
New best model saved with peak-to-second ratio: 1.3799
Early stopping: No improvement for 5/15 epochs. Best: 0.6903, Current: 0.6404




