"""
<PERSON><PERSON><PERSON> to visualize augmented data from the chess board detection dataset.
This helps to verify that the augmentations are working correctly and
that corners are being properly transformed.
"""

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import cv2
import torch
import datetime
from torch.utils.data import DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2

from utils.real_dataset import RealChessBoardDataset
from config import DATA_DIR, INPUT_SIZE

# Define enhanced training augmentation
def get_enhanced_training_augmentation():
    """
    Enhanced training augmentation that ensures the full chess board remains visible.
    Uses black padding and avoids rotations that cause issues with corner detection.
    """
    return <PERSON><PERSON>([
        # Basic flips only (no rotations)
        A.Horizon<PERSON>(p=0.5),
        <PERSON><PERSON>ert<PERSON>lip(p=0.5),

        # Color augmentations
        A.RandomBrightnessContrast(brightness_limit=0.3, contrast_limit=0.3, p=0.7),
        <PERSON><PERSON>alue(hue_shift_limit=15, sat_shift_limit=25, val_shift_limit=15, p=0.5),

        # Noise and blur
        A.<PERSON>([
            <PERSON><PERSON>(p=1.0),
            <PERSON><PERSON>(blur_limit=5, p=1.0),
            A.Blur(blur_limit=3, p=1.0)
        ], p=0.5),

        # Ensure consistent size with padding
        A.PadIfNeeded(
            min_height=INPUT_SIZE[0],
            min_width=INPUT_SIZE[1],
            border_mode=cv2.BORDER_CONSTANT,
            p=1.0
        ),

        # Normalization and conversion to tensor
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))

def visualize_sample(sample, title="Sample"):
    """
    Visualize a sample from the dataset.

    Args:
        sample: Dictionary containing 'image', 'mask', 'corner_heatmaps', and 'corners'
        title: Title for the plot
    """
    # Create figure with subplots
    fig, axs = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(title, fontsize=16)

    # Get image
    image = sample['image']
    if isinstance(image, torch.Tensor):
        # Denormalize image
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        image = image * std + mean
        image = image.permute(1, 2, 0).numpy()
        image = np.clip(image, 0, 1)

    # Get mask
    mask = sample['mask']
    if isinstance(mask, torch.Tensor):
        mask = mask.squeeze().numpy()

    # Get corners
    corners = sample['corners']
    if isinstance(corners, torch.Tensor):
        corners = corners.numpy()

    # Get heatmaps
    heatmaps = sample['corner_heatmaps']
    if isinstance(heatmaps, torch.Tensor):
        heatmaps = heatmaps.numpy()

    # Get image dimensions
    img_h, img_w = image.shape[:2]

    # Plot image
    axs[0, 0].imshow(image)
    axs[0, 0].set_title('Image')
    axs[0, 0].axis('off')

    # Plot mask
    axs[0, 1].imshow(mask, cmap='gray')
    axs[0, 1].set_title('Mask')
    axs[0, 1].axis('off')

    # Plot image with corners
    axs[0, 2].imshow(image)
    # Draw corners
    corner_colors = ['r', 'g', 'b', 'y']
    corner_names = ['TL', 'TR', 'BR', 'BL']  # Original order: TL, TR, BR, BL

    # Draw image boundaries
    axs[0, 2].plot([0, img_w-1, img_w-1, 0, 0], [0, 0, img_h-1, img_h-1, 0], 'c--', linewidth=1)

    # Check if all corners are within image boundaries
    all_corners_valid = True
    for i in range(4):
        x, y = corners[i*2], corners[i*2+1]

        # Check if corner is within image boundaries
        is_valid = 0 <= x < img_w and 0 <= y < img_h
        all_corners_valid = all_corners_valid and is_valid

        # Draw corner with different style based on validity
        marker_style = 'o' if is_valid else 'x'
        marker_size = 8 if is_valid else 10
        axs[0, 2].plot(x, y, marker_style, color=corner_colors[i], markersize=marker_size)

        # Add corner label
        label_text = f"{corner_names[i]}"
        if not is_valid:
            label_text += " (OUT)"
        axs[0, 2].text(max(0, min(x+5, img_w-30)), max(0, min(y+5, img_h-10)),
                      label_text, color=corner_colors[i], fontsize=10)

    # Draw connections between corners to form a quadrilateral
    corners_array = np.array([(corners[i*2], corners[i*2+1]) for i in range(4)])
    corners_array = np.vstack([corners_array, corners_array[0]])  # Close the loop
    axs[0, 2].plot(corners_array[:, 0], corners_array[:, 1], 'g-', linewidth=1, alpha=0.7)

    # Update title to indicate if all corners are valid
    title_suffix = " (All corners valid)" if all_corners_valid else " (Some corners outside image!)"
    axs[0, 2].set_title('Image with Corners' + title_suffix)
    axs[0, 2].axis('off')

    # Plot heatmaps
    for i in range(4):
        if i < heatmaps.shape[0]:
            axs[1, i % 3].imshow(heatmaps[i], cmap='jet')
            axs[1, i % 3].set_title(f'Heatmap {corner_names[i]}')
            axs[1, i % 3].axis('off')

    plt.tight_layout()
    return fig

def main():
    # Load dataset
    data_dir = os.path.join(DATA_DIR, 'real')
    annotation_file = os.path.join(DATA_DIR, 'real_annotations.json')

    # Create dataset with augmentation
    dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=get_enhanced_training_augmentation()
    )

    # Create output directory with timestamp to avoid overwriting
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = os.path.join('visualizations', 'augmentations', f'aug_check_{timestamp}')
    os.makedirs(output_dir, exist_ok=True)

    # Visualize samples
    num_samples = 10  # Increased from 5
    num_augmentations = 5  # Increased from 3

    # Create a summary file to track corner validity
    summary_file = os.path.join(output_dir, 'corner_validity_summary.txt')
    with open(summary_file, 'w') as f:
        f.write("Corner Validity Summary\n")
        f.write("======================\n\n")

    # Track statistics
    total_augmentations = 0
    valid_augmentations = 0

    # Create a dataset without augmentation for original samples
    no_aug_dataset = RealChessBoardDataset(
        data_dir=data_dir,
        annotation_file=annotation_file,
        transform=None
    )

    for i in range(min(num_samples, len(dataset))):
        # Get original sample without augmentation
        original_sample = no_aug_dataset[i]

        # Visualize original sample
        fig = visualize_sample(original_sample, f"Sample {i+1} - Original (No Augmentation)")
        plt.savefig(os.path.join(output_dir, f"sample_{i+1}_original.png"))
        plt.close(fig)

        # Visualize augmented samples
        for j in range(num_augmentations):
            # Get augmented sample
            augmented_sample = dataset[i]

            # Check if all corners are within image boundaries
            corners = augmented_sample['corners']
            if isinstance(corners, torch.Tensor):
                corners = corners.numpy()

            image = augmented_sample['image']
            if isinstance(image, torch.Tensor):
                img_h, img_w = image.shape[1:3]
            else:
                img_h, img_w = image.shape[:2]

            all_corners_valid = True
            for k in range(4):
                x, y = corners[k*2], corners[k*2+1]
                if not (0 <= x < img_w and 0 <= y < img_h):
                    all_corners_valid = False
                    break

            # Update statistics
            total_augmentations += 1
            if all_corners_valid:
                valid_augmentations += 1

            # Visualize augmented sample
            fig = visualize_sample(augmented_sample, f"Sample {i+1} - Augmentation {j+1}")
            status = "valid" if all_corners_valid else "invalid"
            plt.savefig(os.path.join(output_dir, f"sample_{i+1}_aug_{j+1}_{status}.png"))
            plt.close(fig)

            # Log to summary file
            with open(summary_file, 'a') as f:
                f.write(f"Sample {i+1} - Augmentation {j+1}: {'All corners valid' if all_corners_valid else 'Some corners outside image'}\n")

    # Write summary statistics
    with open(summary_file, 'a') as f:
        f.write("\nSummary Statistics\n")
        f.write("=================\n")
        f.write(f"Total augmentations: {total_augmentations}\n")
        f.write(f"Valid augmentations: {valid_augmentations}\n")
        f.write(f"Invalid augmentations: {total_augmentations - valid_augmentations}\n")
        f.write(f"Validity rate: {valid_augmentations / total_augmentations * 100:.2f}%\n")

    print(f"Visualizations saved to {output_dir}")
    print(f"Valid augmentations: {valid_augmentations}/{total_augmentations} ({valid_augmentations / total_augmentations * 100:.2f}%)")

if __name__ == "__main__":
    main()
