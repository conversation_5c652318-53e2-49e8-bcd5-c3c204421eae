PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection/train_breakthrough_v6.py
🚀 BREAKTHROUGH U-NET V6 TRAINING
============================================================
🎯 TARGET: Exceed V5's 0.9341 Dice and achieve 0.95+ with target 0.99!
🏆 V5 Baseline: 0.9341 Dice
🚀 V6 Target: 0.9500 Dice
🌟 V6 Stretch: 0.9900 Dice
Configuration: {
  "dataset_dir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\data\\augmented\\v5.2\\augmented_20250518_153326",
  "save_dir": "chess_board_detection/breakthrough_v6_results",
  "epochs": 100,
  "batch_size": 4,
  "learning_rate": 0.001,
  "base_channels": 32,
  "accumulation_steps": 2,
  "num_workers": 0,
  "v5_baseline": 0.9341,
  "target_dice": 0.95,
  "stretch_target": 0.99
}
Using device: cuda
Creating enhanced dataloaders...
Found 102 total sample folders
Train samples: 81
Val samples: 21
Found 81 valid samples
Found 21 valid samples
Creating Breakthrough U-Net V6...
V6 Model parameters: 4,585,479
Efficiency vs V5: 1.07x (+6.9% change)
🎯 Target: Achieve 0.95+ Dice with 4,585,479 params!
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v6.py:298: FutureWarning: `torch.cuda.amp.GradScaler(args...)` is deprecated. Please use `torch.amp.GradScaler('cuda', args...)` instead.
  scaler = GradScaler()
🚀 Starting V6 breakthrough training for 100 epochs...

🔥 Epoch 1/100
🚀 V6 Breakthrough Training:   0%|                                                  | 0/21 [00:00<?, ?it/s]C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:61: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  image = torch.load(image_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\dataset\augmented_segmentation_dataset.py:62: FutureWarning: You are using `torch.load` with `weights_only=False` (the current default value), which uses the default pickle module implicitly. It is possible to construct malicious pickle data which will execute arbitrary code during unpickling (See https://github.com/pytorch/pytorch/blob/main/SECURITY.md#untrusted-models for more details). In a future release, the default value for `weights_only` will be flipped to `True`. This limits the functions that could be executed during unpickling. Arbitrary objects will no longer be allowed to be loaded via this mode unless they are explicitly allowlisted by the user via `torch.serialization.add_safe_globals`. We recommend you start setting `weights_only=True` for any use case where you don't have full control of the loaded file. Please open an issue on GitHub for any issues related to this experimental feature.
  mask = torch.load(mask_path, map_location='cpu')
C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_breakthrough_v6.py:138: FutureWarning: `torch.cuda.amp.autocast(args...)` is deprecated. Please use `torch.amp.autocast('cuda', args...)` instead.
  with autocast():
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:11<00:00,  1.83it/s, Loss=2.4115, Dice=0.2612, IoU=0.1502, F
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:01<00:00,  5.05it/s, Loss=1.2933, Dice=0.7653, IoU=0.6198, F 
Train - Loss: 1.9104, Dice: 0.4315, IoU: 0.2781, F1: 0.4315
Val   - Loss: 1.6795, Dice: 0.5285, IoU: 0.3785, F1: 0.5285
LR: 0.000040
✅ New best model saved! Val Dice: 0.5285

🔥 Epoch 2/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=2.0295, Dice=0.4318, IoU=0.2754, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.23it/s, Loss=0.9898, Dice=0.7634, IoU=0.6174, F 
Train - Loss: 1.7776, Dice: 0.5006, IoU: 0.3376, F1: 0.5006
Val   - Loss: 1.5196, Dice: 0.5899, IoU: 0.4266, F1: 0.5899
LR: 0.000040
✅ New best model saved! Val Dice: 0.5899

🔥 Epoch 3/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=1.5655, Dice=0.5754, IoU=0.4039, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.21it/s, Loss=0.9733, Dice=0.7634, IoU=0.6174, F 
Train - Loss: 1.6082, Dice: 0.5580, IoU: 0.3910, F1: 0.5580
Val   - Loss: 1.7678, Dice: 0.5507, IoU: 0.3922, F1: 0.5507
LR: 0.000040

🔥 Epoch 4/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=1.4037, Dice=0.6254, IoU=0.4550, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.32it/s, Loss=0.9303, Dice=0.7642, IoU=0.6183, F 
Train - Loss: 1.3897, Dice: 0.6284, IoU: 0.4604, F1: 0.6284
Val   - Loss: 1.8241, Dice: 0.5393, IoU: 0.3828, F1: 0.5393
LR: 0.000041

🔥 Epoch 5/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=1.1925, Dice=0.6943, IoU=0.5317, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.53it/s, Loss=1.2884, Dice=0.6932, IoU=0.5305, F 
Train - Loss: 1.2604, Dice: 0.6646, IoU: 0.5035, F1: 0.6646
Val   - Loss: 2.0671, Dice: 0.5353, IoU: 0.3718, F1: 0.5353
LR: 0.000041

🔥 Epoch 6/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=1.1248, Dice=0.7061, IoU=0.5458, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.44it/s, Loss=2.6129, Dice=0.0596, IoU=0.0307, F 
Train - Loss: 1.1654, Dice: 0.6907, IoU: 0.5327, F1: 0.6907
Val   - Loss: 2.2653, Dice: 0.2627, IoU: 0.1620, F1: 0.2627
LR: 0.000042

🔥 Epoch 7/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.55it/s, Loss=1.0321, Dice=0.7123, IoU=0.5531, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.42it/s, Loss=1.3443, Dice=0.6716, IoU=0.5056, F 
Train - Loss: 1.0138, Dice: 0.7310, IoU: 0.5839, F1: 0.7310
Val   - Loss: 1.8418, Dice: 0.5338, IoU: 0.3734, F1: 0.5338
LR: 0.000043

🔥 Epoch 8/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=0.8835, Dice=0.7812, IoU=0.6410, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.42it/s, Loss=0.7102, Dice=0.8156, IoU=0.6886, F 
Train - Loss: 0.9144, Dice: 0.7596, IoU: 0.6198, F1: 0.7596
Val   - Loss: 1.8500, Dice: 0.6114, IoU: 0.4517, F1: 0.6114
LR: 0.000043
✅ New best model saved! Val Dice: 0.6114

🔥 Epoch 9/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=0.9789, Dice=0.7450, IoU=0.5936, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.40it/s, Loss=0.7368, Dice=0.8037, IoU=0.6719, F 
Train - Loss: 0.8348, Dice: 0.7797, IoU: 0.6453, F1: 0.7797
Val   - Loss: 1.5909, Dice: 0.5989, IoU: 0.4392, F1: 0.5989
LR: 0.000044

🔥 Epoch 10/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.8705, Dice=0.7386, IoU=0.5856, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.28it/s, Loss=0.8881, Dice=0.7942, IoU=0.6587, F 
Train - Loss: 0.7499, Dice: 0.8007, IoU: 0.6734, F1: 0.8007
Val   - Loss: 1.8524, Dice: 0.6081, IoU: 0.4469, F1: 0.6081
LR: 0.000045

🔥 Epoch 11/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.4141, Dice=0.9035, IoU=0.8240, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.27it/s, Loss=0.7065, Dice=0.8334, IoU=0.7144, F 
Train - Loss: 0.6896, Dice: 0.8156, IoU: 0.6968, F1: 0.8156
Val   - Loss: 1.5697, Dice: 0.6245, IoU: 0.4656, F1: 0.6245
LR: 0.000047
✅ New best model saved! Val Dice: 0.6245

🔥 Epoch 12/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.48it/s, Loss=0.3792, Dice=0.9177, IoU=0.8478, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.36it/s, Loss=0.8644, Dice=0.7837, IoU=0.6443, F 
Train - Loss: 0.6323, Dice: 0.8318, IoU: 0.7185, F1: 0.8318
Val   - Loss: 2.1315, Dice: 0.5721, IoU: 0.4103, F1: 0.5721
LR: 0.000048

🔥 Epoch 13/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=1.4901, Dice=0.5395, IoU=0.3694, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.38it/s, Loss=0.7813, Dice=0.8112, IoU=0.6824, F 
Train - Loss: 0.6264, Dice: 0.8296, IoU: 0.7221, F1: 0.8296
Val   - Loss: 1.9849, Dice: 0.6341, IoU: 0.4733, F1: 0.6341
LR: 0.000049
✅ New best model saved! Val Dice: 0.6341

🔥 Epoch 14/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.6066, Dice=0.8264, IoU=0.7042, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.44it/s, Loss=0.7790, Dice=0.8096, IoU=0.6801, F 
Train - Loss: 0.5959, Dice: 0.8397, IoU: 0.7319, F1: 0.8397
Val   - Loss: 1.7955, Dice: 0.6334, IoU: 0.4726, F1: 0.6334
LR: 0.000051

🔥 Epoch 15/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.3730, Dice=0.9081, IoU=0.8316, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.36it/s, Loss=0.9025, Dice=0.7836, IoU=0.6442, F 
Train - Loss: 0.4803, Dice: 0.8750, IoU: 0.7827, F1: 0.8750
Val   - Loss: 1.9899, Dice: 0.6191, IoU: 0.4563, F1: 0.6191
LR: 0.000052

🔥 Epoch 16/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.4471, Dice=0.8907, IoU=0.8030, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=0.9593, Dice=0.8047, IoU=0.6733, F 
Train - Loss: 0.4513, Dice: 0.8837, IoU: 0.7957, F1: 0.8837
Val   - Loss: 1.6885, Dice: 0.6328, IoU: 0.4715, F1: 0.6328
LR: 0.000054

🔥 Epoch 17/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.5961, Dice=0.8305, IoU=0.7102, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.46it/s, Loss=0.7695, Dice=0.8284, IoU=0.7070, F 
Train - Loss: 0.4535, Dice: 0.8808, IoU: 0.7920, F1: 0.8808
Val   - Loss: 1.7202, Dice: 0.6428, IoU: 0.4831, F1: 0.6428
LR: 0.000056
✅ New best model saved! Val Dice: 0.6428

🔥 Epoch 18/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.6769, Dice=0.7998, IoU=0.6664, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.44it/s, Loss=0.7720, Dice=0.8098, IoU=0.6804, F 
Train - Loss: 0.4096, Dice: 0.8932, IoU: 0.8136, F1: 0.8932
Val   - Loss: 1.6838, Dice: 0.6390, IoU: 0.4777, F1: 0.6390
LR: 0.000057

🔥 Epoch 19/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.2758, Dice=0.9394, IoU=0.8856, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.46it/s, Loss=3.4769, Dice=0.0842, IoU=0.0440, F 
Train - Loss: 0.3965, Dice: 0.8969, IoU: 0.8224, F1: 0.8969
Val   - Loss: 2.3524, Dice: 0.4043, IoU: 0.2709, F1: 0.4043
LR: 0.000059

🔥 Epoch 20/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.2071, Dice=0.9542, IoU=0.9123, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.40it/s, Loss=0.7824, Dice=0.8228, IoU=0.6989, F 
Train - Loss: 0.3168, Dice: 0.9191, IoU: 0.8545, F1: 0.9190
Val   - Loss: 1.5069, Dice: 0.6556, IoU: 0.4955, F1: 0.6556
LR: 0.000062
✅ New best model saved! Val Dice: 0.6556

🔥 Epoch 21/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=1.3349, Dice=0.5999, IoU=0.4285, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.32it/s, Loss=0.7619, Dice=0.8157, IoU=0.6887, F 
Train - Loss: 0.3589, Dice: 0.9067, IoU: 0.8373, F1: 0.9067
Val   - Loss: 1.7134, Dice: 0.6424, IoU: 0.4820, F1: 0.6424
LR: 0.000064

🔥 Epoch 22/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.44it/s, Loss=0.9743, Dice=0.7086, IoU=0.5486, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=0.8760, Dice=0.7948, IoU=0.6595, F 
Train - Loss: 0.3190, Dice: 0.9190, IoU: 0.8552, F1: 0.9190
Val   - Loss: 1.7391, Dice: 0.6417, IoU: 0.4794, F1: 0.6417
LR: 0.000066

🔥 Epoch 23/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.44it/s, Loss=0.2611, Dice=0.9379, IoU=0.8830, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.30it/s, Loss=0.8602, Dice=0.8159, IoU=0.6890, F 
Train - Loss: 0.2629, Dice: 0.9350, IoU: 0.8798, F1: 0.9350
Val   - Loss: 1.7249, Dice: 0.6511, IoU: 0.4906, F1: 0.6511
LR: 0.000068

🔥 Epoch 24/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.4053, Dice=0.9088, IoU=0.8329, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.28it/s, Loss=1.4020, Dice=0.5686, IoU=0.3972, F 
Train - Loss: 0.2554, Dice: 0.9363, IoU: 0.8828, F1: 0.9363
Val   - Loss: 1.4379, Dice: 0.5887, IoU: 0.4213, F1: 0.5887
LR: 0.000071

🔥 Epoch 25/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.9597, Dice=0.7625, IoU=0.6162, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.30it/s, Loss=3.8780, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.2796, Dice: 0.9307, IoU: 0.8751, F1: 0.9307
Val   - Loss: 2.6255, Dice: 0.3404, IoU: 0.2232, F1: 0.3404
LR: 0.000073

🔥 Epoch 26/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.1236, Dice=0.9716, IoU=0.9448, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.35it/s, Loss=2.0891, Dice=0.4299, IoU=0.2738, F 
Train - Loss: 0.2042, Dice: 0.9496, IoU: 0.9054, F1: 0.9496
Val   - Loss: 1.9678, Dice: 0.5269, IoU: 0.3608, F1: 0.5269
LR: 0.000076

🔥 Epoch 27/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.3756, Dice=0.8998, IoU=0.8178, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.37it/s, Loss=2.4272, Dice=0.3603, IoU=0.2197, F 
Train - Loss: 0.1916, Dice: 0.9530, IoU: 0.9112, F1: 0.9530
Val   - Loss: 2.1576, Dice: 0.4842, IoU: 0.3248, F1: 0.4842
LR: 0.000079

🔥 Epoch 28/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=1.3042, Dice=0.6091, IoU=0.4379, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.38it/s, Loss=2.3917, Dice=0.3721, IoU=0.2286, F 
Train - Loss: 0.2536, Dice: 0.9351, IoU: 0.8901, F1: 0.9351
Val   - Loss: 1.8304, Dice: 0.5220, IoU: 0.3615, F1: 0.5220
LR: 0.000082

🔥 Epoch 29/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.1129, Dice=0.9746, IoU=0.9504, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.33it/s, Loss=1.0527, Dice=0.7511, IoU=0.6015, F 
Train - Loss: 0.2026, Dice: 0.9485, IoU: 0.9052, F1: 0.9485
Val   - Loss: 1.3684, Dice: 0.6622, IoU: 0.4981, F1: 0.6622
LR: 0.000085
✅ New best model saved! Val Dice: 0.6622

🔥 Epoch 30/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=1.1327, Dice=0.6666, IoU=0.4999, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.41it/s, Loss=4.1497, Dice=0.0037, IoU=0.0018, F 
Train - Loss: 0.2118, Dice: 0.9458, IoU: 0.9032, F1: 0.9458
Val   - Loss: 2.1455, Dice: 0.4737, IoU: 0.3443, F1: 0.4737
LR: 0.000088

🔥 Epoch 31/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=0.1388, Dice=0.9690, IoU=0.9398, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.53it/s, Loss=0.7299, Dice=0.8509, IoU=0.7404, F 
Train - Loss: 0.1455, Dice: 0.9653, IoU: 0.9333, F1: 0.9653
Val   - Loss: 1.6459, Dice: 0.5838, IoU: 0.4439, F1: 0.5838
LR: 0.000091

🔥 Epoch 32/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=0.1040, Dice=0.9751, IoU=0.9514, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.38it/s, Loss=2.5697, Dice=0.1802, IoU=0.0990, F 
Train - Loss: 0.1585, Dice: 0.9602, IoU: 0.9256, F1: 0.9602
Val   - Loss: 1.8232, Dice: 0.5396, IoU: 0.3952, F1: 0.5396
LR: 0.000094

🔥 Epoch 33/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=0.3865, Dice=0.8912, IoU=0.8038, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.47it/s, Loss=3.4129, Dice=0.0315, IoU=0.0160, F 
Train - Loss: 0.1873, Dice: 0.9516, IoU: 0.9097, F1: 0.9516
Val   - Loss: 2.2368, Dice: 0.3973, IoU: 0.2704, F1: 0.3973
LR: 0.000098

🔥 Epoch 34/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.2639, Dice=0.9391, IoU=0.8853, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.30it/s, Loss=0.9621, Dice=0.7773, IoU=0.6357, F 
Train - Loss: 0.1737, Dice: 0.9551, IoU: 0.9168, F1: 0.9551
Val   - Loss: 1.6635, Dice: 0.6353, IoU: 0.4710, F1: 0.6353
LR: 0.000101

🔥 Epoch 35/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.45it/s, Loss=0.1150, Dice=0.9750, IoU=0.9511, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.25it/s, Loss=3.5190, Dice=0.0112, IoU=0.0056, F 
Train - Loss: 0.1536, Dice: 0.9613, IoU: 0.9275, F1: 0.9613
Val   - Loss: 2.1003, Dice: 0.3741, IoU: 0.2611, F1: 0.3741
LR: 0.000105

🔥 Epoch 36/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.44it/s, Loss=0.9682, Dice=0.7223, IoU=0.5653, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.20it/s, Loss=0.7482, Dice=0.8367, IoU=0.7193, F 
Train - Loss: 0.1759, Dice: 0.9538, IoU: 0.9167, F1: 0.9538
Val   - Loss: 1.4925, Dice: 0.6582, IoU: 0.4989, F1: 0.6582
LR: 0.000109

🔥 Epoch 37/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=0.1246, Dice=0.9713, IoU=0.9442, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.37it/s, Loss=0.9545, Dice=0.7600, IoU=0.6129, F 
Train - Loss: 0.1122, Dice: 0.9721, IoU: 0.9462, F1: 0.9721
Val   - Loss: 1.3361, Dice: 0.6573, IoU: 0.4931, F1: 0.6573
LR: 0.000112

🔥 Epoch 38/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=0.0564, Dice=0.9874, IoU=0.9752, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.20it/s, Loss=1.0668, Dice=0.7856, IoU=0.6469, F 
Train - Loss: 0.0994, Dice: 0.9751, IoU: 0.9516, F1: 0.9751
Val   - Loss: 1.7062, Dice: 0.6416, IoU: 0.4778, F1: 0.6416
LR: 0.000116

🔥 Epoch 39/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.1207, Dice=0.9721, IoU=0.9457, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.22it/s, Loss=1.2588, Dice=0.7119, IoU=0.5527, F 
Train - Loss: 0.1336, Dice: 0.9666, IoU: 0.9363, F1: 0.9666
Val   - Loss: 1.6648, Dice: 0.6577, IoU: 0.4921, F1: 0.6577
LR: 0.000120

🔥 Epoch 40/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.1573, Dice=0.9654, IoU=0.9331, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.35it/s, Loss=0.4728, Dice=0.8915, IoU=0.8042, F 
Train - Loss: 0.1792, Dice: 0.9545, IoU: 0.9193, F1: 0.9545
Val   - Loss: 1.1887, Dice: 0.7407, IoU: 0.6018, F1: 0.7407
LR: 0.000124
✅ New best model saved! Val Dice: 0.7407

🔥 Epoch 41/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.0477, Dice=0.9897, IoU=0.9795, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.36it/s, Loss=1.4165, Dice=0.5708, IoU=0.3994, F 
Train - Loss: 0.1129, Dice: 0.9723, IoU: 0.9472, F1: 0.9723
Val   - Loss: 1.2227, Dice: 0.6982, IoU: 0.5463, F1: 0.6982
LR: 0.000128

🔥 Epoch 42/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0891, Dice=0.9791, IoU=0.9590, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.37it/s, Loss=0.6797, Dice=0.8415, IoU=0.7264, F 
Train - Loss: 0.1076, Dice: 0.9726, IoU: 0.9481, F1: 0.9726
Val   - Loss: 1.0577, Dice: 0.7455, IoU: 0.5978, F1: 0.7455
LR: 0.000133
✅ New best model saved! Val Dice: 0.7455

🔥 Epoch 43/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.55it/s, Loss=0.0900, Dice=0.9807, IoU=0.9622, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.45it/s, Loss=0.6723, Dice=0.8417, IoU=0.7267, F 
Train - Loss: 0.0985, Dice: 0.9747, IoU: 0.9515, F1: 0.9747
Val   - Loss: 0.7490, Dice: 0.8621, IoU: 0.7626, F1: 0.8621
LR: 0.000137
✅ New best model saved! Val Dice: 0.8621

🔥 Epoch 44/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=0.0941, Dice=0.9741, IoU=0.9496, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.21it/s, Loss=0.4766, Dice=0.9197, IoU=0.8513, F 
Train - Loss: 0.1239, Dice: 0.9687, IoU: 0.9404, F1: 0.9687
Val   - Loss: 0.7661, Dice: 0.8276, IoU: 0.7093, F1: 0.8276
LR: 0.000141

🔥 Epoch 45/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.3393, Dice=0.9268, IoU=0.8636, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.39it/s, Loss=0.9259, Dice=0.7869, IoU=0.6487, F 
Train - Loss: 0.1090, Dice: 0.9740, IoU: 0.9498, F1: 0.9740
Val   - Loss: 0.8406, Dice: 0.8167, IoU: 0.6994, F1: 0.8167
LR: 0.000146

🔥 Epoch 46/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.1518, Dice=0.9660, IoU=0.9342, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.26it/s, Loss=0.4492, Dice=0.8913, IoU=0.8039, F 
Train - Loss: 0.0997, Dice: 0.9750, IoU: 0.9520, F1: 0.9750
Val   - Loss: 0.9049, Dice: 0.7966, IoU: 0.6663, F1: 0.7966
LR: 0.000150

🔥 Epoch 47/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.48it/s, Loss=0.0960, Dice=0.9741, IoU=0.9496, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=3.0080, Dice=0.1509, IoU=0.0816, F 
Train - Loss: 0.0955, Dice: 0.9754, IoU: 0.9528, F1: 0.9754
Val   - Loss: 1.4412, Dice: 0.6327, IoU: 0.5052, F1: 0.6327
LR: 0.000155

🔥 Epoch 48/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.0590, Dice=0.9847, IoU=0.9698, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.32it/s, Loss=2.4577, Dice=0.2283, IoU=0.1288, F 
Train - Loss: 0.1135, Dice: 0.9721, IoU: 0.9467, F1: 0.9721
Val   - Loss: 1.7893, Dice: 0.5105, IoU: 0.3785, F1: 0.5105
LR: 0.000160

🔥 Epoch 49/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.0385, Dice=0.9913, IoU=0.9827, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.33it/s, Loss=0.8257, Dice=0.8158, IoU=0.6889, F 
Train - Loss: 0.1409, Dice: 0.9647, IoU: 0.9363, F1: 0.9647
Val   - Loss: 0.8599, Dice: 0.8051, IoU: 0.6950, F1: 0.8051
LR: 0.000164

🔥 Epoch 50/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0820, Dice=0.9788, IoU=0.9585, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=0.3857, Dice=0.9170, IoU=0.8467, F 
Train - Loss: 0.0871, Dice: 0.9779, IoU: 0.9572, F1: 0.9779
Val   - Loss: 0.5925, Dice: 0.8738, IoU: 0.7834, F1: 0.8738
LR: 0.000169
✅ New best model saved! Val Dice: 0.8738

🔥 Epoch 51/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.7941, Dice=0.7685, IoU=0.6240, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=1.3044, Dice=0.6584, IoU=0.4907, F 
Train - Loss: 0.1079, Dice: 0.9715, IoU: 0.9479, F1: 0.9715
Val   - Loss: 0.9722, Dice: 0.7621, IoU: 0.6484, F1: 0.7621
LR: 0.000174

🔥 Epoch 52/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=0.0507, Dice=0.9887, IoU=0.9776, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.42it/s, Loss=2.2086, Dice=0.2985, IoU=0.1754, F 
Train - Loss: 0.1016, Dice: 0.9743, IoU: 0.9515, F1: 0.9743
Val   - Loss: 1.4924, Dice: 0.5969, IoU: 0.4544, F1: 0.5969
LR: 0.000179

🔥 Epoch 53/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=1.2440, Dice=0.6388, IoU=0.4693, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.18it/s, Loss=1.2330, Dice=0.6691, IoU=0.5027, F 
Train - Loss: 0.1320, Dice: 0.9645, IoU: 0.9393, F1: 0.9645
Val   - Loss: 1.6870, Dice: 0.5392, IoU: 0.4089, F1: 0.5392
LR: 0.000184

🔥 Epoch 54/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.0554, Dice=0.9870, IoU=0.9743, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.35it/s, Loss=0.1583, Dice=0.9630, IoU=0.9286, F 
Train - Loss: 0.0705, Dice: 0.9824, IoU: 0.9658, F1: 0.9824
Val   - Loss: 0.4580, Dice: 0.9162, IoU: 0.8552, F1: 0.9162
LR: 0.000190
✅ New best model saved! Val Dice: 0.9162

🔥 Epoch 55/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=0.1027, Dice=0.9770, IoU=0.9550, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.26it/s, Loss=0.1095, Dice=0.9727, IoU=0.9469, F 
Train - Loss: 0.0709, Dice: 0.9826, IoU: 0.9659, F1: 0.9826
Val   - Loss: 0.4177, Dice: 0.9224, IoU: 0.8642, F1: 0.9224
LR: 0.000195
✅ New best model saved! Val Dice: 0.9224

🔥 Epoch 56/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.0310, Dice=0.9919, IoU=0.9839, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.39it/s, Loss=0.9778, Dice=0.7523, IoU=0.6029, F 
Train - Loss: 0.0670, Dice: 0.9838, IoU: 0.9683, F1: 0.9838
Val   - Loss: 1.5437, Dice: 0.5899, IoU: 0.4526, F1: 0.5899
LR: 0.000200

🔥 Epoch 57/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0552, Dice=0.9869, IoU=0.9741, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.41it/s, Loss=2.9916, Dice=0.0702, IoU=0.0364, F 
Train - Loss: 0.0553, Dice: 0.9863, IoU: 0.9731, F1: 0.9863
Val   - Loss: 2.1252, Dice: 0.4171, IoU: 0.3144, F1: 0.4171
LR: 0.000206

🔥 Epoch 58/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.0337, Dice=0.9922, IoU=0.9845, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.40it/s, Loss=2.9287, Dice=0.0722, IoU=0.0375, F 
Train - Loss: 0.0792, Dice: 0.9800, IoU: 0.9620, F1: 0.9800
Val   - Loss: 2.2158, Dice: 0.3994, IoU: 0.2934, F1: 0.3994
LR: 0.000211

🔥 Epoch 59/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=0.6447, Dice=0.8097, IoU=0.6802, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.43it/s, Loss=0.1332, Dice=0.9688, IoU=0.9395, F 
Train - Loss: 0.0902, Dice: 0.9759, IoU: 0.9554, F1: 0.9759
Val   - Loss: 0.4995, Dice: 0.9155, IoU: 0.8489, F1: 0.9155
LR: 0.000217

🔥 Epoch 60/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.54it/s, Loss=0.0523, Dice=0.9866, IoU=0.9736, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.39it/s, Loss=0.1818, Dice=0.9566, IoU=0.9169, F 
Train - Loss: 0.0602, Dice: 0.9850, IoU: 0.9707, F1: 0.9850
Val   - Loss: 0.8234, Dice: 0.8149, IoU: 0.7047, F1: 0.8149
LR: 0.000222

🔥 Epoch 61/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.4502, Dice=0.8879, IoU=0.7984, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.42it/s, Loss=1.7818, Dice=0.4980, IoU=0.3316, F 
Train - Loss: 0.0756, Dice: 0.9813, IoU: 0.9641, F1: 0.9813
Val   - Loss: 1.2082, Dice: 0.6991, IoU: 0.5485, F1: 0.6991
LR: 0.000228

🔥 Epoch 62/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.0276, Dice=0.9934, IoU=0.9869, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.39it/s, Loss=4.4004, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0527, Dice: 0.9868, IoU: 0.9742, F1: 0.9868
Val   - Loss: 2.8520, Dice: 0.3613, IoU: 0.2677, F1: 0.3613
LR: 0.000234

🔥 Epoch 63/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.45it/s, Loss=0.0418, Dice=0.9904, IoU=0.9811, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.25it/s, Loss=1.0513, Dice=0.7273, IoU=0.5715, F 
Train - Loss: 0.0531, Dice: 0.9866, IoU: 0.9736, F1: 0.9866
Val   - Loss: 1.7011, Dice: 0.5824, IoU: 0.4490, F1: 0.5824
LR: 0.000240

🔥 Epoch 64/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.48it/s, Loss=0.2018, Dice=0.9547, IoU=0.9133, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.29it/s, Loss=3.7966, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0677, Dice: 0.9837, IoU: 0.9683, F1: 0.9837
Val   - Loss: 2.4615, Dice: 0.3764, IoU: 0.2834, F1: 0.3764
LR: 0.000246

🔥 Epoch 65/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=0.0663, Dice=0.9810, IoU=0.9627, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.30it/s, Loss=0.1544, Dice=0.9675, IoU=0.9371, F 
Train - Loss: 0.0478, Dice: 0.9880, IoU: 0.9763, F1: 0.9880
Val   - Loss: 0.3727, Dice: 0.9304, IoU: 0.8746, F1: 0.9304
LR: 0.000251
✅ New best model saved! Val Dice: 0.9304

🔥 Epoch 66/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.6816, Dice=0.8102, IoU=0.6810, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=2.0218, Dice=0.4109, IoU=0.2586, F 
Train - Loss: 0.1116, Dice: 0.9705, IoU: 0.9472, F1: 0.9705
Val   - Loss: 1.7993, Dice: 0.5333, IoU: 0.4058, F1: 0.5333
LR: 0.000257

🔥 Epoch 67/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0352, Dice=0.9911, IoU=0.9825, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.47it/s, Loss=0.4754, Dice=0.9058, IoU=0.8278, F 
Train - Loss: 0.0626, Dice: 0.9842, IoU: 0.9691, F1: 0.9842
Val   - Loss: 0.8605, Dice: 0.8523, IoU: 0.7661, F1: 0.8523
LR: 0.000264

🔥 Epoch 68/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0396, Dice=0.9906, IoU=0.9814, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.38it/s, Loss=4.2908, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0557, Dice: 0.9865, IoU: 0.9735, F1: 0.9865
Val   - Loss: 2.7059, Dice: 0.3698, IoU: 0.2770, F1: 0.3698
LR: 0.000270

🔥 Epoch 69/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.0447, Dice=0.9896, IoU=0.9794, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.45it/s, Loss=4.2366, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0541, Dice: 0.9862, IoU: 0.9729, F1: 0.9862
Val   - Loss: 2.6185, Dice: 0.3717, IoU: 0.2765, F1: 0.3717
LR: 0.000276

🔥 Epoch 70/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=0.0346, Dice=0.9910, IoU=0.9822, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.50it/s, Loss=0.4696, Dice=0.8901, IoU=0.8020, F 
Train - Loss: 0.0609, Dice: 0.9850, IoU: 0.9706, F1: 0.9850
Val   - Loss: 1.2212, Dice: 0.6954, IoU: 0.5808, F1: 0.6954
LR: 0.000282

🔥 Epoch 71/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.1290, Dice=0.9712, IoU=0.9440, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.30it/s, Loss=0.8940, Dice=0.8001, IoU=0.6668, F 
Train - Loss: 0.0620, Dice: 0.9841, IoU: 0.9692, F1: 0.9841
Val   - Loss: 1.6405, Dice: 0.5830, IoU: 0.4664, F1: 0.5830
LR: 0.000288

🔥 Epoch 72/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.0723, Dice=0.9795, IoU=0.9598, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.28it/s, Loss=0.1053, Dice=0.9755, IoU=0.9521, F 
Train - Loss: 0.0462, Dice: 0.9881, IoU: 0.9765, F1: 0.9881
Val   - Loss: 0.3368, Dice: 0.9391, IoU: 0.8897, F1: 0.9391
LR: 0.000295
🎉 V5 EXCEEDED! Val Dice: 0.9391 > 0.9341
🏆 V6 SURPASSES V5 PERFORMANCE!
🚀 NEW V6 RECORD! Val Dice: 0.9391

🔥 Epoch 73/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.0677, Dice=0.9861, IoU=0.9725, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.26it/s, Loss=0.6860, Dice=0.8400, IoU=0.7241, F 
Train - Loss: 0.0888, Dice: 0.9787, IoU: 0.9611, F1: 0.9787
Val   - Loss: 0.8922, Dice: 0.7914, IoU: 0.6969, F1: 0.7914
LR: 0.000301

🔥 Epoch 74/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.47it/s, Loss=0.0299, Dice=0.9932, IoU=0.9865, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.33it/s, Loss=0.1031, Dice=0.9763, IoU=0.9538, F 
Train - Loss: 0.0446, Dice: 0.9885, IoU: 0.9773, F1: 0.9885
Val   - Loss: 0.4495, Dice: 0.9309, IoU: 0.8753, F1: 0.9309
LR: 0.000308

🔥 Epoch 75/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.1142, Dice=0.9757, IoU=0.9526, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.35it/s, Loss=0.1734, Dice=0.9577, IoU=0.9189, F 
Train - Loss: 0.0444, Dice: 0.9890, IoU: 0.9783, F1: 0.9890
Val   - Loss: 0.4627, Dice: 0.9101, IoU: 0.8457, F1: 0.9101
LR: 0.000314

🔥 Epoch 76/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.47it/s, Loss=0.0519, Dice=0.9872, IoU=0.9747, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.25it/s, Loss=0.1254, Dice=0.9718, IoU=0.9451, F 
Train - Loss: 0.0455, Dice: 0.9887, IoU: 0.9777, F1: 0.9887
Val   - Loss: 0.4950, Dice: 0.8988, IoU: 0.8345, F1: 0.8988
LR: 0.000321

🔥 Epoch 77/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.48it/s, Loss=0.0213, Dice=0.9945, IoU=0.9891, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.32it/s, Loss=0.1352, Dice=0.9646, IoU=0.9317, F 
Train - Loss: 0.0451, Dice: 0.9886, IoU: 0.9775, F1: 0.9886
Val   - Loss: 0.4825, Dice: 0.9105, IoU: 0.8470, F1: 0.9105
LR: 0.000327

🔥 Epoch 78/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0202, Dice=0.9948, IoU=0.9897, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.40it/s, Loss=0.0983, Dice=0.9744, IoU=0.9502, F 
Train - Loss: 0.0425, Dice: 0.9895, IoU: 0.9793, F1: 0.9895
Val   - Loss: 0.4485, Dice: 0.9199, IoU: 0.8538, F1: 0.9199
LR: 0.000334

🔥 Epoch 79/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.0408, Dice=0.9907, IoU=0.9816, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.38it/s, Loss=0.1086, Dice=0.9710, IoU=0.9436, F 
Train - Loss: 0.0482, Dice: 0.9871, IoU: 0.9752, F1: 0.9871
Val   - Loss: 0.5255, Dice: 0.8746, IoU: 0.7818, F1: 0.8746
LR: 0.000340

🔥 Epoch 80/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=0.1629, Dice=0.9690, IoU=0.9399, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.29it/s, Loss=3.8686, Dice=0.0114, IoU=0.0057, F 
Train - Loss: 0.0539, Dice: 0.9871, IoU: 0.9747, F1: 0.9871
Val   - Loss: 2.4586, Dice: 0.3948, IoU: 0.2911, F1: 0.3948
LR: 0.000347

🔥 Epoch 81/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.47it/s, Loss=0.0264, Dice=0.9930, IoU=0.9861, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=0.2582, Dice=0.9413, IoU=0.8891, F 
Train - Loss: 0.0389, Dice: 0.9904, IoU: 0.9811, F1: 0.9904
Val   - Loss: 0.6004, Dice: 0.8782, IoU: 0.7904, F1: 0.8782
LR: 0.000354

🔥 Epoch 82/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:09<00:00,  2.32it/s, Loss=0.0193, Dice=0.9956, IoU=0.9912, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  6.56it/s, Loss=0.1913, Dice=0.9549, IoU=0.9136, F 
Train - Loss: 0.0536, Dice: 0.9865, IoU: 0.9738, F1: 0.9865
Val   - Loss: 0.5710, Dice: 0.8917, IoU: 0.8143, F1: 0.8917
LR: 0.000361

🔥 Epoch 83/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:09<00:00,  2.28it/s, Loss=0.0210, Dice=0.9944, IoU=0.9889, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  6.69it/s, Loss=1.4403, Dice=0.6560, IoU=0.4881, F 
Train - Loss: 0.0593, Dice: 0.9850, IoU: 0.9716, F1: 0.9850
Val   - Loss: 1.1696, Dice: 0.7362, IoU: 0.6176, F1: 0.7362
LR: 0.000368

🔥 Epoch 84/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:09<00:00,  2.21it/s, Loss=0.3501, Dice=0.9220, IoU=0.8553, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  6.16it/s, Loss=4.4425, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0681, Dice: 0.9827, IoU: 0.9671, F1: 0.9827
Val   - Loss: 2.6282, Dice: 0.3881, IoU: 0.2851, F1: 0.3881
LR: 0.000374

🔥 Epoch 85/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:09<00:00,  2.22it/s, Loss=0.0317, Dice=0.9931, IoU=0.9863, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  6.58it/s, Loss=3.6859, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0352, Dice: 0.9910, IoU: 0.9822, F1: 0.9910
Val   - Loss: 2.1632, Dice: 0.4305, IoU: 0.3306, F1: 0.4305
LR: 0.000381

🔥 Epoch 86/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.44it/s, Loss=0.5806, Dice=0.8285, IoU=0.7071, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.52it/s, Loss=3.8103, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0734, Dice: 0.9803, IoU: 0.9637, F1: 0.9803
Val   - Loss: 2.3501, Dice: 0.3842, IoU: 0.2875, F1: 0.3842
LR: 0.000388

🔥 Epoch 87/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0141, Dice=0.9969, IoU=0.9937, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.29it/s, Loss=2.2140, Dice=0.4789, IoU=0.3148, F 
Train - Loss: 0.0393, Dice: 0.9901, IoU: 0.9805, F1: 0.9901
Val   - Loss: 1.5460, Dice: 0.6435, IoU: 0.5149, F1: 0.6435
LR: 0.000395

🔥 Epoch 88/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.0352, Dice=0.9915, IoU=0.9831, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.21it/s, Loss=0.6563, Dice=0.8586, IoU=0.7523, F 
Train - Loss: 0.0331, Dice: 0.9916, IoU: 0.9833, F1: 0.9916
Val   - Loss: 0.9583, Dice: 0.8038, IoU: 0.6886, F1: 0.8038
LR: 0.000402

🔥 Epoch 89/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.0216, Dice=0.9948, IoU=0.9896, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.27it/s, Loss=3.8471, Dice=0.3397, IoU=0.2046, F 
Train - Loss: 0.0631, Dice: 0.9842, IoU: 0.9708, F1: 0.9842
Val   - Loss: 2.3068, Dice: 0.5717, IoU: 0.4541, F1: 0.5717
LR: 0.000409

🔥 Epoch 90/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.0268, Dice=0.9927, IoU=0.9855, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.29it/s, Loss=3.7213, Dice=0.1275, IoU=0.0681, F 
Train - Loss: 0.0420, Dice: 0.9892, IoU: 0.9787, F1: 0.9892
Val   - Loss: 1.7632, Dice: 0.6028, IoU: 0.4910, F1: 0.6028
LR: 0.000416

🔥 Epoch 91/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.47it/s, Loss=0.0199, Dice=0.9955, IoU=0.9911, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.41it/s, Loss=4.4291, Dice=0.0000, IoU=0.0000, F 
Train - Loss: 0.0479, Dice: 0.9871, IoU: 0.9753, F1: 0.9871
Val   - Loss: 2.5689, Dice: 0.3930, IoU: 0.2906, F1: 0.3930
LR: 0.000423

🔥 Epoch 92/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.49it/s, Loss=0.0628, Dice=0.9853, IoU=0.9711, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.39it/s, Loss=4.7483, Dice=0.1705, IoU=0.0932, F 
Train - Loss: 0.0306, Dice: 0.9924, IoU: 0.9850, F1: 0.9924
Val   - Loss: 2.3715, Dice: 0.5134, IoU: 0.4027, F1: 0.5134
LR: 0.000430

🔥 Epoch 93/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.53it/s, Loss=0.0197, Dice=0.9954, IoU=0.9908, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.33it/s, Loss=2.2501, Dice=0.5219, IoU=0.3531, F 
Train - Loss: 0.0290, Dice: 0.9925, IoU: 0.9851, F1: 0.9925
Val   - Loss: 1.7045, Dice: 0.6686, IoU: 0.5476, F1: 0.6686
LR: 0.000437

🔥 Epoch 94/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.0180, Dice=0.9958, IoU=0.9916, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.38it/s, Loss=1.4818, Dice=0.6834, IoU=0.5191, F 
Train - Loss: 0.0284, Dice: 0.9929, IoU: 0.9860, F1: 0.9929
Val   - Loss: 1.0696, Dice: 0.7891, IoU: 0.6849, F1: 0.7891
LR: 0.000445

🔥 Epoch 95/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.0242, Dice=0.9943, IoU=0.9887, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.48it/s, Loss=1.6926, Dice=0.6405, IoU=0.4711, F 
Train - Loss: 0.0267, Dice: 0.9931, IoU: 0.9864, F1: 0.9931
Val   - Loss: 1.1014, Dice: 0.7649, IoU: 0.6564, F1: 0.7649
LR: 0.000452

🔥 Epoch 96/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.52it/s, Loss=0.0149, Dice=0.9966, IoU=0.9933, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.41it/s, Loss=0.5652, Dice=0.8919, IoU=0.8049, F 
Train - Loss: 0.0430, Dice: 0.9888, IoU: 0.9788, F1: 0.9888
Val   - Loss: 0.7920, Dice: 0.8374, IoU: 0.7321, F1: 0.8374
LR: 0.000459

🔥 Epoch 97/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.50it/s, Loss=0.0432, Dice=0.9902, IoU=0.9805, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.36it/s, Loss=0.0743, Dice=0.9838, IoU=0.9682, F 
Train - Loss: 0.0275, Dice: 0.9929, IoU: 0.9858, F1: 0.9929
Val   - Loss: 0.3362, Dice: 0.9341, IoU: 0.8829, F1: 0.9341
LR: 0.000466

🔥 Epoch 98/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.51it/s, Loss=0.1887, Dice=0.9529, IoU=0.9101, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.31it/s, Loss=0.2128, Dice=0.9519, IoU=0.9082, F 
Train - Loss: 0.0335, Dice: 0.9915, IoU: 0.9834, F1: 0.9915
Val   - Loss: 0.2983, Dice: 0.9374, IoU: 0.8877, F1: 0.9374
LR: 0.000473

🔥 Epoch 99/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.46it/s, Loss=0.0527, Dice=0.9851, IoU=0.9707, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.28it/s, Loss=0.1192, Dice=0.9744, IoU=0.9500, F 
Train - Loss: 0.0268, Dice: 0.9931, IoU: 0.9863, F1: 0.9931
Val   - Loss: 0.3859, Dice: 0.9349, IoU: 0.8871, F1: 0.9349
LR: 0.000480

🔥 Epoch 100/100
🚀 V6 Breakthrough Training: 100%|█| 21/21 [00:08<00:00,  2.47it/s, Loss=0.0248, Dice=0.9937, IoU=0.9875, F 
🎯 V6 Breakthrough Validation: 100%|█| 6/6 [00:00<00:00,  7.29it/s, Loss=0.0647, Dice=0.9854, IoU=0.9712, F 
Train - Loss: 0.0239, Dice: 0.9939, IoU: 0.9879, F1: 0.9939
Val   - Loss: 0.4058, Dice: 0.9415, IoU: 0.8969, F1: 0.9414
LR: 0.000488
🚀 NEW V6 RECORD! Val Dice: 0.9415

🎉 V6 Training completed in 0.26 hours
🏆 Best validation Dice: 0.9415

================================================================================
🚀 BREAKTHROUGH U-NET V6 TRAINING COMPLETED!
================================================================================
🚀 V5 EXCEEDED! V6 SURPASSES V5!
🏆 V6: 0.9415 > V5: 0.9341
🎯 Improvement: +0.79%

📊 FINAL V5 vs V6 COMPARISON:
V5: 0.9341 Dice (4.3M params)
V6: 0.9415 Dice (4.6M params)
Improvement: +0.79%

🚀 SUCCESS: V6 exceeds V5 performance!
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 