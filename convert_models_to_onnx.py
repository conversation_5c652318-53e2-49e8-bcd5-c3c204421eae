"""
Convert PyTorch Models to ONNX for Android
This script converts our trained V6 and YOLO models to ONNX format
so they can be used in Kotlin with EXACT same accuracy as Python.
"""

import torch
import torch.onnx
import onnx
import onnxruntime as ort
import numpy as np
from ultralytics import YOLO
import sys
import os

def convert_v6_to_onnx(pytorch_model_path, onnx_output_path):
    """Convert V6 segmentation model to ONNX."""
    print(f"🔄 Converting V6 model: {pytorch_model_path} -> {onnx_output_path}")

    try:
        # Load the V6 model
        import sys
        sys.path.append('chess_board_detection/models')
        from breakthrough_unet_v6_simple import get_breakthrough_v6_model

        model = get_breakthrough_v6_model(base_channels=32, n_channels=3, n_classes=1)

        # Load trained weights
        checkpoint = torch.load(pytorch_model_path, map_location='cpu')
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)

        model.eval()

        # Create dummy input
        dummy_input = torch.randn(1, 3, 512, 512)

        # Export to ONNX
        torch.onnx.export(
            model,
            dummy_input,
            onnx_output_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )

        # Verify the ONNX model
        onnx_model = onnx.load(onnx_output_path)
        onnx.checker.check_model(onnx_model)

        # Test with ONNX Runtime
        ort_session = ort.InferenceSession(onnx_output_path)
        ort_inputs = {ort_session.get_inputs()[0].name: dummy_input.numpy()}
        ort_outputs = ort_session.run(None, ort_inputs)

        print(f"✅ V6 model converted successfully!")
        print(f"📊 Input shape: {dummy_input.shape}")
        print(f"📊 Output shape: {ort_outputs[0].shape}")
        print(f"💾 Model size: {os.path.getsize(onnx_output_path) / (1024*1024):.2f} MB")

        return True

    except Exception as e:
        print(f"❌ Error converting V6 model: {e}")
        return False

def convert_yolo_to_onnx(pytorch_model_path, onnx_output_path):
    """Convert YOLO model to ONNX."""
    print(f"🔄 Converting YOLO model: {pytorch_model_path} -> {onnx_output_path}")

    try:
        # Load YOLO model
        model = YOLO(pytorch_model_path)

        # Export to ONNX
        model.export(
            format='onnx',
            imgsz=416,
            optimize=True,
            half=False,  # Use FP32 for better compatibility
            dynamic=False,
            simplify=True
        )

        # Move the exported file to our desired location
        exported_path = pytorch_model_path.replace('.pt', '.onnx')
        if os.path.exists(exported_path):
            os.rename(exported_path, onnx_output_path)

        # Verify the ONNX model
        onnx_model = onnx.load(onnx_output_path)
        onnx.checker.check_model(onnx_model)

        # Test with ONNX Runtime
        ort_session = ort.InferenceSession(onnx_output_path)
        dummy_input = np.random.randn(1, 3, 416, 416).astype(np.float32)
        ort_inputs = {ort_session.get_inputs()[0].name: dummy_input}
        ort_outputs = ort_session.run(None, ort_inputs)

        print(f"✅ YOLO model converted successfully!")
        print(f"📊 Input shape: {dummy_input.shape}")
        print(f"📊 Output shape: {[out.shape for out in ort_outputs]}")
        print(f"💾 Model size: {os.path.getsize(onnx_output_path) / (1024*1024):.2f} MB")

        return True

    except Exception as e:
        print(f"❌ Error converting YOLO model: {e}")
        return False

def test_onnx_models(v6_onnx_path, yolo_onnx_path):
    """Test the converted ONNX models."""
    print("\n🧪 Testing converted ONNX models...")

    try:
        # Test V6 model
        print("Testing V6 segmentation model...")
        v6_session = ort.InferenceSession(v6_onnx_path)
        v6_input = np.random.randn(1, 3, 512, 512).astype(np.float32)
        v6_output = v6_session.run(None, {v6_session.get_inputs()[0].name: v6_input})
        print(f"✅ V6 test passed - Output shape: {v6_output[0].shape}")

        # Test YOLO model
        print("Testing YOLO detection model...")
        yolo_session = ort.InferenceSession(yolo_onnx_path)
        yolo_input = np.random.randn(1, 3, 416, 416).astype(np.float32)
        yolo_output = yolo_session.run(None, {yolo_session.get_inputs()[0].name: yolo_input})
        print(f"✅ YOLO test passed - Output shapes: {[out.shape for out in yolo_output]}")

        print("\n🎉 All ONNX models are working correctly!")
        print("📱 These models can now be used in Android with EXACT same accuracy as Python!")

        return True

    except Exception as e:
        print(f"❌ Error testing ONNX models: {e}")
        return False

def main():
    """Main conversion function."""
    print("🚀 Converting PyTorch Models to ONNX for Android")
    print("=" * 60)

    # Model paths (actual trained models)
    v6_pytorch_path = "chess_board_detection/breakthrough_v6_results/best_model.pth"
    yolo_pytorch_path = "chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt"

    # Output paths
    output_dir = "app with custom ai/ChessVisionApp/app/src/main/assets/onnx_models"
    os.makedirs(output_dir, exist_ok=True)

    v6_onnx_path = os.path.join(output_dir, "v6_mobile.onnx")
    yolo_onnx_path = os.path.join(output_dir, "yolo_mobile.onnx")

    # Check if input models exist
    if not os.path.exists(v6_pytorch_path):
        print(f"❌ V6 model not found: {v6_pytorch_path}")
        print("Please ensure the V6 model is available")
        return False

    if not os.path.exists(yolo_pytorch_path):
        print(f"❌ YOLO model not found: {yolo_pytorch_path}")
        print("Please ensure the YOLO model is available")
        return False

    # Convert models
    print(f"\n📂 Output directory: {output_dir}")

    success = True

    # Convert V6 model
    if not convert_v6_to_onnx(v6_pytorch_path, v6_onnx_path):
        success = False

    # Convert YOLO model
    if not convert_yolo_to_onnx(yolo_pytorch_path, yolo_onnx_path):
        success = False

    # Test converted models
    if success:
        if not test_onnx_models(v6_onnx_path, yolo_onnx_path):
            success = False

    if success:
        print("\n🎉 SUCCESS! Models converted to ONNX format")
        print("📱 Android app can now use EXACT same models as Python!")
        print(f"📁 V6 ONNX: {v6_onnx_path}")
        print(f"📁 YOLO ONNX: {yolo_onnx_path}")
        print("\n🔧 Next steps:")
        print("1. Add ONNX Runtime dependency to Android app")
        print("2. Update ONNXChessAI.kt to load these models")
        print("3. Test on Android device")
    else:
        print("\n❌ FAILED! Some models could not be converted")
        print("Please check the error messages above")

    return success

if __name__ == "__main__":
    main()
