unstable class ChessBoardState {
  unstable var board: Array<Array<ChessPiece?>>
  stable var selectedSquare$delegate: MutableState<ChessPosition?>
  stable var isEditMode$delegate: MutableState<Boolean>
  stable var currentFENString$delegate: MutableState<String>
  stable var selectedPieceForPlacement$delegate: MutableState<ChessPiece?>
  stable var isBoardFlipped$delegate: MutableState<Boolean>
  stable var draggedPiece$delegate: MutableState<ChessPiece?>
  stable var dragOffset$delegate: MutableState<Offset>
  stable var isDragging$delegate: MutableState<Boolean>
  stable var dragStartPosition$delegate: MutableState<ChessPosition?>
  stable var isDraggingFromTray$delegate: MutableState<Boolean>
  stable var draggedFromTrayPiece$delegate: MutableState<ChessPiece?>
  stable var dragStartOffset$delegate: MutableState<Offset>
  stable var boardBounds$delegate: MutableState<Rect>
  stable var lastMovedSquare$delegate: MutableState<ChessPosition?>
  stable var isAnimatingPiece$delegate: MutableState<Boolean>
  stable var animationTrigger$delegate: MutableState<Int>
  stable var onFENUpdated: Function1<String, Unit>?
  <runtime stability> = Unstable
}
unstable class MainActivity {
  runtime var chessAI: ChessAI
  <runtime stability> = Unstable
}
stable class HardwareProfile {
  stable val isHelioP35: Boolean
  stable val isPowerVRGE8320: Boolean
  stable val totalRAM: Long
  stable val isLowMemory: Boolean
  stable val cpuCores: Int
  stable val optimizationLevel: OptimizationLevel
  <runtime stability> = Stable
}
unstable class BoardSegmentationResult {
  unstable val correctedBoard: Bitmap
  unstable val squares: List<ChessSquare>
  stable val confidence: Float
  <runtime stability> = Unstable
}
stable class PieceDetectionResult {
  stable val className: String
  stable val confidence: Float
  stable val x: Float
  stable val y: Float
  stable val width: Float
  stable val height: Float
  <runtime stability> = Stable
}
stable class ChessSquare {
  stable val file: Int
  stable val rank: Int
  stable val x: Int
  stable val y: Int
  stable val width: Int
  stable val height: Int
  <runtime stability> = Stable
}
unstable class ONNXChessAI {
  unstable val context: Context
  unstable val isInitialized: AtomicBoolean
  unstable val isDisposed: AtomicBoolean
  unstable val initializationMutex: Mutex
  unstable val processingMutex: Mutex
  unstable val ortEnvironment: AtomicReference<OrtEnvironment?>
  unstable val v6Session: AtomicReference<OrtSession?>
  unstable val yoloSession: AtomicReference<OrtSession?>
  stable var operationCount: Int
  unstable val performanceMetrics: MutableMap<String, Long>
  unstable val hardwareProfile$delegate: Lazy<HardwareProfile>
  <runtime stability> = Unstable
}
