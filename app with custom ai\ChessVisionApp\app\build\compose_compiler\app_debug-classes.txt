stable class CameraStateManager {
  unstable val isInitialized: AtomicBoolean
  unstable val isDisposed: AtomicBoolean
  unstable val cameraProvider: AtomicReference<ProcessCameraProvider?>
  unstable val previewView: AtomicReference<PreviewView?>
  unstable val imageCapture: AtomicReference<ImageCapture?>
  unstable val cameraExecutor: AtomicReference<ExecutorService?>
}
unstable class ChessAI {
  unstable val context: Context
  unstable val isInitialized: AtomicBoolean
  unstable val isDisposed: AtomicBoolean
  unstable val initializationMutex: Mutex
  unstable val processingMutex: Mutex
  unstable val onnxAI: AtomicReference<ONNXChessAI?>
  stable var totalOperations: Int
  stable var totalProcessingTime: Long
  unstable val performanceMetrics: MutableMap<String, Any>
  <runtime stability> = Unstable
}
stable class Success {
  stable val fen: String
  stable val confidence: Float
  stable val processingTimeMs: Long
  stable val boardDetectionScore: Float
  stable val pieceDetectionAccuracy: Float
  stable val detectedPieces: Int
  <runtime stability> = Stable
}
stable class Error {
  stable val message: String
  <runtime stability> = Stable
}
stable class ChessAnalysisResult {
  <runtime stability> = Stable
}
unstable class ModelInfo {
  stable val modelType: String
  stable val v6ModelSizeMB: Double
  stable val yoloModelSizeMB: Double
  stable val totalPackageSizeMB: Double
  stable val diceScore: Double
  stable val map50Accuracy: Double
  stable val expectedInferenceTimeMs: Int
  stable val isInitialized: Boolean
  stable val exactSameAsPython: Boolean
  stable val totalOperations: Int
  stable val averageProcessingTime: Long
  unstable val performanceMetrics: Map<String, Any>
  <runtime stability> = Unstable
}
unstable class ChessBoardState {
  unstable var board: Array<Array<ChessPiece?>>
  stable var selectedSquare$delegate: MutableState<ChessPosition?>
  stable var isEditMode$delegate: MutableState<Boolean>
  stable var currentFENString$delegate: MutableState<String>
  stable var selectedPieceForPlacement$delegate: MutableState<ChessPiece?>
  stable var isBoardFlipped$delegate: MutableState<Boolean>
  stable var draggedPiece$delegate: MutableState<ChessPiece?>
  stable var dragOffset$delegate: MutableState<Offset>
  stable var isDragging$delegate: MutableState<Boolean>
  stable var dragStartPosition$delegate: MutableState<ChessPosition?>
  stable var isDraggingFromTray$delegate: MutableState<Boolean>
  stable var draggedFromTrayPiece$delegate: MutableState<ChessPiece?>
  stable var dragStartOffset$delegate: MutableState<Offset>
  stable var boardBounds$delegate: MutableState<Rect>
  stable var lastMovedSquare$delegate: MutableState<ChessPosition?>
  stable var isAnimatingPiece$delegate: MutableState<Boolean>
  stable var animationTrigger$delegate: MutableState<Int>
  stable var onFENUpdated: Function1<String, Unit>?
  <runtime stability> = Unstable
}
stable class ChessPiece {
  stable val type: PieceType
  stable val color: PieceColor
  stable val drawableRes: Int
  <runtime stability> = Stable
}
stable class ChessPosition {
  stable val file: Int
  stable val rank: Int
  <runtime stability> = Stable
}
stable class ChessVisionApplication {
  <runtime stability> = Stable
}
unstable class MainActivity {
  unstable var chessAI: ChessAI
  <runtime stability> = Unstable
}
unstable class BoardSegmentationResult {
  unstable val correctedBoard: Bitmap
  unstable val squares: List<ChessSquare>
  stable val confidence: Float
  <runtime stability> = Unstable
}
stable class PieceDetectionResult {
  stable val className: String
  stable val confidence: Float
  stable val x: Float
  stable val y: Float
  stable val width: Float
  stable val height: Float
  <runtime stability> = Stable
}
stable class ChessSquare {
  stable val file: Int
  stable val rank: Int
  stable val x: Int
  stable val y: Int
  stable val width: Int
  stable val height: Int
  <runtime stability> = Stable
}
unstable class ONNXChessAI {
  unstable val context: Context
  unstable val isInitialized: AtomicBoolean
  unstable val isDisposed: AtomicBoolean
  unstable val initializationMutex: Mutex
  unstable val processingMutex: Mutex
  unstable val ortEnvironment: AtomicReference<OrtEnvironment?>
  unstable val v6Session: AtomicReference<OrtSession?>
  unstable val yoloSession: AtomicReference<OrtSession?>
  stable var operationCount: Int
  unstable val performanceMetrics: MutableMap<String, Long>
  <runtime stability> = Unstable
}
stable class ChessExpressiveAnimations {
  stable val gentleSpring: SpringSpec<Float>
  stable val responsiveSpring: SpringSpec<Float>
  stable val snappySpring: SpringSpec<Float>
  stable val pieceMovement: SpringSpec<Float>
  stable val slideInFromBottom: EnterTransition
  stable val slideInFromTop: EnterTransition
  stable val slideInFromLeft: EnterTransition
  stable val slideInFromRight: EnterTransition
  stable val scaleIn: EnterTransition
  stable val scaleOut: ExitTransition
  <runtime stability> = Stable
}
stable class AdaptiveLayoutInfo {
  stable val screenWidth: Dp
  stable val screenHeight: Dp
  stable val isCompact: Boolean
  stable val isMedium: Boolean
  stable val isExpanded: Boolean
  stable val isLandscape: Boolean
  stable val spacing: Dp
  stable val cardPadding: Dp
  stable val boardSize: Dp
  <runtime stability> = Stable
}
stable class ChessExpressiveSurfaces {
  stable val primaryGradient: Brush
  stable val cardGradient: Brush
  stable val heroGradient: Brush
  stable val glassEffect: Brush
  stable val shimmerGradient: Brush
  <runtime stability> = Stable
}
stable class ImageUtils {
  stable val TAG: String
  <runtime stability> = Stable
}
