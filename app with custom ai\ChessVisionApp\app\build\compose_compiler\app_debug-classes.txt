stable class HardwareProfile {
  stable val isHelioP35: Boolean
  stable val isPowerVRGE8320: Boolean
  stable val totalRAM: Long
  stable val isLowMemory: Boolean
  stable val cpuCores: Int
  stable val optimizationLevel: OptimizationLevel
  <runtime stability> = Stable
}
unstable class ResourceTracker {
  unstable val resources: MutableList<AutoCloseable>
  unstable val isDisposed: AtomicBoolean
  <runtime stability> = Unstable
}
unstable class BoardSegmentationResult {
  unstable val correctedBoard: Bitmap
  unstable val squares: List<ChessSquare>
  stable val confidence: Float
  <runtime stability> = Unstable
}
stable class PieceDetectionResult {
  stable val className: String
  stable val confidence: Float
  stable val x: Float
  stable val y: Float
  stable val width: Float
  stable val height: Float
  <runtime stability> = Stable
}
stable class ChessSquare {
  stable val file: Int
  stable val rank: Int
  stable val x: Int
  stable val y: Int
  stable val width: Int
  stable val height: Int
  <runtime stability> = Stable
}
unstable class ONNXChessAI {
  unstable val context: Context
  unstable val isInitialized: AtomicBoolean
  unstable val isDisposed: AtomicBoolean
  unstable val initializationMutex: Mutex
  unstable val processingMutex: Mutex
  unstable val ortEnvironment: AtomicReference<OrtEnvironment?>
  unstable val v6Session: AtomicReference<OrtSession?>
  unstable val yoloSession: AtomicReference<OrtSession?>
  stable var operationCount: Int
  unstable val performanceMetrics: MutableMap<String, Long>
  unstable val hardwareProfile$delegate: Lazy<HardwareProfile>
  <runtime stability> = Unstable
}
