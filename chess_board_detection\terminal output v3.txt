  detection_rate: 0.7000
  Overall Confidence Score: 2.6455
val Heatmap Components:
  mse_loss: 0.0754
  separation_loss: 690.4685
  peak_separation_loss: 176.7144
  edge_suppression_loss: 21.9745
  peak_enhancement_loss: 0.0846
New best model saved with loss: 777.7581

Epoch 91/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.83s/it] 
train Loss: 846.7904, Seg Loss: 0.1152, Heatmap Loss: 564.1951, Geometric Loss: 0.9564
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.4687
  avg_peak_to_mean_ratio: 3.7032
  avg_peak_to_second_ratio: 1.0171
  detection_rate: 0.5833
  Overall Confidence Score: 1.4431
train Heatmap Components:
  mse_loss: 0.0863
  separation_loss: 755.6795
  peak_separation_loss: 188.1066
  edge_suppression_loss: 23.7214
  peak_enhancement_loss: 0.0855
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.15s/it] 
val Loss: 742.9581, Seg Loss: 0.2561, Heatmap Loss: 494.9958, Geometric Loss: 0.5207
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.5093
  avg_peak_to_mean_ratio: 7.7225
  avg_peak_to_second_ratio: 1.0798
  detection_rate: 0.6500
  Overall Confidence Score: 2.4904
val Heatmap Components:
  mse_loss: 0.0757
  separation_loss: 651.2828
  peak_separation_loss: 178.2305
  edge_suppression_loss: 21.4191
  peak_enhancement_loss: 0.0836
New best model saved with loss: 742.9581

Epoch 92/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.82s/it] 
train Loss: 819.2162, Seg Loss: 0.1609, Heatmap Loss: 545.7746, Geometric Loss: 0.9834
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5041
  avg_peak_to_mean_ratio: 4.4382
  avg_peak_to_second_ratio: 1.0229
  detection_rate: 0.6250
  Overall Confidence Score: 1.6475
train Heatmap Components:
  mse_loss: 0.0872
  separation_loss: 738.0298
  peak_separation_loss: 172.8290
  edge_suppression_loss: 23.4473
  peak_enhancement_loss: 0.0838
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.16s/it] 
val Loss: 741.7990, Seg Loss: 0.3280, Heatmap Loss: 494.1849, Geometric Loss: 0.4843
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6304
  avg_peak_to_mean_ratio: 7.3899
  avg_peak_to_second_ratio: 1.0362
  detection_rate: 0.7500
  Overall Confidence Score: 2.4516
val Heatmap Components:
  mse_loss: 0.0757
  separation_loss: 656.6162
  peak_separation_loss: 169.8522
  edge_suppression_loss: 21.6729
  peak_enhancement_loss: 0.0845
New best model saved with loss: 741.7990

Epoch 93/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.89s/it] 
train Loss: 799.9249, Seg Loss: 0.1148, Heatmap Loss: 532.9601, Geometric Loss: 0.9253
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5076
  avg_peak_to_mean_ratio: 4.6720
  avg_peak_to_second_ratio: 1.0548
  detection_rate: 0.6528
  Overall Confidence Score: 1.7218
train Heatmap Components:
  mse_loss: 0.0890
  separation_loss: 711.4996
  peak_separation_loss: 178.0229
  edge_suppression_loss: 24.1683
  peak_enhancement_loss: 0.0839
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.17s/it] 
val Loss: 743.2688, Seg Loss: 0.3502, Heatmap Loss: 495.1285, Geometric Loss: 0.5646
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6230
  avg_peak_to_mean_ratio: 6.1164
  avg_peak_to_second_ratio: 1.0517
  detection_rate: 0.7500
  Overall Confidence Score: 2.1353
val Heatmap Components:
  mse_loss: 0.0772
  separation_loss: 657.5767
  peak_separation_loss: 170.1722
  edge_suppression_loss: 21.9663
  peak_enhancement_loss: 0.0855

Epoch 94/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.90s/it] 
train Loss: 780.2121, Seg Loss: 0.1261, Heatmap Loss: 519.8377, Geometric Loss: 0.8235
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5636
  avg_peak_to_mean_ratio: 4.6975
  avg_peak_to_second_ratio: 1.0201
  detection_rate: 0.6806
  Overall Confidence Score: 1.7405
train Heatmap Components:
  mse_loss: 0.0895
  separation_loss: 693.9642
  peak_separation_loss: 172.3470
  edge_suppression_loss: 24.5060
  peak_enhancement_loss: 0.0840
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.19s/it] 
val Loss: 719.0818, Seg Loss: 0.3520, Heatmap Loss: 479.0674, Geometric Loss: 0.3218
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6154
  avg_peak_to_mean_ratio: 7.1621
  avg_peak_to_second_ratio: 1.0268
  detection_rate: 0.7500
  Overall Confidence Score: 2.3886
val Heatmap Components:
  mse_loss: 0.0749
  separation_loss: 632.0345
  peak_separation_loss: 168.9450
  edge_suppression_loss: 21.7958
  peak_enhancement_loss: 0.0844
New best model saved with loss: 719.0818

Epoch 95/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.89s/it] 
train Loss: 758.7462, Seg Loss: 0.1389, Heatmap Loss: 505.4783, Geometric Loss: 0.9749
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5164
  avg_peak_to_mean_ratio: 4.9468
  avg_peak_to_second_ratio: 1.0161
  detection_rate: 0.6528
  Overall Confidence Score: 1.7830
train Heatmap Components:
  mse_loss: 0.0879
  separation_loss: 671.5147
  peak_separation_loss: 172.7628
  edge_suppression_loss: 22.9428
  peak_enhancement_loss: 0.0803
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.22s/it] 
val Loss: 692.8099, Seg Loss: 0.3949, Heatmap Loss: 461.4688, Geometric Loss: 0.5293
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6106
  avg_peak_to_mean_ratio: 8.9449
  avg_peak_to_second_ratio: 1.0101
  detection_rate: 0.7500
  Overall Confidence Score: 2.8289
val Heatmap Components:
  mse_loss: 0.0756
  separation_loss: 604.5764
  peak_separation_loss: 166.4316
  edge_suppression_loss: 21.9846
  peak_enhancement_loss: 0.0847
New best model saved with loss: 692.8099

Epoch 96/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.87s/it] 
train Loss: 740.6470, Seg Loss: 0.0863, Heatmap Loss: 493.4978, Geometric Loss: 0.7847
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.4955
  avg_peak_to_mean_ratio: 4.4677
  avg_peak_to_second_ratio: 1.0132
  detection_rate: 0.6111
  Overall Confidence Score: 1.6469
train Heatmap Components:
  mse_loss: 0.0875
  separation_loss: 651.6388
  peak_separation_loss: 173.0192
  edge_suppression_loss: 22.6819
  peak_enhancement_loss: 0.0802
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.16s/it] 
val Loss: 682.2245, Seg Loss: 0.3865, Heatmap Loss: 454.3471, Geometric Loss: 0.7936
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.5934
  avg_peak_to_mean_ratio: 9.5705
  avg_peak_to_second_ratio: 1.0072
  detection_rate: 0.7500
  Overall Confidence Score: 2.9803
val Heatmap Components:
  mse_loss: 0.0743
  separation_loss: 593.0974
  peak_separation_loss: 167.0816
  edge_suppression_loss: 21.1880
  peak_enhancement_loss: 0.0839
New best model saved with loss: 682.2245

Epoch 97/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.82s/it] 
train Loss: 723.3592, Seg Loss: 0.1441, Heatmap Loss: 481.9063, Geometric Loss: 0.8893
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5194
  avg_peak_to_mean_ratio: 4.0337
  avg_peak_to_second_ratio: 1.0393
  detection_rate: 0.6250
  Overall Confidence Score: 1.5543
train Heatmap Components:
  mse_loss: 0.0866
  separation_loss: 634.0170
  peak_separation_loss: 171.3909
  edge_suppression_loss: 22.3880
  peak_enhancement_loss: 0.0849
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.15s/it] 
val Loss: 679.0684, Seg Loss: 0.4230, Heatmap Loss: 452.2463, Geometric Loss: 0.6898
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.5851
  avg_peak_to_mean_ratio: 9.2242
  avg_peak_to_second_ratio: 1.0119
  detection_rate: 0.7500
  Overall Confidence Score: 2.8928
val Heatmap Components:
  mse_loss: 0.0747
  separation_loss: 592.5612
  peak_separation_loss: 165.5127
  edge_suppression_loss: 19.7654
  peak_enhancement_loss: 0.0854
New best model saved with loss: 679.0684

Epoch 98/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.88s/it] 
train Loss: 715.4802, Seg Loss: 0.1207, Heatmap Loss: 476.6781, Geometric Loss: 0.8557
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5859
  avg_peak_to_mean_ratio: 5.2466
  avg_peak_to_second_ratio: 1.0446
  detection_rate: 0.6389
  Overall Confidence Score: 1.8790
train Heatmap Components:
  mse_loss: 0.0855
  separation_loss: 621.3548
  peak_separation_loss: 175.6535
  edge_suppression_loss: 22.7310
  peak_enhancement_loss: 0.0827
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.20s/it] 
val Loss: 671.0391, Seg Loss: 0.3979, Heatmap Loss: 446.7981, Geometric Loss: 1.1101
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.5853
  avg_peak_to_mean_ratio: 10.5010
  avg_peak_to_second_ratio: 1.0176
  detection_rate: 0.7500
  Overall Confidence Score: 3.2135
val Heatmap Components:
  mse_loss: 0.0778
  separation_loss: 582.8143
  peak_separation_loss: 167.1105
  edge_suppression_loss: 19.1906
  peak_enhancement_loss: 0.0862
New best model saved with loss: 671.0391

Epoch 99/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.91s/it] 
train Loss: 695.7608, Seg Loss: 0.1111, Heatmap Loss: 463.5014, Geometric Loss: 0.9942
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5502
  avg_peak_to_mean_ratio: 5.0226
  avg_peak_to_second_ratio: 1.0142
  detection_rate: 0.6389
  Overall Confidence Score: 1.8065
train Heatmap Components:
  mse_loss: 0.0864
  separation_loss: 600.6664
  peak_separation_loss: 172.7813
  edge_suppression_loss: 23.6884
  peak_enhancement_loss: 0.0852
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.19s/it] 
val Loss: 661.9871, Seg Loss: 0.4226, Heatmap Loss: 440.7433, Geometric Loss: 1.1238
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.5884
  avg_peak_to_mean_ratio: 10.3283
  avg_peak_to_second_ratio: 1.0234
  detection_rate: 0.7500
  Overall Confidence Score: 3.1725
val Heatmap Components:
  mse_loss: 0.0801
  separation_loss: 573.7650
  peak_separation_loss: 164.9544
  edge_suppression_loss: 19.8338
  peak_enhancement_loss: 0.0866
New best model saved with loss: 661.9871

Epoch 100/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.89s/it] 
train Loss: 682.2992, Seg Loss: 0.1065, Heatmap Loss: 454.5532, Geometric Loss: 0.9074
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5868
  avg_peak_to_mean_ratio: 5.0649
  avg_peak_to_second_ratio: 1.0234
  detection_rate: 0.6250
  Overall Confidence Score: 1.8250
train Heatmap Components:
  mse_loss: 0.0854
  separation_loss: 588.1277
  peak_separation_loss: 172.8500
  edge_suppression_loss: 21.6078
  peak_enhancement_loss: 0.0815
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.16s/it] 
val Loss: 670.1189, Seg Loss: 0.3849, Heatmap Loss: 446.2013, Geometric Loss: 1.0799
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6100
  avg_peak_to_mean_ratio: 9.5716
  avg_peak_to_second_ratio: 1.0095
  detection_rate: 0.7500
  Overall Confidence Score: 2.9853
val Heatmap Components:
  mse_loss: 0.0773
  separation_loss: 582.8658
  peak_separation_loss: 166.2777
  edge_suppression_loss: 18.8901
  peak_enhancement_loss: 0.0852

Epoch 101/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.91s/it] 
train Loss: 668.6156, Seg Loss: 0.1276, Heatmap Loss: 445.3756, Geometric Loss: 1.0614
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5860
  avg_peak_to_mean_ratio: 5.0341
  avg_peak_to_second_ratio: 1.0167
  detection_rate: 0.6250
  Overall Confidence Score: 1.8155
train Heatmap Components:
  mse_loss: 0.0851
  separation_loss: 572.7358
  peak_separation_loss: 172.9571
  edge_suppression_loss: 21.6135
  peak_enhancement_loss: 0.0820
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.21s/it] 
val Loss: 662.0297, Seg Loss: 0.2766, Heatmap Loss: 440.8063, Geometric Loss: 1.3593
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6450
  avg_peak_to_mean_ratio: 9.3375
  avg_peak_to_second_ratio: 1.0136
  detection_rate: 0.7500
  Overall Confidence Score: 2.9365
val Heatmap Components:
  mse_loss: 0.0726
  separation_loss: 571.5908
  peak_separation_loss: 168.8277
  edge_suppression_loss: 19.0326
  peak_enhancement_loss: 0.0850

Epoch 102/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.87s/it] 
train Loss: 653.3407, Seg Loss: 0.1091, Heatmap Loss: 435.2188, Geometric Loss: 1.0088
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5876
  avg_peak_to_mean_ratio: 5.1362
  avg_peak_to_second_ratio: 1.0180
  detection_rate: 0.6250
  Overall Confidence Score: 1.8417
train Heatmap Components:
  mse_loss: 0.0852
  separation_loss: 556.2836
  peak_separation_loss: 172.7995
  edge_suppression_loss: 21.3169
  peak_enhancement_loss: 0.0837
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.21s/it] 
val Loss: 629.4693, Seg Loss: 0.3389, Heatmap Loss: 419.1011, Geometric Loss: 1.1970
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6491
  avg_peak_to_mean_ratio: 8.9852
  avg_peak_to_second_ratio: 1.0168
  detection_rate: 0.7500
  Overall Confidence Score: 2.8503
val Heatmap Components:
  mse_loss: 0.0748
  separation_loss: 539.3288
  peak_separation_loss: 165.4777
  edge_suppression_loss: 18.0672
  peak_enhancement_loss: 0.0862
New best model saved with loss: 629.4693

Epoch 103/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.90s/it] 
train Loss: 639.8395, Seg Loss: 0.1057, Heatmap Loss: 426.2527, Geometric Loss: 0.8867
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5710
  avg_peak_to_mean_ratio: 5.3030
  avg_peak_to_second_ratio: 1.0336
  detection_rate: 0.6389
  Overall Confidence Score: 1.8866
train Heatmap Components:
  mse_loss: 0.0842
  separation_loss: 542.0488
  peak_separation_loss: 171.9470
  edge_suppression_loss: 21.3203
  peak_enhancement_loss: 0.0829
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.14s/it] 
val Loss: 608.8649, Seg Loss: 0.3736, Heatmap Loss: 405.3974, Geometric Loss: 0.9882
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6412
  avg_peak_to_mean_ratio: 9.9657
  avg_peak_to_second_ratio: 1.0079
  detection_rate: 0.7500
  Overall Confidence Score: 3.0912
val Heatmap Components:
  mse_loss: 0.0747
  separation_loss: 519.0068
  peak_separation_loss: 163.1877
  edge_suppression_loss: 17.5446
  peak_enhancement_loss: 0.0868
New best model saved with loss: 608.8649

Epoch 104/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:18<00:00,  3.80s/it] 
train Loss: 637.1827, Seg Loss: 0.1316, Heatmap Loss: 424.4648, Geometric Loss: 0.8847
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5155
  avg_peak_to_mean_ratio: 3.8717
  avg_peak_to_second_ratio: 1.0270
  detection_rate: 0.5833
  Overall Confidence Score: 1.4994
train Heatmap Components:
  mse_loss: 0.0830
  separation_loss: 539.4054
  peak_separation_loss: 171.9574
  edge_suppression_loss: 21.0259
  peak_enhancement_loss: 0.0835
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.19s/it] 
val Loss: 616.3737, Seg Loss: 0.3919, Heatmap Loss: 410.4042, Geometric Loss: 0.9385
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6081
  avg_peak_to_mean_ratio: 7.1903
  avg_peak_to_second_ratio: 1.0098
  detection_rate: 0.7500
  Overall Confidence Score: 2.3896
val Heatmap Components:
  mse_loss: 0.0733
  separation_loss: 526.2873
  peak_separation_loss: 164.7439
  edge_suppression_loss: 17.3469
  peak_enhancement_loss: 0.0875

Epoch 105/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.90s/it] 
train Loss: 618.1567, Seg Loss: 0.1240, Heatmap Loss: 411.8075, Geometric Loss: 0.8038
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5672
  avg_peak_to_mean_ratio: 4.7446
  avg_peak_to_second_ratio: 1.0282
  detection_rate: 0.6389
  Overall Confidence Score: 1.7447
train Heatmap Components:
  mse_loss: 0.0846
  separation_loss: 520.9098
  peak_separation_loss: 170.2034
  edge_suppression_loss: 20.0463
  peak_enhancement_loss: 0.0859
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.20s/it] 
val Loss: 609.6173, Seg Loss: 0.4528, Heatmap Loss: 405.9096, Geometric Loss: 0.7501
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.6329
  avg_peak_to_mean_ratio: 6.8460
  avg_peak_to_second_ratio: 1.0174
  detection_rate: 0.7500
  Overall Confidence Score: 2.3116
val Heatmap Components:
  mse_loss: 0.0721
  separation_loss: 523.8087
  peak_separation_loss: 160.3338
  edge_suppression_loss: 16.2022
  peak_enhancement_loss: 0.0877

Epoch 106/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.89s/it] 
train Loss: 605.9074, Seg Loss: 0.1478, Heatmap Loss: 403.5613, Geometric Loss: 1.0440
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6736
  avg_peak_to_mean_ratio: 6.7024
  avg_peak_to_second_ratio: 1.0116
  detection_rate: 0.7083
  Overall Confidence Score: 2.2740
train Heatmap Components:
  mse_loss: 0.0824
  separation_loss: 512.7015
  peak_separation_loss: 167.3012
  edge_suppression_loss: 17.3787
  peak_enhancement_loss: 0.0846
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.17s/it] 
val Loss: 583.4562, Seg Loss: 0.4533, Heatmap Loss: 388.4042, Geometric Loss: 0.9916
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7335
  avg_peak_to_mean_ratio: 8.6175
  avg_peak_to_second_ratio: 1.0051
  detection_rate: 0.7500
  Overall Confidence Score: 2.7765
val Heatmap Components:
  mse_loss: 0.0710
  separation_loss: 495.6195
  peak_separation_loss: 160.1150
  edge_suppression_loss: 15.5143
  peak_enhancement_loss: 0.0878
New best model saved with loss: 583.4562

Epoch 107/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.88s/it] 
train Loss: 593.9985, Seg Loss: 0.1734, Heatmap Loss: 395.6850, Geometric Loss: 0.7439
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6423
  avg_peak_to_mean_ratio: 5.3224
  avg_peak_to_second_ratio: 1.0265
  detection_rate: 0.6528
  Overall Confidence Score: 1.9110
train Heatmap Components:
  mse_loss: 0.0820
  separation_loss: 501.2498
  peak_separation_loss: 166.5347
  edge_suppression_loss: 16.4904
  peak_enhancement_loss: 0.0850
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.18s/it] 
val Loss: 558.5150, Seg Loss: 0.3677, Heatmap Loss: 371.9163, Geometric Loss: 0.6821
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7673
  avg_peak_to_mean_ratio: 12.3360
  avg_peak_to_second_ratio: 1.0056
  detection_rate: 0.7500
  Overall Confidence Score: 3.7147
val Heatmap Components:
  mse_loss: 0.0703
  separation_loss: 466.1740
  peak_separation_loss: 163.2344
  edge_suppression_loss: 14.9720
  peak_enhancement_loss: 0.0879
New best model saved with loss: 558.5150

Epoch 108/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.89s/it] 
train Loss: 580.0480, Seg Loss: 0.1532, Heatmap Loss: 386.2983, Geometric Loss: 1.1183
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6065
  avg_peak_to_mean_ratio: 4.3938
  avg_peak_to_second_ratio: 1.0271
  detection_rate: 0.5972
  Overall Confidence Score: 1.6562
train Heatmap Components:
  mse_loss: 0.0812
  separation_loss: 482.7169
  peak_separation_loss: 169.3137
  edge_suppression_loss: 16.9796
  peak_enhancement_loss: 0.0888
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.16s/it] 
val Loss: 546.3307, Seg Loss: 0.2916, Heatmap Loss: 363.9067, Geometric Loss: 0.4473
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7431
  avg_peak_to_mean_ratio: 15.4063
  avg_peak_to_second_ratio: 1.0060
  detection_rate: 0.7500
  Overall Confidence Score: 4.4763
val Heatmap Components:
  mse_loss: 0.0702
  separation_loss: 448.9347
  peak_separation_loss: 167.8039
  edge_suppression_loss: 15.0428
  peak_enhancement_loss: 0.0875
New best model saved with loss: 546.3307

Epoch 109/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.86s/it] 
train Loss: 567.6147, Seg Loss: 0.0983, Heatmap Loss: 378.1438, Geometric Loss: 0.7518
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6060
  avg_peak_to_mean_ratio: 4.7711
  avg_peak_to_second_ratio: 1.0173
  detection_rate: 0.6111
  Overall Confidence Score: 1.7514
train Heatmap Components:
  mse_loss: 0.0808
  separation_loss: 469.5977
  peak_separation_loss: 171.0759
  edge_suppression_loss: 15.3238
  peak_enhancement_loss: 0.0795
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.14s/it] 
val Loss: 546.6505, Seg Loss: 0.2905, Heatmap Loss: 363.9523, Geometric Loss: 1.0788
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7527
  avg_peak_to_mean_ratio: 14.9919
  avg_peak_to_second_ratio: 1.0070
  detection_rate: 0.7500
  Overall Confidence Score: 4.3754
val Heatmap Components:
  mse_loss: 0.0689
  separation_loss: 448.5362
  peak_separation_loss: 168.2533
  edge_suppression_loss: 15.1309
  peak_enhancement_loss: 0.0867

Epoch 110/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.80s/it] 
train Loss: 560.0532, Seg Loss: 0.1402, Heatmap Loss: 373.0854, Geometric Loss: 0.7125
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6240
  avg_peak_to_mean_ratio: 5.4315
  avg_peak_to_second_ratio: 1.1020
  detection_rate: 0.6250
  Overall Confidence Score: 1.9456
train Heatmap Components:
  mse_loss: 0.0811
  separation_loss: 461.5672
  peak_separation_loss: 169.5121
  edge_suppression_loss: 16.0919
  peak_enhancement_loss: 0.0870
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.15s/it] 
val Loss: 554.1762, Seg Loss: 0.3397, Heatmap Loss: 368.9825, Geometric Loss: 0.9070
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7780
  avg_peak_to_mean_ratio: 12.9105
  avg_peak_to_second_ratio: 1.0082
  detection_rate: 0.7500
  Overall Confidence Score: 3.8617
val Heatmap Components:
  mse_loss: 0.0688
  separation_loss: 458.8517
  peak_separation_loss: 166.1111
  edge_suppression_loss: 15.0062
  peak_enhancement_loss: 0.0855

Epoch 111/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.82s/it] 
train Loss: 550.2187, Seg Loss: 0.0948, Heatmap Loss: 366.5323, Geometric Loss: 0.8137
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6804
  avg_peak_to_mean_ratio: 7.2853
  avg_peak_to_second_ratio: 1.0620
  detection_rate: 0.6667
  Overall Confidence Score: 2.4236
train Heatmap Components:
  mse_loss: 0.0814
  separation_loss: 450.8607
  peak_separation_loss: 170.0052
  edge_suppression_loss: 15.5556
  peak_enhancement_loss: 0.0859
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.16s/it] 
val Loss: 551.2849, Seg Loss: 0.3444, Heatmap Loss: 367.0725, Geometric Loss: 0.8294
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.8012
  avg_peak_to_mean_ratio: 11.4277
  avg_peak_to_second_ratio: 1.0084
  detection_rate: 0.7500
  Overall Confidence Score: 3.4968
val Heatmap Components:
  mse_loss: 0.0690
  separation_loss: 458.4799
  peak_separation_loss: 163.1505
  edge_suppression_loss: 14.7103
  peak_enhancement_loss: 0.0860

Epoch 112/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.84s/it] 
train Loss: 540.4792, Seg Loss: 0.1154, Heatmap Loss: 360.0127, Geometric Loss: 0.8616
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6229
  avg_peak_to_mean_ratio: 6.1792
  avg_peak_to_second_ratio: 1.0262
  detection_rate: 0.6389
  Overall Confidence Score: 2.1168
train Heatmap Components:
  mse_loss: 0.0809
  separation_loss: 440.3743
  peak_separation_loss: 170.2716
  edge_suppression_loss: 15.0413
  peak_enhancement_loss: 0.0852
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.17s/it] 
val Loss: 528.7758, Seg Loss: 0.3961, Heatmap Loss: 352.0342, Geometric Loss: 0.8210
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7857
  avg_peak_to_mean_ratio: 12.3912
  avg_peak_to_second_ratio: 1.0063
  detection_rate: 0.7500
  Overall Confidence Score: 3.7333
val Heatmap Components:
  mse_loss: 0.0687
  separation_loss: 435.7311
  peak_separation_loss: 160.1611
  edge_suppression_loss: 14.8626
  peak_enhancement_loss: 0.0850
New best model saved with loss: 528.7758

Epoch 113/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.90s/it] 
train Loss: 536.1866, Seg Loss: 0.1046, Heatmap Loss: 357.1802, Geometric Loss: 0.7792
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.5781
  avg_peak_to_mean_ratio: 4.3142
  avg_peak_to_second_ratio: 1.0206
  detection_rate: 0.5833
  Overall Confidence Score: 1.6240
train Heatmap Components:
  mse_loss: 0.0799
  separation_loss: 435.1693
  peak_separation_loss: 170.3503
  edge_suppression_loss: 15.3996
  peak_enhancement_loss: 0.0875
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.18s/it] 
val Loss: 516.2774, Seg Loss: 0.3314, Heatmap Loss: 343.6674, Geometric Loss: 1.1123
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7392
  avg_peak_to_mean_ratio: 16.2182
  avg_peak_to_second_ratio: 1.0369
  detection_rate: 0.7500
  Overall Confidence Score: 4.6861
val Heatmap Components:
  mse_loss: 0.0682
  separation_loss: 418.6762
  peak_separation_loss: 164.4566
  edge_suppression_loss: 14.4607
  peak_enhancement_loss: 0.0852
New best model saved with loss: 516.2774

Epoch 114/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.90s/it] 
train Loss: 526.0168, Seg Loss: 0.1467, Heatmap Loss: 350.4117, Geometric Loss: 0.6313
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6077
  avg_peak_to_mean_ratio: 5.8268
  avg_peak_to_second_ratio: 1.0357
  detection_rate: 0.5972
  Overall Confidence Score: 2.0169
train Heatmap Components:
  mse_loss: 0.0783
  separation_loss: 424.7877
  peak_separation_loss: 170.0535
  edge_suppression_loss: 14.8464
  peak_enhancement_loss: 0.0830
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.16s/it] 
val Loss: 508.3952, Seg Loss: 0.2955, Heatmap Loss: 338.5522, Geometric Loss: 0.6783
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7483
  avg_peak_to_mean_ratio: 17.0456
  avg_peak_to_second_ratio: 1.0306
  detection_rate: 0.7500
  Overall Confidence Score: 4.8936
val Heatmap Components:
  mse_loss: 0.0681
  separation_loss: 407.3253
  peak_separation_loss: 167.8805
  edge_suppression_loss: 14.4369
  peak_enhancement_loss: 0.0857
New best model saved with loss: 508.3952

Epoch 115/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.93s/it] 
train Loss: 515.9363, Seg Loss: 0.1291, Heatmap Loss: 343.6197, Geometric Loss: 0.9441
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6786
  avg_peak_to_mean_ratio: 6.7647
  avg_peak_to_second_ratio: 1.0214
  detection_rate: 0.6389
  Overall Confidence Score: 2.2759
train Heatmap Components:
  mse_loss: 0.0804
  separation_loss: 413.3298
  peak_separation_loss: 170.2774
  edge_suppression_loss: 14.8005
  peak_enhancement_loss: 0.0847
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.18s/it] 
val Loss: 503.1159, Seg Loss: 0.3462, Heatmap Loss: 334.9443, Geometric Loss: 0.8833
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7899
  avg_peak_to_mean_ratio: 14.2811
  avg_peak_to_second_ratio: 1.0070
  detection_rate: 0.7500
  Overall Confidence Score: 4.2070
val Heatmap Components:
  mse_loss: 0.0703
  separation_loss: 402.5552
  peak_separation_loss: 166.2900
  edge_suppression_loss: 14.5038
  peak_enhancement_loss: 0.0864
New best model saved with loss: 503.1159

Epoch 116/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.83s/it] 
train Loss: 510.3790, Seg Loss: 0.1090, Heatmap Loss: 339.9523, Geometric Loss: 0.8539
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.7130
  avg_peak_to_mean_ratio: 9.6573
  avg_peak_to_second_ratio: 1.0218
  detection_rate: 0.6944
  Overall Confidence Score: 3.0216
train Heatmap Components:
  mse_loss: 0.0794
  separation_loss: 407.8562
  peak_separation_loss: 169.8677
  edge_suppression_loss: 14.5456
  peak_enhancement_loss: 0.0868
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.17s/it] 
val Loss: 500.3278, Seg Loss: 0.3773, Heatmap Loss: 333.1205, Geometric Loss: 0.6742
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.8130
  avg_peak_to_mean_ratio: 9.0985
  avg_peak_to_second_ratio: 1.0023
  detection_rate: 0.7500
  Overall Confidence Score: 2.9160
val Heatmap Components:
  mse_loss: 0.0724
  separation_loss: 403.2299
  peak_separation_loss: 161.5289
  edge_suppression_loss: 14.7160
  peak_enhancement_loss: 0.0889
New best model saved with loss: 500.3278

Epoch 117/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.90s/it] 
train Loss: 501.0262, Seg Loss: 0.1173, Heatmap Loss: 333.6824, Geometric Loss: 0.9631
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6822
  avg_peak_to_mean_ratio: 6.3629
  avg_peak_to_second_ratio: 1.0170
  detection_rate: 0.6250
  Overall Confidence Score: 2.1718
train Heatmap Components:
  mse_loss: 0.0798
  separation_loss: 396.1021
  peak_separation_loss: 171.2015
  edge_suppression_loss: 14.7118
  peak_enhancement_loss: 0.0846
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.18s/it] 
val Loss: 495.7530, Seg Loss: 0.3133, Heatmap Loss: 330.1181, Geometric Loss: 0.6561
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7908
  avg_peak_to_mean_ratio: 13.4778
  avg_peak_to_second_ratio: 1.0036
  detection_rate: 0.7500
  Overall Confidence Score: 4.0056
val Heatmap Components:
  mse_loss: 0.0677
  separation_loss: 396.3325
  peak_separation_loss: 164.5605
  edge_suppression_loss: 14.1808
  peak_enhancement_loss: 0.0882
New best model saved with loss: 495.7530

Epoch 118/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.89s/it] 
train Loss: 493.2459, Seg Loss: 0.1056, Heatmap Loss: 328.4455, Geometric Loss: 1.1803
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6392
  avg_peak_to_mean_ratio: 6.9154
  avg_peak_to_second_ratio: 1.0123
  detection_rate: 0.6250
  Overall Confidence Score: 2.2980
train Heatmap Components:
  mse_loss: 0.0780
  separation_loss: 388.6997
  peak_separation_loss: 170.1580
  edge_suppression_loss: 14.3257
  peak_enhancement_loss: 0.0812
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.15s/it] 
val Loss: 478.0148, Seg Loss: 0.3121, Heatmap Loss: 318.1297, Geometric Loss: 1.2703
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7896
  avg_peak_to_mean_ratio: 15.2198
  avg_peak_to_second_ratio: 1.0052
  detection_rate: 0.7500
  Overall Confidence Score: 4.4411
val Heatmap Components:
  mse_loss: 0.0656
  separation_loss: 377.0065
  peak_separation_loss: 164.0077
  edge_suppression_loss: 14.0187
  peak_enhancement_loss: 0.0863
New best model saved with loss: 478.0148

Epoch 119/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:19<00:00,  3.95s/it] 
train Loss: 485.2131, Seg Loss: 0.1037, Heatmap Loss: 323.2011, Geometric Loss: 0.7695
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6480
  avg_peak_to_mean_ratio: 6.0553
  avg_peak_to_second_ratio: 1.0274
  detection_rate: 0.6389
  Overall Confidence Score: 2.0924
train Heatmap Components:
  mse_loss: 0.0785
  separation_loss: 379.7132
  peak_separation_loss: 170.3282
  edge_suppression_loss: 14.4108
  peak_enhancement_loss: 0.0860
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.17s/it] 
val Loss: 472.5474, Seg Loss: 0.2998, Heatmap Loss: 314.6228, Geometric Loss: 0.7837
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7747
  avg_peak_to_mean_ratio: 15.5446
  avg_peak_to_second_ratio: 1.0070
  detection_rate: 0.7500
  Overall Confidence Score: 4.5191
val Heatmap Components:
  mse_loss: 0.0634
  separation_loss: 369.1155
  peak_separation_loss: 166.6727
  edge_suppression_loss: 13.8715
  peak_enhancement_loss: 0.0873
New best model saved with loss: 472.5474

Epoch 120/120
----------
train: 100%|████████████████████████████████████████████████████████████████████████████████████| 5/5 [00:18<00:00,  3.79s/it] 
train Loss: 478.3816, Seg Loss: 0.0775, Heatmap Loss: 318.5931, Geometric Loss: 1.0361
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.6541
  avg_peak_to_mean_ratio: 5.7481
  avg_peak_to_second_ratio: 1.0878
  detection_rate: 0.5972
  Overall Confidence Score: 2.0218
train Heatmap Components:
  mse_loss: 0.0788
  separation_loss: 370.9577
  peak_separation_loss: 171.9012
  edge_suppression_loss: 14.2079
  peak_enhancement_loss: 0.0870
val: 100%|██████████████████████████████████████████████████████████████████████████████████████| 2/2 [00:02<00:00,  1.13s/it]  
val Loss: 460.6210, Seg Loss: 0.3083, Heatmap Loss: 306.5654, Geometric Loss: 1.1616
=== val Corner Confidence Metrics ===
  avg_peak_value: 0.7719
  avg_peak_to_mean_ratio: 16.0331
  avg_peak_to_second_ratio: 1.0094
  detection_rate: 0.7500
  Overall Confidence Score: 4.6411
val Heatmap Components:
  mse_loss: 0.0633
  separation_loss: 353.7409
  peak_separation_loss: 169.0116
  edge_suppression_loss: 13.8687
  peak_enhancement_loss: 0.0872
New best model saved with loss: 460.6210

Training complete in 43m 43s
Best val loss: 460.6210
Training completed!
Training completed!
Model data saved to v3 folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
To test the model, run:
python inference_enhanced.py --image_path <path_to_image> --model_path C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v3\best_model.pth
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1>