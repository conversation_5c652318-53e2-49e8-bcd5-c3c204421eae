@echo off
REM Run YOLO detection with improved visualization

set MODEL_PATH=chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
set INPUT_PATH=%1
set OUTPUT_DIR=chess_board_detection/outputs/piece_detection

if "%1"=="" (
    echo Usage: run_yolo_detection.bat [input_image_path]
    exit /b 1
)

echo Running chess piece detection with improved visualization...
python chess_board_detection/run_piece_detection.py --model %MODEL_PATH% --input %INPUT_PATH% --output_dir %OUTPUT_DIR% --font_size 0.4 --line_width 1 --conf 0.7

echo Detection completed. Results saved to %OUTPUT_DIR%
