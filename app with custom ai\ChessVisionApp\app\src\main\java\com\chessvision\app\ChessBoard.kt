package com.chessvision.app

import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

/**
 * Main chess board composable that combines all components
 */
@Composable
fun ChessBoard(
    modifier: Modifier = Modifier,
    onFENChanged: (String) -> Unit = {}
) {
    val boardState = rememberChessBoardState()
    
    // Set up FEN update callback
    LaunchedEffect(Unit) {
        boardState.setFENUpdateCallback(onFENChanged)
    }

    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // Chess board controls
        ChessBoardControls(boardState = boardState)
        
        // Interactive chess board
        InteractiveChessBoard(
            boardState = boardState,
            onFENChanged = onFENChanged
        )
        
        // Piece tray (only visible in edit mode)
        PieceTray(boardState = boardState)
        
        // FEN display and editor
        FENDisplay(
            fen = boardState.currentFENString,
            onFenChanged = { newFen ->
                boardState.updateFromFEN(newFen)
                onFENChanged(newFen)
            }
        )
    }
}
