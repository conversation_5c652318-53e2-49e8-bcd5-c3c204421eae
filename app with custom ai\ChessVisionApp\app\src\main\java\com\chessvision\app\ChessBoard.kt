package com.chessvision.app

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.res.painterResource
import androidx.compose.foundation.Image
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import com.chessvision.app.ui.theme.ChessExpressiveTypography
import com.chessvision.app.ui.theme.LocalChessExpressiveSurfaces
import com.chessvision.app.ui.theme.ChessExpressiveAnimations
import com.chessvision.app.ui.theme.rememberAdaptiveLayout
import com.chessvision.app.ui.theme.expressiveClickable
import com.chessvision.app.ui.theme.breathingAnimation
import com.chessvision.app.ui.theme.floatingAnimation

/**
 * Interactive Chess Board Component
 * Features:
 * - Play moves by clicking squares
 * - Edit board positions
 * - Load FEN positions from AI
 * - Export current position as FEN
 */

data class ChessPiece(
    val type: PieceType,
    val color: PieceColor,
    val drawableRes: Int
)

enum class PieceType {
    KING, QUEEN, ROOK, BISHOP, KNIGHT, PAWN
}

enum class PieceColor {
    WHITE, BLACK
}

data class ChessPosition(
    val file: Int, // 0-7 (a-h)
    val rank: Int  // 0-7 (1-8)
) {
    fun isValid(): Boolean = file in 0..7 && rank in 0..7
}

class ChessBoardState {
    var board = Array(8) { Array<ChessPiece?>(8) { null } }
    var selectedSquare by mutableStateOf<ChessPosition?>(null)
    var isEditMode by mutableStateOf(false)
    var currentFENString by mutableStateOf("rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1")
    var selectedPieceForPlacement by mutableStateOf<ChessPiece?>(null)
    var isBoardFlipped by mutableStateOf(false)
    var draggedPiece by mutableStateOf<ChessPiece?>(null)
    var dragOffset by mutableStateOf(Offset.Zero)
    var isDragging by mutableStateOf(false)
    var dragStartPosition by mutableStateOf<ChessPosition?>(null)
    var isDraggingFromTray by mutableStateOf(false)
    var draggedFromTrayPiece by mutableStateOf<ChessPiece?>(null)
    var dragStartOffset by mutableStateOf(Offset.Zero)
    var boardBounds by mutableStateOf(androidx.compose.ui.geometry.Rect.Zero)

    // Animation states for Material 3 Expressive features
    var lastMovedSquare by mutableStateOf<ChessPosition?>(null)
    var isAnimatingPiece by mutableStateOf(false)
    var animationTrigger by mutableStateOf(0)

    // FEN update callback for real-time synchronization
    private var onFENUpdated: ((String) -> Unit)? = null

    fun setFENUpdateCallback(callback: (String) -> Unit) {
        onFENUpdated = callback
    }

    init {
        setupInitialPosition()
    }

    // Enhanced FEN update that always triggers when board changes
    private fun updateFEN() {
        val newFEN = getCurrentFEN()
        if (newFEN != currentFENString) {
            currentFENString = newFEN
            onFENUpdated?.invoke(newFEN)
            // Trigger animation update
            animationTrigger++
        }
    }

    fun setupInitialPosition() {
        // Clear board
        board = Array(8) { Array<ChessPiece?>(8) { null } }

        // Setup initial chess position
        val initialFEN = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1"
        loadFromFEN(initialFEN)
    }

    fun loadFromFEN(fen: String) {
        currentFENString = fen
        val parts = fen.split(" ")
        val position = parts[0]

        // Clear board
        board = Array(8) { Array<ChessPiece?>(8) { null } }

        val ranks = position.split("/")
        for (rankIndex in ranks.indices) {
            var fileIndex = 0
            for (char in ranks[rankIndex]) {
                if (char.isDigit()) {
                    fileIndex += char.digitToInt()
                } else {
                    val piece = charToPiece(char)
                    if (piece != null && fileIndex < 8) {
                        board[7 - rankIndex][fileIndex] = piece
                    }
                    fileIndex++
                }
            }
        }
        // Always update FEN after loading
        updateFEN()
    }

    fun getCurrentFEN(): String {
        val fenBuilder = StringBuilder()

        for (rank in 7 downTo 0) {
            var emptyCount = 0
            for (file in 0..7) {
                val piece = board[rank][file]
                if (piece == null) {
                    emptyCount++
                } else {
                    if (emptyCount > 0) {
                        fenBuilder.append(emptyCount)
                        emptyCount = 0
                    }
                    fenBuilder.append(pieceToChar(piece))
                }
            }
            if (emptyCount > 0) {
                fenBuilder.append(emptyCount)
            }
            if (rank > 0) fenBuilder.append("/")
        }

        fenBuilder.append(" w KQkq - 0 1") // Default game state
        return fenBuilder.toString()
    }

    private fun charToPiece(char: Char): ChessPiece? {
        val color = if (char.isUpperCase()) PieceColor.WHITE else PieceColor.BLACK
        val type = when (char.lowercaseChar()) {
            'k' -> PieceType.KING
            'q' -> PieceType.QUEEN
            'r' -> PieceType.ROOK
            'b' -> PieceType.BISHOP
            'n' -> PieceType.KNIGHT
            'p' -> PieceType.PAWN
            else -> return null
        }
        val drawableRes = getPieceDrawable(type, color)
        return ChessPiece(type, color, drawableRes)
    }

    private fun pieceToChar(piece: ChessPiece): Char {
        val baseChar = when (piece.type) {
            PieceType.KING -> 'k'
            PieceType.QUEEN -> 'q'
            PieceType.ROOK -> 'r'
            PieceType.BISHOP -> 'b'
            PieceType.KNIGHT -> 'n'
            PieceType.PAWN -> 'p'
        }
        return if (piece.color == PieceColor.WHITE) baseChar.uppercaseChar() else baseChar
    }

    private fun getPieceDrawable(type: PieceType, color: PieceColor): Int {
        return when (type to color) {
            PieceType.KING to PieceColor.WHITE -> R.drawable.wk
            PieceType.KING to PieceColor.BLACK -> R.drawable.bk
            PieceType.QUEEN to PieceColor.WHITE -> R.drawable.wq
            PieceType.QUEEN to PieceColor.BLACK -> R.drawable.bq
            PieceType.ROOK to PieceColor.WHITE -> R.drawable.wr
            PieceType.ROOK to PieceColor.BLACK -> R.drawable.br
            PieceType.BISHOP to PieceColor.WHITE -> R.drawable.wb
            PieceType.BISHOP to PieceColor.BLACK -> R.drawable.bb
            PieceType.KNIGHT to PieceColor.WHITE -> R.drawable.wn
            PieceType.KNIGHT to PieceColor.BLACK -> R.drawable.bn
            PieceType.PAWN to PieceColor.WHITE -> R.drawable.wp
            PieceType.PAWN to PieceColor.BLACK -> R.drawable.bp
            else -> R.drawable.wp // Default fallback
        }
    }

    fun onSquareClick(position: ChessPosition) {
        if (isEditMode) {
            // Edit mode: place selected piece or clear square
            if (selectedPieceForPlacement != null) {
                // Place the selected piece
                board[position.rank][position.file] = selectedPieceForPlacement
            } else {
                // Clear the square if no piece is selected
                board[position.rank][position.file] = null
            }
            // Always update FEN after any board change in edit mode
            updateFEN()
            // Set animation state for expressive feedback
            lastMovedSquare = position
            isAnimatingPiece = true
        } else {
            // Play mode: select and move pieces
            handleMove(position)
        }
    }

    private fun cyclePieceAtPosition(position: ChessPosition) {
        val currentPiece = board[position.rank][position.file]
        board[position.rank][position.file] = when (currentPiece?.let { it.type to it.color }) {
            null -> ChessPiece(PieceType.PAWN, PieceColor.WHITE, R.drawable.wp)
            PieceType.PAWN to PieceColor.WHITE -> ChessPiece(PieceType.ROOK, PieceColor.WHITE, R.drawable.wr)
            PieceType.ROOK to PieceColor.WHITE -> ChessPiece(PieceType.KNIGHT, PieceColor.WHITE, R.drawable.wn)
            PieceType.KNIGHT to PieceColor.WHITE -> ChessPiece(PieceType.BISHOP, PieceColor.WHITE, R.drawable.wb)
            PieceType.BISHOP to PieceColor.WHITE -> ChessPiece(PieceType.QUEEN, PieceColor.WHITE, R.drawable.wq)
            PieceType.QUEEN to PieceColor.WHITE -> ChessPiece(PieceType.KING, PieceColor.WHITE, R.drawable.wk)
            PieceType.KING to PieceColor.WHITE -> ChessPiece(PieceType.PAWN, PieceColor.BLACK, R.drawable.bp)
            PieceType.PAWN to PieceColor.BLACK -> ChessPiece(PieceType.ROOK, PieceColor.BLACK, R.drawable.br)
            PieceType.ROOK to PieceColor.BLACK -> ChessPiece(PieceType.KNIGHT, PieceColor.BLACK, R.drawable.bn)
            PieceType.KNIGHT to PieceColor.BLACK -> ChessPiece(PieceType.BISHOP, PieceColor.BLACK, R.drawable.bb)
            PieceType.BISHOP to PieceColor.BLACK -> ChessPiece(PieceType.QUEEN, PieceColor.BLACK, R.drawable.bq)
            PieceType.QUEEN to PieceColor.BLACK -> ChessPiece(PieceType.KING, PieceColor.BLACK, R.drawable.bk)
            PieceType.KING to PieceColor.BLACK -> null
            else -> null
        }
        updateFEN()
    }

    fun updateFromFEN(fen: String) {
        loadFromFEN(fen)
    }

    fun flipBoard() {
        isBoardFlipped = !isBoardFlipped
    }

    fun getLegalMoves(position: ChessPosition): List<ChessPosition> {
        val piece = board[position.rank][position.file] ?: return emptyList()
        val moves = mutableListOf<ChessPosition>()

        when (piece.type) {
            PieceType.PAWN -> {
                val direction = if (piece.color == PieceColor.WHITE) 1 else -1
                val startRank = if (piece.color == PieceColor.WHITE) 1 else 6

                // Forward move
                val oneStep = ChessPosition(position.file, position.rank + direction)
                if (oneStep.isValid() && board[oneStep.rank][oneStep.file] == null) {
                    moves.add(oneStep)

                    // Two steps from starting position
                    if (position.rank == startRank) {
                        val twoStep = ChessPosition(position.file, position.rank + 2 * direction)
                        if (twoStep.isValid() && board[twoStep.rank][twoStep.file] == null) {
                            moves.add(twoStep)
                        }
                    }
                }

                // Captures (diagonal attacks)
                for (fileOffset in listOf(-1, 1)) {
                    val capturePos = ChessPosition(position.file + fileOffset, position.rank + direction)
                    if (capturePos.isValid()) {
                        val targetPiece = board[capturePos.rank][capturePos.file]
                        if (targetPiece != null && targetPiece.color != piece.color) {
                            moves.add(capturePos)
                        }
                    }
                }
            }

            PieceType.ROOK -> {
                // Horizontal and vertical moves
                val directions = listOf(
                    Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0)
                )
                for ((dx, dy) in directions) {
                    for (i in 1..7) {
                        val newPos = ChessPosition(position.file + dx * i, position.rank + dy * i)
                        if (!newPos.isValid()) break

                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            // Empty square - can move here
                            moves.add(newPos)
                        } else {
                            // Square occupied
                            if (targetPiece.color != piece.color) {
                                // Enemy piece - can capture
                                moves.add(newPos)
                            }
                            // Can't move further in this direction (blocked by piece)
                            break
                        }
                    }
                }
            }

            PieceType.KNIGHT -> {
                // Knight moves in L-shape: 2 squares in one direction, 1 in perpendicular
                val knightMoves = listOf(
                    Pair(2, 1), Pair(2, -1), Pair(-2, 1), Pair(-2, -1),
                    Pair(1, 2), Pair(1, -2), Pair(-1, 2), Pair(-1, -2)
                )
                for ((dx, dy) in knightMoves) {
                    val newPos = ChessPosition(position.file + dx, position.rank + dy)
                    if (newPos.isValid()) {
                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            // Empty square - can move here
                            moves.add(newPos)
                        } else if (targetPiece.color != piece.color) {
                            // Enemy piece - can capture
                            moves.add(newPos)
                        }
                        // If friendly piece, can't move there (no need to add)
                    }
                }
            }

            PieceType.BISHOP -> {
                // Diagonal moves
                val directions = listOf(
                    Pair(1, 1), Pair(1, -1), Pair(-1, 1), Pair(-1, -1)
                )
                for ((dx, dy) in directions) {
                    for (i in 1..7) {
                        val newPos = ChessPosition(position.file + dx * i, position.rank + dy * i)
                        if (!newPos.isValid()) break

                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            // Empty square - can move here
                            moves.add(newPos)
                        } else {
                            // Square occupied
                            if (targetPiece.color != piece.color) {
                                // Enemy piece - can capture
                                moves.add(newPos)
                            }
                            // Can't move further in this direction (blocked by piece)
                            break
                        }
                    }
                }
            }

            PieceType.QUEEN -> {
                // Combination of rook and bishop moves (horizontal, vertical, and diagonal)
                val directions = listOf(
                    Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0),  // Rook moves
                    Pair(1, 1), Pair(1, -1), Pair(-1, 1), Pair(-1, -1)  // Bishop moves
                )
                for ((dx, dy) in directions) {
                    for (i in 1..7) {
                        val newPos = ChessPosition(position.file + dx * i, position.rank + dy * i)
                        if (!newPos.isValid()) break

                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            // Empty square - can move here
                            moves.add(newPos)
                        } else {
                            // Square occupied
                            if (targetPiece.color != piece.color) {
                                // Enemy piece - can capture
                                moves.add(newPos)
                            }
                            // Can't move further in this direction (blocked by piece)
                            break
                        }
                    }
                }
            }

            PieceType.KING -> {
                // King moves one square in any direction
                val kingMoves = listOf(
                    Pair(0, 1), Pair(0, -1), Pair(1, 0), Pair(-1, 0),  // Horizontal and vertical
                    Pair(1, 1), Pair(1, -1), Pair(-1, 1), Pair(-1, -1)  // Diagonal
                )
                for ((dx, dy) in kingMoves) {
                    val newPos = ChessPosition(position.file + dx, position.rank + dy)
                    if (newPos.isValid()) {
                        val targetPiece = board[newPos.rank][newPos.file]
                        if (targetPiece == null) {
                            // Empty square - can move here
                            moves.add(newPos)
                        } else if (targetPiece.color != piece.color) {
                            // Enemy piece - can capture
                            moves.add(newPos)
                        }
                        // If friendly piece, can't move there (no need to add)
                    }
                }
                // TODO: Add castling logic later if needed
            }
        }

        return moves
    }

    fun startDrag(piece: ChessPiece, position: ChessPosition) {
        draggedPiece = piece
        isDragging = true
        dragStartPosition = position
        isDraggingFromTray = false
        // Remove piece from board during drag
        board[position.rank][position.file] = null
        android.util.Log.d("ChessDrag", "Started dragging ${piece.type} from board position ${position.file},${position.rank}")
    }

    fun startDragFromTray(piece: ChessPiece, startOffset: Offset) {
        draggedFromTrayPiece = piece
        isDraggingFromTray = true
        isDragging = true
        dragStartOffset = startOffset
        dragOffset = startOffset
        android.util.Log.d("ChessDrag", "Started dragging ${piece.type} from tray")
    }

    fun updateDragPosition(newOffset: Offset) {
        dragOffset = newOffset
    }

    fun updateBoardBounds(bounds: Rect) {
        boardBounds = bounds
    }

    fun isPositionOnBoard(offset: Offset): Boolean {
        return boardBounds.contains(offset)
    }

    fun getSquareFromOffset(offset: Offset): ChessPosition? {
        if (!isPositionOnBoard(offset)) return null

        val squareSize = boardBounds.width / 8f
        val relativeX = offset.x - boardBounds.left
        val relativeY = offset.y - boardBounds.top

        val file = (relativeX / squareSize).toInt().coerceIn(0, 7)
        val rank = (7 - (relativeY / squareSize).toInt()).coerceIn(0, 7)

        return ChessPosition(file, rank)
    }

    fun endDrag(targetPosition: ChessPosition? = null) {
        val finalTargetPosition = targetPosition ?: getSquareFromOffset(dragOffset)

        if (isDraggingFromTray) {
            // Handle drag from tray
            draggedFromTrayPiece?.let { piece ->
                if (finalTargetPosition != null && finalTargetPosition.isValid()) {
                    if (isEditMode) {
                        // Edit mode: always allow placement from tray
                        board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                        lastMovedSquare = finalTargetPosition
                        isAnimatingPiece = true
                        android.util.Log.d("ChessMove", "Placed ${piece.type} from tray to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                    } else {
                        // Play mode: only allow if it's a legal setup (for analysis)
                        board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                        lastMovedSquare = finalTargetPosition
                        isAnimatingPiece = true
                        android.util.Log.d("ChessMove", "Placed ${piece.type} from tray to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                    }
                }
                // Piece from tray doesn't need to be returned anywhere
            }
        } else {
            // Handle drag from board
            draggedPiece?.let { piece ->
                if (finalTargetPosition != null && finalTargetPosition.isValid()) {
                    if (isEditMode) {
                        // Edit mode: always allow placement
                        board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                        lastMovedSquare = finalTargetPosition
                        isAnimatingPiece = true
                        android.util.Log.d("ChessMove", "Edit mode drag: ${piece.type} to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                    } else {
                        // Play mode: check if the drag move is legal
                        val originalPosition = dragStartPosition
                        if (originalPosition != null) {
                            val legalMoves = getLegalMoves(originalPosition)
                            if (legalMoves.contains(finalTargetPosition)) {
                                // Legal drag move - place piece at target position
                                board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                                lastMovedSquare = finalTargetPosition
                                isAnimatingPiece = true
                                selectedSquare = null // Clear selection after successful move
                                android.util.Log.d("ChessMove", "Legal drag move: ${piece.type} to ${finalTargetPosition.file},${finalTargetPosition.rank}")
                            } else {
                                // Illegal drag move - return piece to original position
                                board[originalPosition.rank][originalPosition.file] = piece
                                android.util.Log.d("ChessMove", "Illegal drag move attempted, piece returned to original position")
                            }
                        } else {
                            // Can't find original position - just place it at target (fallback for edit mode)
                            board[finalTargetPosition.rank][finalTargetPosition.file] = piece
                        }
                    }
                } else {
                    // Dragged outside board - return to original position if possible
                    val originalPosition = dragStartPosition
                    if (originalPosition != null) {
                        board[originalPosition.rank][originalPosition.file] = piece
                    } else {
                        // Can't find original position - piece is lost (shouldn't happen normally)
                        android.util.Log.w("ChessMove", "Could not find original position for dragged piece")
                    }
                }
            }
        }

        // Clean up all drag state
        draggedPiece = null
        draggedFromTrayPiece = null
        isDragging = false
        isDraggingFromTray = false
        dragOffset = Offset.Zero
        dragStartPosition = null
        dragStartOffset = Offset.Zero
        updateFEN()
    }

    private fun findOriginalDragPosition(piece: ChessPiece): ChessPosition? {
        // Look for empty squares that might have been the original position
        // This is a fallback - in practice we should store the original position
        for (rank in 0..7) {
            for (file in 0..7) {
                if (board[rank][file] == null) {
                    // This might be where the piece came from
                    // For now, return the first empty square (not ideal but functional)
                    return ChessPosition(file, rank)
                }
            }
        }
        return null
    }

    // Helper function to find where a piece was originally located
    private fun findPiecePosition(piece: ChessPiece): ChessPosition? {
        // This is a simple implementation - in a real chess engine you'd track this better
        for (rank in 0..7) {
            for (file in 0..7) {
                val boardPiece = board[rank][file]
                if (boardPiece != null &&
                    boardPiece.type == piece.type &&
                    boardPiece.color == piece.color &&
                    boardPiece.drawableRes == piece.drawableRes) {
                    // Found a matching piece - this might be the original position
                    // Note: This is not perfect for identical pieces, but works for basic functionality
                    return ChessPosition(file, rank)
                }
            }
        }
        return null
    }

    // Helper function to check if a piece can be selected (for turn-based play)
    private fun canSelectPiece(piece: ChessPiece?): Boolean {
        // For now, allow selecting any piece (no turn restrictions)
        // Later you can add turn-based logic here
        return piece != null
    }

    private fun handleMove(position: ChessPosition) {
        val selected = selectedSquare
        if (selected == null) {
            // Select piece if there's one at this position and it can be selected
            val piece = board[position.rank][position.file]
            if (piece != null && canSelectPiece(piece)) {
                selectedSquare = position
                android.util.Log.d("ChessMove", "Selected ${piece.color} ${piece.type} at ${position.file},${position.rank}")
            }
        } else {
            if (selected == position) {
                // Deselect if clicking same square
                selectedSquare = null
            } else {
                // Check if the move is legal before executing
                val legalMoves = getLegalMoves(selected)
                if (legalMoves.contains(position)) {
                    // Legal move - execute it
                    val movingPiece = board[selected.rank][selected.file]
                    val capturedPiece = board[position.rank][position.file]

                    // Execute the move
                    board[position.rank][position.file] = movingPiece
                    board[selected.rank][selected.file] = null
                    selectedSquare = null
                    lastMovedSquare = position
                    isAnimatingPiece = true

                    // Log the move for debugging
                    android.util.Log.d("ChessMove", "Legal move: ${movingPiece?.type} from ${selected.file},${selected.rank} to ${position.file},${position.rank}")
                    if (capturedPiece != null) {
                        android.util.Log.d("ChessMove", "Captured: ${capturedPiece.type}")
                    }

                    // Always update FEN after any legal move
                    updateFEN()
                } else {
                    // Illegal move - just deselect or select new piece
                    val pieceAtTarget = board[position.rank][position.file]
                    if (pieceAtTarget != null) {
                        // Select the piece at the target position instead
                        selectedSquare = position
                        android.util.Log.d("ChessMove", "Illegal move attempted, selecting piece at target instead")
                    } else {
                        // Deselect if clicking on empty square with illegal move
                        selectedSquare = null
                        android.util.Log.d("ChessMove", "Illegal move to empty square, deselecting")
                    }
                }
            }
        }
    }
}

@Composable
fun InvisibleChessSquare(
    position: ChessPosition,
    piece: ChessPiece?,
    isSelected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    boardState: ChessBoardState? = null,
    isLegalMove: Boolean = false
) {
    // Animated background color with expressive transitions
    val backgroundColor by animateColorAsState(
        targetValue = when {
            isSelected -> Color(0xFF769656).copy(alpha = 0.8f)
            isLegalMove -> Color(0xFF90EE90).copy(alpha = 0.7f)
            boardState?.lastMovedSquare == position -> Color(0xFFFFD700).copy(alpha = 0.6f) // Gold for last moved
            else -> Color.Transparent
        },
        animationSpec = tween(300, easing = EaseOutCubic),
        label = "square_background"
    )

    // Scale animation for piece placement
    val scale by animateFloatAsState(
        targetValue = if (boardState?.lastMovedSquare == position && boardState.isAnimatingPiece) 1.1f else 1f,
        animationSpec = ChessExpressiveAnimations.responsiveSpring,
        label = "piece_placement_scale"
    )

    // Reset animation state after animation completes
    LaunchedEffect(boardState?.animationTrigger) {
        if (boardState?.isAnimatingPiece == true) {
            kotlinx.coroutines.delay(500)
            boardState.isAnimatingPiece = false
        }
    }

    Box(
        modifier = modifier
            .background(backgroundColor)
            .expressiveClickable(onClick = onClick)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .let { mod ->
                if (piece != null) {
                    // Add breakthrough drag functionality with proper coordinate handling
                    mod.pointerInput(piece, position) {
                        detectDragGestures(
                            onDragStart = { offset ->
                                boardState?.startDrag(piece, position)
                                // Set initial drag position to the center of the current square
                                val squareSize = size.width / 8f
                                val initialOffset = Offset(
                                    (position.file + 0.5f) * squareSize,
                                    ((7 - position.rank) + 0.5f) * squareSize
                                )
                                boardState?.updateDragPosition(initialOffset)
                            },
                            onDragEnd = {
                                boardState?.endDrag()
                            },
                            onDrag = { _, dragAmount ->
                                boardState?.let {
                                    it.updateDragPosition(it.dragOffset + dragAmount)
                                }
                            }
                        )
                    }
                } else mod
            },
        contentAlignment = Alignment.Center
    ) {
        // Enhanced legal move indicator with animation
        AnimatedVisibility(
            visible = piece == null && isLegalMove,
            enter = ChessExpressiveAnimations.scaleIn,
            exit = ChessExpressiveAnimations.scaleOut
        ) {
            Box(
                modifier = Modifier
                    .size(14.dp)
                    .background(
                        Color(0xFF4a5c2a),
                        CircleShape
                    )
                    .breathingAnimation(
                        enabled = true,
                        minAlpha = 0.6f,
                        maxAlpha = 1f,
                        duration = 1500
                    )
            )
        }

        // Enhanced piece display with optimized animations
        piece?.let { chessPiece ->
            // Only show piece if it's not being dragged
            val isDraggedPiece = boardState?.isDragging == true && boardState.draggedPiece == chessPiece

            AnimatedVisibility(
                visible = !isDraggedPiece,
                enter = fadeIn(animationSpec = tween(200)),
                exit = fadeOut(animationSpec = tween(200))
            ) {
                Image(
                    painter = painterResource(id = chessPiece.drawableRes),
                    contentDescription = "${chessPiece.color} ${chessPiece.type}",
                    modifier = Modifier
                        .size(36.dp)
                        .let { mod ->
                            // Only add floating animation for recently moved pieces
                            if (boardState?.lastMovedSquare == position && boardState.isAnimatingPiece) {
                                mod.graphicsLayer {
                                    scaleX = 1.1f
                                    scaleY = 1.1f
                                }
                            } else mod
                        }
                )
            }
        }
    }
}

@Composable
fun PieceTrayItem(
    piece: ChessPiece,
    isSelected: Boolean,
    onClick: () -> Unit,
    boardState: ChessBoardState? = null
) {
    val adaptiveLayout = rememberAdaptiveLayout()

    // Optimized selection state with reduced animations
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.05f else 1f,
        animationSpec = tween(200, easing = EaseOutCubic),
        label = "piece_selection_scale"
    )

    val elevation by animateDpAsState(
        targetValue = if (isSelected) 6.dp else 2.dp,
        animationSpec = tween(200, easing = EaseOutCubic),
        label = "piece_selection_elevation"
    )

    Card(
        onClick = onClick,
        modifier = Modifier
            .size(if (adaptiveLayout.isCompact) 40.dp else 48.dp)
            .expressiveClickable(onClick = onClick)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .let { mod ->
                // Add breakthrough drag from tray functionality
                if (boardState != null) {
                    mod.pointerInput(piece) {
                        detectDragGestures(
                            onDragStart = { offset ->
                                // Start dragging from tray with proper initial position
                                boardState.startDragFromTray(piece, offset)
                            },
                            onDragEnd = {
                                boardState.endDrag()
                            },
                            onDrag = { _, dragAmount ->
                                boardState.updateDragPosition(boardState.dragOffset + dragAmount)
                            }
                        )
                    }
                } else mod
            },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected)
                MaterialTheme.colorScheme.primary
            else
                MaterialTheme.colorScheme.surface.copy(alpha = 0.9f)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = elevation),
        shape = RoundedCornerShape(12.dp)
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    if (isSelected) {
                        Brush.radialGradient(
                            colors = listOf(
                                Color.White.copy(alpha = 0.2f),
                                Color.Transparent
                            )
                        )
                    } else Brush.linearGradient(listOf(Color.Transparent, Color.Transparent))
                ),
            contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = piece.drawableRes),
                contentDescription = "${piece.color} ${piece.type}",
                modifier = Modifier
                    .size(if (adaptiveLayout.isCompact) 28.dp else 32.dp)
            )
        }
    }
}

@Composable
fun rememberChessBoardState(): ChessBoardState {
    return remember { ChessBoardState() }
}

@Composable
fun InteractiveChessBoard(
    modifier: Modifier = Modifier,
    boardState: ChessBoardState = rememberChessBoardState(),
    onFENChanged: (String) -> Unit = {}
) {
    LaunchedEffect(boardState.currentFENString) {
        onFENChanged(boardState.currentFENString)
    }

    // Simple non-scrollable chess board - just the board itself
    ChessBoardGrid(
        boardState = boardState,
        modifier = modifier
    )
}

@Composable
fun ChessBoardGrid(
    boardState: ChessBoardState,
    modifier: Modifier = Modifier
) {
    val adaptiveLayout = rememberAdaptiveLayout()

    Card(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1f)
            .padding(horizontal = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        )
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .onGloballyPositioned { coordinates ->
                    // Capture board bounds for accurate drag targeting
                    val bounds = Rect(
                        offset = Offset(
                            coordinates.positionInRoot().x,
                            coordinates.positionInRoot().y
                        ),
                        size = androidx.compose.ui.geometry.Size(
                            coordinates.size.width.toFloat(),
                            coordinates.size.height.toFloat()
                        )
                    )
                    boardState.updateBoardBounds(bounds)
                }
        ) {
            // Chess board background PNG
            Image(
                painter = painterResource(id = R.drawable.chessboard_background),
                contentDescription = "Chess Board Background",
                modifier = Modifier.fillMaxSize(),
                contentScale = androidx.compose.ui.layout.ContentScale.FillBounds
            )

            // 8x8 Grid of squares that perfectly divide the board
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                // Calculate legal moves for selected piece
                val legalMoves = boardState.selectedSquare?.let { selected ->
                    if (!boardState.isEditMode) {
                        boardState.getLegalMoves(selected)
                    } else emptyList()
                } ?: emptyList()

                // Rank range based on board orientation
                val rankRange = if (boardState.isBoardFlipped) (0..7) else (7 downTo 0)
                val fileRange = if (boardState.isBoardFlipped) (7 downTo 0) else (0..7)

                for (rank in rankRange) {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f), // Each row takes exactly 1/8 of the height
                        horizontalArrangement = Arrangement.SpaceEvenly
                    ) {
                        for (file in fileRange) {
                            val currentPosition = ChessPosition(file, rank)
                            InvisibleChessSquare(
                                position = currentPosition,
                                piece = boardState.board[rank][file],
                                isSelected = boardState.selectedSquare == currentPosition,
                                onClick = { boardState.onSquareClick(currentPosition) },
                                modifier = Modifier
                                    .weight(1f) // Each square takes exactly 1/8 of the width
                                    .fillMaxHeight(), // Fill the entire height of the row
                                boardState = boardState,
                                isLegalMove = legalMoves.contains(currentPosition)
                            )
                        }
                    }
                }
            }

        // Enhanced floating dragged piece overlay with smooth animations
        if (boardState.isDragging) {
            val draggedPiece = boardState.draggedPiece ?: boardState.draggedFromTrayPiece
            draggedPiece?.let { piece ->
                // Smooth drag shadow effect
                Image(
                    painter = painterResource(id = piece.drawableRes),
                    contentDescription = "Dragged piece shadow",
                    modifier = Modifier
                        .size(44.dp)
                        .offset {
                            IntOffset(
                                (boardState.dragOffset.x + 2).toInt(),
                                (boardState.dragOffset.y + 2).toInt()
                            )
                        }
                        .graphicsLayer {
                            alpha = 0.3f
                            scaleX = 1.3f
                            scaleY = 1.3f
                        }
                )

                // Main dragged piece
                Image(
                    painter = painterResource(id = piece.drawableRes),
                    contentDescription = "Dragged piece",
                    modifier = Modifier
                        .size(42.dp)
                        .offset {
                            IntOffset(
                                (boardState.dragOffset.x - 21).toInt(), // Center the piece
                                (boardState.dragOffset.y - 21).toInt()
                            )
                        }
                        .graphicsLayer {
                            alpha = 0.9f
                            scaleX = 1.25f
                            scaleY = 1.25f
                            rotationZ = if (boardState.isDraggingFromTray) 5f else 0f
                        }
                )
            }
        }
    }
}

@Composable
fun PieceTray(
    selectedPiece: ChessPiece?,
    onPieceSelected: (ChessPiece?) -> Unit,
    showBlackFirst: Boolean = false,
    showOnlyWhite: Boolean = false,
    showOnlyBlack: Boolean = false,
    boardState: ChessBoardState? = null
) {
    val adaptiveLayout = rememberAdaptiveLayout()
    val expressiveSurfaces = LocalChessExpressiveSurfaces.current

    // Animated visibility for the tray
    AnimatedVisibility(
        visible = true,
        enter = ChessExpressiveAnimations.slideInFromBottom,
        exit = ChessExpressiveAnimations.scaleOut
    ) {
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .shadow(
                    elevation = 8.dp,
                    shape = RoundedCornerShape(20.dp),
                    ambientColor = Color.Black.copy(alpha = 0.1f),
                    spotColor = Color.Black.copy(alpha = 0.2f)
                ),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.95f)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 0.dp), // Using custom shadow
            shape = RoundedCornerShape(20.dp)
        ) {
            // Glass morphism overlay
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(expressiveSurfaces.glassEffect)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(adaptiveLayout.cardPadding)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = when {
                                showOnlyWhite -> "White Pieces"
                                showOnlyBlack -> "Black Pieces"
                                else -> "Piece Tray"
                            },
                            style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
                            color = MaterialTheme.colorScheme.onSurfaceVariant
                        )

                        // Clear selection button with expressive animation
                        Card(
                            onClick = { onPieceSelected(null) },
                            modifier = Modifier.expressiveClickable { onPieceSelected(null) },
                            colors = CardDefaults.cardColors(
                                containerColor = if (selectedPiece == null)
                                    MaterialTheme.colorScheme.primary
                                else
                                    MaterialTheme.colorScheme.surface
                            ),
                            elevation = CardDefaults.cardElevation(
                                defaultElevation = if (selectedPiece == null) 6.dp else 2.dp
                            ),
                            shape = RoundedCornerShape(12.dp)
                        ) {
                            Box(
                                modifier = Modifier
                                    .padding(8.dp)
                                    .animateContentSize(
                                        animationSpec = tween(300, easing = EaseOutCubic)
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Clear,
                                    contentDescription = "Clear",
                                    modifier = Modifier
                                        .size(16.dp)
                                        .graphicsLayer {
                                            rotationZ = if (selectedPiece == null) 0f else 45f
                                        },
                                    tint = if (selectedPiece == null)
                                        MaterialTheme.colorScheme.onPrimary
                                    else
                                        MaterialTheme.colorScheme.onSurface
                                )
                            }
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

            // Piece sections based on parameters
            val whitePieces = listOf(
                ChessPiece(PieceType.PAWN, PieceColor.WHITE, R.drawable.wp),
                ChessPiece(PieceType.ROOK, PieceColor.WHITE, R.drawable.wr),
                ChessPiece(PieceType.KNIGHT, PieceColor.WHITE, R.drawable.wn),
                ChessPiece(PieceType.BISHOP, PieceColor.WHITE, R.drawable.wb),
                ChessPiece(PieceType.QUEEN, PieceColor.WHITE, R.drawable.wq),
                ChessPiece(PieceType.KING, PieceColor.WHITE, R.drawable.wk)
            )

            val blackPieces = listOf(
                ChessPiece(PieceType.PAWN, PieceColor.BLACK, R.drawable.bp),
                ChessPiece(PieceType.ROOK, PieceColor.BLACK, R.drawable.br),
                ChessPiece(PieceType.KNIGHT, PieceColor.BLACK, R.drawable.bn),
                ChessPiece(PieceType.BISHOP, PieceColor.BLACK, R.drawable.bb),
                ChessPiece(PieceType.QUEEN, PieceColor.BLACK, R.drawable.bq),
                ChessPiece(PieceType.KING, PieceColor.BLACK, R.drawable.bk)
            )

            // Show pieces based on parameters
            when {
                showOnlyBlack -> {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        items(blackPieces) { piece ->
                            PieceTrayItem(
                                piece = piece,
                                isSelected = selectedPiece?.let { it.type == piece.type && it.color == piece.color } == true,
                                onClick = { onPieceSelected(piece) },
                                boardState = boardState
                            )
                        }
                    }
                }
                showOnlyWhite -> {
                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        items(whitePieces) { piece ->
                            PieceTrayItem(
                                piece = piece,
                                isSelected = selectedPiece?.let { it.type == piece.type && it.color == piece.color } == true,
                                onClick = { onPieceSelected(piece) },
                                boardState = boardState
                            )
                        }
                    }
                }
                else -> {
                    // Show both colors, black first if specified
                    val firstColor = if (showBlackFirst) blackPieces else whitePieces
                    val secondColor = if (showBlackFirst) whitePieces else blackPieces
                    val firstName = if (showBlackFirst) "Black Pieces" else "White Pieces"
                    val secondName = if (showBlackFirst) "White Pieces" else "Black Pieces"

                    // First color section
                    Text(
                        text = firstName,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
                        modifier = Modifier.padding(bottom = 4.dp)
                    )

                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        items(firstColor) { piece ->
                            PieceTrayItem(
                                piece = piece,
                                isSelected = selectedPiece?.let { it.type == piece.type && it.color == piece.color } == true,
                                onClick = { onPieceSelected(piece) },
                                boardState = boardState
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // Second color section
                    Text(
                        text = secondName,
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.8f),
                        modifier = Modifier.padding(bottom = 4.dp)
                    )

                    LazyRow(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(6.dp)
                    ) {
                        items(secondColor) { piece ->
                            PieceTrayItem(
                                piece = piece,
                                isSelected = selectedPiece?.let { it.type == piece.type && it.color == piece.color } == true,
                                onClick = { onPieceSelected(piece) },
                                boardState = boardState
                            )
                        }
                    }
                }
            }
            }
        }
    }
}



@Composable
fun ChessBoardControls(boardState: ChessBoardState) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF262421)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Edit mode toggle - professional style
            Card(
                onClick = { boardState.isEditMode = !boardState.isEditMode },
                modifier = Modifier.weight(1f),
                colors = CardDefaults.cardColors(
                    containerColor = if (boardState.isEditMode) Color(0xFF769656) else Color(0xFF3a3a3a)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp),
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (boardState.isEditMode) Icons.Default.Edit else Icons.Default.PlayArrow,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp),
                        tint = Color.White
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (boardState.isEditMode) "Edit" else "Play",
                        style = MaterialTheme.typography.bodyMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        color = Color.White
                    )
                }
            }

            // Reset button - professional style
            Card(
                onClick = { boardState.setupInitialPosition() },
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF3a3a3a)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Box(
                    modifier = Modifier.padding(12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Refresh,
                        contentDescription = "Reset",
                        modifier = Modifier.size(20.dp),
                        tint = Color.White
                    )
                }
            }

            // Flip board button - professional style
            Card(
                onClick = { boardState.flipBoard() },
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF3a3a3a)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Box(
                    modifier = Modifier.padding(12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.FlipToBack,
                        contentDescription = "Flip Board",
                        modifier = Modifier.size(20.dp),
                        tint = Color.White
                    )
                }
            }

            // Load Test Position button
            Card(
                onClick = {
                    // Load a test position that matches your chessboard PNG
                    val testFEN = "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1"
                    boardState.loadFromFEN(testFEN)
                },
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF00E676)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Box(
                    modifier = Modifier.padding(12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Image,
                        contentDescription = "Load Test Position",
                        modifier = Modifier.size(20.dp),
                        tint = Color.Black
                    )
                }
            }

            // Clear button - professional style
            Card(
                onClick = {
                    boardState.board = Array(8) { Array<ChessPiece?>(8) { null } }
                    boardState.currentFENString = boardState.getCurrentFEN()
                },
                colors = CardDefaults.cardColors(
                    containerColor = Color(0xFF3a3a3a)
                ),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
            ) {
                Box(
                    modifier = Modifier.padding(12.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        imageVector = Icons.Default.Clear,
                        contentDescription = "Clear",
                        modifier = Modifier.size(20.dp),
                        tint = Color.White
                    )
                }
            }
        }
    }
}








@Composable
fun FENDisplay(
    fen: String,
    onFenChanged: (String) -> Unit
) {
    var editableFen by remember { mutableStateOf(fen) }
    var isEditing by remember { mutableStateOf(false) }

    // Update editable FEN when external FEN changes
    LaunchedEffect(fen) {
        if (!isEditing) {
            editableFen = fen
        }
    }

    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF262421).copy(alpha = 0.95f)
        ),
        shape = RoundedCornerShape(20.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Current Position (FEN)",
                    style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
                    color = Color.White
                )

                // Edit/Save button
                Card(
                    onClick = {
                        if (isEditing) {
                            onFenChanged(editableFen)
                            isEditing = false
                        } else {
                            isEditing = true
                        }
                    },
                    colors = CardDefaults.cardColors(
                        containerColor = if (isEditing) Color(0xFF769656) else Color(0xFF3a3a3a)
                    ),
                    shape = RoundedCornerShape(12.dp)
                ) {
                    Box(
                        modifier = Modifier.padding(10.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = if (isEditing) Icons.Default.Check else Icons.Default.Edit,
                            contentDescription = if (isEditing) "Save" else "Edit",
                            modifier = Modifier.size(18.dp),
                            tint = Color.White
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            if (isEditing) {
                OutlinedTextField(
                    value = editableFen,
                    onValueChange = { editableFen = it },
                    modifier = Modifier.fillMaxWidth(),
                    textStyle = MaterialTheme.typography.bodyMedium.copy(
                        color = Color.White
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color(0xFFE0E0E0),
                        focusedBorderColor = Color(0xFF769656),
                        unfocusedBorderColor = Color(0xFF3a3a3a),
                        cursorColor = Color(0xFF769656)
                    ),
                    shape = RoundedCornerShape(12.dp)
                )
            } else {
                Text(
                    text = editableFen,
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFE0E0E0),
                    modifier = Modifier
                        .fillMaxWidth()
                        .background(
                            Color(0xFF1a1a1a).copy(alpha = 0.8f),
                            RoundedCornerShape(12.dp)
                        )
                        .padding(16.dp)
                        .clickable { isEditing = true }
                )
            }
        }
    }
}

// End of file