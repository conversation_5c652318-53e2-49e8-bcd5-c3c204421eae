package com.chessvision.app

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.core.view.WindowCompat
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowInsetsControllerCompat
import androidx.core.view.WindowInsetsCompat
import androidx.compose.ui.platform.LocalContext
import android.content.Intent
import android.provider.MediaStore
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.compose.rememberLauncherForActivityResult
import android.widget.Toast
import android.Manifest
import androidx.compose.runtime.DisposableEffect
import androidx.core.content.ContextCompat
import android.content.pm.PackageManager
import androidx.core.app.ActivityCompat
import androidx.activity.compose.BackHandler
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.chessvision.app.ui.theme.ChessVisionAppTheme
import kotlinx.coroutines.launch
import kotlinx.coroutines.delay

class MainActivity : ComponentActivity() {
    private lateinit var chessAI: ChessAI

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        // Initialize Chess AI
        chessAI = ChessAI(this)

        // Configure window for immersive experience
        WindowCompat.setDecorFitsSystemWindows(window, false)

        setContent {
            ChessVisionAppTheme {
                val view = LocalView.current

                // Configure system UI for immersive experience
                DisposableEffect(Unit) {
                    val window = (view.context as ComponentActivity).window
                    val insetsController = WindowCompat.getInsetsController(window, view)

                    // Configure appearance
                    insetsController.isAppearanceLightStatusBars = false
                    insetsController.isAppearanceLightNavigationBars = false

                    // Hide navigation bar completely
                    insetsController.hide(WindowInsetsCompat.Type.navigationBars())

                    // Set behavior for immersive mode
                    insetsController.systemBarsBehavior =
                        WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE

                    onDispose {
                        // Restore navigation bar when leaving the app
                        insetsController.show(WindowInsetsCompat.Type.navigationBars())
                    }
                }

                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    MainScreen(chessAI = chessAI)
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(chessAI: ChessAI) {
    var currentScreen by remember { mutableStateOf("main") }
    var isVisible by remember { mutableStateOf(false) }
    var isAIInitialized by remember { mutableStateOf(false) }
    var capturedFEN by remember { mutableStateOf<String?>(null) }
    var isProcessingImage by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    val scope = rememberCoroutineScope()

    // Initialize AI models on first launch
    LaunchedEffect(Unit) {
        delay(300)
        isVisible = true

        // Initialize AI models in background
        scope.launch {
            isAIInitialized = chessAI.initializeModels()
        }
    }

    when (currentScreen) {
        "main" -> MainHomeScreen(
            isVisible = isVisible,
            isAIInitialized = isAIInitialized,
            chessAI = chessAI,
            onNavigateToCamera = { currentScreen = "camera" },
            onNavigateToBoard = { currentScreen = "board" }
        )
        "camera" -> {
            // Handle back button press in camera screen
            BackHandler {
                currentScreen = "main"
            }
            CameraScreen(
                onBackPressed = {
                    isProcessingImage = false
                    currentScreen = "main"
                },
                onImageCaptured = { imagePath ->
                    // Show loading state and process the captured image with our AI
                    isProcessingImage = true
                    scope.launch {
                        try {
                            android.util.Log.d("MainActivity", "🔄 Starting AI processing for image: $imagePath")
                            val result = chessAI.generateFEN(imagePath)
                            when (result) {
                                is ChessAnalysisResult.Success -> {
                                    // Navigate to board with AI-generated FEN
                                    capturedFEN = result.fen
                                    isProcessingImage = false
                                    currentScreen = "board"

                                    // Log the successful AI processing
                                    android.util.Log.d("MainActivity", "🎉 AI generated FEN: ${result.fen}")
                                    android.util.Log.d("MainActivity", "📊 Confidence: ${result.confidence}")
                                    android.util.Log.d("MainActivity", "⚡ Processing time: ${result.processingTimeMs}ms")
                                }
                                is ChessAnalysisResult.Error -> {
                                    android.util.Log.e("MainActivity", "❌ AI processing failed: ${result.message}")
                                    // Show error dialog and stay on main screen
                                    errorMessage = result.message
                                    isProcessingImage = false
                                    currentScreen = "main"
                                }
                            }
                        } catch (e: Exception) {
                            android.util.Log.e("MainActivity", "❌ Exception during AI processing", e)
                            errorMessage = "Unexpected error during AI processing: ${e.message}"
                            isProcessingImage = false
                            currentScreen = "main"
                        }
                    }
                },
                isProcessing = isProcessingImage
            )
        }
        "board" -> {
            // Handle back button press in board screen
            BackHandler {
                currentScreen = "main"
            }
            ChessBoardScreen(
                onBackPressed = {
                    capturedFEN = null
                    currentScreen = "main"
                },
                initialFEN = capturedFEN,
                onFENChanged = { newFEN ->
                    // Update captured FEN when board changes
                    capturedFEN = newFEN
                }
            )
        }
    }

    // Error Dialog
    errorMessage?.let { message ->
        AlertDialog(
            onDismissRequest = { errorMessage = null },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.Error,
                        contentDescription = "Error",
                        tint = Color(0xFFE57373)
                    )
                    Text(
                        text = "AI Processing Error",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Bold
                        )
                    )
                }
            },
            text = {
                Text(
                    text = message,
                    style = MaterialTheme.typography.bodyMedium
                )
            },
            confirmButton = {
                Card(
                    onClick = { errorMessage = null },
                    colors = CardDefaults.cardColors(
                        containerColor = Color(0xFF769656)
                    ),
                    shape = RoundedCornerShape(8.dp)
                ) {
                    Text(
                        text = "OK",
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                        color = Color.White,
                        fontWeight = FontWeight.Bold
                    )
                }
            },
            containerColor = Color(0xFF262421),
            titleContentColor = Color.White,
            textContentColor = Color(0xFFE0E0E0)
        )
    }
}

@Composable
fun MainHomeScreen(
    isVisible: Boolean,
    isAIInitialized: Boolean,
    chessAI: ChessAI,
    onNavigateToCamera: () -> Unit,
    onNavigateToBoard: () -> Unit
) {

    // Professional chess app design inspired by chess.com
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF312e2b)) // Chess.com's dark brown background
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            // Top Header Section
            AnimatedVisibility(
                visible = isVisible,
                enter = slideInVertically(
                    initialOffsetY = { -it },
                    animationSpec = tween(800, easing = EaseOutCubic)
                ) + fadeIn(animationSpec = tween(800))
            ) {
                ChessAppHeader()
            }

            // Main Content
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(20.dp)
            ) {
                item { Spacer(modifier = Modifier.height(8.dp)) }

                // Quick Actions Section
                item {
                    AnimatedVisibility(
                        visible = isVisible,
                        enter = slideInVertically(
                            initialOffsetY = { it },
                            animationSpec = tween(800, delayMillis = 200, easing = EaseOutCubic)
                        ) + fadeIn(animationSpec = tween(800, delayMillis = 200))
                    ) {
                        QuickActionsSection(
                        onNavigateToCamera = onNavigateToCamera,
                        onNavigateToBoard = onNavigateToBoard,
                        isAIInitialized = isAIInitialized
                    )
                    }
                }

                // Features Section
                item {
                    AnimatedVisibility(
                        visible = isVisible,
                        enter = slideInVertically(
                            initialOffsetY = { it },
                            animationSpec = tween(800, delayMillis = 400, easing = EaseOutCubic)
                        ) + fadeIn(animationSpec = tween(800, delayMillis = 400))
                    ) {
                        FeaturesSection(chessAI = chessAI)
                    }
                }

                item { Spacer(modifier = Modifier.height(32.dp)) }
            }
        }
    }
}

@Composable
fun ChessAppHeader() {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF262421) // Darker brown for cards
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column {
                Text(
                    text = "Chess Vision",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        fontSize = 24.sp
                    ),
                    color = Color.White
                )
                Text(
                    text = "AI Position Scanner",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFb8b5b2) // Chess.com's light gray
                )
            }

            // Chess piece icon
            Box(
                modifier = Modifier
                    .size(56.dp)
                    .background(
                        Color(0xFF769656), // Chess.com's green
                        CircleShape
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "♔",
                    style = MaterialTheme.typography.headlineLarge.copy(
                        fontSize = 32.sp
                    ),
                    color = Color.White
                )
            }
        }
    }
}

@Composable
fun QuickActionsSection(
    onNavigateToCamera: () -> Unit,
    onNavigateToBoard: () -> Unit,
    isAIInitialized: Boolean
) {
    val context = LocalContext.current

    // Permission launcher
    val permissionLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // Permission granted, navigate to camera immediately
            onNavigateToCamera()
        } else {
            Toast.makeText(context, "Camera permission required for scanning", Toast.LENGTH_LONG).show()
        }
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "Quick Actions",
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold
            ),
            color = Color.White,
            modifier = Modifier.padding(horizontal = 4.dp)
        )

        // Main scan button - prominent like chess.com's "Play" button
        Card(
            onClick = {
                if (isAIInitialized) {
                    when (ContextCompat.checkSelfPermission(context, Manifest.permission.CAMERA)) {
                        PackageManager.PERMISSION_GRANTED -> onNavigateToCamera()
                        else -> permissionLauncher.launch(Manifest.permission.CAMERA)
                    }
                } else {
                    Toast.makeText(context, "AI models are still loading...", Toast.LENGTH_SHORT).show()
                }
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(100.dp),
            colors = CardDefaults.cardColors(
                containerColor = if (isAIInitialized) Color(0xFF769656) else Color.Gray
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 6.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(20.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.CameraAlt,
                    contentDescription = "Scan",
                    modifier = Modifier.size(40.dp),
                    tint = Color.White
                )

                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "Scan Chess Board",
                        style = MaterialTheme.typography.titleLarge.copy(
                            fontWeight = FontWeight.Bold,
                            fontSize = 20.sp
                        ),
                        color = Color.White
                    )
                    Text(
                        text = if (isAIInitialized) "Instant AI position recognition" else "Loading AI models...",
                        style = MaterialTheme.typography.bodyMedium,
                        color = Color.White.copy(alpha = 0.9f)
                    )
                }

                Icon(
                    imageVector = Icons.AutoMirrored.Filled.ArrowForward,
                    contentDescription = "Go",
                    modifier = Modifier.size(24.dp),
                    tint = Color.White
                )
            }
        }

        // Secondary actions in a row
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            ChessActionCard(
                title = "Chess Board",
                icon = Icons.Default.GridOn,
                modifier = Modifier.weight(1f),
                onClick = onNavigateToBoard
            )

            ChessActionCard(
                title = "Analysis",
                icon = Icons.Default.Psychology,
                modifier = Modifier.weight(1f),
                onClick = {
                    Toast.makeText(context, "Analysis coming soon!", Toast.LENGTH_SHORT).show()
                }
            )
        }
    }
}

@Composable
fun ChessActionCard(
    title: String,
    icon: ImageVector,
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        modifier = modifier.height(80.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFF262421)
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(24.dp),
                tint = Color(0xFF769656)
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = title,
                style = MaterialTheme.typography.bodyMedium.copy(
                    fontWeight = FontWeight.Medium
                ),
                color = Color.White,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
fun FeaturesSection(chessAI: ChessAI) {
    val modelInfo = chessAI.getModelInfo()
    Column(
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Text(
            text = "Features",
            style = MaterialTheme.typography.titleLarge.copy(
                fontWeight = FontWeight.Bold
            ),
            color = Color.White,
            modifier = Modifier.padding(horizontal = 4.dp)
        )

        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = Color(0xFF262421)
            ),
            elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
        ) {
            Column(
                modifier = Modifier.padding(20.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                FeatureItem(
                    icon = "🔬",
                    title = "Breakthrough U-Net V6 Segmentation",
                    description = "${String.format("%.4f", modelInfo.diceScore)} Dice score chess board detection with 5-scale fusion"
                )
                FeatureItem(
                    icon = "🎯",
                    title = "YOLO11n Piece Detection",
                    description = "${String.format("%.1f", modelInfo.map50Accuracy)}% mAP50 accuracy with 2.6M parameter lightweight model"
                )
                FeatureItem(
                    icon = "📱",
                    title = "Mobile-Optimized AI",
                    description = "${String.format("%.2f", modelInfo.totalPackageSizeMB)} MB total package with FP16 quantization"
                )
                FeatureItem(
                    icon = "⚡",
                    title = "${modelInfo.expectedInferenceTimeMs}ms Total Inference",
                    description = "Board detection (300ms) + Piece detection (200ms) on mobile CPU"
                )
                FeatureItem(
                    icon = "🎮",
                    title = "FEN Notation Generation",
                    description = "Instant chess position conversion to standard FEN format"
                )
                FeatureItem(
                    icon = if (modelInfo.isInitialized) "✅" else "⏳",
                    title = "AI Status",
                    description = if (modelInfo.isInitialized) "Models loaded and ready" else "Loading AI models..."
                )
            }
        }
    }
}

@Composable
fun FeatureItem(
    icon: String,
    title: String,
    description: String
) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = icon,
            style = MaterialTheme.typography.headlineMedium,
            modifier = Modifier.size(32.dp)
        )

        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.SemiBold
                ),
                color = Color.White
            )
            Text(
                text = description,
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFb8b5b2)
            )
        }
    }
}

@Composable
fun ChessBoardScreen(
    onBackPressed: () -> Unit,
    initialFEN: String? = null,
    onFENChanged: (String) -> Unit = {}
) {
    val boardState = rememberChessBoardState()

    // Set up FEN update callback for real-time synchronization
    LaunchedEffect(Unit) {
        boardState.setFENUpdateCallback(onFENChanged)
    }

    // Load initial FEN if provided
    LaunchedEffect(initialFEN) {
        initialFEN?.let { fen ->
            boardState.updateFromFEN(fen)
            android.util.Log.d("ChessBoardScreen", "🎯 Loaded initial FEN: $fen")
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF312e2b))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding()
        ) {
            // Modern Header with Gradient Background - Cutting Edge Design
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.horizontalGradient(
                            colors = listOf(
                                Color(0xFF769656),
                                Color(0xFF4a5c2a)
                            )
                        ),
                        shape = RoundedCornerShape(
                            bottomStart = 24.dp,
                            bottomEnd = 24.dp
                        )
                    )
                    .padding(horizontal = 20.dp, vertical = 24.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Modern Back Button with Glass Effect
                    Card(
                        onClick = onBackPressed,
                        colors = CardDefaults.cardColors(
                            containerColor = Color.White.copy(alpha = 0.15f)
                        ),
                        shape = CircleShape,
                        elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(48.dp)
                                .background(
                                    Color.White.copy(alpha = 0.1f),
                                    CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                modifier = Modifier.size(24.dp),
                                tint = Color.White
                            )
                        }
                    }

                    // Title Section with Modern Typography
                    Column(
                        modifier = Modifier.weight(1f)
                    ) {
                        Text(
                            text = "Interactive Chess Board",
                            style = MaterialTheme.typography.headlineSmall.copy(
                                fontWeight = FontWeight.ExtraBold,
                                fontSize = 22.sp
                            ),
                            color = Color.White
                        )
                        Text(
                            text = "Play moves • Edit positions • Analyze",
                            style = MaterialTheme.typography.bodyMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            color = Color.White.copy(alpha = 0.9f)
                        )
                    }

                    // Modern Chess Piece Icon with Glow Effect
                    Box(
                        modifier = Modifier
                            .size(56.dp)
                            .background(
                                Color.White.copy(alpha = 0.2f),
                                CircleShape
                            )
                            .padding(2.dp)
                            .background(
                                Color.White.copy(alpha = 0.1f),
                                CircleShape
                            ),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = "♛",
                            style = MaterialTheme.typography.headlineLarge.copy(
                                fontSize = 32.sp,
                                fontWeight = FontWeight.Bold
                            ),
                            color = Color.White
                        )
                    }
                }
            }

            // Scrollable content with all chess board elements
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 20.dp, vertical = 16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Control Panel with Modern Glass Effect
                item {
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(
                            containerColor = Color(0xFF262421).copy(alpha = 0.9f)
                        ),
                        shape = RoundedCornerShape(16.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                    ) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            // Play Mode Button
                            ModernControlButton(
                                icon = Icons.Default.PlayArrow,
                                label = "Play",
                                isActive = !boardState.isEditMode,
                                onClick = { boardState.isEditMode = false }
                            )

                            // Edit Mode Button
                            ModernControlButton(
                                icon = Icons.Default.Edit,
                                label = "Edit",
                                isActive = boardState.isEditMode,
                                onClick = { boardState.isEditMode = true }
                            )

                            // Reset Button
                            ModernControlButton(
                                icon = Icons.Default.Refresh,
                                label = "Reset",
                                isActive = false,
                                onClick = { boardState.setupInitialPosition() }
                            )

                            // Flip Board Button
                            ModernControlButton(
                                icon = Icons.Default.FlipToBack,
                                label = "Flip",
                                isActive = boardState.isBoardFlipped,
                                onClick = { boardState.isBoardFlipped = !boardState.isBoardFlipped }
                            )
                        }
                    }
                }

                // Piece tray positioned based on board orientation (only in edit mode)
                if (boardState.isEditMode) {
                    // Show black pieces at top when board is flipped (black on top)
                    if (boardState.isBoardFlipped) {
                        item {
                            PieceTray(
                                boardState = boardState
                            )
                        }
                    }
                }

                // Chess Board with Modern Frame
                item {
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .aspectRatio(1f),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.Transparent
                        ),
                        shape = RoundedCornerShape(20.dp),
                        elevation = CardDefaults.cardElevation(defaultElevation = 12.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(
                                    brush = Brush.radialGradient(
                                        colors = listOf(
                                            Color(0xFF4a5c2a).copy(alpha = 0.1f),
                                            Color(0xFF769656).copy(alpha = 0.05f)
                                        )
                                    ),
                                    shape = RoundedCornerShape(20.dp)
                                )
                                .padding(8.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            InteractiveChessBoard(
                                boardState = boardState,
                                modifier = Modifier.fillMaxSize()
                            )
                        }
                    }
                }

                // Piece tray positioned based on board orientation (only in edit mode)
                if (boardState.isEditMode) {
                    // Show white pieces at bottom when board is normal (white on bottom)
                    if (!boardState.isBoardFlipped) {
                        item {
                            PieceTray(
                                boardState = boardState
                            )
                        }
                    } else {
                        // Show white pieces at bottom when board is flipped
                        item {
                            PieceTray(
                                boardState = boardState
                            )
                        }
                    }
                }

                // FEN Display with Modern Design
                item {
                    FENDisplay(
                        fen = boardState.currentFENString,
                        onFenChanged = { newFen ->
                            boardState.updateFromFEN(newFen)
                        }
                    )
                }
            }
        }
    }
}

@Composable
fun ModernControlButton(
    icon: ImageVector,
    label: String,
    isActive: Boolean,
    onClick: () -> Unit
) {
    Card(
        onClick = onClick,
        colors = CardDefaults.cardColors(
            containerColor = if (isActive)
                Color(0xFF769656).copy(alpha = 0.8f)
            else
                Color(0xFF3a3a3a).copy(alpha = 0.6f)
        ),
        shape = RoundedCornerShape(12.dp),
        elevation = CardDefaults.cardElevation(
            defaultElevation = if (isActive) 6.dp else 2.dp
        )
    ) {
        Column(
            modifier = Modifier
                .padding(horizontal = 12.dp, vertical = 8.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Icon(
                imageVector = icon,
                contentDescription = label,
                modifier = Modifier.size(20.dp),
                tint = if (isActive) Color.White else Color(0xFFb8b5b2)
            )
            Spacer(modifier = Modifier.height(4.dp))
            Text(
                text = label,
                style = MaterialTheme.typography.bodySmall.copy(
                    fontWeight = if (isActive) FontWeight.Bold else FontWeight.Medium,
                    fontSize = 11.sp
                ),
                color = if (isActive) Color.White else Color(0xFFb8b5b2)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun MainScreenPreview() {
    ChessVisionAppTheme {
        // Preview with mock ChessAI
        MainHomeScreen(
            isVisible = true,
            isAIInitialized = true,
            chessAI = ChessAI(androidx.compose.ui.platform.LocalContext.current),
            onNavigateToCamera = {},
            onNavigateToBoard = {}
        )
    }
}