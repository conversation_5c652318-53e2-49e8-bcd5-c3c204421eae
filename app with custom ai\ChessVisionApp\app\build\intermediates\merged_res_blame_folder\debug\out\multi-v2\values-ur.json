{"logs": [{"outputFile": "com.chessvision.app-mergeDebugResources-51:/values-ur/values-ur.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bca7edd7dea9a9293306bd31b1a5bbbe\\transformed\\appcompat-1.1.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,897,989,1082,1177,1271,1373,1467,1563,1657,1749,1841,1925,2033,2139,2241,2352,2453,2569,2734,2832", "endColumns": "113,105,108,85,103,119,76,75,91,92,94,93,101,93,95,93,91,91,83,107,105,101,110,100,115,164,97,84", "endOffsets": "214,320,429,515,619,739,816,892,984,1077,1172,1266,1368,1462,1558,1652,1744,1836,1920,2028,2134,2236,2347,2448,2564,2729,2827,2912"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,105", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,325,434,520,624,744,821,897,989,1082,1177,1271,1373,1467,1563,1657,1749,1841,1925,2033,2139,2241,2352,2453,2569,2734,10881", "endColumns": "113,105,108,85,103,119,76,75,91,92,94,93,101,93,95,93,91,91,83,107,105,101,110,100,115,164,97,84", "endOffsets": "214,320,429,515,619,739,816,892,984,1077,1172,1266,1368,1462,1558,1652,1744,1836,1920,2028,2134,2236,2347,2448,2564,2729,2827,10961"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4713635adcc2bac18369e016a04ea54e\\transformed\\ui-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,282,372,469,557,638,731,819,905,972,1039,1122,1207,1282,1357,1423", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "194,277,367,464,552,633,726,814,900,967,1034,1117,1202,1277,1352,1418,1535"}, "to": {"startLines": "36,37,38,39,40,41,42,99,100,101,102,103,104,106,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3553,3647,3730,3820,3917,4005,4086,10405,10493,10579,10646,10713,10796,10966,11142,11217,11283", "endColumns": "93,82,89,96,87,80,92,87,85,66,66,82,84,74,74,65,116", "endOffsets": "3642,3725,3815,3912,4000,4081,4174,10488,10574,10641,10708,10791,10876,11036,11212,11278,11395"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d5b68b955b795828701719ac184f2bfb\\transformed\\core-1.12.0\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,357,461,564,662,776", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "148,250,352,456,559,657,771,872"}, "to": {"startLines": "29,30,31,32,33,34,35,107", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2832,2930,3032,3134,3238,3341,3439,11041", "endColumns": "97,101,101,103,102,97,113,100", "endOffsets": "2925,3027,3129,3233,3336,3434,3548,11137"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2559031d7e9fe48b3590293cf5c6448f\\transformed\\material3-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,295,411,529,627,724,839,974,1098,1238,1323,1427,1523,1623,1740,1870,1979,2123,2266,2395,2593,2718,2837,2960,3098,3195,3290,3414,3538,3639,3744,3850,3993,4142,4248,4352,4428,4524,4621,4711,4802,4917,4997,5082,5185,5291,5388,5491,5576,5682,5781,5884,6005,6085,6187", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "168,290,406,524,622,719,834,969,1093,1233,1318,1422,1518,1618,1735,1865,1974,2118,2261,2390,2588,2713,2832,2955,3093,3190,3285,3409,3533,3634,3739,3845,3988,4137,4243,4347,4423,4519,4616,4706,4797,4912,4992,5077,5180,5286,5383,5486,5571,5677,5776,5879,6000,6080,6182,6276"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4179,4297,4419,4535,4653,4751,4848,4963,5098,5222,5362,5447,5551,5647,5747,5864,5994,6103,6247,6390,6519,6717,6842,6961,7084,7222,7319,7414,7538,7662,7763,7868,7974,8117,8266,8372,8476,8552,8648,8745,8835,8926,9041,9121,9206,9309,9415,9512,9615,9700,9806,9905,10008,10129,10209,10311", "endColumns": "117,121,115,117,97,96,114,134,123,139,84,103,95,99,116,129,108,143,142,128,197,124,118,122,137,96,94,123,123,100,104,105,142,148,105,103,75,95,96,89,90,114,79,84,102,105,96,102,84,105,98,102,120,79,101,93", "endOffsets": "4292,4414,4530,4648,4746,4843,4958,5093,5217,5357,5442,5546,5642,5742,5859,5989,6098,6242,6385,6514,6712,6837,6956,7079,7217,7314,7409,7533,7657,7758,7863,7969,8112,8261,8367,8471,8547,8643,8740,8830,8921,9036,9116,9201,9304,9410,9507,9610,9695,9801,9900,10003,10124,10204,10306,10400"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ead5545371d24235bc023f33a0e98494\\transformed\\foundation-release\\res\\values-ur\\values-ur.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,140", "endColumns": "84,86", "endOffsets": "135,222"}, "to": {"startLines": "111,112", "startColumns": "4,4", "startOffsets": "11400,11485", "endColumns": "84,86", "endOffsets": "11480,11567"}}]}]}