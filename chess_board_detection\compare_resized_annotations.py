"""
<PERSON><PERSON><PERSON> to compare model predictions and manual annotations after resizing to preprocessing size.
This allows direct comparison in the 256x256 space that the models were trained on.
"""

import os
import sys
import numpy as np
import cv2
import json
import matplotlib.pyplot as plt
import pandas as pd
import glob
from matplotlib.patches import Polygon
from matplotlib.collections import PatchCollection

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def load_manual_annotation(annotation_dir):
    """Load the most recent manual annotation."""
    # Find the most recent JSON file
    json_files = glob.glob(os.path.join(annotation_dir, 'manual_annotation_*.json'))
    if not json_files:
        raise ValueError(f"No manual annotations found in {annotation_dir}")
    
    # Sort by modification time (most recent first)
    json_files.sort(key=os.path.getmtime, reverse=True)
    latest_json = json_files[0]
    
    # Load the annotation
    with open(latest_json, 'r') as f:
        annotation = json.load(f)
    
    return annotation, latest_json

def load_model_predictions(predictions_json):
    """Load model predictions from JSON file."""
    if not os.path.exists(predictions_json):
        raise ValueError(f"Model predictions file not found: {predictions_json}")
    
    # Load the predictions
    with open(predictions_json, 'r') as f:
        predictions = json.load(f)
    
    return predictions

def preprocess_image_for_visualization(image_path, target_size=(256, 256)):
    """
    Preprocess an image for visualization while preserving orientation.
    Returns the preprocessed image and preprocessing info needed for coordinate mapping.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")
    
    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()
    
    # Get original dimensions
    original_height, original_width = image.shape[:2]
    
    # Calculate aspect ratio
    aspect = original_width / original_height
    
    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)
    
    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)
    
    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]
    
    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)
    
    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }
    
    return image_final, preprocess_info

def map_original_to_preprocessed(keypoints, preprocess_info):
    """
    Map keypoints from original image space to preprocessed (256x256) space.
    This is the reverse of the mapping done during inference.
    """
    mapped_keypoints = []
    
    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']
    resized_w, resized_h = preprocess_info['resized_size']
    
    for point in keypoints:
        if isinstance(point, dict):
            # Handle dictionary format from manual annotations
            x, y = point['x'], point['y']
        else:
            # Handle tuple format (x, y, conf)
            x, y = point[0], point[1]
        
        # Step 1: Scale from original to resized
        x_in_resized = x * (resized_w / original_w)
        y_in_resized = y * (resized_h / original_h)
        
        # Step 2: Subtract crop offset to get coordinates in crop
        x_in_crop = x_in_resized - crop_start_x
        y_in_crop = y_in_resized - crop_start_y
        
        # Step 3: Scale from crop to target size
        x_in_target = x_in_crop * (target_w / crop_size)
        y_in_target = y_in_crop * (target_h / crop_size)
        
        # Ensure coordinates are within bounds
        x_in_target = max(0, min(x_in_target, target_w - 1))
        y_in_target = max(0, min(y_in_target, target_h - 1))
        
        if isinstance(point, dict):
            mapped_keypoints.append({'x': x_in_target, 'y': y_in_target})
        else:
            mapped_keypoints.append((x_in_target, y_in_target, point[2]))
    
    return mapped_keypoints

def visualize_resized_comparison(image, manual_corners, model_predictions, output_path):
    """
    Create a visualization comparing manual and model annotations in the 256x256 space.
    """
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 10))
    fig.suptitle('Comparison in 256x256 Preprocessing Space', fontsize=16)
    
    # Display the preprocessed image
    ax.imshow(image)
    
    # Define colors and markers
    styles = {
        'Manual': {'color': 'white', 'marker': 'o', 'linestyle': '-', 'linewidth': 3, 'markersize': 10},
        'Phase2_Epoch16': {'color': 'red', 'marker': 'x', 'linestyle': '--', 'linewidth': 2, 'markersize': 10},
        'Phase3_Epoch8': {'color': 'blue', 'marker': '+', 'linestyle': ':', 'linewidth': 2, 'markersize': 10}
    }
    
    # Plot manual annotation
    manual_xs = []
    manual_ys = []
    
    for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
        x = manual_corners[corner]['x']
        y = manual_corners[corner]['y']
        manual_xs.append(x)
        manual_ys.append(y)
        
        ax.scatter(x, y, c=styles['Manual']['color'], marker=styles['Manual']['marker'], 
                  s=styles['Manual']['markersize']**2, linewidths=2)
        ax.text(x+5, y+5, f"Manual\n({int(x)}, {int(y)})", 
               color=styles['Manual']['color'], fontsize=8,
               bbox=dict(facecolor='black', alpha=0.7))
    
    # Close the polygon
    manual_xs.append(manual_xs[0])
    manual_ys.append(manual_ys[0])
    
    # Plot manual polygon
    ax.plot(manual_xs, manual_ys, color=styles['Manual']['color'], 
           linestyle=styles['Manual']['linestyle'], 
           linewidth=styles['Manual']['linewidth'], alpha=0.7)
    
    # Plot model predictions
    legend_elements = [plt.Line2D([0], [0], color=styles['Manual']['color'], 
                                 marker=styles['Manual']['marker'], 
                                 linestyle=styles['Manual']['linestyle'],
                                 linewidth=styles['Manual']['linewidth'],
                                 markersize=styles['Manual']['markersize'],
                                 label='Manual Annotation')]
    
    for model_name, corners in model_predictions.items():
        if model_name not in styles:
            continue
            
        style = styles[model_name]
        xs = []
        ys = []
        
        for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
            x = corners[corner]['x']
            y = corners[corner]['y']
            conf = corners[corner].get('confidence', 1.0)
            xs.append(x)
            ys.append(y)
            
            ax.scatter(x, y, c=style['color'], marker=style['marker'], 
                      s=style['markersize']**2, linewidths=2)
            ax.text(x-5, y-5, f"{model_name}\n({int(x)}, {int(y)})\nConf: {conf:.3f}", 
                   color=style['color'], fontsize=8,
                   bbox=dict(facecolor='black', alpha=0.7))
        
        # Close the polygon
        xs.append(xs[0])
        ys.append(ys[0])
        
        # Plot model polygon
        ax.plot(xs, ys, color=style['color'], 
               linestyle=style['linestyle'], 
               linewidth=style['linewidth'], alpha=0.7)
        
        # Add to legend
        legend_elements.append(plt.Line2D([0], [0], color=style['color'], 
                                         marker=style['marker'], 
                                         linestyle=style['linestyle'],
                                         linewidth=style['linewidth'],
                                         markersize=style['markersize'],
                                         label=f'{model_name}'))
    
    # Add legend
    ax.legend(handles=legend_elements, loc='upper right', fontsize=10)
    
    # Calculate distances between manual and model keypoints
    distances = {}
    for model_name, corners in model_predictions.items():
        distances[model_name] = {}
        total_distance = 0
        
        for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
            manual_x = manual_corners[corner]['x']
            manual_y = manual_corners[corner]['y']
            
            model_x = corners[corner]['x']
            model_y = corners[corner]['y']
            
            # Calculate Euclidean distance
            distance = np.sqrt((manual_x - model_x)**2 + (manual_y - model_y)**2)
            distances[model_name][corner] = distance
            total_distance += distance
        
        distances[model_name]['average'] = total_distance / 4
    
    # Add distance metrics as text
    metrics_text = "Distance from Manual (pixels):\n\n"
    
    for model_name, model_distances in distances.items():
        metrics_text += f"{model_name}:\n"
        metrics_text += f"  Avg Distance: {model_distances['average']:.2f} pixels\n"
        
        for corner, distance in model_distances.items():
            if corner != 'average':
                metrics_text += f"  {corner}: {distance:.2f}\n"
        
        metrics_text += "\n"
    
    # Add metrics text to figure
    plt.figtext(0.02, 0.02, metrics_text, fontsize=10, 
               bbox=dict(facecolor='white', alpha=0.8))
    
    # Remove axes
    ax.axis('off')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    manual_annotation_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\manual_annotations"
    model_predictions_json = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\final_test\\corner_coordinates.json"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\resized_comparison"
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Load manual annotation
    manual_annotation, annotation_path = load_manual_annotation(manual_annotation_dir)
    print(f"Loaded manual annotation from: {annotation_path}")
    
    # Load model predictions
    model_predictions = load_model_predictions(model_predictions_json)
    print(f"Loaded model predictions from: {model_predictions_json}")
    
    # Preprocess image for visualization
    preprocessed_image, preprocess_info = preprocess_image_for_visualization(image_path)
    
    # Map manual annotations to preprocessed space
    manual_corners_original = manual_annotation['corners']
    manual_corners_list = []
    for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
        manual_corners_list.append(manual_corners_original[corner])
    
    manual_corners_preprocessed = map_original_to_preprocessed(manual_corners_list, preprocess_info)
    
    # Reconstruct manual corners dictionary
    manual_corners_mapped = {}
    for i, corner in enumerate(['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']):
        manual_corners_mapped[corner] = manual_corners_preprocessed[i]
    
    # Map model predictions to preprocessed space
    model_predictions_mapped = {}
    for model_name, corners in model_predictions.items():
        model_corners_list = []
        for corner in ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']:
            x = corners[corner]['x']
            y = corners[corner]['y']
            conf = corners[corner]['confidence']
            model_corners_list.append((x, y, conf))
        
        model_corners_preprocessed = map_original_to_preprocessed(model_corners_list, preprocess_info)
        
        # Reconstruct model corners dictionary
        model_predictions_mapped[model_name] = {}
        for i, corner in enumerate(['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']):
            x, y, conf = model_corners_preprocessed[i]
            model_predictions_mapped[model_name][corner] = {
                'x': x,
                'y': y,
                'confidence': conf
            }
    
    # Create comparison visualization
    output_path = os.path.join(output_dir, "resized_comparison.png")
    visualize_resized_comparison(preprocessed_image, manual_corners_mapped, model_predictions_mapped, output_path)
    
    print(f"Resized comparison visualization saved to: {output_path}")
    
    # Print mapped coordinates
    print("\nMapped Coordinates in 256x256 Space:")
    print("====================================")
    
    print("\nManual Annotation:")
    for corner, coords in manual_corners_mapped.items():
        print(f"  {corner}: ({int(coords['x'])}, {int(coords['y'])})")
    
    for model_name, corners in model_predictions_mapped.items():
        print(f"\n{model_name}:")
        for corner, coords in corners.items():
            print(f"  {corner}: ({int(coords['x'])}, {int(coords['y'])}) - Conf: {coords['confidence']:.4f}")

if __name__ == "__main__":
    main()
