  
OnnxTensor ai.onnxruntime  	OnnxValue ai.onnxruntime  OrtEnvironment ai.onnxruntime  
OrtSession ai.onnxruntime  close ai.onnxruntime.OnnxTensor  createTensor ai.onnxruntime.OnnxTensor  floatBuffer ai.onnxruntime.OnnxTensor  getFLOATBuffer ai.onnxruntime.OnnxTensor  getFloatBuffer ai.onnxruntime.OnnxTensor  getINFO ai.onnxruntime.OnnxTensor  getInfo ai.onnxruntime.OnnxTensor  getLET ai.onnxruntime.OnnxTensor  getLet ai.onnxruntime.OnnxTensor  info ai.onnxruntime.OnnxTensor  let ai.onnxruntime.OnnxTensor  setFloatBuffer ai.onnxruntime.OnnxTensor  setInfo ai.onnxruntime.OnnxTensor  close ai.onnxruntime.OnnxTensorLike  let ai.onnxruntime.OnnxTensorLike  
createSession ai.onnxruntime.OrtEnvironment  getEnvironment ai.onnxruntime.OrtEnvironment  Result ai.onnxruntime.OrtSession  SessionOptions ai.onnxruntime.OrtSession  close ai.onnxruntime.OrtSession  equals ai.onnxruntime.OrtSession  run ai.onnxruntime.OrtSession  close  ai.onnxruntime.OrtSession.Result  get  ai.onnxruntime.OrtSession.Result  Boolean (ai.onnxruntime.OrtSession.SessionOptions  Log (ai.onnxruntime.OrtSession.SessionOptions  OptLevel (ai.onnxruntime.OrtSession.SessionOptions  TAG (ai.onnxruntime.OrtSession.SessionOptions  addNnapi (ai.onnxruntime.OrtSession.SessionOptions  ai (ai.onnxruntime.OrtSession.SessionOptions  apply (ai.onnxruntime.OrtSession.SessionOptions  close (ai.onnxruntime.OrtSession.SessionOptions  getAI (ai.onnxruntime.OrtSession.SessionOptions  getAPPLY (ai.onnxruntime.OrtSession.SessionOptions  getAi (ai.onnxruntime.OrtSession.SessionOptions  getApply (ai.onnxruntime.OrtSession.SessionOptions  java (ai.onnxruntime.OrtSession.SessionOptions  setInterOpNumThreads (ai.onnxruntime.OrtSession.SessionOptions  setIntraOpNumThreads (ai.onnxruntime.OrtSession.SessionOptions  setMemoryPatternOptimization (ai.onnxruntime.OrtSession.SessionOptions  setOptimizationLevel (ai.onnxruntime.OrtSession.SessionOptions  ALL_OPT 1ai.onnxruntime.OrtSession.SessionOptions.OptLevel  getSHAPE ai.onnxruntime.TensorInfo  getShape ai.onnxruntime.TensorInfo  setShape ai.onnxruntime.TensorInfo  shape ai.onnxruntime.TensorInfo  Manifest android  R android  
permission android.Manifest  CAMERA android.Manifest.permission  drawable 	android.R  ic_menu_gallery android.R.drawable  Activity android.app  Application android.app  Bundle android.app.Activity  ChessAI android.app.Activity  ChessVisionAppTheme android.app.Activity  ComponentActivity android.app.Activity  DisposableEffect android.app.Activity  	LocalView android.app.Activity  
MainScreen android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  Surface android.app.Activity  Unit android.app.Activity  WindowCompat android.app.Activity  WindowInsetsCompat android.app.Activity  WindowInsetsControllerCompat android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  invoke android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  	setWindow android.app.Activity  window android.app.Activity  android android.app.Application  onCreate android.app.Application  ContentResolver android.content  
ContentValues android.content  Context android.content  Intent android.content  openInputStream android.content.ContentResolver  android android.content.ContentValues  apply android.content.ContentValues  
getANDROID android.content.ContentValues  getAPPLY android.content.ContentValues  
getAndroid android.content.ContentValues  getApply android.content.ContentValues  put android.content.ContentValues  Bundle android.content.Context  ChessAI android.content.Context  ChessVisionAppTheme android.content.Context  ComponentActivity android.content.Context  DisposableEffect android.content.Context  	LocalView android.content.Context  
MainScreen android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  Unit android.content.Context  WindowCompat android.content.Context  WindowInsetsCompat android.content.Context  WindowInsetsControllerCompat android.content.Context  android android.content.Context  assets android.content.Context  contentResolver android.content.Context  enableEdgeToEdge android.content.Context  filesDir android.content.Context  fillMaxSize android.content.Context  	getASSETS android.content.Context  	getAssets android.content.Context  getCONTENTResolver android.content.Context  getContentResolver android.content.Context  getFILESDir android.content.Context  getFilesDir android.content.Context  invoke android.content.Context  onCreate android.content.Context  	setAssets android.content.Context  
setContent android.content.Context  setContentResolver android.content.Context  setFilesDir android.content.Context  Bundle android.content.ContextWrapper  ChessAI android.content.ContextWrapper  ChessVisionAppTheme android.content.ContextWrapper  ComponentActivity android.content.ContextWrapper  DisposableEffect android.content.ContextWrapper  	LocalView android.content.ContextWrapper  
MainScreen android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  Unit android.content.ContextWrapper  WindowCompat android.content.ContextWrapper  WindowInsetsCompat android.content.ContextWrapper  WindowInsetsControllerCompat android.content.ContextWrapper  android android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  invoke android.content.ContextWrapper  onCreate android.content.ContextWrapper  
setContent android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  
Configuration android.content.res  open  android.content.res.AssetManager  screenHeightDp !android.content.res.Configuration  
screenWidthDp !android.content.res.Configuration  Bitmap android.graphics  
BitmapFactory android.graphics  ImageDecoder android.graphics  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  createScaledBitmap android.graphics.Bitmap  equals android.graphics.Bitmap  	getHEIGHT android.graphics.Bitmap  	getHeight android.graphics.Bitmap  	getPixels android.graphics.Bitmap  getWIDTH android.graphics.Bitmap  getWidth android.graphics.Bitmap  height android.graphics.Bitmap  recycle android.graphics.Bitmap  	setHeight android.graphics.Bitmap  	setPixels android.graphics.Bitmap  setWidth android.graphics.Bitmap  width android.graphics.Bitmap  RGB_565 android.graphics.Bitmap.Config  decodeStream android.graphics.BitmapFactory  Source android.graphics.ImageDecoder  createSource android.graphics.ImageDecoder  decodeBitmap android.graphics.ImageDecoder  Uri android.net  equals android.net.Uri  getLET android.net.Uri  getLet android.net.Uri  let android.net.Uri  parse android.net.Uri  toString android.net.Uri  Build 
android.os  Bundle 
android.os  VERSION android.os.Build  
VERSION_CODES android.os.Build  SDK_INT android.os.Build.VERSION  P android.os.Build.VERSION_CODES  S android.os.Build.VERSION_CODES  
MediaStore android.provider  Images android.provider.MediaStore  MediaColumns android.provider.MediaStore  Media "android.provider.MediaStore.Images  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  	getBitmap (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  View android.view  Window android.view  Bundle  android.view.ContextThemeWrapper  ChessAI  android.view.ContextThemeWrapper  ChessVisionAppTheme  android.view.ContextThemeWrapper  ComponentActivity  android.view.ContextThemeWrapper  DisposableEffect  android.view.ContextThemeWrapper  	LocalView  android.view.ContextThemeWrapper  
MainScreen  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  Unit  android.view.ContextThemeWrapper  WindowCompat  android.view.ContextThemeWrapper  WindowInsetsCompat  android.view.ContextThemeWrapper  WindowInsetsControllerCompat  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  invoke  android.view.ContextThemeWrapper  onCreate  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  apply android.view.View  context android.view.View  
getCONTEXT android.view.View  
getContext android.view.View  getISInEditMode android.view.View  getIsInEditMode android.view.View  isInEditMode android.view.View  
setContext android.view.View  setImageBitmap android.view.View  setImageResource android.view.View  
setInEditMode android.view.View  apply android.view.ViewGroup  getSTATUSBarColor android.view.Window  getStatusBarColor android.view.Window  setStatusBarColor android.view.Window  statusBarColor android.view.Window  	ImageView android.widget  Toast android.widget  apply android.widget.FrameLayout  Log android.widget.ImageView  	ScaleType android.widget.ImageView  android android.widget.ImageView  apply android.widget.ImageView  
getANDROID android.widget.ImageView  getAPPLY android.widget.ImageView  
getAndroid android.widget.ImageView  getApply android.widget.ImageView  getSCALEType android.widget.ImageView  getScaleType android.widget.ImageView  	scaleType android.widget.ImageView  setImageBitmap android.widget.ImageView  setImageResource android.widget.ImageView  setScaleType android.widget.ImageView  CENTER_CROP "android.widget.ImageView.ScaleType  LENGTH_LONG android.widget.Toast  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  Bundle #androidx.activity.ComponentActivity  ChessAI #androidx.activity.ComponentActivity  ChessVisionAppTheme #androidx.activity.ComponentActivity  ComponentActivity #androidx.activity.ComponentActivity  DisposableEffect #androidx.activity.ComponentActivity  	LocalView #androidx.activity.ComponentActivity  
MainScreen #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  Unit #androidx.activity.ComponentActivity  WindowCompat #androidx.activity.ComponentActivity  WindowInsetsCompat #androidx.activity.ComponentActivity  WindowInsetsControllerCompat #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  	getWINDOW #androidx.activity.ComponentActivity  	getWindow #androidx.activity.ComponentActivity  invoke #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  	setWindow #androidx.activity.ComponentActivity  window #androidx.activity.ComponentActivity  BackHandler androidx.activity.compose  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  
GetContent 9androidx.activity.result.contract.ActivityResultContracts  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  ActivityResultContracts androidx.camera.core  	Alignment androidx.camera.core  AndroidView androidx.camera.core  Arrangement androidx.camera.core  
AtomicBoolean androidx.camera.core  AtomicReference androidx.camera.core  Box androidx.camera.core  Brush androidx.camera.core  Button androidx.camera.core  ButtonDefaults androidx.camera.core  Camera androidx.camera.core  	CameraAlt androidx.camera.core  CameraSelector androidx.camera.core  CapturedImagePreview androidx.camera.core  Card androidx.camera.core  CardDefaults androidx.camera.core  CenterFocusStrong androidx.camera.core  ChessBoardGuide androidx.camera.core  CircleShape androidx.camera.core  CircularProgressIndicator androidx.camera.core  Color androidx.camera.core  Column androidx.camera.core  
Composable androidx.camera.core  
ContextCompat androidx.camera.core  CornerGuide androidx.camera.core  DisposableEffect androidx.camera.core  	Exception androidx.camera.core  	Executors androidx.camera.core  	FlashAuto androidx.camera.core  FlashOff androidx.camera.core  FlashOn androidx.camera.core  
FontWeight androidx.camera.core  Icon androidx.camera.core  
IconButton androidx.camera.core  Icons androidx.camera.core  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  Info androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  
MaterialTheme androidx.camera.core  Modifier androidx.camera.core  PhotoLibrary androidx.camera.core  Preview androidx.camera.core  PreviewView androidx.camera.core  ProcessCameraProvider androidx.camera.core  
Psychology androidx.camera.core  RoundedCornerShape androidx.camera.core  Row androidx.camera.core  Spacer androidx.camera.core  Stable androidx.camera.core  System androidx.camera.core  TAG androidx.camera.core  Text androidx.camera.core  addListener androidx.camera.core  also androidx.camera.core  android androidx.camera.core  androidx androidx.camera.core  apply androidx.camera.core  
background androidx.camera.core  captureImageSafe androidx.camera.core  fillMaxSize androidx.camera.core  fillMaxWidth androidx.camera.core  getValue androidx.camera.core  height androidx.camera.core  let androidx.camera.core  listOf androidx.camera.core  mutableStateOf androidx.camera.core  navigationBarsPadding androidx.camera.core  padding androidx.camera.core  provideDelegate androidx.camera.core  remember androidx.camera.core  run androidx.camera.core  setValue androidx.camera.core  size androidx.camera.core  statusBarsPadding androidx.camera.core  width androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  FLASH_MODE_AUTO !androidx.camera.core.ImageCapture  FLASH_MODE_OFF !androidx.camera.core.ImageCapture  
FLASH_MODE_ON !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  equals !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  setFlashMode )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  getSAVEDUri 3androidx.camera.core.ImageCapture.OutputFileResults  getSavedUri 3androidx.camera.core.ImageCapture.OutputFileResults  savedUri 3androidx.camera.core.ImageCapture.OutputFileResults  setSavedUri 3androidx.camera.core.ImageCapture.OutputFileResults  message *androidx.camera.core.ImageCaptureException  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  getALSO androidx.camera.core.Preview  getAlso androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  also androidx.camera.core.UseCase  setSurfaceProvider androidx.camera.core.UseCase  takePicture androidx.camera.core.UseCase  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  ImplementationMode  androidx.camera.view.PreviewView  PreviewView  androidx.camera.view.PreviewView  	ScaleType  androidx.camera.view.PreviewView  apply  androidx.camera.view.PreviewView  getAPPLY  androidx.camera.view.PreviewView  getApply  androidx.camera.view.PreviewView  getIMPLEMENTATIONMode  androidx.camera.view.PreviewView  getImplementationMode  androidx.camera.view.PreviewView  getSCALEType  androidx.camera.view.PreviewView  getSURFACEProvider  androidx.camera.view.PreviewView  getScaleType  androidx.camera.view.PreviewView  getSurfaceProvider  androidx.camera.view.PreviewView  implementationMode  androidx.camera.view.PreviewView  	scaleType  androidx.camera.view.PreviewView  setImplementationMode  androidx.camera.view.PreviewView  setScaleType  androidx.camera.view.PreviewView  setSurfaceProvider  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  
COMPATIBLE 3androidx.camera.view.PreviewView.ImplementationMode  FILL_CENTER *androidx.camera.view.PreviewView.ScaleType  ActivityResultContracts androidx.compose.animation  AlertDialog androidx.compose.animation  	Alignment androidx.compose.animation  AnimatedVisibility androidx.compose.animation  AnimatedVisibilityScope androidx.compose.animation  Arrangement androidx.compose.animation  Box androidx.compose.animation  Brush androidx.compose.animation  	CameraAlt androidx.compose.animation  Card androidx.compose.animation  CardDefaults androidx.compose.animation  Check androidx.compose.animation  ChessAI androidx.compose.animation  ChessActionCard androidx.compose.animation  ChessAppHeader androidx.compose.animation  ChessExpressiveAnimations androidx.compose.animation  
ChessPosition androidx.compose.animation  ChessSquare androidx.compose.animation  ChessVisionAppTheme androidx.compose.animation  CircleShape androidx.compose.animation  Clear androidx.compose.animation  Color androidx.compose.animation  Column androidx.compose.animation  
Composable androidx.compose.animation  
ContextCompat androidx.compose.animation  
ControlButton androidx.compose.animation  DisposableEffect androidx.compose.animation  
EaseInBack androidx.compose.animation  
EaseInOutSine androidx.compose.animation  EaseOutBack androidx.compose.animation  EaseOutCubic androidx.compose.animation  Edit androidx.compose.animation  EnterTransition androidx.compose.animation  Error androidx.compose.animation  	Exception androidx.compose.animation  ExitTransition androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  
FENDisplay androidx.compose.animation  FeatureItem androidx.compose.animation  FeaturesSection androidx.compose.animation  
FlipToBack androidx.compose.animation  
FontWeight androidx.compose.animation  GridOn androidx.compose.animation  Icon androidx.compose.animation  Icons androidx.compose.animation  Image androidx.compose.animation  	IntOffset androidx.compose.animation  InteractiveChessBoard androidx.compose.animation  LaunchedEffect androidx.compose.animation  
LazyColumn androidx.compose.animation  LazyRow androidx.compose.animation  LinearEasing androidx.compose.animation  	LocalView androidx.compose.animation  
MainScreen androidx.compose.animation  Manifest androidx.compose.animation  
MaterialTheme androidx.compose.animation  ModernControlButton androidx.compose.animation  Modifier androidx.compose.animation  Offset androidx.compose.animation  OutlinedTextField androidx.compose.animation  OutlinedTextFieldDefaults androidx.compose.animation  PackageManager androidx.compose.animation  
PaddingValues androidx.compose.animation  
PieceColor androidx.compose.animation  	PieceTray androidx.compose.animation  
PieceTrayItem androidx.compose.animation  	PieceType androidx.compose.animation  	PlayArrow androidx.compose.animation  
Psychology androidx.compose.animation  QuickActionsSection androidx.compose.animation  R androidx.compose.animation  Rect androidx.compose.animation  Refresh androidx.compose.animation  
RepeatMode androidx.compose.animation  
RestartAlt androidx.compose.animation  RoundedCornerShape androidx.compose.animation  Row androidx.compose.animation  Size androidx.compose.animation  Spacer androidx.compose.animation  Spring androidx.compose.animation  String androidx.compose.animation  Surface androidx.compose.animation  Text androidx.compose.animation  	TextAlign androidx.compose.animation  Toast androidx.compose.animation  Unit androidx.compose.animation  WindowCompat androidx.compose.animation  WindowInsetsCompat androidx.compose.animation  WindowInsetsControllerCompat androidx.compose.animation  android androidx.compose.animation  androidx androidx.compose.animation  animateColorAsState androidx.compose.animation  animateFloat androidx.compose.animation  animateFloatAsState androidx.compose.animation  aspectRatio androidx.compose.animation  
background androidx.compose.animation  	clickable androidx.compose.animation  delay androidx.compose.animation  detectDragGestures androidx.compose.animation  downTo androidx.compose.animation  	emptyList androidx.compose.animation  enableEdgeToEdge androidx.compose.animation  fadeIn androidx.compose.animation  fadeOut androidx.compose.animation  
fillMaxHeight androidx.compose.animation  fillMaxSize androidx.compose.animation  fillMaxWidth androidx.compose.animation  format androidx.compose.animation  getValue androidx.compose.animation  
graphicsLayer androidx.compose.animation  height androidx.compose.animation  infiniteRepeatable androidx.compose.animation  items androidx.compose.animation  kotlinx androidx.compose.animation  launch androidx.compose.animation  let androidx.compose.animation  listOf androidx.compose.animation  minOf androidx.compose.animation  mutableStateOf androidx.compose.animation  offset androidx.compose.animation  onGloballyPositioned androidx.compose.animation  padding androidx.compose.animation  painterResource androidx.compose.animation  positionInRoot androidx.compose.animation  provideDelegate androidx.compose.animation  remember androidx.compose.animation  rememberCoroutineScope androidx.compose.animation  rememberInfiniteTransition androidx.compose.animation  scaleIn androidx.compose.animation  scaleOut androidx.compose.animation  
setContent androidx.compose.animation  setValue androidx.compose.animation  size androidx.compose.animation  slideInHorizontally androidx.compose.animation  slideInVertically androidx.compose.animation  slideOutHorizontally androidx.compose.animation  slideOutVertically androidx.compose.animation  spring androidx.compose.animation  statusBarsPadding androidx.compose.animation  tween androidx.compose.animation  	Alignment 2androidx.compose.animation.AnimatedVisibilityScope  Arrangement 2androidx.compose.animation.AnimatedVisibilityScope  Box 2androidx.compose.animation.AnimatedVisibilityScope  Brush 2androidx.compose.animation.AnimatedVisibilityScope  Card 2androidx.compose.animation.AnimatedVisibilityScope  CardDefaults 2androidx.compose.animation.AnimatedVisibilityScope  ChessAppHeader 2androidx.compose.animation.AnimatedVisibilityScope  CircleShape 2androidx.compose.animation.AnimatedVisibilityScope  Clear 2androidx.compose.animation.AnimatedVisibilityScope  Color 2androidx.compose.animation.AnimatedVisibilityScope  Column 2androidx.compose.animation.AnimatedVisibilityScope  
ControlButton 2androidx.compose.animation.AnimatedVisibilityScope  FeaturesSection 2androidx.compose.animation.AnimatedVisibilityScope  
FontWeight 2androidx.compose.animation.AnimatedVisibilityScope  Icon 2androidx.compose.animation.AnimatedVisibilityScope  Icons 2androidx.compose.animation.AnimatedVisibilityScope  Image 2androidx.compose.animation.AnimatedVisibilityScope  LazyRow 2androidx.compose.animation.AnimatedVisibilityScope  
MaterialTheme 2androidx.compose.animation.AnimatedVisibilityScope  Modifier 2androidx.compose.animation.AnimatedVisibilityScope  
PaddingValues 2androidx.compose.animation.AnimatedVisibilityScope  
PieceTrayItem 2androidx.compose.animation.AnimatedVisibilityScope  QuickActionsSection 2androidx.compose.animation.AnimatedVisibilityScope  RoundedCornerShape 2androidx.compose.animation.AnimatedVisibilityScope  Row 2androidx.compose.animation.AnimatedVisibilityScope  Spacer 2androidx.compose.animation.AnimatedVisibilityScope  Text 2androidx.compose.animation.AnimatedVisibilityScope  
background 2androidx.compose.animation.AnimatedVisibilityScope  dp 2androidx.compose.animation.AnimatedVisibilityScope  fillMaxSize 2androidx.compose.animation.AnimatedVisibilityScope  fillMaxWidth 2androidx.compose.animation.AnimatedVisibilityScope  
getBACKGROUND 2androidx.compose.animation.AnimatedVisibilityScope  
getBackground 2androidx.compose.animation.AnimatedVisibilityScope  getFILLMaxSize 2androidx.compose.animation.AnimatedVisibilityScope  getFILLMaxWidth 2androidx.compose.animation.AnimatedVisibilityScope  getFillMaxSize 2androidx.compose.animation.AnimatedVisibilityScope  getFillMaxWidth 2androidx.compose.animation.AnimatedVisibilityScope  getGRAPHICSLayer 2androidx.compose.animation.AnimatedVisibilityScope  getGraphicsLayer 2androidx.compose.animation.AnimatedVisibilityScope  	getHEIGHT 2androidx.compose.animation.AnimatedVisibilityScope  	getHeight 2androidx.compose.animation.AnimatedVisibilityScope  getLET 2androidx.compose.animation.AnimatedVisibilityScope  	getLISTOf 2androidx.compose.animation.AnimatedVisibilityScope  getLet 2androidx.compose.animation.AnimatedVisibilityScope  	getListOf 2androidx.compose.animation.AnimatedVisibilityScope  
getPADDING 2androidx.compose.animation.AnimatedVisibilityScope  getPAINTERResource 2androidx.compose.animation.AnimatedVisibilityScope  
getPadding 2androidx.compose.animation.AnimatedVisibilityScope  getPainterResource 2androidx.compose.animation.AnimatedVisibilityScope  getSIZE 2androidx.compose.animation.AnimatedVisibilityScope  getSize 2androidx.compose.animation.AnimatedVisibilityScope  
graphicsLayer 2androidx.compose.animation.AnimatedVisibilityScope  height 2androidx.compose.animation.AnimatedVisibilityScope  invoke 2androidx.compose.animation.AnimatedVisibilityScope  items 2androidx.compose.animation.AnimatedVisibilityScope  let 2androidx.compose.animation.AnimatedVisibilityScope  listOf 2androidx.compose.animation.AnimatedVisibilityScope  padding 2androidx.compose.animation.AnimatedVisibilityScope  painterResource 2androidx.compose.animation.AnimatedVisibilityScope  size 2androidx.compose.animation.AnimatedVisibilityScope  plus *androidx.compose.animation.EnterTransition  plus )androidx.compose.animation.ExitTransition  ActivityResultContracts androidx.compose.animation.core  AlertDialog androidx.compose.animation.core  	Alignment androidx.compose.animation.core  AnimatedVisibility androidx.compose.animation.core  Arrangement androidx.compose.animation.core  Box androidx.compose.animation.core  Brush androidx.compose.animation.core  	CameraAlt androidx.compose.animation.core  Card androidx.compose.animation.core  CardDefaults androidx.compose.animation.core  Check androidx.compose.animation.core  ChessAI androidx.compose.animation.core  ChessActionCard androidx.compose.animation.core  ChessAppHeader androidx.compose.animation.core  ChessExpressiveAnimations androidx.compose.animation.core  
ChessPosition androidx.compose.animation.core  ChessSquare androidx.compose.animation.core  ChessVisionAppTheme androidx.compose.animation.core  CircleShape androidx.compose.animation.core  Clear androidx.compose.animation.core  Color androidx.compose.animation.core  Column androidx.compose.animation.core  
Composable androidx.compose.animation.core  
ContextCompat androidx.compose.animation.core  
ControlButton androidx.compose.animation.core  DisposableEffect androidx.compose.animation.core  
EaseInBack androidx.compose.animation.core  
EaseInOutSine androidx.compose.animation.core  EaseOutBack androidx.compose.animation.core  EaseOutCubic androidx.compose.animation.core  Easing androidx.compose.animation.core  Edit androidx.compose.animation.core  Error androidx.compose.animation.core  	Exception androidx.compose.animation.core  ExperimentalMaterial3Api androidx.compose.animation.core  
FENDisplay androidx.compose.animation.core  FeatureItem androidx.compose.animation.core  FeaturesSection androidx.compose.animation.core  
FlipToBack androidx.compose.animation.core  
FontWeight androidx.compose.animation.core  GridOn androidx.compose.animation.core  Icon androidx.compose.animation.core  Icons androidx.compose.animation.core  Image androidx.compose.animation.core  InfiniteRepeatableSpec androidx.compose.animation.core  InfiniteTransition androidx.compose.animation.core  	IntOffset androidx.compose.animation.core  InteractiveChessBoard androidx.compose.animation.core  LaunchedEffect androidx.compose.animation.core  
LazyColumn androidx.compose.animation.core  LazyRow androidx.compose.animation.core  LinearEasing androidx.compose.animation.core  	LocalView androidx.compose.animation.core  
MainScreen androidx.compose.animation.core  Manifest androidx.compose.animation.core  
MaterialTheme androidx.compose.animation.core  ModernControlButton androidx.compose.animation.core  Modifier androidx.compose.animation.core  Offset androidx.compose.animation.core  OutlinedTextField androidx.compose.animation.core  OutlinedTextFieldDefaults androidx.compose.animation.core  PackageManager androidx.compose.animation.core  
PaddingValues androidx.compose.animation.core  
PieceColor androidx.compose.animation.core  	PieceTray androidx.compose.animation.core  
PieceTrayItem androidx.compose.animation.core  	PieceType androidx.compose.animation.core  	PlayArrow androidx.compose.animation.core  
Psychology androidx.compose.animation.core  QuickActionsSection androidx.compose.animation.core  R androidx.compose.animation.core  Rect androidx.compose.animation.core  Refresh androidx.compose.animation.core  
RepeatMode androidx.compose.animation.core  
RestartAlt androidx.compose.animation.core  RoundedCornerShape androidx.compose.animation.core  Row androidx.compose.animation.core  Size androidx.compose.animation.core  Spacer androidx.compose.animation.core  Spring androidx.compose.animation.core  
SpringSpec androidx.compose.animation.core  String androidx.compose.animation.core  Surface androidx.compose.animation.core  Text androidx.compose.animation.core  	TextAlign androidx.compose.animation.core  Toast androidx.compose.animation.core  	TweenSpec androidx.compose.animation.core  Unit androidx.compose.animation.core  WindowCompat androidx.compose.animation.core  WindowInsetsCompat androidx.compose.animation.core  WindowInsetsControllerCompat androidx.compose.animation.core  android androidx.compose.animation.core  androidx androidx.compose.animation.core  animateColorAsState androidx.compose.animation.core  animateFloat androidx.compose.animation.core  animateFloatAsState androidx.compose.animation.core  aspectRatio androidx.compose.animation.core  
background androidx.compose.animation.core  	clickable androidx.compose.animation.core  delay androidx.compose.animation.core  detectDragGestures androidx.compose.animation.core  downTo androidx.compose.animation.core  	emptyList androidx.compose.animation.core  enableEdgeToEdge androidx.compose.animation.core  fadeIn androidx.compose.animation.core  fadeOut androidx.compose.animation.core  
fillMaxHeight androidx.compose.animation.core  fillMaxSize androidx.compose.animation.core  fillMaxWidth androidx.compose.animation.core  format androidx.compose.animation.core  getValue androidx.compose.animation.core  
graphicsLayer androidx.compose.animation.core  height androidx.compose.animation.core  infiniteRepeatable androidx.compose.animation.core  items androidx.compose.animation.core  kotlinx androidx.compose.animation.core  launch androidx.compose.animation.core  let androidx.compose.animation.core  listOf androidx.compose.animation.core  minOf androidx.compose.animation.core  mutableStateOf androidx.compose.animation.core  offset androidx.compose.animation.core  onGloballyPositioned androidx.compose.animation.core  padding androidx.compose.animation.core  painterResource androidx.compose.animation.core  positionInRoot androidx.compose.animation.core  provideDelegate androidx.compose.animation.core  remember androidx.compose.animation.core  rememberCoroutineScope androidx.compose.animation.core  rememberInfiniteTransition androidx.compose.animation.core  scaleIn androidx.compose.animation.core  scaleOut androidx.compose.animation.core  
setContent androidx.compose.animation.core  setValue androidx.compose.animation.core  size androidx.compose.animation.core  slideInHorizontally androidx.compose.animation.core  slideInVertically androidx.compose.animation.core  slideOutHorizontally androidx.compose.animation.core  slideOutVertically androidx.compose.animation.core  spring androidx.compose.animation.core  statusBarsPadding androidx.compose.animation.core  tween androidx.compose.animation.core  animateFloat 2androidx.compose.animation.core.InfiniteTransition  getANIMATEFloat 2androidx.compose.animation.core.InfiniteTransition  getAnimateFloat 2androidx.compose.animation.core.InfiniteTransition  Restart *androidx.compose.animation.core.RepeatMode  Reverse *androidx.compose.animation.core.RepeatMode  DampingRatioLowBouncy &androidx.compose.animation.core.Spring  DampingRatioMediumBouncy &androidx.compose.animation.core.Spring  
StiffnessHigh &androidx.compose.animation.core.Spring  StiffnessLow &androidx.compose.animation.core.Spring  StiffnessMedium &androidx.compose.animation.core.Spring  StiffnessMediumLow &androidx.compose.animation.core.Spring  BorderStroke androidx.compose.foundation  Image androidx.compose.foundation  
background androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  detectDragGestures $androidx.compose.foundation.gestures  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  AndroidView "androidx.compose.foundation.layout  AnimatedVisibility "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  
AtomicBoolean "androidx.compose.foundation.layout  AtomicReference "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  	CameraAlt "androidx.compose.foundation.layout  CameraSelector "androidx.compose.foundation.layout  CapturedImagePreview "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CenterFocusStrong "androidx.compose.foundation.layout  Check "androidx.compose.foundation.layout  ChessAI "androidx.compose.foundation.layout  ChessActionCard "androidx.compose.foundation.layout  ChessAppHeader "androidx.compose.foundation.layout  ChessBoardControls "androidx.compose.foundation.layout  ChessBoardGuide "androidx.compose.foundation.layout  ChessExpressiveAnimations "androidx.compose.foundation.layout  
ChessPosition "androidx.compose.foundation.layout  ChessSquare "androidx.compose.foundation.layout  ChessVisionAppTheme "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  Clear "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  
ContextCompat "androidx.compose.foundation.layout  
ControlButton "androidx.compose.foundation.layout  CornerGuide "androidx.compose.foundation.layout  DisposableEffect "androidx.compose.foundation.layout  
EaseInBack "androidx.compose.foundation.layout  
EaseInOutSine "androidx.compose.foundation.layout  EaseOutBack "androidx.compose.foundation.layout  EaseOutCubic "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  Error "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  	Executors "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  
FENDisplay "androidx.compose.foundation.layout  FeatureItem "androidx.compose.foundation.layout  FeaturesSection "androidx.compose.foundation.layout  	FlashAuto "androidx.compose.foundation.layout  FlashOff "androidx.compose.foundation.layout  FlashOn "androidx.compose.foundation.layout  
FlipToBack "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  GridOn "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  ImageCapture "androidx.compose.foundation.layout  ImageCaptureException "androidx.compose.foundation.layout  Info "androidx.compose.foundation.layout  	IntOffset "androidx.compose.foundation.layout  InteractiveChessBoard "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  LazyRow "androidx.compose.foundation.layout  LinearEasing "androidx.compose.foundation.layout  	LocalView "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  
MainScreen "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  ModernControlButton "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  PackageManager "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PhotoLibrary "androidx.compose.foundation.layout  
PieceColor "androidx.compose.foundation.layout  	PieceTray "androidx.compose.foundation.layout  
PieceTrayItem "androidx.compose.foundation.layout  	PieceType "androidx.compose.foundation.layout  	PlayArrow "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  PreviewView "androidx.compose.foundation.layout  ProcessCameraProvider "androidx.compose.foundation.layout  
Psychology "androidx.compose.foundation.layout  QuickActionsSection "androidx.compose.foundation.layout  R "androidx.compose.foundation.layout  Rect "androidx.compose.foundation.layout  Refresh "androidx.compose.foundation.layout  
RepeatMode "androidx.compose.foundation.layout  
RestartAlt "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Size "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  Spring "androidx.compose.foundation.layout  Stable "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Surface "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  TAG "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  WindowCompat "androidx.compose.foundation.layout  WindowInsetsCompat "androidx.compose.foundation.layout  WindowInsetsControllerCompat "androidx.compose.foundation.layout  addListener "androidx.compose.foundation.layout  also "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  animateColorAsState "androidx.compose.foundation.layout  animateFloat "androidx.compose.foundation.layout  animateFloatAsState "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  captureImageSafe "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  detectDragGestures "androidx.compose.foundation.layout  downTo "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  enableEdgeToEdge "androidx.compose.foundation.layout  fadeIn "androidx.compose.foundation.layout  fadeOut "androidx.compose.foundation.layout  
fillMaxHeight "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  format "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
graphicsLayer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  infiniteRepeatable "androidx.compose.foundation.layout  items "androidx.compose.foundation.layout  kotlinx "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  minOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  navigationBarsPadding "androidx.compose.foundation.layout  offset "androidx.compose.foundation.layout  onGloballyPositioned "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  painterResource "androidx.compose.foundation.layout  positionInRoot "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  rememberInfiniteTransition "androidx.compose.foundation.layout  run "androidx.compose.foundation.layout  scaleIn "androidx.compose.foundation.layout  scaleOut "androidx.compose.foundation.layout  
setContent "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  slideInHorizontally "androidx.compose.foundation.layout  slideInVertically "androidx.compose.foundation.layout  slideOutHorizontally "androidx.compose.foundation.layout  slideOutVertically "androidx.compose.foundation.layout  spring "androidx.compose.foundation.layout  statusBarsPadding "androidx.compose.foundation.layout  tween "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  	Alignment +androidx.compose.foundation.layout.BoxScope  AndroidView +androidx.compose.foundation.layout.BoxScope  AnimatedVisibility +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  	ArrowBack +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  ButtonDefaults +androidx.compose.foundation.layout.BoxScope  	CameraAlt +androidx.compose.foundation.layout.BoxScope  CapturedImagePreview +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CenterFocusStrong +androidx.compose.foundation.layout.BoxScope  Check +androidx.compose.foundation.layout.BoxScope  ChessAppHeader +androidx.compose.foundation.layout.BoxScope  ChessBoardGuide +androidx.compose.foundation.layout.BoxScope  
ChessPosition +androidx.compose.foundation.layout.BoxScope  ChessSquare +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Clear +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  CornerGuide +androidx.compose.foundation.layout.BoxScope  EaseOutCubic +androidx.compose.foundation.layout.BoxScope  Edit +androidx.compose.foundation.layout.BoxScope  
FENDisplay +androidx.compose.foundation.layout.BoxScope  FeaturesSection +androidx.compose.foundation.layout.BoxScope  	FlashAuto +androidx.compose.foundation.layout.BoxScope  FlashOff +androidx.compose.foundation.layout.BoxScope  FlashOn +androidx.compose.foundation.layout.BoxScope  
FlipToBack +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  ImageCapture +androidx.compose.foundation.layout.BoxScope  Info +androidx.compose.foundation.layout.BoxScope  	IntOffset +androidx.compose.foundation.layout.BoxScope  InteractiveChessBoard +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  ModernControlButton +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  PhotoLibrary +androidx.compose.foundation.layout.BoxScope  	PieceTray +androidx.compose.foundation.layout.BoxScope  	PlayArrow +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  
Psychology +androidx.compose.foundation.layout.BoxScope  QuickActionsSection +androidx.compose.foundation.layout.BoxScope  R +androidx.compose.foundation.layout.BoxScope  Refresh +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  aspectRatio +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  captureImageSafe +androidx.compose.foundation.layout.BoxScope  downTo +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.BoxScope  fadeIn +androidx.compose.foundation.layout.BoxScope  fadeOut +androidx.compose.foundation.layout.BoxScope  
fillMaxHeight +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  getANDROIDX +androidx.compose.foundation.layout.BoxScope  getAPPLY +androidx.compose.foundation.layout.BoxScope  getASPECTRatio +androidx.compose.foundation.layout.BoxScope  getAndroidx +androidx.compose.foundation.layout.BoxScope  getApply +androidx.compose.foundation.layout.BoxScope  getAspectRatio +androidx.compose.foundation.layout.BoxScope  
getBACKGROUND +androidx.compose.foundation.layout.BoxScope  
getBackground +androidx.compose.foundation.layout.BoxScope  getCAPTUREImageSafe +androidx.compose.foundation.layout.BoxScope  getCaptureImageSafe +androidx.compose.foundation.layout.BoxScope  	getDOWNTo +androidx.compose.foundation.layout.BoxScope  	getDownTo +androidx.compose.foundation.layout.BoxScope  getEMPTYList +androidx.compose.foundation.layout.BoxScope  getEmptyList +androidx.compose.foundation.layout.BoxScope  	getFADEIn +androidx.compose.foundation.layout.BoxScope  
getFADEOut +androidx.compose.foundation.layout.BoxScope  getFILLMaxHeight +androidx.compose.foundation.layout.BoxScope  getFILLMaxSize +androidx.compose.foundation.layout.BoxScope  getFILLMaxWidth +androidx.compose.foundation.layout.BoxScope  	getFadeIn +androidx.compose.foundation.layout.BoxScope  
getFadeOut +androidx.compose.foundation.layout.BoxScope  getFillMaxHeight +androidx.compose.foundation.layout.BoxScope  getFillMaxSize +androidx.compose.foundation.layout.BoxScope  getFillMaxWidth +androidx.compose.foundation.layout.BoxScope  getGRAPHICSLayer +androidx.compose.foundation.layout.BoxScope  getGraphicsLayer +androidx.compose.foundation.layout.BoxScope  	getHEIGHT +androidx.compose.foundation.layout.BoxScope  	getHeight +androidx.compose.foundation.layout.BoxScope  getLET +androidx.compose.foundation.layout.BoxScope  	getLISTOf +androidx.compose.foundation.layout.BoxScope  getLet +androidx.compose.foundation.layout.BoxScope  	getListOf +androidx.compose.foundation.layout.BoxScope  getNAVIGATIONBarsPadding +androidx.compose.foundation.layout.BoxScope  getNavigationBarsPadding +androidx.compose.foundation.layout.BoxScope  	getOFFSET +androidx.compose.foundation.layout.BoxScope  	getOffset +androidx.compose.foundation.layout.BoxScope  
getPADDING +androidx.compose.foundation.layout.BoxScope  getPAINTERResource +androidx.compose.foundation.layout.BoxScope  
getPadding +androidx.compose.foundation.layout.BoxScope  getPainterResource +androidx.compose.foundation.layout.BoxScope  
getSCALEIn +androidx.compose.foundation.layout.BoxScope  getSCALEOut +androidx.compose.foundation.layout.BoxScope  getSIZE +androidx.compose.foundation.layout.BoxScope  getSLIDEInVertically +androidx.compose.foundation.layout.BoxScope  getSTATUSBarsPadding +androidx.compose.foundation.layout.BoxScope  
getScaleIn +androidx.compose.foundation.layout.BoxScope  getScaleOut +androidx.compose.foundation.layout.BoxScope  getSize +androidx.compose.foundation.layout.BoxScope  getSlideInVertically +androidx.compose.foundation.layout.BoxScope  getStatusBarsPadding +androidx.compose.foundation.layout.BoxScope  getTWEEN +androidx.compose.foundation.layout.BoxScope  getTween +androidx.compose.foundation.layout.BoxScope  getWIDTH +androidx.compose.foundation.layout.BoxScope  getWidth +androidx.compose.foundation.layout.BoxScope  
graphicsLayer +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  invoke +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  navigationBarsPadding +androidx.compose.foundation.layout.BoxScope  offset +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  painterResource +androidx.compose.foundation.layout.BoxScope  scaleIn +androidx.compose.foundation.layout.BoxScope  scaleOut +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  slideInVertically +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  statusBarsPadding +androidx.compose.foundation.layout.BoxScope  tween +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  AnimatedVisibility .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  ArrowForward .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  	CameraAlt .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CenterFocusStrong .androidx.compose.foundation.layout.ColumnScope  Check .androidx.compose.foundation.layout.ColumnScope  ChessActionCard .androidx.compose.foundation.layout.ColumnScope  ChessAppHeader .androidx.compose.foundation.layout.ColumnScope  ChessBoardControls .androidx.compose.foundation.layout.ColumnScope  
ChessPosition .androidx.compose.foundation.layout.ColumnScope  ChessSquare .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  Clear .androidx.compose.foundation.layout.ColumnScope  Color .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  
ContextCompat .androidx.compose.foundation.layout.ColumnScope  
ControlButton .androidx.compose.foundation.layout.ColumnScope  EaseOutCubic .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  
FENDisplay .androidx.compose.foundation.layout.ColumnScope  FeatureItem .androidx.compose.foundation.layout.ColumnScope  FeaturesSection .androidx.compose.foundation.layout.ColumnScope  	FlashAuto .androidx.compose.foundation.layout.ColumnScope  FlashOff .androidx.compose.foundation.layout.ColumnScope  FlashOn .androidx.compose.foundation.layout.ColumnScope  
FlipToBack .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  GridOn .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  Image .androidx.compose.foundation.layout.ColumnScope  ImageCapture .androidx.compose.foundation.layout.ColumnScope  	IntOffset .androidx.compose.foundation.layout.ColumnScope  InteractiveChessBoard .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  LazyRow .androidx.compose.foundation.layout.ColumnScope  Manifest .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  ModernControlButton .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Offset .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  PackageManager .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  PhotoLibrary .androidx.compose.foundation.layout.ColumnScope  	PieceTray .androidx.compose.foundation.layout.ColumnScope  
PieceTrayItem .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  
Psychology .androidx.compose.foundation.layout.ColumnScope  QuickActionsSection .androidx.compose.foundation.layout.ColumnScope  R .androidx.compose.foundation.layout.ColumnScope  Rect .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  
RestartAlt .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Size .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  String .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  aspectRatio .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  captureImageSafe .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  downTo .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fadeIn .androidx.compose.foundation.layout.ColumnScope  fadeOut .androidx.compose.foundation.layout.ColumnScope  
fillMaxHeight .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  format .androidx.compose.foundation.layout.ColumnScope  getANDROIDX .androidx.compose.foundation.layout.ColumnScope  getASPECTRatio .androidx.compose.foundation.layout.ColumnScope  getAndroidx .androidx.compose.foundation.layout.ColumnScope  getAspectRatio .androidx.compose.foundation.layout.ColumnScope  
getBACKGROUND .androidx.compose.foundation.layout.ColumnScope  
getBackground .androidx.compose.foundation.layout.ColumnScope  getCAPTUREImageSafe .androidx.compose.foundation.layout.ColumnScope  getCLICKABLE .androidx.compose.foundation.layout.ColumnScope  getCaptureImageSafe .androidx.compose.foundation.layout.ColumnScope  getClickable .androidx.compose.foundation.layout.ColumnScope  	getDOWNTo .androidx.compose.foundation.layout.ColumnScope  	getDownTo .androidx.compose.foundation.layout.ColumnScope  getEMPTYList .androidx.compose.foundation.layout.ColumnScope  getEmptyList .androidx.compose.foundation.layout.ColumnScope  	getFADEIn .androidx.compose.foundation.layout.ColumnScope  
getFADEOut .androidx.compose.foundation.layout.ColumnScope  getFILLMaxHeight .androidx.compose.foundation.layout.ColumnScope  getFILLMaxSize .androidx.compose.foundation.layout.ColumnScope  getFILLMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getFORMAT .androidx.compose.foundation.layout.ColumnScope  	getFadeIn .androidx.compose.foundation.layout.ColumnScope  
getFadeOut .androidx.compose.foundation.layout.ColumnScope  getFillMaxHeight .androidx.compose.foundation.layout.ColumnScope  getFillMaxSize .androidx.compose.foundation.layout.ColumnScope  getFillMaxWidth .androidx.compose.foundation.layout.ColumnScope  	getFormat .androidx.compose.foundation.layout.ColumnScope  getGRAPHICSLayer .androidx.compose.foundation.layout.ColumnScope  getGraphicsLayer .androidx.compose.foundation.layout.ColumnScope  	getHEIGHT .androidx.compose.foundation.layout.ColumnScope  	getHeight .androidx.compose.foundation.layout.ColumnScope  getLET .androidx.compose.foundation.layout.ColumnScope  	getLISTOf .androidx.compose.foundation.layout.ColumnScope  getLet .androidx.compose.foundation.layout.ColumnScope  	getListOf .androidx.compose.foundation.layout.ColumnScope  	getOFFSET .androidx.compose.foundation.layout.ColumnScope  getONGloballyPositioned .androidx.compose.foundation.layout.ColumnScope  	getOffset .androidx.compose.foundation.layout.ColumnScope  getOnGloballyPositioned .androidx.compose.foundation.layout.ColumnScope  
getPADDING .androidx.compose.foundation.layout.ColumnScope  getPAINTERResource .androidx.compose.foundation.layout.ColumnScope  getPOSITIONInRoot .androidx.compose.foundation.layout.ColumnScope  
getPadding .androidx.compose.foundation.layout.ColumnScope  getPainterResource .androidx.compose.foundation.layout.ColumnScope  getPositionInRoot .androidx.compose.foundation.layout.ColumnScope  
getSCALEIn .androidx.compose.foundation.layout.ColumnScope  getSCALEOut .androidx.compose.foundation.layout.ColumnScope  getSIZE .androidx.compose.foundation.layout.ColumnScope  getSLIDEInHorizontally .androidx.compose.foundation.layout.ColumnScope  getSLIDEInVertically .androidx.compose.foundation.layout.ColumnScope  getSLIDEOutHorizontally .androidx.compose.foundation.layout.ColumnScope  
getScaleIn .androidx.compose.foundation.layout.ColumnScope  getScaleOut .androidx.compose.foundation.layout.ColumnScope  getSize .androidx.compose.foundation.layout.ColumnScope  getSlideInHorizontally .androidx.compose.foundation.layout.ColumnScope  getSlideInVertically .androidx.compose.foundation.layout.ColumnScope  getSlideOutHorizontally .androidx.compose.foundation.layout.ColumnScope  getTWEEN .androidx.compose.foundation.layout.ColumnScope  getTween .androidx.compose.foundation.layout.ColumnScope  
graphicsLayer .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  invoke .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  offset .androidx.compose.foundation.layout.ColumnScope  onGloballyPositioned .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  painterResource .androidx.compose.foundation.layout.ColumnScope  positionInRoot .androidx.compose.foundation.layout.ColumnScope  scaleIn .androidx.compose.foundation.layout.ColumnScope  scaleOut .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  slideInHorizontally .androidx.compose.foundation.layout.ColumnScope  slideInVertically .androidx.compose.foundation.layout.ColumnScope  slideOutHorizontally .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  tween .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  AnimatedVisibility +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  ArrowForward +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  	CameraAlt +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CenterFocusStrong +androidx.compose.foundation.layout.RowScope  Check +androidx.compose.foundation.layout.RowScope  ChessActionCard +androidx.compose.foundation.layout.RowScope  
ChessPosition +androidx.compose.foundation.layout.RowScope  ChessSquare +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  Clear +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  
ControlButton +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  Error +androidx.compose.foundation.layout.RowScope  	FlashAuto +androidx.compose.foundation.layout.RowScope  FlashOff +androidx.compose.foundation.layout.RowScope  FlashOn +androidx.compose.foundation.layout.RowScope  
FlipToBack +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  GridOn +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  ImageCapture +androidx.compose.foundation.layout.RowScope  Info +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  ModernControlButton +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  PhotoLibrary +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  
Psychology +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  
RestartAlt +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  captureImageSafe +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fadeIn +androidx.compose.foundation.layout.RowScope  fadeOut +androidx.compose.foundation.layout.RowScope  
fillMaxHeight +androidx.compose.foundation.layout.RowScope  getANDROIDX +androidx.compose.foundation.layout.RowScope  getAndroidx +androidx.compose.foundation.layout.RowScope  
getBACKGROUND +androidx.compose.foundation.layout.RowScope  
getBackground +androidx.compose.foundation.layout.RowScope  getCAPTUREImageSafe +androidx.compose.foundation.layout.RowScope  getCaptureImageSafe +androidx.compose.foundation.layout.RowScope  	getFADEIn +androidx.compose.foundation.layout.RowScope  
getFADEOut +androidx.compose.foundation.layout.RowScope  getFILLMaxHeight +androidx.compose.foundation.layout.RowScope  	getFadeIn +androidx.compose.foundation.layout.RowScope  
getFadeOut +androidx.compose.foundation.layout.RowScope  getFillMaxHeight +androidx.compose.foundation.layout.RowScope  	getHEIGHT +androidx.compose.foundation.layout.RowScope  	getHeight +androidx.compose.foundation.layout.RowScope  
getPADDING +androidx.compose.foundation.layout.RowScope  
getPadding +androidx.compose.foundation.layout.RowScope  getSIZE +androidx.compose.foundation.layout.RowScope  getSLIDEInHorizontally +androidx.compose.foundation.layout.RowScope  getSLIDEOutHorizontally +androidx.compose.foundation.layout.RowScope  getSize +androidx.compose.foundation.layout.RowScope  getSlideInHorizontally +androidx.compose.foundation.layout.RowScope  getSlideOutHorizontally +androidx.compose.foundation.layout.RowScope  getWIDTH +androidx.compose.foundation.layout.RowScope  getWidth +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  invoke +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  slideInHorizontally +androidx.compose.foundation.layout.RowScope  slideOutHorizontally +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  LazyRow  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  AnimatedVisibility .androidx.compose.foundation.lazy.LazyItemScope  Arrangement .androidx.compose.foundation.lazy.LazyItemScope  Box .androidx.compose.foundation.lazy.LazyItemScope  Brush .androidx.compose.foundation.lazy.LazyItemScope  Card .androidx.compose.foundation.lazy.LazyItemScope  CardDefaults .androidx.compose.foundation.lazy.LazyItemScope  Color .androidx.compose.foundation.lazy.LazyItemScope  EaseOutCubic .androidx.compose.foundation.lazy.LazyItemScope  Edit .androidx.compose.foundation.lazy.LazyItemScope  
FENDisplay .androidx.compose.foundation.lazy.LazyItemScope  FeaturesSection .androidx.compose.foundation.lazy.LazyItemScope  
FlipToBack .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  InteractiveChessBoard .androidx.compose.foundation.lazy.LazyItemScope  ModernControlButton .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  	PieceTray .androidx.compose.foundation.lazy.LazyItemScope  
PieceTrayItem .androidx.compose.foundation.lazy.LazyItemScope  	PlayArrow .androidx.compose.foundation.lazy.LazyItemScope  QuickActionsSection .androidx.compose.foundation.lazy.LazyItemScope  Refresh .androidx.compose.foundation.lazy.LazyItemScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  aspectRatio .androidx.compose.foundation.lazy.LazyItemScope  
background .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fadeIn .androidx.compose.foundation.lazy.LazyItemScope  fillMaxSize .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  getASPECTRatio .androidx.compose.foundation.lazy.LazyItemScope  getAspectRatio .androidx.compose.foundation.lazy.LazyItemScope  
getBACKGROUND .androidx.compose.foundation.lazy.LazyItemScope  
getBackground .androidx.compose.foundation.lazy.LazyItemScope  	getFADEIn .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxSize .androidx.compose.foundation.lazy.LazyItemScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  	getFadeIn .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxSize .androidx.compose.foundation.lazy.LazyItemScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyItemScope  	getHeight .androidx.compose.foundation.lazy.LazyItemScope  	getLISTOf .androidx.compose.foundation.lazy.LazyItemScope  	getListOf .androidx.compose.foundation.lazy.LazyItemScope  
getPADDING .androidx.compose.foundation.lazy.LazyItemScope  
getPadding .androidx.compose.foundation.lazy.LazyItemScope  getSLIDEInVertically .androidx.compose.foundation.lazy.LazyItemScope  getSlideInVertically .androidx.compose.foundation.lazy.LazyItemScope  getTWEEN .androidx.compose.foundation.lazy.LazyItemScope  getTween .androidx.compose.foundation.lazy.LazyItemScope  height .androidx.compose.foundation.lazy.LazyItemScope  invoke .androidx.compose.foundation.lazy.LazyItemScope  listOf .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  slideInVertically .androidx.compose.foundation.lazy.LazyItemScope  tween .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  AnimatedVisibility .androidx.compose.foundation.lazy.LazyListScope  Arrangement .androidx.compose.foundation.lazy.LazyListScope  Box .androidx.compose.foundation.lazy.LazyListScope  Brush .androidx.compose.foundation.lazy.LazyListScope  Card .androidx.compose.foundation.lazy.LazyListScope  CardDefaults .androidx.compose.foundation.lazy.LazyListScope  Color .androidx.compose.foundation.lazy.LazyListScope  EaseOutCubic .androidx.compose.foundation.lazy.LazyListScope  Edit .androidx.compose.foundation.lazy.LazyListScope  
FENDisplay .androidx.compose.foundation.lazy.LazyListScope  FeaturesSection .androidx.compose.foundation.lazy.LazyListScope  
FlipToBack .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  InteractiveChessBoard .androidx.compose.foundation.lazy.LazyListScope  ModernControlButton .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  	PieceTray .androidx.compose.foundation.lazy.LazyListScope  
PieceTrayItem .androidx.compose.foundation.lazy.LazyListScope  	PlayArrow .androidx.compose.foundation.lazy.LazyListScope  QuickActionsSection .androidx.compose.foundation.lazy.LazyListScope  Refresh .androidx.compose.foundation.lazy.LazyListScope  RoundedCornerShape .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  aspectRatio .androidx.compose.foundation.lazy.LazyListScope  
background .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fadeIn .androidx.compose.foundation.lazy.LazyListScope  fillMaxSize .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  getASPECTRatio .androidx.compose.foundation.lazy.LazyListScope  getAspectRatio .androidx.compose.foundation.lazy.LazyListScope  
getBACKGROUND .androidx.compose.foundation.lazy.LazyListScope  
getBackground .androidx.compose.foundation.lazy.LazyListScope  	getFADEIn .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxSize .androidx.compose.foundation.lazy.LazyListScope  getFILLMaxWidth .androidx.compose.foundation.lazy.LazyListScope  	getFadeIn .androidx.compose.foundation.lazy.LazyListScope  getFillMaxSize .androidx.compose.foundation.lazy.LazyListScope  getFillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  	getHEIGHT .androidx.compose.foundation.lazy.LazyListScope  	getHeight .androidx.compose.foundation.lazy.LazyListScope  getITEMS .androidx.compose.foundation.lazy.LazyListScope  getItems .androidx.compose.foundation.lazy.LazyListScope  	getLISTOf .androidx.compose.foundation.lazy.LazyListScope  	getListOf .androidx.compose.foundation.lazy.LazyListScope  
getPADDING .androidx.compose.foundation.lazy.LazyListScope  
getPadding .androidx.compose.foundation.lazy.LazyListScope  getSLIDEInVertically .androidx.compose.foundation.lazy.LazyListScope  getSlideInVertically .androidx.compose.foundation.lazy.LazyListScope  getTWEEN .androidx.compose.foundation.lazy.LazyListScope  getTween .androidx.compose.foundation.lazy.LazyListScope  height .androidx.compose.foundation.lazy.LazyListScope  invoke .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  listOf .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  slideInVertically .androidx.compose.foundation.lazy.LazyListScope  tween .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  AutoMirrored %androidx.compose.material.icons.Icons  Default %androidx.compose.material.icons.Icons  Filled 2androidx.compose.material.icons.Icons.AutoMirrored  	ArrowBack 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  ArrowForward 9androidx.compose.material.icons.Icons.AutoMirrored.Filled  	CameraAlt ,androidx.compose.material.icons.Icons.Filled  CenterFocusStrong ,androidx.compose.material.icons.Icons.Filled  Check ,androidx.compose.material.icons.Icons.Filled  Clear ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Error ,androidx.compose.material.icons.Icons.Filled  	FlashAuto ,androidx.compose.material.icons.Icons.Filled  FlashOff ,androidx.compose.material.icons.Icons.Filled  FlashOn ,androidx.compose.material.icons.Icons.Filled  
FlipToBack ,androidx.compose.material.icons.Icons.Filled  GridOn ,androidx.compose.material.icons.Icons.Filled  Info ,androidx.compose.material.icons.Icons.Filled  PhotoLibrary ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  
Psychology ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  
RestartAlt ,androidx.compose.material.icons.Icons.Filled  	ArrowBack 3androidx.compose.material.icons.automirrored.filled  ArrowForward 3androidx.compose.material.icons.automirrored.filled  ActivityResultContracts &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  AndroidView &androidx.compose.material.icons.filled  AnimatedVisibility &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  
AtomicBoolean &androidx.compose.material.icons.filled  AtomicReference &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Brush &androidx.compose.material.icons.filled  Button &androidx.compose.material.icons.filled  ButtonDefaults &androidx.compose.material.icons.filled  	CameraAlt &androidx.compose.material.icons.filled  CameraSelector &androidx.compose.material.icons.filled  CapturedImagePreview &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CenterFocusStrong &androidx.compose.material.icons.filled  Check &androidx.compose.material.icons.filled  ChessAI &androidx.compose.material.icons.filled  ChessActionCard &androidx.compose.material.icons.filled  ChessAppHeader &androidx.compose.material.icons.filled  ChessBoardGuide &androidx.compose.material.icons.filled  
ChessPosition &androidx.compose.material.icons.filled  ChessSquare &androidx.compose.material.icons.filled  ChessVisionAppTheme &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  CircularProgressIndicator &androidx.compose.material.icons.filled  Clear &androidx.compose.material.icons.filled  Color &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  
ContextCompat &androidx.compose.material.icons.filled  
ControlButton &androidx.compose.material.icons.filled  CornerGuide &androidx.compose.material.icons.filled  DisposableEffect &androidx.compose.material.icons.filled  EaseOutCubic &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Error &androidx.compose.material.icons.filled  	Exception &androidx.compose.material.icons.filled  	Executors &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FENDisplay &androidx.compose.material.icons.filled  FeatureItem &androidx.compose.material.icons.filled  FeaturesSection &androidx.compose.material.icons.filled  	FlashAuto &androidx.compose.material.icons.filled  FlashOff &androidx.compose.material.icons.filled  FlashOn &androidx.compose.material.icons.filled  
FlipToBack &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  GridOn &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Image &androidx.compose.material.icons.filled  ImageCapture &androidx.compose.material.icons.filled  ImageCaptureException &androidx.compose.material.icons.filled  Info &androidx.compose.material.icons.filled  	IntOffset &androidx.compose.material.icons.filled  InteractiveChessBoard &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  
LazyColumn &androidx.compose.material.icons.filled  LazyRow &androidx.compose.material.icons.filled  	LocalView &androidx.compose.material.icons.filled  Locale &androidx.compose.material.icons.filled  Log &androidx.compose.material.icons.filled  
MainScreen &androidx.compose.material.icons.filled  Manifest &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  ModernControlButton &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  Offset &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  OutlinedTextFieldDefaults &androidx.compose.material.icons.filled  PackageManager &androidx.compose.material.icons.filled  
PaddingValues &androidx.compose.material.icons.filled  PhotoLibrary &androidx.compose.material.icons.filled  
PieceColor &androidx.compose.material.icons.filled  	PieceTray &androidx.compose.material.icons.filled  
PieceTrayItem &androidx.compose.material.icons.filled  	PieceType &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  Preview &androidx.compose.material.icons.filled  PreviewView &androidx.compose.material.icons.filled  ProcessCameraProvider &androidx.compose.material.icons.filled  
Psychology &androidx.compose.material.icons.filled  QuickActionsSection &androidx.compose.material.icons.filled  R &androidx.compose.material.icons.filled  Rect &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  
RestartAlt &androidx.compose.material.icons.filled  RoundedCornerShape &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Size &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  Spring &androidx.compose.material.icons.filled  Stable &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Surface &androidx.compose.material.icons.filled  System &androidx.compose.material.icons.filled  TAG &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  	TextAlign &androidx.compose.material.icons.filled  Toast &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  WindowCompat &androidx.compose.material.icons.filled  WindowInsetsCompat &androidx.compose.material.icons.filled  WindowInsetsControllerCompat &androidx.compose.material.icons.filled  addListener &androidx.compose.material.icons.filled  also &androidx.compose.material.icons.filled  android &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  animateColorAsState &androidx.compose.material.icons.filled  animateFloatAsState &androidx.compose.material.icons.filled  apply &androidx.compose.material.icons.filled  aspectRatio &androidx.compose.material.icons.filled  
background &androidx.compose.material.icons.filled  captureImageSafe &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  delay &androidx.compose.material.icons.filled  detectDragGestures &androidx.compose.material.icons.filled  downTo &androidx.compose.material.icons.filled  	emptyList &androidx.compose.material.icons.filled  enableEdgeToEdge &androidx.compose.material.icons.filled  fadeIn &androidx.compose.material.icons.filled  fadeOut &androidx.compose.material.icons.filled  
fillMaxHeight &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  format &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  
graphicsLayer &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  items &androidx.compose.material.icons.filled  kotlinx &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  let &androidx.compose.material.icons.filled  listOf &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  navigationBarsPadding &androidx.compose.material.icons.filled  offset &androidx.compose.material.icons.filled  onGloballyPositioned &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  painterResource &androidx.compose.material.icons.filled  positionInRoot &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberCoroutineScope &androidx.compose.material.icons.filled  run &androidx.compose.material.icons.filled  scaleIn &androidx.compose.material.icons.filled  scaleOut &androidx.compose.material.icons.filled  
setContent &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  slideInHorizontally &androidx.compose.material.icons.filled  slideInVertically &androidx.compose.material.icons.filled  slideOutHorizontally &androidx.compose.material.icons.filled  slideOutVertically &androidx.compose.material.icons.filled  spring &androidx.compose.material.icons.filled  statusBarsPadding &androidx.compose.material.icons.filled  tween &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  AndroidView androidx.compose.material3  AnimatedVisibility androidx.compose.material3  Arrangement androidx.compose.material3  
AtomicBoolean androidx.compose.material3  AtomicReference androidx.compose.material3  Box androidx.compose.material3  Brush androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  ButtonElevation androidx.compose.material3  	CameraAlt androidx.compose.material3  CameraSelector androidx.compose.material3  CapturedImagePreview androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
CardElevation androidx.compose.material3  CenterFocusStrong androidx.compose.material3  Check androidx.compose.material3  ChessAI androidx.compose.material3  ChessActionCard androidx.compose.material3  ChessAppHeader androidx.compose.material3  ChessBoardGuide androidx.compose.material3  
ChessPosition androidx.compose.material3  ChessSquare androidx.compose.material3  ChessVisionAppTheme androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  Clear androidx.compose.material3  Color androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  
ContextCompat androidx.compose.material3  
ControlButton androidx.compose.material3  CornerGuide androidx.compose.material3  DisposableEffect androidx.compose.material3  EaseOutCubic androidx.compose.material3  Edit androidx.compose.material3  Error androidx.compose.material3  	Exception androidx.compose.material3  	Executors androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
FENDisplay androidx.compose.material3  FeatureItem androidx.compose.material3  FeaturesSection androidx.compose.material3  	FlashAuto androidx.compose.material3  FlashOff androidx.compose.material3  FlashOn androidx.compose.material3  
FlipToBack androidx.compose.material3  
FontWeight androidx.compose.material3  GridOn androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Image androidx.compose.material3  ImageCapture androidx.compose.material3  ImageCaptureException androidx.compose.material3  Info androidx.compose.material3  	IntOffset androidx.compose.material3  InteractiveChessBoard androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  LazyRow androidx.compose.material3  	LocalView androidx.compose.material3  Locale androidx.compose.material3  Log androidx.compose.material3  
MainScreen androidx.compose.material3  Manifest androidx.compose.material3  
MaterialTheme androidx.compose.material3  ModernControlButton androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  OutlinedTextField androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  PackageManager androidx.compose.material3  
PaddingValues androidx.compose.material3  PhotoLibrary androidx.compose.material3  
PieceColor androidx.compose.material3  	PieceTray androidx.compose.material3  
PieceTrayItem androidx.compose.material3  	PieceType androidx.compose.material3  	PlayArrow androidx.compose.material3  Preview androidx.compose.material3  PreviewView androidx.compose.material3  ProcessCameraProvider androidx.compose.material3  
Psychology androidx.compose.material3  QuickActionsSection androidx.compose.material3  R androidx.compose.material3  Rect androidx.compose.material3  Refresh androidx.compose.material3  
RestartAlt androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Size androidx.compose.material3  Spacer androidx.compose.material3  Spring androidx.compose.material3  Stable androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  System androidx.compose.material3  TAG androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  TextFieldColors androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  WindowCompat androidx.compose.material3  WindowInsetsCompat androidx.compose.material3  WindowInsetsControllerCompat androidx.compose.material3  addListener androidx.compose.material3  also androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  animateColorAsState androidx.compose.material3  animateFloatAsState androidx.compose.material3  apply androidx.compose.material3  aspectRatio androidx.compose.material3  
background androidx.compose.material3  captureImageSafe androidx.compose.material3  	clickable androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  detectDragGestures androidx.compose.material3  downTo androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  enableEdgeToEdge androidx.compose.material3  fadeIn androidx.compose.material3  fadeOut androidx.compose.material3  
fillMaxHeight androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  format androidx.compose.material3  getValue androidx.compose.material3  
graphicsLayer androidx.compose.material3  height androidx.compose.material3  items androidx.compose.material3  kotlinx androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  navigationBarsPadding androidx.compose.material3  offset androidx.compose.material3  onGloballyPositioned androidx.compose.material3  padding androidx.compose.material3  painterResource androidx.compose.material3  positionInRoot androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  run androidx.compose.material3  scaleIn androidx.compose.material3  scaleOut androidx.compose.material3  
setContent androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  slideInHorizontally androidx.compose.material3  slideInVertically androidx.compose.material3  slideOutHorizontally androidx.compose.material3  slideOutVertically androidx.compose.material3  spring androidx.compose.material3  statusBarsPadding androidx.compose.material3  tween androidx.compose.material3  width androidx.compose.material3  buttonColors )androidx.compose.material3.ButtonDefaults  buttonElevation )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
cardElevation 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  invoke (androidx.compose.material3.MaterialTheme  
typography (androidx.compose.material3.MaterialTheme  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  headlineMedium %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  
labelSmall %androidx.compose.material3.Typography  
titleLarge %androidx.compose.material3.Typography  titleMedium %androidx.compose.material3.Typography  ActivityResultContracts androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  AndroidView androidx.compose.runtime  AnimatedVisibility androidx.compose.runtime  Arrangement androidx.compose.runtime  Array androidx.compose.runtime  
AtomicBoolean androidx.compose.runtime  AtomicReference androidx.compose.runtime  Box androidx.compose.runtime  Brush androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  	CameraAlt androidx.compose.runtime  CameraSelector androidx.compose.runtime  CapturedImagePreview androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CenterFocusStrong androidx.compose.runtime  Check androidx.compose.runtime  ChessAI androidx.compose.runtime  ChessActionCard androidx.compose.runtime  ChessAppHeader androidx.compose.runtime  ChessBoardControls androidx.compose.runtime  ChessBoardGuide androidx.compose.runtime  ChessExpressiveAnimations androidx.compose.runtime  
ChessPiece androidx.compose.runtime  
ChessPosition androidx.compose.runtime  ChessSquare androidx.compose.runtime  ChessVisionAppTheme androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  Clear androidx.compose.runtime  Color androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  CompositionLocalProvider androidx.compose.runtime  
ContextCompat androidx.compose.runtime  
ControlButton androidx.compose.runtime  CornerGuide androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  
EaseInBack androidx.compose.runtime  
EaseInOutSine androidx.compose.runtime  EaseOutBack androidx.compose.runtime  EaseOutCubic androidx.compose.runtime  Edit androidx.compose.runtime  Error androidx.compose.runtime  	Exception androidx.compose.runtime  	Executors androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  
FENDisplay androidx.compose.runtime  FeatureItem androidx.compose.runtime  FeaturesSection androidx.compose.runtime  	FlashAuto androidx.compose.runtime  FlashOff androidx.compose.runtime  FlashOn androidx.compose.runtime  
FlipToBack androidx.compose.runtime  
FontWeight androidx.compose.runtime  GridOn androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Image androidx.compose.runtime  ImageCapture androidx.compose.runtime  ImageCaptureException androidx.compose.runtime  Info androidx.compose.runtime  	IntOffset androidx.compose.runtime  InteractiveChessBoard androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LazyRow androidx.compose.runtime  LinearEasing androidx.compose.runtime  	LocalView androidx.compose.runtime  Locale androidx.compose.runtime  Log androidx.compose.runtime  
MainScreen androidx.compose.runtime  Manifest androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  ModernControlButton androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  PackageManager androidx.compose.runtime  
PaddingValues androidx.compose.runtime  Pair androidx.compose.runtime  PhotoLibrary androidx.compose.runtime  
PieceColor androidx.compose.runtime  	PieceTray androidx.compose.runtime  
PieceTrayItem androidx.compose.runtime  	PieceType androidx.compose.runtime  	PlayArrow androidx.compose.runtime  Preview androidx.compose.runtime  PreviewView androidx.compose.runtime  ProcessCameraProvider androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  
ProvidedValue androidx.compose.runtime  
Psychology androidx.compose.runtime  QuickActionsSection androidx.compose.runtime  R androidx.compose.runtime  Rect androidx.compose.runtime  Refresh androidx.compose.runtime  
RepeatMode androidx.compose.runtime  
RestartAlt androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Row androidx.compose.runtime  
SideEffect androidx.compose.runtime  Size androidx.compose.runtime  Spacer androidx.compose.runtime  Spring androidx.compose.runtime  Stable androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  
StringBuilder androidx.compose.runtime  Surface androidx.compose.runtime  System androidx.compose.runtime  TAG androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  Toast androidx.compose.runtime  Unit androidx.compose.runtime  WindowCompat androidx.compose.runtime  WindowInsetsCompat androidx.compose.runtime  WindowInsetsControllerCompat androidx.compose.runtime  addListener androidx.compose.runtime  also androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  animateColorAsState androidx.compose.runtime  animateFloat androidx.compose.runtime  animateFloatAsState androidx.compose.runtime  apply androidx.compose.runtime  aspectRatio androidx.compose.runtime  
background androidx.compose.runtime  captureImageSafe androidx.compose.runtime  	clickable androidx.compose.runtime  coerceIn androidx.compose.runtime  delay androidx.compose.runtime  detectDragGestures androidx.compose.runtime  
digitToInt androidx.compose.runtime  downTo androidx.compose.runtime  	emptyList androidx.compose.runtime  enableEdgeToEdge androidx.compose.runtime  fadeIn androidx.compose.runtime  fadeOut androidx.compose.runtime  
fillMaxHeight androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  format androidx.compose.runtime  getValue androidx.compose.runtime  
graphicsLayer androidx.compose.runtime  height androidx.compose.runtime  indices androidx.compose.runtime  infiniteRepeatable androidx.compose.runtime  isDigit androidx.compose.runtime  isUpperCase androidx.compose.runtime  items androidx.compose.runtime  iterator androidx.compose.runtime  kotlinx androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  
lowercaseChar androidx.compose.runtime  minOf androidx.compose.runtime  
mutableListOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  navigationBarsPadding androidx.compose.runtime  offset androidx.compose.runtime  onGloballyPositioned androidx.compose.runtime  padding androidx.compose.runtime  painterResource androidx.compose.runtime  
plusAssign androidx.compose.runtime  positionInRoot androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  rememberInfiniteTransition androidx.compose.runtime  run androidx.compose.runtime  scaleIn androidx.compose.runtime  scaleOut androidx.compose.runtime  
setContent androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  slideInHorizontally androidx.compose.runtime  slideInVertically androidx.compose.runtime  slideOutHorizontally androidx.compose.runtime  slideOutVertically androidx.compose.runtime  split androidx.compose.runtime  spring androidx.compose.runtime  staticCompositionLocalOf androidx.compose.runtime  statusBarsPadding androidx.compose.runtime  to androidx.compose.runtime  tween androidx.compose.runtime  
uppercaseChar androidx.compose.runtime  width androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  provides )androidx.compose.runtime.CompositionLocal  Log .androidx.compose.runtime.DisposableEffectScope  WindowCompat .androidx.compose.runtime.DisposableEffectScope  WindowInsetsCompat .androidx.compose.runtime.DisposableEffectScope  WindowInsetsControllerCompat .androidx.compose.runtime.DisposableEffectScope  	onDispose .androidx.compose.runtime.DisposableEffectScope  getGETValue %androidx.compose.runtime.MutableState  getGetValue %androidx.compose.runtime.MutableState  getPROVIDEDelegate %androidx.compose.runtime.MutableState  getProvideDelegate %androidx.compose.runtime.MutableState  getSETValue %androidx.compose.runtime.MutableState  getSetValue %androidx.compose.runtime.MutableState  getValue %androidx.compose.runtime.MutableState  provideDelegate %androidx.compose.runtime.MutableState  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  provides 3androidx.compose.runtime.ProvidableCompositionLocal  getGETValue androidx.compose.runtime.State  getGetValue androidx.compose.runtime.State  getPROVIDEDelegate androidx.compose.runtime.State  getProvideDelegate androidx.compose.runtime.State  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	BottomEnd androidx.compose.ui.Alignment  BottomStart androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  TopStart androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  	BottomEnd 'androidx.compose.ui.Alignment.Companion  BottomStart 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  TopStart 'androidx.compose.ui.Alignment.Companion  ChessExpressiveAnimations androidx.compose.ui.Modifier  	Companion androidx.compose.ui.Modifier  
EaseInOutSine androidx.compose.ui.Modifier  LinearEasing androidx.compose.ui.Modifier  Modifier androidx.compose.ui.Modifier  
RepeatMode androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  animateFloat androidx.compose.ui.Modifier  animateFloatAsState androidx.compose.ui.Modifier  aspectRatio androidx.compose.ui.Modifier  
background androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  
fillMaxHeight androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  getALIGN androidx.compose.ui.Modifier  getANIMATEFloat androidx.compose.ui.Modifier  getANIMATEFloatAsState androidx.compose.ui.Modifier  getASPECTRatio androidx.compose.ui.Modifier  getAlign androidx.compose.ui.Modifier  getAnimateFloat androidx.compose.ui.Modifier  getAnimateFloatAsState androidx.compose.ui.Modifier  getAspectRatio androidx.compose.ui.Modifier  
getBACKGROUND androidx.compose.ui.Modifier  
getBackground androidx.compose.ui.Modifier  getCLICKABLE androidx.compose.ui.Modifier  getClickable androidx.compose.ui.Modifier  getFILLMaxHeight androidx.compose.ui.Modifier  getFILLMaxSize androidx.compose.ui.Modifier  getFILLMaxWidth androidx.compose.ui.Modifier  getFillMaxHeight androidx.compose.ui.Modifier  getFillMaxSize androidx.compose.ui.Modifier  getFillMaxWidth androidx.compose.ui.Modifier  getGETValue androidx.compose.ui.Modifier  getGRAPHICSLayer androidx.compose.ui.Modifier  getGetValue androidx.compose.ui.Modifier  getGraphicsLayer androidx.compose.ui.Modifier  	getHEIGHT androidx.compose.ui.Modifier  	getHeight androidx.compose.ui.Modifier  getINFINITERepeatable androidx.compose.ui.Modifier  getInfiniteRepeatable androidx.compose.ui.Modifier  getLET androidx.compose.ui.Modifier  getLet androidx.compose.ui.Modifier  getMUTABLEStateOf androidx.compose.ui.Modifier  getMutableStateOf androidx.compose.ui.Modifier  getNAVIGATIONBarsPadding androidx.compose.ui.Modifier  getNavigationBarsPadding androidx.compose.ui.Modifier  	getOFFSET androidx.compose.ui.Modifier  getONGloballyPositioned androidx.compose.ui.Modifier  	getOffset androidx.compose.ui.Modifier  getOnGloballyPositioned androidx.compose.ui.Modifier  
getPADDING androidx.compose.ui.Modifier  getPOINTERInput androidx.compose.ui.Modifier  getPROVIDEDelegate androidx.compose.ui.Modifier  
getPadding androidx.compose.ui.Modifier  getPointerInput androidx.compose.ui.Modifier  getProvideDelegate androidx.compose.ui.Modifier  getREMEMBER androidx.compose.ui.Modifier  getREMEMBERInfiniteTransition androidx.compose.ui.Modifier  getRemember androidx.compose.ui.Modifier  getRememberInfiniteTransition androidx.compose.ui.Modifier  getSETValue androidx.compose.ui.Modifier  getSIZE androidx.compose.ui.Modifier  getSTATUSBarsPadding androidx.compose.ui.Modifier  getSetValue androidx.compose.ui.Modifier  getSize androidx.compose.ui.Modifier  getStatusBarsPadding androidx.compose.ui.Modifier  getTWEEN androidx.compose.ui.Modifier  getTween androidx.compose.ui.Modifier  getValue androidx.compose.ui.Modifier  	getWEIGHT androidx.compose.ui.Modifier  	getWeight androidx.compose.ui.Modifier  
graphicsLayer androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  infiniteRepeatable androidx.compose.ui.Modifier  let androidx.compose.ui.Modifier  mutableStateOf androidx.compose.ui.Modifier  navigationBarsPadding androidx.compose.ui.Modifier  offset androidx.compose.ui.Modifier  onGloballyPositioned androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  pointerInput androidx.compose.ui.Modifier  provideDelegate androidx.compose.ui.Modifier  remember androidx.compose.ui.Modifier  rememberInfiniteTransition androidx.compose.ui.Modifier  setValue androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  statusBarsPadding androidx.compose.ui.Modifier  then androidx.compose.ui.Modifier  tween androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  
background &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  getALIGN &androidx.compose.ui.Modifier.Companion  getAlign &androidx.compose.ui.Modifier.Companion  
getBACKGROUND &androidx.compose.ui.Modifier.Companion  
getBackground &androidx.compose.ui.Modifier.Companion  getCLICKABLE &androidx.compose.ui.Modifier.Companion  getClickable &androidx.compose.ui.Modifier.Companion  getFILLMaxSize &androidx.compose.ui.Modifier.Companion  getFILLMaxWidth &androidx.compose.ui.Modifier.Companion  getFillMaxSize &androidx.compose.ui.Modifier.Companion  getFillMaxWidth &androidx.compose.ui.Modifier.Companion  getGRAPHICSLayer &androidx.compose.ui.Modifier.Companion  getGraphicsLayer &androidx.compose.ui.Modifier.Companion  	getHEIGHT &androidx.compose.ui.Modifier.Companion  	getHeight &androidx.compose.ui.Modifier.Companion  
getPADDING &androidx.compose.ui.Modifier.Companion  
getPadding &androidx.compose.ui.Modifier.Companion  getSIZE &androidx.compose.ui.Modifier.Companion  getSize &androidx.compose.ui.Modifier.Companion  	getWEIGHT &androidx.compose.ui.Modifier.Companion  getWIDTH &androidx.compose.ui.Modifier.Companion  	getWeight &androidx.compose.ui.Modifier.Companion  getWidth &androidx.compose.ui.Modifier.Companion  
graphicsLayer &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  shadow androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Rect androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  Zero #androidx.compose.ui.geometry.Offset  plus #androidx.compose.ui.geometry.Offset  x #androidx.compose.ui.geometry.Offset  y #androidx.compose.ui.geometry.Offset  Zero -androidx.compose.ui.geometry.Offset.Companion  invoke -androidx.compose.ui.geometry.Offset.Companion  Zero !androidx.compose.ui.geometry.Rect  contains !androidx.compose.ui.geometry.Rect  left !androidx.compose.ui.geometry.Rect  top !androidx.compose.ui.geometry.Rect  width !androidx.compose.ui.geometry.Rect  Zero +androidx.compose.ui.geometry.Rect.Companion  invoke +androidx.compose.ui.geometry.Rect.Companion  width !androidx.compose.ui.geometry.Size  invoke +androidx.compose.ui.geometry.Size.Companion  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  GraphicsLayerScope androidx.compose.ui.graphics  Shadow androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  horizontalGradient "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  horizontalGradient ,androidx.compose.ui.graphics.Brush.Companion  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Black "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  	getTOArgb "androidx.compose.ui.graphics.Color  	getToArgb "androidx.compose.ui.graphics.Color  toArgb "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  alpha /androidx.compose.ui.graphics.GraphicsLayerScope  scaleX /androidx.compose.ui.graphics.GraphicsLayerScope  scaleY /androidx.compose.ui.graphics.GraphicsLayerScope  size /androidx.compose.ui.graphics.GraphicsLayerScope  translationX /androidx.compose.ui.graphics.GraphicsLayerScope  translationY /androidx.compose.ui.graphics.GraphicsLayerScope  invoke -androidx.compose.ui.graphics.Shadow.Companion  Painter $androidx.compose.ui.graphics.painter  ImageVector #androidx.compose.ui.graphics.vector  PointerInputChange !androidx.compose.ui.input.pointer  PointerInputScope !androidx.compose.ui.input.pointer  pointerInput !androidx.compose.ui.input.pointer  Offset 3androidx.compose.ui.input.pointer.PointerInputScope  detectDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  getDETECTDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  getDetectDragGestures 3androidx.compose.ui.input.pointer.PointerInputScope  getLET 3androidx.compose.ui.input.pointer.PointerInputScope  getLet 3androidx.compose.ui.input.pointer.PointerInputScope  invoke 3androidx.compose.ui.input.pointer.PointerInputScope  let 3androidx.compose.ui.input.pointer.PointerInputScope  size 3androidx.compose.ui.input.pointer.PointerInputScope  ContentScale androidx.compose.ui.layout  LayoutCoordinates androidx.compose.ui.layout  onGloballyPositioned androidx.compose.ui.layout  positionInRoot androidx.compose.ui.layout  
FillBounds 'androidx.compose.ui.layout.ContentScale  
FillBounds 1androidx.compose.ui.layout.ContentScale.Companion  getPOSITIONInRoot ,androidx.compose.ui.layout.LayoutCoordinates  getPositionInRoot ,androidx.compose.ui.layout.LayoutCoordinates  positionInRoot ,androidx.compose.ui.layout.LayoutCoordinates  size ,androidx.compose.ui.layout.LayoutCoordinates  LocalConfiguration androidx.compose.ui.platform  LocalContext androidx.compose.ui.platform  LocalDensity androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  painterResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  copy "androidx.compose.ui.text.TextStyle  invoke ,androidx.compose.ui.text.TextStyle.Companion  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  GenericFontFamily androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  Default (androidx.compose.ui.text.font.FontFamily  	Monospace (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Monospace 2androidx.compose.ui.text.font.FontFamily.Companion  Black (androidx.compose.ui.text.font.FontWeight  Bold (androidx.compose.ui.text.font.FontWeight  	ExtraBold (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Black 2androidx.compose.ui.text.font.FontWeight.Companion  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	ExtraBold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Preview #androidx.compose.ui.tooling.preview  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  	IntOffset androidx.compose.ui.unit  IntSize androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  	IntOffset  androidx.compose.ui.unit.Density  invoke  androidx.compose.ui.unit.Density  div androidx.compose.ui.unit.Dp  times androidx.compose.ui.unit.Dp  invoke ,androidx.compose.ui.unit.IntOffset.Companion  height  androidx.compose.ui.unit.IntSize  width  androidx.compose.ui.unit.IntSize  AndroidView androidx.compose.ui.viewinterop  ActivityCompat androidx.core.app  Bundle #androidx.core.app.ComponentActivity  ChessAI #androidx.core.app.ComponentActivity  ChessVisionAppTheme #androidx.core.app.ComponentActivity  ComponentActivity #androidx.core.app.ComponentActivity  DisposableEffect #androidx.core.app.ComponentActivity  	LocalView #androidx.core.app.ComponentActivity  
MainScreen #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  Unit #androidx.core.app.ComponentActivity  WindowCompat #androidx.core.app.ComponentActivity  WindowInsetsCompat #androidx.core.app.ComponentActivity  WindowInsetsControllerCompat #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  invoke #androidx.core.app.ComponentActivity  onCreate #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  getMainExecutor #androidx.core.content.ContextCompat  WindowCompat androidx.core.view  WindowInsetsCompat androidx.core.view  WindowInsetsControllerCompat androidx.core.view  getInsetsController androidx.core.view.WindowCompat  setDecorFitsSystemWindows androidx.core.view.WindowCompat  Type %androidx.core.view.WindowInsetsCompat  navigationBars *androidx.core.view.WindowInsetsCompat.Type  %BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE /androidx.core.view.WindowInsetsControllerCompat  "getISAppearanceLightNavigationBars /androidx.core.view.WindowInsetsControllerCompat  getISAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  "getIsAppearanceLightNavigationBars /androidx.core.view.WindowInsetsControllerCompat  getIsAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  getSYSTEMBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  getSystemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  hide /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightNavigationBars /androidx.core.view.WindowInsetsControllerCompat  isAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat   setAppearanceLightNavigationBars /androidx.core.view.WindowInsetsControllerCompat  setAppearanceLightStatusBars /androidx.core.view.WindowInsetsControllerCompat  setSystemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  show /androidx.core.view.WindowInsetsControllerCompat  systemBarsBehavior /androidx.core.view.WindowInsetsControllerCompat  LifecycleOwner androidx.lifecycle  ActivityResultContracts com.chessvision.app  AlertDialog com.chessvision.app  	Alignment com.chessvision.app  AndroidView com.chessvision.app  AnimatedVisibility com.chessvision.app  Any com.chessvision.app  Arrangement com.chessvision.app  Array com.chessvision.app  
AtomicBoolean com.chessvision.app  AtomicReference com.chessvision.app  
AutoCloseable com.chessvision.app  Boolean com.chessvision.app  Box com.chessvision.app  Brush com.chessvision.app  Button com.chessvision.app  ButtonDefaults com.chessvision.app  	CameraAlt com.chessvision.app  CameraScreen com.chessvision.app  CameraSelector com.chessvision.app  CameraStateManager com.chessvision.app  CameraViewScreen com.chessvision.app  CapturedImagePreview com.chessvision.app  Card com.chessvision.app  CardDefaults com.chessvision.app  CenterFocusStrong com.chessvision.app  Char com.chessvision.app  Check com.chessvision.app  ChessAI com.chessvision.app  ChessActionCard com.chessvision.app  ChessAnalysisResult com.chessvision.app  ChessAppHeader com.chessvision.app  
ChessBoard com.chessvision.app  ChessBoardControls com.chessvision.app  ChessBoardGrid com.chessvision.app  ChessBoardGuide com.chessvision.app  ChessBoardScreen com.chessvision.app  ChessBoardState com.chessvision.app  
ChessPiece com.chessvision.app  
ChessPosition com.chessvision.app  ChessSquare com.chessvision.app  ChessVisionAppTheme com.chessvision.app  ChessVisionApplication com.chessvision.app  CircleShape com.chessvision.app  CircularProgressIndicator com.chessvision.app  Clear com.chessvision.app  Color com.chessvision.app  Column com.chessvision.app  
Composable com.chessvision.app  
ContextCompat com.chessvision.app  
ControlButton com.chessvision.app  CornerGuide com.chessvision.app  
DICE_SCORE com.chessvision.app  Dispatchers com.chessvision.app  DisposableEffect com.chessvision.app  Double com.chessvision.app  EaseOutCubic com.chessvision.app  Edit com.chessvision.app  Error com.chessvision.app  	Exception com.chessvision.app  	Executors com.chessvision.app  ExperimentalMaterial3Api com.chessvision.app  
FENDisplay com.chessvision.app  FeatureItem com.chessvision.app  FeaturesSection com.chessvision.app  	FlashAuto com.chessvision.app  FlashOff com.chessvision.app  FlashOn com.chessvision.app  
FlipToBack com.chessvision.app  Float com.chessvision.app  
FontWeight com.chessvision.app  GridOn com.chessvision.app  INFERENCE_TIME_MS com.chessvision.app  Icon com.chessvision.app  
IconButton com.chessvision.app  Icons com.chessvision.app  Image com.chessvision.app  ImageCapture com.chessvision.app  ImageCaptureException com.chessvision.app  Info com.chessvision.app  Int com.chessvision.app  	IntOffset com.chessvision.app  InteractiveChessBoard com.chessvision.app  LaunchedEffect com.chessvision.app  
LazyColumn com.chessvision.app  LazyRow com.chessvision.app  List com.chessvision.app  	LocalView com.chessvision.app  Locale com.chessvision.app  Log com.chessvision.app  Long com.chessvision.app  MAP50_ACCURACY com.chessvision.app  MainActivity com.chessvision.app  MainHomeScreen com.chessvision.app  
MainScreen com.chessvision.app  MainScreenPreview com.chessvision.app  Manifest com.chessvision.app  Map com.chessvision.app  
MaterialTheme com.chessvision.app  	ModelInfo com.chessvision.app  ModernControlButton com.chessvision.app  Modifier com.chessvision.app  Mutex com.chessvision.app  Offset com.chessvision.app  OptIn com.chessvision.app  OutlinedTextField com.chessvision.app  OutlinedTextFieldDefaults com.chessvision.app  PackageManager com.chessvision.app  
PaddingValues com.chessvision.app  Pair com.chessvision.app  PhotoLibrary com.chessvision.app  PhotoPreviewScreen com.chessvision.app  
PieceColor com.chessvision.app  	PieceTray com.chessvision.app  
PieceTrayItem com.chessvision.app  	PieceType com.chessvision.app  	PlayArrow com.chessvision.app  Preview com.chessvision.app  PreviewView com.chessvision.app  ProcessCameraProvider com.chessvision.app  
Psychology com.chessvision.app  QuickActionsSection com.chessvision.app  R com.chessvision.app  Rect com.chessvision.app  Refresh com.chessvision.app  
RestartAlt com.chessvision.app  RoundedCornerShape com.chessvision.app  Row com.chessvision.app  Size com.chessvision.app  Spacer com.chessvision.app  Spring com.chessvision.app  Stable com.chessvision.app  String com.chessvision.app  
StringBuilder com.chessvision.app  Suppress com.chessvision.app  Surface com.chessvision.app  System com.chessvision.app  TAG com.chessvision.app  Text com.chessvision.app  	TextAlign com.chessvision.app  Toast com.chessvision.app  Unit com.chessvision.app  Uri com.chessvision.app  WindowCompat com.chessvision.app  WindowInsetsCompat com.chessvision.app  WindowInsetsControllerCompat com.chessvision.app  addListener com.chessvision.app  also com.chessvision.app  android com.chessvision.app  androidx com.chessvision.app  animateColorAsState com.chessvision.app  animateFloatAsState com.chessvision.app  apply com.chessvision.app  aspectRatio com.chessvision.app  
background com.chessvision.app  captureImageSafe com.chessvision.app  cleanup com.chessvision.app  	clickable com.chessvision.app  coerceIn com.chessvision.app  com com.chessvision.app  delay com.chessvision.app  detectDragGestures com.chessvision.app  
digitToInt com.chessvision.app  downTo com.chessvision.app  	emptyList com.chessvision.app  emptyMap com.chessvision.app  enableEdgeToEdge com.chessvision.app  fadeIn com.chessvision.app  fadeOut com.chessvision.app  
fillMaxHeight com.chessvision.app  fillMaxSize com.chessvision.app  fillMaxWidth com.chessvision.app  format com.chessvision.app  getValue com.chessvision.app  
graphicsLayer com.chessvision.app  height com.chessvision.app  indices com.chessvision.app  invoke com.chessvision.app  isDigit com.chessvision.app  
isInitialized com.chessvision.app  isUpperCase com.chessvision.app  items com.chessvision.app  iterator com.chessvision.app  kotlinx com.chessvision.app  launch com.chessvision.app  let com.chessvision.app  listOf com.chessvision.app  
lowercaseChar com.chessvision.app  
mutableListOf com.chessvision.app  mutableMapOf com.chessvision.app  mutableStateOf com.chessvision.app  navigationBarsPadding com.chessvision.app  offset com.chessvision.app  onGloballyPositioned com.chessvision.app  onnxAI com.chessvision.app  padding com.chessvision.app  painterResource com.chessvision.app  performanceMetrics com.chessvision.app  
plusAssign com.chessvision.app  positionInRoot com.chessvision.app  provideDelegate com.chessvision.app  remember com.chessvision.app  rememberCameraStateManager com.chessvision.app  rememberChessBoardState com.chessvision.app  rememberCoroutineScope com.chessvision.app  run com.chessvision.app  scaleIn com.chessvision.app  scaleOut com.chessvision.app  set com.chessvision.app  
setContent com.chessvision.app  setValue com.chessvision.app  size com.chessvision.app  slideInHorizontally com.chessvision.app  slideInVertically com.chessvision.app  slideOutHorizontally com.chessvision.app  slideOutVertically com.chessvision.app  split com.chessvision.app  spring com.chessvision.app  statusBarsPadding com.chessvision.app  to com.chessvision.app  toMap com.chessvision.app  totalOperations com.chessvision.app  totalProcessingTime com.chessvision.app  tween com.chessvision.app  
uppercaseChar com.chessvision.app  width com.chessvision.app  withContext com.chessvision.app  withLock com.chessvision.app  
AtomicBoolean &com.chessvision.app.CameraStateManager  AtomicReference &com.chessvision.app.CameraStateManager  Boolean &com.chessvision.app.CameraStateManager  CameraSelector &com.chessvision.app.CameraStateManager  Context &com.chessvision.app.CameraStateManager  
ContextCompat &com.chessvision.app.CameraStateManager  	Exception &com.chessvision.app.CameraStateManager  ExecutorService &com.chessvision.app.CameraStateManager  	Executors &com.chessvision.app.CameraStateManager  ImageCapture &com.chessvision.app.CameraStateManager  Int &com.chessvision.app.CameraStateManager  LifecycleOwner &com.chessvision.app.CameraStateManager  Log &com.chessvision.app.CameraStateManager  Preview &com.chessvision.app.CameraStateManager  PreviewView &com.chessvision.app.CameraStateManager  ProcessCameraProvider &com.chessvision.app.CameraStateManager  Suppress &com.chessvision.app.CameraStateManager  TAG &com.chessvision.app.CameraStateManager  addListener &com.chessvision.app.CameraStateManager  also &com.chessvision.app.CameraStateManager  
bindCamera &com.chessvision.app.CameraStateManager  cameraExecutor &com.chessvision.app.CameraStateManager  cameraProvider &com.chessvision.app.CameraStateManager  cleanup &com.chessvision.app.CameraStateManager  dispose &com.chessvision.app.CameraStateManager  getADDListener &com.chessvision.app.CameraStateManager  getALSO &com.chessvision.app.CameraStateManager  getAddListener &com.chessvision.app.CameraStateManager  getAlso &com.chessvision.app.CameraStateManager  getImageCapture &com.chessvision.app.CameraStateManager  imageCapture &com.chessvision.app.CameraStateManager  
initialize &com.chessvision.app.CameraStateManager  
isDisposed &com.chessvision.app.CameraStateManager  
isInitialized &com.chessvision.app.CameraStateManager  previewView &com.chessvision.app.CameraStateManager  updateFlashMode &com.chessvision.app.CameraStateManager  
AtomicBoolean 0com.chessvision.app.CameraStateManager.Companion  AtomicReference 0com.chessvision.app.CameraStateManager.Companion  Boolean 0com.chessvision.app.CameraStateManager.Companion  CameraSelector 0com.chessvision.app.CameraStateManager.Companion  Context 0com.chessvision.app.CameraStateManager.Companion  
ContextCompat 0com.chessvision.app.CameraStateManager.Companion  	Exception 0com.chessvision.app.CameraStateManager.Companion  ExecutorService 0com.chessvision.app.CameraStateManager.Companion  	Executors 0com.chessvision.app.CameraStateManager.Companion  ImageCapture 0com.chessvision.app.CameraStateManager.Companion  Int 0com.chessvision.app.CameraStateManager.Companion  LifecycleOwner 0com.chessvision.app.CameraStateManager.Companion  Log 0com.chessvision.app.CameraStateManager.Companion  Preview 0com.chessvision.app.CameraStateManager.Companion  PreviewView 0com.chessvision.app.CameraStateManager.Companion  ProcessCameraProvider 0com.chessvision.app.CameraStateManager.Companion  Suppress 0com.chessvision.app.CameraStateManager.Companion  TAG 0com.chessvision.app.CameraStateManager.Companion  addListener 0com.chessvision.app.CameraStateManager.Companion  also 0com.chessvision.app.CameraStateManager.Companion  getADDListener 0com.chessvision.app.CameraStateManager.Companion  getALSO 0com.chessvision.app.CameraStateManager.Companion  getAddListener 0com.chessvision.app.CameraStateManager.Companion  getAlso 0com.chessvision.app.CameraStateManager.Companion  invoke 0com.chessvision.app.CameraStateManager.Companion  Any com.chessvision.app.ChessAI  
AtomicBoolean com.chessvision.app.ChessAI  AtomicReference com.chessvision.app.ChessAI  Boolean com.chessvision.app.ChessAI  ChessAnalysisResult com.chessvision.app.ChessAI  Context com.chessvision.app.ChessAI  
DICE_SCORE com.chessvision.app.ChessAI  Dispatchers com.chessvision.app.ChessAI  	Exception com.chessvision.app.ChessAI  INFERENCE_TIME_MS com.chessvision.app.ChessAI  Log com.chessvision.app.ChessAI  MAP50_ACCURACY com.chessvision.app.ChessAI  Map com.chessvision.app.ChessAI  	ModelInfo com.chessvision.app.ChessAI  Mutex com.chessvision.app.ChessAI  String com.chessvision.app.ChessAI  System com.chessvision.app.ChessAI  TAG com.chessvision.app.ChessAI  Uri com.chessvision.app.ChessAI  cleanup com.chessvision.app.ChessAI  com com.chessvision.app.ChessAI  context com.chessvision.app.ChessAI  generateFEN com.chessvision.app.ChessAI  getCOM com.chessvision.app.ChessAI  getCom com.chessvision.app.ChessAI  getMUTABLEMapOf com.chessvision.app.ChessAI  getModelInfo com.chessvision.app.ChessAI  getMutableMapOf com.chessvision.app.ChessAI  
getPLUSAssign com.chessvision.app.ChessAI  
getPlusAssign com.chessvision.app.ChessAI  getSET com.chessvision.app.ChessAI  getSet com.chessvision.app.ChessAI  getTOMap com.chessvision.app.ChessAI  getToMap com.chessvision.app.ChessAI  getWITHContext com.chessvision.app.ChessAI  getWITHLock com.chessvision.app.ChessAI  getWithContext com.chessvision.app.ChessAI  getWithLock com.chessvision.app.ChessAI  initializationMutex com.chessvision.app.ChessAI  initializeModels com.chessvision.app.ChessAI  
isDisposed com.chessvision.app.ChessAI  
isInitialized com.chessvision.app.ChessAI  mutableMapOf com.chessvision.app.ChessAI  onnxAI com.chessvision.app.ChessAI  performanceMetrics com.chessvision.app.ChessAI  
plusAssign com.chessvision.app.ChessAI  processingMutex com.chessvision.app.ChessAI  set com.chessvision.app.ChessAI  toMap com.chessvision.app.ChessAI  totalOperations com.chessvision.app.ChessAI  totalProcessingTime com.chessvision.app.ChessAI  withContext com.chessvision.app.ChessAI  withLock com.chessvision.app.ChessAI  Any %com.chessvision.app.ChessAI.Companion  
AtomicBoolean %com.chessvision.app.ChessAI.Companion  AtomicReference %com.chessvision.app.ChessAI.Companion  Boolean %com.chessvision.app.ChessAI.Companion  ChessAnalysisResult %com.chessvision.app.ChessAI.Companion  Context %com.chessvision.app.ChessAI.Companion  
DICE_SCORE %com.chessvision.app.ChessAI.Companion  Dispatchers %com.chessvision.app.ChessAI.Companion  	Exception %com.chessvision.app.ChessAI.Companion  INFERENCE_TIME_MS %com.chessvision.app.ChessAI.Companion  Log %com.chessvision.app.ChessAI.Companion  MAP50_ACCURACY %com.chessvision.app.ChessAI.Companion  Map %com.chessvision.app.ChessAI.Companion  	ModelInfo %com.chessvision.app.ChessAI.Companion  Mutex %com.chessvision.app.ChessAI.Companion  String %com.chessvision.app.ChessAI.Companion  System %com.chessvision.app.ChessAI.Companion  TAG %com.chessvision.app.ChessAI.Companion  Uri %com.chessvision.app.ChessAI.Companion  cleanup %com.chessvision.app.ChessAI.Companion  com %com.chessvision.app.ChessAI.Companion  getCOM %com.chessvision.app.ChessAI.Companion  getCom %com.chessvision.app.ChessAI.Companion  getMUTABLEMapOf %com.chessvision.app.ChessAI.Companion  getMutableMapOf %com.chessvision.app.ChessAI.Companion  
getPLUSAssign %com.chessvision.app.ChessAI.Companion  
getPlusAssign %com.chessvision.app.ChessAI.Companion  getSET %com.chessvision.app.ChessAI.Companion  getSet %com.chessvision.app.ChessAI.Companion  getTOMap %com.chessvision.app.ChessAI.Companion  getToMap %com.chessvision.app.ChessAI.Companion  getWITHContext %com.chessvision.app.ChessAI.Companion  getWITHLock %com.chessvision.app.ChessAI.Companion  getWithContext %com.chessvision.app.ChessAI.Companion  getWithLock %com.chessvision.app.ChessAI.Companion  invoke %com.chessvision.app.ChessAI.Companion  
isInitialized %com.chessvision.app.ChessAI.Companion  mutableMapOf %com.chessvision.app.ChessAI.Companion  onnxAI %com.chessvision.app.ChessAI.Companion  performanceMetrics %com.chessvision.app.ChessAI.Companion  
plusAssign %com.chessvision.app.ChessAI.Companion  set %com.chessvision.app.ChessAI.Companion  toMap %com.chessvision.app.ChessAI.Companion  totalOperations %com.chessvision.app.ChessAI.Companion  totalProcessingTime %com.chessvision.app.ChessAI.Companion  withContext %com.chessvision.app.ChessAI.Companion  withLock %com.chessvision.app.ChessAI.Companion  ChessAnalysisResult 'com.chessvision.app.ChessAnalysisResult  Error 'com.chessvision.app.ChessAnalysisResult  Float 'com.chessvision.app.ChessAnalysisResult  Int 'com.chessvision.app.ChessAnalysisResult  Long 'com.chessvision.app.ChessAnalysisResult  String 'com.chessvision.app.ChessAnalysisResult  Success 'com.chessvision.app.ChessAnalysisResult  
confidence 'com.chessvision.app.ChessAnalysisResult  fen 'com.chessvision.app.ChessAnalysisResult  message 'com.chessvision.app.ChessAnalysisResult  processingTimeMs 'com.chessvision.app.ChessAnalysisResult  String -com.chessvision.app.ChessAnalysisResult.Error  message -com.chessvision.app.ChessAnalysisResult.Error  Float /com.chessvision.app.ChessAnalysisResult.Success  Int /com.chessvision.app.ChessAnalysisResult.Success  Long /com.chessvision.app.ChessAnalysisResult.Success  String /com.chessvision.app.ChessAnalysisResult.Success  
confidence /com.chessvision.app.ChessAnalysisResult.Success  fen /com.chessvision.app.ChessAnalysisResult.Success  processingTimeMs /com.chessvision.app.ChessAnalysisResult.Success  Array #com.chessvision.app.ChessBoardState  Boolean #com.chessvision.app.ChessBoardState  Char #com.chessvision.app.ChessBoardState  
ChessPiece #com.chessvision.app.ChessBoardState  
ChessPosition #com.chessvision.app.ChessBoardState  Int #com.chessvision.app.ChessBoardState  List #com.chessvision.app.ChessBoardState  Offset #com.chessvision.app.ChessBoardState  Pair #com.chessvision.app.ChessBoardState  
PieceColor #com.chessvision.app.ChessBoardState  	PieceType #com.chessvision.app.ChessBoardState  R #com.chessvision.app.ChessBoardState  Rect #com.chessvision.app.ChessBoardState  String #com.chessvision.app.ChessBoardState  
StringBuilder #com.chessvision.app.ChessBoardState  Unit #com.chessvision.app.ChessBoardState  android #com.chessvision.app.ChessBoardState  animationTrigger #com.chessvision.app.ChessBoardState  board #com.chessvision.app.ChessBoardState  boardBounds #com.chessvision.app.ChessBoardState  canSelectPiece #com.chessvision.app.ChessBoardState  charToPiece #com.chessvision.app.ChessBoardState  coerceIn #com.chessvision.app.ChessBoardState  currentFENString #com.chessvision.app.ChessBoardState  
digitToInt #com.chessvision.app.ChessBoardState  downTo #com.chessvision.app.ChessBoardState  
dragOffset #com.chessvision.app.ChessBoardState  dragStartOffset #com.chessvision.app.ChessBoardState  dragStartPosition #com.chessvision.app.ChessBoardState  draggedFromTrayPiece #com.chessvision.app.ChessBoardState  draggedPiece #com.chessvision.app.ChessBoardState  	emptyList #com.chessvision.app.ChessBoardState  endDrag #com.chessvision.app.ChessBoardState  	flipBoard #com.chessvision.app.ChessBoardState  
getANDROID #com.chessvision.app.ChessBoardState  
getAndroid #com.chessvision.app.ChessBoardState  getCOERCEIn #com.chessvision.app.ChessBoardState  getCoerceIn #com.chessvision.app.ChessBoardState  
getCurrentFEN #com.chessvision.app.ChessBoardState  
getDIGITToInt #com.chessvision.app.ChessBoardState  	getDOWNTo #com.chessvision.app.ChessBoardState  
getDigitToInt #com.chessvision.app.ChessBoardState  	getDownTo #com.chessvision.app.ChessBoardState  getEMPTYList #com.chessvision.app.ChessBoardState  getEmptyList #com.chessvision.app.ChessBoardState  getGETValue #com.chessvision.app.ChessBoardState  getGetValue #com.chessvision.app.ChessBoardState  
getISDigit #com.chessvision.app.ChessBoardState  getISUpperCase #com.chessvision.app.ChessBoardState  getITERATOR #com.chessvision.app.ChessBoardState  
getIsDigit #com.chessvision.app.ChessBoardState  getIsUpperCase #com.chessvision.app.ChessBoardState  getIterator #com.chessvision.app.ChessBoardState  getLET #com.chessvision.app.ChessBoardState  	getLISTOf #com.chessvision.app.ChessBoardState  getLOWERCASEChar #com.chessvision.app.ChessBoardState  
getLegalMoves #com.chessvision.app.ChessBoardState  getLet #com.chessvision.app.ChessBoardState  	getListOf #com.chessvision.app.ChessBoardState  getLowercaseChar #com.chessvision.app.ChessBoardState  getMUTABLEListOf #com.chessvision.app.ChessBoardState  getMUTABLEStateOf #com.chessvision.app.ChessBoardState  getMutableListOf #com.chessvision.app.ChessBoardState  getMutableStateOf #com.chessvision.app.ChessBoardState  
getPLUSAssign #com.chessvision.app.ChessBoardState  getPROVIDEDelegate #com.chessvision.app.ChessBoardState  getPieceDrawable #com.chessvision.app.ChessBoardState  
getPlusAssign #com.chessvision.app.ChessBoardState  getProvideDelegate #com.chessvision.app.ChessBoardState  getSETValue #com.chessvision.app.ChessBoardState  getSPLIT #com.chessvision.app.ChessBoardState  getSetValue #com.chessvision.app.ChessBoardState  getSplit #com.chessvision.app.ChessBoardState  getSquareFromOffset #com.chessvision.app.ChessBoardState  getTO #com.chessvision.app.ChessBoardState  getTo #com.chessvision.app.ChessBoardState  getUPPERCASEChar #com.chessvision.app.ChessBoardState  getUppercaseChar #com.chessvision.app.ChessBoardState  getValue #com.chessvision.app.ChessBoardState  
handleMove #com.chessvision.app.ChessBoardState  indices #com.chessvision.app.ChessBoardState  isAnimatingPiece #com.chessvision.app.ChessBoardState  isBoardFlipped #com.chessvision.app.ChessBoardState  isDigit #com.chessvision.app.ChessBoardState  
isDragging #com.chessvision.app.ChessBoardState  isDraggingFromTray #com.chessvision.app.ChessBoardState  
isEditMode #com.chessvision.app.ChessBoardState  isPositionOnBoard #com.chessvision.app.ChessBoardState  isUpperCase #com.chessvision.app.ChessBoardState  iterator #com.chessvision.app.ChessBoardState  lastMovedSquare #com.chessvision.app.ChessBoardState  let #com.chessvision.app.ChessBoardState  listOf #com.chessvision.app.ChessBoardState  loadFromFEN #com.chessvision.app.ChessBoardState  
lowercaseChar #com.chessvision.app.ChessBoardState  
mutableListOf #com.chessvision.app.ChessBoardState  mutableStateOf #com.chessvision.app.ChessBoardState  onFENUpdated #com.chessvision.app.ChessBoardState  
onSquareClick #com.chessvision.app.ChessBoardState  pieceToChar #com.chessvision.app.ChessBoardState  
plusAssign #com.chessvision.app.ChessBoardState  provideDelegate #com.chessvision.app.ChessBoardState  selectedPieceForPlacement #com.chessvision.app.ChessBoardState  selectedSquare #com.chessvision.app.ChessBoardState  setFENUpdateCallback #com.chessvision.app.ChessBoardState  setValue #com.chessvision.app.ChessBoardState  setupInitialPosition #com.chessvision.app.ChessBoardState  split #com.chessvision.app.ChessBoardState  	startDrag #com.chessvision.app.ChessBoardState  startDragFromTray #com.chessvision.app.ChessBoardState  to #com.chessvision.app.ChessBoardState  updateBoardBounds #com.chessvision.app.ChessBoardState  updateDragPosition #com.chessvision.app.ChessBoardState  	updateFEN #com.chessvision.app.ChessBoardState  
updateFromFEN #com.chessvision.app.ChessBoardState  
uppercaseChar #com.chessvision.app.ChessBoardState  Int com.chessvision.app.ChessPiece  
PieceColor com.chessvision.app.ChessPiece  	PieceType com.chessvision.app.ChessPiece  color com.chessvision.app.ChessPiece  drawableRes com.chessvision.app.ChessPiece  equals com.chessvision.app.ChessPiece  getLET com.chessvision.app.ChessPiece  getLet com.chessvision.app.ChessPiece  let com.chessvision.app.ChessPiece  type com.chessvision.app.ChessPiece  Boolean !com.chessvision.app.ChessPosition  Int !com.chessvision.app.ChessPosition  equals !com.chessvision.app.ChessPosition  file !com.chessvision.app.ChessPosition  getLET !com.chessvision.app.ChessPosition  getLet !com.chessvision.app.ChessPosition  isValid !com.chessvision.app.ChessPosition  let !com.chessvision.app.ChessPosition  rank !com.chessvision.app.ChessPosition  android *com.chessvision.app.ChessVisionApplication  
getANDROID *com.chessvision.app.ChessVisionApplication  
getAndroid *com.chessvision.app.ChessVisionApplication  Bundle  com.chessvision.app.MainActivity  ChessAI  com.chessvision.app.MainActivity  ChessVisionAppTheme  com.chessvision.app.MainActivity  ComponentActivity  com.chessvision.app.MainActivity  DisposableEffect  com.chessvision.app.MainActivity  	LocalView  com.chessvision.app.MainActivity  
MainScreen  com.chessvision.app.MainActivity  
MaterialTheme  com.chessvision.app.MainActivity  Modifier  com.chessvision.app.MainActivity  Surface  com.chessvision.app.MainActivity  Unit  com.chessvision.app.MainActivity  WindowCompat  com.chessvision.app.MainActivity  WindowInsetsCompat  com.chessvision.app.MainActivity  WindowInsetsControllerCompat  com.chessvision.app.MainActivity  chessAI  com.chessvision.app.MainActivity  enableEdgeToEdge  com.chessvision.app.MainActivity  fillMaxSize  com.chessvision.app.MainActivity  getENABLEEdgeToEdge  com.chessvision.app.MainActivity  getEnableEdgeToEdge  com.chessvision.app.MainActivity  getFILLMaxSize  com.chessvision.app.MainActivity  getFillMaxSize  com.chessvision.app.MainActivity  
getSETContent  com.chessvision.app.MainActivity  
getSetContent  com.chessvision.app.MainActivity  	getWINDOW  com.chessvision.app.MainActivity  	getWindow  com.chessvision.app.MainActivity  invoke  com.chessvision.app.MainActivity  
setContent  com.chessvision.app.MainActivity  	setWindow  com.chessvision.app.MainActivity  window  com.chessvision.app.MainActivity  Any com.chessvision.app.ModelInfo  Boolean com.chessvision.app.ModelInfo  Double com.chessvision.app.ModelInfo  Int com.chessvision.app.ModelInfo  Long com.chessvision.app.ModelInfo  Map com.chessvision.app.ModelInfo  String com.chessvision.app.ModelInfo  	diceScore com.chessvision.app.ModelInfo  emptyMap com.chessvision.app.ModelInfo  expectedInferenceTimeMs com.chessvision.app.ModelInfo  
isInitialized com.chessvision.app.ModelInfo  
map50Accuracy com.chessvision.app.ModelInfo  totalPackageSizeMB com.chessvision.app.ModelInfo  BLACK com.chessvision.app.PieceColor  WHITE com.chessvision.app.PieceColor  equals com.chessvision.app.PieceColor  BISHOP com.chessvision.app.PieceType  KING com.chessvision.app.PieceType  KNIGHT com.chessvision.app.PieceType  PAWN com.chessvision.app.PieceType  QUEEN com.chessvision.app.PieceType  ROOK com.chessvision.app.PieceType  getTO com.chessvision.app.PieceType  getTo com.chessvision.app.PieceType  to com.chessvision.app.PieceType  drawable com.chessvision.app.R  bb com.chessvision.app.R.drawable  bk com.chessvision.app.R.drawable  bn com.chessvision.app.R.drawable  bp com.chessvision.app.R.drawable  bq com.chessvision.app.R.drawable  br com.chessvision.app.R.drawable  chessboard_background com.chessvision.app.R.drawable  wb com.chessvision.app.R.drawable  wk com.chessvision.app.R.drawable  wn com.chessvision.app.R.drawable  wp com.chessvision.app.R.drawable  wq com.chessvision.app.R.drawable  wr com.chessvision.app.R.drawable  Array com.chessvision.app.ai  
AtomicBoolean com.chessvision.app.ai  AtomicReference com.chessvision.app.ai  
AutoCloseable com.chessvision.app.ai  Bitmap com.chessvision.app.ai  BoardSegmentationResult com.chessvision.app.ai  Boolean com.chessvision.app.ai  ChessAnalysisResult com.chessvision.app.ai  ChessSquare com.chessvision.app.ai  
DICE_SCORE com.chessvision.app.ai  Dispatchers com.chessvision.app.ai  	Exception com.chessvision.app.ai  File com.chessvision.app.ai  FileOutputStream com.chessvision.app.ai  Float com.chessvision.app.ai  
FloatArray com.chessvision.app.ai  
ImageUtils com.chessvision.app.ai  Int com.chessvision.app.ai  IntArray com.chessvision.app.ai  List com.chessvision.app.ai  Log com.chessvision.app.ai  Long com.chessvision.app.ai  MAP50_ACCURACY com.chessvision.app.ai  MAX_PROCESSING_TIME_MS com.chessvision.app.ai  MEMORY_CLEANUP_THRESHOLD com.chessvision.app.ai  MutableList com.chessvision.app.ai  Mutex com.chessvision.app.ai  ONNXChessAI com.chessvision.app.ai  PieceDetectionResult com.chessvision.app.ai  String com.chessvision.app.ai  System com.chessvision.app.ai  TAG com.chessvision.app.ai  
V6_MODEL_FILE com.chessvision.app.ai  YOLO_MODEL_FILE com.chessvision.app.ai  ai com.chessvision.app.ai  apply com.chessvision.app.ai  arrayOf com.chessvision.app.ai  cleanupResources com.chessvision.app.ai  contentToString com.chessvision.app.ai  context com.chessvision.app.ai  copyModelSafely com.chessvision.app.ai  copyTo com.chessvision.app.ai  downTo com.chessvision.app.ai  	emptyList com.chessvision.app.ai  forEach com.chessvision.app.ai  generateFENFromDetections com.chessvision.app.ai  indices com.chessvision.app.ai  
isInitialized com.chessvision.app.ai  java com.chessvision.app.ai  joinToString com.chessvision.app.ai  kotlin com.chessvision.app.ai  let com.chessvision.app.ai  loadBitmapFromUri com.chessvision.app.ai  longArrayOf com.chessvision.app.ai  mapOf com.chessvision.app.ai  maxOf com.chessvision.app.ai  minOf com.chessvision.app.ai  
mutableListOf com.chessvision.app.ai  mutableMapOf com.chessvision.app.ai  operationCount com.chessvision.app.ai  ortEnvironment com.chessvision.app.ai  performanceMetrics com.chessvision.app.ai  
plusAssign com.chessvision.app.ai  run com.chessvision.app.ai  runV6SegmentationSafe com.chessvision.app.ai  runYOLODetectionSafe com.chessvision.app.ai  set com.chessvision.app.ai  to com.chessvision.app.ai  until com.chessvision.app.ai  use com.chessvision.app.ai  	v6Session com.chessvision.app.ai  withContext com.chessvision.app.ai  withLock com.chessvision.app.ai  yoloSession com.chessvision.app.ai  Array "com.chessvision.app.ai.ONNXChessAI  
AtomicBoolean "com.chessvision.app.ai.ONNXChessAI  AtomicReference "com.chessvision.app.ai.ONNXChessAI  
AutoCloseable "com.chessvision.app.ai.ONNXChessAI  Bitmap "com.chessvision.app.ai.ONNXChessAI  BoardSegmentationResult "com.chessvision.app.ai.ONNXChessAI  Boolean "com.chessvision.app.ai.ONNXChessAI  ChessAnalysisResult "com.chessvision.app.ai.ONNXChessAI  ChessSquare "com.chessvision.app.ai.ONNXChessAI  Context "com.chessvision.app.ai.ONNXChessAI  
DICE_SCORE "com.chessvision.app.ai.ONNXChessAI  Dispatchers "com.chessvision.app.ai.ONNXChessAI  	Exception "com.chessvision.app.ai.ONNXChessAI  File "com.chessvision.app.ai.ONNXChessAI  FileOutputStream "com.chessvision.app.ai.ONNXChessAI  Float "com.chessvision.app.ai.ONNXChessAI  
FloatArray "com.chessvision.app.ai.ONNXChessAI  
ImageUtils "com.chessvision.app.ai.ONNXChessAI  Int "com.chessvision.app.ai.ONNXChessAI  IntArray "com.chessvision.app.ai.ONNXChessAI  List "com.chessvision.app.ai.ONNXChessAI  Log "com.chessvision.app.ai.ONNXChessAI  Long "com.chessvision.app.ai.ONNXChessAI  MAP50_ACCURACY "com.chessvision.app.ai.ONNXChessAI  MAX_PROCESSING_TIME_MS "com.chessvision.app.ai.ONNXChessAI  MEMORY_CLEANUP_THRESHOLD "com.chessvision.app.ai.ONNXChessAI  MutableList "com.chessvision.app.ai.ONNXChessAI  Mutex "com.chessvision.app.ai.ONNXChessAI  PieceDetectionResult "com.chessvision.app.ai.ONNXChessAI  String "com.chessvision.app.ai.ONNXChessAI  System "com.chessvision.app.ai.ONNXChessAI  TAG "com.chessvision.app.ai.ONNXChessAI  Uri "com.chessvision.app.ai.ONNXChessAI  
V6_MODEL_FILE "com.chessvision.app.ai.ONNXChessAI  YOLO_MODEL_FILE "com.chessvision.app.ai.ONNXChessAI  ai "com.chessvision.app.ai.ONNXChessAI  apply "com.chessvision.app.ai.ONNXChessAI  arrayOf "com.chessvision.app.ai.ONNXChessAI  cleanupResources "com.chessvision.app.ai.ONNXChessAI  close "com.chessvision.app.ai.ONNXChessAI  contentToString "com.chessvision.app.ai.ONNXChessAI  context "com.chessvision.app.ai.ONNXChessAI  copyModelSafely "com.chessvision.app.ai.ONNXChessAI  copyTo "com.chessvision.app.ai.ONNXChessAI  downTo "com.chessvision.app.ai.ONNXChessAI  	emptyList "com.chessvision.app.ai.ONNXChessAI  findBestSquareForPiece "com.chessvision.app.ai.ONNXChessAI  generateChessSquares "com.chessvision.app.ai.ONNXChessAI  generateFEN "com.chessvision.app.ai.ONNXChessAI  generateFENFromDetections "com.chessvision.app.ai.ONNXChessAI  getAI "com.chessvision.app.ai.ONNXChessAI  getAPPLY "com.chessvision.app.ai.ONNXChessAI  
getARRAYOf "com.chessvision.app.ai.ONNXChessAI  getAi "com.chessvision.app.ai.ONNXChessAI  getApply "com.chessvision.app.ai.ONNXChessAI  
getArrayOf "com.chessvision.app.ai.ONNXChessAI  getCONTENTToString "com.chessvision.app.ai.ONNXChessAI  	getCOPYTo "com.chessvision.app.ai.ONNXChessAI  getClassNameFromId "com.chessvision.app.ai.ONNXChessAI  getContentToString "com.chessvision.app.ai.ONNXChessAI  	getCopyTo "com.chessvision.app.ai.ONNXChessAI  	getDOWNTo "com.chessvision.app.ai.ONNXChessAI  	getDownTo "com.chessvision.app.ai.ONNXChessAI  getEMPTYList "com.chessvision.app.ai.ONNXChessAI  getEmptyList "com.chessvision.app.ai.ONNXChessAI  getFENSymbolFromPieceName "com.chessvision.app.ai.ONNXChessAI  getJAVA "com.chessvision.app.ai.ONNXChessAI  getJOINToString "com.chessvision.app.ai.ONNXChessAI  getJava "com.chessvision.app.ai.ONNXChessAI  getJoinToString "com.chessvision.app.ai.ONNXChessAI  	getKOTLIN "com.chessvision.app.ai.ONNXChessAI  	getKotlin "com.chessvision.app.ai.ONNXChessAI  getLET "com.chessvision.app.ai.ONNXChessAI  getLONGArrayOf "com.chessvision.app.ai.ONNXChessAI  getLet "com.chessvision.app.ai.ONNXChessAI  getLongArrayOf "com.chessvision.app.ai.ONNXChessAI  getMAPOf "com.chessvision.app.ai.ONNXChessAI  getMAXOf "com.chessvision.app.ai.ONNXChessAI  getMINOf "com.chessvision.app.ai.ONNXChessAI  getMUTABLEListOf "com.chessvision.app.ai.ONNXChessAI  getMUTABLEMapOf "com.chessvision.app.ai.ONNXChessAI  getMapOf "com.chessvision.app.ai.ONNXChessAI  getMaxOf "com.chessvision.app.ai.ONNXChessAI  getMinOf "com.chessvision.app.ai.ONNXChessAI  getMutableListOf "com.chessvision.app.ai.ONNXChessAI  getMutableMapOf "com.chessvision.app.ai.ONNXChessAI  
getPLUSAssign "com.chessvision.app.ai.ONNXChessAI  
getPlusAssign "com.chessvision.app.ai.ONNXChessAI  getRUN "com.chessvision.app.ai.ONNXChessAI  getRun "com.chessvision.app.ai.ONNXChessAI  getSET "com.chessvision.app.ai.ONNXChessAI  getSet "com.chessvision.app.ai.ONNXChessAI  getTO "com.chessvision.app.ai.ONNXChessAI  getTo "com.chessvision.app.ai.ONNXChessAI  getUNTIL "com.chessvision.app.ai.ONNXChessAI  getUSE "com.chessvision.app.ai.ONNXChessAI  getUntil "com.chessvision.app.ai.ONNXChessAI  getUse "com.chessvision.app.ai.ONNXChessAI  getWITHContext "com.chessvision.app.ai.ONNXChessAI  getWITHLock "com.chessvision.app.ai.ONNXChessAI  getWithContext "com.chessvision.app.ai.ONNXChessAI  getWithLock "com.chessvision.app.ai.ONNXChessAI  indices "com.chessvision.app.ai.ONNXChessAI  initializationMutex "com.chessvision.app.ai.ONNXChessAI  initializeModels "com.chessvision.app.ai.ONNXChessAI  
isDisposed "com.chessvision.app.ai.ONNXChessAI  
isInitialized "com.chessvision.app.ai.ONNXChessAI  java "com.chessvision.app.ai.ONNXChessAI  joinToString "com.chessvision.app.ai.ONNXChessAI  kotlin "com.chessvision.app.ai.ONNXChessAI  let "com.chessvision.app.ai.ONNXChessAI  loadBitmapFromUri "com.chessvision.app.ai.ONNXChessAI  longArrayOf "com.chessvision.app.ai.ONNXChessAI  mapOf "com.chessvision.app.ai.ONNXChessAI  maxOf "com.chessvision.app.ai.ONNXChessAI  minOf "com.chessvision.app.ai.ONNXChessAI  
mutableListOf "com.chessvision.app.ai.ONNXChessAI  mutableMapOf "com.chessvision.app.ai.ONNXChessAI  operationCount "com.chessvision.app.ai.ONNXChessAI  ortEnvironment "com.chessvision.app.ai.ONNXChessAI  performanceMetrics "com.chessvision.app.ai.ONNXChessAI  
plusAssign "com.chessvision.app.ai.ONNXChessAI  postprocessV6Output "com.chessvision.app.ai.ONNXChessAI  postprocessYOLOOutput "com.chessvision.app.ai.ONNXChessAI  preprocessImageForV6 "com.chessvision.app.ai.ONNXChessAI  preprocessImageForYOLO "com.chessvision.app.ai.ONNXChessAI  processingMutex "com.chessvision.app.ai.ONNXChessAI  run "com.chessvision.app.ai.ONNXChessAI  runV6Segmentation "com.chessvision.app.ai.ONNXChessAI  runV6SegmentationSafe "com.chessvision.app.ai.ONNXChessAI  runYOLODetection "com.chessvision.app.ai.ONNXChessAI  runYOLODetectionSafe "com.chessvision.app.ai.ONNXChessAI  set "com.chessvision.app.ai.ONNXChessAI  to "com.chessvision.app.ai.ONNXChessAI  until "com.chessvision.app.ai.ONNXChessAI  use "com.chessvision.app.ai.ONNXChessAI  	v6Session "com.chessvision.app.ai.ONNXChessAI  withContext "com.chessvision.app.ai.ONNXChessAI  withLock "com.chessvision.app.ai.ONNXChessAI  yoloSession "com.chessvision.app.ai.ONNXChessAI  Bitmap :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  ChessSquare :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  Float :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  List :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  correctedBoard :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  equals :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  squares :com.chessvision.app.ai.ONNXChessAI.BoardSegmentationResult  Int .com.chessvision.app.ai.ONNXChessAI.ChessSquare  equals .com.chessvision.app.ai.ONNXChessAI.ChessSquare  file .com.chessvision.app.ai.ONNXChessAI.ChessSquare  height .com.chessvision.app.ai.ONNXChessAI.ChessSquare  rank .com.chessvision.app.ai.ONNXChessAI.ChessSquare  width .com.chessvision.app.ai.ONNXChessAI.ChessSquare  x .com.chessvision.app.ai.ONNXChessAI.ChessSquare  y .com.chessvision.app.ai.ONNXChessAI.ChessSquare  Array ,com.chessvision.app.ai.ONNXChessAI.Companion  
AtomicBoolean ,com.chessvision.app.ai.ONNXChessAI.Companion  AtomicReference ,com.chessvision.app.ai.ONNXChessAI.Companion  
AutoCloseable ,com.chessvision.app.ai.ONNXChessAI.Companion  Bitmap ,com.chessvision.app.ai.ONNXChessAI.Companion  BoardSegmentationResult ,com.chessvision.app.ai.ONNXChessAI.Companion  Boolean ,com.chessvision.app.ai.ONNXChessAI.Companion  ChessAnalysisResult ,com.chessvision.app.ai.ONNXChessAI.Companion  ChessSquare ,com.chessvision.app.ai.ONNXChessAI.Companion  Context ,com.chessvision.app.ai.ONNXChessAI.Companion  
DICE_SCORE ,com.chessvision.app.ai.ONNXChessAI.Companion  Dispatchers ,com.chessvision.app.ai.ONNXChessAI.Companion  	Exception ,com.chessvision.app.ai.ONNXChessAI.Companion  File ,com.chessvision.app.ai.ONNXChessAI.Companion  FileOutputStream ,com.chessvision.app.ai.ONNXChessAI.Companion  Float ,com.chessvision.app.ai.ONNXChessAI.Companion  
FloatArray ,com.chessvision.app.ai.ONNXChessAI.Companion  
ImageUtils ,com.chessvision.app.ai.ONNXChessAI.Companion  Int ,com.chessvision.app.ai.ONNXChessAI.Companion  IntArray ,com.chessvision.app.ai.ONNXChessAI.Companion  List ,com.chessvision.app.ai.ONNXChessAI.Companion  Log ,com.chessvision.app.ai.ONNXChessAI.Companion  Long ,com.chessvision.app.ai.ONNXChessAI.Companion  MAP50_ACCURACY ,com.chessvision.app.ai.ONNXChessAI.Companion  MAX_PROCESSING_TIME_MS ,com.chessvision.app.ai.ONNXChessAI.Companion  MEMORY_CLEANUP_THRESHOLD ,com.chessvision.app.ai.ONNXChessAI.Companion  MutableList ,com.chessvision.app.ai.ONNXChessAI.Companion  Mutex ,com.chessvision.app.ai.ONNXChessAI.Companion  PieceDetectionResult ,com.chessvision.app.ai.ONNXChessAI.Companion  String ,com.chessvision.app.ai.ONNXChessAI.Companion  System ,com.chessvision.app.ai.ONNXChessAI.Companion  TAG ,com.chessvision.app.ai.ONNXChessAI.Companion  Uri ,com.chessvision.app.ai.ONNXChessAI.Companion  
V6_MODEL_FILE ,com.chessvision.app.ai.ONNXChessAI.Companion  YOLO_MODEL_FILE ,com.chessvision.app.ai.ONNXChessAI.Companion  ai ,com.chessvision.app.ai.ONNXChessAI.Companion  apply ,com.chessvision.app.ai.ONNXChessAI.Companion  arrayOf ,com.chessvision.app.ai.ONNXChessAI.Companion  cleanupResources ,com.chessvision.app.ai.ONNXChessAI.Companion  contentToString ,com.chessvision.app.ai.ONNXChessAI.Companion  context ,com.chessvision.app.ai.ONNXChessAI.Companion  copyModelSafely ,com.chessvision.app.ai.ONNXChessAI.Companion  copyTo ,com.chessvision.app.ai.ONNXChessAI.Companion  downTo ,com.chessvision.app.ai.ONNXChessAI.Companion  	emptyList ,com.chessvision.app.ai.ONNXChessAI.Companion  generateFENFromDetections ,com.chessvision.app.ai.ONNXChessAI.Companion  getAI ,com.chessvision.app.ai.ONNXChessAI.Companion  getAPPLY ,com.chessvision.app.ai.ONNXChessAI.Companion  
getARRAYOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getAi ,com.chessvision.app.ai.ONNXChessAI.Companion  getApply ,com.chessvision.app.ai.ONNXChessAI.Companion  
getArrayOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getCONTENTToString ,com.chessvision.app.ai.ONNXChessAI.Companion  	getCOPYTo ,com.chessvision.app.ai.ONNXChessAI.Companion  getContentToString ,com.chessvision.app.ai.ONNXChessAI.Companion  	getCopyTo ,com.chessvision.app.ai.ONNXChessAI.Companion  	getDOWNTo ,com.chessvision.app.ai.ONNXChessAI.Companion  	getDownTo ,com.chessvision.app.ai.ONNXChessAI.Companion  getEMPTYList ,com.chessvision.app.ai.ONNXChessAI.Companion  getEmptyList ,com.chessvision.app.ai.ONNXChessAI.Companion  getJAVA ,com.chessvision.app.ai.ONNXChessAI.Companion  getJOINToString ,com.chessvision.app.ai.ONNXChessAI.Companion  getJava ,com.chessvision.app.ai.ONNXChessAI.Companion  getJoinToString ,com.chessvision.app.ai.ONNXChessAI.Companion  	getKOTLIN ,com.chessvision.app.ai.ONNXChessAI.Companion  	getKotlin ,com.chessvision.app.ai.ONNXChessAI.Companion  getLET ,com.chessvision.app.ai.ONNXChessAI.Companion  getLONGArrayOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getLet ,com.chessvision.app.ai.ONNXChessAI.Companion  getLongArrayOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMAPOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMAXOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMINOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMUTABLEListOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMUTABLEMapOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMapOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMaxOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMinOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMutableListOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMutableMapOf ,com.chessvision.app.ai.ONNXChessAI.Companion  
getPLUSAssign ,com.chessvision.app.ai.ONNXChessAI.Companion  
getPlusAssign ,com.chessvision.app.ai.ONNXChessAI.Companion  getSET ,com.chessvision.app.ai.ONNXChessAI.Companion  getSet ,com.chessvision.app.ai.ONNXChessAI.Companion  getTO ,com.chessvision.app.ai.ONNXChessAI.Companion  getTo ,com.chessvision.app.ai.ONNXChessAI.Companion  getUNTIL ,com.chessvision.app.ai.ONNXChessAI.Companion  getUSE ,com.chessvision.app.ai.ONNXChessAI.Companion  getUntil ,com.chessvision.app.ai.ONNXChessAI.Companion  getUse ,com.chessvision.app.ai.ONNXChessAI.Companion  getWITHContext ,com.chessvision.app.ai.ONNXChessAI.Companion  getWITHLock ,com.chessvision.app.ai.ONNXChessAI.Companion  getWithContext ,com.chessvision.app.ai.ONNXChessAI.Companion  getWithLock ,com.chessvision.app.ai.ONNXChessAI.Companion  indices ,com.chessvision.app.ai.ONNXChessAI.Companion  invoke ,com.chessvision.app.ai.ONNXChessAI.Companion  
isInitialized ,com.chessvision.app.ai.ONNXChessAI.Companion  java ,com.chessvision.app.ai.ONNXChessAI.Companion  joinToString ,com.chessvision.app.ai.ONNXChessAI.Companion  kotlin ,com.chessvision.app.ai.ONNXChessAI.Companion  let ,com.chessvision.app.ai.ONNXChessAI.Companion  loadBitmapFromUri ,com.chessvision.app.ai.ONNXChessAI.Companion  longArrayOf ,com.chessvision.app.ai.ONNXChessAI.Companion  mapOf ,com.chessvision.app.ai.ONNXChessAI.Companion  maxOf ,com.chessvision.app.ai.ONNXChessAI.Companion  minOf ,com.chessvision.app.ai.ONNXChessAI.Companion  
mutableListOf ,com.chessvision.app.ai.ONNXChessAI.Companion  mutableMapOf ,com.chessvision.app.ai.ONNXChessAI.Companion  operationCount ,com.chessvision.app.ai.ONNXChessAI.Companion  ortEnvironment ,com.chessvision.app.ai.ONNXChessAI.Companion  performanceMetrics ,com.chessvision.app.ai.ONNXChessAI.Companion  
plusAssign ,com.chessvision.app.ai.ONNXChessAI.Companion  run ,com.chessvision.app.ai.ONNXChessAI.Companion  runV6SegmentationSafe ,com.chessvision.app.ai.ONNXChessAI.Companion  runYOLODetectionSafe ,com.chessvision.app.ai.ONNXChessAI.Companion  set ,com.chessvision.app.ai.ONNXChessAI.Companion  to ,com.chessvision.app.ai.ONNXChessAI.Companion  until ,com.chessvision.app.ai.ONNXChessAI.Companion  use ,com.chessvision.app.ai.ONNXChessAI.Companion  	v6Session ,com.chessvision.app.ai.ONNXChessAI.Companion  withContext ,com.chessvision.app.ai.ONNXChessAI.Companion  withLock ,com.chessvision.app.ai.ONNXChessAI.Companion  yoloSession ,com.chessvision.app.ai.ONNXChessAI.Companion  Float 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  String 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  	className 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  
confidence 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  height 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  width 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  x 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  y 7com.chessvision.app.ai.ONNXChessAI.PieceDetectionResult  AdaptiveLayoutInfo com.chessvision.app.ui.theme  Boolean com.chessvision.app.ui.theme  Build com.chessvision.app.ui.theme  ChessDarkColors com.chessvision.app.ui.theme  ChessExpressiveAnimations com.chessvision.app.ui.theme  ChessExpressiveSurfaces com.chessvision.app.ui.theme  ChessExpressiveTypography com.chessvision.app.ui.theme  ChessFontFamily com.chessvision.app.ui.theme  ChessLightColors com.chessvision.app.ui.theme  ChessVisionAppTheme com.chessvision.app.ui.theme  Color com.chessvision.app.ui.theme  
Composable com.chessvision.app.ui.theme  
EaseInBack com.chessvision.app.ui.theme  
EaseInOutSine com.chessvision.app.ui.theme  EaseOutBack com.chessvision.app.ui.theme  EaseOutCubic com.chessvision.app.ui.theme  Float com.chessvision.app.ui.theme  
FontFamily com.chessvision.app.ui.theme  
FontWeight com.chessvision.app.ui.theme  Int com.chessvision.app.ui.theme  LinearEasing com.chessvision.app.ui.theme  LocalChessExpressiveSurfaces com.chessvision.app.ui.theme  Modifier com.chessvision.app.ui.theme  Offset com.chessvision.app.ui.theme  
RepeatMode com.chessvision.app.ui.theme  Shadow com.chessvision.app.ui.theme  Spring com.chessvision.app.ui.theme  	TextStyle com.chessvision.app.ui.theme  
Typography com.chessvision.app.ui.theme  Unit com.chessvision.app.ui.theme  WindowCompat com.chessvision.app.ui.theme  animateFloat com.chessvision.app.ui.theme  animateFloatAsState com.chessvision.app.ui.theme  breathingAnimation com.chessvision.app.ui.theme  	clickable com.chessvision.app.ui.theme  expressiveClickable com.chessvision.app.ui.theme  fadeIn com.chessvision.app.ui.theme  fadeOut com.chessvision.app.ui.theme  floatingAnimation com.chessvision.app.ui.theme  getValue com.chessvision.app.ui.theme  
graphicsLayer com.chessvision.app.ui.theme  infiniteRepeatable com.chessvision.app.ui.theme  listOf com.chessvision.app.ui.theme  minOf com.chessvision.app.ui.theme  mutableStateOf com.chessvision.app.ui.theme  provideDelegate com.chessvision.app.ui.theme  remember com.chessvision.app.ui.theme  rememberAdaptiveLayout com.chessvision.app.ui.theme  rememberInfiniteTransition com.chessvision.app.ui.theme  scaleIn com.chessvision.app.ui.theme  scaleOut com.chessvision.app.ui.theme  setValue com.chessvision.app.ui.theme  
shimmerEffect com.chessvision.app.ui.theme  slideInHorizontally com.chessvision.app.ui.theme  slideInVertically com.chessvision.app.ui.theme  spring com.chessvision.app.ui.theme  tween com.chessvision.app.ui.theme  Boolean /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  Dp /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  dp /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  getMINOf /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  getMinOf /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  
isExpanded /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  isMedium /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  minOf /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  screenHeight /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  screenWidth /com.chessvision.app.ui.theme.AdaptiveLayoutInfo  
EaseInBack 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  EaseOutBack 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  EaseOutCubic 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  Float 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  Int 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  Spring 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  fadeIn 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  fadeOut 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  	getFADEIn 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  
getFADEOut 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  	getFadeIn 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  
getFadeOut 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  
getSCALEIn 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getSCALEOut 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getSLIDEInHorizontally 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getSLIDEInVertically 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  	getSPRING 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  
getScaleIn 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getScaleOut 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getSlideInHorizontally 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getSlideInVertically 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  	getSpring 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getTWEEN 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  getTween 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  responsiveSpring 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  scaleIn 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  scaleOut 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  slideInHorizontally 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  slideInVertically 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  spring 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  tween 6com.chessvision.app.ui.theme.ChessExpressiveAnimations  Brush 4com.chessvision.app.ui.theme.ChessExpressiveSurfaces  Color 4com.chessvision.app.ui.theme.ChessExpressiveSurfaces  invoke 4com.chessvision.app.ui.theme.ChessExpressiveSurfaces  listOf 4com.chessvision.app.ui.theme.ChessExpressiveSurfaces  Bitmap com.chessvision.app.utils  
BitmapFactory com.chessvision.app.utils  	Exception com.chessvision.app.utils  
ImageUtils com.chessvision.app.utils  IntArray com.chessvision.app.utils  Log com.chessvision.app.utils  String com.chessvision.app.utils  until com.chessvision.app.utils  Bitmap $com.chessvision.app.utils.ImageUtils  
BitmapFactory $com.chessvision.app.utils.ImageUtils  Context $com.chessvision.app.utils.ImageUtils  	Exception $com.chessvision.app.utils.ImageUtils  InputStream $com.chessvision.app.utils.ImageUtils  IntArray $com.chessvision.app.utils.ImageUtils  Log $com.chessvision.app.utils.ImageUtils  String $com.chessvision.app.utils.ImageUtils  TAG $com.chessvision.app.utils.ImageUtils  Uri $com.chessvision.app.utils.ImageUtils  createTestChessboard $com.chessvision.app.utils.ImageUtils  getUNTIL $com.chessvision.app.utils.ImageUtils  getUntil $com.chessvision.app.utils.ImageUtils  loadBitmapFromUri $com.chessvision.app.utils.ImageUtils  until $com.chessvision.app.utils.ImageUtils  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  getADDListener 2com.google.common.util.concurrent.ListenableFuture  getAddListener 2com.google.common.util.concurrent.ListenableFuture  File java.io  FileOutputStream java.io  InputStream java.io  absolutePath java.io.File  delete java.io.File  exists java.io.File  getABSOLUTEPath java.io.File  getAbsolutePath java.io.File  getNAME java.io.File  getName java.io.File  	getPARENT java.io.File  	getParent java.io.File  mkdirs java.io.File  name java.io.File  parent java.io.File  renameTo java.io.File  setAbsolutePath java.io.File  setName java.io.File  	setParent java.io.File  sync java.io.FileDescriptor  fd java.io.FileOutputStream  flush java.io.FileOutputStream  getFD java.io.FileOutputStream  getFd java.io.FileOutputStream  getUSE java.io.FileOutputStream  getUse java.io.FileOutputStream  setFD java.io.FileOutputStream  use java.io.FileOutputStream  close java.io.InputStream  copyTo java.io.InputStream  	getCOPYTo java.io.InputStream  	getCopyTo java.io.InputStream  getUSE java.io.InputStream  getUse java.io.InputStream  use java.io.InputStream  flush java.io.OutputStream  use java.io.OutputStream  ActivityResultContracts 	java.lang  	Alignment 	java.lang  AndroidView 	java.lang  AnimatedVisibility 	java.lang  Arrangement 	java.lang  Array 	java.lang  
AtomicBoolean 	java.lang  AtomicReference 	java.lang  
AutoCloseable 	java.lang  Bitmap 	java.lang  
BitmapFactory 	java.lang  BoardSegmentationResult 	java.lang  Boolean 	java.lang  Box 	java.lang  Brush 	java.lang  Build 	java.lang  Button 	java.lang  ButtonDefaults 	java.lang  CameraSelector 	java.lang  CapturedImagePreview 	java.lang  Card 	java.lang  CardDefaults 	java.lang  ChessAI 	java.lang  ChessActionCard 	java.lang  ChessAnalysisResult 	java.lang  ChessAppHeader 	java.lang  ChessBoardControls 	java.lang  ChessBoardGuide 	java.lang  ChessExpressiveAnimations 	java.lang  ChessFontFamily 	java.lang  
ChessPiece 	java.lang  
ChessPosition 	java.lang  ChessSquare 	java.lang  ChessVisionAppTheme 	java.lang  CircleShape 	java.lang  CircularProgressIndicator 	java.lang  Class 	java.lang  Color 	java.lang  Column 	java.lang  
ContextCompat 	java.lang  
ControlButton 	java.lang  CornerGuide 	java.lang  
DICE_SCORE 	java.lang  Dispatchers 	java.lang  DisposableEffect 	java.lang  
EaseInBack 	java.lang  
EaseInOutSine 	java.lang  EaseOutBack 	java.lang  EaseOutCubic 	java.lang  	Exception 	java.lang  	Executors 	java.lang  ExperimentalMaterial3Api 	java.lang  
FENDisplay 	java.lang  FeatureItem 	java.lang  FeaturesSection 	java.lang  File 	java.lang  FileOutputStream 	java.lang  Float 	java.lang  
FloatArray 	java.lang  
FontFamily 	java.lang  
FontWeight 	java.lang  INFERENCE_TIME_MS 	java.lang  Icon 	java.lang  
IconButton 	java.lang  Icons 	java.lang  Image 	java.lang  ImageCapture 	java.lang  
ImageUtils 	java.lang  IntArray 	java.lang  	IntOffset 	java.lang  InteractiveChessBoard 	java.lang  
LazyColumn 	java.lang  LazyRow 	java.lang  LinearEasing 	java.lang  	LocalView 	java.lang  Locale 	java.lang  Log 	java.lang  MAP50_ACCURACY 	java.lang  MAX_PROCESSING_TIME_MS 	java.lang  MEMORY_CLEANUP_THRESHOLD 	java.lang  
MainScreen 	java.lang  Manifest 	java.lang  
MaterialTheme 	java.lang  	ModelInfo 	java.lang  ModernControlButton 	java.lang  Modifier 	java.lang  Mutex 	java.lang  Offset 	java.lang  OutlinedTextField 	java.lang  OutlinedTextFieldDefaults 	java.lang  PackageManager 	java.lang  
PaddingValues 	java.lang  Pair 	java.lang  
PieceColor 	java.lang  PieceDetectionResult 	java.lang  	PieceTray 	java.lang  
PieceTrayItem 	java.lang  	PieceType 	java.lang  Preview 	java.lang  PreviewView 	java.lang  ProcessCameraProvider 	java.lang  QuickActionsSection 	java.lang  R 	java.lang  Rect 	java.lang  
RepeatMode 	java.lang  RoundedCornerShape 	java.lang  Row 	java.lang  Shadow 	java.lang  Size 	java.lang  Spacer 	java.lang  Spring 	java.lang  String 	java.lang  
StringBuilder 	java.lang  Surface 	java.lang  System 	java.lang  TAG 	java.lang  Text 	java.lang  	TextAlign 	java.lang  	TextStyle 	java.lang  Toast 	java.lang  Unit 	java.lang  Uri 	java.lang  
V6_MODEL_FILE 	java.lang  WindowCompat 	java.lang  WindowInsetsCompat 	java.lang  WindowInsetsControllerCompat 	java.lang  YOLO_MODEL_FILE 	java.lang  addListener 	java.lang  ai 	java.lang  also 	java.lang  android 	java.lang  androidx 	java.lang  animateFloat 	java.lang  animateFloatAsState 	java.lang  apply 	java.lang  arrayOf 	java.lang  aspectRatio 	java.lang  
background 	java.lang  captureImageSafe 	java.lang  cleanup 	java.lang  cleanupResources 	java.lang  	clickable 	java.lang  coerceIn 	java.lang  com 	java.lang  contentToString 	java.lang  context 	java.lang  copyModelSafely 	java.lang  copyTo 	java.lang  delay 	java.lang  
digitToInt 	java.lang  downTo 	java.lang  	emptyList 	java.lang  emptyMap 	java.lang  fadeIn 	java.lang  fadeOut 	java.lang  
fillMaxHeight 	java.lang  fillMaxSize 	java.lang  fillMaxWidth 	java.lang  forEach 	java.lang  format 	java.lang  generateFENFromDetections 	java.lang  getValue 	java.lang  
graphicsLayer 	java.lang  height 	java.lang  indices 	java.lang  infiniteRepeatable 	java.lang  isDigit 	java.lang  
isInitialized 	java.lang  isUpperCase 	java.lang  iterator 	java.lang  java 	java.lang  joinToString 	java.lang  kotlin 	java.lang  kotlinx 	java.lang  launch 	java.lang  let 	java.lang  listOf 	java.lang  loadBitmapFromUri 	java.lang  longArrayOf 	java.lang  
lowercaseChar 	java.lang  mapOf 	java.lang  maxOf 	java.lang  minOf 	java.lang  
mutableListOf 	java.lang  mutableMapOf 	java.lang  mutableStateOf 	java.lang  navigationBarsPadding 	java.lang  offset 	java.lang  onGloballyPositioned 	java.lang  onnxAI 	java.lang  operationCount 	java.lang  ortEnvironment 	java.lang  padding 	java.lang  painterResource 	java.lang  performanceMetrics 	java.lang  
plusAssign 	java.lang  positionInRoot 	java.lang  provideDelegate 	java.lang  remember 	java.lang  rememberInfiniteTransition 	java.lang  run 	java.lang  runV6SegmentationSafe 	java.lang  runYOLODetectionSafe 	java.lang  scaleIn 	java.lang  scaleOut 	java.lang  set 	java.lang  setValue 	java.lang  size 	java.lang  slideInHorizontally 	java.lang  slideInVertically 	java.lang  slideOutHorizontally 	java.lang  split 	java.lang  spring 	java.lang  statusBarsPadding 	java.lang  to 	java.lang  toMap 	java.lang  totalOperations 	java.lang  totalProcessingTime 	java.lang  tween 	java.lang  until 	java.lang  
uppercaseChar 	java.lang  use 	java.lang  	v6Session 	java.lang  width 	java.lang  withContext 	java.lang  withLock 	java.lang  yoloSession 	java.lang  close java.lang.AutoCloseable  	getMethod java.lang.Class  message java.lang.Exception  <SAM-CONSTRUCTOR> java.lang.Runnable  append java.lang.StringBuilder  toString java.lang.StringBuilder  currentTimeMillis java.lang.System  gc java.lang.System  Method java.lang.reflect  invoke "java.lang.reflect.AccessibleObject  invoke java.lang.reflect.Executable  invoke java.lang.reflect.Method  Buffer java.nio  
ByteBuffer java.nio  	ByteOrder java.nio  FloatBuffer java.nio  array java.nio.Buffer  
asFloatBuffer java.nio.Buffer  get java.nio.Buffer  order java.nio.Buffer  put java.nio.Buffer  	remaining java.nio.Buffer  rewind java.nio.Buffer  allocateDirect java.nio.ByteBuffer  
asFloatBuffer java.nio.ByteBuffer  order java.nio.ByteBuffer  nativeOrder java.nio.ByteOrder  array java.nio.FloatBuffer  get java.nio.FloatBuffer  put java.nio.FloatBuffer  	remaining java.nio.FloatBuffer  rewind java.nio.FloatBuffer  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  ActivityResultContracts 	java.util  	Alignment 	java.util  AndroidView 	java.util  Arrangement 	java.util  
AtomicBoolean 	java.util  AtomicReference 	java.util  Box 	java.util  Brush 	java.util  Button 	java.util  ButtonDefaults 	java.util  	CameraAlt 	java.util  CameraSelector 	java.util  CapturedImagePreview 	java.util  Card 	java.util  CardDefaults 	java.util  CenterFocusStrong 	java.util  ChessBoardGuide 	java.util  CircleShape 	java.util  CircularProgressIndicator 	java.util  Color 	java.util  Column 	java.util  
Composable 	java.util  
ContextCompat 	java.util  CornerGuide 	java.util  DisposableEffect 	java.util  	Exception 	java.util  	Executors 	java.util  	FlashAuto 	java.util  FlashOff 	java.util  FlashOn 	java.util  
FontWeight 	java.util  Icon 	java.util  
IconButton 	java.util  Icons 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  Info 	java.util  Locale 	java.util  Log 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  PhotoLibrary 	java.util  Preview 	java.util  PreviewView 	java.util  ProcessCameraProvider 	java.util  
Psychology 	java.util  RoundedCornerShape 	java.util  Row 	java.util  Spacer 	java.util  Stable 	java.util  System 	java.util  TAG 	java.util  Text 	java.util  addListener 	java.util  also 	java.util  android 	java.util  androidx 	java.util  apply 	java.util  
background 	java.util  captureImageSafe 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  getValue 	java.util  height 	java.util  let 	java.util  listOf 	java.util  mutableStateOf 	java.util  navigationBarsPadding 	java.util  padding 	java.util  provideDelegate 	java.util  remember 	java.util  run 	java.util  setValue 	java.util  size 	java.util  statusBarsPadding 	java.util  width 	java.util  US java.util.Locale  Executor java.util.concurrent  ExecutorService java.util.concurrent  	Executors java.util.concurrent  shutdown $java.util.concurrent.ExecutorService  newSingleThreadExecutor java.util.concurrent.Executors  
AtomicBoolean java.util.concurrent.atomic  AtomicReference java.util.concurrent.atomic  
compareAndSet )java.util.concurrent.atomic.AtomicBoolean  get )java.util.concurrent.atomic.AtomicBoolean  set )java.util.concurrent.atomic.AtomicBoolean  get +java.util.concurrent.atomic.AtomicReference  	getAndSet +java.util.concurrent.atomic.AtomicReference  set +java.util.concurrent.atomic.AtomicReference  ActivityResultContracts kotlin  	Alignment kotlin  AndroidView kotlin  AnimatedVisibility kotlin  Any kotlin  Arrangement kotlin  Array kotlin  
AtomicBoolean kotlin  AtomicReference kotlin  
AutoCloseable kotlin  Bitmap kotlin  
BitmapFactory kotlin  BoardSegmentationResult kotlin  Boolean kotlin  Box kotlin  Brush kotlin  Build kotlin  Button kotlin  ButtonDefaults kotlin  CameraSelector kotlin  CapturedImagePreview kotlin  Card kotlin  CardDefaults kotlin  Char kotlin  ChessAI kotlin  ChessActionCard kotlin  ChessAnalysisResult kotlin  ChessAppHeader kotlin  ChessBoardControls kotlin  ChessBoardGuide kotlin  ChessExpressiveAnimations kotlin  ChessFontFamily kotlin  
ChessPiece kotlin  
ChessPosition kotlin  ChessSquare kotlin  ChessVisionAppTheme kotlin  CircleShape kotlin  CircularProgressIndicator kotlin  Color kotlin  Column kotlin  
ContextCompat kotlin  
ControlButton kotlin  CornerGuide kotlin  
DICE_SCORE kotlin  Dispatchers kotlin  DisposableEffect kotlin  Double kotlin  
EaseInBack kotlin  
EaseInOutSine kotlin  EaseOutBack kotlin  EaseOutCubic kotlin  	Exception kotlin  	Executors kotlin  ExperimentalMaterial3Api kotlin  
FENDisplay kotlin  FeatureItem kotlin  FeaturesSection kotlin  File kotlin  FileOutputStream kotlin  Float kotlin  
FloatArray kotlin  
FontFamily kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  INFERENCE_TIME_MS kotlin  Icon kotlin  
IconButton kotlin  Icons kotlin  Image kotlin  ImageCapture kotlin  
ImageUtils kotlin  Int kotlin  IntArray kotlin  	IntOffset kotlin  InteractiveChessBoard kotlin  
LazyColumn kotlin  LazyRow kotlin  LinearEasing kotlin  	LocalView kotlin  Locale kotlin  Log kotlin  Long kotlin  	LongArray kotlin  MAP50_ACCURACY kotlin  MAX_PROCESSING_TIME_MS kotlin  MEMORY_CLEANUP_THRESHOLD kotlin  
MainScreen kotlin  Manifest kotlin  
MaterialTheme kotlin  	ModelInfo kotlin  ModernControlButton kotlin  Modifier kotlin  Mutex kotlin  Nothing kotlin  Offset kotlin  OptIn kotlin  OutlinedTextField kotlin  OutlinedTextFieldDefaults kotlin  PackageManager kotlin  
PaddingValues kotlin  Pair kotlin  
PieceColor kotlin  PieceDetectionResult kotlin  	PieceTray kotlin  
PieceTrayItem kotlin  	PieceType kotlin  Preview kotlin  PreviewView kotlin  ProcessCameraProvider kotlin  QuickActionsSection kotlin  R kotlin  Rect kotlin  
RepeatMode kotlin  RoundedCornerShape kotlin  Row kotlin  Shadow kotlin  Size kotlin  Spacer kotlin  Spring kotlin  String kotlin  
StringBuilder kotlin  Suppress kotlin  Surface kotlin  System kotlin  TAG kotlin  Text kotlin  	TextAlign kotlin  	TextStyle kotlin  Toast kotlin  Unit kotlin  Uri kotlin  
V6_MODEL_FILE kotlin  WindowCompat kotlin  WindowInsetsCompat kotlin  WindowInsetsControllerCompat kotlin  YOLO_MODEL_FILE kotlin  addListener kotlin  ai kotlin  also kotlin  android kotlin  androidx kotlin  animateFloat kotlin  animateFloatAsState kotlin  apply kotlin  arrayOf kotlin  aspectRatio kotlin  
background kotlin  captureImageSafe kotlin  cleanup kotlin  cleanupResources kotlin  	clickable kotlin  coerceIn kotlin  com kotlin  contentToString kotlin  context kotlin  copyModelSafely kotlin  copyTo kotlin  delay kotlin  
digitToInt kotlin  downTo kotlin  	emptyList kotlin  emptyMap kotlin  fadeIn kotlin  fadeOut kotlin  
fillMaxHeight kotlin  fillMaxSize kotlin  fillMaxWidth kotlin  forEach kotlin  format kotlin  generateFENFromDetections kotlin  getValue kotlin  
graphicsLayer kotlin  height kotlin  indices kotlin  infiniteRepeatable kotlin  isDigit kotlin  
isInitialized kotlin  isUpperCase kotlin  iterator kotlin  java kotlin  joinToString kotlin  kotlin kotlin  kotlinx kotlin  launch kotlin  let kotlin  listOf kotlin  loadBitmapFromUri kotlin  longArrayOf kotlin  
lowercaseChar kotlin  mapOf kotlin  maxOf kotlin  minOf kotlin  
mutableListOf kotlin  mutableMapOf kotlin  mutableStateOf kotlin  navigationBarsPadding kotlin  offset kotlin  onGloballyPositioned kotlin  onnxAI kotlin  operationCount kotlin  ortEnvironment kotlin  padding kotlin  painterResource kotlin  performanceMetrics kotlin  
plusAssign kotlin  positionInRoot kotlin  provideDelegate kotlin  remember kotlin  rememberInfiniteTransition kotlin  run kotlin  runV6SegmentationSafe kotlin  runYOLODetectionSafe kotlin  scaleIn kotlin  scaleOut kotlin  set kotlin  setValue kotlin  size kotlin  slideInHorizontally kotlin  slideInVertically kotlin  slideOutHorizontally kotlin  split kotlin  spring kotlin  statusBarsPadding kotlin  to kotlin  toMap kotlin  totalOperations kotlin  totalProcessingTime kotlin  tween kotlin  until kotlin  
uppercaseChar kotlin  use kotlin  	v6Session kotlin  width kotlin  withContext kotlin  withLock kotlin  yoloSession kotlin  
getINDICES kotlin.Array  
getIndices kotlin.Array  	Companion kotlin.Boolean  
getDIGITToInt kotlin.Char  
getDigitToInt kotlin.Char  
getISDigit kotlin.Char  getISUpperCase kotlin.Char  
getIsDigit kotlin.Char  getIsUpperCase kotlin.Char  getLOWERCASEChar kotlin.Char  getLowercaseChar kotlin.Char  getUPPERCASEChar kotlin.Char  getUppercaseChar kotlin.Char  isDigit kotlin.Char  isUpperCase kotlin.Char  getSP 
kotlin.Double  getSp 
kotlin.Double  getCOERCEIn 
kotlin.Int  getCoerceIn 
kotlin.Int  	getDOWNTo 
kotlin.Int  getDP 
kotlin.Int  	getDownTo 
kotlin.Int  getDp 
kotlin.Int  
getPLUSAssign 
kotlin.Int  
getPlusAssign 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getUNTIL 
kotlin.Int  getUntil 
kotlin.Int  
getPLUSAssign kotlin.Long  
getPlusAssign kotlin.Long  getCONTENTToString kotlin.LongArray  getContentToString kotlin.LongArray  
component1 kotlin.Pair  
component2 kotlin.Pair  equals kotlin.Pair  getITERATOR 
kotlin.String  getIterator 
kotlin.String  getLET 
kotlin.String  getLet 
kotlin.String  
getPLUSAssign 
kotlin.String  
getPlusAssign 
kotlin.String  getSPLIT 
kotlin.String  getSplit 
kotlin.String  getTO 
kotlin.String  getTo 
kotlin.String  	getFORMAT kotlin.String.Companion  	getFormat kotlin.String.Companion  ActivityResultContracts kotlin.annotation  	Alignment kotlin.annotation  AndroidView kotlin.annotation  AnimatedVisibility kotlin.annotation  Arrangement kotlin.annotation  Array kotlin.annotation  
AtomicBoolean kotlin.annotation  AtomicReference kotlin.annotation  
AutoCloseable kotlin.annotation  Bitmap kotlin.annotation  
BitmapFactory kotlin.annotation  BoardSegmentationResult kotlin.annotation  Boolean kotlin.annotation  Box kotlin.annotation  Brush kotlin.annotation  Build kotlin.annotation  Button kotlin.annotation  ButtonDefaults kotlin.annotation  CameraSelector kotlin.annotation  CapturedImagePreview kotlin.annotation  Card kotlin.annotation  CardDefaults kotlin.annotation  ChessAI kotlin.annotation  ChessActionCard kotlin.annotation  ChessAnalysisResult kotlin.annotation  ChessAppHeader kotlin.annotation  ChessBoardControls kotlin.annotation  ChessBoardGuide kotlin.annotation  ChessExpressiveAnimations kotlin.annotation  ChessFontFamily kotlin.annotation  
ChessPiece kotlin.annotation  
ChessPosition kotlin.annotation  ChessSquare kotlin.annotation  ChessVisionAppTheme kotlin.annotation  CircleShape kotlin.annotation  CircularProgressIndicator kotlin.annotation  Color kotlin.annotation  Column kotlin.annotation  
ContextCompat kotlin.annotation  
ControlButton kotlin.annotation  CornerGuide kotlin.annotation  
DICE_SCORE kotlin.annotation  Dispatchers kotlin.annotation  DisposableEffect kotlin.annotation  
EaseInBack kotlin.annotation  
EaseInOutSine kotlin.annotation  EaseOutBack kotlin.annotation  EaseOutCubic kotlin.annotation  	Exception kotlin.annotation  	Executors kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  
FENDisplay kotlin.annotation  FeatureItem kotlin.annotation  FeaturesSection kotlin.annotation  File kotlin.annotation  FileOutputStream kotlin.annotation  Float kotlin.annotation  
FloatArray kotlin.annotation  
FontFamily kotlin.annotation  
FontWeight kotlin.annotation  INFERENCE_TIME_MS kotlin.annotation  Icon kotlin.annotation  
IconButton kotlin.annotation  Icons kotlin.annotation  Image kotlin.annotation  ImageCapture kotlin.annotation  
ImageUtils kotlin.annotation  IntArray kotlin.annotation  	IntOffset kotlin.annotation  InteractiveChessBoard kotlin.annotation  
LazyColumn kotlin.annotation  LazyRow kotlin.annotation  LinearEasing kotlin.annotation  	LocalView kotlin.annotation  Locale kotlin.annotation  Log kotlin.annotation  MAP50_ACCURACY kotlin.annotation  MAX_PROCESSING_TIME_MS kotlin.annotation  MEMORY_CLEANUP_THRESHOLD kotlin.annotation  
MainScreen kotlin.annotation  Manifest kotlin.annotation  
MaterialTheme kotlin.annotation  	ModelInfo kotlin.annotation  ModernControlButton kotlin.annotation  Modifier kotlin.annotation  Mutex kotlin.annotation  Offset kotlin.annotation  OutlinedTextField kotlin.annotation  OutlinedTextFieldDefaults kotlin.annotation  PackageManager kotlin.annotation  
PaddingValues kotlin.annotation  Pair kotlin.annotation  
PieceColor kotlin.annotation  PieceDetectionResult kotlin.annotation  	PieceTray kotlin.annotation  
PieceTrayItem kotlin.annotation  	PieceType kotlin.annotation  Preview kotlin.annotation  PreviewView kotlin.annotation  ProcessCameraProvider kotlin.annotation  QuickActionsSection kotlin.annotation  R kotlin.annotation  Rect kotlin.annotation  
RepeatMode kotlin.annotation  RoundedCornerShape kotlin.annotation  Row kotlin.annotation  Shadow kotlin.annotation  Size kotlin.annotation  Spacer kotlin.annotation  Spring kotlin.annotation  String kotlin.annotation  
StringBuilder kotlin.annotation  Surface kotlin.annotation  System kotlin.annotation  TAG kotlin.annotation  Text kotlin.annotation  	TextAlign kotlin.annotation  	TextStyle kotlin.annotation  Toast kotlin.annotation  Unit kotlin.annotation  Uri kotlin.annotation  
V6_MODEL_FILE kotlin.annotation  WindowCompat kotlin.annotation  WindowInsetsCompat kotlin.annotation  WindowInsetsControllerCompat kotlin.annotation  YOLO_MODEL_FILE kotlin.annotation  addListener kotlin.annotation  ai kotlin.annotation  also kotlin.annotation  android kotlin.annotation  androidx kotlin.annotation  animateFloat kotlin.annotation  animateFloatAsState kotlin.annotation  apply kotlin.annotation  arrayOf kotlin.annotation  aspectRatio kotlin.annotation  
background kotlin.annotation  captureImageSafe kotlin.annotation  cleanup kotlin.annotation  cleanupResources kotlin.annotation  	clickable kotlin.annotation  coerceIn kotlin.annotation  com kotlin.annotation  contentToString kotlin.annotation  context kotlin.annotation  copyModelSafely kotlin.annotation  copyTo kotlin.annotation  delay kotlin.annotation  
digitToInt kotlin.annotation  downTo kotlin.annotation  	emptyList kotlin.annotation  emptyMap kotlin.annotation  fadeIn kotlin.annotation  fadeOut kotlin.annotation  
fillMaxHeight kotlin.annotation  fillMaxSize kotlin.annotation  fillMaxWidth kotlin.annotation  forEach kotlin.annotation  format kotlin.annotation  generateFENFromDetections kotlin.annotation  getValue kotlin.annotation  
graphicsLayer kotlin.annotation  height kotlin.annotation  indices kotlin.annotation  infiniteRepeatable kotlin.annotation  isDigit kotlin.annotation  
isInitialized kotlin.annotation  isUpperCase kotlin.annotation  iterator kotlin.annotation  java kotlin.annotation  joinToString kotlin.annotation  kotlin kotlin.annotation  kotlinx kotlin.annotation  launch kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  loadBitmapFromUri kotlin.annotation  longArrayOf kotlin.annotation  
lowercaseChar kotlin.annotation  mapOf kotlin.annotation  maxOf kotlin.annotation  minOf kotlin.annotation  
mutableListOf kotlin.annotation  mutableMapOf kotlin.annotation  mutableStateOf kotlin.annotation  navigationBarsPadding kotlin.annotation  offset kotlin.annotation  onGloballyPositioned kotlin.annotation  onnxAI kotlin.annotation  operationCount kotlin.annotation  ortEnvironment kotlin.annotation  padding kotlin.annotation  painterResource kotlin.annotation  performanceMetrics kotlin.annotation  
plusAssign kotlin.annotation  positionInRoot kotlin.annotation  provideDelegate kotlin.annotation  remember kotlin.annotation  rememberInfiniteTransition kotlin.annotation  run kotlin.annotation  runV6SegmentationSafe kotlin.annotation  runYOLODetectionSafe kotlin.annotation  scaleIn kotlin.annotation  scaleOut kotlin.annotation  set kotlin.annotation  setValue kotlin.annotation  size kotlin.annotation  slideInHorizontally kotlin.annotation  slideInVertically kotlin.annotation  slideOutHorizontally kotlin.annotation  split kotlin.annotation  spring kotlin.annotation  statusBarsPadding kotlin.annotation  to kotlin.annotation  toMap kotlin.annotation  totalOperations kotlin.annotation  totalProcessingTime kotlin.annotation  tween kotlin.annotation  until kotlin.annotation  
uppercaseChar kotlin.annotation  use kotlin.annotation  	v6Session kotlin.annotation  width kotlin.annotation  withContext kotlin.annotation  withLock kotlin.annotation  yoloSession kotlin.annotation  ActivityResultContracts kotlin.collections  	Alignment kotlin.collections  AndroidView kotlin.collections  AnimatedVisibility kotlin.collections  Arrangement kotlin.collections  Array kotlin.collections  
AtomicBoolean kotlin.collections  AtomicReference kotlin.collections  
AutoCloseable kotlin.collections  Bitmap kotlin.collections  
BitmapFactory kotlin.collections  BoardSegmentationResult kotlin.collections  Boolean kotlin.collections  Box kotlin.collections  Brush kotlin.collections  Build kotlin.collections  Button kotlin.collections  ButtonDefaults kotlin.collections  CameraSelector kotlin.collections  CapturedImagePreview kotlin.collections  Card kotlin.collections  CardDefaults kotlin.collections  ChessAI kotlin.collections  ChessActionCard kotlin.collections  ChessAnalysisResult kotlin.collections  ChessAppHeader kotlin.collections  ChessBoardControls kotlin.collections  ChessBoardGuide kotlin.collections  ChessExpressiveAnimations kotlin.collections  ChessFontFamily kotlin.collections  
ChessPiece kotlin.collections  
ChessPosition kotlin.collections  ChessSquare kotlin.collections  ChessVisionAppTheme kotlin.collections  CircleShape kotlin.collections  CircularProgressIndicator kotlin.collections  Color kotlin.collections  Column kotlin.collections  
ContextCompat kotlin.collections  
ControlButton kotlin.collections  CornerGuide kotlin.collections  
DICE_SCORE kotlin.collections  Dispatchers kotlin.collections  DisposableEffect kotlin.collections  
EaseInBack kotlin.collections  
EaseInOutSine kotlin.collections  EaseOutBack kotlin.collections  EaseOutCubic kotlin.collections  	Exception kotlin.collections  	Executors kotlin.collections  ExperimentalMaterial3Api kotlin.collections  
FENDisplay kotlin.collections  FeatureItem kotlin.collections  FeaturesSection kotlin.collections  File kotlin.collections  FileOutputStream kotlin.collections  Float kotlin.collections  
FloatArray kotlin.collections  
FontFamily kotlin.collections  
FontWeight kotlin.collections  INFERENCE_TIME_MS kotlin.collections  Icon kotlin.collections  
IconButton kotlin.collections  Icons kotlin.collections  Image kotlin.collections  ImageCapture kotlin.collections  
ImageUtils kotlin.collections  IntArray kotlin.collections  	IntOffset kotlin.collections  InteractiveChessBoard kotlin.collections  
LazyColumn kotlin.collections  LazyRow kotlin.collections  LinearEasing kotlin.collections  List kotlin.collections  	LocalView kotlin.collections  Locale kotlin.collections  Log kotlin.collections  MAP50_ACCURACY kotlin.collections  MAX_PROCESSING_TIME_MS kotlin.collections  MEMORY_CLEANUP_THRESHOLD kotlin.collections  
MainScreen kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  
MaterialTheme kotlin.collections  	ModelInfo kotlin.collections  ModernControlButton kotlin.collections  Modifier kotlin.collections  MutableList kotlin.collections  
MutableMap kotlin.collections  Mutex kotlin.collections  Offset kotlin.collections  OutlinedTextField kotlin.collections  OutlinedTextFieldDefaults kotlin.collections  PackageManager kotlin.collections  
PaddingValues kotlin.collections  Pair kotlin.collections  
PieceColor kotlin.collections  PieceDetectionResult kotlin.collections  	PieceTray kotlin.collections  
PieceTrayItem kotlin.collections  	PieceType kotlin.collections  Preview kotlin.collections  PreviewView kotlin.collections  ProcessCameraProvider kotlin.collections  QuickActionsSection kotlin.collections  R kotlin.collections  Rect kotlin.collections  
RepeatMode kotlin.collections  RoundedCornerShape kotlin.collections  Row kotlin.collections  Shadow kotlin.collections  Size kotlin.collections  Spacer kotlin.collections  Spring kotlin.collections  String kotlin.collections  
StringBuilder kotlin.collections  Surface kotlin.collections  System kotlin.collections  TAG kotlin.collections  Text kotlin.collections  	TextAlign kotlin.collections  	TextStyle kotlin.collections  Toast kotlin.collections  Unit kotlin.collections  Uri kotlin.collections  
V6_MODEL_FILE kotlin.collections  WindowCompat kotlin.collections  WindowInsetsCompat kotlin.collections  WindowInsetsControllerCompat kotlin.collections  YOLO_MODEL_FILE kotlin.collections  addListener kotlin.collections  ai kotlin.collections  also kotlin.collections  android kotlin.collections  androidx kotlin.collections  animateFloat kotlin.collections  animateFloatAsState kotlin.collections  apply kotlin.collections  arrayOf kotlin.collections  aspectRatio kotlin.collections  
background kotlin.collections  captureImageSafe kotlin.collections  cleanup kotlin.collections  cleanupResources kotlin.collections  	clickable kotlin.collections  coerceIn kotlin.collections  com kotlin.collections  contentToString kotlin.collections  context kotlin.collections  copyModelSafely kotlin.collections  copyTo kotlin.collections  delay kotlin.collections  
digitToInt kotlin.collections  downTo kotlin.collections  	emptyList kotlin.collections  emptyMap kotlin.collections  fadeIn kotlin.collections  fadeOut kotlin.collections  
fillMaxHeight kotlin.collections  fillMaxSize kotlin.collections  fillMaxWidth kotlin.collections  forEach kotlin.collections  format kotlin.collections  generateFENFromDetections kotlin.collections  getValue kotlin.collections  
graphicsLayer kotlin.collections  height kotlin.collections  indices kotlin.collections  infiniteRepeatable kotlin.collections  isDigit kotlin.collections  
isInitialized kotlin.collections  isUpperCase kotlin.collections  iterator kotlin.collections  java kotlin.collections  joinToString kotlin.collections  kotlin kotlin.collections  kotlinx kotlin.collections  launch kotlin.collections  let kotlin.collections  listOf kotlin.collections  loadBitmapFromUri kotlin.collections  longArrayOf kotlin.collections  
lowercaseChar kotlin.collections  mapOf kotlin.collections  maxOf kotlin.collections  minOf kotlin.collections  
mutableListOf kotlin.collections  mutableMapOf kotlin.collections  mutableStateOf kotlin.collections  navigationBarsPadding kotlin.collections  offset kotlin.collections  onGloballyPositioned kotlin.collections  onnxAI kotlin.collections  operationCount kotlin.collections  ortEnvironment kotlin.collections  padding kotlin.collections  painterResource kotlin.collections  performanceMetrics kotlin.collections  
plusAssign kotlin.collections  positionInRoot kotlin.collections  provideDelegate kotlin.collections  remember kotlin.collections  rememberInfiniteTransition kotlin.collections  run kotlin.collections  runV6SegmentationSafe kotlin.collections  runYOLODetectionSafe kotlin.collections  scaleIn kotlin.collections  scaleOut kotlin.collections  set kotlin.collections  setValue kotlin.collections  size kotlin.collections  slideInHorizontally kotlin.collections  slideInVertically kotlin.collections  slideOutHorizontally kotlin.collections  split kotlin.collections  spring kotlin.collections  statusBarsPadding kotlin.collections  to kotlin.collections  toMap kotlin.collections  totalOperations kotlin.collections  totalProcessingTime kotlin.collections  tween kotlin.collections  until kotlin.collections  
uppercaseChar kotlin.collections  use kotlin.collections  	v6Session kotlin.collections  width kotlin.collections  withContext kotlin.collections  withLock kotlin.collections  yoloSession kotlin.collections  hasNext kotlin.collections.CharIterator  next kotlin.collections.CharIterator  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  
getINDICES kotlin.collections.List  
getIndices kotlin.collections.List  getJOINToString kotlin.collections.MutableList  getJoinToString kotlin.collections.MutableList  getSET kotlin.collections.MutableMap  getSet kotlin.collections.MutableMap  getTOMap kotlin.collections.MutableMap  getToMap kotlin.collections.MutableMap  ActivityResultContracts kotlin.comparisons  	Alignment kotlin.comparisons  AndroidView kotlin.comparisons  AnimatedVisibility kotlin.comparisons  Arrangement kotlin.comparisons  Array kotlin.comparisons  
AtomicBoolean kotlin.comparisons  AtomicReference kotlin.comparisons  
AutoCloseable kotlin.comparisons  Bitmap kotlin.comparisons  
BitmapFactory kotlin.comparisons  BoardSegmentationResult kotlin.comparisons  Boolean kotlin.comparisons  Box kotlin.comparisons  Brush kotlin.comparisons  Build kotlin.comparisons  Button kotlin.comparisons  ButtonDefaults kotlin.comparisons  CameraSelector kotlin.comparisons  CapturedImagePreview kotlin.comparisons  Card kotlin.comparisons  CardDefaults kotlin.comparisons  ChessAI kotlin.comparisons  ChessActionCard kotlin.comparisons  ChessAnalysisResult kotlin.comparisons  ChessAppHeader kotlin.comparisons  ChessBoardControls kotlin.comparisons  ChessBoardGuide kotlin.comparisons  ChessExpressiveAnimations kotlin.comparisons  ChessFontFamily kotlin.comparisons  
ChessPiece kotlin.comparisons  
ChessPosition kotlin.comparisons  ChessSquare kotlin.comparisons  ChessVisionAppTheme kotlin.comparisons  CircleShape kotlin.comparisons  CircularProgressIndicator kotlin.comparisons  Color kotlin.comparisons  Column kotlin.comparisons  
ContextCompat kotlin.comparisons  
ControlButton kotlin.comparisons  CornerGuide kotlin.comparisons  
DICE_SCORE kotlin.comparisons  Dispatchers kotlin.comparisons  DisposableEffect kotlin.comparisons  
EaseInBack kotlin.comparisons  
EaseInOutSine kotlin.comparisons  EaseOutBack kotlin.comparisons  EaseOutCubic kotlin.comparisons  	Exception kotlin.comparisons  	Executors kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  
FENDisplay kotlin.comparisons  FeatureItem kotlin.comparisons  FeaturesSection kotlin.comparisons  File kotlin.comparisons  FileOutputStream kotlin.comparisons  Float kotlin.comparisons  
FloatArray kotlin.comparisons  
FontFamily kotlin.comparisons  
FontWeight kotlin.comparisons  INFERENCE_TIME_MS kotlin.comparisons  Icon kotlin.comparisons  
IconButton kotlin.comparisons  Icons kotlin.comparisons  Image kotlin.comparisons  ImageCapture kotlin.comparisons  
ImageUtils kotlin.comparisons  IntArray kotlin.comparisons  	IntOffset kotlin.comparisons  InteractiveChessBoard kotlin.comparisons  
LazyColumn kotlin.comparisons  LazyRow kotlin.comparisons  LinearEasing kotlin.comparisons  	LocalView kotlin.comparisons  Locale kotlin.comparisons  Log kotlin.comparisons  MAP50_ACCURACY kotlin.comparisons  MAX_PROCESSING_TIME_MS kotlin.comparisons  MEMORY_CLEANUP_THRESHOLD kotlin.comparisons  
MainScreen kotlin.comparisons  Manifest kotlin.comparisons  
MaterialTheme kotlin.comparisons  	ModelInfo kotlin.comparisons  ModernControlButton kotlin.comparisons  Modifier kotlin.comparisons  Mutex kotlin.comparisons  Offset kotlin.comparisons  OutlinedTextField kotlin.comparisons  OutlinedTextFieldDefaults kotlin.comparisons  PackageManager kotlin.comparisons  
PaddingValues kotlin.comparisons  Pair kotlin.comparisons  
PieceColor kotlin.comparisons  PieceDetectionResult kotlin.comparisons  	PieceTray kotlin.comparisons  
PieceTrayItem kotlin.comparisons  	PieceType kotlin.comparisons  Preview kotlin.comparisons  PreviewView kotlin.comparisons  ProcessCameraProvider kotlin.comparisons  QuickActionsSection kotlin.comparisons  R kotlin.comparisons  Rect kotlin.comparisons  
RepeatMode kotlin.comparisons  RoundedCornerShape kotlin.comparisons  Row kotlin.comparisons  Shadow kotlin.comparisons  Size kotlin.comparisons  Spacer kotlin.comparisons  Spring kotlin.comparisons  String kotlin.comparisons  
StringBuilder kotlin.comparisons  Surface kotlin.comparisons  System kotlin.comparisons  TAG kotlin.comparisons  Text kotlin.comparisons  	TextAlign kotlin.comparisons  	TextStyle kotlin.comparisons  Toast kotlin.comparisons  Unit kotlin.comparisons  Uri kotlin.comparisons  
V6_MODEL_FILE kotlin.comparisons  WindowCompat kotlin.comparisons  WindowInsetsCompat kotlin.comparisons  WindowInsetsControllerCompat kotlin.comparisons  YOLO_MODEL_FILE kotlin.comparisons  addListener kotlin.comparisons  ai kotlin.comparisons  also kotlin.comparisons  android kotlin.comparisons  androidx kotlin.comparisons  animateFloat kotlin.comparisons  animateFloatAsState kotlin.comparisons  apply kotlin.comparisons  arrayOf kotlin.comparisons  aspectRatio kotlin.comparisons  
background kotlin.comparisons  captureImageSafe kotlin.comparisons  cleanup kotlin.comparisons  cleanupResources kotlin.comparisons  	clickable kotlin.comparisons  coerceIn kotlin.comparisons  com kotlin.comparisons  contentToString kotlin.comparisons  context kotlin.comparisons  copyModelSafely kotlin.comparisons  copyTo kotlin.comparisons  delay kotlin.comparisons  
digitToInt kotlin.comparisons  downTo kotlin.comparisons  	emptyList kotlin.comparisons  emptyMap kotlin.comparisons  fadeIn kotlin.comparisons  fadeOut kotlin.comparisons  
fillMaxHeight kotlin.comparisons  fillMaxSize kotlin.comparisons  fillMaxWidth kotlin.comparisons  forEach kotlin.comparisons  format kotlin.comparisons  generateFENFromDetections kotlin.comparisons  getValue kotlin.comparisons  
graphicsLayer kotlin.comparisons  height kotlin.comparisons  indices kotlin.comparisons  infiniteRepeatable kotlin.comparisons  isDigit kotlin.comparisons  
isInitialized kotlin.comparisons  isUpperCase kotlin.comparisons  iterator kotlin.comparisons  java kotlin.comparisons  joinToString kotlin.comparisons  kotlin kotlin.comparisons  kotlinx kotlin.comparisons  launch kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  loadBitmapFromUri kotlin.comparisons  longArrayOf kotlin.comparisons  
lowercaseChar kotlin.comparisons  mapOf kotlin.comparisons  maxOf kotlin.comparisons  minOf kotlin.comparisons  
mutableListOf kotlin.comparisons  mutableMapOf kotlin.comparisons  mutableStateOf kotlin.comparisons  navigationBarsPadding kotlin.comparisons  offset kotlin.comparisons  onGloballyPositioned kotlin.comparisons  onnxAI kotlin.comparisons  operationCount kotlin.comparisons  ortEnvironment kotlin.comparisons  padding kotlin.comparisons  painterResource kotlin.comparisons  performanceMetrics kotlin.comparisons  
plusAssign kotlin.comparisons  positionInRoot kotlin.comparisons  provideDelegate kotlin.comparisons  remember kotlin.comparisons  rememberInfiniteTransition kotlin.comparisons  run kotlin.comparisons  runV6SegmentationSafe kotlin.comparisons  runYOLODetectionSafe kotlin.comparisons  scaleIn kotlin.comparisons  scaleOut kotlin.comparisons  set kotlin.comparisons  setValue kotlin.comparisons  size kotlin.comparisons  slideInHorizontally kotlin.comparisons  slideInVertically kotlin.comparisons  slideOutHorizontally kotlin.comparisons  split kotlin.comparisons  spring kotlin.comparisons  statusBarsPadding kotlin.comparisons  to kotlin.comparisons  toMap kotlin.comparisons  totalOperations kotlin.comparisons  totalProcessingTime kotlin.comparisons  tween kotlin.comparisons  until kotlin.comparisons  
uppercaseChar kotlin.comparisons  use kotlin.comparisons  	v6Session kotlin.comparisons  width kotlin.comparisons  withContext kotlin.comparisons  withLock kotlin.comparisons  yoloSession kotlin.comparisons  SuspendFunction1 kotlin.coroutines  ActivityResultContracts 	kotlin.io  	Alignment 	kotlin.io  AndroidView 	kotlin.io  AnimatedVisibility 	kotlin.io  Arrangement 	kotlin.io  Array 	kotlin.io  
AtomicBoolean 	kotlin.io  AtomicReference 	kotlin.io  
AutoCloseable 	kotlin.io  Bitmap 	kotlin.io  
BitmapFactory 	kotlin.io  BoardSegmentationResult 	kotlin.io  Boolean 	kotlin.io  Box 	kotlin.io  Brush 	kotlin.io  Build 	kotlin.io  Button 	kotlin.io  ButtonDefaults 	kotlin.io  CameraSelector 	kotlin.io  CapturedImagePreview 	kotlin.io  Card 	kotlin.io  CardDefaults 	kotlin.io  ChessAI 	kotlin.io  ChessActionCard 	kotlin.io  ChessAnalysisResult 	kotlin.io  ChessAppHeader 	kotlin.io  ChessBoardControls 	kotlin.io  ChessBoardGuide 	kotlin.io  ChessExpressiveAnimations 	kotlin.io  ChessFontFamily 	kotlin.io  
ChessPiece 	kotlin.io  
ChessPosition 	kotlin.io  ChessSquare 	kotlin.io  ChessVisionAppTheme 	kotlin.io  CircleShape 	kotlin.io  CircularProgressIndicator 	kotlin.io  Color 	kotlin.io  Column 	kotlin.io  
ContextCompat 	kotlin.io  
ControlButton 	kotlin.io  CornerGuide 	kotlin.io  
DICE_SCORE 	kotlin.io  Dispatchers 	kotlin.io  DisposableEffect 	kotlin.io  
EaseInBack 	kotlin.io  
EaseInOutSine 	kotlin.io  EaseOutBack 	kotlin.io  EaseOutCubic 	kotlin.io  	Exception 	kotlin.io  	Executors 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  
FENDisplay 	kotlin.io  FeatureItem 	kotlin.io  FeaturesSection 	kotlin.io  File 	kotlin.io  FileOutputStream 	kotlin.io  Float 	kotlin.io  
FloatArray 	kotlin.io  
FontFamily 	kotlin.io  
FontWeight 	kotlin.io  INFERENCE_TIME_MS 	kotlin.io  Icon 	kotlin.io  
IconButton 	kotlin.io  Icons 	kotlin.io  Image 	kotlin.io  ImageCapture 	kotlin.io  
ImageUtils 	kotlin.io  IntArray 	kotlin.io  	IntOffset 	kotlin.io  InteractiveChessBoard 	kotlin.io  
LazyColumn 	kotlin.io  LazyRow 	kotlin.io  LinearEasing 	kotlin.io  	LocalView 	kotlin.io  Locale 	kotlin.io  Log 	kotlin.io  MAP50_ACCURACY 	kotlin.io  MAX_PROCESSING_TIME_MS 	kotlin.io  MEMORY_CLEANUP_THRESHOLD 	kotlin.io  
MainScreen 	kotlin.io  Manifest 	kotlin.io  
MaterialTheme 	kotlin.io  	ModelInfo 	kotlin.io  ModernControlButton 	kotlin.io  Modifier 	kotlin.io  Mutex 	kotlin.io  Offset 	kotlin.io  OutlinedTextField 	kotlin.io  OutlinedTextFieldDefaults 	kotlin.io  PackageManager 	kotlin.io  
PaddingValues 	kotlin.io  Pair 	kotlin.io  
PieceColor 	kotlin.io  PieceDetectionResult 	kotlin.io  	PieceTray 	kotlin.io  
PieceTrayItem 	kotlin.io  	PieceType 	kotlin.io  Preview 	kotlin.io  PreviewView 	kotlin.io  ProcessCameraProvider 	kotlin.io  QuickActionsSection 	kotlin.io  R 	kotlin.io  Rect 	kotlin.io  
RepeatMode 	kotlin.io  RoundedCornerShape 	kotlin.io  Row 	kotlin.io  Shadow 	kotlin.io  Size 	kotlin.io  Spacer 	kotlin.io  Spring 	kotlin.io  String 	kotlin.io  
StringBuilder 	kotlin.io  Surface 	kotlin.io  System 	kotlin.io  TAG 	kotlin.io  Text 	kotlin.io  	TextAlign 	kotlin.io  	TextStyle 	kotlin.io  Toast 	kotlin.io  Unit 	kotlin.io  Uri 	kotlin.io  
V6_MODEL_FILE 	kotlin.io  WindowCompat 	kotlin.io  WindowInsetsCompat 	kotlin.io  WindowInsetsControllerCompat 	kotlin.io  YOLO_MODEL_FILE 	kotlin.io  addListener 	kotlin.io  ai 	kotlin.io  also 	kotlin.io  android 	kotlin.io  androidx 	kotlin.io  animateFloat 	kotlin.io  animateFloatAsState 	kotlin.io  apply 	kotlin.io  arrayOf 	kotlin.io  aspectRatio 	kotlin.io  
background 	kotlin.io  captureImageSafe 	kotlin.io  cleanup 	kotlin.io  cleanupResources 	kotlin.io  	clickable 	kotlin.io  coerceIn 	kotlin.io  com 	kotlin.io  contentToString 	kotlin.io  context 	kotlin.io  copyModelSafely 	kotlin.io  copyTo 	kotlin.io  delay 	kotlin.io  
digitToInt 	kotlin.io  downTo 	kotlin.io  	emptyList 	kotlin.io  emptyMap 	kotlin.io  fadeIn 	kotlin.io  fadeOut 	kotlin.io  
fillMaxHeight 	kotlin.io  fillMaxSize 	kotlin.io  fillMaxWidth 	kotlin.io  forEach 	kotlin.io  format 	kotlin.io  generateFENFromDetections 	kotlin.io  getValue 	kotlin.io  
graphicsLayer 	kotlin.io  height 	kotlin.io  indices 	kotlin.io  infiniteRepeatable 	kotlin.io  isDigit 	kotlin.io  
isInitialized 	kotlin.io  isUpperCase 	kotlin.io  iterator 	kotlin.io  java 	kotlin.io  joinToString 	kotlin.io  kotlin 	kotlin.io  kotlinx 	kotlin.io  launch 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  loadBitmapFromUri 	kotlin.io  longArrayOf 	kotlin.io  
lowercaseChar 	kotlin.io  mapOf 	kotlin.io  maxOf 	kotlin.io  minOf 	kotlin.io  
mutableListOf 	kotlin.io  mutableMapOf 	kotlin.io  mutableStateOf 	kotlin.io  navigationBarsPadding 	kotlin.io  offset 	kotlin.io  onGloballyPositioned 	kotlin.io  onnxAI 	kotlin.io  operationCount 	kotlin.io  ortEnvironment 	kotlin.io  padding 	kotlin.io  painterResource 	kotlin.io  performanceMetrics 	kotlin.io  
plusAssign 	kotlin.io  positionInRoot 	kotlin.io  provideDelegate 	kotlin.io  remember 	kotlin.io  rememberInfiniteTransition 	kotlin.io  run 	kotlin.io  runV6SegmentationSafe 	kotlin.io  runYOLODetectionSafe 	kotlin.io  scaleIn 	kotlin.io  scaleOut 	kotlin.io  set 	kotlin.io  setValue 	kotlin.io  size 	kotlin.io  slideInHorizontally 	kotlin.io  slideInVertically 	kotlin.io  slideOutHorizontally 	kotlin.io  split 	kotlin.io  spring 	kotlin.io  statusBarsPadding 	kotlin.io  to 	kotlin.io  toMap 	kotlin.io  totalOperations 	kotlin.io  totalProcessingTime 	kotlin.io  tween 	kotlin.io  until 	kotlin.io  
uppercaseChar 	kotlin.io  use 	kotlin.io  	v6Session 	kotlin.io  width 	kotlin.io  withContext 	kotlin.io  withLock 	kotlin.io  yoloSession 	kotlin.io  ActivityResultContracts 
kotlin.jvm  	Alignment 
kotlin.jvm  AndroidView 
kotlin.jvm  AnimatedVisibility 
kotlin.jvm  Arrangement 
kotlin.jvm  Array 
kotlin.jvm  
AtomicBoolean 
kotlin.jvm  AtomicReference 
kotlin.jvm  
AutoCloseable 
kotlin.jvm  Bitmap 
kotlin.jvm  
BitmapFactory 
kotlin.jvm  BoardSegmentationResult 
kotlin.jvm  Boolean 
kotlin.jvm  Box 
kotlin.jvm  Brush 
kotlin.jvm  Build 
kotlin.jvm  Button 
kotlin.jvm  ButtonDefaults 
kotlin.jvm  CameraSelector 
kotlin.jvm  CapturedImagePreview 
kotlin.jvm  Card 
kotlin.jvm  CardDefaults 
kotlin.jvm  ChessAI 
kotlin.jvm  ChessActionCard 
kotlin.jvm  ChessAnalysisResult 
kotlin.jvm  ChessAppHeader 
kotlin.jvm  ChessBoardControls 
kotlin.jvm  ChessBoardGuide 
kotlin.jvm  ChessExpressiveAnimations 
kotlin.jvm  ChessFontFamily 
kotlin.jvm  
ChessPiece 
kotlin.jvm  
ChessPosition 
kotlin.jvm  ChessSquare 
kotlin.jvm  ChessVisionAppTheme 
kotlin.jvm  CircleShape 
kotlin.jvm  CircularProgressIndicator 
kotlin.jvm  Color 
kotlin.jvm  Column 
kotlin.jvm  
ContextCompat 
kotlin.jvm  
ControlButton 
kotlin.jvm  CornerGuide 
kotlin.jvm  
DICE_SCORE 
kotlin.jvm  Dispatchers 
kotlin.jvm  DisposableEffect 
kotlin.jvm  
EaseInBack 
kotlin.jvm  
EaseInOutSine 
kotlin.jvm  EaseOutBack 
kotlin.jvm  EaseOutCubic 
kotlin.jvm  	Exception 
kotlin.jvm  	Executors 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  
FENDisplay 
kotlin.jvm  FeatureItem 
kotlin.jvm  FeaturesSection 
kotlin.jvm  File 
kotlin.jvm  FileOutputStream 
kotlin.jvm  Float 
kotlin.jvm  
FloatArray 
kotlin.jvm  
FontFamily 
kotlin.jvm  
FontWeight 
kotlin.jvm  INFERENCE_TIME_MS 
kotlin.jvm  Icon 
kotlin.jvm  
IconButton 
kotlin.jvm  Icons 
kotlin.jvm  Image 
kotlin.jvm  ImageCapture 
kotlin.jvm  
ImageUtils 
kotlin.jvm  IntArray 
kotlin.jvm  	IntOffset 
kotlin.jvm  InteractiveChessBoard 
kotlin.jvm  
LazyColumn 
kotlin.jvm  LazyRow 
kotlin.jvm  LinearEasing 
kotlin.jvm  	LocalView 
kotlin.jvm  Locale 
kotlin.jvm  Log 
kotlin.jvm  MAP50_ACCURACY 
kotlin.jvm  MAX_PROCESSING_TIME_MS 
kotlin.jvm  MEMORY_CLEANUP_THRESHOLD 
kotlin.jvm  
MainScreen 
kotlin.jvm  Manifest 
kotlin.jvm  
MaterialTheme 
kotlin.jvm  	ModelInfo 
kotlin.jvm  ModernControlButton 
kotlin.jvm  Modifier 
kotlin.jvm  Mutex 
kotlin.jvm  Offset 
kotlin.jvm  OutlinedTextField 
kotlin.jvm  OutlinedTextFieldDefaults 
kotlin.jvm  PackageManager 
kotlin.jvm  
PaddingValues 
kotlin.jvm  Pair 
kotlin.jvm  
PieceColor 
kotlin.jvm  PieceDetectionResult 
kotlin.jvm  	PieceTray 
kotlin.jvm  
PieceTrayItem 
kotlin.jvm  	PieceType 
kotlin.jvm  Preview 
kotlin.jvm  PreviewView 
kotlin.jvm  ProcessCameraProvider 
kotlin.jvm  QuickActionsSection 
kotlin.jvm  R 
kotlin.jvm  Rect 
kotlin.jvm  
RepeatMode 
kotlin.jvm  RoundedCornerShape 
kotlin.jvm  Row 
kotlin.jvm  Shadow 
kotlin.jvm  Size 
kotlin.jvm  Spacer 
kotlin.jvm  Spring 
kotlin.jvm  String 
kotlin.jvm  
StringBuilder 
kotlin.jvm  Surface 
kotlin.jvm  System 
kotlin.jvm  TAG 
kotlin.jvm  Text 
kotlin.jvm  	TextAlign 
kotlin.jvm  	TextStyle 
kotlin.jvm  Toast 
kotlin.jvm  Unit 
kotlin.jvm  Uri 
kotlin.jvm  
V6_MODEL_FILE 
kotlin.jvm  WindowCompat 
kotlin.jvm  WindowInsetsCompat 
kotlin.jvm  WindowInsetsControllerCompat 
kotlin.jvm  YOLO_MODEL_FILE 
kotlin.jvm  addListener 
kotlin.jvm  ai 
kotlin.jvm  also 
kotlin.jvm  android 
kotlin.jvm  androidx 
kotlin.jvm  animateFloat 
kotlin.jvm  animateFloatAsState 
kotlin.jvm  apply 
kotlin.jvm  arrayOf 
kotlin.jvm  aspectRatio 
kotlin.jvm  
background 
kotlin.jvm  captureImageSafe 
kotlin.jvm  cleanup 
kotlin.jvm  cleanupResources 
kotlin.jvm  	clickable 
kotlin.jvm  coerceIn 
kotlin.jvm  com 
kotlin.jvm  contentToString 
kotlin.jvm  context 
kotlin.jvm  copyModelSafely 
kotlin.jvm  copyTo 
kotlin.jvm  delay 
kotlin.jvm  
digitToInt 
kotlin.jvm  downTo 
kotlin.jvm  	emptyList 
kotlin.jvm  emptyMap 
kotlin.jvm  fadeIn 
kotlin.jvm  fadeOut 
kotlin.jvm  
fillMaxHeight 
kotlin.jvm  fillMaxSize 
kotlin.jvm  fillMaxWidth 
kotlin.jvm  forEach 
kotlin.jvm  format 
kotlin.jvm  generateFENFromDetections 
kotlin.jvm  getValue 
kotlin.jvm  
graphicsLayer 
kotlin.jvm  height 
kotlin.jvm  indices 
kotlin.jvm  infiniteRepeatable 
kotlin.jvm  isDigit 
kotlin.jvm  
isInitialized 
kotlin.jvm  isUpperCase 
kotlin.jvm  iterator 
kotlin.jvm  java 
kotlin.jvm  joinToString 
kotlin.jvm  kotlin 
kotlin.jvm  kotlinx 
kotlin.jvm  launch 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  loadBitmapFromUri 
kotlin.jvm  longArrayOf 
kotlin.jvm  
lowercaseChar 
kotlin.jvm  mapOf 
kotlin.jvm  maxOf 
kotlin.jvm  minOf 
kotlin.jvm  
mutableListOf 
kotlin.jvm  mutableMapOf 
kotlin.jvm  mutableStateOf 
kotlin.jvm  navigationBarsPadding 
kotlin.jvm  offset 
kotlin.jvm  onGloballyPositioned 
kotlin.jvm  onnxAI 
kotlin.jvm  operationCount 
kotlin.jvm  ortEnvironment 
kotlin.jvm  padding 
kotlin.jvm  painterResource 
kotlin.jvm  performanceMetrics 
kotlin.jvm  
plusAssign 
kotlin.jvm  positionInRoot 
kotlin.jvm  provideDelegate 
kotlin.jvm  remember 
kotlin.jvm  rememberInfiniteTransition 
kotlin.jvm  run 
kotlin.jvm  runV6SegmentationSafe 
kotlin.jvm  runYOLODetectionSafe 
kotlin.jvm  scaleIn 
kotlin.jvm  scaleOut 
kotlin.jvm  set 
kotlin.jvm  setValue 
kotlin.jvm  size 
kotlin.jvm  slideInHorizontally 
kotlin.jvm  slideInVertically 
kotlin.jvm  slideOutHorizontally 
kotlin.jvm  split 
kotlin.jvm  spring 
kotlin.jvm  statusBarsPadding 
kotlin.jvm  to 
kotlin.jvm  toMap 
kotlin.jvm  totalOperations 
kotlin.jvm  totalProcessingTime 
kotlin.jvm  tween 
kotlin.jvm  until 
kotlin.jvm  
uppercaseChar 
kotlin.jvm  use 
kotlin.jvm  	v6Session 
kotlin.jvm  width 
kotlin.jvm  withContext 
kotlin.jvm  withLock 
kotlin.jvm  yoloSession 
kotlin.jvm  exp kotlin.math  sin kotlin.math  ActivityResultContracts 
kotlin.ranges  	Alignment 
kotlin.ranges  AndroidView 
kotlin.ranges  AnimatedVisibility 
kotlin.ranges  Arrangement 
kotlin.ranges  Array 
kotlin.ranges  
AtomicBoolean 
kotlin.ranges  AtomicReference 
kotlin.ranges  
AutoCloseable 
kotlin.ranges  Bitmap 
kotlin.ranges  
BitmapFactory 
kotlin.ranges  BoardSegmentationResult 
kotlin.ranges  Boolean 
kotlin.ranges  Box 
kotlin.ranges  Brush 
kotlin.ranges  Build 
kotlin.ranges  Button 
kotlin.ranges  ButtonDefaults 
kotlin.ranges  CameraSelector 
kotlin.ranges  CapturedImagePreview 
kotlin.ranges  Card 
kotlin.ranges  CardDefaults 
kotlin.ranges  ChessAI 
kotlin.ranges  ChessActionCard 
kotlin.ranges  ChessAnalysisResult 
kotlin.ranges  ChessAppHeader 
kotlin.ranges  ChessBoardControls 
kotlin.ranges  ChessBoardGuide 
kotlin.ranges  ChessExpressiveAnimations 
kotlin.ranges  ChessFontFamily 
kotlin.ranges  
ChessPiece 
kotlin.ranges  
ChessPosition 
kotlin.ranges  ChessSquare 
kotlin.ranges  ChessVisionAppTheme 
kotlin.ranges  CircleShape 
kotlin.ranges  CircularProgressIndicator 
kotlin.ranges  Color 
kotlin.ranges  Column 
kotlin.ranges  
ContextCompat 
kotlin.ranges  
ControlButton 
kotlin.ranges  CornerGuide 
kotlin.ranges  
DICE_SCORE 
kotlin.ranges  Dispatchers 
kotlin.ranges  DisposableEffect 
kotlin.ranges  
EaseInBack 
kotlin.ranges  
EaseInOutSine 
kotlin.ranges  EaseOutBack 
kotlin.ranges  EaseOutCubic 
kotlin.ranges  	Exception 
kotlin.ranges  	Executors 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  
FENDisplay 
kotlin.ranges  FeatureItem 
kotlin.ranges  FeaturesSection 
kotlin.ranges  File 
kotlin.ranges  FileOutputStream 
kotlin.ranges  Float 
kotlin.ranges  
FloatArray 
kotlin.ranges  
FontFamily 
kotlin.ranges  
FontWeight 
kotlin.ranges  INFERENCE_TIME_MS 
kotlin.ranges  Icon 
kotlin.ranges  
IconButton 
kotlin.ranges  Icons 
kotlin.ranges  Image 
kotlin.ranges  ImageCapture 
kotlin.ranges  
ImageUtils 
kotlin.ranges  IntArray 
kotlin.ranges  	IntOffset 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  InteractiveChessBoard 
kotlin.ranges  
LazyColumn 
kotlin.ranges  LazyRow 
kotlin.ranges  LinearEasing 
kotlin.ranges  	LocalView 
kotlin.ranges  Locale 
kotlin.ranges  Log 
kotlin.ranges  MAP50_ACCURACY 
kotlin.ranges  MAX_PROCESSING_TIME_MS 
kotlin.ranges  MEMORY_CLEANUP_THRESHOLD 
kotlin.ranges  
MainScreen 
kotlin.ranges  Manifest 
kotlin.ranges  
MaterialTheme 
kotlin.ranges  	ModelInfo 
kotlin.ranges  ModernControlButton 
kotlin.ranges  Modifier 
kotlin.ranges  Mutex 
kotlin.ranges  Offset 
kotlin.ranges  OutlinedTextField 
kotlin.ranges  OutlinedTextFieldDefaults 
kotlin.ranges  PackageManager 
kotlin.ranges  
PaddingValues 
kotlin.ranges  Pair 
kotlin.ranges  
PieceColor 
kotlin.ranges  PieceDetectionResult 
kotlin.ranges  	PieceTray 
kotlin.ranges  
PieceTrayItem 
kotlin.ranges  	PieceType 
kotlin.ranges  Preview 
kotlin.ranges  PreviewView 
kotlin.ranges  ProcessCameraProvider 
kotlin.ranges  QuickActionsSection 
kotlin.ranges  R 
kotlin.ranges  Rect 
kotlin.ranges  
RepeatMode 
kotlin.ranges  RoundedCornerShape 
kotlin.ranges  Row 
kotlin.ranges  Shadow 
kotlin.ranges  Size 
kotlin.ranges  Spacer 
kotlin.ranges  Spring 
kotlin.ranges  String 
kotlin.ranges  
StringBuilder 
kotlin.ranges  Surface 
kotlin.ranges  System 
kotlin.ranges  TAG 
kotlin.ranges  Text 
kotlin.ranges  	TextAlign 
kotlin.ranges  	TextStyle 
kotlin.ranges  Toast 
kotlin.ranges  Unit 
kotlin.ranges  Uri 
kotlin.ranges  
V6_MODEL_FILE 
kotlin.ranges  WindowCompat 
kotlin.ranges  WindowInsetsCompat 
kotlin.ranges  WindowInsetsControllerCompat 
kotlin.ranges  YOLO_MODEL_FILE 
kotlin.ranges  addListener 
kotlin.ranges  ai 
kotlin.ranges  also 
kotlin.ranges  android 
kotlin.ranges  androidx 
kotlin.ranges  animateFloat 
kotlin.ranges  animateFloatAsState 
kotlin.ranges  apply 
kotlin.ranges  arrayOf 
kotlin.ranges  aspectRatio 
kotlin.ranges  
background 
kotlin.ranges  captureImageSafe 
kotlin.ranges  cleanup 
kotlin.ranges  cleanupResources 
kotlin.ranges  	clickable 
kotlin.ranges  coerceIn 
kotlin.ranges  com 
kotlin.ranges  contentToString 
kotlin.ranges  context 
kotlin.ranges  copyModelSafely 
kotlin.ranges  copyTo 
kotlin.ranges  delay 
kotlin.ranges  
digitToInt 
kotlin.ranges  downTo 
kotlin.ranges  	emptyList 
kotlin.ranges  emptyMap 
kotlin.ranges  fadeIn 
kotlin.ranges  fadeOut 
kotlin.ranges  
fillMaxHeight 
kotlin.ranges  fillMaxSize 
kotlin.ranges  fillMaxWidth 
kotlin.ranges  forEach 
kotlin.ranges  format 
kotlin.ranges  generateFENFromDetections 
kotlin.ranges  getValue 
kotlin.ranges  
graphicsLayer 
kotlin.ranges  height 
kotlin.ranges  indices 
kotlin.ranges  infiniteRepeatable 
kotlin.ranges  isDigit 
kotlin.ranges  
isInitialized 
kotlin.ranges  isUpperCase 
kotlin.ranges  iterator 
kotlin.ranges  java 
kotlin.ranges  joinToString 
kotlin.ranges  kotlin 
kotlin.ranges  kotlinx 
kotlin.ranges  launch 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  loadBitmapFromUri 
kotlin.ranges  longArrayOf 
kotlin.ranges  
lowercaseChar 
kotlin.ranges  mapOf 
kotlin.ranges  maxOf 
kotlin.ranges  minOf 
kotlin.ranges  
mutableListOf 
kotlin.ranges  mutableMapOf 
kotlin.ranges  mutableStateOf 
kotlin.ranges  navigationBarsPadding 
kotlin.ranges  offset 
kotlin.ranges  onGloballyPositioned 
kotlin.ranges  onnxAI 
kotlin.ranges  operationCount 
kotlin.ranges  ortEnvironment 
kotlin.ranges  padding 
kotlin.ranges  painterResource 
kotlin.ranges  performanceMetrics 
kotlin.ranges  
plusAssign 
kotlin.ranges  positionInRoot 
kotlin.ranges  provideDelegate 
kotlin.ranges  remember 
kotlin.ranges  rememberInfiniteTransition 
kotlin.ranges  run 
kotlin.ranges  runV6SegmentationSafe 
kotlin.ranges  runYOLODetectionSafe 
kotlin.ranges  scaleIn 
kotlin.ranges  scaleOut 
kotlin.ranges  set 
kotlin.ranges  setValue 
kotlin.ranges  size 
kotlin.ranges  slideInHorizontally 
kotlin.ranges  slideInVertically 
kotlin.ranges  slideOutHorizontally 
kotlin.ranges  split 
kotlin.ranges  spring 
kotlin.ranges  statusBarsPadding 
kotlin.ranges  to 
kotlin.ranges  toMap 
kotlin.ranges  totalOperations 
kotlin.ranges  totalProcessingTime 
kotlin.ranges  tween 
kotlin.ranges  until 
kotlin.ranges  
uppercaseChar 
kotlin.ranges  use 
kotlin.ranges  	v6Session 
kotlin.ranges  width 
kotlin.ranges  withContext 
kotlin.ranges  withLock 
kotlin.ranges  yoloSession 
kotlin.ranges  contains kotlin.ranges.IntProgression  iterator kotlin.ranges.IntProgression  contains kotlin.ranges.IntRange  iterator kotlin.ranges.IntRange  KClass kotlin.reflect  getJAVA kotlin.reflect.KClass  getJava kotlin.reflect.KClass  java kotlin.reflect.KClass  ActivityResultContracts kotlin.sequences  	Alignment kotlin.sequences  AndroidView kotlin.sequences  AnimatedVisibility kotlin.sequences  Arrangement kotlin.sequences  Array kotlin.sequences  
AtomicBoolean kotlin.sequences  AtomicReference kotlin.sequences  
AutoCloseable kotlin.sequences  Bitmap kotlin.sequences  
BitmapFactory kotlin.sequences  BoardSegmentationResult kotlin.sequences  Boolean kotlin.sequences  Box kotlin.sequences  Brush kotlin.sequences  Build kotlin.sequences  Button kotlin.sequences  ButtonDefaults kotlin.sequences  CameraSelector kotlin.sequences  CapturedImagePreview kotlin.sequences  Card kotlin.sequences  CardDefaults kotlin.sequences  ChessAI kotlin.sequences  ChessActionCard kotlin.sequences  ChessAnalysisResult kotlin.sequences  ChessAppHeader kotlin.sequences  ChessBoardControls kotlin.sequences  ChessBoardGuide kotlin.sequences  ChessExpressiveAnimations kotlin.sequences  ChessFontFamily kotlin.sequences  
ChessPiece kotlin.sequences  
ChessPosition kotlin.sequences  ChessSquare kotlin.sequences  ChessVisionAppTheme kotlin.sequences  CircleShape kotlin.sequences  CircularProgressIndicator kotlin.sequences  Color kotlin.sequences  Column kotlin.sequences  
ContextCompat kotlin.sequences  
ControlButton kotlin.sequences  CornerGuide kotlin.sequences  
DICE_SCORE kotlin.sequences  Dispatchers kotlin.sequences  DisposableEffect kotlin.sequences  
EaseInBack kotlin.sequences  
EaseInOutSine kotlin.sequences  EaseOutBack kotlin.sequences  EaseOutCubic kotlin.sequences  	Exception kotlin.sequences  	Executors kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  
FENDisplay kotlin.sequences  FeatureItem kotlin.sequences  FeaturesSection kotlin.sequences  File kotlin.sequences  FileOutputStream kotlin.sequences  Float kotlin.sequences  
FloatArray kotlin.sequences  
FontFamily kotlin.sequences  
FontWeight kotlin.sequences  INFERENCE_TIME_MS kotlin.sequences  Icon kotlin.sequences  
IconButton kotlin.sequences  Icons kotlin.sequences  Image kotlin.sequences  ImageCapture kotlin.sequences  
ImageUtils kotlin.sequences  IntArray kotlin.sequences  	IntOffset kotlin.sequences  InteractiveChessBoard kotlin.sequences  
LazyColumn kotlin.sequences  LazyRow kotlin.sequences  LinearEasing kotlin.sequences  	LocalView kotlin.sequences  Locale kotlin.sequences  Log kotlin.sequences  MAP50_ACCURACY kotlin.sequences  MAX_PROCESSING_TIME_MS kotlin.sequences  MEMORY_CLEANUP_THRESHOLD kotlin.sequences  
MainScreen kotlin.sequences  Manifest kotlin.sequences  
MaterialTheme kotlin.sequences  	ModelInfo kotlin.sequences  ModernControlButton kotlin.sequences  Modifier kotlin.sequences  Mutex kotlin.sequences  Offset kotlin.sequences  OutlinedTextField kotlin.sequences  OutlinedTextFieldDefaults kotlin.sequences  PackageManager kotlin.sequences  
PaddingValues kotlin.sequences  Pair kotlin.sequences  
PieceColor kotlin.sequences  PieceDetectionResult kotlin.sequences  	PieceTray kotlin.sequences  
PieceTrayItem kotlin.sequences  	PieceType kotlin.sequences  Preview kotlin.sequences  PreviewView kotlin.sequences  ProcessCameraProvider kotlin.sequences  QuickActionsSection kotlin.sequences  R kotlin.sequences  Rect kotlin.sequences  
RepeatMode kotlin.sequences  RoundedCornerShape kotlin.sequences  Row kotlin.sequences  Shadow kotlin.sequences  Size kotlin.sequences  Spacer kotlin.sequences  Spring kotlin.sequences  String kotlin.sequences  
StringBuilder kotlin.sequences  Surface kotlin.sequences  System kotlin.sequences  TAG kotlin.sequences  Text kotlin.sequences  	TextAlign kotlin.sequences  	TextStyle kotlin.sequences  Toast kotlin.sequences  Unit kotlin.sequences  Uri kotlin.sequences  
V6_MODEL_FILE kotlin.sequences  WindowCompat kotlin.sequences  WindowInsetsCompat kotlin.sequences  WindowInsetsControllerCompat kotlin.sequences  YOLO_MODEL_FILE kotlin.sequences  addListener kotlin.sequences  ai kotlin.sequences  also kotlin.sequences  android kotlin.sequences  androidx kotlin.sequences  animateFloat kotlin.sequences  animateFloatAsState kotlin.sequences  apply kotlin.sequences  arrayOf kotlin.sequences  aspectRatio kotlin.sequences  
background kotlin.sequences  captureImageSafe kotlin.sequences  cleanup kotlin.sequences  cleanupResources kotlin.sequences  	clickable kotlin.sequences  coerceIn kotlin.sequences  com kotlin.sequences  contentToString kotlin.sequences  context kotlin.sequences  copyModelSafely kotlin.sequences  copyTo kotlin.sequences  delay kotlin.sequences  
digitToInt kotlin.sequences  downTo kotlin.sequences  	emptyList kotlin.sequences  emptyMap kotlin.sequences  fadeIn kotlin.sequences  fadeOut kotlin.sequences  
fillMaxHeight kotlin.sequences  fillMaxSize kotlin.sequences  fillMaxWidth kotlin.sequences  forEach kotlin.sequences  format kotlin.sequences  generateFENFromDetections kotlin.sequences  getValue kotlin.sequences  
graphicsLayer kotlin.sequences  height kotlin.sequences  indices kotlin.sequences  infiniteRepeatable kotlin.sequences  isDigit kotlin.sequences  
isInitialized kotlin.sequences  isUpperCase kotlin.sequences  iterator kotlin.sequences  java kotlin.sequences  joinToString kotlin.sequences  kotlin kotlin.sequences  kotlinx kotlin.sequences  launch kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  loadBitmapFromUri kotlin.sequences  longArrayOf kotlin.sequences  
lowercaseChar kotlin.sequences  mapOf kotlin.sequences  maxOf kotlin.sequences  minOf kotlin.sequences  
mutableListOf kotlin.sequences  mutableMapOf kotlin.sequences  mutableStateOf kotlin.sequences  navigationBarsPadding kotlin.sequences  offset kotlin.sequences  onGloballyPositioned kotlin.sequences  onnxAI kotlin.sequences  operationCount kotlin.sequences  ortEnvironment kotlin.sequences  padding kotlin.sequences  painterResource kotlin.sequences  performanceMetrics kotlin.sequences  
plusAssign kotlin.sequences  positionInRoot kotlin.sequences  provideDelegate kotlin.sequences  remember kotlin.sequences  rememberInfiniteTransition kotlin.sequences  run kotlin.sequences  runV6SegmentationSafe kotlin.sequences  runYOLODetectionSafe kotlin.sequences  scaleIn kotlin.sequences  scaleOut kotlin.sequences  set kotlin.sequences  setValue kotlin.sequences  size kotlin.sequences  slideInHorizontally kotlin.sequences  slideInVertically kotlin.sequences  slideOutHorizontally kotlin.sequences  split kotlin.sequences  spring kotlin.sequences  statusBarsPadding kotlin.sequences  to kotlin.sequences  toMap kotlin.sequences  totalOperations kotlin.sequences  totalProcessingTime kotlin.sequences  tween kotlin.sequences  until kotlin.sequences  
uppercaseChar kotlin.sequences  use kotlin.sequences  	v6Session kotlin.sequences  width kotlin.sequences  withContext kotlin.sequences  withLock kotlin.sequences  yoloSession kotlin.sequences  ActivityResultContracts kotlin.text  	Alignment kotlin.text  AndroidView kotlin.text  AnimatedVisibility kotlin.text  Arrangement kotlin.text  Array kotlin.text  
AtomicBoolean kotlin.text  AtomicReference kotlin.text  
AutoCloseable kotlin.text  Bitmap kotlin.text  
BitmapFactory kotlin.text  BoardSegmentationResult kotlin.text  Boolean kotlin.text  Box kotlin.text  Brush kotlin.text  Build kotlin.text  Button kotlin.text  ButtonDefaults kotlin.text  CameraSelector kotlin.text  CapturedImagePreview kotlin.text  Card kotlin.text  CardDefaults kotlin.text  ChessAI kotlin.text  ChessActionCard kotlin.text  ChessAnalysisResult kotlin.text  ChessAppHeader kotlin.text  ChessBoardControls kotlin.text  ChessBoardGuide kotlin.text  ChessExpressiveAnimations kotlin.text  ChessFontFamily kotlin.text  
ChessPiece kotlin.text  
ChessPosition kotlin.text  ChessSquare kotlin.text  ChessVisionAppTheme kotlin.text  CircleShape kotlin.text  CircularProgressIndicator kotlin.text  Color kotlin.text  Column kotlin.text  
ContextCompat kotlin.text  
ControlButton kotlin.text  CornerGuide kotlin.text  
DICE_SCORE kotlin.text  Dispatchers kotlin.text  DisposableEffect kotlin.text  
EaseInBack kotlin.text  
EaseInOutSine kotlin.text  EaseOutBack kotlin.text  EaseOutCubic kotlin.text  	Exception kotlin.text  	Executors kotlin.text  ExperimentalMaterial3Api kotlin.text  
FENDisplay kotlin.text  FeatureItem kotlin.text  FeaturesSection kotlin.text  File kotlin.text  FileOutputStream kotlin.text  Float kotlin.text  
FloatArray kotlin.text  
FontFamily kotlin.text  
FontWeight kotlin.text  INFERENCE_TIME_MS kotlin.text  Icon kotlin.text  
IconButton kotlin.text  Icons kotlin.text  Image kotlin.text  ImageCapture kotlin.text  
ImageUtils kotlin.text  IntArray kotlin.text  	IntOffset kotlin.text  InteractiveChessBoard kotlin.text  
LazyColumn kotlin.text  LazyRow kotlin.text  LinearEasing kotlin.text  	LocalView kotlin.text  Locale kotlin.text  Log kotlin.text  MAP50_ACCURACY kotlin.text  MAX_PROCESSING_TIME_MS kotlin.text  MEMORY_CLEANUP_THRESHOLD kotlin.text  
MainScreen kotlin.text  Manifest kotlin.text  
MaterialTheme kotlin.text  	ModelInfo kotlin.text  ModernControlButton kotlin.text  Modifier kotlin.text  Mutex kotlin.text  Offset kotlin.text  OutlinedTextField kotlin.text  OutlinedTextFieldDefaults kotlin.text  PackageManager kotlin.text  
PaddingValues kotlin.text  Pair kotlin.text  
PieceColor kotlin.text  PieceDetectionResult kotlin.text  	PieceTray kotlin.text  
PieceTrayItem kotlin.text  	PieceType kotlin.text  Preview kotlin.text  PreviewView kotlin.text  ProcessCameraProvider kotlin.text  QuickActionsSection kotlin.text  R kotlin.text  Rect kotlin.text  
RepeatMode kotlin.text  RoundedCornerShape kotlin.text  Row kotlin.text  Shadow kotlin.text  Size kotlin.text  Spacer kotlin.text  Spring kotlin.text  String kotlin.text  
StringBuilder kotlin.text  Surface kotlin.text  System kotlin.text  TAG kotlin.text  Text kotlin.text  	TextAlign kotlin.text  	TextStyle kotlin.text  Toast kotlin.text  Unit kotlin.text  Uri kotlin.text  
V6_MODEL_FILE kotlin.text  WindowCompat kotlin.text  WindowInsetsCompat kotlin.text  WindowInsetsControllerCompat kotlin.text  YOLO_MODEL_FILE kotlin.text  addListener kotlin.text  ai kotlin.text  also kotlin.text  android kotlin.text  androidx kotlin.text  animateFloat kotlin.text  animateFloatAsState kotlin.text  apply kotlin.text  arrayOf kotlin.text  aspectRatio kotlin.text  
background kotlin.text  captureImageSafe kotlin.text  cleanup kotlin.text  cleanupResources kotlin.text  	clickable kotlin.text  coerceIn kotlin.text  com kotlin.text  contentToString kotlin.text  context kotlin.text  copyModelSafely kotlin.text  copyTo kotlin.text  delay kotlin.text  
digitToInt kotlin.text  downTo kotlin.text  	emptyList kotlin.text  emptyMap kotlin.text  fadeIn kotlin.text  fadeOut kotlin.text  
fillMaxHeight kotlin.text  fillMaxSize kotlin.text  fillMaxWidth kotlin.text  forEach kotlin.text  format kotlin.text  generateFENFromDetections kotlin.text  getValue kotlin.text  
graphicsLayer kotlin.text  height kotlin.text  indices kotlin.text  infiniteRepeatable kotlin.text  isDigit kotlin.text  
isInitialized kotlin.text  isUpperCase kotlin.text  iterator kotlin.text  java kotlin.text  joinToString kotlin.text  kotlin kotlin.text  kotlinx kotlin.text  launch kotlin.text  let kotlin.text  listOf kotlin.text  loadBitmapFromUri kotlin.text  longArrayOf kotlin.text  
lowercaseChar kotlin.text  mapOf kotlin.text  maxOf kotlin.text  minOf kotlin.text  
mutableListOf kotlin.text  mutableMapOf kotlin.text  mutableStateOf kotlin.text  navigationBarsPadding kotlin.text  offset kotlin.text  onGloballyPositioned kotlin.text  onnxAI kotlin.text  operationCount kotlin.text  ortEnvironment kotlin.text  padding kotlin.text  painterResource kotlin.text  performanceMetrics kotlin.text  
plusAssign kotlin.text  positionInRoot kotlin.text  provideDelegate kotlin.text  remember kotlin.text  rememberInfiniteTransition kotlin.text  run kotlin.text  runV6SegmentationSafe kotlin.text  runYOLODetectionSafe kotlin.text  scaleIn kotlin.text  scaleOut kotlin.text  set kotlin.text  setValue kotlin.text  size kotlin.text  slideInHorizontally kotlin.text  slideInVertically kotlin.text  slideOutHorizontally kotlin.text  split kotlin.text  spring kotlin.text  statusBarsPadding kotlin.text  to kotlin.text  toMap kotlin.text  totalOperations kotlin.text  totalProcessingTime kotlin.text  tween kotlin.text  until kotlin.text  
uppercaseChar kotlin.text  use kotlin.text  	v6Session kotlin.text  width kotlin.text  withContext kotlin.text  withLock kotlin.text  yoloSession kotlin.text  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  Boolean !kotlinx.coroutines.CoroutineScope  ChessAnalysisResult !kotlinx.coroutines.CoroutineScope  
DICE_SCORE !kotlinx.coroutines.CoroutineScope  	Exception !kotlinx.coroutines.CoroutineScope  File !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  MAP50_ACCURACY !kotlinx.coroutines.CoroutineScope  MAX_PROCESSING_TIME_MS !kotlinx.coroutines.CoroutineScope  MEMORY_CLEANUP_THRESHOLD !kotlinx.coroutines.CoroutineScope  System !kotlinx.coroutines.CoroutineScope  TAG !kotlinx.coroutines.CoroutineScope  
V6_MODEL_FILE !kotlinx.coroutines.CoroutineScope  YOLO_MODEL_FILE !kotlinx.coroutines.CoroutineScope  ai !kotlinx.coroutines.CoroutineScope  android !kotlinx.coroutines.CoroutineScope  apply !kotlinx.coroutines.CoroutineScope  cleanup !kotlinx.coroutines.CoroutineScope  cleanupResources !kotlinx.coroutines.CoroutineScope  context !kotlinx.coroutines.CoroutineScope  copyModelSafely !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  generateFENFromDetections !kotlinx.coroutines.CoroutineScope  getAI !kotlinx.coroutines.CoroutineScope  
getANDROID !kotlinx.coroutines.CoroutineScope  getAPPLY !kotlinx.coroutines.CoroutineScope  getAi !kotlinx.coroutines.CoroutineScope  
getAndroid !kotlinx.coroutines.CoroutineScope  getApply !kotlinx.coroutines.CoroutineScope  
getCLEANUP !kotlinx.coroutines.CoroutineScope  getCLEANUPResources !kotlinx.coroutines.CoroutineScope  
getCONTEXT !kotlinx.coroutines.CoroutineScope  getCOPYModelSafely !kotlinx.coroutines.CoroutineScope  
getCleanup !kotlinx.coroutines.CoroutineScope  getCleanupResources !kotlinx.coroutines.CoroutineScope  
getContext !kotlinx.coroutines.CoroutineScope  getCopyModelSafely !kotlinx.coroutines.CoroutineScope  getDELAY !kotlinx.coroutines.CoroutineScope  getDelay !kotlinx.coroutines.CoroutineScope  getGenerateFENFromDetections !kotlinx.coroutines.CoroutineScope  getISInitialized !kotlinx.coroutines.CoroutineScope  getIsInitialized !kotlinx.coroutines.CoroutineScope  
getKOTLINX !kotlinx.coroutines.CoroutineScope  
getKotlinx !kotlinx.coroutines.CoroutineScope  	getLAUNCH !kotlinx.coroutines.CoroutineScope  getLET !kotlinx.coroutines.CoroutineScope  getLOADBitmapFromUri !kotlinx.coroutines.CoroutineScope  	getLaunch !kotlinx.coroutines.CoroutineScope  getLet !kotlinx.coroutines.CoroutineScope  getLoadBitmapFromUri !kotlinx.coroutines.CoroutineScope  getMUTABLEListOf !kotlinx.coroutines.CoroutineScope  getMutableListOf !kotlinx.coroutines.CoroutineScope  getOPERATIONCount !kotlinx.coroutines.CoroutineScope  getORTEnvironment !kotlinx.coroutines.CoroutineScope  	getOnnxAI !kotlinx.coroutines.CoroutineScope  getOperationCount !kotlinx.coroutines.CoroutineScope  getOrtEnvironment !kotlinx.coroutines.CoroutineScope  getPERFORMANCEMetrics !kotlinx.coroutines.CoroutineScope  
getPLUSAssign !kotlinx.coroutines.CoroutineScope  getPerformanceMetrics !kotlinx.coroutines.CoroutineScope  
getPlusAssign !kotlinx.coroutines.CoroutineScope  getRUNV6SegmentationSafe !kotlinx.coroutines.CoroutineScope  getRunV6SegmentationSafe !kotlinx.coroutines.CoroutineScope  getRunYOLODetectionSafe !kotlinx.coroutines.CoroutineScope  getSET !kotlinx.coroutines.CoroutineScope  getSet !kotlinx.coroutines.CoroutineScope  getTOTALOperations !kotlinx.coroutines.CoroutineScope  getTOTALProcessingTime !kotlinx.coroutines.CoroutineScope  getTotalOperations !kotlinx.coroutines.CoroutineScope  getTotalProcessingTime !kotlinx.coroutines.CoroutineScope  getV6Session !kotlinx.coroutines.CoroutineScope  getYOLOSession !kotlinx.coroutines.CoroutineScope  getYoloSession !kotlinx.coroutines.CoroutineScope  
isInitialized !kotlinx.coroutines.CoroutineScope  java !kotlinx.coroutines.CoroutineScope  kotlinx !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  loadBitmapFromUri !kotlinx.coroutines.CoroutineScope  
mutableListOf !kotlinx.coroutines.CoroutineScope  onnxAI !kotlinx.coroutines.CoroutineScope  operationCount !kotlinx.coroutines.CoroutineScope  ortEnvironment !kotlinx.coroutines.CoroutineScope  performanceMetrics !kotlinx.coroutines.CoroutineScope  
plusAssign !kotlinx.coroutines.CoroutineScope  runV6SegmentationSafe !kotlinx.coroutines.CoroutineScope  runYOLODetectionSafe !kotlinx.coroutines.CoroutineScope  set !kotlinx.coroutines.CoroutineScope  totalOperations !kotlinx.coroutines.CoroutineScope  totalProcessingTime !kotlinx.coroutines.CoroutineScope  	v6Session !kotlinx.coroutines.CoroutineScope  yoloSession !kotlinx.coroutines.CoroutineScope  IO kotlinx.coroutines.Dispatchers  Mutex kotlinx.coroutines.sync  withLock kotlinx.coroutines.sync  getWITHLock kotlinx.coroutines.sync.Mutex  getWithLock kotlinx.coroutines.sync.Mutex  withLock kotlinx.coroutines.sync.Mutex  WINDOW_SERVICE android.content.Context  getSystemService android.content.Context  
WindowManager android.view  getROTATION android.view.Display  getRotation android.view.Display  rotation android.view.Display  setRotation android.view.Display  defaultDisplay android.view.WindowManager  getDEFAULTDisplay android.view.WindowManager  getDefaultDisplay android.view.WindowManager  setDefaultDisplay android.view.WindowManager  Context androidx.camera.core  setTargetRotation )androidx.camera.core.ImageCapture.Builder  Context "androidx.compose.foundation.layout  Context &androidx.compose.material.icons.filled  Context androidx.compose.material3  Context androidx.compose.runtime  Context com.chessvision.app  
WindowManager &com.chessvision.app.CameraStateManager  getRUN &com.chessvision.app.CameraStateManager  getRun &com.chessvision.app.CameraStateManager  run &com.chessvision.app.CameraStateManager  
WindowManager 0com.chessvision.app.CameraStateManager.Companion  run 0com.chessvision.app.CameraStateManager.Companion  Context 	java.lang  Context 	java.util  Context kotlin  Context kotlin.annotation  Context kotlin.collections  Context kotlin.comparisons  Context 	kotlin.io  Context 
kotlin.jvm  Context 
kotlin.ranges  Context kotlin.sequences  Context kotlin.text  display android.content.Context  
getDISPLAY android.content.Context  
getDisplay android.content.Context  
setDisplay android.content.Context  R android.os.Build.VERSION_CODES  Surface android.view  
ROTATION_0 android.view.Surface  Build androidx.camera.core  Build "androidx.compose.foundation.layout  Build &androidx.compose.material.icons.filled  Build androidx.compose.material3  Build androidx.compose.runtime  Build com.chessvision.app  Build &com.chessvision.app.CameraStateManager  android &com.chessvision.app.CameraStateManager  
getANDROID &com.chessvision.app.CameraStateManager  
getAndroid &com.chessvision.app.CameraStateManager  Build 0com.chessvision.app.CameraStateManager.Companion  android 0com.chessvision.app.CameraStateManager.Companion  
getANDROID 0com.chessvision.app.CameraStateManager.Companion  
getAndroid 0com.chessvision.app.CameraStateManager.Companion  Build 	java.util  OptimizationLevel com.chessvision.app.ai  HardwareProfile com.chessvision.app.ai  OptimizationLevel (ai.onnxruntime.OrtSession.SessionOptions  getHARDWAREProfile (ai.onnxruntime.OrtSession.SessionOptions  getHardwareProfile (ai.onnxruntime.OrtSession.SessionOptions  getMINOf (ai.onnxruntime.OrtSession.SessionOptions  getMinOf (ai.onnxruntime.OrtSession.SessionOptions  hardwareProfile (ai.onnxruntime.OrtSession.SessionOptions  minOf (ai.onnxruntime.OrtSession.SessionOptions  	BASIC_OPT 1ai.onnxruntime.OrtSession.SessionOptions.OptLevel  ActivityManager android.app  
MemoryInfo android.app.ActivityManager  getISLowRamDevice android.app.ActivityManager  getIsLowRamDevice android.app.ActivityManager  
getMemoryInfo android.app.ActivityManager  isLowRamDevice android.app.ActivityManager  setLowRamDevice android.app.ActivityManager  totalMem &android.app.ActivityManager.MemoryInfo  ACTIVITY_SERVICE android.content.Context  BOARD android.os.Build  HARDWARE android.os.Build  MANUFACTURER android.os.Build  MODEL android.os.Build  SOC_MANUFACTURER android.os.Build  	SOC_MODEL android.os.Build  ActivityManager com.chessvision.app.ai  Build com.chessvision.app.ai  Context com.chessvision.app.ai  Runtime com.chessvision.app.ai  contains com.chessvision.app.ai  getValue com.chessvision.app.ai  hardwareProfile com.chessvision.app.ai  lazy com.chessvision.app.ai  provideDelegate com.chessvision.app.ai  Boolean &com.chessvision.app.ai.HardwareProfile  Int &com.chessvision.app.ai.HardwareProfile  Long &com.chessvision.app.ai.HardwareProfile  OptimizationLevel &com.chessvision.app.ai.HardwareProfile  cpuCores &com.chessvision.app.ai.HardwareProfile  isLowMemory &com.chessvision.app.ai.HardwareProfile  isPowerVRGE8320 &com.chessvision.app.ai.HardwareProfile  optimizationLevel &com.chessvision.app.ai.HardwareProfile  ActivityManager "com.chessvision.app.ai.ONNXChessAI  Build "com.chessvision.app.ai.ONNXChessAI  HardwareProfile "com.chessvision.app.ai.ONNXChessAI  OptimizationLevel "com.chessvision.app.ai.ONNXChessAI  Runtime "com.chessvision.app.ai.ONNXChessAI  contains "com.chessvision.app.ai.ONNXChessAI  detectHardwareProfile "com.chessvision.app.ai.ONNXChessAI  getCONTAINS "com.chessvision.app.ai.ONNXChessAI  getContains "com.chessvision.app.ai.ONNXChessAI  getGETValue "com.chessvision.app.ai.ONNXChessAI  getGetValue "com.chessvision.app.ai.ONNXChessAI  getLAZY "com.chessvision.app.ai.ONNXChessAI  getLazy "com.chessvision.app.ai.ONNXChessAI  getPROVIDEDelegate "com.chessvision.app.ai.ONNXChessAI  getProvideDelegate "com.chessvision.app.ai.ONNXChessAI  getValue "com.chessvision.app.ai.ONNXChessAI  hardwareProfile "com.chessvision.app.ai.ONNXChessAI  lazy "com.chessvision.app.ai.ONNXChessAI  provideDelegate "com.chessvision.app.ai.ONNXChessAI  ActivityManager ,com.chessvision.app.ai.ONNXChessAI.Companion  Build ,com.chessvision.app.ai.ONNXChessAI.Companion  HardwareProfile ,com.chessvision.app.ai.ONNXChessAI.Companion  OptimizationLevel ,com.chessvision.app.ai.ONNXChessAI.Companion  Runtime ,com.chessvision.app.ai.ONNXChessAI.Companion  contains ,com.chessvision.app.ai.ONNXChessAI.Companion  getCONTAINS ,com.chessvision.app.ai.ONNXChessAI.Companion  getContains ,com.chessvision.app.ai.ONNXChessAI.Companion  getGETValue ,com.chessvision.app.ai.ONNXChessAI.Companion  getGetValue ,com.chessvision.app.ai.ONNXChessAI.Companion  getLAZY ,com.chessvision.app.ai.ONNXChessAI.Companion  getLazy ,com.chessvision.app.ai.ONNXChessAI.Companion  getPROVIDEDelegate ,com.chessvision.app.ai.ONNXChessAI.Companion  getProvideDelegate ,com.chessvision.app.ai.ONNXChessAI.Companion  getValue ,com.chessvision.app.ai.ONNXChessAI.Companion  hardwareProfile ,com.chessvision.app.ai.ONNXChessAI.Companion  lazy ,com.chessvision.app.ai.ONNXChessAI.Companion  provideDelegate ,com.chessvision.app.ai.ONNXChessAI.Companion  BALANCED (com.chessvision.app.ai.OptimizationLevel  MEMORY_OPTIMIZED (com.chessvision.app.ai.OptimizationLevel  
ULTRA_FAST (com.chessvision.app.ai.OptimizationLevel  ActivityManager 	java.lang  HardwareProfile 	java.lang  OptimizationLevel 	java.lang  Runtime 	java.lang  contains 	java.lang  hardwareProfile 	java.lang  lazy 	java.lang  availableProcessors java.lang.Runtime  
getRuntime java.lang.Runtime  ActivityManager kotlin  HardwareProfile kotlin  Lazy kotlin  OptimizationLevel kotlin  Runtime kotlin  contains kotlin  hardwareProfile kotlin  lazy kotlin  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getCONTAINS 
kotlin.String  getContains 
kotlin.String  ActivityManager kotlin.annotation  HardwareProfile kotlin.annotation  OptimizationLevel kotlin.annotation  Runtime kotlin.annotation  contains kotlin.annotation  hardwareProfile kotlin.annotation  lazy kotlin.annotation  ActivityManager kotlin.collections  HardwareProfile kotlin.collections  OptimizationLevel kotlin.collections  Runtime kotlin.collections  contains kotlin.collections  hardwareProfile kotlin.collections  lazy kotlin.collections  ActivityManager kotlin.comparisons  HardwareProfile kotlin.comparisons  OptimizationLevel kotlin.comparisons  Runtime kotlin.comparisons  contains kotlin.comparisons  hardwareProfile kotlin.comparisons  lazy kotlin.comparisons  ActivityManager 	kotlin.io  HardwareProfile 	kotlin.io  OptimizationLevel 	kotlin.io  Runtime 	kotlin.io  contains 	kotlin.io  hardwareProfile 	kotlin.io  lazy 	kotlin.io  ActivityManager 
kotlin.jvm  HardwareProfile 
kotlin.jvm  OptimizationLevel 
kotlin.jvm  Runtime 
kotlin.jvm  contains 
kotlin.jvm  hardwareProfile 
kotlin.jvm  lazy 
kotlin.jvm  ActivityManager 
kotlin.ranges  HardwareProfile 
kotlin.ranges  OptimizationLevel 
kotlin.ranges  Runtime 
kotlin.ranges  contains 
kotlin.ranges  hardwareProfile 
kotlin.ranges  lazy 
kotlin.ranges  ActivityManager kotlin.sequences  HardwareProfile kotlin.sequences  OptimizationLevel kotlin.sequences  Runtime kotlin.sequences  contains kotlin.sequences  hardwareProfile kotlin.sequences  lazy kotlin.sequences  ActivityManager kotlin.text  HardwareProfile kotlin.text  OptimizationLevel kotlin.text  Runtime kotlin.text  contains kotlin.text  hardwareProfile kotlin.text  lazy kotlin.text  OptimizationLevel !kotlinx.coroutines.CoroutineScope  getHARDWAREProfile !kotlinx.coroutines.CoroutineScope  getHardwareProfile !kotlinx.coroutines.CoroutineScope  getMINOf !kotlinx.coroutines.CoroutineScope  getMinOf !kotlinx.coroutines.CoroutineScope  hardwareProfile !kotlinx.coroutines.CoroutineScope  minOf !kotlinx.coroutines.CoroutineScope  
ExecutionMode (ai.onnxruntime.OrtSession.SessionOptions  setExecutionMode (ai.onnxruntime.OrtSession.SessionOptions  PARALLEL 6ai.onnxruntime.OrtSession.SessionOptions.ExecutionMode  ResourceTracker com.chessvision.app.ai  getLET  ai.onnxruntime.OrtSession.Result  getLet  ai.onnxruntime.OrtSession.Result  let  ai.onnxruntime.OrtSession.Result  reversed com.chessvision.app.ai  synchronized com.chessvision.app.ai  
isHelioP35 &com.chessvision.app.ai.HardwareProfile  ResourceTracker "com.chessvision.app.ai.ONNXChessAI  ResourceTracker ,com.chessvision.app.ai.ONNXChessAI.Companion  
AtomicBoolean &com.chessvision.app.ai.ResourceTracker  
AutoCloseable &com.chessvision.app.ai.ResourceTracker  	Exception &com.chessvision.app.ai.ResourceTracker  Int &com.chessvision.app.ai.ResourceTracker  Log &com.chessvision.app.ai.ResourceTracker  close &com.chessvision.app.ai.ResourceTracker  getMUTABLEListOf &com.chessvision.app.ai.ResourceTracker  getMutableListOf &com.chessvision.app.ai.ResourceTracker  getREVERSED &com.chessvision.app.ai.ResourceTracker  getReversed &com.chessvision.app.ai.ResourceTracker  getSYNCHRONIZED &com.chessvision.app.ai.ResourceTracker  getSynchronized &com.chessvision.app.ai.ResourceTracker  getUSE &com.chessvision.app.ai.ResourceTracker  getUse &com.chessvision.app.ai.ResourceTracker  
isDisposed &com.chessvision.app.ai.ResourceTracker  
mutableListOf &com.chessvision.app.ai.ResourceTracker  	resources &com.chessvision.app.ai.ResourceTracker  reversed &com.chessvision.app.ai.ResourceTracker  size &com.chessvision.app.ai.ResourceTracker  synchronized &com.chessvision.app.ai.ResourceTracker  track &com.chessvision.app.ai.ResourceTracker  use &com.chessvision.app.ai.ResourceTracker  ResourceTracker 	java.lang  reversed 	java.lang  synchronized 	java.lang  ResourceTracker kotlin  reversed kotlin  synchronized kotlin  ResourceTracker kotlin.annotation  reversed kotlin.annotation  synchronized kotlin.annotation  ResourceTracker kotlin.collections  reversed kotlin.collections  synchronized kotlin.collections  getREVERSED kotlin.collections.MutableList  getReversed kotlin.collections.MutableList  ResourceTracker kotlin.comparisons  reversed kotlin.comparisons  synchronized kotlin.comparisons  limitedParallelism 1kotlin.coroutines.AbstractCoroutineContextElement  ResourceTracker 	kotlin.io  reversed 	kotlin.io  synchronized 	kotlin.io  ResourceTracker 
kotlin.jvm  reversed 
kotlin.jvm  synchronized 
kotlin.jvm  ResourceTracker 
kotlin.ranges  reversed 
kotlin.ranges  synchronized 
kotlin.ranges  ResourceTracker kotlin.sequences  reversed kotlin.sequences  synchronized kotlin.sequences  ResourceTracker kotlin.text  reversed kotlin.text  synchronized kotlin.text  limitedParallelism &kotlinx.coroutines.CoroutineDispatcher  ResourceTracker !kotlinx.coroutines.CoroutineScope  getUSE !kotlinx.coroutines.CoroutineScope  getUse !kotlinx.coroutines.CoroutineScope  use !kotlinx.coroutines.CoroutineScope  addConfigEntry (ai.onnxruntime.OrtSession.SessionOptions  	Exception android.app.Activity  System android.app.Activity  android android.app.Activity  	Exception android.content.Context  System android.content.Context  	Exception android.content.ContextWrapper  System android.content.ContextWrapper  	Exception  android.view.ContextThemeWrapper  System  android.view.ContextThemeWrapper  android  android.view.ContextThemeWrapper  	Exception #androidx.activity.ComponentActivity  System #androidx.activity.ComponentActivity  android #androidx.activity.ComponentActivity  System androidx.compose.animation  System androidx.compose.animation.core  Snapshot "androidx.compose.runtime.snapshots  	Exception #androidx.core.app.ComponentActivity  System #androidx.core.app.ComponentActivity  android #androidx.core.app.ComponentActivity  	Exception  com.chessvision.app.MainActivity  System  com.chessvision.app.MainActivity  android  com.chessvision.app.MainActivity  
getANDROID  com.chessvision.app.MainActivity  
getAndroid  com.chessvision.app.MainActivity  Thread com.chessvision.app.ai  activeOperations com.chessvision.app.ai  mutableSetOf com.chessvision.app.ai  operationMutex com.chessvision.app.ai  Thread "com.chessvision.app.ai.ONNXChessAI  activeOperations "com.chessvision.app.ai.ONNXChessAI  getMUTABLESetOf "com.chessvision.app.ai.ONNXChessAI  getMutableSetOf "com.chessvision.app.ai.ONNXChessAI  mutableSetOf "com.chessvision.app.ai.ONNXChessAI  operationMutex "com.chessvision.app.ai.ONNXChessAI  Thread ,com.chessvision.app.ai.ONNXChessAI.Companion  activeOperations ,com.chessvision.app.ai.ONNXChessAI.Companion  getMUTABLESetOf ,com.chessvision.app.ai.ONNXChessAI.Companion  getMutableSetOf ,com.chessvision.app.ai.ONNXChessAI.Companion  mutableSetOf ,com.chessvision.app.ai.ONNXChessAI.Companion  operationMutex ,com.chessvision.app.ai.ONNXChessAI.Companion  Thread 	java.lang  activeOperations 	java.lang  mutableSetOf 	java.lang  operationMutex 	java.lang  setProperty java.lang.System  MIN_PRIORITY java.lang.Thread  
currentThread java.lang.Thread  getNAME java.lang.Thread  getName java.lang.Thread  getPRIORITY java.lang.Thread  getPriority java.lang.Thread  name java.lang.Thread  priority java.lang.Thread  setName java.lang.Thread  setPriority java.lang.Thread  sleep java.lang.Thread  Thread kotlin  activeOperations kotlin  mutableSetOf kotlin  operationMutex kotlin  Thread kotlin.annotation  activeOperations kotlin.annotation  mutableSetOf kotlin.annotation  operationMutex kotlin.annotation  
MutableSet kotlin.collections  Thread kotlin.collections  activeOperations kotlin.collections  mutableSetOf kotlin.collections  operationMutex kotlin.collections  Thread kotlin.comparisons  activeOperations kotlin.comparisons  mutableSetOf kotlin.comparisons  operationMutex kotlin.comparisons  Thread 	kotlin.io  activeOperations 	kotlin.io  mutableSetOf 	kotlin.io  operationMutex 	kotlin.io  Thread 
kotlin.jvm  activeOperations 
kotlin.jvm  mutableSetOf 
kotlin.jvm  operationMutex 
kotlin.jvm  Thread 
kotlin.ranges  activeOperations 
kotlin.ranges  mutableSetOf 
kotlin.ranges  operationMutex 
kotlin.ranges  Thread kotlin.sequences  activeOperations kotlin.sequences  mutableSetOf kotlin.sequences  operationMutex kotlin.sequences  Thread kotlin.text  activeOperations kotlin.text  mutableSetOf kotlin.text  operationMutex kotlin.text  Thread !kotlinx.coroutines.CoroutineScope  activeOperations !kotlinx.coroutines.CoroutineScope  getACTIVEOperations !kotlinx.coroutines.CoroutineScope  getActiveOperations !kotlinx.coroutines.CoroutineScope  getOPERATIONMutex !kotlinx.coroutines.CoroutineScope  getOperationMutex !kotlinx.coroutines.CoroutineScope  getWITHLock !kotlinx.coroutines.CoroutineScope  getWithLock !kotlinx.coroutines.CoroutineScope  operationMutex !kotlinx.coroutines.CoroutineScope  withLock !kotlinx.coroutines.CoroutineScope                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             