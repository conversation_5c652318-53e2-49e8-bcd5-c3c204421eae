"""
<PERSON><PERSON>t to verify that PT files match their JPG visualizations in the augmented dataset.
This script loads PT files and their corresponding JPG visualizations and displays them side by side.
"""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import cv2
from PIL import Image
import random
import argparse

def load_pt_image(pt_file):
    """Load a PyTorch tensor image file and convert to numpy for visualization"""
    tensor = torch.load(pt_file)

    # Denormalize if needed
    if tensor.max() <= 1.0:
        mean = torch.tensor([0.485, 0.456, 0.406]).view(3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225]).view(3, 1, 1)
        tensor = tensor * std + mean

    # Convert to numpy and ensure proper range
    img = tensor.permute(1, 2, 0).numpy()
    img = np.clip(img * 255, 0, 255).astype(np.uint8)
    return img

def load_pt_mask(pt_file):
    """Load a PyTorch tensor mask file and convert to numpy for visualization"""
    tensor = torch.load(pt_file)

    # Convert to numpy and ensure proper range
    mask = tensor.squeeze().numpy()
    mask = np.clip(mask * 255, 0, 255).astype(np.uint8)
    return mask

def load_pt_heatmaps(pt_file):
    """Load PyTorch tensor heatmaps and convert to numpy for visualization"""
    tensor = torch.load(pt_file)

    # Convert to numpy
    heatmaps = []
    for i in range(tensor.shape[0]):
        hm = tensor[i].numpy()
        heatmaps.append(hm)

    return heatmaps

def load_pt_corners(pt_file):
    """Load PyTorch tensor corners"""
    tensor = torch.load(pt_file)
    return tensor.numpy()

def load_jpg_image(jpg_file):
    """Load a JPG image file"""
    img = cv2.imread(jpg_file)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    return img

def verify_sample(sample_dir):
    """Verify PT files match JPG visualizations for a sample"""
    print(f"Verifying sample: {sample_dir}")

    # Load PT files
    pt_image = load_pt_image(os.path.join(sample_dir, "image.pt"))
    pt_mask = load_pt_mask(os.path.join(sample_dir, "mask.pt"))
    pt_heatmaps = load_pt_heatmaps(os.path.join(sample_dir, "corner_heatmaps.pt"))
    pt_corners = load_pt_corners(os.path.join(sample_dir, "corners.pt"))

    # Load JPG files
    jpg_image = load_jpg_image(os.path.join(sample_dir, "image.jpg"))
    jpg_mask = load_jpg_image(os.path.join(sample_dir, "mask.jpg"))

    # Load corner orientation if available
    corner_orientation_file = os.path.join(sample_dir, "corner_orientation.json")
    corner_names = ["TL", "TR", "BR", "BL"]

    # Create figure for comparison
    plt.figure(figsize=(15, 10))

    # Compare image
    plt.subplot(2, 3, 1)
    plt.imshow(pt_image)
    plt.title("PT Image")
    plt.axis('off')

    plt.subplot(2, 3, 2)
    plt.imshow(jpg_image)
    plt.title("JPG Image")
    plt.axis('off')

    # Compare mask
    plt.subplot(2, 3, 3)
    plt.imshow(pt_mask, cmap='gray')
    plt.title("PT Mask")
    plt.axis('off')

    plt.subplot(2, 3, 4)
    plt.imshow(jpg_mask, cmap='gray')
    plt.title("JPG Mask")
    plt.axis('off')

    # Show heatmaps with corners
    plt.subplot(2, 3, 5)
    plt.imshow(pt_image)

    # Add heatmaps as overlay with consistent colors
    corner_cmaps = ['Blues', 'Greens', 'Reds', 'cool']  # TL, TR, BR, BL
    colors = ['blue', 'green', 'red', 'cyan']  # TL, TR, BR, BL

    for i, hm in enumerate(pt_heatmaps):
        plt.imshow(hm, alpha=0.3, cmap=corner_cmaps[i], vmin=0, vmax=1)

    # Add corners with consistent colors
    for i in range(0, len(pt_corners), 2):
        if i//2 < len(corner_names):
            x, y = pt_corners[i], pt_corners[i+1]
            plt.plot(x, y, 'o', color=colors[i//2], markersize=8)
            plt.text(x+5, y+5, corner_names[i//2], color='white', fontsize=12,
                    bbox=dict(facecolor='black', alpha=0.7))

    plt.title("PT Heatmaps & Corners")
    plt.axis('off')

    # Show comprehensive visualization
    plt.subplot(2, 3, 6)
    comp_vis = load_jpg_image(os.path.join(sample_dir, "comprehensive_visualization.jpg"))
    plt.imshow(comp_vis)
    plt.title("Comprehensive Visualization")
    plt.axis('off')

    plt.tight_layout()
    plt.savefig(os.path.join(sample_dir, "verification.jpg"))
    plt.close()

    print(f"Verification image saved to: {os.path.join(sample_dir, 'verification.jpg')}")
    return os.path.join(sample_dir, "verification.jpg")

def main():
    parser = argparse.ArgumentParser(description="Verify PT files match JPG visualizations")
    parser.add_argument("--dataset_dir", type=str, default="data/augmented/v5.2/augmented_20250518_145831",
                        help="Path to the augmented dataset directory")
    parser.add_argument("--num_samples", type=int, default=5,
                        help="Number of random samples to verify")

    args = parser.parse_args()

    # Get all sample directories
    sample_dirs = []
    for root, dirs, files in os.walk(args.dataset_dir):
        if "sample_" in os.path.basename(root) and "image.pt" in files:
            sample_dirs.append(root)

    # Select random samples
    if args.num_samples < len(sample_dirs):
        sample_dirs = random.sample(sample_dirs, args.num_samples)

    # Verify each sample
    verification_images = []
    for sample_dir in sample_dirs:
        verification_image = verify_sample(sample_dir)
        verification_images.append(verification_image)

    print(f"Verified {len(verification_images)} samples.")
    print("Verification images saved to each sample directory.")

if __name__ == "__main__":
    main()
