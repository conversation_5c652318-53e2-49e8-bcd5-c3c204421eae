@echo off
echo Interactive Continue Training for Chess Piece Detection
echo =========================================================
echo.

echo This script will continue training from epoch 90 exactly as it was before the crash.
echo The best model from epoch 86 will be used as the starting point.
echo No modifications to weights or training strategy will be made.
echo Models will be saved in a new directory to avoid overwriting existing models.
echo.

echo INTERACTIVE MODE:
echo - Training will proceed one epoch at a time
echo - After each epoch, you will be asked if you want to continue
echo - When reaching the target epoch (100), you can choose to train for 10 more epochs
echo - Training metrics will be displayed after each epoch
echo.

echo Memory optimization is enabled to prevent crashes.
echo.

echo Press any key to start training...
pause > nul

python simple_continue_training.py

echo.
echo Training completed.
pause
