PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python chess_board_detection\train_v5_improved.py --continue_from_v4 --data_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real" --annotation_file "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json" --output_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection" --epochs_phase1 40 --epochs_phase2 80 --lr_phase1 0.001 --lr_phase2 0.0005 --dropout_rate 0.3 --weight_decay 1e-4 --batch_size 4 --save_interval 5 --cpu --use_scheduler
CUDA not available. Using CPU.
Using device: cpu
Creating expanded validation set with enhanced augmentations...
Train dataset size: 14
Validation dataset size: 16
Initializing improved v5.1 model...
Loading v4 best model: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v4\best_model.pth
Loaded v4 best model into base model
Model moved to cpu
Model parameters: 17485782
Trainable parameters: 17485782

=== Phase 1: Focus on peak-to-second ratio and detection rate ===
Saving outputs to v5.1 folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Learning rate scheduler with warm restarts initialized
Epoch 1/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.3534, Heatmap Loss: 2113.0483, Geometric Loss: 0.8000, Total: 3170.5657
 25%|██████████████████████                                                                  | 1/4 [00:06<00:18,  6.10s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
Batch 1, train - Seg Loss: 0.4988, Heatmap Loss: 2160.8240, Geometric Loss: 0.6869, Total: 3242.2842
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.61s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
Batch 2, train - Seg Loss: 0.5624, Heatmap Loss: 2162.0591, Geometric Loss: 0.6346, Total: 3244.1587
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.82s/it]
train Loss: 3218.4135, Seg Loss: 0.4839, Heatmap Loss: 2144.9045, Geometric Loss: 0.7162
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6944
  avg_peak_to_mean_ratio: 10.8787
  avg_peak_to_second_ratio: 1.0061
  detection_rate: 0.7143
  Overall Confidence Score: 3.8234
train Heatmap Components:
  mse_loss: 1.4893
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 127.0715
  peak_to_second_ratio_loss: 0.0168
  detection_rate_loss: 1.7278
  segmentation_guidance_loss: 1.1346
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.1035, Heatmap Loss: 3054.8945, Geometric Loss: 0.7913, Total: 4583.0786
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.60s/it]Batch 1, val - Seg Loss: 0.0947, Heatmap Loss: 3122.4268, Geometric Loss: 0.6965, Total: 4684.2920
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.50s/it]Batch 2, val - Seg Loss: 0.0926, Heatmap Loss: 2929.1892, Geometric Loss: 0.7493, Total: 4394.4761
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.51s/it]
val Loss: 4211.1398, Seg Loss: 0.0944, Heatmap Loss: 2806.9739, Geometric Loss: 0.7305
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8588
  avg_peak_to_mean_ratio: 1.4178
  avg_peak_to_second_ratio: 1.0208
  detection_rate: 0.8281
  Overall Confidence Score: 1.2814
val Heatmap Components:
  mse_loss: 4.2612
  separation_loss: 1000.0000
  peak_separation_loss: 750.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 182.6936
  peak_to_second_ratio_loss: 0.0178
  detection_rate_loss: 0.8920
  segmentation_guidance_loss: 6.3082
New best model saved with loss: 4211.1398
Current learning rate: 0.000976

Epoch 2/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
Batch 0, train - Seg Loss: 0.3394, Heatmap Loss: 2092.3167, Geometric Loss: 0.9140, Total: 3139.5457
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.51s/it]Batch 1, train - Seg Loss: 0.5040, Heatmap Loss: 2077.4851, Geometric Loss: 1.0643, Total: 3117.5828
 50%|████████████████████████████████████████████                                            | 2/4 [00:12<00:12,  6.28s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 2, train - Seg Loss: 0.4704, Heatmap Loss: 2043.0166, Geometric Loss: 0.5892, Total: 3065.4668
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:18<00:06,  6.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:22<00:00,  5.51s/it]
train Loss: 3113.4188, Seg Loss: 0.4161, Heatmap Loss: 2074.8946, Geometric Loss: 0.8261
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8102
  avg_peak_to_mean_ratio: 6.7289
  avg_peak_to_second_ratio: 1.0040
  detection_rate: 0.7857
  Overall Confidence Score: 2.5822
train Heatmap Components:
  mse_loss: 1.6341
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 62.4931
  peak_to_second_ratio_loss: 0.0235
  detection_rate_loss: 1.0995
  segmentation_guidance_loss: 0.8093
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.2981, Heatmap Loss: 2185.1147, Geometric Loss: 0.4485, Total: 3278.3291
 25%|██████████████████████                                                                  | 1/4 [00:02<00:08,  2.81s/it]Batch 1, val - Seg Loss: 0.2435, Heatmap Loss: 2200.5381, Geometric Loss: 0.7272, Total: 3301.6323
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.79s/it]Batch 2, val - Seg Loss: 0.2536, Heatmap Loss: 2115.1067, Geometric Loss: 0.7770, Total: 3173.5354
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.76s/it]
val Loss: 3261.3041, Seg Loss: 0.2550, Heatmap Loss: 2173.6563, Geometric Loss: 0.7057
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8300
  avg_peak_to_mean_ratio: 15.0967
  avg_peak_to_second_ratio: 1.0054
  detection_rate: 0.9844
  Overall Confidence Score: 4.7291
val Heatmap Components:
  mse_loss: 6.2103
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 165.5843
  peak_to_second_ratio_loss: 0.0226
  detection_rate_loss: 0.0350
  segmentation_guidance_loss: 0.6213
New best model saved with loss: 3261.3041
Current learning rate: 0.000905

Epoch 3/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 0, train - Seg Loss: 0.4246, Heatmap Loss: 2073.5459, Geometric Loss: 0.8287, Total: 3111.4065
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.46s/it]Batch 1, train - Seg Loss: 0.5555, Heatmap Loss: 2082.9126, Geometric Loss: 0.8163, Total: 3125.5774
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.64s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
Batch 2, train - Seg Loss: 0.4636, Heatmap Loss: 2052.2573, Geometric Loss: 0.7468, Total: 3079.4470
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.93s/it]
train Loss: 3099.2310, Seg Loss: 0.4583, Heatmap Loss: 2065.4298, Geometric Loss: 0.7849
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4282
  avg_peak_to_mean_ratio: 4.7546
  avg_peak_to_second_ratio: 1.0047
  detection_rate: 0.7143
  Overall Confidence Score: 1.9755
train Heatmap Components:
  mse_loss: 1.5594
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 994.9496
  peak_enhancement_loss: 56.6356
  peak_to_second_ratio_loss: 0.0285
  detection_rate_loss: 1.3476
  segmentation_guidance_loss: 0.5380
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.2627, Heatmap Loss: 2172.1018, Geometric Loss: 0.8345, Total: 3259.0833
 25%|██████████████████████                                                                  | 1/4 [00:02<00:08,  2.71s/it]Batch 1, val - Seg Loss: 0.2536, Heatmap Loss: 2178.7046, Geometric Loss: 0.6471, Total: 3268.8284
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.69s/it]Batch 2, val - Seg Loss: 0.2291, Heatmap Loss: 2113.7644, Geometric Loss: 0.5467, Total: 3171.3127
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.72s/it]
val Loss: 3218.8531, Seg Loss: 0.2422, Heatmap Loss: 2145.3709, Geometric Loss: 0.6932
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6417
  avg_peak_to_mean_ratio: 10.7610
  avg_peak_to_second_ratio: 1.0067
  detection_rate: 1.0000
  Overall Confidence Score: 3.6024
val Heatmap Components:
  mse_loss: 4.3187
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 140.6165
  peak_to_second_ratio_loss: 0.0290
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 3218.8531
Current learning rate: 0.000796

Epoch 4/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.77s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:18<00:06,  6.35s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:22<00:00,  5.69s/it]
train Loss: 3746.8253, Seg Loss: 0.3802, Heatmap Loss: 2497.1009, Geometric Loss: 0.9923
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.0794
  avg_peak_to_mean_ratio: 1.8238
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 0.5536
  Overall Confidence Score: 1.1146
train Heatmap Components:
  mse_loss: 1.1254
  separation_loss: 1000.0000
  peak_separation_loss: 571.4286
  edge_suppression_loss: 968.2201
  peak_enhancement_loss: 48.4895
  peak_to_second_ratio_loss: 0.0371
  detection_rate_loss: 2.6685
  segmentation_guidance_loss: 0.1094
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:13<00:00,  3.41s/it] 
val Loss: 3162.2335, Seg Loss: 0.2535, Heatmap Loss: 2107.5649, Geometric Loss: 0.7908
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.5241
  avg_peak_to_mean_ratio: 2.9047
  avg_peak_to_second_ratio: 1.0060
  detection_rate: 1.0000
  Overall Confidence Score: 1.6087
val Heatmap Components:
  mse_loss: 2.7612
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 104.2921
  peak_to_second_ratio_loss: 0.0341
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 3162.2335
Current learning rate: 0.000658

Epoch 5/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:12<00:13,  6.51s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:22<00:00,  5.52s/it]
train Loss: 2865.1128, Seg Loss: 0.4067, Heatmap Loss: 1909.3409, Geometric Loss: 0.8685
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.3495
  avg_peak_to_mean_ratio: 9.1802
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.6250
  Overall Confidence Score: 3.0391
train Heatmap Components:
  mse_loss: 1.3192
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 818.0287
  peak_enhancement_loss: 72.5831
  peak_to_second_ratio_loss: 0.0415
  detection_rate_loss: 2.0190
  segmentation_guidance_loss: 0.3176
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.83s/it] 
val Loss: 3183.4089, Seg Loss: 0.2704, Heatmap Loss: 2121.6893, Geometric Loss: 0.7556
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6204
  avg_peak_to_mean_ratio: 1.9720
  avg_peak_to_second_ratio: 1.0030
  detection_rate: 1.0000
  Overall Confidence Score: 1.3989
val Heatmap Components:
  mse_loss: 2.7300
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 117.8429
  peak_to_second_ratio_loss: 0.0415
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2467
Current learning rate: 0.000505

Epoch 6/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.88s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:21<00:00,  5.36s/it]
train Loss: 3067.1960, Seg Loss: 0.3900, Heatmap Loss: 2044.0981, Geometric Loss: 0.8236
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7159
  avg_peak_to_mean_ratio: 11.7319
  avg_peak_to_second_ratio: 1.0026
  detection_rate: 0.7679
  Overall Confidence Score: 3.8045
train Heatmap Components:
  mse_loss: 1.6413
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 984.7233
  peak_enhancement_loss: 44.8652
  peak_to_second_ratio_loss: 0.0491
  detection_rate_loss: 1.4462
  segmentation_guidance_loss: 0.2812
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.16s/it] 
val Loss: 3168.6243, Seg Loss: 0.2876, Heatmap Loss: 2111.8617, Geometric Loss: 0.6802
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.5856
  avg_peak_to_mean_ratio: 9.6034
  avg_peak_to_second_ratio: 1.0023
  detection_rate: 1.0000
  Overall Confidence Score: 3.2978
val Heatmap Components:
  mse_loss: 2.2548
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 107.9983
  peak_to_second_ratio_loss: 0.0530
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.4067
Current learning rate: 0.000352

Epoch 7/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:07<00:22,  7.44s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:16<00:16,  8.23s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:22<00:07,  7.37s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:25<00:00,  6.45s/it]
train Loss: 2861.9819, Seg Loss: 0.3974, Heatmap Loss: 1907.2638, Geometric Loss: 0.8608
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6219
  avg_peak_to_mean_ratio: 5.5507
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.6786
  Overall Confidence Score: 2.2132
train Heatmap Components:
  mse_loss: 1.3686
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 862.6506
  peak_enhancement_loss: 28.4386
  peak_to_second_ratio_loss: 0.0591
  detection_rate_loss: 1.6313
  segmentation_guidance_loss: 0.4348
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.79s/it] 
val Loss: 3160.4667, Seg Loss: 0.2925, Heatmap Loss: 2106.4360, Geometric Loss: 0.6504
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7122
  avg_peak_to_mean_ratio: 2.6586
  avg_peak_to_second_ratio: 1.0021
  detection_rate: 1.0000
  Overall Confidence Score: 1.5932
val Heatmap Components:
  mse_loss: 1.9265
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 102.7319
  peak_to_second_ratio_loss: 0.0603
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.4365
New best model saved with loss: 3160.4667
Current learning rate: 0.000214

Epoch 8/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:06<00:19,  6.52s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:24<00:00,  6.19s/it]
train Loss: 3018.5073, Seg Loss: 0.3759, Heatmap Loss: 2011.6156, Geometric Loss: 0.8850
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4984
  avg_peak_to_mean_ratio: 6.6908
  avg_peak_to_second_ratio: 1.0022
  detection_rate: 0.6607
  Overall Confidence Score: 2.4630
train Heatmap Components:
  mse_loss: 1.3055
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 953.1914
  peak_enhancement_loss: 40.3925
  peak_to_second_ratio_loss: 0.0697
  detection_rate_loss: 1.8509
  segmentation_guidance_loss: 0.4365
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:13<00:00,  3.26s/it] 
val Loss: 3123.6955, Seg Loss: 0.3023, Heatmap Loss: 2081.8087, Geometric Loss: 0.8503
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7208
  avg_peak_to_mean_ratio: 5.5265
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 1.0000
  Overall Confidence Score: 2.3123
val Heatmap Components:
  mse_loss: 1.5198
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 78.2584
  peak_to_second_ratio_loss: 0.0687
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.5001
New best model saved with loss: 3123.6955
Current learning rate: 0.000105

Epoch 9/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:13<00:13,  6.72s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:25<00:00,  6.34s/it]
train Loss: 3000.5593, Seg Loss: 0.4425, Heatmap Loss: 1999.5987, Geometric Loss: 0.8986
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9009
  avg_peak_to_mean_ratio: 10.4513
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.7143
  Overall Confidence Score: 3.5171
train Heatmap Components:
  mse_loss: 1.4788
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 953.0381
  peak_enhancement_loss: 33.4415
  peak_to_second_ratio_loss: 0.0797
  detection_rate_loss: 1.2636
  segmentation_guidance_loss: 0.1680
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.05s/it] 
val Loss: 2930.2761, Seg Loss: 0.2545, Heatmap Loss: 1952.9338, Geometric Loss: 0.7763
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7387
  avg_peak_to_mean_ratio: 47.6739
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 1.0000
  Overall Confidence Score: 12.8534
val Heatmap Components:
  mse_loss: 1.3503
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 910.6520
  peak_enhancement_loss: 39.0927
  peak_to_second_ratio_loss: 0.0813
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.3094
New best model saved with loss: 2930.2761
Current learning rate: 0.000034

Epoch 10/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:15<00:14,  7.18s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:23<00:00,  5.89s/it]
train Loss: 3024.2864, Seg Loss: 0.3631, Heatmap Loss: 2015.5218, Geometric Loss: 0.8009
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6972
  avg_peak_to_mean_ratio: 8.3852
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 0.7500
  Overall Confidence Score: 2.9585
train Heatmap Components:
  mse_loss: 1.4734
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 944.7230
  peak_enhancement_loss: 52.6653
  peak_to_second_ratio_loss: 0.0897
  detection_rate_loss: 1.7886
  segmentation_guidance_loss: 0.5027
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 2923.5446, Seg Loss: 0.2738, Heatmap Loss: 1948.4411, Geometric Loss: 0.7616
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6579
  avg_peak_to_mean_ratio: 3.8379
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 1.8744
val Heatmap Components:
  mse_loss: 1.3286
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 880.5126
  peak_enhancement_loss: 64.1297
  peak_to_second_ratio_loss: 0.0924
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.5424
New best model saved with loss: 2923.5446
Current learning rate: 0.001000

Epoch 11/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.60s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.80s/it]
train Loss: 3000.1614, Seg Loss: 0.3809, Heatmap Loss: 1999.4938, Geometric Loss: 0.6747
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8833
  avg_peak_to_mean_ratio: 94.3482
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 0.8214
  Overall Confidence Score: 24.5136
train Heatmap Components:
  mse_loss: 1.6709
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 940.3817
  peak_enhancement_loss: 50.4271
  peak_to_second_ratio_loss: 0.1016
  detection_rate_loss: 0.5531
  segmentation_guidance_loss: 0.5328
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 3151.9319, Seg Loss: 0.2996, Heatmap Loss: 2100.6971, Geometric Loss: 0.7334
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8495
  avg_peak_to_mean_ratio: 6.5540
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 1.0000
  Overall Confidence Score: 2.6012
val Heatmap Components:
  mse_loss: 2.2324
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 96.0753
  peak_to_second_ratio_loss: 0.1051
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.4065
Current learning rate: 0.000994

Epoch 12/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:04<00:14,  4.90s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.74s/it]
train Loss: 2872.1844, Seg Loss: 0.4013, Heatmap Loss: 1914.0360, Geometric Loss: 0.9113
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7699
  avg_peak_to_mean_ratio: 9.9196
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.7857
  Overall Confidence Score: 3.3691
train Heatmap Components:
  mse_loss: 1.6078
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 875.1418
  peak_enhancement_loss: 28.5469
  peak_to_second_ratio_loss: 0.1174
  detection_rate_loss: 0.7780
  segmentation_guidance_loss: 0.3774
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 3073.0129, Seg Loss: 0.3336, Heatmap Loss: 2048.0587, Geometric Loss: 0.7391
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9059
  avg_peak_to_mean_ratio: 4.9191
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 1.0000
  Overall Confidence Score: 2.2065
val Heatmap Components:
  mse_loss: 2.7710
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 987.9866
  peak_enhancement_loss: 55.2907
  peak_to_second_ratio_loss: 0.1175
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1241
Current learning rate: 0.000976

Epoch 13/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.67s/it]
train Loss: 3053.6284, Seg Loss: 0.4176, Heatmap Loss: 2034.9762, Geometric Loss: 0.9332
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5162
  avg_peak_to_mean_ratio: 3.0883
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 0.7143
  Overall Confidence Score: 1.5800
train Heatmap Components:
  mse_loss: 1.4370
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 953.6040
  peak_enhancement_loss: 64.0778
  peak_to_second_ratio_loss: 0.1240
  detection_rate_loss: 1.7278
  segmentation_guidance_loss: 0.0871
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.55s/it] 
val Loss: 3087.1415, Seg Loss: 0.3337, Heatmap Loss: 2057.4479, Geometric Loss: 0.7950
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8796
  avg_peak_to_mean_ratio: 2.8563
  avg_peak_to_second_ratio: 1.0013
  detection_rate: 1.0000
  Overall Confidence Score: 1.6843
val Heatmap Components:
  mse_loss: 3.0490
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 52.2918
  peak_to_second_ratio_loss: 0.1322
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0617
Current learning rate: 0.000946

Epoch 14/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.70s/it]
train Loss: 2952.5986, Seg Loss: 0.3617, Heatmap Loss: 1967.7346, Geometric Loss: 0.7938
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4719
  avg_peak_to_mean_ratio: 3.6691
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.7500
  Overall Confidence Score: 1.7230
train Heatmap Components:
  mse_loss: 1.5448
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 938.9105
  peak_enhancement_loss: 17.7364
  peak_to_second_ratio_loss: 0.1445
  detection_rate_loss: 0.8601
  segmentation_guidance_loss: 0.2475
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.89s/it] 
val Loss: 3078.4071, Seg Loss: 0.3186, Heatmap Loss: 2051.5897, Geometric Loss: 0.8798
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.5613
  avg_peak_to_mean_ratio: 21.7085
  avg_peak_to_second_ratio: 1.0013
  detection_rate: 1.0000
  Overall Confidence Score: 6.3178
val Heatmap Components:
  mse_loss: 1.3684
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 47.6247
  peak_to_second_ratio_loss: 0.1469
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1970
Current learning rate: 0.000905

Epoch 15/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.19s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:21<00:00,  5.49s/it]
train Loss: 3000.0396, Seg Loss: 0.3866, Heatmap Loss: 1999.3209, Geometric Loss: 0.8393
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6488
  avg_peak_to_mean_ratio: 25.7411
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.6786
  Overall Confidence Score: 7.2673
train Heatmap Components:
  mse_loss: 1.4390
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 952.0585
  peak_enhancement_loss: 28.7138
  peak_to_second_ratio_loss: 0.1586
  detection_rate_loss: 1.8099
  segmentation_guidance_loss: 0.1258
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 3087.8496, Seg Loss: 0.3071, Heatmap Loss: 2058.0118, Geometric Loss: 0.6560
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.5121
  avg_peak_to_mean_ratio: 30.7718
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 8.5711
val Heatmap Components:
  mse_loss: 1.2626
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 54.3247
  peak_to_second_ratio_loss: 0.1611
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0036
Current learning rate: 0.000855

Epoch 16/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 2908.6388, Seg Loss: 0.3812, Heatmap Loss: 1938.4169, Geometric Loss: 0.7905
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7766
  avg_peak_to_mean_ratio: 6.5939
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.7679
  Overall Confidence Score: 2.5349
train Heatmap Components:
  mse_loss: 1.5813
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 899.3543
  peak_enhancement_loss: 26.9152
  peak_to_second_ratio_loss: 0.1758
  detection_rate_loss: 0.9619
  segmentation_guidance_loss: 0.1168
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 2918.2985, Seg Loss: 0.2996, Heatmap Loss: 1944.9616, Geometric Loss: 0.6956
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6493
  avg_peak_to_mean_ratio: 10.9157
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 1.0000
  Overall Confidence Score: 3.6413
val Heatmap Components:
  mse_loss: 1.3700
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 901.5859
  peak_enhancement_loss: 38.8049
  peak_to_second_ratio_loss: 0.1780
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2656
New best model saved with loss: 2918.2985
Current learning rate: 0.000796

Epoch 17/40
----------
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.82s/it] 
train Loss: 2946.1402, Seg Loss: 0.4166, Heatmap Loss: 1963.3456, Geometric Loss: 0.8815
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.3307
  avg_peak_to_mean_ratio: 2.0560
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 0.6429
  Overall Confidence Score: 1.2578
train Heatmap Components:
  mse_loss: 1.3147
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 881.2644
  peak_enhancement_loss: 61.1915
  peak_to_second_ratio_loss: 0.1917
  detection_rate_loss: 2.0348
  segmentation_guidance_loss: 0.2108
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 3008.0615, Seg Loss: 0.2640, Heatmap Loss: 2004.8361, Geometric Loss: 0.6792
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7017
  avg_peak_to_mean_ratio: 2.6625
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 1.0000
  Overall Confidence Score: 1.5911
val Heatmap Components:
  mse_loss: 1.3036
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 971.3087
  peak_enhancement_loss: 28.8384
  peak_to_second_ratio_loss: 0.1938
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2390
Current learning rate: 0.000730

Epoch 18/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.65s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.55s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.85s/it]
train Loss: 2827.9338, Seg Loss: 0.4415, Heatmap Loss: 1884.5312, Geometric Loss: 0.8692
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7211
  avg_peak_to_mean_ratio: 22.5353
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.7500
  Overall Confidence Score: 6.5018
train Heatmap Components:
  mse_loss: 1.5860
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 826.7773
  peak_enhancement_loss: 41.1074
  peak_to_second_ratio_loss: 0.2025
  detection_rate_loss: 1.5029
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 3086.7747, Seg Loss: 0.3258, Heatmap Loss: 2057.2422, Geometric Loss: 0.7319
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7447
  avg_peak_to_mean_ratio: 17.4803
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 1.0000
  Overall Confidence Score: 5.3063
val Heatmap Components:
  mse_loss: 1.2788
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 52.7910
  peak_to_second_ratio_loss: 0.2115
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000658

Epoch 19/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.00s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 2985.6565, Seg Loss: 0.4390, Heatmap Loss: 1989.6282, Geometric Loss: 0.9691
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.3288
  avg_peak_to_mean_ratio: 2.2153
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.6071
  Overall Confidence Score: 1.2880
train Heatmap Components:
  mse_loss: 1.2554
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 682.3849
  peak_enhancement_loss: 54.1809
  peak_to_second_ratio_loss: 0.2242
  detection_rate_loss: 2.4026
  segmentation_guidance_loss: 0.3259
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 2900.2348, Seg Loss: 0.3954, Heatmap Loss: 1932.8475, Geometric Loss: 0.7103
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7034
  avg_peak_to_mean_ratio: 13.8108
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 4.3786
val Heatmap Components:
  mse_loss: 1.1404
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 865.9343
  peak_enhancement_loss: 62.3549
  peak_to_second_ratio_loss: 0.2279
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2900.2348
Current learning rate: 0.000582

Epoch 20/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:14<00:14,  7.36s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:24<00:00,  6.10s/it]
train Loss: 2685.4395, Seg Loss: 0.4777, Heatmap Loss: 1789.5070, Geometric Loss: 0.8767
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7146
  avg_peak_to_mean_ratio: 5.1738
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 0.7321
  Overall Confidence Score: 2.1553
train Heatmap Components:
  mse_loss: 1.5478
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 719.7565
  peak_enhancement_loss: 53.1599
  peak_to_second_ratio_loss: 0.2448
  detection_rate_loss: 1.3654
  segmentation_guidance_loss: 0.2238
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.69s/it] 
val Loss: 2924.9245, Seg Loss: 0.3642, Heatmap Loss: 1949.3237, Geometric Loss: 0.7184
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6945
  avg_peak_to_mean_ratio: 11.8919
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 1.0000
  Overall Confidence Score: 3.8967
val Heatmap Components:
  mse_loss: 1.2250
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 895.3632
  peak_enhancement_loss: 49.0347
  peak_to_second_ratio_loss: 0.2467
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000505

Epoch 21/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.87s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.04s/it]
train Loss: 2423.0611, Seg Loss: 0.4811, Heatmap Loss: 1614.6366, Geometric Loss: 0.7811
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6525
  avg_peak_to_mean_ratio: 3.7184
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.6786
  Overall Confidence Score: 1.7626
train Heatmap Components:
  mse_loss: 1.3107
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 523.3606
  peak_enhancement_loss: 69.8929
  peak_to_second_ratio_loss: 0.2586
  detection_rate_loss: 2.0242
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 2857.3025, Seg Loss: 0.3349, Heatmap Loss: 1904.3000, Geometric Loss: 0.6470
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7250
  avg_peak_to_mean_ratio: 7.0516
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 2.6943
val Heatmap Components:
  mse_loss: 1.1984
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 865.9645
  peak_enhancement_loss: 33.1550
  peak_to_second_ratio_loss: 0.2655
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2857.3025
Current learning rate: 0.000428

Epoch 22/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.65s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.79s/it]
train Loss: 2708.2400, Seg Loss: 0.4822, Heatmap Loss: 1804.6528, Geometric Loss: 0.9733
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7696
  avg_peak_to_mean_ratio: 14.7352
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.7321
  Overall Confidence Score: 4.5595
train Heatmap Components:
  mse_loss: 1.5620
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 760.8129
  peak_enhancement_loss: 28.5014
  peak_to_second_ratio_loss: 0.2777
  detection_rate_loss: 1.1868
  segmentation_guidance_loss: 0.0583
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 3068.8961, Seg Loss: 0.3747, Heatmap Loss: 2045.3026, Geometric Loss: 0.7093
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7210
  avg_peak_to_mean_ratio: 8.8321
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 3.1384
val Heatmap Components:
  mse_loss: 1.1044
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 39.6969
  peak_to_second_ratio_loss: 0.2855
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1095
Current learning rate: 0.000352

Epoch 23/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.67s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.75s/it]
train Loss: 2448.8797, Seg Loss: 0.4692, Heatmap Loss: 1631.8047, Geometric Loss: 0.8794
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0371
  avg_peak_to_mean_ratio: 64.1857
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.7857
  Overall Confidence Score: 17.0023
train Heatmap Components:
  mse_loss: 1.6138
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 575.0767
  peak_enhancement_loss: 43.2261
  peak_to_second_ratio_loss: 0.2957
  detection_rate_loss: 0.9209
  segmentation_guidance_loss: 0.0424
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 2907.2552, Seg Loss: 0.3741, Heatmap Loss: 1937.5391, Geometric Loss: 0.7156
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7772
  avg_peak_to_mean_ratio: 10.0109
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 3.4471
val Heatmap Components:
  mse_loss: 1.2176
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 895.7148
  peak_enhancement_loss: 35.7358
  peak_to_second_ratio_loss: 0.3059
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1413
Current learning rate: 0.000280

Epoch 24/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.73s/it]
train Loss: 2796.3587, Seg Loss: 0.5205, Heatmap Loss: 1863.4064, Geometric Loss: 0.9109
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9105
  avg_peak_to_mean_ratio: 9.4035
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.7857
  Overall Confidence Score: 3.2751
train Heatmap Components:
  mse_loss: 1.6168
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 815.9155
  peak_enhancement_loss: 35.9342
  peak_to_second_ratio_loss: 0.3239
  detection_rate_loss: 0.6352
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 2932.5322, Seg Loss: 0.3644, Heatmap Loss: 1954.4228, Geometric Loss: 0.6671
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8188
  avg_peak_to_mean_ratio: 12.9001
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 4.1798
val Heatmap Components:
  mse_loss: 1.2841
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 919.7800
  peak_enhancement_loss: 28.3637
  peak_to_second_ratio_loss: 0.3267
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0470
Current learning rate: 0.000214

Epoch 25/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.43s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.45s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.79s/it]
train Loss: 2351.5520, Seg Loss: 0.4963, Heatmap Loss: 1566.8279, Geometric Loss: 1.0176
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7117
  avg_peak_to_mean_ratio: 60.2324
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 0.7143
  Overall Confidence Score: 15.9147
train Heatmap Components:
  mse_loss: 1.3807
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 506.8683
  peak_enhancement_loss: 40.4375
  peak_to_second_ratio_loss: 0.3451
  detection_rate_loss: 1.6207
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 2995.1693, Seg Loss: 0.3602, Heatmap Loss: 1996.1918, Geometric Loss: 0.6518
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8114
  avg_peak_to_mean_ratio: 23.4680
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 6.8200
val Heatmap Components:
  mse_loss: 1.2282
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 951.1842
  peak_enhancement_loss: 38.0660
  peak_to_second_ratio_loss: 0.3491
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2386
Current learning rate: 0.000155

Epoch 26/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.65s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.83s/it]
train Loss: 2443.3726, Seg Loss: 0.5002, Heatmap Loss: 1628.1056, Geometric Loss: 0.8924
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9387
  avg_peak_to_mean_ratio: 9.4735
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 0.6964
  Overall Confidence Score: 3.2772
train Heatmap Components:
  mse_loss: 1.4682
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 573.7844
  peak_enhancement_loss: 34.6324
  peak_to_second_ratio_loss: 0.3665
  detection_rate_loss: 1.5903
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.51s/it] 
val Loss: 3067.4739, Seg Loss: 0.3654, Heatmap Loss: 2044.4002, Geometric Loss: 0.6354
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8685
  avg_peak_to_mean_ratio: 12.6877
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 4.1392
val Heatmap Components:
  mse_loss: 1.2206
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 37.4452
  peak_to_second_ratio_loss: 0.3689
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1006
Current learning rate: 0.000105

Epoch 27/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.62s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.88s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.92s/it]
train Loss: 2853.0451, Seg Loss: 0.5111, Heatmap Loss: 1901.2050, Geometric Loss: 0.9082
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7347
  avg_peak_to_mean_ratio: 3.9586
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.6607
  Overall Confidence Score: 1.8388
train Heatmap Components:
  mse_loss: 1.4420
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 603.1342
  peak_enhancement_loss: 46.0665
  peak_to_second_ratio_loss: 0.3915
  detection_rate_loss: 1.9581
  segmentation_guidance_loss: 0.2266
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.56s/it] 
val Loss: 2851.6629, Seg Loss: 0.3461, Heatmap Loss: 1900.5144, Geometric Loss: 0.6815
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8162
  avg_peak_to_mean_ratio: 17.7710
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 5.3969
val Heatmap Components:
  mse_loss: 1.1566
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 848.3707
  peak_enhancement_loss: 45.1620
  peak_to_second_ratio_loss: 0.3883
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2851.6629
Current learning rate: 0.000064

Epoch 28/40
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.29s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.61s/it]
train Loss: 2991.0513, Seg Loss: 0.4881, Heatmap Loss: 1993.2095, Geometric Loss: 0.9361
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7358
  avg_peak_to_mean_ratio: 6.9091
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 0.6786
  Overall Confidence Score: 2.5810
train Heatmap Components:
  mse_loss: 1.3502
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 687.7215
  peak_enhancement_loss: 54.1040
  peak_to_second_ratio_loss: 0.4189
  detection_rate_loss: 1.8099
  segmentation_guidance_loss: 0.3503
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 3043.3010, Seg Loss: 0.3587, Heatmap Loss: 2028.2701, Geometric Loss: 0.6714
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7928
  avg_peak_to_mean_ratio: 27.5381
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 7.8329
val Heatmap Components:
  avg_peak_value: 1.7928
  avg_peak_to_mean_ratio: 27.5381
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 7.8329
val Heatmap Components:
  Overall Confidence Score: 7.8329
val Heatmap Components:
  mse_loss: 1.0522
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 964.5461
  peak_enhancement_loss: 56.4405
  peak_to_second_ratio_loss: 0.4154
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000034

Epoch 29/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.06s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.80s/it]
train Loss: 2709.3627, Seg Loss: 0.4813, Heatmap Loss: 1805.4696, Geometric Loss: 0.8464
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7600
  avg_peak_to_mean_ratio: 23.7094
  avg_peak_to_second_ratio: 1.0039
  detection_rate: 0.6786
  Overall Confidence Score: 6.7880
train Heatmap Components:
  mse_loss: 1.3373
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 732.2773
  peak_enhancement_loss: 51.2992
  peak_to_second_ratio_loss: 0.4238
  detection_rate_loss: 1.7424
  segmentation_guidance_loss: 0.1299
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 2848.4992, Seg Loss: 0.3487, Heatmap Loss: 1898.4165, Geometric Loss: 0.6572
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8053
  avg_peak_to_mean_ratio: 16.4644
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 5.0675
val Heatmap Components:
  mse_loss: 1.1176
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 838.2656
  peak_enhancement_loss: 52.4144
  peak_to_second_ratio_loss: 0.4413
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2848.4992
Current learning rate: 0.000016

Epoch 30/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.63s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.92s/it]
train Loss: 2496.6673, Seg Loss: 0.4575, Heatmap Loss: 1663.6201, Geometric Loss: 0.9748
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0443
  avg_peak_to_mean_ratio: 9.9102
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 0.7857
  Overall Confidence Score: 3.4354
train Heatmap Components:
  mse_loss: 1.6307
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 612.6023
  peak_enhancement_loss: 34.8471
  peak_to_second_ratio_loss: 0.4591
  detection_rate_loss: 0.9566
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 3013.7298, Seg Loss: 0.3564, Heatmap Loss: 2008.5869, Geometric Loss: 0.6163
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8493
  avg_peak_to_mean_ratio: 15.9705
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 4.9551
val Heatmap Components:
  mse_loss: 1.1008
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 947.3773
  peak_enhancement_loss: 53.1296
  peak_to_second_ratio_loss: 0.4653
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.001000

Epoch 31/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.69s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.75s/it]
train Loss: 2606.8330, Seg Loss: 0.4713, Heatmap Loss: 1737.0358, Geometric Loss: 1.0102
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9659
  avg_peak_to_mean_ratio: 11.4010
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 0.7321
  Overall Confidence Score: 3.7748
train Heatmap Components:
  mse_loss: 1.5529
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 682.1475
  peak_enhancement_loss: 36.3683
  peak_to_second_ratio_loss: 0.4942
  detection_rate_loss: 1.1868
  segmentation_guidance_loss: 0.0298
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 3096.5548, Seg Loss: 0.3681, Heatmap Loss: 2063.7733, Geometric Loss: 0.6585
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7876
  avg_peak_to_mean_ratio: 25.0019
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 7.1975
val Heatmap Components:
  mse_loss: 1.0387
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 993.5086
  peak_enhancement_loss: 61.8349
  peak_to_second_ratio_loss: 0.4927
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000998

Epoch 32/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.37s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.70s/it]
train Loss: 2438.5338, Seg Loss: 0.4598, Heatmap Loss: 1624.8960, Geometric Loss: 0.9125
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8215
  avg_peak_to_mean_ratio: 8.0040
  avg_peak_to_second_ratio: 1.0022
  detection_rate: 0.7500
  Overall Confidence Score: 2.8944
train Heatmap Components:
  mse_loss: 1.5224
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 558.9754
  peak_enhancement_loss: 47.5443
  peak_to_second_ratio_loss: 0.4754
  detection_rate_loss: 1.1458
  segmentation_guidance_loss: 0.2783
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.51s/it] 
val Loss: 2813.2927, Seg Loss: 0.3625, Heatmap Loss: 1874.8802, Geometric Loss: 0.7625
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9676
  avg_peak_to_mean_ratio: 6.2929
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 2.5652
val Heatmap Components:
  mse_loss: 1.6643
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 817.4230
  peak_enhancement_loss: 48.2988
  peak_to_second_ratio_loss: 0.4926
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0529
New best model saved with loss: 2813.2927
Current learning rate: 0.000994

Epoch 33/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.20s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.46s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 2945.9528, Seg Loss: 0.4810, Heatmap Loss: 1963.1678, Geometric Loss: 0.9002
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8817
  avg_peak_to_mean_ratio: 15.9710
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.7500
  Overall Confidence Score: 4.9008
train Heatmap Components:
  mse_loss: 1.5315
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 654.7642
  peak_enhancement_loss: 62.9546
  peak_to_second_ratio_loss: 0.4882
  detection_rate_loss: 1.0029
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 2899.4464, Seg Loss: 0.3488, Heatmap Loss: 1932.3858, Geometric Loss: 0.6486
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9005
  avg_peak_to_mean_ratio: 2.7824
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 1.0000
  Overall Confidence Score: 1.6709
val Heatmap Components:
  mse_loss: 1.9106
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 874.4829
  peak_enhancement_loss: 48.6657
  peak_to_second_ratio_loss: 0.4884
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000986

Epoch 34/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.65s/it]
train Loss: 2514.8556, Seg Loss: 0.4991, Heatmap Loss: 1675.7144, Geometric Loss: 0.9812
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4147
  avg_peak_to_mean_ratio: 8.1756
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.6429
  Overall Confidence Score: 2.8085
train Heatmap Components:
  mse_loss: 1.3119
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 363.6416
  peak_enhancement_loss: 57.5214
  peak_to_second_ratio_loss: 0.4920
  detection_rate_loss: 2.0348
  segmentation_guidance_loss: 0.5051
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.56s/it] 
val Loss: 2690.9973, Seg Loss: 0.3510, Heatmap Loss: 1793.3847, Geometric Loss: 0.7115
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7059
  avg_peak_to_mean_ratio: 3.8422
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 1.0000
  Overall Confidence Score: 1.8873
val Heatmap Components:
  mse_loss: 1.3611
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 740.7884
  peak_enhancement_loss: 43.8498
  peak_to_second_ratio_loss: 0.4924
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2690.9973
Current learning rate: 0.000976

Epoch 35/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.90s/it]
train Loss: 3009.0001, Seg Loss: 0.4481, Heatmap Loss: 2005.1426, Geometric Loss: 1.0479
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6175
  avg_peak_to_mean_ratio: 12.2476
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.7143
  Overall Confidence Score: 3.8951
train Heatmap Components:
  mse_loss: 1.5631
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 949.9730
  peak_enhancement_loss: 35.1795
  peak_to_second_ratio_loss: 0.4870
  detection_rate_loss: 1.3903
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.60s/it] 
val Loss: 2946.1583, Seg Loss: 0.3405, Heatmap Loss: 1963.4968, Geometric Loss: 0.7156
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6688
  avg_peak_to_mean_ratio: 10.5783
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 1.0000
  Overall Confidence Score: 3.5619
val Heatmap Components:
  mse_loss: 1.2098
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 918.1135
  peak_enhancement_loss: 36.7605
  peak_to_second_ratio_loss: 0.4942
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000962

Epoch 36/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:11,  5.54s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 2539.1441, Seg Loss: 0.4254, Heatmap Loss: 1692.0361, Geometric Loss: 0.8306
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7544
  avg_peak_to_mean_ratio: 47.8452
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.7857
  Overall Confidence Score: 12.8466
train Heatmap Components:
  mse_loss: 1.5872
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 640.4895
  peak_enhancement_loss: 36.6033
  peak_to_second_ratio_loss: 0.4755
  detection_rate_loss: 0.7780
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 3014.2807, Seg Loss: 0.3497, Heatmap Loss: 2008.8830, Geometric Loss: 0.7581
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7105
  avg_peak_to_mean_ratio: 16.5621
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 1.0000
  Overall Confidence Score: 5.0683
val Heatmap Components:
  mse_loss: 1.3953
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 986.2351
  peak_enhancement_loss: 13.8766
  peak_to_second_ratio_loss: 0.4917
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000946

Epoch 37/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.13s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.10s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 3059.7615, Seg Loss: 0.4015, Heatmap Loss: 2039.1327, Geometric Loss: 0.8260
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5135
  avg_peak_to_mean_ratio: 21.5594
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.6429
  Overall Confidence Score: 6.1792
train Heatmap Components:
  mse_loss: 1.3377
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 742.4613
  peak_enhancement_loss: 43.0934
  peak_to_second_ratio_loss: 0.4927
  detection_rate_loss: 2.0348
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.52s/it] 
val Loss: 2996.4796, Seg Loss: 0.2885, Heatmap Loss: 1997.0150, Geometric Loss: 0.8356
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9445
  avg_peak_to_mean_ratio: 13.1433
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 1.0000
  Overall Confidence Score: 4.2720
val Heatmap Components:
  mse_loss: 2.0798
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 960.2843
  peak_enhancement_loss: 27.2167
  peak_to_second_ratio_loss: 0.4956
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000927

Epoch 38/40
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.35s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.64s/it]
train Loss: 2323.5954, Seg Loss: 0.4255, Heatmap Loss: 1548.3295, Geometric Loss: 0.8446
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6232
  avg_peak_to_mean_ratio: 4.9036
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 0.6607
  Overall Confidence Score: 2.0471
train Heatmap Components:
  mse_loss: 1.4321
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 506.4691
  peak_enhancement_loss: 18.2102
  peak_to_second_ratio_loss: 0.4940
  detection_rate_loss: 1.8509
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 3002.1321, Seg Loss: 0.2898, Heatmap Loss: 2000.7801, Geometric Loss: 0.8402
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1282
  avg_peak_to_mean_ratio: 1.6591
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 1.4469
val Heatmap Components:
  mse_loss: 3.6530
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 950.0111
  peak_enhancement_loss: 39.6786
  peak_to_second_ratio_loss: 0.4958
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000905

Epoch 39/40
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.50s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.73s/it]
train Loss: 2971.8137, Seg Loss: 0.4235, Heatmap Loss: 1980.5046, Geometric Loss: 0.7916
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7777
  avg_peak_to_mean_ratio: 5.1207
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.7857
  Overall Confidence Score: 2.1711
train Heatmap Components:
  mse_loss: 1.6189
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 922.9007
  peak_enhancement_loss: 38.7800
  peak_to_second_ratio_loss: 0.4844
  detection_rate_loss: 1.2423
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.53s/it] 
val Loss: 2906.3438, Seg Loss: 0.3112, Heatmap Loss: 1936.9765, Geometric Loss: 0.7097
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8978
  avg_peak_to_mean_ratio: 24.3616
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 7.0650
val Heatmap Components:
  mse_loss: 2.1808
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 898.6850
  peak_enhancement_loss: 28.7097
  peak_to_second_ratio_loss: 0.4934
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000881

Epoch 40/40
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.27s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.46s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.77s/it]
train Loss: 2801.6680, Seg Loss: 0.4382, Heatmap Loss: 1867.0385, Geometric Loss: 0.8400
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5360
  avg_peak_to_mean_ratio: 5.3386
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.6429
  Overall Confidence Score: 2.1297
train Heatmap Components:
  mse_loss: 1.4908
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 813.1201
  peak_enhancement_loss: 26.7243
  peak_to_second_ratio_loss: 0.4759
  detection_rate_loss: 2.3205
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.54s/it] 
val Loss: 3054.5272, Seg Loss: 0.3002, Heatmap Loss: 2035.8123, Geometric Loss: 0.6356
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6405
  avg_peak_to_mean_ratio: 6.9460
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 1.0000
  Overall Confidence Score: 2.6469
val Heatmap Components:
  mse_loss: 1.4411
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 27.0797
  peak_to_second_ratio_loss: 0.4861
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000855

Training complete in 20m 44s
Best val loss: 2690.9973

=== Phase 2: Fine-tune with balanced weights ===
Saving outputs to v5.1 folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Learning rate scheduler with warm restarts initialized
Epoch 1/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.4957, Heatmap Loss: 1624.4867, Geometric Loss: 0.7843, Total: 2437.8530
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.28s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
Batch 1, train - Seg Loss: 0.4405, Heatmap Loss: 1490.5421, Geometric Loss: 0.9525, Total: 2237.0156
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 2, train - Seg Loss: 0.4210, Heatmap Loss: 1587.7726, Geometric Loss: 0.7432, Total: 2382.6746
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.82s/it]
train Loss: 2309.0954, Seg Loss: 0.4364, Heatmap Loss: 1538.6524, Geometric Loss: 0.8506
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7538
  avg_peak_to_mean_ratio: 11.1390
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.7143
  Overall Confidence Score: 3.6519
train Heatmap Components:
  mse_loss: 1.6414
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 890.2325
  peak_enhancement_loss: 20.3313
  peak_to_second_ratio_loss: 0.0184
  detection_rate_loss: 1.5850
  segmentation_guidance_loss: 0.3009
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.2470, Heatmap Loss: 1656.5176, Geometric Loss: 0.7751, Total: 2485.6436
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.56s/it]Batch 1, val - Seg Loss: 0.2119, Heatmap Loss: 1642.5299, Geometric Loss: 0.8130, Total: 2464.6572
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.66s/it]Batch 2, val - Seg Loss: 0.1841, Heatmap Loss: 1635.7004, Geometric Loss: 0.6012, Total: 2454.2158
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it]
val Loss: 2465.3734, Seg Loss: 0.2055, Heatmap Loss: 1643.0534, Geometric Loss: 0.7346
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.5786
  avg_peak_to_mean_ratio: 1.0429
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 1.0000
  Overall Confidence Score: 1.1556
val Heatmap Components:
  mse_loss: 1.3921
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 51.8483
  peak_to_second_ratio_loss: 0.0183
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2465.3734
Current learning rate: 0.000488

Epoch 2/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
Batch 0, train - Seg Loss: 0.4614, Heatmap Loss: 1338.9762, Geometric Loss: 1.0047, Total: 2009.7296
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.46s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
Batch 1, train - Seg Loss: 0.3905, Heatmap Loss: 1158.0239, Geometric Loss: 0.9161, Total: 1738.1593
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.39s/it]Batch 2, train - Seg Loss: 0.4527, Heatmap Loss: 1269.7278, Geometric Loss: 0.8487, Total: 1905.7234
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.85s/it]
train Loss: 1831.1395, Seg Loss: 0.4179, Heatmap Loss: 1219.9889, Geometric Loss: 0.9227
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9581
  avg_peak_to_mean_ratio: 134.2788
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.7321
  Overall Confidence Score: 34.4924
train Heatmap Components:
  mse_loss: 1.5685
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 496.4338
  peak_enhancement_loss: 18.3208
  peak_to_second_ratio_loss: 0.0219
  detection_rate_loss: 1.1868
  segmentation_guidance_loss: 0.3090
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.2848, Heatmap Loss: 1655.8157, Geometric Loss: 0.5541, Total: 2484.4519
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.64s/it]Batch 1, val - Seg Loss: 0.2645, Heatmap Loss: 1642.7700, Geometric Loss: 0.6530, Total: 2464.9419
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.69s/it]Batch 2, val - Seg Loss: 0.2550, Heatmap Loss: 1629.1698, Geometric Loss: 0.5226, Total: 2444.4275
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it]
val Loss: 2456.7971, Seg Loss: 0.2664, Heatmap Loss: 1637.3711, Geometric Loss: 0.5925
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7696
  avg_peak_to_mean_ratio: 2.1244
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 1.0000
  Overall Confidence Score: 1.4737
val Heatmap Components:
  mse_loss: 1.3150
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 44.7659
  peak_to_second_ratio_loss: 0.0243
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2456.7971
Current learning rate: 0.000453

Epoch 3/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
Batch 0, train - Seg Loss: 0.4397, Heatmap Loss: 999.9228, Geometric Loss: 0.7031, Total: 1500.8864
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.53s/it]Batch 1, train - Seg Loss: 0.3417, Heatmap Loss: 1284.3873, Geometric Loss: 0.8505, Total: 1927.6031
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.58s/it]Batch 2, train - Seg Loss: 0.3873, Heatmap Loss: 1198.3931, Geometric Loss: 0.8048, Total: 1798.6207
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.39s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 1723.9594, Seg Loss: 0.3872, Heatmap Loss: 1148.6319, Geometric Loss: 0.7806
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6751
  avg_peak_to_mean_ratio: 55.9284
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 0.6786
  Overall Confidence Score: 14.8206
train Heatmap Components:
  mse_loss: 1.4674
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 387.8209
  peak_enhancement_loss: 33.7978
  peak_to_second_ratio_loss: 0.0302
  detection_rate_loss: 1.8813
  segmentation_guidance_loss: 0.1071
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.3115, Heatmap Loss: 1663.9077, Geometric Loss: 0.7895, Total: 2496.8047
 25%|██████████████████████                                                                  | 1/4 [00:02<00:07,  2.59s/it]Batch 1, val - Seg Loss: 0.2919, Heatmap Loss: 1645.4214, Geometric Loss: 0.8928, Total: 2469.1382
 50%|████████████████████████████████████████████                                            | 2/4 [00:05<00:05,  2.65s/it]Batch 2, val - Seg Loss: 0.2812, Heatmap Loss: 1636.4784, Geometric Loss: 0.6735, Total: 2455.5376
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it]
val Loss: 2466.3342, Seg Loss: 0.2886, Heatmap Loss: 1643.6332, Geometric Loss: 0.7447
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7851
  avg_peak_to_mean_ratio: 113.8694
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 29.4138
val Heatmap Components:
  mse_loss: 1.2975
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 52.5329
  peak_to_second_ratio_loss: 0.0309
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000398

Epoch 4/80
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.44s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.75s/it]
train Loss: 1977.5168, Seg Loss: 0.4028, Heatmap Loss: 1317.5876, Geometric Loss: 0.9158
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9773
  avg_peak_to_mean_ratio: 26.6022
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.7679
  Overall Confidence Score: 7.5870
train Heatmap Components:
  mse_loss: 1.6321
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 615.9147
  peak_enhancement_loss: 21.9670
  peak_to_second_ratio_loss: 0.0348
  detection_rate_loss: 0.9619
  segmentation_guidance_loss: 0.3285
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.71s/it] 
val Loss: 2407.5905, Seg Loss: 0.2985, Heatmap Loss: 1604.4725, Geometric Loss: 0.7292
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0300
  avg_peak_to_mean_ratio: 11.2844
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 3.8287
val Heatmap Components:
  mse_loss: 1.8683
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 962.9143
  peak_enhancement_loss: 39.8645
  peak_to_second_ratio_loss: 0.0381
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2407.5905
Current learning rate: 0.000329

Epoch 5/80
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.58s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.86s/it]
train Loss: 2088.7544, Seg Loss: 0.4479, Heatmap Loss: 1391.6843, Geometric Loss: 0.9753
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6561
  avg_peak_to_mean_ratio: 3.9742
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 0.7321
  Overall Confidence Score: 1.8410
train Heatmap Components:
  mse_loss: 1.3446
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 469.2142
  peak_enhancement_loss: 46.0276
  peak_to_second_ratio_loss: 0.0405
  detection_rate_loss: 1.2225
  segmentation_guidance_loss: 0.1336
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 2399.5771, Seg Loss: 0.2920, Heatmap Loss: 1599.1735, Geometric Loss: 0.6559
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0837
  avg_peak_to_mean_ratio: 13.8623
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 4.4866
val Heatmap Components:
  mse_loss: 2.0449
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 966.2844
  peak_enhancement_loss: 29.5522
  peak_to_second_ratio_loss: 0.0459
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2399.5771
Current learning rate: 0.000253

Epoch 6/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.54s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.91s/it]
train Loss: 2392.0593, Seg Loss: 0.4284, Heatmap Loss: 1593.9362, Geometric Loss: 0.9083
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9176
  avg_peak_to_mean_ratio: 3.5876
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 0.7857
  Overall Confidence Score: 1.8231
train Heatmap Components:
  mse_loss: 1.5988
  separation_loss: 1000.0000
  peak_separation_loss: 142.8571
  edge_suppression_loss: 840.6798
  peak_enhancement_loss: 34.8039
  peak_to_second_ratio_loss: 0.0520
  detection_rate_loss: 1.0995
  segmentation_guidance_loss: 0.1461
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 2200.3225, Seg Loss: 0.2887, Heatmap Loss: 1466.3457, Geometric Loss: 0.6441
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9603
  avg_peak_to_mean_ratio: 266.7624
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 67.6808
val Heatmap Components:
  mse_loss: 1.8528
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 799.7372
  peak_enhancement_loss: 30.2047
  peak_to_second_ratio_loss: 0.0539
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
New best model saved with loss: 2200.3225
Current learning rate: 0.000176

Epoch 7/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.53s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.47s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.84s/it]
train Loss: 2128.8413, Seg Loss: 0.3634, Heatmap Loss: 1418.5261, Geometric Loss: 0.8608
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8179
  avg_peak_to_mean_ratio: 6.3317
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 0.6786
  Overall Confidence Score: 2.4571
train Heatmap Components:
  mse_loss: 1.3468
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 489.7040
  peak_enhancement_loss: 56.7560
  peak_to_second_ratio_loss: 0.0605
  detection_rate_loss: 1.5956
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it] 
val Loss: 2453.6920, Seg Loss: 0.2938, Heatmap Loss: 1635.2339, Geometric Loss: 0.6843
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9617
  avg_peak_to_mean_ratio: 60.6046
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 16.1417
val Heatmap Components:
  mse_loss: 1.7596
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 41.0570
  peak_to_second_ratio_loss: 0.0629
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000107

Epoch 8/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.45s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.69s/it]
train Loss: 1903.8777, Seg Loss: 0.3879, Heatmap Loss: 1268.4651, Geometric Loss: 0.9903
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8034
  avg_peak_to_mean_ratio: 18.3733
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.7143
  Overall Confidence Score: 5.4730
train Heatmap Components:
  mse_loss: 1.5079
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 542.6967
  peak_enhancement_loss: 30.6920
  peak_to_second_ratio_loss: 0.0713
  detection_rate_loss: 1.3707
  segmentation_guidance_loss: 0.4534
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it] 
val Loss: 2320.7886, Seg Loss: 0.2930, Heatmap Loss: 1546.6284, Geometric Loss: 0.6913
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8628
  avg_peak_to_mean_ratio: 13.5168
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 4.3450
val Heatmap Components:
  mse_loss: 1.4749
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 883.3780
  peak_enhancement_loss: 47.1564
  peak_to_second_ratio_loss: 0.0726
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000052

Epoch 9/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.33s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 2173.6603, Seg Loss: 0.3687, Heatmap Loss: 1448.3659, Geometric Loss: 0.9285
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6786
  avg_peak_to_mean_ratio: 12.9248
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.6786
  Overall Confidence Score: 4.0707
train Heatmap Components:
  mse_loss: 1.3501
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 544.6213
  peak_enhancement_loss: 39.7788
  peak_to_second_ratio_loss: 0.0803
  detection_rate_loss: 1.4527
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 2282.1084, Seg Loss: 0.3057, Heatmap Loss: 1520.7932, Geometric Loss: 0.7661
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8730
  avg_peak_to_mean_ratio: 24.6649
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 7.1346
val Heatmap Components:
  mse_loss: 1.4528
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 842.0626
  peak_enhancement_loss: 56.0691
  peak_to_second_ratio_loss: 0.0835
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000017

Epoch 10/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.82s/it]
train Loss: 1808.3238, Seg Loss: 0.3699, Heatmap Loss: 1204.8570, Geometric Loss: 0.8354
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8165
  avg_peak_to_mean_ratio: 4.2549
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.7500
  Overall Confidence Score: 1.9555
train Heatmap Components:
  mse_loss: 1.4945
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 464.8739
  peak_enhancement_loss: 30.6300
  peak_to_second_ratio_loss: 0.0937
  detection_rate_loss: 1.1458
  segmentation_guidance_loss: 0.1955
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 2342.6379, Seg Loss: 0.3079, Heatmap Loss: 1561.1391, Geometric Loss: 0.7766
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7857
  avg_peak_to_mean_ratio: 9.2774
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 1.0000
  Overall Confidence Score: 3.2659
val Heatmap Components:
  mse_loss: 1.1441
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 897.7131
  peak_enhancement_loss: 51.1019
  peak_to_second_ratio_loss: 0.0943
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000500

Epoch 11/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:10,  5.50s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.73s/it]
train Loss: 1783.5636, Seg Loss: 0.4288, Heatmap Loss: 1188.3271, Geometric Loss: 0.8051
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5239
  avg_peak_to_mean_ratio: 2.6482
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 0.6429
  Overall Confidence Score: 1.4542
train Heatmap Components:
  mse_loss: 1.3557
  separation_loss: 1000.0000
  peak_separation_loss: 27.5605
  edge_suppression_loss: 394.4163
  peak_enhancement_loss: 55.9852
  peak_to_second_ratio_loss: 0.1011
  detection_rate_loss: 1.8205
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 2202.9487, Seg Loss: 0.2793, Heatmap Loss: 1468.0361, Geometric Loss: 0.7690
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7120
  avg_peak_to_mean_ratio: 17.8870
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 5.4002
val Heatmap Components:
  mse_loss: 0.8981
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 770.2476
  peak_enhancement_loss: 62.3913
  peak_to_second_ratio_loss: 0.1027
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000497

Epoch 12/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.27s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.73s/it]
train Loss: 1591.5344, Seg Loss: 0.3717, Heatmap Loss: 1060.2955, Geometric Loss: 0.8991
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5550
  avg_peak_to_mean_ratio: 5.5246
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.6429
  Overall Confidence Score: 2.1808
train Heatmap Components:
  mse_loss: 1.3059
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 287.1324
  peak_enhancement_loss: 24.4631
  peak_to_second_ratio_loss: 0.1152
  detection_rate_loss: 1.6777
  segmentation_guidance_loss: 0.1156
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 2276.8147, Seg Loss: 0.2705, Heatmap Loss: 1517.2336, Geometric Loss: 0.8674
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6946
  avg_peak_to_mean_ratio: 1.1193
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 1.2038
val Heatmap Components:
  mse_loss: 0.7864
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 820.0375
  peak_enhancement_loss: 74.0831
  peak_to_second_ratio_loss: 0.1151
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000488

Epoch 13/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.33s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.58s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.79s/it]
train Loss: 1655.4260, Seg Loss: 0.3818, Heatmap Loss: 1102.8653, Geometric Loss: 0.9329
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.5476
  avg_peak_to_mean_ratio: 20.8071
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.6250
  Overall Confidence Score: 5.9951
train Heatmap Components:
  mse_loss: 1.1731
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 298.9847
  peak_enhancement_loss: 63.9207
  peak_to_second_ratio_loss: 0.1281
  detection_rate_loss: 1.8973
  segmentation_guidance_loss: 0.4004
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 1778.7609, Seg Loss: 0.2785, Heatmap Loss: 1185.3130, Geometric Loss: 0.6410
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.7967
  avg_peak_to_mean_ratio: 5.7539
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 1.0000
  Overall Confidence Score: 2.3878
val Heatmap Components:
  mse_loss: 1.6599
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 432.3622
  peak_enhancement_loss: 45.2979
  peak_to_second_ratio_loss: 0.1335
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1263
New best model saved with loss: 1778.7609
Current learning rate: 0.000473

Epoch 14/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.01s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 1825.7927, Seg Loss: 0.3703, Heatmap Loss: 1216.4517, Geometric Loss: 0.9310
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6235
  avg_peak_to_mean_ratio: 9.2791
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.6429
  Overall Confidence Score: 3.1365
train Heatmap Components:
  mse_loss: 1.3280
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 461.5175
  peak_enhancement_loss: 42.5291
  peak_to_second_ratio_loss: 0.1432
  detection_rate_loss: 2.0705
  segmentation_guidance_loss: 0.0680
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 1873.0079, Seg Loss: 0.2995, Heatmap Loss: 1248.1049, Geometric Loss: 0.6889
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.4309
  avg_peak_to_mean_ratio: 31.8447
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 1.0000
  Overall Confidence Score: 8.8191
val Heatmap Components:
  mse_loss: 0.8277
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 495.6111
  peak_enhancement_loss: 61.4584
  peak_to_second_ratio_loss: 0.1462
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1062
Current learning rate: 0.000453

Epoch 15/80
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.37s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 1838.1728, Seg Loss: 0.4025, Heatmap Loss: 1224.8144, Geometric Loss: 0.6858
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1377
  avg_peak_to_mean_ratio: 27.2577
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.8571
  Overall Confidence Score: 7.8134
train Heatmap Components:
  mse_loss: 1.6916
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 490.7523
  peak_enhancement_loss: 31.3964
  peak_to_second_ratio_loss: 0.1592
  detection_rate_loss: 0.6139
  segmentation_guidance_loss: 0.4945
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1669.0911, Seg Loss: 0.3317, Heatmap Loss: 1112.1237, Geometric Loss: 0.7173
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.4281
  avg_peak_to_mean_ratio: 64.5557
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 1.0000
  Overall Confidence Score: 16.9961
val Heatmap Components:
  mse_loss: 0.7323
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 311.1883
  peak_enhancement_loss: 75.8530
  peak_to_second_ratio_loss: 0.1619
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0932
New best model saved with loss: 1669.0911
Current learning rate: 0.000428

Epoch 16/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.74s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:17<00:05,  5.81s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.15s/it]
train Loss: 1743.9117, Seg Loss: 0.3994, Heatmap Loss: 1161.8941, Geometric Loss: 0.8390
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7479
  avg_peak_to_mean_ratio: 6.7064
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 0.7321
  Overall Confidence Score: 2.5467
train Heatmap Components:
  mse_loss: 1.3930
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 398.2627
  peak_enhancement_loss: 42.1361
  peak_to_second_ratio_loss: 0.1768
  detection_rate_loss: 1.1868
  segmentation_guidance_loss: 0.3201
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.04s/it] 
val Loss: 1627.2556, Seg Loss: 0.3263, Heatmap Loss: 1084.1986, Geometric Loss: 0.7891
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.6990
  avg_peak_to_mean_ratio: 3.7739
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 1.0000
  Overall Confidence Score: 1.8684
val Heatmap Components:
  mse_loss: 1.3172
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 265.4387
  peak_enhancement_loss: 85.8197
  peak_to_second_ratio_loss: 0.1768
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0713
New best model saved with loss: 1627.2556
Current learning rate: 0.000398

Epoch 17/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:06<00:18,  6.27s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:12<00:12,  6.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:19<00:06,  6.49s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:22<00:00,  5.61s/it]
train Loss: 1821.5875, Seg Loss: 0.4221, Heatmap Loss: 1213.6390, Geometric Loss: 0.8837
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7474
  avg_peak_to_mean_ratio: 5.3828
  avg_peak_to_second_ratio: 1.0075
  detection_rate: 0.7143
  Overall Confidence Score: 2.2130
train Heatmap Components:
  mse_loss: 1.3710
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 450.1661
  peak_enhancement_loss: 55.1980
  peak_to_second_ratio_loss: 0.1837
  detection_rate_loss: 1.2278
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.02s/it] 
val Loss: 1816.0169, Seg Loss: 0.2993, Heatmap Loss: 1210.0794, Geometric Loss: 0.7480
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8212
  avg_peak_to_mean_ratio: 26.9053
  avg_peak_to_second_ratio: 1.0003
  detection_rate: 1.0000
  Overall Confidence Score: 7.6817
val Heatmap Components:
  mse_loss: 1.4920
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 445.7675
  peak_enhancement_loss: 62.4221
  peak_to_second_ratio_loss: 0.1945
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0605
Current learning rate: 0.000365

Epoch 18/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:06<00:19,  6.54s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:12<00:11,  5.95s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:18<00:05,  5.96s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.17s/it]
train Loss: 1684.5312, Seg Loss: 0.4067, Heatmap Loss: 1122.3116, Geometric Loss: 0.8213
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8490
  avg_peak_to_mean_ratio: 32.7300
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.6964
  Overall Confidence Score: 9.0690
train Heatmap Components:
  mse_loss: 1.4136
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 341.8395
  peak_enhancement_loss: 45.3467
  peak_to_second_ratio_loss: 0.2054
  detection_rate_loss: 1.6747
  segmentation_guidance_loss: 0.4809
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 1881.1385, Seg Loss: 0.2778, Heatmap Loss: 1253.4908, Geometric Loss: 0.7806
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8928
  avg_peak_to_mean_ratio: 16.6187
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 1.0000
  Overall Confidence Score: 5.1281
val Heatmap Components:
  mse_loss: 1.4653
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 496.5353
  peak_enhancement_loss: 65.8832
  peak_to_second_ratio_loss: 0.2091
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000329

Epoch 19/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.23s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.38s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 1540.1991, Seg Loss: 0.3860, Heatmap Loss: 1026.0633, Geometric Loss: 0.8976
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6292
  avg_peak_to_mean_ratio: 7.1473
  avg_peak_to_second_ratio: 1.0027
  detection_rate: 0.5536
  Overall Confidence Score: 2.5832
train Heatmap Components:
  mse_loss: 1.1313
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 192.0656
  peak_enhancement_loss: 67.6273
  peak_to_second_ratio_loss: 0.2220
  detection_rate_loss: 2.9916
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 1863.8040, Seg Loss: 0.2803, Heatmap Loss: 1241.9732, Geometric Loss: 0.7050
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0187
  avg_peak_to_mean_ratio: 3.8139
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 1.0000
  Overall Confidence Score: 1.9583
val Heatmap Components:
  mse_loss: 1.8781
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 483.6572
  peak_enhancement_loss: 63.6411
  peak_to_second_ratio_loss: 0.2256
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000291

Epoch 20/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.10s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.70s/it]
train Loss: 1590.3095, Seg Loss: 0.3857, Heatmap Loss: 1059.4933, Geometric Loss: 0.8549
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8365
  avg_peak_to_mean_ratio: 13.5899
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.6607
  Overall Confidence Score: 4.2720
train Heatmap Components:
  mse_loss: 1.2805
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 242.0200
  peak_enhancement_loss: 65.3640
  peak_to_second_ratio_loss: 0.2419
  detection_rate_loss: 1.9581
  segmentation_guidance_loss: 0.0644
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 1987.2154, Seg Loss: 0.3007, Heatmap Loss: 1324.2420, Geometric Loss: 0.6896
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8562
  avg_peak_to_mean_ratio: 32.1396
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 1.0000
  Overall Confidence Score: 8.9992
val Heatmap Components:
  mse_loss: 1.1844
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 581.9197
  peak_enhancement_loss: 68.8762
  peak_to_second_ratio_loss: 0.2421
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
Current learning rate: 0.000253

Epoch 21/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.68s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 1578.9485, Seg Loss: 0.3808, Heatmap Loss: 1051.9145, Geometric Loss: 0.8698
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0910
  avg_peak_to_mean_ratio: 32.0389
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 0.6786
  Overall Confidence Score: 8.9524
train Heatmap Components:
  mse_loss: 1.4367
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 248.1785
  peak_enhancement_loss: 51.6718
  peak_to_second_ratio_loss: 0.2593
  detection_rate_loss: 1.5956
  segmentation_guidance_loss: 0.0174
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1777.6397, Seg Loss: 0.2803, Heatmap Loss: 1184.5414, Geometric Loss: 0.6841
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0437
  avg_peak_to_mean_ratio: 208.3920
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 1.0000
  Overall Confidence Score: 53.1091
val Heatmap Components:
  mse_loss: 1.2617
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 419.9469
  peak_enhancement_loss: 55.8553
  peak_to_second_ratio_loss: 0.2628
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0065
Current learning rate: 0.000214

Epoch 22/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.52s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.45s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.83s/it]
train Loss: 1487.4952, Seg Loss: 0.3762, Heatmap Loss: 990.9531, Geometric Loss: 0.8619
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7461
  avg_peak_to_mean_ratio: 27.6001
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.6429
  Overall Confidence Score: 7.7474
train Heatmap Components:
  mse_loss: 1.3339
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 163.5749
  peak_enhancement_loss: 58.2810
  peak_to_second_ratio_loss: 0.2808
  detection_rate_loss: 1.8205
  segmentation_guidance_loss: 0.1492
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.64s/it] 
val Loss: 1719.4346, Seg Loss: 0.3037, Heatmap Loss: 1145.7161, Geometric Loss: 0.6960
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1461
  avg_peak_to_mean_ratio: 12.7978
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 1.0000
  Overall Confidence Score: 4.2362
val Heatmap Components:
  mse_loss: 1.3119
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 364.9499
  peak_enhancement_loss: 61.8804
  peak_to_second_ratio_loss: 0.2817
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0818
Current learning rate: 0.000176

Epoch 23/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.30s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 1453.5433, Seg Loss: 0.3903, Heatmap Loss: 968.2375, Geometric Loss: 0.9959
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9595
  avg_peak_to_mean_ratio: 16.5287
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 0.6964
  Overall Confidence Score: 5.0463
train Heatmap Components:
  mse_loss: 1.3774
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 158.1935
  peak_enhancement_loss: 35.8828
  peak_to_second_ratio_loss: 0.2995
  detection_rate_loss: 1.5903
  segmentation_guidance_loss: 0.4350
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1582.7448, Seg Loss: 0.3133, Heatmap Loss: 1054.5960, Geometric Loss: 0.6718
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0003
  avg_peak_to_mean_ratio: 34.2673
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 9.5672
val Heatmap Components:
  mse_loss: 1.3268
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 259.5690
  peak_enhancement_loss: 53.1452
  peak_to_second_ratio_loss: 0.2958
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0933
New best model saved with loss: 1582.7448
Current learning rate: 0.000140

Epoch 24/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:04<00:14,  4.97s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 1459.8518, Seg Loss: 0.4003, Heatmap Loss: 972.4917, Geometric Loss: 0.8924
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6465
  avg_peak_to_mean_ratio: 11.5986
  avg_peak_to_second_ratio: 1.0004
  detection_rate: 0.5893
  Overall Confidence Score: 3.7087
train Heatmap Components:
  mse_loss: 1.0903
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 150.8614
  peak_enhancement_loss: 43.3445
  peak_to_second_ratio_loss: 0.3244
  detection_rate_loss: 2.4436
  segmentation_guidance_loss: 0.3831
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1470.1002, Seg Loss: 0.3056, Heatmap Loss: 979.5163, Geometric Loss: 0.6501
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0224
  avg_peak_to_mean_ratio: 118.4997
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 1.0000
  Overall Confidence Score: 30.6308
val Heatmap Components:
  mse_loss: 1.2355
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 155.1542
  peak_enhancement_loss: 63.5357
  peak_to_second_ratio_loss: 0.3178
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1004
New best model saved with loss: 1470.1002
Current learning rate: 0.000107

Epoch 25/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 1487.0654, Seg Loss: 0.4258, Heatmap Loss: 990.5768, Geometric Loss: 0.9680
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.9500
  avg_peak_to_mean_ratio: 15.7778
  avg_peak_to_second_ratio: 1.0028
  detection_rate: 0.6429
  Overall Confidence Score: 4.8434
train Heatmap Components:
  mse_loss: 1.5328
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 197.2602
  peak_enhancement_loss: 20.3968
  peak_to_second_ratio_loss: 0.3421
  detection_rate_loss: 2.1864
  segmentation_guidance_loss: 0.3771
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 1465.0472, Seg Loss: 0.2962, Heatmap Loss: 976.1498, Geometric Loss: 0.6579
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9690
  avg_peak_to_mean_ratio: 7.7480
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 0.9688
  Overall Confidence Score: 2.9217
val Heatmap Components:
  mse_loss: 1.1554
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 151.3228
  peak_enhancement_loss: 61.9301
  peak_to_second_ratio_loss: 0.3426
  detection_rate_loss: 0.1707
  segmentation_guidance_loss: 0.0750
New best model saved with loss: 1465.0472
Current learning rate: 0.000077

Epoch 26/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.77s/it]
train Loss: 1552.7844, Seg Loss: 0.3899, Heatmap Loss: 1034.4634, Geometric Loss: 0.8744
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0767
  avg_peak_to_mean_ratio: 188.4729
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 0.6964
  Overall Confidence Score: 48.0619
train Heatmap Components:
  mse_loss: 1.3107
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 224.6021
  peak_enhancement_loss: 50.5873
  peak_to_second_ratio_loss: 0.3562
  detection_rate_loss: 1.6974
  segmentation_guidance_loss: 0.6347
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.68s/it] 
val Loss: 1435.6004, Seg Loss: 0.3234, Heatmap Loss: 956.4390, Geometric Loss: 0.7732
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.8155
  avg_peak_to_mean_ratio: 11182.2160
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 2796.5083
val Heatmap Components:
  mse_loss: 0.7871
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 117.0382
  peak_enhancement_loss: 72.7690
  peak_to_second_ratio_loss: 0.3562
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1625
New best model saved with loss: 1435.6004
Current learning rate: 0.000052

Epoch 27/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.18s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.84s/it]
train Loss: 1541.5072, Seg Loss: 0.4119, Heatmap Loss: 1026.9203, Geometric Loss: 0.8936
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2611
  avg_peak_to_mean_ratio: 33.7686
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.6964
  Overall Confidence Score: 9.4318
train Heatmap Components:
  mse_loss: 1.4980
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 231.1472
  peak_enhancement_loss: 35.0107
  peak_to_second_ratio_loss: 0.3860
  detection_rate_loss: 1.5903
  segmentation_guidance_loss: 0.4561
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it] 
val Loss: 1437.8141, Seg Loss: 0.3058, Heatmap Loss: 957.9479, Geometric Loss: 0.7330
=== val Corner Confidence Metrics ===
  avg_peak_value: 1.9672
  avg_peak_to_mean_ratio: 40.1490
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 1.0000
  Overall Confidence Score: 11.0293
val Heatmap Components:
  mse_loss: 0.9404
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 115.3136
  peak_enhancement_loss: 75.8334
  peak_to_second_ratio_loss: 0.3880
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1397
Current learning rate: 0.000032

Epoch 28/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.79s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.61s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.90s/it]
train Loss: 1398.4343, Seg Loss: 0.4137, Heatmap Loss: 931.5188, Geometric Loss: 0.9281
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7997
  avg_peak_to_mean_ratio: 20.6510
  avg_peak_to_second_ratio: 1.0013
  detection_rate: 0.5357
  Overall Confidence Score: 5.9969
train Heatmap Components:
  mse_loss: 1.2784
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 109.3717
  peak_enhancement_loss: 26.7427
  peak_to_second_ratio_loss: 0.4039
  detection_rate_loss: 3.2987
  segmentation_guidance_loss: 0.5441
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 1436.3014, Seg Loss: 0.2948, Heatmap Loss: 956.9805, Geometric Loss: 0.6698
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0043
  avg_peak_to_mean_ratio: 18.6355
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 5.6603
val Heatmap Components:
  mse_loss: 1.0783
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 111.6532
  peak_enhancement_loss: 77.7347
  peak_to_second_ratio_loss: 0.4144
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1650
Current learning rate: 0.000017

Epoch 29/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.25s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.79s/it]
train Loss: 1459.0957, Seg Loss: 0.3847, Heatmap Loss: 972.0242, Geometric Loss: 0.8434
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8737
  avg_peak_to_mean_ratio: 16.1062
  avg_peak_to_second_ratio: 1.0137
  detection_rate: 0.5893
  Overall Confidence Score: 4.8957
train Heatmap Components:
  mse_loss: 1.3134
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 140.4292
  peak_enhancement_loss: 52.1040
  peak_to_second_ratio_loss: 0.4085
  detection_rate_loss: 2.4772
  segmentation_guidance_loss: 0.1422
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 1453.6861, Seg Loss: 0.2919, Heatmap Loss: 968.5860, Geometric Loss: 0.6441
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1091
  avg_peak_to_mean_ratio: 48.2973
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 1.0000
  Overall Confidence Score: 13.1019
val Heatmap Components:
  mse_loss: 1.3874
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 129.8572
  peak_enhancement_loss: 73.4254
  peak_to_second_ratio_loss: 0.4392
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1200
Current learning rate: 0.000008

Epoch 30/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.48s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.88s/it]
train Loss: 1428.3595, Seg Loss: 0.3896, Heatmap Loss: 951.5206, Geometric Loss: 0.8614
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2813
  avg_peak_to_mean_ratio: 22.5094
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 0.6964
  Overall Confidence Score: 6.6223
train Heatmap Components:
  mse_loss: 1.4509
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 120.3659
  peak_enhancement_loss: 51.2414
  peak_to_second_ratio_loss: 0.4533
  detection_rate_loss: 1.5712
  segmentation_guidance_loss: 0.2630
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.69s/it] 
val Loss: 1457.2676, Seg Loss: 0.3003, Heatmap Loss: 970.9527, Geometric Loss: 0.6728
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0260
  avg_peak_to_mean_ratio: 28.1190
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 1.0000
  Overall Confidence Score: 8.0367
val Heatmap Components:
  mse_loss: 1.1401
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 133.3704
  peak_enhancement_loss: 73.0564
  peak_to_second_ratio_loss: 0.4582
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0596
Current learning rate: 0.000500

Epoch 31/80
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.73s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.55s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.03s/it]
train Loss: 1462.9682, Seg Loss: 0.3711, Heatmap Loss: 974.5281, Geometric Loss: 1.0061
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8087
  avg_peak_to_mean_ratio: 33.9064
  avg_peak_to_second_ratio: 1.0026
  detection_rate: 0.6607
  Overall Confidence Score: 9.3446
train Heatmap Components:
  mse_loss: 1.3248
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 135.9416
  peak_enhancement_loss: 62.7486
  peak_to_second_ratio_loss: 0.4803
  detection_rate_loss: 1.8509
  segmentation_guidance_loss: 0.1288
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.60s/it] 
val Loss: 1416.5933, Seg Loss: 0.3225, Heatmap Loss: 943.7576, Geometric Loss: 0.7929
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0382
  avg_peak_to_mean_ratio: 30.1020
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 8.5354
val Heatmap Components:
  mse_loss: 0.8965
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 101.4715
  peak_enhancement_loss: 70.7627
  peak_to_second_ratio_loss: 0.4886
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1255
New best model saved with loss: 1416.5933
Current learning rate: 0.000499

Epoch 32/80
----------
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.75s/it]
train Loss: 1375.1183, Seg Loss: 0.3596, Heatmap Loss: 916.0747, Geometric Loss: 0.8083
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6486
  avg_peak_to_mean_ratio: 15.2197
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 0.7679
  Overall Confidence Score: 4.9092
train Heatmap Components:
  mse_loss: 1.6765
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 121.0234
  peak_enhancement_loss: 6.8951
  peak_to_second_ratio_loss: 0.4865
  detection_rate_loss: 1.4262
  segmentation_guidance_loss: 0.0449
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1491.1259, Seg Loss: 0.3456, Heatmap Loss: 993.4619, Geometric Loss: 0.7344
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0269
  avg_peak_to_mean_ratio: 19.9376
  avg_peak_to_second_ratio: 1.0055
  detection_rate: 0.9375
  Overall Confidence Score: 5.9769
val Heatmap Components:
  mse_loss: 1.1031
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 154.8026
  peak_enhancement_loss: 75.7299
  peak_to_second_ratio_loss: 0.4764
  detection_rate_loss: 0.5969
  segmentation_guidance_loss: 0.1229
Current learning rate: 0.000497

Epoch 33/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.45s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.91s/it]
train Loss: 1510.5326, Seg Loss: 0.4130, Heatmap Loss: 1006.2502, Geometric Loss: 0.9304
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0439
  avg_peak_to_mean_ratio: 29.2822
  avg_peak_to_second_ratio: 1.0008
  detection_rate: 0.5714
  Overall Confidence Score: 8.2246
train Heatmap Components:
  mse_loss: 1.2587
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 165.4617
  peak_enhancement_loss: 66.3334
  peak_to_second_ratio_loss: 0.4861
  detection_rate_loss: 2.7834
  segmentation_guidance_loss: 0.5184
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 1484.1957, Seg Loss: 0.2936, Heatmap Loss: 988.9393, Geometric Loss: 0.6165
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.2638
  avg_peak_to_mean_ratio: 56.2060
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 15.1179
val Heatmap Components:
  mse_loss: 1.0391
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 157.3435
  peak_enhancement_loss: 71.3197
  peak_to_second_ratio_loss: 0.4850
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0795
Current learning rate: 0.000493

Epoch 34/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.27s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:11,  5.51s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.83s/it]
train Loss: 1417.5821, Seg Loss: 0.4154, Heatmap Loss: 944.3173, Geometric Loss: 0.8634
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3930
  avg_peak_to_mean_ratio: 35.6912
  avg_peak_to_second_ratio: 1.0024
  detection_rate: 0.7143
  Overall Confidence Score: 9.9502
train Heatmap Components:
  mse_loss: 1.4204
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 108.5544
  peak_enhancement_loss: 56.1170
  peak_to_second_ratio_loss: 0.4797
  detection_rate_loss: 1.2278
  segmentation_guidance_loss: 0.1488
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 1447.6182, Seg Loss: 0.2911, Heatmap Loss: 964.4718, Geometric Loss: 0.7743
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5127
  avg_peak_to_mean_ratio: 25.8987
  avg_peak_to_second_ratio: 1.0019
  detection_rate: 1.0000
  Overall Confidence Score: 7.6033
val Heatmap Components:
  mse_loss: 1.2077
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 143.2513
  peak_enhancement_loss: 54.6872
  peak_to_second_ratio_loss: 0.4794
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0797
Current learning rate: 0.000488

Epoch 35/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.80s/it]
train Loss: 1533.2983, Seg Loss: 0.4023, Heatmap Loss: 1021.4690, Geometric Loss: 0.8656
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3104
  avg_peak_to_mean_ratio: 29.3857
  avg_peak_to_second_ratio: 1.0055
  detection_rate: 0.5714
  Overall Confidence Score: 8.3183
train Heatmap Components:
  mse_loss: 1.2258
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 192.3740
  peak_enhancement_loss: 58.5725
  peak_to_second_ratio_loss: 0.4632
  detection_rate_loss: 2.9479
  segmentation_guidance_loss: 0.0764
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 1438.8055, Seg Loss: 0.2863, Heatmap Loss: 958.6251, Geometric Loss: 0.7271
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5358
  avg_peak_to_mean_ratio: 30.2818
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 1.0000
  Overall Confidence Score: 8.7049
val Heatmap Components:
  mse_loss: 1.0978
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 126.6011
  peak_enhancement_loss: 64.3035
  peak_to_second_ratio_loss: 0.4743
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0405
Current learning rate: 0.000481

Epoch 36/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.70s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.55s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.53s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.96s/it]
train Loss: 1642.2562, Seg Loss: 0.4091, Heatmap Loss: 1094.1187, Geometric Loss: 0.8363
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5530
  avg_peak_to_mean_ratio: 23.2987
  avg_peak_to_second_ratio: 1.0102
  detection_rate: 0.6964
  Overall Confidence Score: 6.8896
train Heatmap Components:
  mse_loss: 1.5845
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 288.4977
  peak_enhancement_loss: 58.6027
  peak_to_second_ratio_loss: 0.4622
  detection_rate_loss: 2.0464
  segmentation_guidance_loss: 0.0000
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.64s/it] 
val Loss: 1546.1720, Seg Loss: 0.2627, Heatmap Loss: 1030.1419, Geometric Loss: 0.8705
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6448
  avg_peak_to_mean_ratio: 10.7707
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 1.0000
  Overall Confidence Score: 3.8542
val Heatmap Components:
  mse_loss: 1.2510
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 217.5836
  peak_enhancement_loss: 62.4344
  peak_to_second_ratio_loss: 0.4761
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0767
Current learning rate: 0.000473

Epoch 37/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.43s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.23s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 1507.1601, Seg Loss: 0.3939, Heatmap Loss: 1004.0144, Geometric Loss: 0.9306
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5202
  avg_peak_to_mean_ratio: 32.8638
  avg_peak_to_second_ratio: 1.0013
  detection_rate: 0.6429
  Overall Confidence Score: 9.2571
train Heatmap Components:
  mse_loss: 1.3767
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 189.1952
  peak_enhancement_loss: 43.2723
  peak_to_second_ratio_loss: 0.4837
  detection_rate_loss: 2.1777
  segmentation_guidance_loss: 0.6257
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1458.0908, Seg Loss: 0.2768, Heatmap Loss: 971.5049, Geometric Loss: 0.6957
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.9387
  avg_peak_to_mean_ratio: 17.4287
  avg_peak_to_second_ratio: 1.0032
  detection_rate: 1.0000
  Overall Confidence Score: 5.5926
val Heatmap Components:
  mse_loss: 1.9310
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 135.7221
  peak_enhancement_loss: 70.1662
  peak_to_second_ratio_loss: 0.4717
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0975
Current learning rate: 0.000464

Epoch 38/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.77s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.88s/it]
train Loss: 1611.6228, Seg Loss: 0.3697, Heatmap Loss: 1073.7242, Geometric Loss: 0.8335
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0258
  avg_peak_to_mean_ratio: 28.4186
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 0.6250
  Overall Confidence Score: 8.0177
train Heatmap Components:
  mse_loss: 1.4968
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 256.1756
  peak_enhancement_loss: 61.2203
  peak_to_second_ratio_loss: 0.4837
  detection_rate_loss: 2.6213
  segmentation_guidance_loss: 0.2446
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1557.3981, Seg Loss: 0.2793, Heatmap Loss: 1037.7398, Geometric Loss: 0.6364
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8286
  avg_peak_to_mean_ratio: 50.2134
  avg_peak_to_second_ratio: 1.0023
  detection_rate: 1.0000
  Overall Confidence Score: 13.7611
val Heatmap Components:
  mse_loss: 1.5291
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 220.8988
  peak_enhancement_loss: 68.1055
  peak_to_second_ratio_loss: 0.4770
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1580
Current learning rate: 0.000453

Epoch 39/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.85s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.80s/it]
train Loss: 1481.9135, Seg Loss: 0.3945, Heatmap Loss: 987.1417, Geometric Loss: 1.0082
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6992
  avg_peak_to_mean_ratio: 155.3197
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.6786
  Overall Confidence Score: 39.9246
train Heatmap Components:
  mse_loss: 1.2828
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 173.5440
  peak_enhancement_loss: 39.3698
  peak_to_second_ratio_loss: 0.4778
  detection_rate_loss: 1.9527
  segmentation_guidance_loss: 0.6576
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.55s/it] 
val Loss: 1671.0312, Seg Loss: 0.2747, Heatmap Loss: 1113.4464, Geometric Loss: 0.7338
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.4715
  avg_peak_to_mean_ratio: 8.9056
  avg_peak_to_second_ratio: 1.0026
  detection_rate: 0.9688
  Overall Confidence Score: 3.3371
val Heatmap Components:
  mse_loss: 0.9980
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 316.0748
  peak_enhancement_loss: 67.2201
  peak_to_second_ratio_loss: 0.4735
  detection_rate_loss: 0.1801
  segmentation_guidance_loss: 0.1182
Current learning rate: 0.000441

Epoch 40/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.08s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.50s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 1779.9102, Seg Loss: 0.4122, Heatmap Loss: 1185.9114, Geometric Loss: 0.7885
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2297
  avg_peak_to_mean_ratio: 52.1870
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 0.5893
  Overall Confidence Score: 14.0019
train Heatmap Components:
  mse_loss: 1.2857
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 160.8940
  peak_enhancement_loss: 80.8015
  peak_to_second_ratio_loss: 0.4858
  detection_rate_loss: 2.7849
  segmentation_guidance_loss: 0.7054
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1659.1276, Seg Loss: 0.2920, Heatmap Loss: 1105.5246, Geometric Loss: 0.6859
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3757
  avg_peak_to_mean_ratio: 2.0987
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.9688
  Overall Confidence Score: 1.6112
val Heatmap Components:
  mse_loss: 0.7937
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 293.2125
  peak_enhancement_loss: 80.3754
  peak_to_second_ratio_loss: 0.4793
  detection_rate_loss: 0.1769
  segmentation_guidance_loss: 0.1219
Current learning rate: 0.000428

Epoch 41/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.61s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.88s/it]
train Loss: 1430.8051, Seg Loss: 0.3961, Heatmap Loss: 953.0872, Geometric Loss: 0.9727
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.7059
  avg_peak_to_mean_ratio: 88.8041
  avg_peak_to_second_ratio: 1.0029
  detection_rate: 0.6607
  Overall Confidence Score: 23.2934
train Heatmap Components:
  mse_loss: 1.4751
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 113.2175
  peak_enhancement_loss: 58.0383
  peak_to_second_ratio_loss: 0.4747
  detection_rate_loss: 1.8718
  segmentation_guidance_loss: 0.3344
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.64s/it] 
val Loss: 1477.9490, Seg Loss: 0.2852, Heatmap Loss: 984.7612, Geometric Loss: 0.6525
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8788
  avg_peak_to_mean_ratio: 56.0620
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 15.2356
val Heatmap Components:
  mse_loss: 1.1231
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 151.3942
  peak_enhancement_loss: 72.0878
  peak_to_second_ratio_loss: 0.4789
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0425
Current learning rate: 0.000413

Epoch 42/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.30s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 1552.1846, Seg Loss: 0.4121, Heatmap Loss: 1034.0675, Geometric Loss: 0.8391
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5888
  avg_peak_to_mean_ratio: 26.4132
  avg_peak_to_second_ratio: 1.0058
  detection_rate: 0.6250
  Overall Confidence Score: 7.6582
train Heatmap Components:
  mse_loss: 1.4934
  separation_loss: 1000.0000
  peak_separation_loss: 122.4624
  edge_suppression_loss: 103.5764
  peak_enhancement_loss: 69.5599
  peak_to_second_ratio_loss: 0.4813
  detection_rate_loss: 2.8249
  segmentation_guidance_loss: 1.1003
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 1403.9501, Seg Loss: 0.2657, Heatmap Loss: 935.4547, Geometric Loss: 0.6278
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.0346
  avg_peak_to_mean_ratio: 36.0353
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 10.2679
val Heatmap Components:
  mse_loss: 1.4429
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 102.2147
  peak_enhancement_loss: 59.2566
  peak_to_second_ratio_loss: 0.4822
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0082
New best model saved with loss: 1403.9501
Current learning rate: 0.000398

Epoch 43/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:04<00:14,  4.91s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.74s/it]
train Loss: 1546.6475, Seg Loss: 0.4023, Heatmap Loss: 1030.3155, Geometric Loss: 0.9648
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3599
  avg_peak_to_mean_ratio: 53.8938
  avg_peak_to_second_ratio: 1.0066
  detection_rate: 0.5179
  Overall Confidence Score: 14.4445
train Heatmap Components:
  mse_loss: 1.2900
  separation_loss: 1000.0000
  peak_separation_loss: 142.8571
  edge_suppression_loss: 86.9280
  peak_enhancement_loss: 64.3764
  peak_to_second_ratio_loss: 0.4683
  detection_rate_loss: 3.4013
  segmentation_guidance_loss: 0.3855
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.70s/it] 
val Loss: 1394.4846, Seg Loss: 0.2892, Heatmap Loss: 929.1207, Geometric Loss: 0.6430
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7703
  avg_peak_to_mean_ratio: 20.0171
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 0.9688
  Overall Confidence Score: 6.1895
val Heatmap Components:
  mse_loss: 1.2551
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 89.9083
  peak_enhancement_loss: 62.7997
  peak_to_second_ratio_loss: 0.4776
  detection_rate_loss: 0.1803
  segmentation_guidance_loss: 0.0147
New best model saved with loss: 1394.4846
Current learning rate: 0.000382

Epoch 44/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.43s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.95s/it]
train Loss: 1481.3013, Seg Loss: 0.4191, Heatmap Loss: 986.7593, Geometric Loss: 0.9291
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9388
  avg_peak_to_mean_ratio: 22.6218
  avg_peak_to_second_ratio: 1.0040
  detection_rate: 0.6607
  Overall Confidence Score: 6.8063
train Heatmap Components:
  mse_loss: 1.4110
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 167.6875
  peak_enhancement_loss: 45.3596
  peak_to_second_ratio_loss: 0.4584
  detection_rate_loss: 1.8724
  segmentation_guidance_loss: 0.6431
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.68s/it] 
val Loss: 1417.6637, Seg Loss: 0.2551, Heatmap Loss: 944.5399, Geometric Loss: 0.7484
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6718
  avg_peak_to_mean_ratio: 68.7367
  avg_peak_to_second_ratio: 1.0024
  detection_rate: 1.0000
  Overall Confidence Score: 18.3527
val Heatmap Components:
  mse_loss: 1.0845
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 95.6037
  peak_enhancement_loss: 77.6866
  peak_to_second_ratio_loss: 0.4679
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0961
Current learning rate: 0.000365

Epoch 45/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.85s/it]
train Loss: 1317.8463, Seg Loss: 0.3998, Heatmap Loss: 877.8678, Geometric Loss: 0.8060
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9012
  avg_peak_to_mean_ratio: 30.9682
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.7500
  Overall Confidence Score: 8.9053
train Heatmap Components:
  mse_loss: 1.5462
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 62.6847
  peak_enhancement_loss: 14.9499
  peak_to_second_ratio_loss: 0.4762
  detection_rate_loss: 1.5029
  segmentation_guidance_loss: 1.2912
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.62s/it] 
val Loss: 1379.2995, Seg Loss: 0.2777, Heatmap Loss: 918.9353, Geometric Loss: 0.7735
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3448
  avg_peak_to_mean_ratio: 33.8874
  avg_peak_to_second_ratio: 1.0028
  detection_rate: 0.9688
  Overall Confidence Score: 9.5510
val Heatmap Components:
  mse_loss: 0.8094
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 56.8198
  peak_enhancement_loss: 83.4769
  peak_to_second_ratio_loss: 0.4810
  detection_rate_loss: 0.1812
  segmentation_guidance_loss: 0.1150
New best model saved with loss: 1379.2995
Current learning rate: 0.000347

Epoch 46/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.85s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.82s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.93s/it]
train Loss: 1476.2263, Seg Loss: 0.4322, Heatmap Loss: 983.3644, Geometric Loss: 0.9344
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3868
  avg_peak_to_mean_ratio: 17.7719
  avg_peak_to_second_ratio: 1.0042
  detection_rate: 0.6071
  Overall Confidence Score: 5.4425
train Heatmap Components:
  mse_loss: 1.4812
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 138.7231
  peak_enhancement_loss: 64.2753
  peak_to_second_ratio_loss: 0.4636
  detection_rate_loss: 2.8594
  segmentation_guidance_loss: 0.3679
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.70s/it] 
val Loss: 1419.9565, Seg Loss: 0.2685, Heatmap Loss: 946.0970, Geometric Loss: 0.6781
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3840
  avg_peak_to_mean_ratio: 75.8760
  avg_peak_to_second_ratio: 1.0057
  detection_rate: 0.9688
  Overall Confidence Score: 20.0586
val Heatmap Components:
  mse_loss: 0.7638
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 94.1799
  peak_enhancement_loss: 80.3084
  peak_to_second_ratio_loss: 0.4685
  detection_rate_loss: 0.1825
  segmentation_guidance_loss: 0.0970
Current learning rate: 0.000329

Epoch 47/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.88s/it]
train Loss: 1591.5033, Seg Loss: 0.4252, Heatmap Loss: 1060.2326, Geometric Loss: 0.9115
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5894
  avg_peak_to_mean_ratio: 32.0370
  avg_peak_to_second_ratio: 1.0030
  detection_rate: 0.6964
  Overall Confidence Score: 9.0815
train Heatmap Components:
  mse_loss: 1.5754
  separation_loss: 1000.0000
  peak_separation_loss: 142.8571
  edge_suppression_loss: 144.6954
  peak_enhancement_loss: 52.2930
  peak_to_second_ratio_loss: 0.4750
  detection_rate_loss: 1.9298
  segmentation_guidance_loss: 0.6358
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 1377.5855, Seg Loss: 0.2805, Heatmap Loss: 917.8210, Geometric Loss: 0.7168
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1193
  avg_peak_to_mean_ratio: 59.4328
  avg_peak_to_second_ratio: 1.0031
  detection_rate: 0.9375
  Overall Confidence Score: 15.8732
val Heatmap Components:
  mse_loss: 0.6518
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 55.8268
  peak_enhancement_loss: 82.2686
  peak_to_second_ratio_loss: 0.4703
  detection_rate_loss: 0.3598
  segmentation_guidance_loss: 0.1274
New best model saved with loss: 1377.5855
Current learning rate: 0.000310

Epoch 48/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.51s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.87s/it]
train Loss: 1431.0040, Seg Loss: 0.3622, Heatmap Loss: 953.2881, Geometric Loss: 0.8871
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8734
  avg_peak_to_mean_ratio: 21.4325
  avg_peak_to_second_ratio: 1.0021
  detection_rate: 0.6607
  Overall Confidence Score: 6.4922
train Heatmap Components:
  mse_loss: 1.2758
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 116.7151
  peak_enhancement_loss: 55.0723
  peak_to_second_ratio_loss: 0.4675
  detection_rate_loss: 1.9697
  segmentation_guidance_loss: 0.0390
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1428.7137, Seg Loss: 0.2882, Heatmap Loss: 951.8830, Geometric Loss: 0.7512
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.4154
  avg_peak_to_mean_ratio: 46.5902
  avg_peak_to_second_ratio: 1.0045
  detection_rate: 0.9375
  Overall Confidence Score: 12.7369
val Heatmap Components:
  mse_loss: 0.9250
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 108.7141
  peak_enhancement_loss: 71.5344
  peak_to_second_ratio_loss: 0.4803
  detection_rate_loss: 0.3518
  segmentation_guidance_loss: 0.1315
Current learning rate: 0.000291

Epoch 49/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.82s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.59s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.99s/it]
train Loss: 1659.5442, Seg Loss: 0.4078, Heatmap Loss: 1105.6101, Geometric Loss: 0.9015
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4536
  avg_peak_to_mean_ratio: 24.5719
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 0.5893
  Overall Confidence Score: 7.1540
train Heatmap Components:
  mse_loss: 1.3701
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 98.2534
  peak_enhancement_loss: 34.7656
  peak_to_second_ratio_loss: 0.4810
  detection_rate_loss: 2.8196
  segmentation_guidance_loss: 4.9921
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.64s/it] 
val Loss: 1410.0314, Seg Loss: 0.2781, Heatmap Loss: 939.4434, Geometric Loss: 0.7353
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8456
  avg_peak_to_mean_ratio: 38.7689
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 1.0000
  Overall Confidence Score: 10.9040
val Heatmap Components:
  mse_loss: 1.2091
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 99.1615
  peak_enhancement_loss: 67.4724
  peak_to_second_ratio_loss: 0.4842
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0570
Current learning rate: 0.000272

Epoch 50/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.23s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:11,  5.52s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.79s/it]
train Loss: 1433.4310, Seg Loss: 0.4015, Heatmap Loss: 954.9043, Geometric Loss: 0.8412
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2793
  avg_peak_to_mean_ratio: 21.4922
  avg_peak_to_second_ratio: 1.0022
  detection_rate: 0.6250
  Overall Confidence Score: 6.3497
train Heatmap Components:
  mse_loss: 1.2910
  separation_loss: 1000.0000
  peak_separation_loss: 50.7920
  edge_suppression_loss: 70.7203
  peak_enhancement_loss: 62.4709
  peak_to_second_ratio_loss: 0.4791
  detection_rate_loss: 2.1898
  segmentation_guidance_loss: 0.5635
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 1443.2421, Seg Loss: 0.3292, Heatmap Loss: 961.6702, Geometric Loss: 0.5096
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3308
  avg_peak_to_mean_ratio: 139.9896
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 0.9375
  Overall Confidence Score: 36.0650
val Heatmap Components:
  mse_loss: 0.8817
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 103.5844
  peak_enhancement_loss: 88.9918
  peak_to_second_ratio_loss: 0.4842
  detection_rate_loss: 0.3525
  segmentation_guidance_loss: 0.0818
Current learning rate: 0.000253

Epoch 51/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.33s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.30s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.71s/it]
train Loss: 1435.7461, Seg Loss: 0.3778, Heatmap Loss: 956.4304, Geometric Loss: 0.9033
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8941
  avg_peak_to_mean_ratio: 132.6174
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 0.7321
  Overall Confidence Score: 34.3113
train Heatmap Components:
  mse_loss: 1.5986
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 128.1844
  peak_enhancement_loss: 47.3853
  peak_to_second_ratio_loss: 0.4883
  detection_rate_loss: 1.7805
  segmentation_guidance_loss: 0.3935
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 1393.5261, Seg Loss: 0.2972, Heatmap Loss: 928.4657, Geometric Loss: 0.6630
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.8283
  avg_peak_to_mean_ratio: 21.0316
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 1.0000
  Overall Confidence Score: 6.4653
val Heatmap Components:
  mse_loss: 1.3720
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 79.2815
  peak_enhancement_loss: 73.4635
  peak_to_second_ratio_loss: 0.4836
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0414
Current learning rate: 0.000233

Epoch 52/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:11,  5.51s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 1650.3906, Seg Loss: 0.3609, Heatmap Loss: 1099.5949, Geometric Loss: 0.7967
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6320
  avg_peak_to_mean_ratio: 57.9961
  avg_peak_to_second_ratio: 1.0023
  detection_rate: 0.6429
  Overall Confidence Score: 15.5683
train Heatmap Components:
  mse_loss: 1.3592
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 97.3855
  peak_enhancement_loss: 41.7418
  peak_to_second_ratio_loss: 0.4799
  detection_rate_loss: 2.1186
  segmentation_guidance_loss: 0.0756
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.64s/it] 
val Loss: 1401.4314, Seg Loss: 0.3462, Heatmap Loss: 933.7753, Geometric Loss: 0.5278
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3209
  avg_peak_to_mean_ratio: 38.2175
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 1.0000
  Overall Confidence Score: 10.6350
val Heatmap Components:
  mse_loss: 0.7976
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 74.7942
  peak_enhancement_loss: 85.3538
  peak_to_second_ratio_loss: 0.4795
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0430
Current learning rate: 0.000214

Epoch 53/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.73s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.43s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.80s/it]
train Loss: 1592.3050, Seg Loss: 0.3749, Heatmap Loss: 1060.7505, Geometric Loss: 1.0052
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.7405
  avg_peak_to_mean_ratio: 31.6064
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.6429
  Overall Confidence Score: 8.9976
train Heatmap Components:
  mse_loss: 1.3677
  separation_loss: 1000.0000
  peak_separation_loss: 142.8571
  edge_suppression_loss: 131.4806
  peak_enhancement_loss: 65.1023
  peak_to_second_ratio_loss: 0.4684
  detection_rate_loss: 2.1748
  segmentation_guidance_loss: 0.5626
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 1368.5783, Seg Loss: 0.3250, Heatmap Loss: 911.8508, Geometric Loss: 0.5963
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7022
  avg_peak_to_mean_ratio: 15.7754
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 5.1197
val Heatmap Components:
  mse_loss: 1.1415
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 69.4453
  peak_enhancement_loss: 62.8062
  peak_to_second_ratio_loss: 0.4834
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0496
New best model saved with loss: 1368.5783
Current learning rate: 0.000195

Epoch 54/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.25s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.48s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.77s/it]
train Loss: 1384.7222, Seg Loss: 0.4900, Heatmap Loss: 922.2911, Geometric Loss: 0.9944
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3434
  avg_peak_to_mean_ratio: 15.5339
  avg_peak_to_second_ratio: 1.0036
  detection_rate: 0.6250
  Overall Confidence Score: 4.8765
train Heatmap Components:
  mse_loss: 1.4907
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 88.7261
  peak_enhancement_loss: 40.1353
  peak_to_second_ratio_loss: 0.4813
  detection_rate_loss: 2.3503
  segmentation_guidance_loss: 0.7646
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it] 
val Loss: 1373.9642, Seg Loss: 0.3162, Heatmap Loss: 915.4345, Geometric Loss: 0.6202
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7455
  avg_peak_to_mean_ratio: 11.6261
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 1.0000
  Overall Confidence Score: 4.0932
val Heatmap Components:
  mse_loss: 1.2759
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 63.9478
  peak_enhancement_loss: 72.5797
  peak_to_second_ratio_loss: 0.4873
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0425
Current learning rate: 0.000176

Epoch 55/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.82s/it]
train Loss: 1380.3676, Seg Loss: 0.4391, Heatmap Loss: 919.3757, Geometric Loss: 1.0810
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6070
  avg_peak_to_mean_ratio: 34.2057
  avg_peak_to_second_ratio: 1.0117
  detection_rate: 0.6071
  Overall Confidence Score: 9.6079
train Heatmap Components:
  mse_loss: 1.4144
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 90.3215
  peak_enhancement_loss: 34.1404
  peak_to_second_ratio_loss: 0.4574
  detection_rate_loss: 2.4544
  segmentation_guidance_loss: 1.0305
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.68s/it] 
val Loss: 1355.0352, Seg Loss: 0.2890, Heatmap Loss: 902.8643, Geometric Loss: 0.5622
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6322
  avg_peak_to_mean_ratio: 17.9539
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 1.0000
  Overall Confidence Score: 5.6468
val Heatmap Components:
  mse_loss: 1.1679
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 62.4974
  peak_enhancement_loss: 58.4973
  peak_to_second_ratio_loss: 0.4844
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0375
New best model saved with loss: 1355.0352
Current learning rate: 0.000158

Epoch 56/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.81s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.94s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.01s/it]
train Loss: 1403.3379, Seg Loss: 0.3975, Heatmap Loss: 934.7449, Geometric Loss: 1.0289
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1745
  avg_peak_to_mean_ratio: 13.8668
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.6250
  Overall Confidence Score: 4.4168
train Heatmap Components:
  mse_loss: 1.4049
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 117.6306
  peak_enhancement_loss: 29.3359
  peak_to_second_ratio_loss: 0.4893
  detection_rate_loss: 1.9922
  segmentation_guidance_loss: 0.6087
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 1390.6799, Seg Loss: 0.3319, Heatmap Loss: 926.5312, Geometric Loss: 0.6890
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1021
  avg_peak_to_mean_ratio: 21.9114
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 6.5038
val Heatmap Components:
  mse_loss: 0.5951
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 72.9759
  peak_enhancement_loss: 78.3608
  peak_to_second_ratio_loss: 0.4805
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0409
Current learning rate: 0.000140

Epoch 57/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.70s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.88s/it]
train Loss: 1380.5156, Seg Loss: 0.4753, Heatmap Loss: 919.5402, Geometric Loss: 0.9125
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6098
  avg_peak_to_mean_ratio: 39.2027
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.7500
  Overall Confidence Score: 10.8909
train Heatmap Components:
  mse_loss: 1.4559
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 81.3481
  peak_enhancement_loss: 50.4647
  peak_to_second_ratio_loss: 0.4772
  detection_rate_loss: 1.1815
  segmentation_guidance_loss: 1.3029
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.63s/it] 
val Loss: 1339.4060, Seg Loss: 0.3247, Heatmap Loss: 892.3232, Geometric Loss: 0.7455
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1514
  avg_peak_to_mean_ratio: 21.1908
  avg_peak_to_second_ratio: 1.0021
  detection_rate: 0.9688
  Overall Confidence Score: 6.3283
val Heatmap Components:
  mse_loss: 0.7211
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 28.0811
  peak_enhancement_loss: 79.1686
  peak_to_second_ratio_loss: 0.4840
  detection_rate_loss: 0.1809
  segmentation_guidance_loss: 0.0382
New best model saved with loss: 1339.4060
Current learning rate: 0.000123

Epoch 58/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.28s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 1663.4082, Seg Loss: 0.4696, Heatmap Loss: 1108.1016, Geometric Loss: 0.9827
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8671
  avg_peak_to_mean_ratio: 42.0609
  avg_peak_to_second_ratio: 1.0023
  detection_rate: 0.6607
  Overall Confidence Score: 11.6478
train Heatmap Components:
  mse_loss: 1.3615
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 98.6037
  peak_enhancement_loss: 49.4531
  peak_to_second_ratio_loss: 0.4617
  detection_rate_loss: 2.0821
  segmentation_guidance_loss: 1.2257
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 1327.4674, Seg Loss: 0.3052, Heatmap Loss: 884.4324, Geometric Loss: 0.6421
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.4475
  avg_peak_to_mean_ratio: 25.0764
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 7.3813
val Heatmap Components:
  mse_loss: 1.0218
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 27.7900
  peak_enhancement_loss: 70.3682
  peak_to_second_ratio_loss: 0.4827
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0381
New best model saved with loss: 1327.4674
Current learning rate: 0.000107

Epoch 59/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.35s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 1578.1053, Seg Loss: 0.4338, Heatmap Loss: 1051.3065, Geometric Loss: 0.8897
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6069
  avg_peak_to_mean_ratio: 62.0146
  avg_peak_to_second_ratio: 1.0034
  detection_rate: 0.7143
  Overall Confidence Score: 16.5848
train Heatmap Components:
  mse_loss: 1.5150
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 72.9294
  peak_enhancement_loss: 8.7856
  peak_to_second_ratio_loss: 0.4756
  detection_rate_loss: 1.4989
  segmentation_guidance_loss: 0.4935
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1359.2317, Seg Loss: 0.3139, Heatmap Loss: 905.5452, Geometric Loss: 0.7500
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.4323
  avg_peak_to_mean_ratio: 47.4268
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 1.0000
  Overall Confidence Score: 12.9652
val Heatmap Components:
  mse_loss: 0.9064
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 49.6969
  peak_enhancement_loss: 74.9681
  peak_to_second_ratio_loss: 0.4851
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0373
Current learning rate: 0.000092


Epoch 60/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.63s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.92s/it]
train Loss: 1400.1031, Seg Loss: 0.3915, Heatmap Loss: 932.6360, Geometric Loss: 0.9471
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6307
  avg_peak_to_mean_ratio: 13.7853
  avg_peak_to_second_ratio: 1.0012
  detection_rate: 0.6429
  Overall Confidence Score: 4.5150
train Heatmap Components:
  mse_loss: 1.3730
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 91.6986
  peak_enhancement_loss: 51.5682
  peak_to_second_ratio_loss: 0.4863
  detection_rate_loss: 2.2326
  segmentation_guidance_loss: 0.4154
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1345.7626, Seg Loss: 0.3282, Heatmap Loss: 896.6208, Geometric Loss: 0.6291
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.2939
  avg_peak_to_mean_ratio: 77.1943
  avg_peak_to_second_ratio: 1.0025
  detection_rate: 0.9375
  Overall Confidence Score: 20.3570
val Heatmap Components:
  mse_loss: 0.7958
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 34.1938
  peak_enhancement_loss: 75.7095
  peak_to_second_ratio_loss: 0.4803
  detection_rate_loss: 0.6056
  segmentation_guidance_loss: 0.0475
Current learning rate: 0.000077

Epoch 61/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.37s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.81s/it]
train Loss: 1406.9037, Seg Loss: 0.4004, Heatmap Loss: 937.1815, Geometric Loss: 0.9140
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3076
  avg_peak_to_mean_ratio: 22.9021
  avg_peak_to_second_ratio: 1.0005
  detection_rate: 0.5714
  Overall Confidence Score: 6.6954
train Heatmap Components:
  mse_loss: 1.3210
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 99.2053
  peak_enhancement_loss: 46.2951
  peak_to_second_ratio_loss: 0.4897
  detection_rate_loss: 2.8989
  segmentation_guidance_loss: 0.0458
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.59s/it] 
val Loss: 1354.1618, Seg Loss: 0.3174, Heatmap Loss: 902.2555, Geometric Loss: 0.5765
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3316
  avg_peak_to_mean_ratio: 34.0965
  avg_peak_to_second_ratio: 1.0013
  detection_rate: 1.0000
  Overall Confidence Score: 9.6073
val Heatmap Components:
  mse_loss: 0.8155
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 34.6706
  peak_enhancement_loss: 86.1211
  peak_to_second_ratio_loss: 0.4747
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0399
Current learning rate: 0.000064

Epoch 62/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.08s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.75s/it]
train Loss: 1342.7111, Seg Loss: 0.3972, Heatmap Loss: 894.3320, Geometric Loss: 1.0200
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0313
  avg_peak_to_mean_ratio: 30.8237
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.7143
  Overall Confidence Score: 8.8926
train Heatmap Components:
  mse_loss: 1.4437
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 67.6413
  peak_enhancement_loss: 33.8324
  peak_to_second_ratio_loss: 0.4833
  detection_rate_loss: 1.2636
  segmentation_guidance_loss: 0.3723
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.58s/it] 
val Loss: 1339.1788, Seg Loss: 0.3131, Heatmap Loss: 892.2400, Geometric Loss: 0.6321
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.4160
  avg_peak_to_mean_ratio: 124.6982
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 1.0000
  Overall Confidence Score: 32.2790
val Heatmap Components:
  mse_loss: 0.8253
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 34.2558
  peak_enhancement_loss: 73.9815
  peak_to_second_ratio_loss: 0.4765
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0402
Current learning rate: 0.000052

Epoch 63/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.38s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.32s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.75s/it]
train Loss: 1365.6251, Seg Loss: 0.4161, Heatmap Loss: 909.6118, Geometric Loss: 0.9892
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4772
  avg_peak_to_mean_ratio: 17.0930
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 0.6429
  Overall Confidence Score: 5.3036
train Heatmap Components:
  mse_loss: 1.2659
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 66.4886
  peak_enhancement_loss: 50.5083
  peak_to_second_ratio_loss: 0.4584
  detection_rate_loss: 1.8919
  segmentation_guidance_loss: 0.4701
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.64s/it] 
val Loss: 1350.5782, Seg Loss: 0.3046, Heatmap Loss: 899.8450, Geometric Loss: 0.6327
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5982
  avg_peak_to_mean_ratio: 14.2132
  avg_peak_to_second_ratio: 1.0019
  detection_rate: 1.0000
  Overall Confidence Score: 4.7033
val Heatmap Components:
  mse_loss: 1.0609
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 41.6631
  peak_enhancement_loss: 75.6689
  peak_to_second_ratio_loss: 0.4860
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0392
Current learning rate: 0.000041

Epoch 64/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:04<00:14,  4.99s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.47s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.80s/it]
train Loss: 1337.1665, Seg Loss: 0.4337, Heatmap Loss: 890.7074, Geometric Loss: 0.8396
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8242
  avg_peak_to_mean_ratio: 21.0104
  avg_peak_to_second_ratio: 1.0031
  detection_rate: 0.6964
  Overall Confidence Score: 6.3836
train Heatmap Components:
  mse_loss: 1.4574
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 61.2829
  peak_enhancement_loss: 33.2248
  peak_to_second_ratio_loss: 0.4772
  detection_rate_loss: 1.6737
  segmentation_guidance_loss: 0.3358
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1352.1653, Seg Loss: 0.3332, Heatmap Loss: 900.8965, Geometric Loss: 0.6092
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3436
  avg_peak_to_mean_ratio: 47.7759
  avg_peak_to_second_ratio: 1.0026
  detection_rate: 0.9375
  Overall Confidence Score: 13.0149
val Heatmap Components:
  mse_loss: 0.8625
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 32.5878
  peak_enhancement_loss: 82.5786
  peak_to_second_ratio_loss: 0.4814
  detection_rate_loss: 0.6026
  segmentation_guidance_loss: 0.0494
Current learning rate: 0.000032

Epoch 65/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.20s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.18s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.76s/it]
train Loss: 1356.1277, Seg Loss: 0.3812, Heatmap Loss: 903.3094, Geometric Loss: 0.9781
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4528
  avg_peak_to_mean_ratio: 33.2875
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 0.7321
  Overall Confidence Score: 9.3685
train Heatmap Components:
  mse_loss: 1.6478
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 61.8321
  peak_enhancement_loss: 50.2289
  peak_to_second_ratio_loss: 0.4809
  detection_rate_loss: 1.3297
  segmentation_guidance_loss: 0.3706
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.66s/it] 
val Loss: 1338.8466, Seg Loss: 0.3060, Heatmap Loss: 892.0351, Geometric Loss: 0.6098
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.3099
  avg_peak_to_mean_ratio: 12.7196
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 4.2578
val Heatmap Components:
  mse_loss: 0.8356
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 30.0386
  peak_enhancement_loss: 77.7854
  peak_to_second_ratio_loss: 0.4886
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0362
Current learning rate: 0.000024

Epoch 66/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.59s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.85s/it]
train Loss: 1384.3037, Seg Loss: 0.4338, Heatmap Loss: 922.0084, Geometric Loss: 1.0717
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2594
  avg_peak_to_mean_ratio: 18.5283
  avg_peak_to_second_ratio: 1.0007
  detection_rate: 0.5536
  Overall Confidence Score: 5.5855
train Heatmap Components:
  mse_loss: 1.4196
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 60.5645
  peak_enhancement_loss: 62.7991
  peak_to_second_ratio_loss: 0.4723
  detection_rate_loss: 3.1334
  segmentation_guidance_loss: 1.0052
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.57s/it] 
val Loss: 1338.8023, Seg Loss: 0.3157, Heatmap Loss: 891.9756, Geometric Loss: 0.6540
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1165
  avg_peak_to_mean_ratio: 12.2555
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.9375
  Overall Confidence Score: 4.0778
val Heatmap Components:
  mse_loss: 0.6268
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 24.2967
  peak_enhancement_loss: 80.2560
  peak_to_second_ratio_loss: 0.4767
  detection_rate_loss: 0.5730
  segmentation_guidance_loss: 0.0500
Current learning rate: 0.000017

Epoch 67/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.12s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.34s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.43s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:18<00:00,  4.68s/it]
train Loss: 1351.7368, Seg Loss: 0.3987, Heatmap Loss: 900.3656, Geometric Loss: 0.9872
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8044
  avg_peak_to_mean_ratio: 16.2639
  avg_peak_to_second_ratio: 1.0009
  detection_rate: 0.7143
  Overall Confidence Score: 5.1959
train Heatmap Components:
  mse_loss: 1.5572
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 66.5721
  peak_enhancement_loss: 40.2198
  peak_to_second_ratio_loss: 0.4750
  detection_rate_loss: 1.5347
  segmentation_guidance_loss: 0.6342
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.65s/it] 
val Loss: 1355.3141, Seg Loss: 0.3215, Heatmap Loss: 902.9510, Geometric Loss: 0.7075
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1469
  avg_peak_to_mean_ratio: 44.9318
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 12.2701
val Heatmap Components:
  mse_loss: 0.5893
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 34.8247
  peak_enhancement_loss: 86.9728
  peak_to_second_ratio_loss: 0.4864
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0400
Current learning rate: 0.000012

Epoch 68/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:15,  5.00s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:11,  5.56s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.46s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 1320.9502, Seg Loss: 0.3938, Heatmap Loss: 879.8958, Geometric Loss: 0.8908
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.1848
  avg_peak_to_mean_ratio: 34.0904
  avg_peak_to_second_ratio: 1.0013
  detection_rate: 0.7321
  Overall Confidence Score: 9.7522
train Heatmap Components:
  mse_loss: 1.6424
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 48.7615
  peak_enhancement_loss: 34.8301
  peak_to_second_ratio_loss: 0.4746
  detection_rate_loss: 1.3068
  segmentation_guidance_loss: 0.0671
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.56s/it] 
val Loss: 1337.4593, Seg Loss: 0.3324, Heatmap Loss: 891.0381, Geometric Loss: 0.7123
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.0853
  avg_peak_to_mean_ratio: 61.9787
  avg_peak_to_second_ratio: 1.0019
  detection_rate: 1.0000
  Overall Confidence Score: 16.5165
val Heatmap Components:
  mse_loss: 0.5714
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 22.1412
  peak_enhancement_loss: 84.8443
  peak_to_second_ratio_loss: 0.4795
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0556
Current learning rate: 0.000008

Epoch 69/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.36s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.78s/it]
train Loss: 1582.6298, Seg Loss: 0.4522, Heatmap Loss: 1054.3040, Geometric Loss: 0.9021
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3566
  avg_peak_to_mean_ratio: 34.6682
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.6607
  Overall Confidence Score: 9.6716
train Heatmap Components:
  mse_loss: 1.2900
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 57.6679
  peak_enhancement_loss: 25.4550
  peak_to_second_ratio_loss: 0.4800
  detection_rate_loss: 1.7667
  segmentation_guidance_loss: 0.9691
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.69s/it] 
val Loss: 1328.4881, Seg Loss: 0.2935, Heatmap Loss: 885.1119, Geometric Loss: 0.6584
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5471
  avg_peak_to_mean_ratio: 45.5973
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 12.5364
val Heatmap Components:
  mse_loss: 1.0172
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 36.5593
  peak_enhancement_loss: 62.3816
  peak_to_second_ratio_loss: 0.4882
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0397
Current learning rate: 0.000006

Epoch 70/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.59s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.31s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.95s/it]
train Loss: 1616.0999, Seg Loss: 0.3555, Heatmap Loss: 1076.6152, Geometric Loss: 1.0270
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6175
  avg_peak_to_mean_ratio: 44.7676
  avg_peak_to_second_ratio: 1.0010
  detection_rate: 0.6786
  Overall Confidence Score: 12.2662
train Heatmap Components:
  mse_loss: 1.2915
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 70.4449
  peak_enhancement_loss: 43.4637
  peak_to_second_ratio_loss: 0.4835
  detection_rate_loss: 1.4885
  segmentation_guidance_loss: 0.3270
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.72s/it] 
val Loss: 1332.6339, Seg Loss: 0.3292, Heatmap Loss: 887.8266, Geometric Loss: 0.7059
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1368
  avg_peak_to_mean_ratio: 20.8582
  avg_peak_to_second_ratio: 1.0022
  detection_rate: 0.9688
  Overall Confidence Score: 6.2415
val Heatmap Components:
  mse_loss: 0.6537
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 25.8225
  peak_enhancement_loss: 75.9492
  peak_to_second_ratio_loss: 0.4790
  detection_rate_loss: 0.1794
  segmentation_guidance_loss: 0.0454
Current learning rate: 0.000500

Epoch 71/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.63s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.64s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.89s/it]
train Loss: 1340.1558, Seg Loss: 0.4018, Heatmap Loss: 892.6723, Geometric Loss: 0.9318
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9548
  avg_peak_to_mean_ratio: 19.7767
  avg_peak_to_second_ratio: 1.0022
  detection_rate: 0.7679
  Overall Confidence Score: 6.1254
train Heatmap Components:
  mse_loss: 1.6194
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 64.6303
  peak_enhancement_loss: 37.5162
  peak_to_second_ratio_loss: 0.4698
  detection_rate_loss: 0.8190
  segmentation_guidance_loss: 0.3617
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.60s/it] 
val Loss: 1400.3273, Seg Loss: 0.3469, Heatmap Loss: 932.9684, Geometric Loss: 0.6597
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1816
  avg_peak_to_mean_ratio: 48.0169
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 1.0000
  Overall Confidence Score: 13.0501
val Heatmap Components:
  mse_loss: 0.7303
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 71.9794
  peak_enhancement_loss: 86.9915
  peak_to_second_ratio_loss: 0.4831
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1534
Current learning rate: 0.000500

Epoch 72/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.47s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.42s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.87s/it]
train Loss: 1392.5188, Seg Loss: 0.4506, Heatmap Loss: 927.5652, Geometric Loss: 0.9005
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0306
  avg_peak_to_mean_ratio: 34.5994
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 0.7500
  Overall Confidence Score: 9.8454
train Heatmap Components:
  mse_loss: 1.7356
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 111.9535
  peak_enhancement_loss: 29.7152
  peak_to_second_ratio_loss: 0.4783
  detection_rate_loss: 1.4672
  segmentation_guidance_loss: 0.2505
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.74s/it] 
val Loss: 1384.7609, Seg Loss: 0.3256, Heatmap Loss: 922.6036, Geometric Loss: 0.6623
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.2259
  avg_peak_to_mean_ratio: 34.7796
  avg_peak_to_second_ratio: 1.0016
  detection_rate: 1.0000
  Overall Confidence Score: 9.7518
val Heatmap Components:
  mse_loss: 0.6590
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 63.1787
  peak_enhancement_loss: 83.0539
  peak_to_second_ratio_loss: 0.4837
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0812
Current learning rate: 0.000499

Epoch 73/80
----------
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:16<00:05,  5.67s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.97s/it]
train Loss: 1374.9633, Seg Loss: 0.4157, Heatmap Loss: 915.8672, Geometric Loss: 0.9335
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.7446
  avg_peak_to_mean_ratio: 28.5282
  avg_peak_to_second_ratio: 1.0044
  detection_rate: 0.6607
  Overall Confidence Score: 8.2345
train Heatmap Components:
  mse_loss: 1.5033
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 76.3665
  peak_enhancement_loss: 47.2204
  peak_to_second_ratio_loss: 0.4627
  detection_rate_loss: 1.9019
  segmentation_guidance_loss: 0.9054
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.72s/it] 
val Loss: 1407.1375, Seg Loss: 0.3123, Heatmap Loss: 937.5291, Geometric Loss: 0.6644
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1757
  avg_peak_to_mean_ratio: 15.4796
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 1.0000
  Overall Confidence Score: 4.9143
val Heatmap Components:
  mse_loss: 0.4766
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 85.3311
  peak_enhancement_loss: 79.8601
  peak_to_second_ratio_loss: 0.4750
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0998
Current learning rate: 0.000498

Epoch 74/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:05<00:17,  5.84s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.90s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:17<00:05,  5.91s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:20<00:00,  5.13s/it]
train Loss: 1448.6015, Seg Loss: 0.4030, Heatmap Loss: 965.0209, Geometric Loss: 0.8339
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6208
  avg_peak_to_mean_ratio: 19.1454
  avg_peak_to_second_ratio: 1.0038
  detection_rate: 0.6250
  Overall Confidence Score: 5.8487
train Heatmap Components:
  mse_loss: 1.3724
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 149.1049
  peak_enhancement_loss: 32.4789
  peak_to_second_ratio_loss: 0.4668
  detection_rate_loss: 2.6021
  segmentation_guidance_loss: 0.4690
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1436.8765, Seg Loss: 0.3405, Heatmap Loss: 957.3053, Geometric Loss: 0.7224
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.1282
  avg_peak_to_mean_ratio: 2.5205
  avg_peak_to_second_ratio: 1.0019
  detection_rate: 0.9688
  Overall Confidence Score: 1.6548
val Heatmap Components:
  mse_loss: 0.5308
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 99.7576
  peak_enhancement_loss: 88.7900
  peak_to_second_ratio_loss: 0.4853
  detection_rate_loss: 0.1761
  segmentation_guidance_loss: 0.1352
Current learning rate: 0.000497

Epoch 75/80
----------
 25%|██████████████████████                                                                  | 1/4 [00:05<00:16,  5.51s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:11<00:11,  5.57s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.88s/it]
train Loss: 1441.7322, Seg Loss: 0.3891, Heatmap Loss: 960.3775, Geometric Loss: 0.9710
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2264
  avg_peak_to_mean_ratio: 59.0127
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 0.6071
  Overall Confidence Score: 15.7119
train Heatmap Components:
  mse_loss: 1.2528
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 121.9523
  peak_enhancement_loss: 56.0184
  peak_to_second_ratio_loss: 0.4764
  detection_rate_loss: 2.3774
  segmentation_guidance_loss: 0.0649
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1475.3892, Seg Loss: 0.3370, Heatmap Loss: 983.0159, Geometric Loss: 0.6604
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5477
  avg_peak_to_mean_ratio: 101.3946
  avg_peak_to_second_ratio: 1.0011
  detection_rate: 1.0000
  Overall Confidence Score: 26.4858
val Heatmap Components:
  mse_loss: 0.7549
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 146.6828
  peak_enhancement_loss: 74.8656
  peak_to_second_ratio_loss: 0.4826
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1310
Current learning rate: 0.000495

Epoch 76/80
----------
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.97s/it] 
train Loss: 1362.0916, Seg Loss: 0.4169, Heatmap Loss: 907.3449, Geometric Loss: 0.8216
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0902
  avg_peak_to_mean_ratio: 40.3032
  avg_peak_to_second_ratio: 1.0019
  detection_rate: 0.6964
  Overall Confidence Score: 11.2729
train Heatmap Components:
  mse_loss: 1.4361
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 80.5674
  peak_enhancement_loss: 33.2328
  peak_to_second_ratio_loss: 0.4790
  detection_rate_loss: 1.7332
  segmentation_guidance_loss: 0.9418
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.61s/it] 
val Loss: 1404.1807, Seg Loss: 0.3128, Heatmap Loss: 935.5856, Geometric Loss: 0.6117
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6831
  avg_peak_to_mean_ratio: 57.0382
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 15.4307
val Heatmap Components:
  mse_loss: 0.9108
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 94.8535
  peak_enhancement_loss: 67.2923
  peak_to_second_ratio_loss: 0.4875
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0554
Current learning rate: 0.000493

Epoch 77/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:04<00:14,  4.94s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:10<00:10,  5.11s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 75%|██████████████████████████████████████████████████████████████████                      | 3/4 [00:15<00:05,  5.41s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.79s/it]
train Loss: 1361.3748, Seg Loss: 0.4531, Heatmap Loss: 906.6905, Geometric Loss: 1.1075
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3204
  avg_peak_to_mean_ratio: 13.9884
  avg_peak_to_second_ratio: 1.0042
  detection_rate: 0.6071
  Overall Confidence Score: 4.4800
train Heatmap Components:
  mse_loss: 1.3541
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 62.4486
  peak_enhancement_loss: 45.5551
  peak_to_second_ratio_loss: 0.4698
  detection_rate_loss: 2.4822
  segmentation_guidance_loss: 1.2164
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:10<00:00,  2.67s/it] 
val Loss: 1415.6466, Seg Loss: 0.3185, Heatmap Loss: 943.2236, Geometric Loss: 0.6157
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7655
  avg_peak_to_mean_ratio: 68.2044
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 18.2428
val Heatmap Components:
  mse_loss: 0.9958
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 102.9836
  peak_enhancement_loss: 68.6695
  peak_to_second_ratio_loss: 0.4836
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0466
Current learning rate: 0.000491

Epoch 78/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([0]), torch.Size([8]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:19<00:00,  4.75s/it]
train Loss: 1632.0007, Seg Loss: 0.4364, Heatmap Loss: 1087.1922, Geometric Loss: 0.9700
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.2019
  avg_peak_to_mean_ratio: 45.1596
  avg_peak_to_second_ratio: 1.0045
  detection_rate: 0.7321
  Overall Confidence Score: 12.5246
train Heatmap Components:
  mse_loss: 1.5008
  separation_loss: 1000.0000
  peak_separation_loss: 285.7143
  edge_suppression_loss: 78.1883
  peak_enhancement_loss: 48.1953
  peak_to_second_ratio_loss: 0.4688
  detection_rate_loss: 1.3415
  segmentation_guidance_loss: 1.1736
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.02s/it] 
val Loss: 1380.1956, Seg Loss: 0.3095, Heatmap Loss: 919.6440, Geometric Loss: 0.5252
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.6763
  avg_peak_to_mean_ratio: 22.4994
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 6.7943
val Heatmap Components:
  mse_loss: 0.7808
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 61.5145
  peak_enhancement_loss: 80.8413
  peak_to_second_ratio_loss: 0.4834
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0964
Current learning rate: 0.000488

Epoch 79/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([8])]. Keeping as list.
 25%|██████████████████████                                                                  | 1/4 [00:06<00:19,  6.40s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([8]), torch.Size([8])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:12<00:12,  6.17s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:22<00:00,  5.56s/it]
train Loss: 1428.4548, Seg Loss: 0.4145, Heatmap Loss: 951.5257, Geometric Loss: 0.9398
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9053
  avg_peak_to_mean_ratio: 38.1507
  avg_peak_to_second_ratio: 1.0018
  detection_rate: 0.7500
  Overall Confidence Score: 10.7020
train Heatmap Components:
  mse_loss: 1.6399
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 146.1289
  peak_enhancement_loss: 27.2851
  peak_to_second_ratio_loss: 0.4801
  detection_rate_loss: 1.1553
  segmentation_guidance_loss: 0.3849
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:12<00:00,  3.02s/it] 
val Loss: 1372.7937, Seg Loss: 0.3222, Heatmap Loss: 914.6037, Geometric Loss: 0.7074
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7319
  avg_peak_to_mean_ratio: 32.8969
  avg_peak_to_second_ratio: 1.0015
  detection_rate: 1.0000
  Overall Confidence Score: 9.4076
val Heatmap Components:
  mse_loss: 0.7276
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 57.1670
  peak_enhancement_loss: 78.6620
  peak_to_second_ratio_loss: 0.4877
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2241
Current learning rate: 0.000485

Epoch 80/80
----------
  0%|                                                                                                | 0/4 [00:00<?, ?it/s]Warning: Tensors with key 'corners' have different shapes: [torch.Size([8]), torch.Size([0]), torch.Size([0]), torch.Size([0])]. Keeping as list.
 50%|████████████████████████████████████████████                                            | 2/4 [00:12<00:12,  6.02s/it]Warning: Tensors with key 'corners' have different shapes: [torch.Size([0]), torch.Size([8]), torch.Size([0]), torch.Size([0])]. Keeping as list.
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:21<00:00,  5.35s/it]
train Loss: 1409.8655, Seg Loss: 0.3760, Heatmap Loss: 939.1026, Geometric Loss: 1.0445
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5775
  avg_peak_to_mean_ratio: 24.3200
  avg_peak_to_second_ratio: 1.0056
  detection_rate: 0.6607
  Overall Confidence Score: 7.1410
train Heatmap Components:
  mse_loss: 1.3224
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 96.8443
  peak_enhancement_loss: 57.0694
  peak_to_second_ratio_loss: 0.4621
  detection_rate_loss: 1.7944
  segmentation_guidance_loss: 0.7040
100%|████████████████████████████████████████████████████████████████████████████████████████| 4/4 [00:11<00:00,  2.94s/it] 
val Loss: 1356.8428, Seg Loss: 0.2948, Heatmap Loss: 904.0485, Geometric Loss: 0.5941
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.7349
  avg_peak_to_mean_ratio: 31.6316
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 9.0920
val Heatmap Components:
  mse_loss: 0.6506
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 51.7491
  peak_enhancement_loss: 70.7835
  peak_to_second_ratio_loss: 0.4819
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.3687
Current learning rate: 0.000481

Training complete in 40m 25s
Best val loss: 1327.4674
Training completed!
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> 

