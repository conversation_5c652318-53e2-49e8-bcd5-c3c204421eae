PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> cd "app with custom ai\ChessVisionApp"; .\gradlew.bat assembleDebug
Calculating task graph as configuration cache cannot be reused because file 'app\build.gradle.kts' has changed.

> Task :app:compileDebugKotlin FAILED
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:5:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:6:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:13:2 Unresolved reference: Database     
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:21:2 Unresolved reference: TypeConverters
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:22:38 Unresolved reference: RoomDatabase
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/ChessVisionDatabase.kt:33:32 Unresolved reference: Room        
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:4:19 Unresolved reference: gson
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:5:19 Unresolved reference: gson
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:9:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:11:16 Unresolved reference: Gson
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:14:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:16:33 Unresolved reference: TypeToken
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:16:62 Unresolved reference: type
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:17:16 Unresolved reference: Gson
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:20:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:22:16 Unresolved reference: Gson
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:25:6 Unresolved reference: TypeConverter
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:27:32 Unresolved reference: TypeToken
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:27:68 Unresolved reference: type
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/Converters.kt:28:16 Unresolved reference: Gson
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:7:2 Unresolved reference: Dao
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:10:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:13:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:16:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:19:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:19:26 Unresolved reference: OnConflictStrategy  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:22:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:25:6 Unresolved reference: Delete
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:28:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:31:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:34:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/GameDao.kt:37:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:7:2 Unresolved reference: Dao
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:10:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:13:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:16:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:19:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:22:6 Unresolved reference: Insert
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:22:26 Unresolved reference: OnConflictStrategy
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:25:6 Unresolved reference: Update
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:28:6 Unresolved reference: Delete
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:31:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:34:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:37:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/database/dao/PositionDao.kt:40:6 Unresolved reference: Query
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/model/ChessModels.kt:3:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/model/ChessModels.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/model/ChessModels.kt:191:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/model/ChessModels.kt:193:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/model/ChessModels.kt:203:2 Unresolved reference: Entity
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/model/ChessModels.kt:205:6 Unresolved reference: PrimaryKey
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/FenDetectionRepository.kt:8:14 Unresolved reference: inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/FenDetectionRepository.kt:9:14 Unresolved reference: inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/FenDetectionRepository.kt:11:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/FenDetectionRepository.kt:12:31 Unresolved reference: Inject 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/GameRepository.kt:10:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/GameRepository.kt:11:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/GameRepository.kt:13:2 Unresolved reference: Singleton       
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/GameRepository.kt:14:23 Unresolved reference: Inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:7:8 Unresolved reference: timber      
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:8:14 Unresolved reference: inject     
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:9:14 Unresolved reference: inject     
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:11:2 Unresolved reference: Singleton  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:12:28 Unresolved reference: Inject    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:25:17 Unresolved reference: Timber    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:45:17 Unresolved reference: Timber    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:57:17 Unresolved reference: Timber    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:68:17 Unresolved reference: Timber    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/repository/StockfishRepository.kt:79:17 Unresolved reference: Timber    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:3:12 Unresolved reference: chaquo   
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:4:12 Unresolved reference: chaquo   
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:8:8 Unresolved reference: timber    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:9:14 Unresolved reference: inject   
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:10:14 Unresolved reference: inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:12:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:13:34 Unresolved reference: Inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:15:31 Unresolved reference: PyObject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:23:26 Unresolved reference: Python  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:28:13 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:30:13 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:42:37 Unresolved reference: callAttr
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:50:17 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:56:43 Unresolved reference: PyObject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:89:13 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:94:41 Unresolved reference: PyObject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:126:13 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:131:46 Unresolved reference: PyObject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/PythonFenDetectionService.kt:152:13 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:6:8 Unresolved reference: timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:9:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:10:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:12:2 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:13:24 Unresolved reference: Inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:26:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:29:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:54:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:85:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:92:9 Not enough information to infer type variable T
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:95:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:97:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:184:13 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/data/source/StockfishEngine.kt:186:13 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:4:17 Unresolved reference: room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:13:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:14:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:15:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:16:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:17:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:18:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:20:2 Unresolved reference: Module
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:21:2 Unresolved reference: InstallIn
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:21:12 Unresolved reference: SingletonComponent
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:24:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:25:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:27:10 Unresolved reference: ApplicationContext
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:29:16 Unresolved reference: Room
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:36:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:41:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:46:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:47:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:52:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:53:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:60:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:61:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:66:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:67:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:74:6 Unresolved reference: Provides
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/di/AppModule.kt:75:6 Unresolved reference: Singleton
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:5:17 Unresolved reference: navigation    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:6:17 Unresolved reference: navigation    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:7:17 Unresolved reference: navigation    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:16:20 Unresolved reference: NavHostController
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:19:5 Unresolved reference: NavHost       
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:24:9 Unresolved reference: composable    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:25:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:38:9 Unresolved reference: composable    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:39:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:45:25 Unresolved reference: popUpTo      
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:51:9 Unresolved reference: composable    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:54:13 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:56:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:67:9 Unresolved reference: composable    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:70:13 Cannot infer a type for this parameter. Please specify it explicitly.
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:72:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:80:9 Unresolved reference: composable    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/ChessVisionNavigation.kt:81:13 @Composable invocations can only happen from the context of a @Composable function
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:3:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:4:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:5:17 Unresolved reference: navigation
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:9:25 Unresolved reference: NamedNavArgument
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:18:13 Unresolved reference: navArgument
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:19:17 Unresolved reference: type
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:19:24 Unresolved reference: NavType
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:20:17 Unresolved reference: defaultValue
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:32:13 Unresolved reference: navArgument
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:33:17 Unresolved reference: type
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:33:24 Unresolved reference: NavType
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/navigation/Screen.kt:34:17 Unresolved reference: defaultValue
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:18:17 Unresolved reference: hilt       
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:27:36 Unresolved reference: hiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:108:36 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:109:46 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:121:33 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:122:33 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisScreen.kt:123:32 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:8:8 Unresolved reference: dagger    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:13:8 Unresolved reference: timber   
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:14:14 Unresolved reference: inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:26:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:27:26 Unresolved reference: Inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:46:13 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:68:17 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:111:17 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/analysis/AnalysisViewModel.kt:161:13 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraScreen.kt:7:29 Conflicting import, imported name 'Preview' is ambiguous
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraScreen.kt:27:44 Conflicting import, imported name 'Preview' is ambiguous
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraScreen.kt:207:35 Unresolved reference. None of the following candidates is applicable because of receiver type mismatch:
public val Icons.Filled.Preview: ImageVector defined in androidx.compose.material.icons.filled
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraScreen.kt:208:25 Unresolved reference: it
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraScreen.kt:395:2 Unresolved reference: Preview        
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraViewModel.kt:6:8 Unresolved reference: dagger        
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraViewModel.kt:12:14 Unresolved reference: inject      
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraViewModel.kt:21:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/camera/CameraViewModel.kt:22:24 Unresolved reference: Inject      
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:16:17 Unresolved reference: hilt   
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:26:38 Unresolved reference: hiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:223:61 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:226:64 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:239:44 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:246:29 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardScreen.kt:254:40 Smart cast to 'AnalysisResult' is impossible, because 'uiState.analysisResult' is a complex expression
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:8:8 Unresolved reference: dagger
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:13:8 Unresolved reference: timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:14:14 Unresolved reference: inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:26:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:27:28 Unresolved reference: Inject
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:51:13 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:96:13 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:146:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/chessboard/ChessBoardViewModel.kt:194:17 Unresolved reference: Timber
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/home/<USER>
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/home/<USER>
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/home/<USER>
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/home/<USER>
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/home/<USER>
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/home/<USER>
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsScreen.kt:15:17 Unresolved reference: hilt       
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsScreen.kt:22:36 Unresolved reference: hiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:7:8 Unresolved reference: dagger    
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:12:8 Unresolved reference: timber   
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:13:14 Unresolved reference: inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:42:2 Unresolved reference: HiltViewModel
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:43:26 Unresolved reference: Inject  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:68:17 Unresolved reference: Timber  
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:134:17 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:136:17 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:155:17 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:157:17 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:170:17 Unresolved reference: Timber 
e: file:///C:/Users/<USER>/OneDrive/Desktop/a1%20v1/app%20with%20custom%20ai/ChessVisionApp/app/src/main/java/com/chessvision/app/ui/screens/settings/SettingsViewModel.kt:172:17 Unresolved reference: Timber 

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --stacktrace option to get the stack trace.
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

BUILD FAILED in 30s
29 actionable tasks: 11 executed, 18 up-to-date
Configuration cache entry stored.
PS C:\Users\<USER>\OneDrive\Desktop\a1 v1\app with custom ai\ChessVisionApp> 
