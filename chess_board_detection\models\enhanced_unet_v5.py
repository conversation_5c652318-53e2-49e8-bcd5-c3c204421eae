"""
Enhanced U-Net model for chess board detection (v5).
This version includes specialized components for improved corner detection:
1. Peak sharpening module to improve peak-to-second ratio
2. Geometric refinement module for better geometric consistency
3. Detection recovery mechanism to improve detection rate
4. Regularization to improve validation stability
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from .enhanced_unet import EnhancedChessBoardUNet, DoubleConv, Down, Up, OutConv


class PeakSharpeningModule(nn.Module):
    """
    Module that enhances the primary peak in each heatmap while suppressing secondary peaks.
    This addresses the persistent challenge with peak-to-second ratio.
    """
    def __init__(self, kernel_size=5, channels=4):
        super(PeakSharpeningModule, self).__init__()
        
        # Convolutional layers to identify and enhance primary peaks
        self.peak_detector = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=kernel_size, padding=kernel_size//2, groups=channels),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.<PERSON>g<PERSON><PERSON>()
        )
        
        # Attention mechanism to focus on primary peaks
        self.attention = nn.Sequential(
            nn.Conv2d(channels, channels, kernel_size=1),
            nn.Sigmoid()
        )
        
        # Suppression mechanism for secondary peaks
        self.suppressor = nn.Sequential(
            nn.Conv2d(channels*2, channels, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1),
        )
    
    def forward(self, heatmaps):
        # Detect peaks
        peak_map = self.peak_detector(heatmaps)
        
        # Generate attention weights
        attention = self.attention(heatmaps)
        
        # Apply attention to enhance primary peaks
        enhanced = heatmaps * (1 + attention)
        
        # Concatenate original and enhanced heatmaps
        combined = torch.cat([heatmaps, enhanced], dim=1)
        
        # Apply suppression to reduce secondary peaks
        refined = self.suppressor(combined)
        
        # Residual connection
        output = heatmaps + refined
        
        return output


class GeometricRefinementModule(nn.Module):
    """
    Module that refines corner positions to ensure geometric consistency.
    This addresses the challenge with geometric loss.
    """
    def __init__(self, channels=4, features=64):
        super(GeometricRefinementModule, self).__init__()
        
        # Feature extraction
        self.features = nn.Sequential(
            nn.Conv2d(channels, features, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=3, padding=1),
            nn.ReLU()
        )
        
        # Geometric relationship modeling
        self.geometric = nn.Sequential(
            nn.Conv2d(features, features, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(features, features, kernel_size=3, padding=1),
            nn.ReLU()
        )
        
        # Refinement
        self.refine = nn.Sequential(
            nn.Conv2d(features + channels, channels, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        )
    
    def forward(self, heatmaps, segmentation=None):
        # Extract features
        feat = self.features(heatmaps)
        
        # Model geometric relationships
        geo = self.geometric(feat)
        
        # Concatenate with original heatmaps
        combined = torch.cat([geo, heatmaps], dim=1)
        
        # Refine heatmaps
        refined = self.refine(combined)
        
        # Residual connection
        output = heatmaps + refined
        
        return output


class DetectionRecoveryModule(nn.Module):
    """
    Module that ensures all corners are detected by recovering missed corners.
    This addresses the challenge with detection rate.
    """
    def __init__(self, channels=4, features=32):
        super(DetectionRecoveryModule, self).__init__()
        
        # Multi-scale detection
        self.multi_scale = nn.ModuleList([
            nn.Conv2d(channels, channels, kernel_size=k, padding=k//2, groups=channels)
            for k in [3, 5, 7]
        ])
        
        # Feature fusion
        self.fusion = nn.Sequential(
            nn.Conv2d(channels * 3, features, kernel_size=1),
            nn.ReLU(),
            nn.Conv2d(features, channels, kernel_size=3, padding=1),
            nn.Sigmoid()
        )
        
        # Recovery mechanism
        self.recovery = nn.Sequential(
            nn.Conv2d(channels * 2, channels, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        )
    
    def forward(self, heatmaps, segmentation=None):
        # Multi-scale detection
        multi_scale_features = [module(heatmaps) for module in self.multi_scale]
        
        # Concatenate multi-scale features
        concat = torch.cat(multi_scale_features, dim=1)
        
        # Fuse features
        recovery_weights = self.fusion(concat)
        
        # Apply recovery weights
        recovery_heatmaps = heatmaps * recovery_weights
        
        # Concatenate with original heatmaps
        combined = torch.cat([heatmaps, recovery_heatmaps], dim=1)
        
        # Generate recovery heatmaps
        recovery = self.recovery(combined)
        
        # Combine with original heatmaps
        output = heatmaps + recovery
        
        return output


class EnhancedChessBoardUNetV5(nn.Module):
    """
    Enhanced U-Net for chess board detection with v5 improvements:
    - Peak sharpening module
    - Geometric refinement stage
    - Detection recovery mechanism
    - Dropout for regularization
    """
    def __init__(self, n_channels=3, bilinear=True, dropout_rate=0.2):
        super(EnhancedChessBoardUNetV5, self).__init__()
        
        # Base architecture from v4
        self.base_model = EnhancedChessBoardUNet(n_channels, bilinear)
        
        # New peak sharpening module
        self.peak_sharpener = PeakSharpeningModule()
        
        # Geometric refinement stage
        self.geometric_refiner = GeometricRefinementModule()
        
        # Detection recovery mechanism
        self.detection_recovery = DetectionRecoveryModule()
        
        # Add dropout to segmentation branch
        self.segmentation_dropout = nn.Dropout2d(dropout_rate)
    
    def forward(self, x):
        # Get base outputs
        outputs = self.base_model(x)
        
        # Apply dropout to segmentation
        outputs['segmentation'] = self.segmentation_dropout(outputs['segmentation'])
        
        # Apply peak sharpening to heatmaps
        sharpened_heatmaps = self.peak_sharpener(outputs['corner_heatmaps'])
        
        # Apply geometric refinement
        refined_heatmaps = self.geometric_refiner(sharpened_heatmaps, outputs['segmentation'])
        
        # Apply detection recovery
        recovered_heatmaps = self.detection_recovery(refined_heatmaps, outputs['segmentation'])
        
        # Update outputs
        outputs['corner_heatmaps'] = recovered_heatmaps
        
        return outputs
