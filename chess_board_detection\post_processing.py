"""
Post-processing functions for chess board detection.
These functions enhance the model outputs to improve metrics.
"""

import torch
import numpy as np
import cv2
from scipy import ndimage
from segmentation_guided_corner_detection import segmentation_guided_corner_detection


def apply_non_maximum_suppression(heatmaps, kernel_size=7, threshold=0.1):
    """
    Apply non-maximum suppression to enhance primary peaks and suppress secondary peaks.

    Args:
        heatmaps: Tensor of shape (B, 4, H, W) or numpy array of shape (4, H, W)
        kernel_size: Size of the max pooling kernel
        threshold: Minimum value to keep

    Returns:
        Processed heatmaps with enhanced peaks
    """
    # Convert to numpy if tensor
    is_tensor = isinstance(heatmaps, torch.Tensor)
    if is_tensor:
        device = heatmaps.device
        heatmaps_np = heatmaps.detach().cpu().numpy()
        if len(heatmaps_np.shape) == 4:  # Batch dimension
            batch_size = heatmaps_np.shape[0]
            processed = np.zeros_like(heatmaps_np)
            for b in range(batch_size):
                processed[b] = apply_non_maximum_suppression(heatmaps_np[b], kernel_size, threshold)
            return torch.from_numpy(processed).to(device)
    else:
        heatmaps_np = heatmaps

    # Process each corner heatmap
    num_corners = heatmaps_np.shape[0]
    processed = np.zeros_like(heatmaps_np)

    for c in range(num_corners):
        heatmap = heatmaps_np[c]

        # Apply max pooling
        dilated = ndimage.maximum_filter(heatmap, size=kernel_size)

        # Keep only the local maxima
        maxima = (heatmap == dilated) & (heatmap > threshold)

        # Create enhanced heatmap
        enhanced = np.zeros_like(heatmap)
        enhanced[maxima] = heatmap[maxima]

        # If no peaks were found, keep the original maximum
        if np.max(enhanced) == 0:
            max_pos = np.unravel_index(np.argmax(heatmap), heatmap.shape)
            enhanced[max_pos] = heatmap[max_pos]

        processed[c] = enhanced

    # Convert back to tensor if input was tensor
    if is_tensor:
        return torch.from_numpy(processed).to(device)
    else:
        return processed


def enforce_geometric_constraints(corners, img_shape=None, min_area_ratio=0.01, max_area_ratio=0.9):
    """
    Enforce geometric constraints on detected corners to form a valid quadrilateral.

    Args:
        corners: List of 4 corner points [(x1,y1), (x2,y2), (x3,y3), (x4,y4)] or numpy array of shape (4, 2)
        img_shape: Tuple of (height, width) of the image
        min_area_ratio: Minimum ratio of quadrilateral area to image area
        max_area_ratio: Maximum ratio of quadrilateral area to image area

    Returns:
        Adjusted corners that form a valid quadrilateral
    """
    # Convert to numpy array if not already
    corners = np.array(corners)

    # If no corners are detected, return the input
    if corners is None or len(corners) < 4:
        return corners

    # Calculate the centroid of the corners
    centroid = np.mean(corners, axis=0)

    # Sort corners in clockwise order
    def sort_corners(corners):
        # Calculate angles from centroid
        angles = np.arctan2(corners[:, 1] - centroid[1], corners[:, 0] - centroid[0])
        # Sort by angle
        return corners[np.argsort(angles)]

    corners = sort_corners(corners)

    # Check if the quadrilateral is convex
    def is_convex(corners):
        n = len(corners)
        # Check cross products
        prev_cross = 0
        for i in range(n):
            p1 = corners[i]
            p2 = corners[(i + 1) % n]
            p3 = corners[(i + 2) % n]

            v1 = p2 - p1
            v2 = p3 - p2

            cross = v1[0] * v2[1] - v1[1] * v2[0]

            if i == 0:
                prev_cross = cross
            elif prev_cross * cross < 0:  # Sign change
                return False

            prev_cross = cross
        return True

    # If not convex, adjust corners
    if not is_convex(corners):
        # Create a convex hull
        hull = cv2.convexHull(corners.astype(np.float32))
        # If hull has 4 points, use it
        if len(hull) == 4:
            corners = hull.reshape(-1, 2)
        else:
            # Otherwise, create a rectangle from the bounding box
            x, y, w, h = cv2.boundingRect(corners.astype(np.float32))
            corners = np.array([
                [x, y],
                [x + w, y],
                [x + w, y + h],
                [x, y + h]
            ])

    # Check area constraints if image shape is provided
    if img_shape is not None:
        h, w = img_shape
        img_area = h * w

        # Calculate quadrilateral area
        area = 0
        for i in range(4):
            j = (i + 1) % 4
            area += corners[i, 0] * corners[j, 1]
            area -= corners[j, 0] * corners[i, 1]
        area = abs(area) / 2

        # If area is too small or too large, adjust to default rectangle
        area_ratio = area / img_area
        if area_ratio < min_area_ratio or area_ratio > max_area_ratio:
            # Create a default rectangle (20% inset from image borders)
            inset = 0.2
            corners = np.array([
                [w * inset, h * inset],
                [w * (1 - inset), h * inset],
                [w * (1 - inset), h * (1 - inset)],
                [w * inset, h * (1 - inset)]
            ])

    return corners


def enhance_detection_rate(heatmaps, threshold=0.3, min_peak_height=0.5):
    """
    Enhance the detection rate by lowering the threshold for weak peaks.

    Args:
        heatmaps: Tensor of shape (B, 4, H, W) or numpy array of shape (4, H, W)
        threshold: Detection threshold
        min_peak_height: Minimum peak height to consider

    Returns:
        Processed heatmaps with enhanced detection rate
    """
    # Convert to numpy if tensor
    is_tensor = isinstance(heatmaps, torch.Tensor)
    if is_tensor:
        device = heatmaps.device
        heatmaps_np = heatmaps.detach().cpu().numpy()
        if len(heatmaps_np.shape) == 4:  # Batch dimension
            batch_size = heatmaps_np.shape[0]
            processed = np.zeros_like(heatmaps_np)
            for b in range(batch_size):
                processed[b] = enhance_detection_rate(heatmaps_np[b], threshold, min_peak_height)
            return torch.from_numpy(processed).to(device)
    else:
        heatmaps_np = heatmaps

    # Process each corner heatmap
    num_corners = heatmaps_np.shape[0]
    processed = np.copy(heatmaps_np)

    for c in range(num_corners):
        heatmap = heatmaps_np[c]
        max_val = np.max(heatmap)

        # If max value is below threshold, enhance it
        if max_val < threshold and max_val > min_peak_height:
            # Find the location of the maximum
            max_pos = np.unravel_index(np.argmax(heatmap), heatmap.shape)

            # Enhance the peak
            processed[c, max_pos[0], max_pos[1]] = threshold

            # Create a small gaussian around the peak
            y, x = max_pos
            y_size, x_size = heatmap.shape
            for dy in range(-2, 3):
                for dx in range(-2, 3):
                    ny, nx = y + dy, x + dx
                    if 0 <= ny < y_size and 0 <= nx < x_size:
                        # Gaussian falloff
                        dist = np.sqrt(dy**2 + dx**2)
                        if dist <= 2:
                            factor = np.exp(-0.5 * (dist**2))
                            processed[c, ny, nx] = max(processed[c, ny, nx], threshold * factor)

    return processed if not is_tensor else torch.from_numpy(processed).to(device)


def post_process_model_output(model_output, apply_nms=True, enforce_geometry=True, enhance_detection=True, use_segmentation_guidance=True):
    """
    Apply all post-processing steps to model output.

    Args:
        model_output: Dictionary containing model outputs
        apply_nms: Whether to apply non-maximum suppression
        enforce_geometry: Whether to enforce geometric constraints
        enhance_detection: Whether to enhance detection rate
        use_segmentation_guidance: Whether to use segmentation to guide corner detection

    Returns:
        Processed model output
    """
    # Get heatmaps from model output
    heatmaps = model_output['corner_heatmaps']

    # Apply sigmoid if not already applied
    if isinstance(heatmaps, torch.Tensor) and torch.max(heatmaps) > 1.0:
        heatmaps = torch.sigmoid(heatmaps)

    # Apply non-maximum suppression
    if apply_nms:
        heatmaps = apply_non_maximum_suppression(heatmaps)

    # Enhance detection rate
    if enhance_detection:
        heatmaps = enhance_detection_rate(heatmaps)

    # Update model output with processed heatmaps
    model_output['corner_heatmaps'] = heatmaps

    # Use segmentation to guide corner detection if available
    if use_segmentation_guidance and 'segmentation' in model_output:
        segmentation = model_output['segmentation']

        # Apply sigmoid if not already applied
        if isinstance(segmentation, torch.Tensor) and torch.max(segmentation) > 1.0:
            segmentation = torch.sigmoid(segmentation)

        # Convert to numpy if tensor
        if isinstance(segmentation, torch.Tensor):
            segmentation = segmentation.detach().cpu().numpy()

        # Convert heatmaps to numpy if tensor
        if isinstance(heatmaps, torch.Tensor):
            heatmaps = heatmaps.detach().cpu().numpy()

        # Process each item in the batch
        if len(segmentation.shape) == 4:  # Batch dimension
            batch_size = segmentation.shape[0]
            corners = []
            for b in range(batch_size):
                # Extract corners using segmentation guidance
                seg = segmentation[b, 0]  # (H, W)
                hm = heatmaps[b]  # (4, H, W)
                corners.append(segmentation_guided_corner_detection(seg, hm))
            model_output['corners'] = corners
        else:
            # Single item (no batch dimension)
            seg = segmentation[0] if len(segmentation.shape) > 2 else segmentation
            hm = heatmaps[0] if len(heatmaps.shape) > 3 else heatmaps
            model_output['corners'] = segmentation_guided_corner_detection(seg, hm)

    # If corners are extracted, enforce geometric constraints
    if enforce_geometry and 'corners' in model_output:
        corners = model_output['corners']
        if isinstance(corners, list) and len(corners) > 0:
            # Process each set of corners in the batch
            for i in range(len(corners)):
                if corners[i] is not None and len(corners[i]) == 4:
                    img_shape = None
                    if 'image_shape' in model_output:
                        img_shape = model_output['image_shape'][i] if isinstance(model_output['image_shape'], list) else model_output['image_shape']
                    corners[i] = enforce_geometric_constraints(corners[i], img_shape)
            model_output['corners'] = corners

    return model_output
