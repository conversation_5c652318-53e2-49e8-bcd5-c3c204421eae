"""
Enhanced ensemble techniques for chess board detection (v5.2).

This module implements:
1. Dynamic ensemble weighting based on model performance on specific metrics
2. Specialized ensemble members for different aspects of the task
3. Adaptive ensemble combination strategies
4. Confidence-based ensemble fusion
"""

import os
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from collections import defaultdict
import json
import matplotlib.pyplot as plt
from models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from utils.metrics import calculate_corner_confidence


class DynamicEnsembleModel(nn.Module):
    """
    Ensemble model with dynamic weighting based on model performance.
    
    This ensemble:
    1. Dynamically adjusts weights based on each model's performance on specific metrics
    2. Uses specialized models for different aspects (segmentation, corner detection)
    3. Applies adaptive fusion strategies based on input characteristics
    4. Provides confidence metrics for ensemble predictions
    """
    def __init__(self, model_paths, device='cpu', metric_weights=None):
        """
        Initialize the ensemble.
        
        Args:
            model_paths: List of paths to model checkpoints
            device: Device to run models on
            metric_weights: Dictionary of metric names to weights for ensemble weighting
        """
        super(DynamicEnsembleModel, self).__init__()
        self.device = device
        self.models = []
        self.model_names = []
        self.model_weights = []
        self.model_specialties = []
        self.model_metrics = {}
        
        # Default metric weights if not provided
        self.metric_weights = metric_weights or {
            'peak_to_second_ratio': 2.0,  # Most important metric
            'detection_rate': 1.5,
            'segmentation_iou': 1.0,
            'geometric_consistency': 0.8
        }
        
        # Load models
        for path in model_paths:
            model_name = os.path.basename(path)
            model = EnhancedChessBoardUNetV5_2(n_channels=3)
            model.load_state_dict(torch.load(path, map_location=device))
            model.to(device)
            model.eval()
            
            self.models.append(model)
            self.model_names.append(model_name)
            
            # Initial equal weights
            self.model_weights.append(1.0 / len(model_paths))
            
            # Try to determine model specialty from filename
            specialty = self._determine_specialty(model_name)
            self.model_specialties.append(specialty)
            
            # Initialize metrics
            self.model_metrics[model_name] = {
                'peak_to_second_ratio': 1.0,
                'detection_rate': 0.9,
                'segmentation_iou': 0.85,
                'geometric_consistency': 0.9
            }
        
        # Adaptive fusion parameters
        self.fusion_temperature = 1.0  # Controls sharpness of softmax weighting
        self.confidence_threshold = 0.7  # Threshold for high-confidence predictions
        
        # Track performance history
        self.performance_history = defaultdict(list)
    
    def _determine_specialty(self, model_name):
        """Determine model specialty based on filename"""
        name_lower = model_name.lower()
        if 'seg' in name_lower:
            return 'segmentation'
        elif 'corner' in name_lower or 'peak' in name_lower:
            return 'corner_detection'
        elif 'geometric' in name_lower:
            return 'geometric'
        else:
            return 'general'
    
    def update_model_metrics(self, metrics_dict):
        """
        Update metrics for each model.
        
        Args:
            metrics_dict: Dictionary mapping model names to their metrics
        """
        for model_name, metrics in metrics_dict.items():
            if model_name in self.model_metrics:
                self.model_metrics[model_name].update(metrics)
                
                # Record in history
                for metric_name, value in metrics.items():
                    self.performance_history[f"{model_name}_{metric_name}"].append(value)
        
        # Update weights based on new metrics
        self._update_weights()
    
    def _update_weights(self):
        """Update model weights based on their performance metrics"""
        # Calculate weighted score for each model
        scores = []
        for i, model_name in enumerate(self.model_names):
            metrics = self.model_metrics[model_name]
            specialty = self.model_specialties[i]
            
            # Base score from metrics
            score = sum(metrics.get(metric, 0) * weight 
                        for metric, weight in self.metric_weights.items())
            
            # Boost score for specialized models in their domain
            if specialty == 'segmentation':
                score *= 1.2 * metrics.get('segmentation_iou', 1.0)
            elif specialty == 'corner_detection':
                score *= 1.2 * metrics.get('peak_to_second_ratio', 1.0)
            elif specialty == 'geometric':
                score *= 1.2 * metrics.get('geometric_consistency', 1.0)
            
            scores.append(score)
        
        # Convert scores to weights using softmax with temperature
        scores = torch.tensor(scores) / self.fusion_temperature
        weights = F.softmax(scores, dim=0).tolist()
        
        # Update model weights
        self.model_weights = weights
    
    def forward(self, x, input_type=None):
        """
        Forward pass through the ensemble.
        
        Args:
            x: Input tensor
            input_type: Optional hint about input type for adaptive weighting
            
        Returns:
            Tuple of (segmentation, heatmaps) with ensemble predictions
        """
        batch_size = x.size(0)
        
        # Get predictions from all models
        all_segmentations = []
        all_heatmaps = []
        all_confidences = []
        
        with torch.no_grad():
            for i, model in enumerate(self.models):
                seg, hm = model(x)
                all_segmentations.append(seg)
                all_heatmaps.append(hm)
                
                # Calculate confidence for this model's predictions
                confidence = self._calculate_prediction_confidence(seg, hm)
                all_confidences.append(confidence)
        
        # Adjust weights based on input-specific confidence if input_type is provided
        if input_type is not None:
            adjusted_weights = self._adjust_weights_for_input(input_type, all_confidences)
        else:
            adjusted_weights = self.model_weights
        
        # Convert to tensor for weighted averaging
        weights_tensor = torch.tensor(adjusted_weights, device=self.device).view(-1, 1, 1, 1)
        
        # Weighted average for segmentation
        seg_stack = torch.stack(all_segmentations)
        weighted_seg = (seg_stack * weights_tensor.view(-1, 1, 1, 1, 1)).sum(dim=0)
        
        # Weighted average for heatmaps
        hm_stack = torch.stack(all_heatmaps)
        weighted_hm = (hm_stack * weights_tensor.view(-1, 1, 1, 1, 1)).sum(dim=0)
        
        return weighted_seg, weighted_hm
    
    def _calculate_prediction_confidence(self, segmentation, heatmaps):
        """Calculate confidence score for a model's predictions"""
        # For segmentation: measure "decisiveness" of predictions
        seg_confidence = torch.mean(torch.abs(segmentation - 0.5) * 2).item()
        
        # For heatmaps: use peak-to-second ratio as confidence
        hm_confidence = 0
        for b in range(heatmaps.size(0)):
            for c in range(heatmaps.size(1)):
                hm = heatmaps[b, c].view(-1)
                values, _ = torch.topk(hm, k=2)
                if values.size(0) >= 2 and values[1] > 0:
                    ratio = values[0] / values[1]
                    hm_confidence += ratio.item()
        
        hm_confidence /= (heatmaps.size(0) * heatmaps.size(1))
        
        # Combined confidence
        return 0.4 * seg_confidence + 0.6 * hm_confidence
    
    def _adjust_weights_for_input(self, input_type, confidences):
        """Adjust weights based on input type and model confidences"""
        adjusted_weights = self.model_weights.copy()
        
        # Boost weights for specialized models based on input type
        for i, specialty in enumerate(self.model_specialties):
            if specialty == input_type:
                adjusted_weights[i] *= 1.5
            
            # Also consider confidence
            if confidences[i] > self.confidence_threshold:
                adjusted_weights[i] *= (1.0 + (confidences[i] - self.confidence_threshold))
        
        # Normalize weights
        total = sum(adjusted_weights)
        return [w / total for w in adjusted_weights]
    
    def get_ensemble_stats(self):
        """Get statistics about the ensemble"""
        return {
            'model_names': self.model_names,
            'model_weights': self.model_weights,
            'model_specialties': self.model_specialties,
            'fusion_temperature': self.fusion_temperature
        }
    
    def visualize_weights(self, save_path=None):
        """Visualize model weights"""
        plt.figure(figsize=(10, 6))
        plt.bar(self.model_names, self.model_weights)
        plt.xlabel('Model')
        plt.ylabel('Weight')
        plt.title('Ensemble Model Weights')
        plt.xticks(rotation=45)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
