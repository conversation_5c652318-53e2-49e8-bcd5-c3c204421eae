"""
Simple Continue Training Script for Chess Piece Detection

This script continues training from the best model (epoch 86) exactly as it was before the crash,
without any modifications to the weights or training strategy.
"""

import os
import sys
import gc
import torch
from ultralytics import YOLO

# Configuration
CONFIG = {
    # Paths
    "base_model_path": "runs/detect/train/weights/best.pt",  # Best model from epoch 86
    "dataset_yaml": "chess_board_detection/piece_detection/targeted_dataset/dataset.yaml",
    "output_dir": "runs/detect/simple_continue",

    # Training parameters
    "start_epoch": 91,  # Continue from epoch 91 (after 90)
    "epochs": 10,       # Train for 10 more epochs (to reach 100)
    "batch_size": 16,   # Same batch size as original training
    "img_size": 416,    # Same image size as original training

    # Memory optimization
    "cache": False,     # Disable cache to reduce memory usage
    "workers": 4,       # Reduce number of workers
    "amp": True,        # Use mixed precision to reduce memory usage
}

def set_seed(seed=42):
    """Set random seed for reproducibility."""
    import random
    import numpy as np
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)

def print_system_info():
    """Print system information for debugging."""
    print(f"Python version: {sys.version}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")

    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        print(f"CUDA Version: {torch.version.cuda}")
        print(f"cuDNN Version: {torch.backends.cudnn.version()}")

def optimize_memory():
    """Optimize memory usage to prevent crashes."""
    # Clear CUDA cache
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

    # Run garbage collection
    gc.collect()

    # Print memory usage
    if torch.cuda.is_available():
        print(f"GPU Memory allocated: {torch.cuda.memory_allocated() / 1e9:.2f} GB")
        print(f"GPU Memory reserved: {torch.cuda.memory_reserved() / 1e9:.2f} GB")

def continue_training():
    """Continue training from the best model without any modifications."""
    # Set random seed
    set_seed(42)

    # Print system information
    print_system_info()

    # Create output directory
    os.makedirs(CONFIG["output_dir"], exist_ok=True)

    # Load model
    print(f"Loading model from {CONFIG['base_model_path']}...")
    model = YOLO(CONFIG["base_model_path"])

    # Optimize memory before training
    optimize_memory()

    # Initialize current epoch
    current_epoch = CONFIG["start_epoch"]

    # Continue training one epoch at a time
    while True:
        # Print current training status
        print(f"\nContinuing training for epoch {current_epoch}...")
        print(f"Using the exact same configuration as before the crash")

        # Train for one epoch
        results = model.train(
            data=CONFIG["dataset_yaml"],
            epochs=1,  # Train for just one epoch at a time
            imgsz=CONFIG["img_size"],
            batch=CONFIG["batch_size"],
            device=0,
            workers=CONFIG["workers"],
            project=CONFIG["output_dir"],
            name=f"continue_epoch{current_epoch}",
            exist_ok=True,
            pretrained=False,
            verbose=True,
            seed=42,
            cache=CONFIG["cache"],
            close_mosaic=10,
            amp=CONFIG["amp"],
            # Use the same augmentation as the original training
            augment=True,
            mosaic=0.0,  # Mosaic was disabled after epoch 90
            mixup=0.0,
            degrees=0.0,
            translate=0.05,
            scale=0.1,
            shear=0.0,
            fliplr=0.5,
            perspective=0.0,
            # Use the same learning rate settings
            lr0=0.0001,
            lrf=0.00001,
            # Use the same validation settings
            val=True,
            # Use the same NMS settings
            conf=0.001,
            iou=0.7
        )

        # Optimize memory after training
        optimize_memory()

        # Increment current epoch
        current_epoch += 1

        # Ask user if they want to continue training
        print("\n" + "="*50)
        print(f"Completed epoch {current_epoch-1}.")
        print("="*50)

        # Extract and display metrics if available
        try:
            # Get validation results
            val_results = results.validator.metrics.results_dict
            precision = val_results.get('precision', 0)
            recall = val_results.get('recall', 0)
            map50 = val_results.get('map50', 0)
            map = val_results.get('map', 0)

            # Get training loss values
            train_stats = results.results_dict
            cls_loss = None
            box_loss = None
            dfl_loss = None

            for key in train_stats:
                if 'loss_cls' in key or 'cls_loss' in key:
                    cls_loss = float(train_stats[key][-1])  # Get the last value
                elif 'loss_box' in key or 'box_loss' in key:
                    box_loss = float(train_stats[key][-1])
                elif 'loss_dfl' in key or 'dfl_loss' in key:
                    dfl_loss = float(train_stats[key][-1])

            # Print metrics
            print("\nTraining Metrics:")
            if cls_loss is not None:
                print(f"Classification Loss: {cls_loss:.6f}")
            if box_loss is not None:
                print(f"Box Loss: {box_loss:.6f}")
            if dfl_loss is not None:
                print(f"DFL Loss: {dfl_loss:.6f}")

            print("\nValidation Metrics:")
            print(f"Precision: {precision:.4f}")
            print(f"Recall: {recall:.4f}")
            print(f"mAP50: {map50:.4f}")
            print(f"mAP50-95: {map:.4f}")
        except Exception as e:
            print(f"Could not extract metrics: {e}")

        # Ask user if they want to continue
        user_input = input("\nDo you want to continue training for another epoch? (y/n): ").strip().lower()

        if user_input != 'y':
            print("\nTraining stopped by user.")
            break

        # Check if we've reached the target number of epochs
        if current_epoch >= CONFIG["start_epoch"] + CONFIG["epochs"]:
            print(f"\nReached target of {CONFIG['start_epoch'] + CONFIG['epochs']} epochs.")
            user_input = input("Do you want to train for 10 more epochs? (y/n): ").strip().lower()

            if user_input != 'y':
                print("\nTraining completed.")
                break
            else:
                # Add 10 more epochs to the target
                CONFIG["epochs"] += 10
                print(f"\nExtending training to {CONFIG['start_epoch'] + CONFIG['epochs']} epochs.")

    print(f"\nTraining complete. Results saved to {CONFIG['output_dir']}")

if __name__ == "__main__":
    continue_training()
