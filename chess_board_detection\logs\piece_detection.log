2025-05-21 10:40:34,518 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:40:34,518 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:40:34,519 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg
2025-05-21 10:40:34,519 - INFO - Confidence threshold: 0.7
2025-05-21 10:40:34,520 - INFO - Show labels: True
2025-05-21 10:40:34,520 - INFO - Show confidence: False
2025-05-21 10:40:34,647 - INFO - Model loaded in 0.13 seconds
2025-05-21 10:40:34,648 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:40:36,524 - INFO - Inference completed in 1.88 seconds
2025-05-21 10:40:36,555 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:40:36,557 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:40:36,557 - INFO - Total detections after post-processing: 18
2025-05-21 10:40:36,557 - INFO - Starting visualization of results
2025-05-21 10:40:36,601 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_24.jpg
2025-05-21 10:40:36,602 - INFO - Visualization completed in 0.04 seconds
2025-05-21 10:40:36,602 - INFO - Detection counts by class:
2025-05-21 10:40:36,603 - INFO -   white_rook: 3
2025-05-21 10:40:36,603 - INFO -   white_bishop: 1
2025-05-21 10:40:36,604 - INFO -   white_pawn: 5
2025-05-21 10:40:36,604 - INFO -   black_pawn: 5
2025-05-21 10:40:36,604 - INFO -   black_rook: 1
2025-05-21 10:40:36,605 - INFO -   black_bishop: 1
2025-05-21 10:40:36,605 - INFO -   white_king: 1
2025-05-21 10:40:36,605 - INFO -   black_king: 1
2025-05-21 10:40:36,605 - INFO - Total processing time: 2.08 seconds
2025-05-21 10:40:39,252 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:40:39,253 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:40:39,254 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\25.jpg
2025-05-21 10:40:39,254 - INFO - Confidence threshold: 0.7
2025-05-21 10:40:39,255 - INFO - Show labels: True
2025-05-21 10:40:39,255 - INFO - Show confidence: False
2025-05-21 10:40:39,362 - INFO - Model loaded in 0.11 seconds
2025-05-21 10:40:39,363 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:40:41,290 - INFO - Inference completed in 1.93 seconds
2025-05-21 10:40:41,290 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:40:41,292 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:40:41,292 - INFO - Total detections after post-processing: 18
2025-05-21 10:40:41,292 - INFO - Starting visualization of results
2025-05-21 10:40:41,338 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_25.jpg
2025-05-21 10:40:41,339 - INFO - Visualization completed in 0.05 seconds
2025-05-21 10:40:41,340 - INFO - Detection counts by class:
2025-05-21 10:40:41,340 - INFO -   black_pawn: 4
2025-05-21 10:40:41,340 - INFO -   white_pawn: 4
2025-05-21 10:40:41,341 - INFO -   white_rook: 2
2025-05-21 10:40:41,341 - INFO -   black_rook: 2
2025-05-21 10:40:41,341 - INFO -   white_king: 1
2025-05-21 10:40:41,342 - INFO -   black_queen: 2
2025-05-21 10:40:41,342 - INFO -   black_knight: 1
2025-05-21 10:40:41,342 - INFO -   white_knight: 1
2025-05-21 10:40:41,342 - INFO -   black_king: 1
2025-05-21 10:40:41,343 - INFO - Total processing time: 2.09 seconds
2025-05-21 10:40:44,027 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:40:44,027 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:40:44,027 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\26.jpg
2025-05-21 10:40:44,028 - INFO - Confidence threshold: 0.7
2025-05-21 10:40:44,028 - INFO - Show labels: True
2025-05-21 10:40:44,028 - INFO - Show confidence: False
2025-05-21 10:40:44,132 - INFO - Model loaded in 0.10 seconds
2025-05-21 10:40:44,132 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:40:46,011 - INFO - Inference completed in 1.88 seconds
2025-05-21 10:40:46,011 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:40:46,015 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:40:46,016 - INFO - Total detections after post-processing: 20
2025-05-21 10:40:46,016 - INFO - Starting visualization of results
2025-05-21 10:40:46,063 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_26.jpg
2025-05-21 10:40:46,064 - INFO - Visualization completed in 0.05 seconds
2025-05-21 10:40:46,064 - INFO - Detection counts by class:
2025-05-21 10:40:46,066 - INFO -   black_pawn: 5
2025-05-21 10:40:46,066 - INFO -   white_bishop: 2
2025-05-21 10:40:46,066 - INFO -   white_rook: 1
2025-05-21 10:40:46,066 - INFO -   black_queen: 1
2025-05-21 10:40:46,066 - INFO -   white_pawn: 5
2025-05-21 10:40:46,067 - INFO -   black_bishop: 2
2025-05-21 10:40:46,067 - INFO -   black_rook: 1
2025-05-21 10:40:46,067 - INFO -   white_queen: 1
2025-05-21 10:40:46,068 - INFO -   white_king: 1
2025-05-21 10:40:46,068 - INFO -   black_king: 1
2025-05-21 10:40:46,068 - INFO - Total processing time: 2.04 seconds
2025-05-21 10:40:48,654 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:40:48,655 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:40:48,655 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\27.jpg
2025-05-21 10:40:48,655 - INFO - Confidence threshold: 0.7
2025-05-21 10:40:48,655 - INFO - Show labels: True
2025-05-21 10:40:48,656 - INFO - Show confidence: False
2025-05-21 10:40:48,763 - INFO - Model loaded in 0.11 seconds
2025-05-21 10:40:48,763 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:40:50,632 - INFO - Inference completed in 1.87 seconds
2025-05-21 10:40:50,632 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:40:50,634 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:40:50,635 - INFO - Total detections after post-processing: 23
2025-05-21 10:40:50,635 - INFO - Starting visualization of results
2025-05-21 10:40:50,683 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_27.jpg
2025-05-21 10:40:50,683 - INFO - Visualization completed in 0.05 seconds
2025-05-21 10:40:50,684 - INFO - Detection counts by class:
2025-05-21 10:40:50,685 - INFO -   black_king: 1
2025-05-21 10:40:50,685 - INFO -   white_bishop: 3
2025-05-21 10:40:50,685 - INFO -   black_pawn: 5
2025-05-21 10:40:50,686 - INFO -   white_pawn: 4
2025-05-21 10:40:50,686 - INFO -   black_bishop: 2
2025-05-21 10:40:50,686 - INFO -   black_rook: 2
2025-05-21 10:40:50,686 - INFO -   white_rook: 2
2025-05-21 10:40:50,687 - INFO -   black_knight: 2
2025-05-21 10:40:50,687 - INFO -   white_knight: 1
2025-05-21 10:40:50,687 - INFO -   white_king: 1
2025-05-21 10:40:50,687 - INFO - Total processing time: 2.03 seconds
2025-05-21 10:42:46,278 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:42:46,279 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:42:46,279 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\24.jpg
2025-05-21 10:42:46,279 - INFO - Confidence threshold: 0.7
2025-05-21 10:42:46,279 - INFO - Show labels: True
2025-05-21 10:42:46,279 - INFO - Show confidence: False
2025-05-21 10:42:46,388 - INFO - Model loaded in 0.11 seconds
2025-05-21 10:42:46,389 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:42:48,246 - INFO - Inference completed in 1.86 seconds
2025-05-21 10:42:48,246 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:42:48,248 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:42:48,248 - INFO - Total detections after post-processing: 18
2025-05-21 10:42:48,249 - INFO - Starting visualization of results
2025-05-21 10:42:48,290 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_24.jpg
2025-05-21 10:42:48,290 - INFO - Visualization completed in 0.04 seconds
2025-05-21 10:42:48,292 - INFO - Detection counts by class:
2025-05-21 10:42:48,292 - INFO -   white_rook: 3
2025-05-21 10:42:48,292 - INFO -   white_bishop: 1
2025-05-21 10:42:48,293 - INFO -   white_pawn: 5
2025-05-21 10:42:48,293 - INFO -   black_pawn: 5
2025-05-21 10:42:48,293 - INFO -   black_rook: 1
2025-05-21 10:42:48,294 - INFO -   black_bishop: 1
2025-05-21 10:42:48,294 - INFO -   white_king: 1
2025-05-21 10:42:48,294 - INFO -   black_king: 1
2025-05-21 10:42:48,294 - INFO - Total processing time: 2.02 seconds
2025-05-21 10:42:50,924 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:42:50,924 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:42:50,924 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\25.jpg
2025-05-21 10:42:50,925 - INFO - Confidence threshold: 0.7
2025-05-21 10:42:50,925 - INFO - Show labels: True
2025-05-21 10:42:50,925 - INFO - Show confidence: False
2025-05-21 10:42:51,022 - INFO - Model loaded in 0.10 seconds
2025-05-21 10:42:51,023 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:42:53,012 - INFO - Inference completed in 1.99 seconds
2025-05-21 10:42:53,012 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:42:53,014 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:42:53,014 - INFO - Total detections after post-processing: 18
2025-05-21 10:42:53,014 - INFO - Starting visualization of results
2025-05-21 10:42:53,060 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_25.jpg
2025-05-21 10:42:53,060 - INFO - Visualization completed in 0.04 seconds
2025-05-21 10:42:53,062 - INFO - Detection counts by class:
2025-05-21 10:42:53,062 - INFO -   black_pawn: 4
2025-05-21 10:42:53,062 - INFO -   white_pawn: 4
2025-05-21 10:42:53,063 - INFO -   white_rook: 2
2025-05-21 10:42:53,063 - INFO -   black_rook: 2
2025-05-21 10:42:53,063 - INFO -   white_king: 1
2025-05-21 10:42:53,063 - INFO -   black_queen: 2
2025-05-21 10:42:53,063 - INFO -   black_knight: 1
2025-05-21 10:42:53,063 - INFO -   white_knight: 1
2025-05-21 10:42:53,063 - INFO -   black_king: 1
2025-05-21 10:42:53,064 - INFO - Total processing time: 2.14 seconds
2025-05-21 10:42:55,769 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:42:55,770 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:42:55,770 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\26.jpg
2025-05-21 10:42:55,770 - INFO - Confidence threshold: 0.7
2025-05-21 10:42:55,770 - INFO - Show labels: True
2025-05-21 10:42:55,770 - INFO - Show confidence: False
2025-05-21 10:42:55,884 - INFO - Model loaded in 0.11 seconds
2025-05-21 10:42:55,885 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:42:57,662 - INFO - Inference completed in 1.78 seconds
2025-05-21 10:42:57,663 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:42:57,664 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:42:57,664 - INFO - Total detections after post-processing: 20
2025-05-21 10:42:57,665 - INFO - Starting visualization of results
2025-05-21 10:42:57,709 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_26.jpg
2025-05-21 10:42:57,709 - INFO - Visualization completed in 0.04 seconds
2025-05-21 10:42:57,710 - INFO - Detection counts by class:
2025-05-21 10:42:57,711 - INFO -   black_pawn: 5
2025-05-21 10:42:57,711 - INFO -   white_bishop: 2
2025-05-21 10:42:57,711 - INFO -   white_rook: 1
2025-05-21 10:42:57,711 - INFO -   black_queen: 1
2025-05-21 10:42:57,711 - INFO -   white_pawn: 5
2025-05-21 10:42:57,712 - INFO -   black_bishop: 2
2025-05-21 10:42:57,712 - INFO -   black_rook: 1
2025-05-21 10:42:57,712 - INFO -   white_queen: 1
2025-05-21 10:42:57,712 - INFO -   white_king: 1
2025-05-21 10:42:57,713 - INFO -   black_king: 1
2025-05-21 10:42:57,713 - INFO - Total processing time: 1.94 seconds
2025-05-21 10:43:00,410 - INFO - Output directory: chess_board_detection/outputs/piece_detection
2025-05-21 10:43:00,411 - INFO - Starting detection with model: chess_board_detection/piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt
2025-05-21 10:43:00,411 - INFO - Source: C:\Users\<USER>\OneDrive\Desktop\New folder (4)\27.jpg
2025-05-21 10:43:00,412 - INFO - Confidence threshold: 0.7
2025-05-21 10:43:00,412 - INFO - Show labels: True
2025-05-21 10:43:00,413 - INFO - Show confidence: False
2025-05-21 10:43:00,520 - INFO - Model loaded in 0.11 seconds
2025-05-21 10:43:00,521 - INFO - Running inference with IoU threshold: 0.9 (default is 0.45)
2025-05-21 10:43:02,558 - INFO - Inference completed in 2.04 seconds
2025-05-21 10:43:02,558 - INFO - Starting additional post-processing to remove overlapping detections
2025-05-21 10:43:02,560 - INFO - Post-processing completed in 0.00 seconds
2025-05-21 10:43:02,560 - INFO - Total detections after post-processing: 23
2025-05-21 10:43:02,560 - INFO - Starting visualization of results
2025-05-21 10:43:02,610 - INFO - Saved custom result to chess_board_detection\outputs\piece_detection\detection_27.jpg
2025-05-21 10:43:02,611 - INFO - Visualization completed in 0.05 seconds
2025-05-21 10:43:02,612 - INFO - Detection counts by class:
2025-05-21 10:43:02,612 - INFO -   black_king: 1
2025-05-21 10:43:02,612 - INFO -   white_bishop: 3
2025-05-21 10:43:02,613 - INFO -   black_pawn: 5
2025-05-21 10:43:02,613 - INFO -   white_pawn: 4
2025-05-21 10:43:02,613 - INFO -   black_bishop: 2
2025-05-21 10:43:02,613 - INFO -   black_rook: 2
2025-05-21 10:43:02,614 - INFO -   white_rook: 2
2025-05-21 10:43:02,614 - INFO -   black_knight: 2
2025-05-21 10:43:02,614 - INFO -   white_knight: 1
2025-05-21 10:43:02,614 - INFO -   white_king: 1
2025-05-21 10:43:02,615 - INFO - Total processing time: 2.20 seconds
