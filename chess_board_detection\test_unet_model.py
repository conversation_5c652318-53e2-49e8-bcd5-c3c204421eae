"""
Test the U-Net model to make sure it's working correctly.
"""

import torch
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.chessboard_segmentation_unet import get_model

def test_model():
    """Test the U-Net model."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Test standard U-Net
    print("Testing Standard U-Net...")
    model = get_model(model_type="standard", n_channels=3, n_classes=1)
    model = model.to(device)
    
    # Count parameters
    total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"Standard U-Net parameters: {total_params:,}")
    
    # Test forward pass
    x = torch.randn(2, 3, 256, 256).to(device)
    
    with torch.no_grad():
        output = model(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {output.shape}")
    
    print("Standard U-Net test passed!")
    
    # Test Attention U-Net
    print("\nTesting Attention U-Net...")
    att_model = get_model(model_type="attention", n_channels=3, n_classes=1)
    att_model = att_model.to(device)
    
    # Count parameters
    att_params = sum(p.numel() for p in att_model.parameters() if p.requires_grad)
    print(f"Attention U-Net parameters: {att_params:,}")
    
    # Test forward pass
    with torch.no_grad():
        att_output = att_model(x)
        print(f"Input shape: {x.shape}")
        print(f"Output shape: {att_output.shape}")
    
    print("Attention U-Net test passed!")
    
    return model, att_model

if __name__ == "__main__":
    test_model()
