"""
<PERSON><PERSON><PERSON> to visualize how models trained on 256x256 images handle real-world high-resolution images.
This shows the preprocessing steps and how predictions map between different resolutions.
"""

import os
import sys
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import time

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the model
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2

def load_model(model_path):
    """Load a trained model from the given path."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for model input, returning all intermediate steps."""
    # Load the original image
    original_image = Image.open(image_path).convert('RGB')
    original_np = np.array(original_image)
    original_size = original_image.size  # (width, height)
    
    # Step 1: Resize while maintaining aspect ratio
    w, h = original_image.size
    ratio = min(target_size[0] / w, target_size[1] / h)
    new_size = (int(w * ratio), int(h * ratio))
    resized_image = original_image.resize(new_size, Image.LANCZOS)
    resized_np = np.array(resized_image)
    
    # Step 2: Create a black canvas and paste the resized image
    canvas = Image.new('RGB', target_size, (0, 0, 0))
    offset = ((target_size[0] - new_size[0]) // 2, (target_size[1] - new_size[1]) // 2)
    canvas.paste(resized_image, offset)
    canvas_np = np.array(canvas)
    
    # Step 3: Convert to tensor and normalize
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    input_tensor = transform(canvas).unsqueeze(0)
    
    # Return all intermediate results
    return {
        'original_image': original_np,
        'original_size': original_size,
        'resized_image': resized_np,
        'resized_size': new_size,
        'canvas_image': canvas_np,
        'canvas_size': target_size,
        'input_tensor': input_tensor,
        'scale_ratio': ratio,
        'offset': offset
    }

def detect_corners(model, input_tensor):
    """Run inference with the model and extract corner coordinates."""
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation and heatmaps from outputs
    segmentation = outputs['segmentation']
    heatmaps = outputs['corner_heatmaps']
    
    # Apply sigmoid to get probability maps
    segmentation = torch.sigmoid(segmentation)
    heatmaps = torch.sigmoid(heatmaps)
    
    # Process heatmaps to get corner coordinates
    batch_size, num_keypoints, height, width = heatmaps.shape
    keypoints = []
    
    for k in range(num_keypoints):
        heatmap = heatmaps[0, k].cpu().numpy()
        # Find the location of the maximum value
        idx = np.argmax(heatmap)
        y, x = np.unravel_index(idx, heatmap.shape)
        confidence = heatmap[y, x]
        keypoints.append((x, y, confidence))
    
    return segmentation, heatmaps, keypoints

def map_keypoints_across_resolutions(keypoints, preprocessing_info):
    """Map keypoints between different resolutions."""
    # Map from model input (256x256) to original image
    model_to_original = []
    for x, y, conf in keypoints:
        # Remove offset (from padding)
        x_no_offset = x - preprocessing_info['offset'][0]
        y_no_offset = y - preprocessing_info['offset'][1]
        
        # Scale back to original size
        orig_x = x_no_offset / preprocessing_info['scale_ratio']
        orig_y = y_no_offset / preprocessing_info['scale_ratio']
        
        model_to_original.append((orig_x, orig_y, conf))
    
    # Map from model input to resized image (before padding)
    model_to_resized = []
    for x, y, conf in keypoints:
        # Remove offset (from padding)
        x_no_offset = x - preprocessing_info['offset'][0]
        y_no_offset = y - preprocessing_info['offset'][1]
        
        model_to_resized.append((x_no_offset, y_no_offset, conf))
    
    return {
        'model_input': keypoints,
        'resized': model_to_resized,
        'original': model_to_original
    }

def visualize_resolution_effects(model_name, preprocessing_info, segmentation, heatmaps, keypoints_mapped, output_path):
    """Create a visualization showing how the model handles different resolutions."""
    # Create figure with 3 rows and 3 columns
    fig = plt.figure(figsize=(18, 12))
    
    # Define grid layout
    gs = plt.GridSpec(3, 3, figure=fig)
    
    # Row 1: Original image with keypoints
    ax1 = fig.add_subplot(gs[0, :])
    ax1.imshow(preprocessing_info['original_image'])
    ax1.set_title(f'Original Image ({preprocessing_info["original_size"][0]}x{preprocessing_info["original_size"][1]}) with Detected Corners', fontsize=14)
    
    # Plot keypoints on original image
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['red', 'green', 'blue', 'yellow']
    
    # Draw keypoints and lines
    xs, ys = [], []
    for i, (x, y, conf) in enumerate(keypoints_mapped['original']):
        xs.append(x)
        ys.append(y)
        ax1.scatter(x, y, c=colors[i], marker='x', s=100, linewidths=2)
        ax1.text(x+10, y+10, f"{corner_names[i]}\n{conf:.3f}", 
                color=colors[i], fontsize=10,
                bbox=dict(facecolor='white', alpha=0.7))
    
    # Close the polygon
    xs.append(xs[0])
    ys.append(ys[0])
    ax1.plot(xs, ys, 'g-', linewidth=2, alpha=0.7)
    ax1.axis('off')
    
    # Row 2, Col 1: Resized image (before padding)
    ax2 = fig.add_subplot(gs[1, 0])
    ax2.imshow(preprocessing_info['resized_image'])
    ax2.set_title(f'Resized Image ({preprocessing_info["resized_size"][0]}x{preprocessing_info["resized_size"][1]})', fontsize=12)
    
    # Plot keypoints on resized image
    for i, (x, y, conf) in enumerate(keypoints_mapped['resized']):
        ax2.scatter(x, y, c=colors[i], marker='x', s=50, linewidths=2)
    ax2.axis('off')
    
    # Row 2, Col 2: Model input (256x256 with padding)
    ax3 = fig.add_subplot(gs[1, 1])
    ax3.imshow(preprocessing_info['canvas_image'])
    ax3.set_title(f'Model Input (256x256 with padding)', fontsize=12)
    
    # Plot keypoints on model input
    for i, (x, y, conf) in enumerate(keypoints_mapped['model_input']):
        ax3.scatter(x, y, c=colors[i], marker='x', s=50, linewidths=2)
    
    # Show padding boundaries
    offset_x, offset_y = preprocessing_info['offset']
    resized_w, resized_h = preprocessing_info['resized_size']
    rect = plt.Rectangle((offset_x, offset_y), resized_w, resized_h, 
                         linewidth=1, edgecolor='white', facecolor='none')
    ax3.add_patch(rect)
    ax3.axis('off')
    
    # Row 2, Col 3: Segmentation mask
    ax4 = fig.add_subplot(gs[1, 2])
    ax4.imshow(segmentation[0, 0].cpu().numpy(), cmap='gray')
    ax4.set_title('Segmentation Mask (256x256)', fontsize=12)
    ax4.axis('off')
    
    # Row 3: Heatmaps
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i in range(4):
        ax = fig.add_subplot(gs[2, i % 3] if i < 3 else gs[2, 2])
        hm = heatmaps[0, i].cpu().numpy()
        ax.imshow(hm, cmap='jet')
        ax.set_title(f'{corner_names[i]} Heatmap\nConfidence: {keypoints_mapped["model_input"][i][2]:.3f}', fontsize=12)
        
        # Mark the detected peak
        x, y, _ = keypoints_mapped['model_input'][i]
        ax.scatter(x, y, c='white', marker='x', s=50, linewidths=2)
        ax.axis('off')
        
        # If this is the last heatmap, add a colorbar
        if i == 3:
            from mpl_toolkits.axes_grid1 import make_axes_locatable
            divider = make_axes_locatable(ax)
            cax = divider.append_axes("right", size="5%", pad=0.05)
            plt.colorbar(ax.get_images()[0], cax=cax)
    
    # Add model name as overall title
    fig.suptitle(f'Resolution Effects Analysis - {model_name}', fontsize=16)
    
    # Adjust layout and save
    plt.tight_layout(rect=[0, 0, 1, 0.96])
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    return output_path

def main():
    # Define paths
    image_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg"
    output_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs"
    
    model_paths = {
        "Phase2_Epoch16": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth",
        "Phase3_Epoch8": "chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase3.pth"
    }
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Process each model
    for model_name, model_path in model_paths.items():
        print(f"Processing {model_name}...")
        
        # Load model
        model = load_model(model_path)
        
        # Preprocess image, getting all intermediate steps
        preprocessing_info = preprocess_image(image_path)
        
        # Detect corners
        segmentation, heatmaps, keypoints = detect_corners(model, preprocessing_info['input_tensor'])
        
        # Map keypoints across different resolutions
        keypoints_mapped = map_keypoints_across_resolutions(keypoints, preprocessing_info)
        
        # Create visualization
        output_path = os.path.join(output_dir, f"{model_name}_resolution_effects.png")
        visualize_resolution_effects(
            model_name,
            preprocessing_info,
            segmentation,
            heatmaps,
            keypoints_mapped,
            output_path
        )
        
        print(f"Resolution effects visualization saved to: {output_path}")
    
    print("All visualizations completed!")

if __name__ == "__main__":
    main()
