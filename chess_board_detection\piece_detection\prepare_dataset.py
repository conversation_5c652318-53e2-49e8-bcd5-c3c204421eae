"""
Prepare a dataset for training YOLO to detect chess pieces.
This script:
1. Takes images of chess boards
2. Detects the chess board using the distilled segmentation model
3. Extracts and normalizes the chess board region
4. Saves the normalized board images for annotation
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
import shutil
from tqdm import tqdm

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import models and utilities
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel

def load_model(model_path):
    """Load the distilled segmentation model."""
    model = TinySegmentationModel(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """Preprocess an image for model input."""
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def normalize_for_model(image):
    """Normalize image for model input."""
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)
    
    return input_tensor

def find_corners_from_segmentation(segmentation, threshold=0.5):
    """Find corners of the chess board using the segmentation mask."""
    # Create binary mask
    binary_mask = (segmentation > threshold).astype(np.uint8)
    
    # Apply morphological operations to clean up the mask
    kernel = np.ones((5, 5), np.uint8)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # Find the largest contour (the chess board)
    largest_contour = max(contours, key=cv2.contourArea)
    
    # Approximate the contour to get a polygon
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx_polygon = cv2.approxPolyDP(largest_contour, epsilon, True)
    
    # If we don't get exactly 4 corners, try to find the best 4 corners
    if len(approx_polygon) != 4:
        # If we have more than 4 corners, find the 4 corners that form the largest quadrilateral
        if len(approx_polygon) > 4:
            # Convert to a more convenient format
            points = [point[0] for point in approx_polygon]
            
            # Find the convex hull
            hull = cv2.convexHull(np.array(points))
            
            # Approximate the hull to get 4 corners
            epsilon = 0.02 * cv2.arcLength(hull, True)
            approx_polygon = cv2.approxPolyDP(hull, epsilon, True)
            
            # If we still don't have 4 corners, use the minimum area rectangle
            if len(approx_polygon) != 4:
                rect = cv2.minAreaRect(hull)
                box = cv2.boxPoints(rect)
                approx_polygon = np.int0(box)
        else:
            # If we have fewer than 4 corners, use the minimum area rectangle
            rect = cv2.minAreaRect(largest_contour)
            box = cv2.boxPoints(rect)
            approx_polygon = np.int0(box)
    
    # Extract corners
    corners = [(point[0][0], point[0][1]) for point in approx_polygon]
    
    # Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left
    corners = sort_corners(corners)
    
    return corners

def sort_corners(corners):
    """Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left."""
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)
    
    # Sort corners based on their angle from the center
    def get_angle(point):
        return np.arctan2(point[1] - center_y, point[0] - center_x)
    
    # Sort corners by angle
    sorted_corners = sorted(corners, key=get_angle)
    
    # Rearrange to get top-left, top-right, bottom-right, bottom-left
    # Find the top-left corner (minimum sum of x and y)
    min_sum_idx = np.argmin([x + y for x, y in sorted_corners])
    
    # Rotate the list so that the top-left corner is first
    sorted_corners = sorted_corners[min_sum_idx:] + sorted_corners[:min_sum_idx]
    
    return sorted_corners

def extract_and_normalize_board(image, corners, output_size=(640, 640)):
    """
    Extract and normalize the chess board region.
    
    Args:
        image: Input image
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        output_size: Size of the output normalized board image
        
    Returns:
        normalized_board: Normalized chess board image
    """
    # Define the target corners (square)
    target_corners = np.array([
        [0, 0],  # Top-left
        [output_size[0], 0],  # Top-right
        [output_size[0], output_size[1]],  # Bottom-right
        [0, output_size[1]]  # Bottom-left
    ], dtype=np.float32)
    
    # Convert corners to numpy array
    source_corners = np.array(corners, dtype=np.float32)
    
    # Calculate perspective transform
    transform_matrix = cv2.getPerspectiveTransform(source_corners, target_corners)
    
    # Apply perspective transform
    normalized_board = cv2.warpPerspective(image, transform_matrix, output_size)
    
    return normalized_board

def detect_and_extract_board(model, image_path, output_size=(640, 640)):
    """
    Detect the chess board and extract a normalized view.
    
    Args:
        model: The segmentation model
        image_path: Path to the input image
        output_size: Size of the output normalized board image
        
    Returns:
        normalized_board: Normalized chess board image
        success: Whether the detection was successful
    """
    try:
        # Preprocess image
        preprocessed_image, preprocess_info = preprocess_image(image_path)
        
        # Normalize for model
        input_tensor = normalize_for_model(preprocessed_image)
        
        # Run inference
        with torch.no_grad():
            outputs = model(input_tensor)
        
        # Extract segmentation
        segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]
        
        # Find corners from segmentation
        keypoints = find_corners_from_segmentation(segmentation)
        
        if keypoints is None:
            print(f"No chess board detected in {image_path}")
            return None, False
        
        # Extract and normalize board
        normalized_board = extract_and_normalize_board(preprocessed_image, keypoints, output_size)
        
        return normalized_board, True
    
    except Exception as e:
        print(f"Error processing {image_path}: {e}")
        return None, False

def prepare_yolo_dataset(model, input_dir, output_dir, output_size=(640, 640), split_ratio=0.8):
    """
    Prepare a dataset for training YOLO.
    
    Args:
        model: The segmentation model
        input_dir: Directory containing input images
        output_dir: Directory to save the output dataset
        output_size: Size of the output normalized board images
        split_ratio: Train/val split ratio
    """
    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    
    train_dir = os.path.join(output_dir, 'train')
    val_dir = os.path.join(output_dir, 'val')
    
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    
    # Find all images in the input directory
    image_paths = []
    for root, _, files in os.walk(input_dir):
        for file in files:
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_paths.append(os.path.join(root, file))
    
    if not image_paths:
        print(f"No images found in {input_dir}")
        return
    
    print(f"Found {len(image_paths)} images")
    
    # Shuffle images
    np.random.shuffle(image_paths)
    
    # Split into train and val
    split_idx = int(len(image_paths) * split_ratio)
    train_paths = image_paths[:split_idx]
    val_paths = image_paths[split_idx:]
    
    print(f"Processing {len(train_paths)} training images")
    
    # Process training images
    for i, image_path in enumerate(tqdm(train_paths)):
        normalized_board, success = detect_and_extract_board(model, image_path, output_size)
        
        if success:
            # Save normalized board
            output_path = os.path.join(train_dir, f"board_{i:06d}.jpg")
            cv2.imwrite(output_path, cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR))
    
    print(f"Processing {len(val_paths)} validation images")
    
    # Process validation images
    for i, image_path in enumerate(tqdm(val_paths)):
        normalized_board, success = detect_and_extract_board(model, image_path, output_size)
        
        if success:
            # Save normalized board
            output_path = os.path.join(val_dir, f"board_{i:06d}.jpg")
            cv2.imwrite(output_path, cv2.cvtColor(normalized_board, cv2.COLOR_RGB2BGR))
    
    # Create data.yaml file
    data_yaml = os.path.join(output_dir, 'data.yaml')
    with open(data_yaml, 'w') as f:
        f.write(f"train: {os.path.abspath(train_dir)}\n")
        f.write(f"val: {os.path.abspath(val_dir)}\n")
        f.write("nc: 12\n")
        f.write("names: ['white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king', 'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king']\n")
    
    print(f"Dataset prepared at {output_dir}")
    print(f"Next steps:")
    print(f"1. Annotate the images using a tool like labelImg or CVAT")
    print(f"2. Train YOLO using the data.yaml file")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Prepare YOLO dataset for chess piece detection')
    parser.add_argument('--model_path', type=str, default='chess_board_detection/models/segmentation_only/tiny_20250519_091307/best_model_dice.pth',
                        help='Path to segmentation model')
    parser.add_argument('--input_dir', type=str, required=True,
                        help='Directory containing input images')
    parser.add_argument('--output_dir', type=str, default='chess_board_detection/piece_detection/dataset',
                        help='Directory to save the output dataset')
    parser.add_argument('--output_size', type=int, default=640,
                        help='Size of the output normalized board images')
    parser.add_argument('--split_ratio', type=float, default=0.8,
                        help='Train/val split ratio')
    args = parser.parse_args()
    
    # Load model
    print(f"Loading model from {args.model_path}")
    model = load_model(args.model_path)
    
    # Prepare dataset
    prepare_yolo_dataset(
        model,
        args.input_dir,
        args.output_dir,
        output_size=(args.output_size, args.output_size),
        split_ratio=args.split_ratio
    )

if __name__ == "__main__":
    main()
