"""
Simple U-Net training script with verbose output.
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chess_board_detection.models.chessboard_segmentation_unet import get_model
from chess_board_detection.dataset.segmentation_dataset import create_dataloaders

class DiceLoss(nn.Module):
    """Dice loss for segmentation."""

    def __init__(self, smooth=1e-6):
        super(DiceLoss, self).__init__()
        self.smooth = smooth

    def forward(self, predictions, targets):
        predictions = torch.sigmoid(predictions)

        # Flatten
        predictions = predictions.view(-1)
        targets = targets.view(-1)

        intersection = (predictions * targets).sum()
        dice = (2. * intersection + self.smooth) / (predictions.sum() + targets.sum() + self.smooth)

        return 1 - dice

def calculate_iou(predictions, targets, threshold=0.5):
    """Calculate IoU metric."""
    # Convert to float first to avoid casting issues
    predictions = (torch.sigmoid(predictions) > threshold).float()
    targets = (targets > threshold).float()

    intersection = (predictions * targets).sum()
    union = predictions.sum() + targets.sum() - intersection

    if union == 0:
        return 1.0 if intersection == 0 else 0.0

    return (intersection / union).item()

def train_simple():
    """Simple training function."""
    print("Starting simple U-Net training...")

    # Setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")

    if torch.cuda.is_available():
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")

    # Data paths
    IMAGE_DIR = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real"
    ANNOTATION_FILE = r"C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json"

    print("Creating dataloaders...")
    try:
        train_loader, val_loader = create_dataloaders(
            IMAGE_DIR, ANNOTATION_FILE,
            batch_size=4,  # Smaller batch size for 6GB GPU
            image_size=(256, 256),
            train_split=0.8,
            num_workers=0  # No multiprocessing to avoid issues
        )
        print(f"Train batches: {len(train_loader)}, Val batches: {len(val_loader)}")
    except Exception as e:
        print(f"Error creating dataloaders: {e}")
        return

    # Create model
    print("Creating model...")
    try:
        model = get_model(model_type="standard", n_channels=3, n_classes=1)
        model = model.to(device)

        total_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        print(f"Model parameters: {total_params:,}")
    except Exception as e:
        print(f"Error creating model: {e}")
        return

    # Loss and optimizer
    print("Setting up training...")
    criterion = nn.BCEWithLogitsLoss()
    dice_loss = DiceLoss()
    optimizer = optim.Adam(model.parameters(), lr=1e-4)

    # Training loop
    epochs = 20
    print(f"Starting training for {epochs} epochs...")

    for epoch in range(epochs):
        print(f"\nEpoch {epoch+1}/{epochs}")

        # Training
        model.train()
        train_loss = 0
        train_iou = 0
        num_train_batches = 0

        print("Training...")
        for batch_idx, (images, masks) in enumerate(train_loader):
            try:
                images = images.to(device)
                masks = masks.to(device).unsqueeze(1)  # Add channel dimension for masks

                optimizer.zero_grad()

                outputs = model(images)
                bce_loss = criterion(outputs, masks)
                dice_loss_val = dice_loss(outputs, masks)
                loss = bce_loss + dice_loss_val

                loss.backward()
                optimizer.step()

                # Calculate metrics
                with torch.no_grad():
                    iou = calculate_iou(outputs, masks)

                train_loss += loss.item()
                train_iou += iou
                num_train_batches += 1

                print(f"  Batch {batch_idx+1}/{len(train_loader)}: Loss={loss.item():.4f}, IoU={iou:.4f}")

            except Exception as e:
                print(f"Error in training batch {batch_idx}: {e}")
                continue

        # Validation
        model.eval()
        val_loss = 0
        val_iou = 0
        num_val_batches = 0

        print("Validating...")
        with torch.no_grad():
            for batch_idx, (images, masks) in enumerate(val_loader):
                try:
                    images = images.to(device)
                    masks = masks.to(device).unsqueeze(1)  # Add channel dimension for masks

                    outputs = model(images)
                    bce_loss = criterion(outputs, masks)
                    dice_loss_val = dice_loss(outputs, masks)
                    loss = bce_loss + dice_loss_val

                    iou = calculate_iou(outputs, masks)

                    val_loss += loss.item()
                    val_iou += iou
                    num_val_batches += 1

                    print(f"  Val Batch {batch_idx+1}/{len(val_loader)}: Loss={loss.item():.4f}, IoU={iou:.4f}")

                except Exception as e:
                    print(f"Error in validation batch {batch_idx}: {e}")
                    continue

        # Print epoch results
        if num_train_batches > 0 and num_val_batches > 0:
            avg_train_loss = train_loss / num_train_batches
            avg_train_iou = train_iou / num_train_batches
            avg_val_loss = val_loss / num_val_batches
            avg_val_iou = val_iou / num_val_batches

            print(f"Epoch {epoch+1} Results:")
            print(f"  Train - Loss: {avg_train_loss:.4f}, IoU: {avg_train_iou:.4f}")
            print(f"  Val   - Loss: {avg_val_loss:.4f}, IoU: {avg_val_iou:.4f}")

        # Save checkpoint every 5 epochs
        if (epoch + 1) % 5 == 0:
            checkpoint_path = f"chess_board_detection/unet_checkpoint_epoch_{epoch+1}.pth"
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': avg_train_loss if num_train_batches > 0 else 0,
                'val_loss': avg_val_loss if num_val_batches > 0 else 0,
            }, checkpoint_path)
            print(f"Checkpoint saved: {checkpoint_path}")

    # Save final model
    final_model_path = "chess_board_detection/unet_final_model.pth"
    torch.save(model.state_dict(), final_model_path)
    print(f"Final model saved: {final_model_path}")

    print("Training completed!")
    return model

if __name__ == "__main__":
    try:
        model = train_simple()
    except Exception as e:
        print(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()
