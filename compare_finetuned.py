import os
import cv2
import numpy as np
import argparse

def create_comparison(original_dir, finetuned_dir, output_dir):
    """Create comparison images between original and fine-tuned detections"""
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all detection images in the original directory
    original_files = [f for f in os.listdir(original_dir) if f.startswith("detection_") and f.endswith((".jpg", ".jpeg", ".png"))]
    
    for file in original_files:
        # Load the original and fine-tuned detection images
        original_path = os.path.join(original_dir, file)
        finetuned_path = os.path.join(finetuned_dir, file)
        
        if not os.path.exists(finetuned_path):
            print(f"Warning: Fine-tuned detection image {finetuned_path} not found, skipping")
            continue
        
        original_img = cv2.imread(original_path)
        finetuned_img = cv2.imread(finetuned_path)
        
        if original_img is None or finetuned_img is None:
            print(f"Warning: Could not read images for {file}, skipping")
            continue
        
        # Create a comparison image
        h, w = original_img.shape[:2]
        comparison = np.zeros((h, w * 2 + 20, 3), dtype=np.uint8)
        
        # Add the original image on the left
        comparison[:, :w] = original_img
        
        # Add the fine-tuned image on the right
        comparison[:, w + 20:] = finetuned_img
        
        # Add labels
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(comparison, "Original", (10, 30), font, 1, (255, 255, 255), 2)
        cv2.putText(comparison, "Fine-tuned", (w + 30, 30), font, 1, (255, 255, 255), 2)
        
        # Add a vertical separator
        cv2.line(comparison, (w + 10, 0), (w + 10, h), (255, 255, 255), 2)
        
        # Save the comparison image
        output_path = os.path.join(output_dir, f"comparison_{file.replace('detection_', '')}")
        cv2.imwrite(output_path, comparison)
        
        print(f"Saved comparison to {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Compare original and fine-tuned detections")
    parser.add_argument("--original", default="improved_detections", help="Directory with original detection images")
    parser.add_argument("--finetuned", default="finetuned_detections", help="Directory with fine-tuned detection images")
    parser.add_argument("--output", default="comparison_results", help="Directory to save comparison images")
    
    args = parser.parse_args()
    
    create_comparison(args.original, args.finetuned, args.output)

if __name__ == "__main__":
    main()
