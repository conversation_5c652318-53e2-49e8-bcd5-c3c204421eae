"""
<PERSON><PERSON><PERSON> to test the enhanced chess board detection model with v3 configuration.
This script will load the model from the v3 folder and run inference on a test image.
"""

import os
import argparse
import subprocess
import sys
import glob

from config import MODELS_DIR, DATA_DIR, DEVICE


def find_test_image(data_dir):
    """
    Find a test image in the data directory.
    
    Args:
        data_dir (str): Data directory.
        
    Returns:
        str: Path to a test image.
    """
    # Try to find a test image in the real data directory
    test_dir = os.path.join(data_dir, 'real')
    if os.path.exists(test_dir):
        for ext in ['*.jpg', '*.jpeg', '*.png']:
            images = glob.glob(os.path.join(test_dir, ext))
            if images:
                return images[0]
    
    # If no image found in real data, try synthetic data
    test_dir = os.path.join(data_dir, 'synthetic')
    if os.path.exists(test_dir):
        for ext in ['*.jpg', '*.jpeg', '*.png']:
            images = glob.glob(os.path.join(test_dir, ext))
            if images:
                return images[0]
    
    return None


def main():
    """
    Main function to test the enhanced model with v3 configuration.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Test enhanced chess board detection model with v3 configuration')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR, help='Data directory')
    parser.add_argument('--model_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Model directory')
    parser.add_argument('--image_path', type=str, default=None, help='Path to test image')
    parser.add_argument('--threshold', type=float, default=0.05, help='Confidence threshold for corner detection')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    args = parser.parse_args()
    
    # Set model path
    model_path = os.path.join(args.model_dir, 'checkpoints', 'v3', 'best_model.pth')
    
    # Check if model exists
    if not os.path.exists(model_path):
        print(f"Model not found at {model_path}")
        print("Please train the model first using train_v3.py")
        return
    
    # Set output directory
    output_dir = os.path.join(args.model_dir, 'visualizations', 'v3', 'test_results')
    os.makedirs(output_dir, exist_ok=True)
    
    # Find a test image if not provided
    if args.image_path is None:
        args.image_path = find_test_image(args.data_dir)
        if args.image_path is None:
            print("No test image found. Please provide a test image.")
            return
    
    # Print configuration
    print("=== Testing Enhanced Model with v3 Configuration ===")
    print(f"Model path: {model_path}")
    print(f"Test image: {args.image_path}")
    print(f"Output directory: {output_dir}")
    print(f"Threshold: {args.threshold}")
    print(f"Using CPU: {args.cpu}")
    
    # Build the command to run the inference script
    test_cmd = [
        sys.executable, 'inference_enhanced.py',
        '--image_path', args.image_path,
        '--model_path', model_path,
        '--output_dir', output_dir,
        '--threshold', str(args.threshold)
    ]
    
    if args.cpu:
        test_cmd.append('--cpu')
    
    # Run the inference script
    print(f"Running command: {' '.join(test_cmd)}")
    subprocess.run(test_cmd)
    
    print("Testing completed!")
    print(f"Results saved to {output_dir}")


if __name__ == "__main__":
    main()
