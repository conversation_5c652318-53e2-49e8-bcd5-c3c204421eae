@echo off
echo Training YOLO model on targeted augmented dataset...
echo Using configuration from the first successful v11n model
echo.
echo This script will:
echo 1. Train the model for 100 epochs with patience=100
echo 2. Use the exact parameters from the first successful v11n model
echo 3. Apply class weighting to focus on problematic pieces
echo 4. Generate confusion matrix and visualizations for problematic pieces
echo 5. Display per-class metrics to evaluate improvement
echo.

set /p find_lr="Would you like to run the learning rate finder first? (y/n): "

if /i "%find_lr%"=="y" (
    echo Running with learning rate finder...
    python chess_board_detection/piece_detection/train_targeted_model.py ^
    --model "yolo11n.pt" ^
    --dataset "chess_board_detection/piece_detection/targeted_dataset/dataset.yaml" ^
    --epochs 100 ^
    --patience 100 ^
    --output_dir "chess_board_detection/piece_detection/models/targeted_yolo" ^
    --find_lr
) else (
    echo Using default learning rate from successful model...
    python chess_board_detection/piece_detection/train_targeted_model.py ^
    --model "yolo11n.pt" ^
    --dataset "chess_board_detection/piece_detection/targeted_dataset/dataset.yaml" ^
    --epochs 100 ^
    --patience 100 ^
    --output_dir "chess_board_detection/piece_detection/models/targeted_yolo"
)

echo.
echo Training complete! Check the results to see if problematic pieces have improved.
pause
