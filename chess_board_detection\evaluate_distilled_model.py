"""
Evaluate the distilled segmentation model on real-world images.
Compare its performance with the original model.
"""

import os
import sys
import argparse
import time
import torch
import numpy as np
import cv2
from PIL import Image
import matplotlib.pyplot as plt
from torchvision import transforms
from scipy import ndimage
from skimage.measure import regionprops

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models
from chess_board_detection.models.enhanced_unet_v5_2 import EnhancedChessBoardUNetV5_2
from chess_board_detection.models.segmentation_only_model import SegmentationOnlyModel, TinySegmentationModel

def load_original_model(model_path):
    """Load the original model."""
    model = EnhancedChessBoardUNetV5_2(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def load_distilled_model(model_path, model_type='tiny'):
    """Load the distilled model."""
    if model_type == 'tiny':
        model = TinySegmentationModel(n_channels=3)
    else:
        model = SegmentationOnlyModel(n_channels=3)
    
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input while preserving orientation.
    No flipping or rotation is applied.
    """
    # Load image in original orientation
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Convert BGR to RGB (OpenCV loads as BGR)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    original_image = image.copy()

    # Get original dimensions
    original_height, original_width = image.shape[:2]

    # Calculate aspect ratio
    aspect = original_width / original_height

    # Determine new dimensions while preserving aspect ratio
    if aspect > 1:  # Wider than tall
        new_width = min(original_width, 1024)  # Cap width
        new_height = int(new_width / aspect)
    else:  # Taller than wide
        new_height = min(original_height, 1024)  # Cap height
        new_width = int(new_height * aspect)

    # Resize image while preserving aspect ratio
    image_resized = cv2.resize(image, (new_width, new_height), interpolation=cv2.INTER_AREA)

    # Create a center crop that maintains aspect ratio but fits within target_size
    crop_size = min(new_width, new_height)
    start_x = (new_width - crop_size) // 2
    start_y = (new_height - crop_size) // 2
    image_cropped = image_resized[start_y:start_y+crop_size, start_x:start_x+crop_size]

    # Final resize to target size
    image_final = cv2.resize(image_cropped, target_size, interpolation=cv2.INTER_AREA)

    # Store preprocessing info for coordinate mapping
    preprocess_info = {
        'original_image': original_image,
        'original_size': (original_width, original_height),
        'resized_size': (new_width, new_height),
        'crop_start': (start_x, start_y),
        'crop_size': crop_size,
        'target_size': target_size
    }

    return image_final, preprocess_info

def enhance_image(image):
    """Apply basic image enhancement."""
    # Convert to float32 for processing
    image_float = image.astype(np.float32) / 255.0
    
    # Apply contrast stretching
    p2, p98 = np.percentile(image_float, (2, 98))
    enhanced = np.clip((image_float - p2) / (p98 - p2), 0, 1)
    
    # Convert back to uint8
    enhanced = (enhanced * 255).astype(np.uint8)
    
    return enhanced

def normalize_for_model(image):
    """Normalize image for model input."""
    # Convert to tensor
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)
    
    return input_tensor

def find_corners_from_segmentation(segmentation, threshold=0.5):
    """
    Find corners of the chess board using the segmentation mask.
    
    Args:
        segmentation: Segmentation mask (H, W)
        threshold: Threshold for binary mask
        
    Returns:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    """
    # Create binary mask
    binary_mask = (segmentation > threshold).astype(np.uint8)
    
    # Apply morphological operations to clean up the mask
    kernel = np.ones((5, 5), np.uint8)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_CLOSE, kernel)
    binary_mask = cv2.morphologyEx(binary_mask, cv2.MORPH_OPEN, kernel)
    
    # Find contours
    contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if not contours:
        return None
    
    # Find the largest contour (the chess board)
    largest_contour = max(contours, key=cv2.contourArea)
    
    # Approximate the contour to get a polygon
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx_polygon = cv2.approxPolyDP(largest_contour, epsilon, True)
    
    # If we don't get exactly 4 corners, try to find the best 4 corners
    if len(approx_polygon) != 4:
        # If we have more than 4 corners, find the 4 corners that form the largest quadrilateral
        if len(approx_polygon) > 4:
            # Convert to a more convenient format
            points = [point[0] for point in approx_polygon]
            
            # Find the convex hull
            hull = cv2.convexHull(np.array(points))
            
            # Approximate the hull to get 4 corners
            epsilon = 0.02 * cv2.arcLength(hull, True)
            approx_polygon = cv2.approxPolyDP(hull, epsilon, True)
            
            # If we still don't have 4 corners, use the minimum area rectangle
            if len(approx_polygon) != 4:
                rect = cv2.minAreaRect(hull)
                box = cv2.boxPoints(rect)
                approx_polygon = np.int0(box)
        else:
            # If we have fewer than 4 corners, use the minimum area rectangle
            rect = cv2.minAreaRect(largest_contour)
            box = cv2.boxPoints(rect)
            approx_polygon = np.int0(box)
    
    # Extract corners
    corners = [(point[0][0], point[0][1]) for point in approx_polygon]
    
    # Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left
    corners = sort_corners(corners)
    
    return corners

def sort_corners(corners):
    """
    Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left.
    
    Args:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
        
    Returns:
        sorted_corners: Sorted list of corner coordinates
    """
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)
    
    # Sort corners based on their angle from the center
    def get_angle(point):
        return np.arctan2(point[1] - center_y, point[0] - center_x)
    
    # Sort corners by angle
    sorted_corners = sorted(corners, key=get_angle)
    
    # Rearrange to get top-left, top-right, bottom-right, bottom-left
    # This assumes that the first corner after sorting is the top-left
    # We need to rotate the list to get the desired order
    # Find the top-left corner (minimum sum of x and y)
    min_sum_idx = np.argmin([x + y for x, y in sorted_corners])
    
    # Rotate the list so that the top-left corner is first
    sorted_corners = sorted_corners[min_sum_idx:] + sorted_corners[:min_sum_idx]
    
    return sorted_corners

def map_to_original_coordinates(keypoints, preprocess_info):
    """
    Map keypoints from model input space (256x256) back to original image coordinates.
    Accounts for all preprocessing steps: resize, crop, and final resize.
    """
    mapped_keypoints = []
    
    target_w, target_h = preprocess_info['target_size']
    crop_size = preprocess_info['crop_size']
    crop_start_x, crop_start_y = preprocess_info['crop_start']
    original_w, original_h = preprocess_info['original_size']
    resized_w, resized_h = preprocess_info['resized_size']
    
    for kp in keypoints:
        if kp is None:
            mapped_keypoints.append(None)
            continue
            
        x, y = kp
        
        # Map from target size to crop size
        x_crop = x * crop_size / target_w
        y_crop = y * crop_size / target_h
        
        # Map from crop to resized image
        x_resized = x_crop + crop_start_x
        y_resized = y_crop + crop_start_y
        
        # Map from resized to original image
        x_original = x_resized * original_w / resized_w
        y_original = y_resized * original_h / resized_h
        
        mapped_keypoints.append((int(x_original), int(y_original)))
    
    return mapped_keypoints

def run_inference(model, image_path, model_name, is_distilled=False):
    """
    Run inference on an image using the given model.
    
    Args:
        model: The model to use for inference
        image_path: Path to the input image
        model_name: Name of the model (for display)
        is_distilled: Whether the model is a distilled model
        
    Returns:
        Dictionary containing inference results
    """
    # Preprocess image
    start_time = time.time()
    preprocessed_image, preprocess_info = preprocess_image(image_path)
    enhanced_image = enhance_image(preprocessed_image)
    
    # Normalize for model
    input_tensor = normalize_for_model(enhanced_image)
    
    # Run inference
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]
    
    # Find corners from segmentation
    keypoints = find_corners_from_segmentation(segmentation)
    
    if keypoints is None:
        print(f"No chess board region detected in the image for {model_name}")
        return None
    
    # Map keypoints back to original image
    original_keypoints = map_to_original_coordinates(keypoints, preprocess_info)
    
    # Calculate inference time
    inference_time = time.time() - start_time
    
    return {
        'model_name': model_name,
        'preprocessed_image': enhanced_image,
        'segmentation': segmentation,
        'keypoints': keypoints,
        'original_keypoints': original_keypoints,
        'original_image': preprocess_info['original_image'],
        'inference_time': inference_time,
        'is_distilled': is_distilled
    }

def visualize_results(results, output_path):
    """
    Visualize the results of inference.
    
    Args:
        results: Dictionary containing inference results
        output_path: Path to save the visualization
    """
    # Create figure with 2x2 subplots
    fig, axs = plt.subplots(2, 2, figsize=(12, 10))
    
    # Plot original image
    axs[0, 0].imshow(results['preprocessed_image'])
    axs[0, 0].set_title('Preprocessed Image')
    axs[0, 0].axis('off')
    
    # Plot segmentation mask
    axs[0, 1].imshow(results['segmentation'], cmap='viridis')
    axs[0, 1].set_title('Segmentation Mask')
    axs[0, 1].axis('off')
    
    # Plot binary mask
    binary_mask = (results['segmentation'] > 0.5).astype(np.uint8)
    axs[1, 0].imshow(binary_mask, cmap='gray')
    axs[1, 0].set_title('Binary Mask')
    axs[1, 0].axis('off')
    
    # Plot keypoints
    axs[1, 1].imshow(results['preprocessed_image'])
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['r', 'g', 'b', 'y']
    
    # Draw lines connecting the corners
    if len(results['keypoints']) == 4:
        for i in range(4):
            j = (i + 1) % 4
            axs[1, 1].plot([results['keypoints'][i][0], results['keypoints'][j][0]], 
                          [results['keypoints'][i][1], results['keypoints'][j][1]], 
                          c='cyan', linewidth=2)
    
    for i, (kp, name, color) in enumerate(zip(results['keypoints'], corner_names, colors)):
        if kp is not None:
            x, y = kp
            axs[1, 1].scatter(x, y, c=color, s=50)
            axs[1, 1].text(x+5, y+5, name, color=color, fontsize=10)
    
    # Add inference time to title
    model_type = "Distilled" if results['is_distilled'] else "Original"
    axs[1, 1].set_title(f'Detected Corners ({model_type}, {results["inference_time"]:.3f}s)')
    axs[1, 1].axis('off')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

def visualize_original_with_keypoints(results, output_path):
    """
    Visualize the original image with detected keypoints.
    
    Args:
        results: Dictionary containing inference results
        output_path: Path to save the visualization
    """
    plt.figure(figsize=(10, 8))
    plt.imshow(results['original_image'])
    
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['r', 'g', 'b', 'y']
    
    # Draw lines connecting the corners
    if len(results['original_keypoints']) == 4:
        for i in range(4):
            j = (i + 1) % 4
            plt.plot([results['original_keypoints'][i][0], results['original_keypoints'][j][0]], 
                    [results['original_keypoints'][i][1], results['original_keypoints'][j][1]], 
                    c='cyan', linewidth=2)
    
    for i, (kp, name, color) in enumerate(zip(results['original_keypoints'], corner_names, colors)):
        if kp is not None:
            x, y = kp
            plt.scatter(x, y, c=color, s=100, marker='o')
            plt.text(x+15, y+15, name, color=color, fontsize=12, weight='bold')
    
    # Add inference time to title
    model_type = "Distilled" if results['is_distilled'] else "Original"
    plt.title(f'{model_type} Model: {results["model_name"]} (Inference: {results["inference_time"]:.3f}s)', fontsize=16)
    plt.axis('off')
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close()

def compare_models(original_results, distilled_results, output_path):
    """
    Create a side-by-side comparison of original and distilled model results.
    
    Args:
        original_results: Dictionary containing original model inference results
        distilled_results: Dictionary containing distilled model inference results
        output_path: Path to save the comparison visualization
    """
    # Create figure with 2x2 subplots
    fig, axs = plt.subplots(2, 2, figsize=(15, 12))
    
    # Plot original model segmentation
    axs[0, 0].imshow(original_results['segmentation'], cmap='viridis')
    axs[0, 0].set_title(f'Original Model Segmentation\nInference: {original_results["inference_time"]:.3f}s')
    axs[0, 0].axis('off')
    
    # Plot distilled model segmentation
    axs[0, 1].imshow(distilled_results['segmentation'], cmap='viridis')
    axs[0, 1].set_title(f'Distilled Model Segmentation\nInference: {distilled_results["inference_time"]:.3f}s')
    axs[0, 1].axis('off')
    
    # Plot original model corners
    axs[1, 0].imshow(original_results['preprocessed_image'])
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    colors = ['r', 'g', 'b', 'y']
    
    # Draw lines connecting the corners
    if len(original_results['keypoints']) == 4:
        for i in range(4):
            j = (i + 1) % 4
            axs[1, 0].plot([original_results['keypoints'][i][0], original_results['keypoints'][j][0]], 
                          [original_results['keypoints'][i][1], original_results['keypoints'][j][1]], 
                          c='cyan', linewidth=2)
    
    for i, (kp, name, color) in enumerate(zip(original_results['keypoints'], corner_names, colors)):
        if kp is not None:
            x, y = kp
            axs[1, 0].scatter(x, y, c=color, s=50)
            axs[1, 0].text(x+5, y+5, name, color=color, fontsize=10)
    
    axs[1, 0].set_title('Original Model Corners')
    axs[1, 0].axis('off')
    
    # Plot distilled model corners
    axs[1, 1].imshow(distilled_results['preprocessed_image'])
    
    # Draw lines connecting the corners
    if len(distilled_results['keypoints']) == 4:
        for i in range(4):
            j = (i + 1) % 4
            axs[1, 1].plot([distilled_results['keypoints'][i][0], distilled_results['keypoints'][j][0]], 
                          [distilled_results['keypoints'][i][1], distilled_results['keypoints'][j][1]], 
                          c='cyan', linewidth=2)
    
    for i, (kp, name, color) in enumerate(zip(distilled_results['keypoints'], corner_names, colors)):
        if kp is not None:
            x, y = kp
            axs[1, 1].scatter(x, y, c=color, s=50)
            axs[1, 1].text(x+5, y+5, name, color=color, fontsize=10)
    
    axs[1, 1].set_title('Distilled Model Corners')
    axs[1, 1].axis('off')
    
    # Add speedup information
    speedup = original_results['inference_time'] / distilled_results['inference_time']
    plt.figtext(0.5, 0.01, f'Speedup: {speedup:.2f}x', ha='center', fontsize=14, bbox=dict(facecolor='yellow', alpha=0.5))
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Evaluate distilled segmentation model')
    parser.add_argument('--original_model', type=str, default='chess_board_detection/models/improved_corner_detection/checkpoints/v5.2(3rd attempt)/best_model_loss_phase2.pth',
                        help='Path to original model')
    parser.add_argument('--distilled_model', type=str, required=True,
                        help='Path to distilled model')
    parser.add_argument('--model_type', type=str, default='tiny', choices=['small', 'tiny'],
                        help='Type of distilled model')
    parser.add_argument('--image_path', type=str, default="C:\\Users\\<USER>\\OneDrive\\Desktop\\24.jpg",
                        help='Path to input image')
    parser.add_argument('--output_dir', type=str, default="C:\\Users\\<USER>\\OneDrive\\Desktop\\a1 v1\\chess_board_detection\\outputs\\distilled_evaluation",
                        help='Path to output directory')
    args = parser.parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load models
    print(f"Loading original model from {args.original_model}")
    original_model = load_original_model(args.original_model)
    
    print(f"Loading distilled model from {args.distilled_model}")
    distilled_model = load_distilled_model(args.distilled_model, args.model_type)
    
    # Print model sizes
    original_params = sum(p.numel() for p in original_model.parameters() if p.requires_grad)
    distilled_params = sum(p.numel() for p in distilled_model.parameters() if p.requires_grad)
    print(f"Original model parameters: {original_params:,}")
    print(f"Distilled model parameters: {distilled_params:,}")
    print(f"Size reduction: {original_params / distilled_params:.2f}x")
    
    # Run inference with original model
    print(f"Running inference with original model on {args.image_path}")
    original_results = run_inference(original_model, args.image_path, "Original", is_distilled=False)
    
    if original_results is None:
        print("Original model failed to detect chess board")
        return
    
    # Run inference with distilled model
    print(f"Running inference with distilled model on {args.image_path}")
    distilled_results = run_inference(distilled_model, args.image_path, "Distilled", is_distilled=True)
    
    if distilled_results is None:
        print("Distilled model failed to detect chess board")
        return
    
    # Create visualizations
    original_output_path = os.path.join(args.output_dir, "original_results.png")
    visualize_results(original_results, original_output_path)
    
    distilled_output_path = os.path.join(args.output_dir, "distilled_results.png")
    visualize_results(distilled_results, distilled_output_path)
    
    original_keypoints_path = os.path.join(args.output_dir, "original_keypoints.png")
    visualize_original_with_keypoints(original_results, original_keypoints_path)
    
    distilled_keypoints_path = os.path.join(args.output_dir, "distilled_keypoints.png")
    visualize_original_with_keypoints(distilled_results, distilled_keypoints_path)
    
    comparison_path = os.path.join(args.output_dir, "model_comparison.png")
    compare_models(original_results, distilled_results, comparison_path)
    
    # Print inference times
    print(f"\nInference times:")
    print(f"Original model: {original_results['inference_time']:.3f}s")
    print(f"Distilled model: {distilled_results['inference_time']:.3f}s")
    print(f"Speedup: {original_results['inference_time'] / distilled_results['inference_time']:.2f}x")
    
    # Print corner coordinates
    print(f"\nOriginal model corner coordinates:")
    corner_names = ['Top-Left', 'Top-Right', 'Bottom-Right', 'Bottom-Left']
    for i, (name, kp) in enumerate(zip(corner_names, original_results['original_keypoints'])):
        if kp is not None:
            print(f"{name}: {kp}")
        else:
            print(f"{name}: Not detected")
    
    print(f"\nDistilled model corner coordinates:")
    for i, (name, kp) in enumerate(zip(corner_names, distilled_results['original_keypoints'])):
        if kp is not None:
            print(f"{name}: {kp}")
        else:
            print(f"{name}: Not detected")
    
    print(f"\nAll visualizations saved to {args.output_dir}")

if __name__ == "__main__":
    main()
