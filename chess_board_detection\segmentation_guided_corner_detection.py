"""
Segmentation-guided corner detection module for chess board detection.
This module integrates segmentation and corner detection to improve accuracy.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
from scipy import ndimage


def segmentation_guided_corner_detection(segmentation, heatmaps, threshold=0.3, boundary_width=5):
    """
    Use segmentation mask to guide corner detection.

    Args:
        segmentation: Segmentation mask (H, W)
        heatmaps: Corner heatmaps (4, H, W)
        threshold: Detection threshold
        boundary_width: Width of the boundary region to consider for corners

    Returns:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    """
    # Create a binary mask from segmentation
    binary_mask = (segmentation > 0.5).astype(np.float32)

    # Find contours in the binary mask
    contours = cv2.findContours(binary_mask.astype(np.uint8),
                               cv2.RETR_EXTERNAL,
                               cv2.CHAIN_APPROX_SIMPLE)[0]

    # If no contours found, fall back to regular corner detection
    if not contours:
        return extract_corners_from_heatmaps(heatmaps, threshold)

    # Find the largest contour (the chess board)
    largest_contour = max(contours, key=cv2.contourArea)

    # Approximate the contour to get a polygon
    epsilon = 0.02 * cv2.arcLength(largest_contour, True)
    approx_polygon = cv2.approxPolyDP(largest_contour, epsilon, True)

    # If we get a quadrilateral, use its corners
    if len(approx_polygon) == 4:
        corners = [(pt[0][0], pt[0][1]) for pt in approx_polygon]
        # Sort corners in clockwise order from top-left
        corners = sort_corners(corners)
        return corners

    # Otherwise, use the heatmaps but constrain to the segmentation boundary
    # Create a distance transform from the boundary
    dist_transform = cv2.distanceTransform(binary_mask.astype(np.uint8), cv2.DIST_L2, 3)
    boundary_mask = (dist_transform < boundary_width).astype(np.float32)  # Within boundary_width pixels of boundary

    # Process each corner heatmap
    corners = []
    for i in range(4):
        heatmap = heatmaps[i].copy()

        # Apply boundary mask to heatmap
        masked_heatmap = heatmap * boundary_mask

        # Find the maximum point
        max_val = np.max(masked_heatmap)
        if max_val >= threshold:
            max_idx = np.argmax(masked_heatmap)
            y, x = np.unravel_index(max_idx, masked_heatmap.shape)
            corners.append((x, y))
        else:
            # If no strong peak in boundary, try the original heatmap
            max_val = np.max(heatmap)
            if max_val >= threshold:
                max_idx = np.argmax(heatmap)
                y, x = np.unravel_index(max_idx, heatmap.shape)
                corners.append((x, y))
            else:
                corners.append(None)

    # If we have all corners, sort them
    if all(corner is not None for corner in corners):
        corners = sort_corners(corners)

    return corners


def extract_corners_from_heatmaps(heatmaps, threshold=0.3):
    """
    Extract corner coordinates from heatmaps without segmentation guidance.

    Args:
        heatmaps: Corner heatmaps (4, H, W)
        threshold: Detection threshold

    Returns:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]
    """
    corners = []
    for i in range(4):
        heatmap = heatmaps[i]
        max_val = np.max(heatmap)

        if max_val >= threshold:
            # Find the location of the maximum value
            max_idx = np.argmax(heatmap)
            y, x = np.unravel_index(max_idx, heatmap.shape)
            corners.append((x, y))
        else:
            corners.append(None)

    return corners


def sort_corners(corners):
    """
    Sort corners in clockwise order: top-left, top-right, bottom-right, bottom-left.

    Args:
        corners: List of corner coordinates [(x1, y1), (x2, y2), (x3, y3), (x4, y4)]

    Returns:
        sorted_corners: Sorted corner coordinates
    """
    # Calculate the center of the corners
    center_x = sum(x for x, y in corners) / len(corners)
    center_y = sum(y for x, y in corners) / len(corners)

    # Sort corners based on their position relative to the center
    def get_angle(point):
        return np.arctan2(point[1] - center_y, point[0] - center_x)

    # Sort corners by angle
    sorted_corners = sorted(corners, key=get_angle)

    # Rotate the list so that the top-left corner is first
    # Top-left corner has the smallest sum of x and y coordinates
    min_sum_idx = np.argmin([x + y for x, y in sorted_corners])
    sorted_corners = sorted_corners[min_sum_idx:] + sorted_corners[:min_sum_idx]

    return sorted_corners


class SegmentationGuidedCornerDetectionLoss(nn.Module):
    """
    Loss function that enforces consistency between segmentation and corner heatmaps.

    This loss ensures that corner heatmaps are activated near the boundaries of the
    segmentation mask, encouraging the model to detect corners at the edges of the
    segmented chess board.
    """
    def __init__(self, weight=1.0, boundary_width=5):
        super(SegmentationGuidedCornerDetectionLoss, self).__init__()
        self.weight = weight
        self.boundary_width = boundary_width

    def forward(self, segmentation, heatmaps):
        """
        Calculate the segmentation-guided corner detection loss.

        Args:
            segmentation: Segmentation mask (B, 1, H, W)
            heatmaps: Corner heatmaps (B, 4, H, W)

        Returns:
            loss: Consistency loss
        """
        batch_size = segmentation.size(0)
        loss = torch.tensor(0.0, device=segmentation.device)

        for i in range(batch_size):
            seg = segmentation[i, 0]  # (H, W)

            # Create a boundary mask from segmentation
            # Use gradient magnitude to find edges
            grad_x = torch.abs(seg[:, 1:] - seg[:, :-1])
            grad_x = F.pad(grad_x, (0, 1), mode='constant', value=0)
            grad_y = torch.abs(seg[1:, :] - seg[:-1, :])
            grad_y = F.pad(grad_y, (0, 0, 0, 1), mode='constant', value=0)
            boundary = torch.max(grad_x, grad_y)

            # Dilate the boundary to create a boundary region
            boundary = F.max_pool2d(boundary.unsqueeze(0).unsqueeze(0),
                                    kernel_size=self.boundary_width,
                                    stride=1,
                                    padding=self.boundary_width//2)[0, 0]

            # For each corner heatmap
            for c in range(4):
                hm = heatmaps[i, c]  # (H, W)

                # Penalize heatmap activations that are not on the boundary
                penalty = hm * (1.0 - boundary)
                loss += torch.sum(penalty) / (torch.sum(hm) + 1e-6)

                # Also penalize if no activation on boundary
                boundary_activation = torch.sum(hm * boundary)
                total_activation = torch.sum(hm)
                if total_activation > 0:
                    ratio = boundary_activation / total_activation
                    loss += torch.exp(-10 * ratio)  # Exponential penalty for low ratio

        return self.weight * loss / batch_size
