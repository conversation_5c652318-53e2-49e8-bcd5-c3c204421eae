"""
Bayesian hyperparameter optimization for chess board detection (v5.2).

This module implements:
1. Bayesian optimization for hyperparameter tuning
2. Meta-learning for hyperparameter adjustment strategies
3. Multi-objective optimization for balanced metrics
4. Adaptive search space refinement
"""

import os
import numpy as np
import torch
import json
import matplotlib.pyplot as plt
from scipy.stats import norm
from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import Matern
from sklearn.preprocessing import StandardScaler
import time
from collections import defaultdict


class BayesianHyperparameterOptimizer:
    """
    Bayesian hyperparameter optimizer with meta-learning capabilities.
    
    This optimizer:
    1. Uses Gaussian Process regression to model the hyperparameter-performance relationship
    2. Employs Expected Improvement acquisition function for efficient exploration
    3. Learns from previous optimization runs to improve search efficiency
    4. <PERSON>les multi-objective optimization for balanced metric improvement
    """
    def __init__(self, param_space, objective_metrics, history_path=None, 
                 exploration_weight=0.1, n_initial_points=5, meta_learning=True):
        """
        Initialize the optimizer.
        
        Args:
            param_space: Dictionary mapping parameter names to (min, max) ranges
            objective_metrics: List of metric names to optimize
            history_path: Path to save/load optimization history
            exploration_weight: Weight for exploration vs exploitation (0-1)
            n_initial_points: Number of initial random points to evaluate
            meta_learning: Whether to use meta-learning from previous runs
        """
        self.param_space = param_space
        self.param_names = list(param_space.keys())
        self.objective_metrics = objective_metrics
        self.history_path = history_path
        self.exploration_weight = exploration_weight
        self.n_initial_points = n_initial_points
        self.meta_learning = meta_learning
        
        # Initialize Gaussian Process models for each objective
        self.gp_models = {}
        for metric in objective_metrics:
            kernel = Matern(nu=2.5)
            self.gp_models[metric] = GaussianProcessRegressor(
                kernel=kernel,
                alpha=1e-6,
                normalize_y=True,
                n_restarts_optimizer=5,
                random_state=42
            )
        
        # Initialize history
        self.X_history = []  # Parameter configurations
        self.y_history = defaultdict(list)  # Results for each metric
        self.best_params = {}  # Best parameters for each metric
        self.best_values = {metric: -np.inf for metric in objective_metrics}  # Best values
        
        # For multi-objective optimization
        self.pareto_front = []  # Pareto-optimal configurations
        self.pareto_metrics = []  # Corresponding metric values
        
        # Meta-learning components
        self.meta_knowledge = {}  # Knowledge from previous runs
        self.param_sensitivity = {}  # Sensitivity of each parameter
        self.metric_correlations = np.eye(len(objective_metrics))  # Correlation between metrics
        
        # Scalers for normalizing parameters
        self.param_scaler = StandardScaler()
        self.metric_scalers = {metric: StandardScaler() for metric in objective_metrics}
        
        # Load history if available
        if history_path and os.path.exists(history_path):
            self.load_history()
    
    def suggest(self, n_suggestions=1):
        """
        Suggest the next set of parameters to evaluate.
        
        Args:
            n_suggestions: Number of suggestions to generate
            
        Returns:
            List of dictionaries with parameter suggestions
        """
        # If we don't have enough data, use random sampling
        if len(self.X_history) < self.n_initial_points:
            return self._random_suggestions(n_suggestions)
        
        # Update GP models with current data
        self._update_models()
        
        # Generate suggestions using acquisition function
        suggestions = []
        for _ in range(n_suggestions):
            if np.random.random() < self.exploration_weight:
                # Occasionally use pure exploration
                suggestion = self._random_suggestions(1)[0]
            else:
                # Use expected improvement
                suggestion = self._suggest_with_ei()
            
            suggestions.append(suggestion)
        
        return suggestions
    
    def _random_suggestions(self, n):
        """Generate random parameter suggestions"""
        suggestions = []
        for _ in range(n):
            params = {}
            for name, (min_val, max_val) in self.param_space.items():
                params[name] = np.random.uniform(min_val, max_val)
            suggestions.append(params)
        return suggestions
    
    def _suggest_with_ei(self):
        """Generate suggestion using Expected Improvement"""
        # For multi-objective, use scalarization
        weights = self._get_objective_weights()
        
        # Define acquisition function (Expected Improvement)
        def expected_improvement(x):
            x = x.reshape(1, -1)
            ei_values = []
            
            for i, metric in enumerate(self.objective_metrics):
                mu, sigma = self.gp_models[metric].predict(x, return_std=True)
                
                # If sigma is very small, we're certain about this point
                if sigma < 1e-6:
                    ei_values.append(0)
                    continue
                
                # Calculate improvement over best observed value
                improvement = mu - self.best_values[metric]
                
                # Expected improvement
                z = improvement / sigma
                ei = improvement * norm.cdf(z) + sigma * norm.pdf(z)
                
                # Apply weight for this objective
                ei_values.append(ei * weights[i])
            
            # Sum weighted EI values
            return -np.sum(ei_values)  # Negative because we're minimizing
        
        # Use random restarts to find global optimum of acquisition function
        best_x = None
        best_ei = np.inf
        
        # Try 10 random starting points
        for _ in range(10):
            x0 = np.array([np.random.uniform(self.param_space[p][0], self.param_space[p][1]) 
                          for p in self.param_names])
            
            # Bounds for optimization
            bounds = [(self.param_space[p][0], self.param_space[p][1]) for p in self.param_names]
            
            # Local optimization from this starting point
            from scipy.optimize import minimize
            result = minimize(expected_improvement, x0, bounds=bounds, method='L-BFGS-B')
            
            if result.fun < best_ei:
                best_ei = result.fun
                best_x = result.x
        
        # Convert back to dictionary
        suggestion = {name: value for name, value in zip(self.param_names, best_x)}
        
        return suggestion
    
    def _get_objective_weights(self):
        """Get weights for each objective based on current performance"""
        if not self.X_history:
            return [1.0 / len(self.objective_metrics)] * len(self.objective_metrics)
        
        # Calculate normalized distance from best possible value for each metric
        distances = []
        for metric in self.objective_metrics:
            if self.y_history[metric]:
                current_best = max(self.y_history[metric])
                # Estimate best possible value (optimistic)
                estimated_best = current_best * 1.2
                # Normalized distance
                distance = (estimated_best - current_best) / estimated_best
                distances.append(distance)
            else:
                distances.append(1.0)  # Maximum distance if no data
        
        # Convert distances to weights (larger distance = higher weight)
        total_distance = sum(distances)
        if total_distance > 0:
            weights = [d / total_distance for d in distances]
        else:
            weights = [1.0 / len(distances)] * len(distances)
        
        # Apply meta-knowledge to adjust weights
        if self.meta_learning and self.meta_knowledge:
            # If we know certain metrics are harder to improve, increase their weight
            for i, metric in enumerate(self.objective_metrics):
                if metric in self.meta_knowledge.get('difficult_metrics', []):
                    weights[i] *= 1.2
            
            # Renormalize
            total = sum(weights)
            weights = [w / total for w in weights]
        
        return weights
    
    def _update_models(self):
        """Update GP models with current data"""
        if not self.X_history:
            return
        
        # Convert parameter history to array
        X = np.array(self.X_history)
        
        # Normalize parameters
        X_scaled = self.param_scaler.fit_transform(X)
        
        # Update each GP model
        for metric in self.objective_metrics:
            if not self.y_history[metric]:
                continue
                
            y = np.array(self.y_history[metric])
            y_scaled = self.metric_scalers[metric].fit_transform(y.reshape(-1, 1)).ravel()
            
            # Fit GP model
            self.gp_models[metric].fit(X_scaled, y_scaled)
            
            # Update best value
            best_idx = np.argmax(y)
            if y[best_idx] > self.best_values[metric]:
                self.best_values[metric] = y[best_idx]
                self.best_params[metric] = {name: X[best_idx, i] for i, name in enumerate(self.param_names)}
        
        # Update parameter sensitivity
        self._update_parameter_sensitivity()
        
        # Update metric correlations
        self._update_metric_correlations()
        
        # Update Pareto front
        self._update_pareto_front()
    
    def _update_parameter_sensitivity(self):
        """Update sensitivity analysis for parameters"""
        if not self.X_history or len(self.X_history) < 5:
            return
            
        # For each parameter, calculate correlation with each metric
        for i, param_name in enumerate(self.param_names):
            param_values = np.array([x[i] for x in self.X_history])
            sensitivities = {}
            
            for metric in self.objective_metrics:
                if not self.y_history[metric]:
                    continue
                    
                metric_values = np.array(self.y_history[metric])
                
                # Calculate correlation
                correlation = np.corrcoef(param_values, metric_values)[0, 1]
                sensitivities[metric] = correlation
            
            self.param_sensitivity[param_name] = sensitivities
    
    def _update_metric_correlations(self):
        """Update correlations between metrics"""
        metrics_with_data = [m for m in self.objective_metrics if self.y_history[m]]
        if len(metrics_with_data) < 2:
            return
            
        # Create correlation matrix
        n_metrics = len(metrics_with_data)
        corr_matrix = np.eye(n_metrics)
        
        for i, metric1 in enumerate(metrics_with_data):
            for j in range(i+1, n_metrics):
                metric2 = metrics_with_data[j]
                
                values1 = np.array(self.y_history[metric1])
                values2 = np.array(self.y_history[metric2])
                
                # Ensure same length
                min_len = min(len(values1), len(values2))
                values1 = values1[:min_len]
                values2 = values2[:min_len]
                
                # Calculate correlation
                correlation = np.corrcoef(values1, values2)[0, 1]
                corr_matrix[i, j] = correlation
                corr_matrix[j, i] = correlation
        
        # Store correlation matrix
        self.metric_correlations = corr_matrix
    
    def _update_pareto_front(self):
        """Update Pareto front for multi-objective optimization"""
        if not all(self.y_history[m] for m in self.objective_metrics):
            return
            
        # Get all metric values
        metric_values = []
        for i in range(len(self.X_history)):
            point = [self.y_history[m][i] for m in self.objective_metrics]
            metric_values.append(point)
        
        metric_values = np.array(metric_values)
        
        # Find Pareto-optimal points
        is_pareto = np.ones(len(self.X_history), dtype=bool)
        for i in range(len(self.X_history)):
            for j in range(len(self.X_history)):
                if i != j:
                    # Check if j dominates i
                    if np.all(metric_values[j] >= metric_values[i]) and np.any(metric_values[j] > metric_values[i]):
                        is_pareto[i] = False
                        break
        
        # Update Pareto front
        self.pareto_front = [self.X_history[i] for i in range(len(self.X_history)) if is_pareto[i]]
        self.pareto_metrics = [metric_values[i] for i in range(len(metric_values)) if is_pareto[i]]
    
    def update(self, params, results):
        """
        Update optimizer with new evaluation results.
        
        Args:
            params: Dictionary of parameter values
            results: Dictionary mapping metric names to values
        """
        # Convert params to list in correct order
        param_list = [params[name] for name in self.param_names]
        self.X_history.append(param_list)
        
        # Record results for each metric
        for metric in self.objective_metrics:
            if metric in results:
                self.y_history[metric].append(results[metric])
                
                # Update best value
                if results[metric] > self.best_values[metric]:
                    self.best_values[metric] = results[metric]
                    self.best_params[metric] = params.copy()
        
        # Save history
        if self.history_path:
            self.save_history()
    
    def save_history(self):
        """Save optimization history to file"""
        history = {
            'param_space': self.param_space,
            'param_names': self.param_names,
            'objective_metrics': self.objective_metrics,
            'X_history': self.X_history,
            'y_history': dict(self.y_history),
            'best_params': self.best_params,
            'best_values': self.best_values,
            'meta_knowledge': self.meta_knowledge,
            'param_sensitivity': self.param_sensitivity
        }
        
        with open(self.history_path, 'w') as f:
            json.dump(history, f, indent=2)
    
    def load_history(self):
        """Load optimization history from file"""
        try:
            with open(self.history_path, 'r') as f:
                history = json.load(f)
            
            # Validate and load compatible history
            if set(history['param_names']) == set(self.param_names):
                self.X_history = history['X_history']
                self.y_history = defaultdict(list, history['y_history'])
                self.best_params = history['best_params']
                self.best_values = history['best_values']
                
                if 'meta_knowledge' in history:
                    self.meta_knowledge = history['meta_knowledge']
                
                if 'param_sensitivity' in history:
                    self.param_sensitivity = history['param_sensitivity']
                
                print(f"Loaded optimization history with {len(self.X_history)} evaluations")
            else:
                print("Parameter space in history file doesn't match current parameters")
        except Exception as e:
            print(f"Error loading history: {e}")
    
    def get_best_params(self, metric=None):
        """
        Get best parameters found so far.
        
        Args:
            metric: Specific metric to get best params for, or None for Pareto-optimal
            
        Returns:
            Dictionary of best parameters
        """
        if metric is not None and metric in self.best_params:
            return self.best_params[metric]
        
        # If no specific metric, return a balanced solution from Pareto front
        if self.pareto_front:
            # Find the most balanced solution on Pareto front
            best_idx = 0
            best_balance = -np.inf
            
            for i, metrics in enumerate(self.pareto_metrics):
                # Calculate balance as minimum normalized value across metrics
                normalized = [m / self.best_values[metric] if self.best_values[metric] > 0 else 0
                             for m, metric in zip(metrics, self.objective_metrics)]
                balance = min(normalized)
                
                if balance > best_balance:
                    best_balance = balance
                    best_idx = i
            
            # Convert to dictionary
            return {name: value for name, value in zip(self.param_names, self.pareto_front[best_idx])}
        
        # Fallback to random best metric
        for metric in self.objective_metrics:
            if metric in self.best_params:
                return self.best_params[metric]
        
        # If no data yet, return middle of parameter space
        return {name: (min_val + max_val) / 2 for name, (min_val, max_val) in self.param_space.items()}
