[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_bq.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\bq.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_br.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\br.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\xml_file_paths.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\xml\\file_paths.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_wq.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\wq.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_wp.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\wp.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_bk.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\bk.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_bp.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\bp.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_wk.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\wk.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_wb.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\wb.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_wr.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\wr.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_bn.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\bn.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_bb.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\bb.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-merged_res-53:\\drawable_wn.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.4\\com.chessvision.app-main-55:\\drawable\\wn.png"}, {"merged": "com.chessvision.app-merged_res-53:/drawable_chessboard_background.png.flat", "source": "com.chessvision.app-main-55:/drawable/chessboard_background.png"}]