"""
Fixed training script for chess board detection with proper loss handling.
"""

import os
import argparse
import json
import time
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from tqdm import tqdm
import matplotlib.pyplot as plt

from models.unet import ChessBoardUNet
from utils.real_dataset import get_data_loaders
from fixed_loss import DiceLoss, FixedHeatmapLoss
from config import (
    DATA_DIR, MODELS_DIR, DEVICE,
    LEARNING_RATE, NUM_EPOCHS, INPUT_SIZE
)


def train_model(model, dataloaders, criterion_seg, criterion_heatmap, optimizer, 
                output_dir, num_epochs=25, heatmap_weight=1.5, save_interval=10):
    """
    Train the model with organized output saving and proper loss handling.

    Args:
        model: PyTorch model
        dataloaders: Dictionary with 'train' and 'val' dataloaders
        criterion_seg: Loss function for segmentation
        criterion_heatmap: Loss function for heatmap regression
        optimizer: PyTorch optimizer
        output_dir: Directory to save outputs
        num_epochs: Number of epochs to train for
        heatmap_weight: Weight for heatmap loss in total loss
        save_interval: Interval for saving checkpoints

    Returns:
        model: Trained model
        history: Training history
    """
    since = time.time()

    # Create directories
    checkpoints_dir = os.path.join(output_dir, 'checkpoints')
    logs_dir = os.path.join(output_dir, 'logs')
    vis_dir = os.path.join(output_dir, 'visualizations')
    
    os.makedirs(checkpoints_dir, exist_ok=True)
    os.makedirs(logs_dir, exist_ok=True)
    os.makedirs(vis_dir, exist_ok=True)

    history = {
        'train_loss': [],
        'val_loss': [],
        'train_seg_loss': [],
        'val_seg_loss': [],
        'train_heatmap_loss': [],
        'val_heatmap_loss': []
    }

    best_model_wts = model.state_dict()
    best_loss = float('inf')

    for epoch in range(num_epochs):
        print(f'Epoch {epoch+1}/{num_epochs}')
        print('-' * 10)

        # Each epoch has a training and validation phase
        for phase in ['train', 'val']:
            if phase == 'train':
                model.train()  # Set model to training mode
            else:
                model.eval()   # Set model to evaluate mode

            running_loss = 0.0
            running_seg_loss = 0.0
            running_heatmap_loss = 0.0

            # Iterate over data
            for batch_idx, batch in enumerate(tqdm(dataloaders[phase], desc=phase)):
                # Print debug info for first batch
                if batch_idx == 0 and epoch == 0:
                    print(f"\nBatch shapes:")
                    print(f"Image: {batch['image'].shape}, device: {batch['image'].device}")
                    print(f"Mask: {batch['mask'].shape}, device: {batch['mask'].device}")
                    print(f"Heatmaps: {batch['corner_heatmaps'].shape}, device: {batch['corner_heatmaps'].device}\n")

                # Move data to device
                inputs = batch['image'].to(DEVICE)
                masks = batch['mask'].to(DEVICE)
                heatmaps = batch['corner_heatmaps'].to(DEVICE)

                # Zero the parameter gradients
                optimizer.zero_grad()

                # Forward
                with torch.set_grad_enabled(phase == 'train'):
                    outputs = model(inputs)
                    seg_outputs = outputs['segmentation']
                    heatmap_outputs = outputs['corner_heatmaps']

                    # Calculate losses
                    seg_loss = criterion_seg(seg_outputs, masks)
                    heatmap_loss = criterion_heatmap(heatmap_outputs, heatmaps)

                    # Ensure losses are positive
                    seg_loss = torch.abs(seg_loss)
                    heatmap_loss = torch.abs(heatmap_loss)

                    # Combined loss with custom weight for heatmap loss
                    loss = seg_loss + heatmap_weight * heatmap_loss

                    # Print loss values for debugging in first few batches
                    if batch_idx < 3 and epoch < 3:
                        print(f"Batch {batch_idx}, {phase} - Seg Loss: {seg_loss.item():.4f}, Heatmap Loss: {heatmap_loss.item():.4f}, Total: {loss.item():.4f}")

                    # Backward + optimize only if in training phase
                    if phase == 'train':
                        loss.backward()
                        # Clip gradients to prevent explosion
                        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
                        optimizer.step()

                # Statistics
                running_loss += loss.item() * inputs.size(0)
                running_seg_loss += seg_loss.item() * inputs.size(0)
                running_heatmap_loss += heatmap_loss.item() * inputs.size(0)

            epoch_loss = running_loss / len(dataloaders[phase].dataset)
            epoch_seg_loss = running_seg_loss / len(dataloaders[phase].dataset)
            epoch_heatmap_loss = running_heatmap_loss / len(dataloaders[phase].dataset)

            print(f'{phase} Loss: {epoch_loss:.4f}, Seg Loss: {epoch_seg_loss:.4f}, Heatmap Loss: {epoch_heatmap_loss:.4f}')

            # Record history
            history[f'{phase}_loss'].append(epoch_loss)
            history[f'{phase}_seg_loss'].append(epoch_seg_loss)
            history[f'{phase}_heatmap_loss'].append(epoch_heatmap_loss)

            # Deep copy the model if it's the best so far
            if phase == 'val' and epoch_loss < best_loss:
                best_loss = epoch_loss
                best_model_wts = model.state_dict().copy()

                # Save the best model
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': best_model_wts,
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': best_loss,
                }, os.path.join(checkpoints_dir, 'best_model.pth'))
                
                print(f"Saved best model with loss: {best_loss:.4f}")
            
            # Save checkpoint at regular intervals
            if (epoch + 1) % save_interval == 0 or epoch == num_epochs - 1:
                torch.save({
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'loss': epoch_loss,
                }, os.path.join(checkpoints_dir, f'checkpoint_epoch_{epoch+1}.pth'))
                
                # Save current history
                with open(os.path.join(logs_dir, 'training_history.json'), 'w') as f:
                    json.dump(history, f)
                
                # Plot current history
                plot_training_history(history, os.path.join(vis_dir, f'training_history_epoch_{epoch+1}.png'))

        print()

    time_elapsed = time.time() - since
    print(f'Training complete in {time_elapsed // 60:.0f}m {time_elapsed % 60:.0f}s')
    print(f'Best val loss: {best_loss:.4f}')

    # Load best model weights
    model.load_state_dict(best_model_wts)

    # Save final training history
    with open(os.path.join(logs_dir, 'final_training_history.json'), 'w') as f:
        json.dump(history, f)

    # Plot final training history
    plot_training_history(history, os.path.join(vis_dir, 'final_training_history.png'))

    return model, history


def plot_training_history(history, output_path):
    """
    Plot training history.

    Args:
        history: Dictionary with training history
        output_path: Path to save the plot
    """
    plt.figure(figsize=(15, 5))

    # Plot total loss
    plt.subplot(1, 3, 1)
    plt.plot(history['train_loss'], label='Train')
    plt.plot(history['val_loss'], label='Validation')
    plt.title('Total Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    # Plot segmentation loss
    plt.subplot(1, 3, 2)
    plt.plot(history['train_seg_loss'], label='Train')
    plt.plot(history['val_seg_loss'], label='Validation')
    plt.title('Segmentation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    # Plot heatmap loss
    plt.subplot(1, 3, 3)
    plt.plot(history['train_heatmap_loss'], label='Train')
    plt.plot(history['val_heatmap_loss'], label='Validation')
    plt.title('Heatmap Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()

    plt.tight_layout()
    plt.savefig(output_path)
    plt.close()


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Train Chess Board Detection Model with Fixed Loss')
    parser.add_argument('--epochs', type=int, default=100, help='Number of epochs to train')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--separation_weight', type=float, default=0.3, 
                        help='Weight for separation loss in heatmap loss')
    parser.add_argument('--peak_separation_weight', type=float, default=0.2, 
                        help='Weight for peak separation loss in heatmap loss')
    parser.add_argument('--heatmap_weight', type=float, default=1.0, 
                        help='Weight for heatmap loss in total loss')
    parser.add_argument('--output_dir', type=str, 
                        default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Directory to save outputs')
    parser.add_argument('--save_interval', type=int, default=5,
                        help='Interval for saving checkpoints')
    args = parser.parse_args()

    # Print GPU information
    print("\n" + "="*50)
    print("CUDA Available:", torch.cuda.is_available())
    if torch.cuda.is_available():
        print("GPU Device Count:", torch.cuda.device_count())
        print("GPU Device Name:", torch.cuda.get_device_name(0))
        print("Using Device:", DEVICE)
    print("="*50 + "\n")

    # Get data loaders
    print("Loading data...")
    dataloaders = get_data_loaders(
        data_dir=os.path.join(DATA_DIR, 'real'),
        annotation_file=os.path.join(DATA_DIR, 'real_annotations.json')
    )

    # Initialize model
    print("Initializing model...")
    model = ChessBoardUNet(n_channels=3, bilinear=True)
    model = model.to(DEVICE)
    print(f"Model moved to {DEVICE}")

    # Define loss functions and optimizer
    criterion_seg = DiceLoss()
    criterion_heatmap = FixedHeatmapLoss(
        separation_weight=args.separation_weight,
        peak_separation_weight=args.peak_separation_weight
    )
    optimizer = optim.Adam(model.parameters(), lr=args.lr)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Train model
    print(f"Starting training for {args.epochs} epochs...")
    model, history = train_model(
        model=model,
        dataloaders={'train': dataloaders['train'], 'val': dataloaders['val']},
        criterion_seg=criterion_seg,
        criterion_heatmap=criterion_heatmap,
        optimizer=optimizer,
        output_dir=args.output_dir,
        num_epochs=args.epochs,
        heatmap_weight=args.heatmap_weight,
        save_interval=args.save_interval
    )

    print("Training completed!")


if __name__ == "__main__":
    main()
