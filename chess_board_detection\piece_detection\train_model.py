import os
import argparse
from ultralytics import YOLO
import torch
import platform
from datetime import datetime

def train_model(
    model_path,
    data_yaml,
    epochs=100,
    batch_size=4,
    img_size=416,
    device='cpu',
    workers=1,
    patience=20,
    output_dir=None
):
    """
    Train a YOLO model on the chess pieces dataset.
    
    Args:
        model_path: Path to the pre-trained model
        data_yaml: Path to the dataset YAML file
        epochs: Number of training epochs
        batch_size: Batch size
        img_size: Image size for training
        device: Device to train on ('cpu' or 'cuda')
        workers: Number of worker threads
        patience: Early stopping patience
        output_dir: Directory to save results
    """
    # Create timestamp for the run
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Set default output directory if not provided
    if output_dir is None:
        output_dir = os.path.join('chess_board_detection/piece_detection/models', f'run_{timestamp}')
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Print system info
    print(f"Python version: {platform.python_version()}")
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    print(f"Training on: {device}")
    
    # Load the model
    model = YOLO(model_path)
    
    # Train the model
    results = model.train(
        data=data_yaml,
        epochs=epochs,
        imgsz=img_size,
        batch=batch_size,
        patience=patience,
        device=device,
        workers=workers,
        project=output_dir,
        name=f'chess_pieces_{timestamp}',
        exist_ok=True,
        pretrained=True,
        verbose=True,
        seed=42,
        cache=True,  # Cache images for faster training
        close_mosaic=10,  # Disable mosaic augmentation for final epochs
        amp=False,  # Disable mixed precision (more stable on CPU)
    )
    
    # Export the model to ONNX format
    model.export(format='onnx', dynamic=True, simplify=True)
    
    print(f"Training complete. Model saved to {output_dir}")
    return results

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train YOLO model on chess pieces dataset")
    parser.add_argument("--model", type=str, default="yolo11n.pt", help="Path to pre-trained model")
    parser.add_argument("--data", type=str, default="chess_board_detection/piece_detection/chess_pieces.yaml", 
                        help="Path to dataset YAML file")
    parser.add_argument("--epochs", type=int, default=100, help="Number of training epochs")
    parser.add_argument("--batch", type=int, default=4, help="Batch size")
    parser.add_argument("--img-size", type=int, default=416, help="Image size for training")
    parser.add_argument("--device", type=str, default="cpu", help="Device to train on ('cpu' or 'cuda')")
    parser.add_argument("--workers", type=int, default=1, help="Number of worker threads")
    parser.add_argument("--patience", type=int, default=20, help="Early stopping patience")
    parser.add_argument("--output-dir", type=str, default=None, help="Directory to save results")
    
    args = parser.parse_args()
    train_model(
        args.model,
        args.data,
        args.epochs,
        args.batch,
        args.img_size,
        args.device,
        args.workers,
        args.patience,
        args.output_dir
    )
