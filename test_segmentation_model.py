"""
Test the segmentation model to visualize its output.
"""

import os
import sys
import argparse
import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
from torchvision import transforms

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import models
from chess_board_detection.models.segmentation_only_model import TinySegmentationModel

# Configuration
CONFIG = {
    # Segmentation model
    "segmentation_model": "chess_board_detection/models/segmentation_only/tiny_20250519_091307/best_model_dice.pth",
}

def load_model(model_path):
    """Load the TinySegmentationModel."""
    model = TinySegmentationModel(n_channels=3)
    model.load_state_dict(torch.load(model_path, map_location=torch.device('cpu')))
    model.eval()
    return model

def preprocess_image(image_path, target_size=(256, 256)):
    """
    Preprocess an image for model input.
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image from {image_path}")

    # Save original image for reference
    cv2.imwrite('original_image.png', image)

    # Convert BGR to RGB
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize to target size
    image_resized = cv2.resize(image_rgb, target_size, interpolation=cv2.INTER_AREA)
    
    # Save resized image for reference
    cv2.imwrite('resized_image.png', cv2.cvtColor(image_resized, cv2.COLOR_RGB2BGR))
    
    return image_resized, image

def normalize_for_model(image):
    """Normalize image for model input."""
    # Convert to tensor and normalize
    transform = transforms.Compose([
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    # Apply transformation
    input_tensor = transform(Image.fromarray(image)).unsqueeze(0)
    
    return input_tensor

def visualize_segmentation(image, segmentation, output_path):
    """
    Visualize the segmentation results.
    """
    # Create figure with subplots
    fig, axs = plt.subplots(2, 3, figsize=(15, 10))
    
    # Plot original image
    axs[0, 0].imshow(image)
    axs[0, 0].set_title('Input Image (256x256)')
    axs[0, 0].axis('off')
    
    # Plot raw segmentation
    axs[0, 1].imshow(segmentation, cmap='gray')
    axs[0, 1].set_title(f'Raw Segmentation (min={segmentation.min():.3f}, max={segmentation.max():.3f})')
    axs[0, 1].axis('off')
    
    # Plot normalized segmentation
    normalized = cv2.normalize(segmentation, None, 0, 1, cv2.NORM_MINMAX)
    axs[0, 2].imshow(normalized, cmap='gray')
    axs[0, 2].set_title('Normalized Segmentation')
    axs[0, 2].axis('off')
    
    # Plot binary masks with different thresholds
    thresholds = [0.3, 0.5, 0.7]
    for i, threshold in enumerate(thresholds):
        binary_mask = (segmentation > threshold).astype(np.uint8)
        axs[1, i].imshow(binary_mask, cmap='gray')
        axs[1, i].set_title(f'Binary Mask (threshold={threshold})')
        axs[1, i].axis('off')
    
    # Save figure
    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches='tight')
    plt.close(fig)
    
    # Also save individual images
    cv2.imwrite('segmentation_raw.png', (segmentation * 255).astype(np.uint8))
    cv2.imwrite('segmentation_normalized.png', (normalized * 255).astype(np.uint8))
    
    for threshold in thresholds:
        binary_mask = (segmentation > threshold).astype(np.uint8) * 255
        cv2.imwrite(f'segmentation_binary_{threshold}.png', binary_mask)

def main():
    # Parse arguments
    parser = argparse.ArgumentParser(description='Test segmentation model')
    parser.add_argument('--image_path', type=str, required=True,
                        help='Path to input image')
    parser.add_argument('--model_path', type=str, default=CONFIG["segmentation_model"],
                        help='Path to segmentation model')
    parser.add_argument('--output_path', type=str, default='segmentation_results.png',
                        help='Path to output visualization')
    args = parser.parse_args()
    
    # Load model
    print(f"Loading model from {args.model_path}")
    model = load_model(args.model_path)
    
    # Preprocess image
    print(f"Processing image: {args.image_path}")
    preprocessed_image, original_image = preprocess_image(args.image_path)
    
    # Normalize for model
    input_tensor = normalize_for_model(preprocessed_image)
    
    # Run inference
    print("Running inference...")
    with torch.no_grad():
        outputs = model(input_tensor)
    
    # Extract segmentation
    segmentation = torch.sigmoid(outputs['segmentation']).cpu().numpy()[0, 0]
    
    # Print segmentation statistics
    print(f"Segmentation min: {segmentation.min():.4f}, max: {segmentation.max():.4f}, mean: {segmentation.mean():.4f}")
    
    # Visualize results
    print("Creating visualizations...")
    visualize_segmentation(preprocessed_image, segmentation, args.output_path)
    
    print(f"Results saved to {args.output_path}")

if __name__ == "__main__":
    main()
