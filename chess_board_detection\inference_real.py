"""
Inference script for chess board detection using the model trained on real images.
"""

import os
import argparse
import numpy as np
import cv2
import torch
import matplotlib.pyplot as plt
from scipy.ndimage import gaussian_filter
from scipy.ndimage.measurements import maximum_position
import albumentations as A
from albumentations.pytorch import ToTensorV2

from models.unet import ChessBoardUNet
from config import MODELS_DIR, INPUT_SIZE, DEVICE


def load_model(model_path):
    """
    Load a trained model.

    Args:
        model_path (str): Path to the model checkpoint.

    Returns:
        model: Loaded PyTorch model.
    """
    # Initialize model
    model = ChessBoardUNet(n_channels=3, bilinear=True)

    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=DEVICE)
    model.load_state_dict(checkpoint['model_state_dict'])

    # Set model to evaluation mode
    model.eval()
    model = model.to(DEVICE)

    return model


def preprocess_image(image_path):
    """
    Preprocess an image for inference.

    Args:
        image_path (str): Path to the image.

    Returns:
        tuple: (preprocessed_image, original_image, original_height, original_width)
    """
    # Read image
    image = cv2.imread(image_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

    # Store original dimensions
    original_height, original_width = image.shape[:2]
    original_image = image.copy()

    # Define preprocessing
    transform = A.Compose([
        A.Resize(INPUT_SIZE[0], INPUT_SIZE[1]),
        A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
        ToTensorV2(),
    ])

    # Apply preprocessing
    preprocessed = transform(image=image)
    preprocessed_image = preprocessed['image']

    # Add batch dimension
    preprocessed_image = preprocessed_image.unsqueeze(0)

    return preprocessed_image, original_image, original_height, original_width


def validate_corners(corners, mask):
    """
    Validate and correct detected corners to ensure they form a valid quadrilateral.

    Args:
        corners (list): List of (x, y) corner coordinates.
        mask (numpy.ndarray): Binary mask of the chess board.

    Returns:
        list: Corrected corner coordinates.
    """
    # Convert corners to numpy array for easier manipulation
    corners_array = np.array(corners)

    # Check if any corners are too close to each other (potential duplicates)
    min_distance = 20  # Minimum distance between corners in pixels

    # Function to calculate distance between two points
    def distance(p1, p2):
        return np.sqrt((p1[0] - p2[0])**2 + (p1[1] - p2[1])**2)

    # Check for duplicate corners (too close to each other)
    for i in range(4):
        for j in range(i+1, 4):
            if distance(corners[i], corners[j]) < min_distance:
                print(f"Warning: Corners {i+1} and {j+1} are too close. Attempting to correct.")

                # Find the centroid of the mask to help determine corner positions
                y_indices, x_indices = np.where(mask > 0)
                if len(x_indices) == 0 or len(y_indices) == 0:
                    continue  # Skip if mask is empty

                centroid_x = np.mean(x_indices)
                centroid_y = np.mean(y_indices)

                # Expected corner positions relative to centroid
                # 0: top-left, 1: top-right, 2: bottom-right, 3: bottom-left
                expected_positions = [
                    (-1, -1),  # top-left: negative x, negative y from centroid
                    (1, -1),   # top-right: positive x, negative y
                    (1, 1),    # bottom-right: positive x, positive y
                    (-1, 1)    # bottom-left: negative x, positive y
                ]

                # Adjust the problematic corners based on their expected positions
                for corner_idx in [i, j]:
                    # Calculate vector from centroid to expected position
                    expected_x = centroid_x + expected_positions[corner_idx][0] * mask.shape[1] * 0.3
                    expected_y = centroid_y + expected_positions[corner_idx][1] * mask.shape[0] * 0.3

                    # Move the corner towards its expected position
                    corners_array[corner_idx] = [expected_x, expected_y]

    # Enhanced geometric validation to ensure corners form a valid quadrilateral

    # First, check if we have a valid mask to work with
    if np.sum(mask) > 100:  # Ensure we have a reasonable mask
        # Find the centroid of the mask
        y_indices, x_indices = np.where(mask > 0)
        centroid_x = np.mean(x_indices)
        centroid_y = np.mean(y_indices)

        # Calculate angle from centroid to each corner
        angles = []
        for corner in corners_array:
            dx = corner[0] - centroid_x
            dy = corner[1] - centroid_y
            angle = np.arctan2(dy, dx)
            # Convert to degrees in range [0, 360)
            angle_deg = (np.degrees(angle) + 360) % 360
            angles.append(angle_deg)

        # Sort corners by angle around centroid
        # This handles arbitrary orientations of the chess board
        sorted_indices = np.argsort(angles)
        sorted_corners = corners_array[sorted_indices]

        # Find the corner with the smallest y-coordinate (topmost)
        topmost_idx = np.argmin(sorted_corners[:, 1])

        # Reorder so that we start with the topmost corner
        # and go clockwise: top-left, top-right, bottom-right, bottom-left
        reordered_corners = np.roll(sorted_corners, -topmost_idx, axis=0)

        # Now we need to determine if we're starting with top-left or top-right
        # by checking if the first corner is on the left or right half
        if reordered_corners[0][0] > centroid_x:
            # We're starting with top-right, so shift by 1 to start with top-left
            reordered_corners = np.roll(reordered_corners, 1, axis=0)

        # Convert to the expected order: top-left, top-right, bottom-right, bottom-left
        ordered_corners = [tuple(corner) for corner in reordered_corners]

        # Verify the ordering makes a convex quadrilateral
        # If not, fall back to simpler ordering method
        if not is_convex_quadrilateral(ordered_corners):
            print("Warning: Ordered corners don't form a convex quadrilateral. Using fallback method.")
            ordered_corners = order_corners_by_position(corners_array)
    else:
        # Fallback to simpler ordering if mask is not valid
        ordered_corners = order_corners_by_position(corners_array)

    return ordered_corners


def is_convex_quadrilateral(corners):
    """
    Check if the given corners form a convex quadrilateral.

    Args:
        corners (list): List of (x, y) corner coordinates.

    Returns:
        bool: True if corners form a convex quadrilateral, False otherwise.
    """
    # Convert to numpy array for easier manipulation
    corners_array = np.array(corners)

    # Add the first corner at the end to close the loop
    corners_array = np.vstack([corners_array, corners_array[0]])

    # Calculate cross products of consecutive edges
    cross_products = []
    for i in range(4):
        v1 = corners_array[i+1] - corners_array[i]
        v2 = corners_array[(i+2) % 5] - corners_array[i+1]
        cross_products.append(np.cross(v1, v2))

    # Check if all cross products have the same sign (all positive or all negative)
    return all(cp > 0 for cp in cross_products) or all(cp < 0 for cp in cross_products)


def order_corners_by_position(corners_array):
    """
    Order corners by their position: top-left, top-right, bottom-right, bottom-left.
    This is a fallback method when geometric ordering fails.

    Args:
        corners_array (numpy.ndarray): Array of corner coordinates.

    Returns:
        list: Ordered corner coordinates.
    """
    # Sort by y-coordinate first to separate top and bottom corners
    top_corners = corners_array[corners_array[:, 1].argsort()][:2]
    bottom_corners = corners_array[corners_array[:, 1].argsort()][2:]

    # Sort top corners by x-coordinate (left to right)
    top_corners = top_corners[top_corners[:, 0].argsort()]

    # Sort bottom corners by x-coordinate (left to right)
    bottom_corners = bottom_corners[bottom_corners[:, 0].argsort()]

    # Combine into the correct order: top-left, top-right, bottom-right, bottom-left
    ordered_corners = [
        tuple(top_corners[0]),      # top-left
        tuple(top_corners[1]),      # top-right
        tuple(bottom_corners[1]),   # bottom-right
        tuple(bottom_corners[0])    # bottom-left
    ]

    return ordered_corners


def find_peaks(heatmap, threshold=0.5, smooth=True):
    """
    Find the peak in a heatmap with improved reliability.

    Args:
        heatmap (numpy.ndarray): Heatmap.
        threshold (float): Threshold for peak detection.
        smooth (bool): Whether to apply Gaussian smoothing.

    Returns:
        tuple: (x, y) coordinates of the peak.
    """
    # Apply Gaussian smoothing if requested
    if smooth:
        heatmap = gaussian_filter(heatmap, sigma=2)

    # Find the global maximum first
    max_val = heatmap.max()
    if max_val < 0.1:  # If the heatmap has very low confidence
        y, x = maximum_position(heatmap)
        return x, y

    # Apply additional Gaussian smoothing with a smaller kernel
    # This helps reduce noise while preserving the peak location
    heatmap = gaussian_filter(heatmap, sigma=1)

    # Use a dynamic threshold based on the maximum value
    # Lower threshold to better capture the peak in focused heatmaps
    dynamic_threshold = max(0.2, threshold) * max_val

    # Threshold the heatmap
    thresholded = (heatmap > dynamic_threshold).astype(np.uint8)

    # If no peaks are found, return the maximum position
    if thresholded.sum() == 0:
        y, x = maximum_position(heatmap)
        return x, y

    # Find connected components
    num_labels, labels, stats, centroids = cv2.connectedComponentsWithStats(thresholded)

    if num_labels <= 1:
        y, x = maximum_position(heatmap)
        return x, y

    # Find the component with the highest peak value, not just the largest area
    component_max_values = []
    for i in range(1, num_labels):  # Skip background (0)
        component_mask = (labels == i)
        max_val_in_component = np.max(heatmap * component_mask)
        component_max_values.append((i, max_val_in_component))

    # Sort by max value and get the label with highest value
    best_label = sorted(component_max_values, key=lambda x: x[1], reverse=True)[0][0]

    # Get the centroid of the best component
    x, y = centroids[best_label]

    return int(x), int(y)


def detect_chessboard(model, image_path, output_path=None):
    """
    Detect a chess board in an image.

    Args:
        model: PyTorch model.
        image_path (str): Path to the input image.
        output_path (str, optional): Path to save the output visualization.

    Returns:
        dict: Detection results.
    """
    # Preprocess image
    preprocessed_image, original_image, original_height, original_width = preprocess_image(image_path)

    # Move to device
    preprocessed_image = preprocessed_image.to(DEVICE)

    # Perform inference
    with torch.no_grad():
        outputs = model(preprocessed_image)

    # Get segmentation mask
    segmentation = outputs['segmentation']
    segmentation = torch.sigmoid(segmentation).cpu().numpy()[0, 0]

    # Resize mask to original dimensions
    segmentation = cv2.resize(segmentation, (original_width, original_height))

    # Threshold mask
    binary_mask = (segmentation > 0.5).astype(np.uint8) * 255

    # Get corner heatmaps
    corner_heatmaps = outputs['corner_heatmaps'].cpu().numpy()[0]

    # Find peaks in each heatmap and scale to original dimensions
    corners = []
    for i in range(4):
        heatmap = corner_heatmaps[i]
        # Resize heatmap to original dimensions
        heatmap = cv2.resize(heatmap, (original_width, original_height))
        # Find peak
        x, y = find_peaks(heatmap)
        corners.append((x, y))

    # Validate and correct corners to ensure they form a valid quadrilateral
    corners = validate_corners(corners, binary_mask)

    # Visualize results
    if output_path is not None:
        plt.figure(figsize=(15, 10))

        # Original image
        plt.subplot(2, 3, 1)
        plt.imshow(original_image)
        plt.title('Original Image')
        plt.axis('off')

        # Segmentation mask
        plt.subplot(2, 3, 2)
        plt.imshow(binary_mask, cmap='gray')
        plt.title('Segmentation Mask')
        plt.axis('off')

        # Detected corners
        plt.subplot(2, 3, 3)
        plt.imshow(original_image)
        for i, (x, y) in enumerate(corners):
            plt.plot(x, y, 'ro', markersize=8)
            plt.text(x, y, str(i+1), color='white', fontsize=12,
                    bbox=dict(facecolor='red', alpha=0.7))

        # Draw the chess board outline
        corners_array = np.array(corners)
        corners_array = np.vstack([corners_array, corners_array[0]])
        plt.plot(corners_array[:, 0], corners_array[:, 1], 'g-', linewidth=2)
        plt.title('Detected Chess Board')
        plt.axis('off')

        # Create a new figure for corner heatmaps to show all 4
        plt.figure(figsize=(15, 5))
        for i in range(4):  # Show all 4 corner heatmaps
            plt.subplot(1, 4, i+1)
            heatmap = cv2.resize(corner_heatmaps[i], (original_width, original_height))
            plt.imshow(heatmap, cmap='jet')
            plt.title(f'Corner {i+1} Heatmap')
            plt.axis('off')
            plt.plot(corners[i][0], corners[i][1], 'ro', markersize=8)

        plt.tight_layout()
        plt.savefig(output_path.replace('.png', '_heatmaps.png'))
        plt.close()

        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()

    return {
        'segmentation': binary_mask,
        'corners': corners
    }


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Chess Board Detection')
    parser.add_argument('--image', type=str, required=True, help='Path to input image')
    parser.add_argument('--model', type=str, default=os.path.join(MODELS_DIR, 'best_model_real.pth'),
                        help='Path to model checkpoint')
    parser.add_argument('--output', type=str, default=None, help='Path to save output visualization')
    args = parser.parse_args()

    # Load model
    model = load_model(args.model)

    # Detect chess board
    results = detect_chessboard(model, args.image, args.output)

    print(f"Chess board detection completed. Results saved to {args.output}")

    # Print corner coordinates
    print("Detected corners:")
    for i, (x, y) in enumerate(results['corners']):
        print(f"Corner {i+1}: ({x}, {y})")


if __name__ == "__main__":
    main()
