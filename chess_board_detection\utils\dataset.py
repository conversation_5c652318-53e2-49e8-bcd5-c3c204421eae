"""
Dataset classes for chess board detection.
"""

import os
import json
import numpy as np
import cv2
from PIL import Image
import torch
from torch.utils.data import Dataset, DataLoader
import albumentations as A
from albumentations.pytorch import ToTensorV2

import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import INPUT_SIZE, BATCH_SIZE, AUGMENTATION_PROBABILITY


class ChessBoardDataset(Dataset):
    """
    Dataset for chess board detection.
    """
    def __init__(self, data_dir, split='train', transform=None):
        """
        Args:
            data_dir (str): Directory with all the images and annotations.
            split (str): 'train', 'val', or 'test'.
            transform (callable, optional): Optional transform to be applied on a sample.
        """
        self.data_dir = data_dir
        self.split = split
        self.transform = transform
        
        # Load the dataset index file
        index_file = os.path.join(data_dir, f'{split}_index.json')
        if os.path.exists(index_file):
            with open(index_file, 'r') as f:
                self.data_index = json.load(f)
        else:
            # If index file doesn't exist, create one by scanning the directory
            self.data_index = self._create_index()
            with open(index_file, 'w') as f:
                json.dump(self.data_index, f)
    
    def _create_index(self):
        """
        Create an index of images and their corresponding masks and keypoints.
        """
        data_index = []
        image_dir = os.path.join(self.data_dir, 'images')
        mask_dir = os.path.join(self.data_dir, 'masks')
        keypoints_dir = os.path.join(self.data_dir, 'keypoints')
        
        for filename in os.listdir(image_dir):
            if filename.endswith('.jpg') or filename.endswith('.png'):
                image_path = os.path.join(image_dir, filename)
                mask_path = os.path.join(mask_dir, filename.replace('.jpg', '.png').replace('.png', '.png'))
                keypoints_path = os.path.join(keypoints_dir, filename.replace('.jpg', '.json').replace('.png', '.json'))
                
                if os.path.exists(mask_path) and os.path.exists(keypoints_path):
                    data_index.append({
                        'image': image_path,
                        'mask': mask_path,
                        'keypoints': keypoints_path
                    })
        
        return data_index
    
    def __len__(self):
        return len(self.data_index)
    
    def __getitem__(self, idx):
        """
        Get a sample from the dataset.
        """
        sample = self.data_index[idx]
        
        # Load image
        image = cv2.imread(sample['image'])
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Load mask
        mask = cv2.imread(sample['mask'], cv2.IMREAD_GRAYSCALE)
        mask = mask / 255.0  # Normalize to [0, 1]
        
        # Load keypoints
        with open(sample['keypoints'], 'r') as f:
            keypoints_data = json.load(f)
        
        # Extract corner keypoints (assuming format is [x1, y1, x2, y2, x3, y3, x4, y4])
        keypoints = np.array(keypoints_data['corners'], dtype=np.float32)
        
        # Apply transformations
        if self.transform:
            # For albumentations, we need to format keypoints as [x, y, visibility]
            keypoints_list = []
            for i in range(0, len(keypoints), 2):
                keypoints_list.append([keypoints[i], keypoints[i+1], 1])  # 1 for visible
            
            transformed = self.transform(
                image=image,
                mask=mask,
                keypoints=keypoints_list
            )
            
            image = transformed['image']
            mask = transformed['mask']
            
            # Convert keypoints back to flat array
            keypoints = []
            for kp in transformed['keypoints']:
                keypoints.extend([kp[0], kp[1]])
            keypoints = np.array(keypoints, dtype=np.float32)
        else:
            # Convert to tensors
            image = torch.from_numpy(image.transpose((2, 0, 1))).float() / 255.0
            mask = torch.from_numpy(mask).float().unsqueeze(0)
        
        return {
            'image': image,
            'mask': mask,
            'keypoints': torch.from_numpy(keypoints),
            'filename': os.path.basename(sample['image'])
        }


def get_transforms(phase):
    """
    Get transformations for different phases.
    
    Args:
        phase (str): 'train', 'val', or 'test'.
    
    Returns:
        albumentations.Compose: Composition of transforms.
    """
    if phase == 'train':
        return A.Compose([
            A.Resize(INPUT_SIZE[0], INPUT_SIZE[1]),
            A.HorizontalFlip(p=AUGMENTATION_PROBABILITY),
            A.VerticalFlip(p=AUGMENTATION_PROBABILITY),
            A.RandomRotate90(p=AUGMENTATION_PROBABILITY),
            A.RandomBrightnessContrast(p=AUGMENTATION_PROBABILITY),
            A.GaussNoise(p=AUGMENTATION_PROBABILITY),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))
    else:
        return A.Compose([
            A.Resize(INPUT_SIZE[0], INPUT_SIZE[1]),
            A.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
            ToTensorV2(),
        ], keypoint_params=A.KeypointParams(format='xy', remove_invisible=False))


def get_data_loaders(data_dir):
    """
    Get data loaders for training, validation, and testing.
    
    Args:
        data_dir (str): Directory with all the data.
    
    Returns:
        dict: Dictionary with data loaders for 'train', 'val', and 'test'.
    """
    train_dataset = ChessBoardDataset(
        data_dir=data_dir,
        split='train',
        transform=get_transforms('train')
    )
    
    val_dataset = ChessBoardDataset(
        data_dir=data_dir,
        split='val',
        transform=get_transforms('val')
    )
    
    test_dataset = ChessBoardDataset(
        data_dir=data_dir,
        split='test',
        transform=get_transforms('test')
    )
    
    return {
        'train': DataLoader(train_dataset, batch_size=BATCH_SIZE, shuffle=True, num_workers=4),
        'val': DataLoader(val_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4),
        'test': DataLoader(test_dataset, batch_size=BATCH_SIZE, shuffle=False, num_workers=4)
    }
