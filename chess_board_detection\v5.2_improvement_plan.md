# Chess Board Detection Model v5.2 Improvement Plan

## Summary of v5.1 Results

| Metric | Target for v5.1 | Final Result (v5.1) | Status | Balance Priority |
|--------|----------------|--------------------------|--------|-----------------|
| Detection Rate | 1.0 | 1.0000 | ✅ ACHIEVED | Maintain |
| Peak-to-Second Ratio | > 2.0 | 1.0014 | ❌ NOT ACHIEVED | Improve significantly |
| Peak-to-Mean Ratio | > 5.0 | 31.6316 | ✅ EXCEEDED | Moderate reduction acceptable |
| Avg Peak Value | > 2.0 | 2.7349 | ✅ EXCEEDED | Maintain |
| Total Loss | < 1000 | 1356.84 | ❌ NOT ACHIEVED | Improve moderately |
| Segmentation Loss | < 0.1 | 0.2948 | ❌ NOT ACHIEVED | Improve moderately |
| Geometric Loss | < 100 | 0.5941 | ✅ EXCEEDED | Maintain |
| MSE Loss | < 1.0 | 0.6506 | ✅ ACHIEVED | Maintain |

## Key Insights from v5.1 Training

1. **Unbalanced Metric Improvement**: Some metrics far exceeded targets while others showed minimal progress
2. **Peak-to-Second Ratio Challenge**: Despite 80 epochs of training, this metric barely improved from baseline
3. **Trade-offs Between Metrics**: Improvements in some metrics came at the expense of others
4. **Excessive Peak-to-Mean Ratio**: Achieved extraordinary values (up to 11,182) far beyond what's necessary
5. **Resource Allocation Issue**: Model focused resources on already-strong metrics instead of lagging ones

## Core Architectural Improvements for v5.2

### 1. Peak Competition Module

```python
class PeakCompetitionModule(nn.Module):
    def __init__(self, channels=4, competition_strength=3.0):
        super().__init__()
        self.channels = channels
        self.competition_strength = competition_strength
        self.conv = nn.Conv2d(channels, channels, kernel_size=3, padding=1)
        self.bn = nn.BatchNorm2d(channels)

    def forward(self, x):
        # Find peaks and their values
        batch_size, channels, h, w = x.shape
        x_flat = x.view(batch_size, channels, -1)
        values, indices = torch.topk(x_flat, k=min(5, x_flat.shape[2]), dim=2)

        # Create competition mask
        enhanced = x.clone()
        for b in range(batch_size):
            for c in range(channels):
                # Get primary peak location
                primary_idx = indices[b, c, 0].item()
                primary_y, primary_x = primary_idx // w, primary_idx % w

                # Create distance-based suppression for other peaks
                for k in range(1, values.shape[2]):
                    secondary_idx = indices[b, c, k].item()
                    secondary_y, secondary_x = secondary_idx // w, secondary_idx % w

                    # Calculate suppression factor based on relative value
                    value_ratio = values[b, c, 0] / (values[b, c, k] + 1e-6)
                    suppression = self.competition_strength * (value_ratio - 1.0)

                    # Apply suppression
                    enhanced[b, c, secondary_y, secondary_x] /= (1.0 + suppression)

        # Apply convolution and normalization
        return self.bn(self.conv(enhanced))
```

### 2. Cross-Attention Mechanism

```python
class CrossAttentionModule(nn.Module):
    def __init__(self, seg_channels, heatmap_channels):
        super().__init__()
        self.seg_proj = nn.Conv2d(seg_channels, heatmap_channels, kernel_size=1)
        self.query_conv = nn.Conv2d(heatmap_channels, heatmap_channels//8, kernel_size=1)
        self.key_conv = nn.Conv2d(heatmap_channels, heatmap_channels//8, kernel_size=1)
        self.value_conv = nn.Conv2d(heatmap_channels, heatmap_channels, kernel_size=1)
        self.gamma = nn.Parameter(torch.zeros(1))

    def forward(self, seg_features, heatmap_features):
        # Project segmentation features to heatmap space
        seg_proj = self.seg_proj(seg_features)

        # Compute attention
        batch_size, C, height, width = heatmap_features.shape

        # Compute query from heatmap features
        proj_query = self.query_conv(heatmap_features).view(batch_size, -1, height*width).permute(0, 2, 1)

        # Compute key from segmentation features
        proj_key = self.key_conv(seg_proj).view(batch_size, -1, height*width)

        # Compute attention map
        energy = torch.bmm(proj_query, proj_key)
        attention = F.softmax(energy, dim=-1)

        # Compute value from segmentation features
        proj_value = self.value_conv(seg_proj).view(batch_size, -1, height*width)

        # Apply attention
        out = torch.bmm(proj_value, attention.permute(0, 2, 1))
        out = out.view(batch_size, C, height, width)

        # Add weighted attention to original features
        out = self.gamma * out + heatmap_features

        return out
```

### 3. Balanced Peak-to-Second Ratio Loss

```python
class BalancedPeakToSecondRatioLoss(nn.Module):
    def __init__(self, target_ratio=1.8, min_ratio=1.2, max_ratio=2.2, weight=10.0):
        super().__init__()
        self.target_ratio = target_ratio
        self.min_ratio = min_ratio
        self.max_ratio = max_ratio
        self.weight = weight
        self.current_epoch = 0
        self.other_metrics = {}  # Will store other metrics for balance calculations

    def set_epoch(self, epoch):
        self.current_epoch = epoch

    def update_other_metrics(self, metrics_dict):
        """Update other metrics to enable balanced optimization"""
        self.other_metrics = metrics_dict

    def forward(self, heatmaps):
        batch_size, channels, h, w = heatmaps.shape
        loss = 0.0
        ratios = []

        for b in range(batch_size):
            for c in range(channels):
                hm = heatmaps[b, c].view(-1)

                # Find top two values
                values, _ = torch.topk(hm, k=2)
                if values.shape[0] < 2:
                    continue

                peak1, peak2 = values[0], values[1]
                current_ratio = peak1 / (peak2 + 1e-6)
                ratios.append(current_ratio.item())

                # Progressive target based on current performance and balance
                adaptive_target = self.calculate_adaptive_target(current_ratio)

                # Calculate loss with balanced penalty
                if current_ratio < adaptive_target:
                    # Need improvement - apply progressive penalty
                    ratio_gap = adaptive_target - current_ratio
                    # Use smoother penalty for better gradient flow
                    loss += torch.log1p(ratio_gap * 5) * 2.0
                elif current_ratio > self.max_ratio:
                    # Too high - apply penalty to prevent excessive values
                    excess = current_ratio - self.max_ratio
                    loss += torch.log1p(excess * 3) * 1.5

        avg_ratio = sum(ratios) / len(ratios) if ratios else 1.0

        # Apply dynamic weight based on balance with other metrics
        effective_weight = self.calculate_effective_weight(avg_ratio)

        return effective_weight * loss / (batch_size * channels), avg_ratio

    def calculate_adaptive_target(self, current_ratio):
        """Calculate adaptive target based on current performance and balance"""
        # Start with minimum target and gradually increase
        base_target = self.min_ratio + (self.target_ratio - self.min_ratio) * min(1.0, self.current_epoch / 25)

        # If other metrics are performing well, we can be more aggressive
        if self.other_metrics and 'peak_to_mean_ratio' in self.other_metrics:
            ptm_ratio = self.other_metrics['peak_to_mean_ratio']
            if ptm_ratio > 20.0:  # If peak-to-mean is very high
                # We can afford to focus more on peak-to-second
                base_target = min(base_target * 1.1, self.target_ratio)

        return base_target

    def calculate_effective_weight(self, current_ratio):
        """Calculate effective weight based on balance with other metrics"""
        # Start with base weight
        effective_weight = self.weight

        # If we're far from target, increase weight
        target_gap_percent = abs(current_ratio - self.target_ratio) / self.target_ratio
        if target_gap_percent > 0.3:  # More than 30% from target
            effective_weight *= 1.2

        # If other metrics are suffering, reduce weight
        if self.other_metrics:
            metrics_below_target = sum(1 for k, v in self.other_metrics.items()
                                     if k != 'peak_to_second_ratio' and v < 0.9)  # 90% of target
            if metrics_below_target > 1:  # If multiple metrics are below target
                effective_weight *= 0.9

        return effective_weight
```

## Essential Metrics to Monitor for Peak-to-Second Ratio

### 1. Detailed Peak-to-Second Ratio Metrics
- **Per-Corner Type P2S Ratio**: Track ratio separately for each corner type
- **P2S Ratio Distribution**: Histogram of ratios across all samples
- **P2S Ratio Gradient Magnitude**: Track gradient flow for this specific component
- **P2S Ratio Improvement Rate**: Change in ratio per epoch
- **P2S Ratio Stability**: Variance of ratio across batches

### 2. Secondary Peak Analysis
- **Secondary Peak Distance**: Average distance between primary and secondary peaks
- **Secondary Peak Relative Position**: Direction of secondary peak relative to primary
- **Secondary Peak Suppression Rate**: How effectively secondary peaks are being suppressed
- **Secondary Peak Pattern**: Identify common patterns in secondary peak formation

### 3. Primary Peak Characteristics
- **Primary Peak Sharpness**: Gradient magnitude around primary peak
- **Primary Peak Isolation**: Distance to nearest significant activation
- **Primary Peak Localization Error**: Distance from ground truth corner
- **Primary Peak Consistency**: Variation in characteristics across samples

### 4. Failure Case Analysis
- **Low P2S Ratio Samples**: Identify and analyze samples with lowest ratios
- **P2S Ratio Regression**: Identify cases where ratio decreases between epochs
- **Corner Type Difficulty**: Which corner types have lowest average P2S ratio
- **Image Characteristic Correlation**: Correlate low P2S ratio with image features

## Balanced Improvement Strategy

### 1. Convergence-Targeted Training Approach
- **Phase 1 (20 epochs)**: Balanced foundation with smooth convergence path
  - Implement dynamic weight normalization to prevent any single metric from dominating
  - Use metric-specific learning rate multipliers based on gap-to-target
  - Monitor all metrics equally with automatic weight adjustment
  - Initial learning rate: 0.0008 with convergence-targeted adjustment
  - Apply SmoothConvergenceLRScheduler with warmup_epochs=5, target_loss=10.0
  - Track convergence progress against ideal loss curve

- **Phase 2 (40 epochs)**: Proportional improvement with targeted convergence
  - Adjust weights proportionally to gap-to-target percentage
  - Implement metric improvement velocity tracking
  - Apply gradient scaling based on improvement velocity
  - Base learning rate follows smooth decay curve toward target loss
  - Adjust learning rate based on distance from ideal convergence path
  - Visualize actual vs. target loss curves after each epoch

- **Phase 3 (20 epochs)**: Final convergence and harmonization
  - Focus on harmonizing all metrics to meet targets simultaneously
  - Implement anti-regression safeguards for achieved metrics
  - Use targeted fine-tuning for specific underperforming metrics
  - Learning rate follows final convergence curve to near-zero loss
  - Apply higher learning rates to components affecting lagging metrics
  - Ensure smooth final approach to target loss values

### 2. Balanced Weight Management System
```python
class BalancedMetricWeightManager:
    def __init__(self, target_metrics, initial_weights, min_weights=None, max_weights=None):
        self.target_metrics = target_metrics  # Dictionary of metric names to target values
        self.weights = initial_weights        # Dictionary of metric names to weights
        self.min_weights = min_weights or {k: 0.1 * v for k, v in initial_weights.items()}
        self.max_weights = max_weights or {k: 10.0 * v for k, v in initial_weights.items()}
        self.current_values = {k: 0.0 for k in target_metrics}
        self.improvement_velocity = {k: 0.0 for k in target_metrics}
        self.history = {k: [] for k in target_metrics}

    def update_metrics(self, current_values):
        # Calculate improvement velocity
        for k, v in current_values.items():
            if len(self.history[k]) > 0:
                prev_value = self.history[k][-1]
                target = self.target_metrics[k]
                # Normalize improvement relative to gap
                if target > prev_value:  # Higher is better
                    gap = max(0.001, target - prev_value)
                    self.improvement_velocity[k] = (v - prev_value) / gap
                else:  # Lower is better
                    gap = max(0.001, prev_value - target)
                    self.improvement_velocity[k] = (prev_value - v) / gap

            self.current_values[k] = v
            self.history[k].append(v)

    def adjust_weights(self):
        # Calculate gap to target for each metric
        gaps = {}
        for k, v in self.current_values.items():
            target = self.target_metrics[k]
            if target > v:  # Higher is better
                gaps[k] = (target - v) / target
            else:  # Lower is better
                gaps[k] = (v - target) / target

        # Normalize gaps
        total_gap = sum(gaps.values())
        if total_gap > 0:
            normalized_gaps = {k: v / total_gap for k, v in gaps.items()}
        else:
            normalized_gaps = {k: 1.0 / len(gaps) for k in gaps}

        # Adjust weights based on gaps and improvement velocity
        for k in self.weights:
            # If improving quickly, reduce weight; if improving slowly, increase weight
            velocity_factor = 1.0
            if self.improvement_velocity[k] > 0.1:
                velocity_factor = 0.9  # Reducing weight if improving well
            elif self.improvement_velocity[k] < 0.01:
                velocity_factor = 1.2  # Increasing weight if improving slowly

            # Adjust weight based on normalized gap and velocity
            self.weights[k] = self.weights[k] * (1.0 + normalized_gaps[k] * 0.5) * velocity_factor

            # Ensure weight stays within bounds
            self.weights[k] = max(self.min_weights[k], min(self.max_weights[k], self.weights[k]))

        # Normalize weights to prevent overall loss magnitude changes
        total_weight = sum(self.weights.values())
        self.weights = {k: v / total_weight * len(self.weights) for k, v in self.weights.items()}

        return self.weights
```

### 3. Multi-Loss Convergence Monitoring System
- **Unified Loss Dashboard**: Real-time visualization of all loss components with their convergence curves
- **Component Convergence Tracker**: Monitor each loss component's progress toward its target value
- **Convergence Rate Comparison**: Compare convergence rates across different loss components
- **Component Balance Visualization**: Track the relative contribution of each loss component
- **Convergence Pattern Detection**: Identify if components are converging, oscillating, or diverging
- **Synchronized Convergence Projection**: Estimate when all components will reach target values
- **Learning Rate Impact Analysis**: Visualize how learning rate changes affect each loss component
- **Convergence Harmony Score**: Measure how uniformly all components are converging
- **End-of-Training Loss Projection**: Project final values for all loss components

## Balanced Success Metrics for v5.2

### Target Metrics with Balance Thresholds
| Metric | Target | Min Acceptable | Max Acceptable | Current (v5.1) | Gap to Target |
|--------|--------|----------------|----------------|----------------|---------------|
| Peak-to-Second Ratio | 1.8 | 1.6 | 2.2 | 1.0014 | +79.8% |
| Detection Rate | 1.0 | 0.98 | 1.0 | 1.0000 | 0% |
| Peak-to-Mean Ratio | 10.0 | 8.0 | 15.0 | 31.6316 | -216.3% (exceeded) |
| Avg Peak Value | 2.0 | 1.8 | 2.5 | 2.7349 | -36.7% (exceeded) |
| Total Loss | < 1000 | < 1200 | < 800 | 1356.84 | +35.7% |
| Segmentation Loss | < 0.15 | < 0.2 | < 0.1 | 0.2948 | +96.5% |
| Geometric Loss | < 1.0 | < 1.5 | < 0.5 | 0.5941 | -40.6% (exceeded) |
| MSE Loss | < 0.6 | < 0.8 | < 0.4 | 0.6506 | +8.4% |

### Balance Success Criteria
- **Metric Harmony Score**: All metrics must be within their acceptable ranges
- **No Excessive Overachievement**: No metric should exceed its max acceptable value by more than 20%
- **No Metric Left Behind**: No metric should be more than 10% outside its min acceptable value
- **Balanced Improvement**: Gap-to-target reduction should be within 15% across all metrics
- **Stability**: Metrics should not fluctuate by more than 10% between consecutive epochs
- **Learning Rate Efficiency**: Training should progress without excessive learning rate reductions
- **Oscillation Control**: Loss curves should show smooth convergence without significant oscillations
- **Adaptive Response**: Learning rate adjustments should correlate with improved metric performance
- **Component Balance**: All model components should show balanced gradient flow and parameter updates

## Advanced Dynamic Learning Rate Management

### 1. Multi-Loss Convergence Scheduler

```python
class MultiLossConvergenceScheduler:
    def __init__(self, optimizer, total_epochs=80, initial_lr=0.001, min_lr=0.00005,
                 warmup_epochs=5, target_losses=None, loss_weights=None,
                 smoothing_factor=0.8, convergence_shape='exponential'):
        """
        Learning rate scheduler that ensures all loss components converge smoothly to near-zero.

        Args:
            optimizer: PyTorch optimizer
            total_epochs: Total number of training epochs
            initial_lr: Initial learning rate
            min_lr: Minimum learning rate
            warmup_epochs: Number of warmup epochs
            target_losses: Dictionary of target values for each loss component
            loss_weights: Dictionary of weights for each loss component
            smoothing_factor: EMA smoothing factor (0-1)
            convergence_shape: Shape of convergence curve ('exponential', 'linear', or 'sigmoid')
        """
        self.optimizer = optimizer
        self.total_epochs = total_epochs
        self.initial_lr = initial_lr
        self.min_lr = min_lr
        self.warmup_epochs = warmup_epochs
        self.smoothing_factor = smoothing_factor
        self.convergence_shape = convergence_shape

        # Default target losses if not provided
        self.target_losses = target_losses or {
            'total_loss': 10.0,
            'segmentation_loss': 0.05,
            'heatmap_loss': 5.0,
            'geometric_loss': 0.1,
            'mse_loss': 0.1
        }

        # Default loss weights if not provided
        self.loss_weights = loss_weights or {
            'total_loss': 1.0,
            'segmentation_loss': 0.8,
            'heatmap_loss': 1.0,
            'geometric_loss': 0.5,
            'mse_loss': 0.7
        }

        # Import math module for calculations
        import math
        self.math = math

        # State variables
        self.current_epoch = 0
        self.loss_histories = {k: [] for k in self.target_losses}
        self.smoothed_losses = {k: None for k in self.target_losses}
        self.lr_history = []
        self.loss_velocities = {k: [] for k in self.target_losses}

        # Precompute ideal loss curves for each component
        self.ideal_loss_curves = {}
        for loss_name, target in self.target_losses.items():
            # Estimate initial loss based on v5.1 results
            if loss_name == 'total_loss':
                initial_estimate = 1500.0
            elif loss_name == 'segmentation_loss':
                initial_estimate = 0.3
            elif loss_name == 'heatmap_loss':
                initial_estimate = 1000.0
            elif loss_name == 'geometric_loss':
                initial_estimate = 1.0
            elif loss_name == 'mse_loss':
                initial_estimate = 0.7
            else:
                initial_estimate = 100.0  # Default for unknown loss types

            self.ideal_loss_curves[loss_name] = self._compute_ideal_curve(
                initial_estimate, target, self.convergence_shape)

    def _compute_ideal_curve(self, initial_value, target_value, shape='exponential'):
        """Compute ideal convergence curve from initial to target value"""
        curve = []

        # Warmup phase - slight decrease
        for i in range(self.warmup_epochs):
            factor = i / max(1, self.warmup_epochs)
            curve.append(initial_value * (1.0 - 0.1 * factor))

        # Main training phase
        remaining_epochs = self.total_epochs - self.warmup_epochs
        for i in range(remaining_epochs):
            progress = i / max(1, remaining_epochs)

            if shape == 'exponential':
                # Exponential decay (faster early, slower later)
                decay_factor = self.math.exp(-5 * progress)
                value = target_value + (initial_value - target_value) * decay_factor

            elif shape == 'linear':
                # Linear decay (constant rate)
                value = initial_value + progress * (target_value - initial_value)

            elif shape == 'sigmoid':
                # Sigmoid decay (slower early and late, faster in middle)
                sigmoid = 1.0 / (1.0 + self.math.exp(-10 * (progress - 0.5)))
                value = initial_value + sigmoid * (target_value - initial_value)

            else:  # Default to exponential
                decay_factor = self.math.exp(-5 * progress)
                value = target_value + (initial_value - target_value) * decay_factor

            curve.append(value)

        return curve

    def step(self, losses, metrics=None):
        """
        Update learning rate based on multiple loss components

        Args:
            losses: Dictionary of current loss values
            metrics: Optional dictionary of other metrics
        """
        # Record loss histories
        for loss_name, loss_value in losses.items():
            if loss_name in self.loss_histories:
                self.loss_histories[loss_name].append(loss_value)

                # Calculate smoothed loss using exponential moving average
                if self.smoothed_losses[loss_name] is None:
                    self.smoothed_losses[loss_name] = loss_value
                else:
                    self.smoothed_losses[loss_name] = (
                        self.smoothing_factor * self.smoothed_losses[loss_name] +
                        (1 - self.smoothing_factor) * loss_value
                    )

                # Calculate loss velocity (rate of change)
                if len(self.loss_histories[loss_name]) > 1:
                    velocity = (self.loss_histories[loss_name][-1] -
                               self.loss_histories[loss_name][-2])
                    self.loss_velocities[loss_name].append(velocity)

        # Get current learning rate
        current_lr = self.optimizer.param_groups[0]['lr']
        self.lr_history.append(current_lr)

        # Determine new learning rate for smooth convergence of all losses
        new_lr = self._calculate_multi_loss_lr()

        # Apply learning rate
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = new_lr

        self.current_epoch += 1
        return new_lr

    def _calculate_multi_loss_lr(self):
        """Calculate learning rate to ensure all loss components converge smoothly"""
        # During warmup phase
        if self.current_epoch < self.warmup_epochs:
            warmup_progress = (self.current_epoch + 1) / self.warmup_epochs
            return self.min_lr + (self.initial_lr - self.min_lr) * warmup_progress

        # After warmup phase
        progress = (self.current_epoch - self.warmup_epochs) / (self.total_epochs - self.warmup_epochs)

        # Calculate convergence gap for each loss component
        convergence_gaps = {}
        weighted_gaps = []

        for loss_name, history in self.loss_histories.items():
            if not history:
                continue

            # Get ideal loss for current epoch
            if self.current_epoch < len(self.ideal_loss_curves[loss_name]):
                ideal_loss = self.ideal_loss_curves[loss_name][self.current_epoch]
                actual_loss = self.smoothed_losses[loss_name]

                # Calculate gap ratio (>1 means we're above ideal curve, <1 means below)
                gap_ratio = actual_loss / (ideal_loss + 1e-8)
                convergence_gaps[loss_name] = gap_ratio

                # Apply weight to this loss component
                weight = self.loss_weights.get(loss_name, 1.0)
                weighted_gaps.append((gap_ratio, weight))

        # Calculate weighted average gap
        if weighted_gaps:
            total_weight = sum(w for _, w in weighted_gaps)
            avg_gap = sum(gap * w for gap, w in weighted_gaps) / (total_weight + 1e-8)
        else:
            avg_gap = 1.0  # Default to no adjustment

        # Base learning rate follows a smooth decay curve
        # Cosine annealing with warm restarts
        cycle_progress = progress % 0.2  # 20% of training is one cycle
        normalized_cycle_progress = cycle_progress / 0.2
        cosine_factor = 0.5 * (1 + self.math.cos(self.math.pi * normalized_cycle_progress))

        # Decay the max learning rate over time
        cycle_number = int(progress / 0.2)
        max_lr_decay = 0.8 ** cycle_number

        # Calculate base learning rate
        base_lr = self.min_lr + (self.initial_lr - self.min_lr) * max_lr_decay * cosine_factor

        # Adjust learning rate based on convergence gap
        if avg_gap > 1.1:  # We're above ideal curve
            # Increase learning rate to catch up, but with a cap
            adjustment = min(1.3, 1.0 + 0.3 * (avg_gap - 1.0))
            new_lr = base_lr * adjustment
        elif avg_gap < 0.9:  # We're below ideal curve
            # Decrease learning rate to avoid overshooting
            adjustment = max(0.7, 1.0 - 0.3 * (1.0 - avg_gap))
            new_lr = base_lr * adjustment
        else:
            # We're close to ideal curve, use base learning rate
            new_lr = base_lr

        # Special handling for final 20% of training
        if progress > 0.8:
            # Ensure smooth final approach to target losses
            final_phase_progress = (progress - 0.8) / 0.2  # 0 to 1 in final phase
            final_lr = self.min_lr + (base_lr - self.min_lr) * (1 - final_phase_progress)
            new_lr = min(new_lr, final_lr)

            # If any loss component is far from target, slow down the decay
            max_gap = max(convergence_gaps.values()) if convergence_gaps else 1.0
            if max_gap > 2.0:
                # Increase learning rate for components that are far from target
                new_lr = max(new_lr, self.min_lr * 5)

        # Smooth learning rate changes to avoid sudden jumps
        if self.lr_history:
            prev_lr = self.lr_history[-1]
            max_change = prev_lr * 0.2  # Limit change to 20% of previous LR
            new_lr = max(prev_lr - max_change, min(prev_lr + max_change, new_lr))

        # Ensure learning rate stays within bounds
        new_lr = max(self.min_lr, min(self.initial_lr, new_lr))

        return new_lr

    def get_convergence_status(self):
        """Get detailed convergence status for all loss components"""
        status = {}

        for loss_name in self.loss_histories:
            if not self.loss_histories[loss_name]:
                continue

            current_loss = self.smoothed_losses[loss_name]
            target_loss = self.target_losses[loss_name]

            if self.current_epoch < len(self.ideal_loss_curves[loss_name]):
                ideal_loss = self.ideal_loss_curves[loss_name][self.current_epoch]
                # How close we are to ideal curve (1.0 is perfect)
                on_track_ratio = ideal_loss / (current_loss + 1e-8)
                # Clamp to reasonable range for display
                on_track_ratio = max(0.1, min(10.0, on_track_ratio))
            else:
                ideal_loss = target_loss
                on_track_ratio = 1.0

            # Calculate progress toward target
            progress = 1.0 - (current_loss - target_loss) / (self.loss_histories[loss_name][0] - target_loss + 1e-8)
            progress = max(0.0, min(1.0, progress))

            # Estimate epochs to target
            if len(self.loss_velocities[loss_name]) > 5:
                recent_velocities = self.loss_velocities[loss_name][-5:]
                avg_velocity = sum(recent_velocities) / len(recent_velocities)

                if avg_velocity < 0:  # Loss is decreasing
                    epochs_to_target = (current_loss - target_loss) / (-avg_velocity + 1e-8)
                else:
                    epochs_to_target = float('inf')
            else:
                epochs_to_target = None

            status[loss_name] = {
                'current': current_loss,
                'ideal': ideal_loss,
                'target': target_loss,
                'on_track_ratio': on_track_ratio,
                'progress': progress,
                'epochs_to_target': epochs_to_target
            }

        return status

    def plot_convergence_curves(self, save_path=None):
        """Plot convergence curves for all loss components"""
        try:
            import matplotlib.pyplot as plt

            # Create figure with subplots
            n_losses = len(self.loss_histories)
            fig, axs = plt.subplots(n_losses + 1, 1, figsize=(12, 4 * (n_losses + 1)))

            # Plot each loss component
            for i, (loss_name, history) in enumerate(self.loss_histories.items()):
                if not history:
                    continue

                ax = axs[i]
                epochs = list(range(len(history)))

                # Plot actual loss
                ax.plot(epochs, history, 'b-', label='Actual')

                # Plot smoothed loss
                smoothed = []
                for j in range(len(history)):
                    if j == 0:
                        smoothed.append(history[0])
                    else:
                        smoothed.append(self.smoothing_factor * smoothed[-1] +
                                       (1 - self.smoothing_factor) * history[j])
                ax.plot(epochs, smoothed, 'g-', label='Smoothed')

                # Plot ideal curve
                ideal_epochs = list(range(len(self.ideal_loss_curves[loss_name])))
                ax.plot(ideal_epochs, self.ideal_loss_curves[loss_name], 'r--',
                       label='Ideal Curve')

                # Plot target
                ax.axhline(y=self.target_losses[loss_name], color='k', linestyle=':',
                          label=f'Target: {self.target_losses[loss_name]:.4f}')

                ax.set_title(f'{loss_name} Convergence')
                ax.set_xlabel('Epoch')
                ax.set_ylabel('Loss')
                ax.legend()
                ax.grid(True)

            # Plot learning rate
            ax = axs[-1]
            lr_epochs = list(range(len(self.lr_history)))
            ax.plot(lr_epochs, self.lr_history, 'b-')
            ax.set_title('Learning Rate')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Learning Rate')
            ax.grid(True)

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()

        except ImportError:
            print("Matplotlib not available for plotting")
```

### 2. Metric-Specific Learning Rate Adjustment

```python
class MetricSpecificLROptimizer:
    def __init__(self, model, base_optimizer, base_lr=0.001):
        self.model = model
        self.base_optimizer = base_optimizer
        self.base_lr = base_lr
        self.param_groups = base_optimizer.param_groups

        # Map model components to metrics they influence
        self.component_to_metric_map = {
            'encoder': ['segmentation_loss', 'mse_loss'],
            'decoder': ['detection_rate'],
            'segmentation': ['segmentation_loss'],
            'heatmap': ['mse_loss', 'peak_to_second_ratio'],
            'peak_competition': ['peak_to_second_ratio'],
            'cross_attention': ['peak_to_second_ratio', 'segmentation_loss']
        }

        # Initialize parameter-specific learning rates
        self.param_lrs = {}
        for name, param in model.named_parameters():
            if param.requires_grad:
                self.param_lrs[name] = base_lr

    def update_metric_priorities(self, metrics, targets):
        """Update learning rates based on metric performance vs targets"""
        # Calculate metric priorities based on gap to target
        metric_priorities = {}
        for metric, value in metrics.items():
            if metric in targets:
                target = targets[metric]
                # Calculate normalized gap (0-1 range)
                if target > value:  # Higher is better
                    gap = (target - value) / target
                else:  # Lower is better
                    gap = (value - target) / target

                # Convert gap to priority (0-2 range, where 1 is neutral)
                metric_priorities[metric] = 1.0 + min(gap, 1.0)

        # Update parameter learning rates based on metric priorities
        for name, param in self.model.named_parameters():
            if not param.requires_grad:
                continue

            # Find which component this parameter belongs to
            component = None
            for comp_name in self.component_to_metric_map:
                if comp_name in name:
                    component = comp_name
                    break

            if component:
                # Get metrics influenced by this component
                influenced_metrics = self.component_to_metric_map[component]

                # Calculate average priority for this parameter
                priorities = [metric_priorities.get(metric, 1.0) for metric in influenced_metrics
                             if metric in metric_priorities]

                if priorities:
                    # Emphasize peak-to-second ratio
                    if 'peak_to_second_ratio' in influenced_metrics and 'peak_to_second_ratio' in metric_priorities:
                        p2s_priority = metric_priorities['peak_to_second_ratio']
                        priorities.append(p2s_priority)  # Add it twice for more weight

                    avg_priority = sum(priorities) / len(priorities)

                    # Update learning rate based on priority
                    self.param_lrs[name] = self.base_lr * avg_priority

        # Apply updated learning rates to optimizer
        self._apply_param_lrs()

    def _apply_param_lrs(self):
        """Apply parameter-specific learning rates to optimizer"""
        # Group parameters by learning rate
        lr_groups = {}
        for name, param in self.model.named_parameters():
            if param.requires_grad:
                lr = self.param_lrs.get(name, self.base_lr)
                if lr not in lr_groups:
                    lr_groups[lr] = []
                lr_groups[lr].append(param)

        # Create new param groups
        new_param_groups = [{'params': params, 'lr': lr} for lr, params in lr_groups.items()]

        # Replace optimizer's param_groups
        self.base_optimizer.param_groups = new_param_groups
        self.param_groups = self.base_optimizer.param_groups

    def step(self):
        """Perform optimization step"""
        return self.base_optimizer.step()

    def zero_grad(self):
        """Zero gradients"""
        return self.base_optimizer.zero_grad()
```

### 3. Loss Component Balancer

```python
class LossComponentBalancer:
    def __init__(self, loss_components=None, target_ratios=None, initial_weights=None):
        """
        Dynamically balances loss components to ensure all converge smoothly.

        Args:
            loss_components: List of loss component names
            target_ratios: Dictionary of target ratios between components
            initial_weights: Dictionary of initial weights for each component
        """
        # Default loss components if not provided
        self.loss_components = loss_components or [
            'total_loss', 'segmentation_loss', 'heatmap_loss',
            'geometric_loss', 'mse_loss'
        ]

        # Default target ratios if not provided (relative importance)
        self.target_ratios = target_ratios or {
            'segmentation_loss/total_loss': 0.05,  # Segmentation should be 5% of total
            'heatmap_loss/total_loss': 0.5,        # Heatmap should be 50% of total
            'geometric_loss/total_loss': 0.1,      # Geometric should be 10% of total
            'mse_loss/total_loss': 0.1            # MSE should be 10% of total
        }

        # Default initial weights if not provided
        self.weights = initial_weights or {
            'total_loss': 1.0,
            'segmentation_loss': 0.8,
            'heatmap_loss': 1.0,
            'geometric_loss': 0.5,
            'mse_loss': 0.7
        }

        # State variables
        self.loss_histories = {k: [] for k in self.loss_components}
        self.convergence_rates = {k: [] for k in self.loss_components}
        self.current_epoch = 0
        self.weight_history = {k: [] for k in self.weights}

    def update(self, current_losses):
        """
        Update component weights based on convergence rates

        Args:
            current_losses: Dictionary of current loss values

        Returns:
            Dictionary of updated weights
        """
        # Record loss histories
        for component, value in current_losses.items():
            if component in self.loss_histories:
                self.loss_histories[component].append(value)

        # Calculate convergence rates for each component
        for component, history in self.loss_histories.items():
            if len(history) >= 5:  # Need at least 5 epochs to calculate rate
                # Calculate average rate of change over last 5 epochs
                recent = history[-5:]
                if recent[0] > 0:  # Avoid division by zero
                    # Normalized rate: negative means decreasing (good)
                    rate = (recent[-1] - recent[0]) / (recent[0] * 5)
                    self.convergence_rates[component].append(rate)

        # Check if we have enough data to balance components
        if all(len(rates) >= 3 for rates in self.convergence_rates.values()):
            self._balance_components()

        # Record weight history
        for component, weight in self.weights.items():
            self.weight_history[component].append(weight)

        self.current_epoch += 1
        return self.weights

    def _balance_components(self):
        """Balance component weights to ensure all converge at similar rates"""
        # Calculate average convergence rate for each component
        avg_rates = {}
        for component, rates in self.convergence_rates.items():
            if rates:
                # Use recent rates (last 3)
                avg_rates[component] = sum(rates[-3:]) / 3

        if not avg_rates:
            return

        # Find the component with the worst (least negative) convergence rate
        worst_component = max(avg_rates.items(), key=lambda x: x[1])[0]
        worst_rate = avg_rates[worst_component]

        # Find the component with the best (most negative) convergence rate
        best_component = min(avg_rates.items(), key=lambda x: x[1])[0]
        best_rate = avg_rates[best_component]

        # Check if we need to balance (significant difference in rates)
        if worst_rate > best_rate * 0.5:  # If worst is converging less than half as fast
            # Increase weight for worst component
            self.weights[worst_component] *= 1.2

            # Slightly decrease weight for best component to maintain balance
            self.weights[best_component] *= 0.95

        # Check component ratios against target ratios
        for ratio_key, target in self.target_ratios.items():
            num, denom = ratio_key.split('/')
            if num in self.loss_histories and denom in self.loss_histories:
                if len(self.loss_histories[num]) > 0 and len(self.loss_histories[denom]) > 0:
                    # Calculate current ratio
                    current_ratio = (self.loss_histories[num][-1] /
                                    (self.loss_histories[denom][-1] + 1e-8))

                    # Adjust weights if ratio is far from target
                    if current_ratio > target * 1.5:  # Too high
                        self.weights[num] *= 0.9  # Decrease numerator weight
                    elif current_ratio < target * 0.5:  # Too low
                        self.weights[num] *= 1.1  # Increase numerator weight

        # Normalize weights to prevent overall scaling issues
        total_weight = sum(self.weights.values())
        self.weights = {k: v / total_weight * len(self.weights)
                       for k, v in self.weights.items()}

    def get_convergence_analysis(self):
        """Get analysis of convergence patterns for all components"""
        analysis = {}

        for component, history in self.loss_histories.items():
            if len(history) < 10:  # Need reasonable history
                continue

            # Calculate overall reduction
            initial = history[0]
            current = history[-1]
            reduction_percent = (initial - current) / initial * 100 if initial > 0 else 0

            # Calculate convergence speed (average percent change per epoch)
            speeds = []
            for i in range(1, len(history)):
                if history[i-1] > 0:  # Avoid division by zero
                    speed = (history[i] - history[i-1]) / history[i-1] * 100
                    speeds.append(speed)

            avg_speed = sum(speeds) / len(speeds) if speeds else 0

            # Detect if converging, diverging, or oscillating
            if len(speeds) >= 5:
                recent_speeds = speeds[-5:]
                direction_changes = sum(1 for i in range(1, len(recent_speeds))
                                      if (recent_speeds[i] > 0) != (recent_speeds[i-1] > 0))

                if direction_changes >= 3:
                    pattern = "oscillating"
                elif avg_speed < -0.5:
                    pattern = "converging"
                elif avg_speed > 0.5:
                    pattern = "diverging"
                else:
                    pattern = "stagnant"
            else:
                pattern = "insufficient data"

            # Estimate epochs to convergence
            if pattern == "converging" and avg_speed < 0:
                # Assuming exponential decay pattern
                decay_rate = 1 + (avg_speed / 100)  # Convert percent to rate
                target = initial * 0.01  # 1% of initial value

                # Solve for n: current * decay_rate^n = target
                import math
                if current > 0 and target > 0:
                    epochs_to_convergence = math.log(target / current) / math.log(decay_rate)
                    epochs_to_convergence = max(0, int(epochs_to_convergence))
                else:
                    epochs_to_convergence = float('inf')
            else:
                epochs_to_convergence = float('inf')

            analysis[component] = {
                'initial_value': initial,
                'current_value': current,
                'reduction_percent': reduction_percent,
                'avg_change_per_epoch': avg_speed,
                'pattern': pattern,
                'epochs_to_convergence': epochs_to_convergence
            }

        return analysis

    def plot_component_convergence(self, save_path=None):
        """Plot convergence of all loss components and their weights"""
        try:
            import matplotlib.pyplot as plt

            # Create figure with subplots
            fig, axs = plt.subplots(2, 1, figsize=(12, 10))

            # Plot loss components
            ax = axs[0]
            epochs = list(range(self.current_epoch))

            for component, history in self.loss_histories.items():
                if len(history) > 0:
                    # Normalize to 0-1 range for better comparison
                    normalized = [(v - min(history)) / (max(history) - min(history) + 1e-8)
                                 for v in history]
                    ax.plot(epochs[:len(history)], normalized, label=component)

            ax.set_title('Normalized Loss Component Convergence')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Normalized Loss')
            ax.legend()
            ax.grid(True)

            # Plot component weights
            ax = axs[1]

            for component, history in self.weight_history.items():
                if len(history) > 0:
                    ax.plot(epochs[:len(history)], history, label=f"{component} weight")

            ax.set_title('Loss Component Weights')
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Weight')
            ax.legend()
            ax.grid(True)

            plt.tight_layout()

            if save_path:
                plt.savefig(save_path)
                plt.close()
            else:
                plt.show()

        except ImportError:
            print("Matplotlib not available for plotting")
```

## Implementation Timeline with Balance Checkpoints

1. **Preparation Phase (1-2 Weeks)**:
   - Implement the Peak Competition Module and Cross-Attention Mechanism
   - Develop the Balanced Metric Weight Manager and Dynamic Learning Rate Scheduler
   - Create comprehensive monitoring dashboard for all metrics
   - **Balance Checkpoint**: Verify all components maintain metric balance in isolated tests

2. **Integration Phase (2-3 Weeks)**:
   - Integrate all new components into the v5.2 architecture
   - Implement the metric-balanced training approach
   - Develop anti-regression safeguards for achieved metrics
   - **Balance Checkpoint**: Verify integrated system maintains balance in short training runs

3. **Training Phase (4-6 Weeks)**:
   - Execute all three phases of balanced training
   - Perform weekly balance assessments and adjust weights as needed
   - Implement corrective actions if any metric shows excessive improvement or stagnation
   - **Balance Checkpoint**: After each phase, verify all metrics are improving harmoniously

4. **Evaluation Phase (1-2 Weeks)**:
   - Comprehensive analysis of final model performance
   - Document balanced improvement across all metrics
   - Identify any remaining imbalances for future versions
   - **Final Balance Assessment**: Calculate final Metric Harmony Score
