"""
Create an enhanced dataset for chess piece detection with improved color differentiation.
This script creates a highly augmented dataset with special focus on color preservation
and piece type differentiation to achieve 99%+ accuracy and precision.
"""

import os
import sys
import argparse
import random
import shutil
import yaml
import json
import cv2
import numpy as np
from pathlib import Path
from tqdm import tqdm
import albumentations as A
from datetime import datetime

def create_color_focused_augmentation():
    """
    Create an augmentation pipeline that preserves color information
    while providing diverse training examples.
    """
    return <PERSON><PERSON>([
        # Resize to target size for mobile deployment
        A.Resize(416, 416),

        # Color-preserving augmentations
        A.OneOf([
            A.RandomBrightnessContrast(brightness_limit=0.2, contrast_limit=0.2, p=1.0),
            <PERSON><PERSON>Value(hue_shift_limit=5, sat_shift_limit=20, val_shift_limit=15, p=1.0),
            A.RGBShift(r_shift_limit=10, g_shift_limit=10, b_shift_limit=10, p=1.0),
        ], p=0.8),

        # Noise and blur (limited to preserve piece details)
        A.<PERSON>([
            <PERSON><PERSON>(var_limit=(5.0, 15.0), p=1.0),
            <PERSON><PERSON>(blur_limit=3, p=1.0),
            A.MedianBlur(blur_limit=3, p=1.0),
        ], p=0.3),

        # Lighting variations (common in real-world scenarios)
        A.OneOf([
            A.RandomShadow(shadow_roi=(0, 0, 1, 1), p=1.0),
            A.RandomBrightnessContrast(brightness_limit=0.2, p=1.0),
            A.RandomToneCurve(scale=0.1, p=1.0),
        ], p=0.3),

        # Geometric transformations (limited to preserve piece orientation)
        A.OneOf([
            A.ShiftScaleRotate(shift_limit=0.1, scale_limit=0.15, rotate_limit=10,
                              border_mode=cv2.BORDER_CONSTANT, p=1.0),
            A.Perspective(scale=(0.01, 0.03), p=1.0),
        ], p=0.5),
    ], bbox_params=A.BboxParams(format='yolo', label_fields=['class_labels']))

def create_enhanced_dataset(
    input_images_dir,
    input_labels_dir,
    output_dir,
    num_augmentations=20,
    train_ratio=0.8,
    min_val_examples_per_class=3,  # Minimum examples of each piece type in validation set
    seed=42
):
    """
    Create an enhanced dataset for chess piece detection with focus on color accuracy.
    Uses stratified splitting to ensure all piece types are represented in both training and validation sets.

    Args:
        input_images_dir: Directory containing original images
        input_labels_dir: Directory containing original labels
        output_dir: Directory to save augmented dataset
        num_augmentations: Number of augmentations per image
        train_ratio: Ratio of images to use for training
        min_val_examples_per_class: Minimum number of examples per class in validation set
        seed: Random seed for reproducibility
    """
    random.seed(seed)
    np.random.seed(seed)

    # Create output directories
    train_images_dir = os.path.join(output_dir, "images", "train")
    train_labels_dir = os.path.join(output_dir, "labels", "train")
    val_images_dir = os.path.join(output_dir, "images", "val")
    val_labels_dir = os.path.join(output_dir, "labels", "val")

    os.makedirs(train_images_dir, exist_ok=True)
    os.makedirs(train_labels_dir, exist_ok=True)
    os.makedirs(val_images_dir, exist_ok=True)
    os.makedirs(val_labels_dir, exist_ok=True)

    # Get list of image files
    image_files = [f for f in os.listdir(input_images_dir) if f.endswith(('.jpg', '.jpeg', '.png'))]

    # Analyze class distribution in each image
    class_distribution = {}
    image_classes = {}

    print("Analyzing class distribution in dataset...")
    for img_file in tqdm(image_files):
        label_file = os.path.splitext(img_file)[0] + '.txt'
        label_path = os.path.join(input_labels_dir, label_file)

        if not os.path.exists(label_path):
            continue

        # Read labels
        with open(label_path, 'r') as f:
            labels = f.read().strip().split('\n')

        # Extract class IDs
        classes_in_image = set()
        for label in labels:
            if label.strip():
                parts = label.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    classes_in_image.add(class_id)

                    if class_id not in class_distribution:
                        class_distribution[class_id] = 0
                    class_distribution[class_id] += 1

        # Store classes in this image
        image_classes[img_file] = classes_in_image

    # Print class distribution
    print("\nClass distribution in dataset:")
    for class_id, count in sorted(class_distribution.items()):
        print(f"Class {class_id}: {count} instances")

    # Perform stratified split to ensure all classes are represented in both sets
    train_files = []
    val_files = []

    # Track how many examples of each class we have in validation set
    val_class_counts = {class_id: 0 for class_id in class_distribution.keys()}

    # First, ensure minimum examples of each class in validation set
    # Sort images by number of rare classes they contain
    rare_classes = [class_id for class_id, count in class_distribution.items() if count < 10]

    # Sort images by how many rare classes they contain
    images_by_rare_class_count = sorted(
        [(img, len(classes & set(rare_classes))) for img, classes in image_classes.items()],
        key=lambda x: x[1],
        reverse=True
    )

    # First pass: add images with rare classes to validation set until minimums are met
    remaining_images = set(image_files)

    for img, _ in images_by_rare_class_count:
        if img not in remaining_images:
            continue

        classes = image_classes[img]

        # Check if this image helps meet minimum requirements for any class
        needed_for_val = False
        for class_id in classes:
            if val_class_counts[class_id] < min_val_examples_per_class:
                needed_for_val = True
                break

        if needed_for_val:
            val_files.append(img)
            remaining_images.remove(img)

            # Update validation class counts
            for class_id in classes:
                val_class_counts[class_id] += 1

    # Check if all classes have minimum examples in validation set
    all_minimums_met = all(count >= min_val_examples_per_class for count in val_class_counts.values())

    if not all_minimums_met:
        print("\nWarning: Could not meet minimum validation examples for all classes.")
        print("Validation class counts after first pass:")
        for class_id, count in sorted(val_class_counts.items()):
            status = "✓" if count >= min_val_examples_per_class else "✗"
            print(f"Class {class_id}: {count}/{min_val_examples_per_class} {status}")

    # Second pass: randomly assign remaining images to maintain desired ratio
    remaining_list = list(remaining_images)
    random.shuffle(remaining_list)

    # Calculate how many more validation images we need
    target_val_count = int(len(image_files) * (1 - train_ratio))
    additional_val_needed = max(0, target_val_count - len(val_files))

    # Add remaining images to train/val sets
    additional_val = remaining_list[:additional_val_needed]
    additional_train = remaining_list[additional_val_needed:]

    val_files.extend(additional_val)
    train_files.extend(additional_train)

    print(f"\nFinal split:")
    print(f"Found {len(image_files)} images")
    print(f"Using {len(train_files)} for training and {len(val_files)} for validation")

    # Generate detailed class distribution report for final split
    train_class_counts = {class_id: 0 for class_id in class_distribution.keys()}
    for img in train_files:
        if img in image_classes:
            for class_id in image_classes[img]:
                train_class_counts[class_id] += 1

    # Print final class distribution
    print("\nFinal class distribution:")
    print(f"{'Class ID':<10} {'Total':<10} {'Train':<10} {'Val':<10} {'Train %':<10} {'Val %':<10}")
    print("-" * 60)

    for class_id in sorted(class_distribution.keys()):
        total = class_distribution[class_id]
        train_count = train_class_counts[class_id]
        val_count = val_class_counts[class_id]
        train_percent = train_count / total * 100 if total > 0 else 0
        val_percent = val_count / total * 100 if total > 0 else 0

        print(f"{class_id:<10} {total:<10} {train_count:<10} {val_count:<10} {train_percent:.1f}%{'':<5} {val_percent:.1f}%")

    # Create augmentation pipeline
    transform = create_color_focused_augmentation()

    # Process training images
    print("Processing training images...")
    process_images(train_files, input_images_dir, input_labels_dir,
                  train_images_dir, train_labels_dir, transform, num_augmentations)

    # Process validation images (fewer augmentations for validation)
    print("Processing validation images...")
    process_images(val_files, input_images_dir, input_labels_dir,
                  val_images_dir, val_labels_dir, transform, num_augmentations // 4)

    # Create dataset.yaml file
    yaml_path = os.path.join(output_dir, 'dataset.yaml')
    class_names = [
        'white_pawn', 'white_knight', 'white_bishop', 'white_rook', 'white_queen', 'white_king',
        'black_pawn', 'black_knight', 'black_bishop', 'black_rook', 'black_queen', 'black_king'
    ]

    with open(yaml_path, 'w') as f:
        f.write(f"path: {output_dir}\n")
        f.write("train: images/train\n")
        f.write("val: images/val\n")
        f.write("nc: 12\n")
        f.write("names:\n")
        for i, name in enumerate(class_names):
            f.write(f"  {i}: {name}\n")

    print(f"Dataset created at {output_dir}")
    print(f"Dataset configuration saved to {yaml_path}")

    # Create dataset summary
    class_summary = {}
    for class_id in sorted(class_distribution.keys()):
        total = class_distribution[class_id]
        train_count = train_class_counts[class_id]
        val_count = val_class_counts[class_id]

        class_summary[str(class_id)] = {
            "total": total,
            "train": train_count,
            "val": val_count,
            "train_percent": round(train_count / total * 100 if total > 0 else 0, 1),
            "val_percent": round(val_count / total * 100 if total > 0 else 0, 1)
        }

    summary = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "original_images": len(image_files),
        "train_images": len(train_files),
        "val_images": len(val_files),
        "augmentations_per_train_image": num_augmentations,
        "augmentations_per_val_image": num_augmentations // 4,
        "total_train_images": len(train_files) * (num_augmentations + 1),
        "total_val_images": len(val_files) * (num_augmentations // 4 + 1),
        "class_distribution": class_summary,
        "stratified_split": {
            "min_val_examples_per_class": min_val_examples_per_class,
            "all_minimums_met": all_minimums_met
        }
    }

    # Save summary
    with open(os.path.join(output_dir, 'dataset_summary.json'), 'w') as f:
        json.dump(summary, f, indent=2)

    # Also save a more detailed report as CSV
    with open(os.path.join(output_dir, 'class_distribution.csv'), 'w') as f:
        f.write("class_id,total,train,val,train_percent,val_percent\n")
        for class_id in sorted(class_distribution.keys()):
            total = class_distribution[class_id]
            train_count = train_class_counts[class_id]
            val_count = val_class_counts[class_id]
            train_percent = train_count / total * 100 if total > 0 else 0
            val_percent = val_count / total * 100 if total > 0 else 0

            f.write(f"{class_id},{total},{train_count},{val_count},{train_percent:.1f},{val_percent:.1f}\n")

    return yaml_path

def process_images(image_files, input_images_dir, input_labels_dir,
                  output_images_dir, output_labels_dir, transform, num_augmentations):
    """
    Process and augment images.

    Args:
        image_files: List of image files to process
        input_images_dir: Directory containing input images
        input_labels_dir: Directory containing input labels
        output_images_dir: Directory to save output images
        output_labels_dir: Directory to save output labels
        transform: Albumentations transform
        num_augmentations: Number of augmentations per image
    """
    for idx, img_file in enumerate(tqdm(image_files)):
        # Get image and label paths
        img_path = os.path.join(input_images_dir, img_file)
        label_file = os.path.splitext(img_file)[0] + '.txt'
        label_path = os.path.join(input_labels_dir, label_file)

        # Skip if label file doesn't exist
        if not os.path.exists(label_path):
            print(f"Warning: Label file not found for {img_file}, skipping")
            continue

        # Read image
        img = cv2.imread(img_path)
        if img is None:
            print(f"Warning: Could not read image {img_path}, skipping")
            continue

        # Read labels
        with open(label_path, 'r') as f:
            labels = f.read().strip().split('\n')

        # Parse bounding boxes
        bboxes = []
        class_labels = []

        for label in labels:
            if label.strip():
                parts = label.strip().split()
                if len(parts) >= 5:
                    class_id = int(parts[0])
                    x_center, y_center, width, height = map(float, parts[1:5])
                    bboxes.append([x_center, y_center, width, height])
                    class_labels.append(class_id)

        # Save original image
        orig_img_file = f"{idx:06d}_orig.jpg"
        orig_label_file = f"{idx:06d}_orig.txt"
        cv2.imwrite(os.path.join(output_images_dir, orig_img_file), img)
        shutil.copy(label_path, os.path.join(output_labels_dir, orig_label_file))

        # Generate augmentations
        for aug_idx in range(num_augmentations):
            # Apply augmentation
            augmented = transform(image=img, bboxes=bboxes, class_labels=class_labels)
            aug_img = augmented['image']
            aug_bboxes = augmented['bboxes']
            aug_class_labels = augmented['class_labels']

            # Save augmented image
            aug_img_file = f"{idx:06d}_aug_{aug_idx:03d}.jpg"
            aug_label_file = f"{idx:06d}_aug_{aug_idx:03d}.txt"

            cv2.imwrite(os.path.join(output_images_dir, aug_img_file), aug_img)

            # Save augmented labels
            with open(os.path.join(output_labels_dir, aug_label_file), 'w') as f:
                for bbox_idx in range(len(aug_bboxes)):
                    x_center, y_center, width, height = aug_bboxes[bbox_idx]
                    class_id = aug_class_labels[bbox_idx]
                    f.write(f"{class_id} {x_center} {y_center} {width} {height}\n")

def main():
    parser = argparse.ArgumentParser(description="Create enhanced dataset for chess piece detection")
    parser.add_argument("--input_images", type=str, required=True, help="Directory containing input images")
    parser.add_argument("--input_labels", type=str, required=True, help="Directory containing input labels")
    parser.add_argument("--output_dir", type=str, required=True, help="Directory to save augmented dataset")
    parser.add_argument("--num_augmentations", type=int, default=20, help="Number of augmentations per image")
    parser.add_argument("--train_ratio", type=float, default=0.8, help="Ratio of images to use for training")
    parser.add_argument("--min_val_examples", type=int, default=3,
                        help="Minimum number of examples per class in validation set")
    parser.add_argument("--seed", type=int, default=42, help="Random seed for reproducibility")

    args = parser.parse_args()

    create_enhanced_dataset(
        args.input_images,
        args.input_labels,
        args.output_dir,
        args.num_augmentations,
        args.train_ratio,
        args.min_val_examples,
        args.seed
    )

if __name__ == "__main__":
    main()
