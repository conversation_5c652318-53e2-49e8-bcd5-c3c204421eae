# Chess Piece Class Mapping
# Old naming convention to new naming convention

Old naming convention:
0: white_pawn
1: white_knight
2: white_bishop
3: white_rook
4: white_queen
5: white_king
6: black_pawn
7: black_knight
8: black_bishop
9: black_rook
10: black_queen
11: black_king

New naming convention:
0: w_pawn
1: w_knight
2: w_bishop
3: w_rook
4: w_queen
5: w_king
6: b_pawn
7: b_knight
8: b_bishop
9: b_rook
10: b_queen
11: b_king

# This naming convention change makes the labels more compact and easier to read
# when displayed on detection images. The shorter format reduces visual clutter
# while still clearly indicating the piece color (w/b) and type.
