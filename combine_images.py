import os
import cv2
import numpy as np
import glob

def combine_images(input_dir, legend_path, output_dir):
    """Combine each image with the legend"""
    # Read the legend image
    legend = cv2.imread(legend_path)
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    # Get all images in the input directory
    image_paths = glob.glob(os.path.join(input_dir, '*.jpg'))
    
    for img_path in image_paths:
        # Read the image
        img = cv2.imread(img_path)
        
        # Resize legend to match the width of the image
        legend_resized = cv2.resize(legend, (img.shape[1], int(legend.shape[0] * img.shape[1] / legend.shape[1])))
        
        # Combine image and legend vertically
        combined = np.vstack((img, legend_resized))
        
        # Save the combined image
        output_path = os.path.join(output_dir, os.path.basename(img_path))
        cv2.imwrite(output_path, combined)
        print(f"Combined image saved to {output_path}")

if __name__ == "__main__":
    # Combine images from best model
    combine_images("colored_output_best", "chess_piece_legend.png", "combined_best")
    
    # Combine images from last model
    combine_images("colored_output_last", "chess_piece_legend.png", "combined_last")
