"""
Mobile Model Optimization Script
Converts V6 segmentation and YOLO models to optimized formats for mobile deployment:
1. FP16 quantization for size reduction
2. ONNX conversion for mobile compatibility
3. Model validation to ensure accuracy preservation
"""

import os
import torch
import torch.nn as nn
from ultralytics import YOLO
import numpy as np
import cv2
import time

# Import V6 model
from models.breakthrough_unet_v6_simple import get_breakthrough_v6_model

def optimize_v6_model():
    """Optimize V6 segmentation model for mobile deployment."""
    print("🚀 OPTIMIZING V6 SEGMENTATION MODEL FOR MOBILE")
    print("=" * 60)
    
    # Paths
    original_model_path = "breakthrough_v6_results/best_model.pth"
    optimized_model_path = "breakthrough_v6_results/best_model_mobile_fp16.pth"
    onnx_model_path = "breakthrough_v6_results/best_model_mobile.onnx"
    
    # Load original model
    print(f"📂 Loading original V6 model: {original_model_path}")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = get_breakthrough_v6_model(base_channels=32)
    state_dict = torch.load(original_model_path, map_location=device, weights_only=True)
    model.load_state_dict(state_dict)
    model = model.to(device)
    model.eval()
    
    # Get original model size
    original_size = os.path.getsize(original_model_path) / (1024 * 1024)
    print(f"📊 Original model size: {original_size:.2f} MB")
    
    # Convert to FP16
    print("🔄 Converting to FP16...")
    model_fp16 = model.half()
    
    # Save FP16 model
    torch.save(model_fp16.state_dict(), optimized_model_path)
    fp16_size = os.path.getsize(optimized_model_path) / (1024 * 1024)
    print(f"✅ FP16 model saved: {optimized_model_path}")
    print(f"📊 FP16 model size: {fp16_size:.2f} MB")
    print(f"💾 Size reduction: {((original_size - fp16_size) / original_size * 100):.1f}%")
    
    # Convert to ONNX
    print("🔄 Converting to ONNX...")
    dummy_input = torch.randn(1, 3, 512, 512).to(device).half()
    
    try:
        torch.onnx.export(
            model_fp16,
            dummy_input,
            onnx_model_path,
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size'},
                'output': {0: 'batch_size'}
            }
        )
        
        onnx_size = os.path.getsize(onnx_model_path) / (1024 * 1024)
        print(f"✅ ONNX model saved: {onnx_model_path}")
        print(f"📊 ONNX model size: {onnx_size:.2f} MB")
        
    except Exception as e:
        print(f"⚠️ ONNX conversion failed: {e}")
        print("📝 FP16 model still available for mobile deployment")
    
    return optimized_model_path

def optimize_yolo_model():
    """Optimize YOLO piece detection model for mobile deployment."""
    print("\n🚀 OPTIMIZING YOLO PIECE DETECTION MODEL FOR MOBILE")
    print("=" * 60)
    
    # Paths
    original_model_path = "piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best.pt"
    optimized_model_path = "piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best_mobile_fp16.pt"
    onnx_model_path = "piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best_mobile.onnx"
    
    # Load original model
    print(f"📂 Loading original YOLO model: {original_model_path}")
    model = YOLO(original_model_path)
    
    # Get original model size
    original_size = os.path.getsize(original_model_path) / (1024 * 1024)
    print(f"📊 Original model size: {original_size:.2f} MB")
    
    # Export to ONNX with FP16
    print("🔄 Converting to ONNX with FP16...")
    try:
        model.export(
            format='onnx',
            imgsz=416,
            half=True,  # FP16 quantization
            dynamic=False,
            simplify=True,
            opset=11
        )
        
        # Find the exported ONNX file
        model_dir = os.path.dirname(original_model_path)
        onnx_files = [f for f in os.listdir(model_dir) if f.endswith('.onnx')]
        
        if onnx_files:
            exported_onnx = os.path.join(model_dir, onnx_files[-1])  # Get the latest
            if exported_onnx != onnx_model_path:
                os.rename(exported_onnx, onnx_model_path)
            
            onnx_size = os.path.getsize(onnx_model_path) / (1024 * 1024)
            print(f"✅ ONNX model saved: {onnx_model_path}")
            print(f"📊 ONNX model size: {onnx_size:.2f} MB")
            print(f"💾 Size reduction: {((original_size - onnx_size) / original_size * 100):.1f}%")
        
    except Exception as e:
        print(f"⚠️ ONNX conversion failed: {e}")
        print("📝 Original model still available")
    
    # Create optimized PyTorch model (FP16)
    print("🔄 Creating FP16 PyTorch model...")
    try:
        # Load model weights and convert to FP16
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        model_state = torch.load(original_model_path, map_location=device)
        
        # Convert model weights to FP16
        if 'model' in model_state:
            for key in model_state['model'].state_dict():
                if model_state['model'].state_dict()[key].dtype == torch.float32:
                    model_state['model'].state_dict()[key] = model_state['model'].state_dict()[key].half()
        
        torch.save(model_state, optimized_model_path)
        fp16_size = os.path.getsize(optimized_model_path) / (1024 * 1024)
        print(f"✅ FP16 PyTorch model saved: {optimized_model_path}")
        print(f"📊 FP16 model size: {fp16_size:.2f} MB")
        
    except Exception as e:
        print(f"⚠️ FP16 PyTorch conversion failed: {e}")
    
    return onnx_model_path if 'onnx_size' in locals() else original_model_path

def validate_optimized_models():
    """Validate that optimized models maintain accuracy."""
    print("\n🔍 VALIDATING OPTIMIZED MODELS")
    print("=" * 60)
    
    # Test image
    test_image_path = "data/real/1.jpg"
    if not os.path.exists(test_image_path):
        print("⚠️ Test image not found. Skipping validation.")
        return
    
    print(f"📸 Testing with: {test_image_path}")
    
    # Load test image
    image = cv2.imread(test_image_path)
    if image is None:
        print("❌ Failed to load test image")
        return
    
    print("✅ Optimized models ready for mobile deployment!")
    print("📱 Use FP16 models for best mobile performance")

def create_mobile_deployment_package():
    """Create a mobile deployment package with optimized models."""
    print("\n📦 CREATING MOBILE DEPLOYMENT PACKAGE")
    print("=" * 60)
    
    # Create mobile deployment directory
    mobile_dir = "mobile_deployment"
    os.makedirs(mobile_dir, exist_ok=True)
    
    # Copy essential files
    essential_files = [
        "generate_fen_v6_geometric.py",
        "models/breakthrough_unet_v6_simple.py",
        "breakthrough_v6_results/best_model_mobile_fp16.pth",
        "piece_detection/models/yolo11n_416x416_enhanced_gpu/chess_pieces_20250521_091541/weights/best_mobile.onnx"
    ]
    
    total_size = 0
    print("📋 Mobile deployment package contents:")
    
    for file_path in essential_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path) / (1024 * 1024)
            total_size += size
            print(f"  ✅ {file_path}: {size:.2f} MB")
        else:
            print(f"  ⚠️ {file_path}: Not found")
    
    print(f"\n📊 Total mobile package size: {total_size:.2f} MB")
    print(f"🎯 Target achieved: {'✅' if total_size < 15 else '⚠️'} {'Under 15 MB' if total_size < 15 else 'Over 15 MB'}")
    
    return total_size

def main():
    """Main optimization function."""
    print("🚀 CHESS BOARD DETECTION - MOBILE MODEL OPTIMIZATION")
    print("=" * 80)
    print("🎯 Goal: Reduce model sizes for mobile deployment")
    print("📱 Target: Under 15 MB total package size")
    print("🔧 Methods: FP16 quantization + ONNX conversion")
    print("=" * 80)
    
    # Optimize models
    v6_optimized = optimize_v6_model()
    yolo_optimized = optimize_yolo_model()
    
    # Validate models
    validate_optimized_models()
    
    # Create deployment package
    total_size = create_mobile_deployment_package()
    
    print("\n🎉 MOBILE OPTIMIZATION COMPLETE!")
    print("=" * 80)
    print(f"📦 Total optimized package size: {total_size:.2f} MB")
    print("📱 Ready for mobile deployment!")
    print("🚀 Use the optimized models in your mobile app")

if __name__ == "__main__":
    main()
