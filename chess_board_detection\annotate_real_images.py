"""
Tool for annotating real chess board images.
This script helps you create annotations for your real chess board images
by allowing you to click on the four corners of each board.
"""

import os
import argparse
import json
import cv2
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.widgets import <PERSON><PERSON>

from config import DATA_DIR


class ChessBoardAnnotator:
    """
    Interactive tool for annotating chess board corners.
    """
    def __init__(self, data_dir, output_file):
        self.data_dir = data_dir
        self.output_file = output_file
        self.annotations = []
        self.current_image_idx = 0
        self.corners = []
        self.image_files = []
        
        # Get all image files
        for f in os.listdir(data_dir):
            if f.endswith('.jpg') or f.endswith('.png'):
                self.image_files.append(f)
        
        if not self.image_files:
            print(f"No images found in {data_dir}")
            return
        
        # Load existing annotations if available
        if os.path.exists(output_file):
            with open(output_file, 'r') as f:
                self.annotations = json.load(f)
            print(f"Loaded {len(self.annotations)} existing annotations")
            
            # Find already annotated images
            annotated_files = [a['image'] for a in self.annotations]
            self.image_files = [f for f in self.image_files if f not in annotated_files]
            
            if not self.image_files:
                print("All images are already annotated")
                return
        
        # Set up the figure
        self.fig, self.ax = plt.subplots(figsize=(12, 10))
        self.fig.subplots_adjust(bottom=0.2)
        
        # Add buttons
        self.ax_prev = plt.axes([0.1, 0.05, 0.1, 0.075])
        self.ax_next = plt.axes([0.8, 0.05, 0.1, 0.075])
        self.ax_save = plt.axes([0.45, 0.05, 0.1, 0.075])
        self.ax_reset = plt.axes([0.3, 0.05, 0.1, 0.075])
        self.ax_quit = plt.axes([0.6, 0.05, 0.1, 0.075])
        
        self.btn_prev = Button(self.ax_prev, 'Previous')
        self.btn_next = Button(self.ax_next, 'Next')
        self.btn_save = Button(self.ax_save, 'Save')
        self.btn_reset = Button(self.ax_reset, 'Reset')
        self.btn_quit = Button(self.ax_quit, 'Quit')
        
        self.btn_prev.on_clicked(self.prev_image)
        self.btn_next.on_clicked(self.next_image)
        self.btn_save.on_clicked(self.save_annotations)
        self.btn_reset.on_clicked(self.reset_corners)
        self.btn_quit.on_clicked(self.quit)
        
        # Connect click event
        self.cid = self.fig.canvas.mpl_connect('button_press_event', self.on_click)
        
        # Show the first image
        self.show_current_image()
        plt.show()
    
    def show_current_image(self):
        """
        Display the current image.
        """
        if self.current_image_idx >= len(self.image_files):
            print("No more images to annotate")
            return
        
        image_file = self.image_files[self.current_image_idx]
        image_path = os.path.join(self.data_dir, image_file)
        
        # Load and display the image
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        self.ax.clear()
        self.ax.imshow(image)
        self.ax.set_title(f"Image {self.current_image_idx + 1}/{len(self.image_files)}: {image_file}\n"
                         "Click on the 4 corners of the chess board in order:\n"
                         "top-left, top-right, bottom-right, bottom-left")
        
        # Reset corners
        self.corners = []
        self.current_image = image
        self.current_image_file = image_file
        
        # Draw existing corners if any
        self.draw_corners()
        
        self.fig.canvas.draw()
    
    def on_click(self, event):
        """
        Handle mouse click events.
        """
        if event.inaxes != self.ax:
            return
        
        if len(self.corners) < 4:
            self.corners.append((event.xdata, event.ydata))
            self.draw_corners()
            
            if len(self.corners) == 4:
                # Draw the board outline
                corners_array = np.array(self.corners)
                corners_array = corners_array.reshape(-1, 2)
                corners_array = np.vstack([corners_array, corners_array[0]])
                self.ax.plot(corners_array[:, 0], corners_array[:, 1], 'g-', linewidth=2)
                self.fig.canvas.draw()
    
    def draw_corners(self):
        """
        Draw the corners on the image.
        """
        if not self.corners:
            return
        
        # Clear previous corners
        self.ax.clear()
        self.ax.imshow(self.current_image)
        self.ax.set_title(f"Image {self.current_image_idx + 1}/{len(self.image_files)}: {self.current_image_file}\n"
                         "Click on the 4 corners of the chess board in order:\n"
                         "top-left, top-right, bottom-right, bottom-left")
        
        # Draw corners
        for i, (x, y) in enumerate(self.corners):
            self.ax.plot(x, y, 'ro', markersize=8)
            self.ax.text(x, y, str(i+1), color='white', fontsize=12, 
                        bbox=dict(facecolor='red', alpha=0.7))
        
        # Draw lines between corners
        if len(self.corners) > 1:
            for i in range(len(self.corners) - 1):
                x1, y1 = self.corners[i]
                x2, y2 = self.corners[i+1]
                self.ax.plot([x1, x2], [y1, y2], 'r-', linewidth=2)
            
            # Close the polygon if all 4 corners are selected
            if len(self.corners) == 4:
                x1, y1 = self.corners[3]
                x2, y2 = self.corners[0]
                self.ax.plot([x1, x2], [y1, y2], 'r-', linewidth=2)
        
        self.fig.canvas.draw()
    
    def save_current_annotation(self):
        """
        Save the annotation for the current image.
        """
        if len(self.corners) != 4:
            print("Need 4 corners to save annotation")
            return False
        
        # Flatten corners to [x1, y1, x2, y2, x3, y3, x4, y4]
        corners_flat = []
        for corner in self.corners:
            corners_flat.extend([corner[0], corner[1]])
        
        # Add to annotations
        self.annotations.append({
            'image': self.current_image_file,
            'corners': corners_flat,
            'image_size': [self.current_image.shape[1], self.current_image.shape[0]]
        })
        
        print(f"Saved annotation for {self.current_image_file}")
        return True
    
    def next_image(self, event=None):
        """
        Move to the next image.
        """
        if len(self.corners) == 4:
            self.save_current_annotation()
        
        self.current_image_idx += 1
        if self.current_image_idx >= len(self.image_files):
            print("No more images to annotate")
            self.save_annotations()
            plt.close(self.fig)
            return
        
        self.show_current_image()
    
    def prev_image(self, event=None):
        """
        Move to the previous image.
        """
        if self.current_image_idx > 0:
            self.current_image_idx -= 1
            self.show_current_image()
    
    def reset_corners(self, event=None):
        """
        Reset the corners for the current image.
        """
        self.corners = []
        self.show_current_image()
    
    def save_annotations(self, event=None):
        """
        Save all annotations to file.
        """
        # Save current annotation if needed
        if len(self.corners) == 4:
            self.save_current_annotation()
        
        # Save all annotations
        with open(self.output_file, 'w') as f:
            json.dump(self.annotations, f, indent=2)
        
        print(f"Saved {len(self.annotations)} annotations to {self.output_file}")
    
    def quit(self, event=None):
        """
        Quit the annotator.
        """
        self.save_annotations()
        plt.close(self.fig)


def main():
    """
    Main function.
    """
    parser = argparse.ArgumentParser(description='Chess Board Annotation Tool')
    parser.add_argument('--data-dir', type=str, default=os.path.join(DATA_DIR, 'real'),
                        help='Directory with real chess board images')
    parser.add_argument('--output', type=str, default=os.path.join(DATA_DIR, 'real_annotations.json'),
                        help='Output annotation file')
    args = parser.parse_args()
    
    # Create data directory if it doesn't exist
    os.makedirs(args.data_dir, exist_ok=True)
    
    # Check if there are any images
    image_files = [f for f in os.listdir(args.data_dir) if f.endswith('.jpg') or f.endswith('.png')]
    if not image_files:
        print(f"No images found in {args.data_dir}")
        print("Please add some images and run the script again")
        return
    
    # Start the annotator
    annotator = ChessBoardAnnotator(args.data_dir, args.output)


if __name__ == "__main__":
    main()
