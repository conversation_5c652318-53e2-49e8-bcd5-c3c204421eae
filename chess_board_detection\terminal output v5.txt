PS C:\Users\<USER>\OneDrive\Desktop\a1 v1> python "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\train_v5.py" --continue_from_v4 --data_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real" --annotation_file "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\data\real_annotations.json" --output_dir "C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection" --epdels\improvedelsdels\improved_corner_detection" --epochs_phase1 40 --epochs_phase2 80 --lr_phase1 0.001 --lr_phase2 0.0005 --dropout_rate 0.2 --weight_decay 1e-5 --save_interval 10 --cpu
CUDA not available. Using CPU.
Using device: cpu
Creating expanded validation set...
Train dataset size: 14
Validation dataset size: 16
Initializing v5 model...
Loading v4 best model: C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection\checkpoints\v4\best_model.pth
Loaded v4 best model into base model
Model moved to cpu
Model parameters: 17478928
Trainable parameters: 17478928

=== Phase 1: Focus on peak-to-second ratio and detection rate ===
Saving outputs to v5 folders in C:\Users\<USER>\OneDrive\Desktop\a1 v1\chess_board_detection\models\improved_corner_detection
Epoch 1/40
----------
train:   0%|                                                                             | 0/2 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.3073, Heatmap Loss: 2152.2468, Geometric Loss: 0.8114, Total: 3229.3267
train:  50%|██████████████████████████████████▌                                  | 1/2 [00:03<00:03,  3.59s/it]Batch 1, train - Seg Loss: 0.0880, Heatmap Loss: 2050.1526, Geometric Loss: 0.9160, Total: 3076.0498
train: 100%|█████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.23s/it]
train Loss: 1120.4859, Seg Loss: 0.0628, Heatmap Loss: 746.7822, Geometric Loss: 0.3122
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.8602
  avg_peak_to_mean_ratio: 0.6310
  avg_peak_to_second_ratio: 0.3772
  detection_rate: 0.3214
  Overall Confidence Score: 0.5475
train Heatmap Components:
  mse_loss: 5.5663
  separation_loss: 357.1429
  peak_separation_loss: 357.1429
  edge_suppression_loss: 357.1429
  peak_enhancement_loss: 186.7513
  peak_to_second_ratio_loss: 0.3257
  detection_rate_loss: 0.2158
  segmentation_guidance_loss: 0.0000
val:   0%|                                                                                           | 0/2 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.0769, Heatmap Loss: 2023.5316, Geometric Loss: 0.7902, Total: 3036.0063
val:  50%|█████████████████████████████████████████▌                                         | 1/2 [00:04<00:04,  4.67s/it]Batch 1, val - Seg Loss: 0.0598, Heatmap Loss: 1973.4595, Geometric Loss: 0.9596, Total: 2961.0166
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.76s/it]
val Loss: 2998.5115, Seg Loss: 0.0684, Heatmap Loss: 1998.4955, Geometric Loss: 0.8749
=== val Corner Confidence Metrics ===
  avg_peak_value: 2.5122
  avg_peak_to_mean_ratio: 0.1154
  avg_peak_to_second_ratio: 1.1662
  detection_rate: 0.7500
  Overall Confidence Score: 1.1360
val Heatmap Components:
  mse_loss: 13.5814
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 346.8684
  peak_to_second_ratio_loss: 1.0476
  detection_rate_loss: 0.1196
  segmentation_guidance_loss: 0.0477
New best model saved with loss: 2998.5115

Epoch 2/40
----------
train:   0%|                                                                                         | 0/2 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.2054, Heatmap Loss: 2037.8793, Geometric Loss: 0.9155, Total: 3057.7566
train:  50%|████████████████████████████████████████▌                                        | 1/2 [00:06<00:06,  6.13s/it]Batch 1, train - Seg Loss: 0.3283, Heatmap Loss: 1959.1200, Geometric Loss: 0.7578, Total: 2939.6145
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.63s/it]
train Loss: 1293.5897, Seg Loss: 0.1056, Heatmap Loss: 862.1255, Geometric Loss: 0.3698
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.0473
  avg_peak_to_mean_ratio: 0.7495
  avg_peak_to_second_ratio: 0.4372
  detection_rate: 0.4286
  Overall Confidence Score: 0.6656
train Heatmap Components:
  mse_loss: 5.4903
  separation_loss: 428.5714
  peak_separation_loss: 428.5714
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 161.5139
  peak_to_second_ratio_loss: 0.4404
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0457
val:   0%|                                                                                           | 0/2 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.1085, Heatmap Loss: 1423.5980, Geometric Loss: 0.8726, Total: 2136.2034
val:  50%|█████████████████████████████████████████▌                                         | 1/2 [00:04<00:04,  4.94s/it]Batch 1, val - Seg Loss: 0.1317, Heatmap Loss: 1358.9247, Geometric Loss: 0.7718, Total: 2039.1361
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.90s/it]
val Loss: 2087.6697, Seg Loss: 0.1201, Heatmap Loss: 1391.2614, Geometric Loss: 0.8222
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.5649
  avg_peak_to_mean_ratio: 6.2853
  avg_peak_to_second_ratio: 1.0040
  detection_rate: 0.7344
  Overall Confidence Score: 3.3971
val Heatmap Components:
  mse_loss: 25.9723
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 93.3574
  peak_to_second_ratio_loss: 1.0937
  detection_rate_loss: 0.9591
  segmentation_guidance_loss: 0.0008
New best model saved with loss: 2087.6697

Epoch 3/40
----------
train:   0%|                                                                                         | 0/2 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.3677, Heatmap Loss: 1387.0447, Geometric Loss: 0.6881, Total: 2081.4851
train:  50%|████████████████████████████████████████▌                                        | 1/2 [00:05<00:05,  5.90s/it]Batch 1, train - Seg Loss: 0.2656, Heatmap Loss: 1345.8666, Geometric Loss: 1.0027, Total: 2019.8676
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.71s/it]
train Loss: 883.2625, Seg Loss: 0.1430, Heatmap Loss: 588.5651, Geometric Loss: 0.3398
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8193
  avg_peak_to_mean_ratio: 2.2388
  avg_peak_to_second_ratio: 0.4404
  detection_rate: 0.3214
  Overall Confidence Score: 1.2050
train Heatmap Components:
  mse_loss: 8.9949
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 33.8360
  peak_to_second_ratio_loss: 0.4366
  detection_rate_loss: 0.1164
  segmentation_guidance_loss: 0.2124
val:   0%|                                                                                           | 0/2 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.0721, Heatmap Loss: 1402.0876, Geometric Loss: 1.0406, Total: 2104.0359
val:  50%|█████████████████████████████████████████▌                                         | 1/2 [00:04<00:04,  4.94s/it]Batch 1, val - Seg Loss: 0.0578, Heatmap Loss: 1411.8351, Geometric Loss: 1.0875, Total: 2118.6804
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.90s/it]
val Loss: 2111.3582, Seg Loss: 0.0650, Heatmap Loss: 1406.9614, Geometric Loss: 1.0641
=== val Corner Confidence Metrics ===
  avg_peak_value: 11.6248
  avg_peak_to_mean_ratio: 2.7277
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.7500
  Overall Confidence Score: 4.0258
val Heatmap Components:
  mse_loss: 87.5121
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1084
  detection_rate_loss: 1.0041
  segmentation_guidance_loss: 0.3325

  Epoch 2/40
----------
train:   0%|                                                                                         | 0/2 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.2054, Heatmap Loss: 2037.8793, Geometric Loss: 0.9155, Total: 3057.7566
train:  50%|████████████████████████████████████████▌                                        | 1/2 [00:06<00:06,  6.13s/it]Batch 1, train - Seg Loss: 0.3283, Heatmap Loss: 1959.1200, Geometric Loss: 0.7578, Total: 2939.6145
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.63s/it]
train Loss: 1293.5897, Seg Loss: 0.1056, Heatmap Loss: 862.1255, Geometric Loss: 0.3698
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.0473
  avg_peak_to_mean_ratio: 0.7495
  avg_peak_to_second_ratio: 0.4372
  detection_rate: 0.4286
  Overall Confidence Score: 0.6656
train Heatmap Components:
  mse_loss: 5.4903
  separation_loss: 428.5714
  peak_separation_loss: 428.5714
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 161.5139
  peak_to_second_ratio_loss: 0.4404
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0457
val:   0%|                                                                                           | 0/2 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.1085, Heatmap Loss: 1423.5980, Geometric Loss: 0.8726, Total: 2136.2034
val:  50%|█████████████████████████████████████████▌                                         | 1/2 [00:04<00:04,  4.94s/it]Batch 1, val - Seg Loss: 0.1317, Heatmap Loss: 1358.9247, Geometric Loss: 0.7718, Total: 2039.1361
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.90s/it]
val Loss: 2087.6697, Seg Loss: 0.1201, Heatmap Loss: 1391.2614, Geometric Loss: 0.8222
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.5649
  avg_peak_to_mean_ratio: 6.2853
  avg_peak_to_second_ratio: 1.0040
  detection_rate: 0.7344
  Overall Confidence Score: 3.3971
val Heatmap Components:
  mse_loss: 25.9723
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 93.3574
  peak_to_second_ratio_loss: 1.0937
  detection_rate_loss: 0.9591
  segmentation_guidance_loss: 0.0008
New best model saved with loss: 2087.6697

Epoch 3/40
----------
train:   0%|                                                                                         | 0/2 [00:00<?, ?it/s]Batch 0, train - Seg Loss: 0.3677, Heatmap Loss: 1387.0447, Geometric Loss: 0.6881, Total: 2081.4851
train:  50%|████████████████████████████████████████▌                                        | 1/2 [00:05<00:05,  5.90s/it]Batch 1, train - Seg Loss: 0.2656, Heatmap Loss: 1345.8666, Geometric Loss: 1.0027, Total: 2019.8676
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.71s/it]
train Loss: 883.2625, Seg Loss: 0.1430, Heatmap Loss: 588.5651, Geometric Loss: 0.3398
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8193
  avg_peak_to_mean_ratio: 2.2388
  avg_peak_to_second_ratio: 0.4404
  detection_rate: 0.3214
  Overall Confidence Score: 1.2050
train Heatmap Components:
  mse_loss: 8.9949
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 33.8360
  peak_to_second_ratio_loss: 0.4366
  detection_rate_loss: 0.1164
  segmentation_guidance_loss: 0.2124
val:   0%|                                                                                           | 0/2 [00:00<?, ?it/s]Batch 0, val - Seg Loss: 0.0721, Heatmap Loss: 1402.0876, Geometric Loss: 1.0406, Total: 2104.0359
val:  50%|█████████████████████████████████████████▌                                         | 1/2 [00:04<00:04,  4.94s/it]Batch 1, val - Seg Loss: 0.0578, Heatmap Loss: 1411.8351, Geometric Loss: 1.0875, Total: 2118.6804
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.90s/it]
val Loss: 2111.3582, Seg Loss: 0.0650, Heatmap Loss: 1406.9614, Geometric Loss: 1.0641
=== val Corner Confidence Metrics ===
  avg_peak_value: 11.6248
  avg_peak_to_mean_ratio: 2.7277
  avg_peak_to_second_ratio: 1.0006
  detection_rate: 0.7500
  Overall Confidence Score: 4.0258
val Heatmap Components:
  mse_loss: 87.5121
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1084
  detection_rate_loss: 1.0041
  segmentation_guidance_loss: 0.3325

  Epoch 4/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.65s/it] 
train Loss: 878.4839, Seg Loss: 0.1714, Heatmap Loss: 585.3609, Geometric Loss: 0.3387
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.1378
  avg_peak_to_mean_ratio: 1.5089
  avg_peak_to_second_ratio: 0.4790
  detection_rate: 0.3214
  Overall Confidence Score: 1.3618
train Heatmap Components:
  mse_loss: 20.9231
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4670
  detection_rate_loss: 0.2890
  segmentation_guidance_loss: 0.3132
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.79s/it] 
val Loss: 2091.9436, Seg Loss: 0.0442, Heatmap Loss: 1393.9969, Geometric Loss: 1.1301
=== val Corner Confidence Metrics ===
  avg_peak_value: 11.7194
  avg_peak_to_mean_ratio: 2.5173
  avg_peak_to_second_ratio: 1.0134
  detection_rate: 0.7500
  Overall Confidence Score: 4.0000
val Heatmap Components:
  mse_loss: 79.5422
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1051
  detection_rate_loss: 0.3983
  segmentation_guidance_loss: 0.2178

Epoch 5/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:12<00:00,  6.40s/it] 
train Loss: 1300.0701, Seg Loss: 0.2434, Heatmap Loss: 866.2725, Geometric Loss: 0.5225
=== train Corner Confidence Metrics ===
  avg_peak_value: 5.0790
  avg_peak_to_mean_ratio: 2.3903
  avg_peak_to_second_ratio: 0.8052
  detection_rate: 0.5893
  Overall Confidence Score: 2.2160
train Heatmap Components:
  mse_loss: 24.0054
  separation_loss: 642.8571
  peak_separation_loss: 0.0000
  edge_suppression_loss: 642.8571
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.6076
  detection_rate_loss: 0.0412
  segmentation_guidance_loss: 0.1471
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.73s/it] 
val Loss: 2028.8273, Seg Loss: 0.1646, Heatmap Loss: 1351.9429, Geometric Loss: 0.9354
=== val Corner Confidence Metrics ===
  avg_peak_value: 9.3386
  avg_peak_to_mean_ratio: 2.8795
  avg_peak_to_second_ratio: 1.0424
  detection_rate: 0.7656
  Overall Confidence Score: 3.5065
val Heatmap Components:
  mse_loss: 39.7637
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0608
  detection_rate_loss: 0.1554
  segmentation_guidance_loss: 0.3281
New best model saved with loss: 2028.8273
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0608
  detection_rate_loss: 0.1554
  segmentation_guidance_loss: 0.3281
New best model saved with loss: 2028.8273
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0608
  detection_rate_loss: 0.1554
  segmentation_guidance_loss: 0.3281
New best model saved with loss: 2028.8273
  detection_rate_loss: 0.1554
  segmentation_guidance_loss: 0.3281
New best model saved with loss: 2028.8273
New best model saved with loss: 2028.8273

Epoch 6/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.17s/it] 
train Loss: 998.3894, Seg Loss: 0.1901, Heatmap Loss: 665.1692, Geometric Loss: 0.5569
train Loss: 998.3894, Seg Loss: 0.1901, Heatmap Loss: 665.1692, Geometric Loss: 0.5569
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0454
  avg_peak_to_mean_ratio: 1.2648
  avg_peak_to_second_ratio: 0.5165
  detection_rate: 0.4821
  Overall Confidence Score: 1.3272
train Heatmap Components:
  mse_loss: 10.1990
  separation_loss: 500.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4935
  detection_rate_loss: 0.0044
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.84s/it] 
val Loss: 2015.1646, Seg Loss: 0.1504, Heatmap Loss: 1342.8784, Geometric Loss: 0.8708
=== val Corner Confidence Metrics ===
  avg_peak_value: 7.1610
  avg_peak_to_mean_ratio: 7.9593
  avg_peak_to_second_ratio: 1.0414
  detection_rate: 0.9531
  Overall Confidence Score: 4.2787
val Heatmap Components:
  mse_loss: 20.8839
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 23.0125
  peak_to_second_ratio_loss: 0.9992
  detection_rate_loss: 0.0101
  segmentation_guidance_loss: 0.4152
New best model saved with loss: 2015.1646

Epoch 7/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.61s/it] 
train Loss: 580.0558, Seg Loss: 0.1250, Heatmap Loss: 386.4802, Geometric Loss: 0.2632
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4195
  avg_peak_to_mean_ratio: 0.4807
  avg_peak_to_second_ratio: 0.2941
  detection_rate: 0.2679
  Overall Confidence Score: 0.6155
train Heatmap Components:
  mse_loss: 3.2229
  separation_loss: 285.7143
  peak_separation_loss: 0.0000
  edge_suppression_loss: 285.7143
  peak_enhancement_loss: 17.9433
  peak_to_second_ratio_loss: 0.2853
  detection_rate_loss: 0.0006
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.71s/it] 
val Loss: 2006.8009, Seg Loss: 0.1583, Heatmap Loss: 1337.2946, Geometric Loss: 0.8760
=== val Corner Confidence Metrics ===
  avg_peak_value: 7.6596
  avg_peak_to_mean_ratio: 3.3229
  avg_peak_to_second_ratio: 1.0477
  detection_rate: 0.9375
  Overall Confidence Score: 3.2419
val Heatmap Components:
  mse_loss: 24.5252
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 3.8403
  peak_to_second_ratio_loss: 1.0603
  detection_rate_loss: 0.0039
  segmentation_guidance_loss: 0.2151
New best model saved with loss: 2006.8009

Epoch 8/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:15<00:00,  7.65s/it] 
train Loss: 1559.2670, Seg Loss: 0.2590, Heatmap Loss: 1038.9882, Geometric Loss: 0.6572
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.6048
  avg_peak_to_mean_ratio: 2.5538
  avg_peak_to_second_ratio: 0.8043
  detection_rate: 0.7679
  Overall Confidence Score: 1.9327
train Heatmap Components:
  mse_loss: 9.2629
  separation_loss: 785.7143
  peak_separation_loss: 0.0000
  edge_suppression_loss: 785.7143
  peak_enhancement_loss: 0.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 785.7143
  peak_enhancement_loss: 0.0000
  edge_suppression_loss: 785.7143
  peak_enhancement_loss: 0.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.8076
  detection_rate_loss: 0.0039
  segmentation_guidance_loss: 0.1900
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.81s/it] 
val Loss: 1993.6102, Seg Loss: 0.2163, Heatmap Loss: 1328.4395, Geometric Loss: 0.9183
=== val Corner Confidence Metrics ===
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.81s/it] 
val Loss: 1993.6102, Seg Loss: 0.2163, Heatmap Loss: 1328.4395, Geometric Loss: 0.9183
=== val Corner Confidence Metrics ===
  avg_peak_value: 7.7129
  avg_peak_to_mean_ratio: 3.0546
=== val Corner Confidence Metrics ===
  avg_peak_value: 7.7129
  avg_peak_to_mean_ratio: 3.0546
  avg_peak_value: 7.7129
  avg_peak_to_mean_ratio: 3.0546
  avg_peak_to_mean_ratio: 3.0546
  avg_peak_to_second_ratio: 1.0280
  detection_rate: 0.9844
  Overall Confidence Score: 3.1950
val Heatmap Components:
  mse_loss: 17.7446
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0529
  detection_rate_loss: 0.0033
  segmentation_guidance_loss: 0.1391
New best model saved with loss: 1993.6102

Epoch 9/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.10s/it] 
train Loss: 991.6570, Seg Loss: 0.1590, Heatmap Loss: 660.7597, Geometric Loss: 0.4481
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2278
  avg_peak_to_mean_ratio: 2.2857
  avg_peak_to_second_ratio: 0.5082
  detection_rate: 0.4643
  Overall Confidence Score: 1.3715
train Heatmap Components:
  mse_loss: 5.5015
  separation_loss: 500.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5245
  detection_rate_loss: 0.0017
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.81s/it] 
val Loss: 1985.8196, Seg Loss: 0.2117, Heatmap Loss: 1323.1438, Geometric Loss: 1.1152
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.5723
  avg_peak_to_mean_ratio: 2.8434
  avg_peak_to_second_ratio: 1.0112
  detection_rate: 0.9531
  Overall Confidence Score: 2.8450
val Heatmap Components:
  mse_loss: 12.2540
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0631
  detection_rate_loss: 0.0117
  segmentation_guidance_loss: 0.1654
New best model saved with loss: 1985.8196

Epoch 10/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:11<00:00,  5.64s/it] 
train Loss: 1142.2552, Seg Loss: 0.1412, Heatmap Loss: 761.0959, Geometric Loss: 0.5877
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9530
  avg_peak_to_mean_ratio: 1.3153
  avg_peak_to_second_ratio: 0.5766
  detection_rate: 0.5357
  Overall Confidence Score: 1.3451
train Heatmap Components:
  mse_loss: 6.1467
  separation_loss: 571.4286
  peak_separation_loss: 0.0000
  edge_suppression_loss: 571.4286
  peak_enhancement_loss: 11.7119
  peak_to_second_ratio_loss: 0.6130
  detection_rate_loss: 0.0036
  segmentation_guidance_loss: 0.0773
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.81s/it] 
val Loss: 2738.4586, Seg Loss: 0.2065, Heatmap Loss: 1825.0139, Geometric Loss: 0.9141
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.9843
  avg_peak_to_mean_ratio: 2.0569
  avg_peak_to_second_ratio: 1.0098
  detection_rate: 0.9688
  Overall Confidence Score: 2.7549
val Heatmap Components:
  mse_loss: 14.0698
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0687
  detection_rate_loss: 0.0040
  segmentation_guidance_loss: 0.2253

Epoch 11/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.67s/it] 
train Loss: 1173.0577, Seg Loss: 0.0855, Heatmap Loss: 781.7388, Geometric Loss: 0.4550
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6626
  avg_peak_to_mean_ratio: 0.9834
  avg_peak_to_second_ratio: 0.4324
  detection_rate: 0.4286
  Overall Confidence Score: 1.1268
train Heatmap Components:
  mse_loss: 5.7170
  separation_loss: 428.5714
  peak_separation_loss: 428.5714
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4593
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.63s/it] 
val Loss: 2741.7462, Seg Loss: 0.1481, Heatmap Loss: 1827.2191, Geometric Loss: 0.9620
=== val Corner Confidence Metrics ===
  avg_peak_value: 7.2208
  avg_peak_to_mean_ratio: 2.0122
  avg_peak_to_second_ratio: 1.0115
  detection_rate: 1.0000
  Overall Confidence Score: 2.8111
val Heatmap Components:
  mse_loss: 16.3391
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0618
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2623

Epoch 12/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  5.00s/it] 
train Loss: 1369.4440, Seg Loss: 0.1849, Heatmap Loss: 912.5519, Geometric Loss: 0.5390
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.2731
  avg_peak_to_mean_ratio: 1.0177
  avg_peak_to_second_ratio: 0.5051
  detection_rate: 0.5000
  Overall Confidence Score: 1.3240
train Heatmap Components:
  mse_loss: 7.0172
  separation_loss: 500.0000
  peak_separation_loss: 500.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5333
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2018
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.74s/it] 
val Loss: 2737.4568, Seg Loss: 0.1135, Heatmap Loss: 1824.3700, Geometric Loss: 0.9854
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.7748
  avg_peak_to_mean_ratio: 2.4447
  avg_peak_to_second_ratio: 1.0182
  detection_rate: 1.0000
  Overall Confidence Score: 2.8094
val Heatmap Components:
  mse_loss: 13.7818
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0399
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1892

Epoch 13/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.54s/it] 
train Loss: 782.0741, Seg Loss: 0.1108, Heatmap Loss: 521.1430, Geometric Loss: 0.3110
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.6701
  avg_peak_to_mean_ratio: 0.5921
  avg_peak_to_second_ratio: 0.2876
  detection_rate: 0.2857
  Overall Confidence Score: 0.7089
train Heatmap Components:
  mse_loss: 3.3957
  separation_loss: 285.7143
  peak_separation_loss: 285.7143
  edge_suppression_loss: 285.7143
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.3094
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.3673
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.76s/it] 
val Loss: 2742.1533, Seg Loss: 0.0747, Heatmap Loss: 1827.5554, Geometric Loss: 0.9319
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.0294
  avg_peak_to_mean_ratio: 2.6081
  avg_peak_to_second_ratio: 1.0136
  detection_rate: 1.0000
  Overall Confidence Score: 2.6628
val Heatmap Components:
  mse_loss: 9.1827
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 15.5722
  peak_to_second_ratio_loss: 1.0531
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0556

Epoch 14/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.59s/it] 
train Loss: 1010.2387, Seg Loss: 0.0397, Heatmap Loss: 673.2654, Geometric Loss: 0.3761
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1826
  avg_peak_to_mean_ratio: 1.2060
  avg_peak_to_second_ratio: 0.4320
  detection_rate: 0.4286
  Overall Confidence Score: 1.0623
train Heatmap Components:
  mse_loss: 3.2426
  separation_loss: 428.5714
  peak_separation_loss: 214.2857
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 2.2469
  peak_to_second_ratio_loss: 0.4614
  detection_rate_loss: 0.0000
  peak_to_second_ratio_loss: 0.4614
  peak_to_second_ratio_loss: 0.4614
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.74s/it] 
val Loss: 2730.0399, Seg Loss: 0.0393, Heatmap Loss: 1819.5554, Geometric Loss: 0.8342
val Loss: 2730.0399, Seg Loss: 0.0393, Heatmap Loss: 1819.5554, Geometric Loss: 0.8342
=== val Corner Confidence Metrics ===
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.5737
  avg_peak_to_mean_ratio: 5.6241
  avg_peak_to_second_ratio: 1.0100
  detection_rate: 1.0000
  Overall Confidence Score: 3.3019
val Heatmap Components:
  mse_loss: 7.2541
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 3.2338
  peak_to_second_ratio_loss: 1.0684
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000

Epoch 15/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.37s/it] 
train Loss: 1363.4864, Seg Loss: 0.1256, Heatmap Loss: 908.6580, Geometric Loss: 0.4674
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3396
  avg_peak_to_mean_ratio: 1.3675
  avg_peak_to_second_ratio: 0.5064
  detection_rate: 0.5000
  Overall Confidence Score: 1.1784
train Heatmap Components:
  mse_loss: 3.3723
  separation_loss: 500.0000
  peak_separation_loss: 500.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5286
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.73s/it] 
val Loss: 2727.8203, Seg Loss: 0.0411, Heatmap Loss: 1818.0668, Geometric Loss: 0.8487
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.4243
  avg_peak_to_mean_ratio: 4.8720
  avg_peak_to_second_ratio: 1.0107
  detection_rate: 1.0000
  Overall Confidence Score: 3.0768
val Heatmap Components:
  mse_loss: 6.9836
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0649
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.4340

 Epoch 16/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:13<00:00,  6.96s/it] 
train Loss: 1947.2136, Seg Loss: 0.1409, Heatmap Loss: 1297.7247, Geometric Loss: 0.6069
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.3379
  avg_peak_to_mean_ratio: 5.3754
  avg_peak_to_second_ratio: 0.7226
  detection_rate: 0.7143
  Overall Confidence Score: 2.5376
train Heatmap Components:
  mse_loss: 4.3697
  separation_loss: 714.2857
  peak_separation_loss: 714.2857
  edge_suppression_loss: 714.2857
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.7575
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0662
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.83s/it] 
val Loss: 2726.7092, Seg Loss: 0.0421, Heatmap Loss: 1817.3266, Geometric Loss: 0.8463
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.3173
  avg_peak_to_mean_ratio: 5.7616
  avg_peak_to_second_ratio: 1.0107
  detection_rate: 1.0000
  Overall Confidence Score: 3.2724
val Heatmap Components:
  mse_loss: 6.4890
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0650
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1874

Epoch 17/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.04s/it] 
train Loss: 1362.2368, Seg Loss: 0.1028, Heatmap Loss: 907.8969, Geometric Loss: 0.3608
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2283
  avg_peak_to_mean_ratio: 1.3641
  avg_peak_to_second_ratio: 0.5081
  detection_rate: 0.5000
  Overall Confidence Score: 1.1502
train Heatmap Components:
  mse_loss: 2.6889
  separation_loss: 500.0000
  peak_separation_loss: 500.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5208
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.88s/it] 
val Loss: 2723.2053, Seg Loss: 0.0420, Heatmap Loss: 1814.9740, Geometric Loss: 0.8779
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.5784
  avg_peak_to_mean_ratio: 10.0069
  avg_peak_to_second_ratio: 1.0130
  detection_rate: 1.0000
  Overall Confidence Score: 4.1496
val Heatmap Components:
  mse_loss: 4.3154
  separation_loss: 1000.0000
  peak_separation_loss: 1000.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0549
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1096

Epoch 18/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:13<00:00,  6.97s/it] 
train Loss: 1945.3523, Seg Loss: 0.1360, Heatmap Loss: 1296.4784, Geometric Loss: 0.6231
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.0635
  avg_peak_to_mean_ratio: 2.3987
  avg_peak_to_second_ratio: 0.7240
  detection_rate: 0.7143
  Overall Confidence Score: 1.7251
train Heatmap Components:
  mse_loss: 3.2431
  separation_loss: 714.2857
  peak_separation_loss: 714.2857
  edge_suppression_loss: 714.2857
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.7521
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.93s/it] 
val Loss: 2347.4339, Seg Loss: 0.0460, Heatmap Loss: 1564.4880, Geometric Loss: 0.8200
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.2762
  avg_peak_to_mean_ratio: 10.0151
  avg_peak_to_second_ratio: 1.0133
  detection_rate: 1.0000
  Overall Confidence Score: 4.0761
val Heatmap Components:
  mse_loss: 3.6563
  separation_loss: 1000.0000
  peak_separation_loss: 500.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0539
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2923

Epoch 19/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:13<00:00,  6.94s/it] 
train Loss: 1732.8669, Seg Loss: 0.1774, Heatmap Loss: 1154.8152, Geometric Loss: 0.5833
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8006
  avg_peak_to_mean_ratio: 5.6276
  avg_peak_to_second_ratio: 0.7258
  detection_rate: 0.7143
  Overall Confidence Score: 2.4671
train Heatmap Components:
  mse_loss: 2.4329
  separation_loss: 714.2857
  peak_separation_loss: 428.5714
  edge_suppression_loss: 714.2857
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.7454
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 2.0715
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.84s/it] 
val Loss: 1973.3640, Seg Loss: 0.0444, Heatmap Loss: 1315.1124, Geometric Loss: 0.8137
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.4560
  avg_peak_to_mean_ratio: 48.4515
  avg_peak_to_second_ratio: 1.0261
  detection_rate: 1.0000
  Overall Confidence Score: 13.4834
val Heatmap Components:
  mse_loss: 4.5411
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0044
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.5270
New best model saved with loss: 1973.3640

Epoch 20/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.53s/it] 
train Loss: 852.6222, Seg Loss: 0.1314, Heatmap Loss: 568.1287, Geometric Loss: 0.3722
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4272
  avg_peak_to_mean_ratio: 4.1772
  avg_peak_to_second_ratio: 0.4345
  detection_rate: 0.4286
  Overall Confidence Score: 1.6169
train Heatmap Components:
  mse_loss: 1.4713
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 9.5452
  peak_to_second_ratio_loss: 0.4508
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2336
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.82s/it] 
val Loss: 1985.5858, Seg Loss: 0.0671, Heatmap Loss: 1323.1453, Geometric Loss: 1.0009
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.2980
  avg_peak_to_mean_ratio: 1.9996
  avg_peak_to_second_ratio: 1.0180
  detection_rate: 1.0000
  Overall Confidence Score: 1.8289
val Heatmap Components:
  mse_loss: 7.5910
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 8.3581
  peak_to_second_ratio_loss: 1.0395
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.9806

Epoch 21/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.58s/it] 
train Loss: 858.3089, Seg Loss: 0.1134, Heatmap Loss: 571.8531, Geometric Loss: 0.5198
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.4973
  avg_peak_to_mean_ratio: 2.0989
  avg_peak_to_second_ratio: 0.4337
  detection_rate: 0.4286
  Overall Confidence Score: 1.1146
train Heatmap Components:
  mse_loss: 1.7400
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 15.7180
  peak_to_second_ratio_loss: 0.4541
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.5705
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.69s/it] 
val Loss: 2003.6202, Seg Loss: 0.1113, Heatmap Loss: 1335.2076, Geometric Loss: 0.8719
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.5536
  avg_peak_to_mean_ratio: 1.7218
  avg_peak_to_second_ratio: 1.0062
  detection_rate: 1.0000
  Overall Confidence Score: 1.8204
val Heatmap Components:
  mse_loss: 9.9058
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 27.3375
  peak_to_second_ratio_loss: 1.0847
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.7862

Epoch 22/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.71s/it] 
train Loss: 851.0486, Seg Loss: 0.1417, Heatmap Loss: 567.0554, Geometric Loss: 0.4048
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.7259
  avg_peak_to_mean_ratio: 5.1137
  avg_peak_to_second_ratio: 0.4323
  detection_rate: 0.4286
  Overall Confidence Score: 1.9251
train Heatmap Components:
  mse_loss: 1.9347
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 5.5023
  peak_to_second_ratio_loss: 0.4601
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.6261
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.82s/it] 
val Loss: 1983.4581, Seg Loss: 0.1289, Heatmap Loss: 1321.7415, Geometric Loss: 0.8963
=== val Corner Confidence Metrics ===
  avg_peak_value: 3.8435
  avg_peak_to_mean_ratio: 2.8046
  avg_peak_to_second_ratio: 1.0063
  detection_rate: 1.0000
  Overall Confidence Score: 2.1636
val Heatmap Components:
  mse_loss: 8.7138
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 3.9054
  peak_to_second_ratio_loss: 1.0839
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2359

Epoch 23/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.57s/it] 
train Loss: 850.9339, Seg Loss: 0.1258, Heatmap Loss: 566.9761, Geometric Loss: 0.4298
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8772
  avg_peak_to_mean_ratio: 13.8993
  avg_peak_to_second_ratio: 0.4347
  detection_rate: 0.4286
  Overall Confidence Score: 4.1599
train Heatmap Components:
  mse_loss: 2.1826
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 6.2933
  peak_to_second_ratio_loss: 0.4504
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.84s/it] 
val Loss: 1983.5482, Seg Loss: 0.1743, Heatmap Loss: 1321.6678, Geometric Loss: 1.0903
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.2379
  avg_peak_to_mean_ratio: 3.5879
  avg_peak_to_second_ratio: 1.0032
  detection_rate: 1.0000
  Overall Confidence Score: 2.4573
val Heatmap Components:
  mse_loss: 9.3538
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 2.5697
  peak_to_second_ratio_loss: 1.0970
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0594

Epoch 24/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.51s/it] 
train Loss: 847.1773, Seg Loss: 0.0969, Heatmap Loss: 564.4200, Geometric Loss: 0.5628
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.2024
  avg_peak_to_mean_ratio: 4.3818
  avg_peak_to_second_ratio: 0.4313
  detection_rate: 0.4286
  Overall Confidence Score: 1.8610
train Heatmap Components:
  mse_loss: 2.4741
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4644
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1595
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.85s/it] 
val Loss: 1982.6580, Seg Loss: 0.1724, Heatmap Loss: 1320.9113, Geometric Loss: 1.3983
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.3590
  avg_peak_to_mean_ratio: 14.4185
  avg_peak_to_second_ratio: 1.0025
  detection_rate: 1.0000
  Overall Confidence Score: 5.1950
val Heatmap Components:
  mse_loss: 8.2997
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0999
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 1.6123

Epoch 25/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.50s/it] 
train Loss: 849.8656, Seg Loss: 0.1676, Heatmap Loss: 566.2162, Geometric Loss: 0.4672
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.1907
  avg_peak_to_mean_ratio: 2.8144
  avg_peak_to_second_ratio: 0.4318
  detection_rate: 0.4286
  Overall Confidence Score: 1.4664
train Heatmap Components:
  mse_loss: 2.8037
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 2.5483
  peak_to_second_ratio_loss: 0.4622
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.3738
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.77s/it] 
val Loss: 1980.5869, Seg Loss: 0.1558, Heatmap Loss: 1319.5986, Geometric Loss: 1.2914
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.8308
  avg_peak_to_mean_ratio: 4.5127
  avg_peak_to_second_ratio: 1.0030
  detection_rate: 1.0000
  Overall Confidence Score: 2.8366
val Heatmap Components:
  mse_loss: 8.2172
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0980
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.4015

Epoch 26/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.37s/it] 
train Loss: 989.9165, Seg Loss: 0.1781, Heatmap Loss: 659.5320, Geometric Loss: 0.5505
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.8557
  avg_peak_to_mean_ratio: 5.5383
  avg_peak_to_second_ratio: 0.5034
  detection_rate: 0.5000
  Overall Confidence Score: 2.3493
train Heatmap Components:
  mse_loss: 3.5368
  separation_loss: 500.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5409
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.5860
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.68s/it] 
val Loss: 1990.1623, Seg Loss: 0.1751, Heatmap Loss: 1326.1046, Geometric Loss: 1.0380
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.6792
  avg_peak_to_mean_ratio: 4.2974
  avg_peak_to_second_ratio: 1.0026
  detection_rate: 1.0000
  Overall Confidence Score: 2.9948
val Heatmap Components:
  mse_loss: 14.5021
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0995
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.6079

Epoch 27/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:11<00:00,  5.68s/it] 
train Loss: 1131.9528, Seg Loss: 0.1658, Heatmap Loss: 754.2286, Geometric Loss: 0.5551
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9213
  avg_peak_to_mean_ratio: 2.2374
  avg_peak_to_second_ratio: 0.5729
  detection_rate: 0.5714
  Overall Confidence Score: 1.5758
train Heatmap Components:
  mse_loss: 4.5827
  separation_loss: 571.4286
  peak_separation_loss: 0.0000
  edge_suppression_loss: 571.4286
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.6284
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.5045
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.80s/it] 
val Loss: 1990.3811, Seg Loss: 0.1638, Heatmap Loss: 1326.3020, Geometric Loss: 0.9553
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.9718
  avg_peak_to_mean_ratio: 3.2451
  avg_peak_to_second_ratio: 1.0023
  detection_rate: 1.0000
  Overall Confidence Score: 2.8048
val Heatmap Components:
  mse_loss: 14.6661
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1010
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.6256

Epoch 28/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.05s/it] 
train Loss: 707.3815, Seg Loss: 0.1008, Heatmap Loss: 471.3039, Geometric Loss: 0.4060
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.0379
  avg_peak_to_mean_ratio: 1.3547
  avg_peak_to_second_ratio: 0.3585
  detection_rate: 0.3571
  Overall Confidence Score: 1.0271
train Heatmap Components:
  mse_loss: 3.0790
  separation_loss: 357.1429
  peak_separation_loss: 0.0000
  edge_suppression_loss: 357.1429
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.3909
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0305
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.71s/it] 
val Loss: 1988.0411, Seg Loss: 0.1391, Heatmap Loss: 1324.8365, Geometric Loss: 0.8090
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.1053
  avg_peak_to_mean_ratio: 2.9583
  avg_peak_to_second_ratio: 1.0026
  detection_rate: 1.0000
  Overall Confidence Score: 2.7666
val Heatmap Components:
  mse_loss: 13.4244
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0998
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.4143

Epoch 29/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:13<00:00,  6.68s/it] 
train Loss: 1415.0313, Seg Loss: 0.2709, Heatmap Loss: 942.7430, Geometric Loss: 0.8075
=== train Corner Confidence Metrics ===
  avg_peak_value: 3.8877
  avg_peak_to_mean_ratio: 3.3589
  avg_peak_to_second_ratio: 0.7172
  detection_rate: 0.7143
  Overall Confidence Score: 2.1695
train Heatmap Components:
  mse_loss: 6.1357
  separation_loss: 714.2857
  peak_separation_loss: 0.0000
  edge_suppression_loss: 714.2857
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.7807
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2291
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.80s/it] 
val Loss: 1986.7378, Seg Loss: 0.1557, Heatmap Loss: 1323.9332, Geometric Loss: 0.8527
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.1177
  avg_peak_to_mean_ratio: 2.8463
  avg_peak_to_second_ratio: 1.0019
  detection_rate: 1.0000
  Overall Confidence Score: 2.7415
val Heatmap Components:
  mse_loss: 12.5369
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1028
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.3685

Epoch 30/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:08<00:00,  4.42s/it] 
train Loss: 848.9041, Seg Loss: 0.1133, Heatmap Loss: 565.6282, Geometric Loss: 0.4355
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.6009
  avg_peak_to_mean_ratio: 3.0834
  avg_peak_to_second_ratio: 0.4309
  detection_rate: 0.4286
  Overall Confidence Score: 1.6359
train Heatmap Components:
  mse_loss: 3.8251
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4660
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.87s/it] 
val Loss: 1988.3214, Seg Loss: 0.1604, Heatmap Loss: 1324.8901, Geometric Loss: 1.0323
=== val Corner Confidence Metrics ===
  avg_peak_value: 6.0686
  avg_peak_to_mean_ratio: 3.4386
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 2.8772
val Heatmap Components:
  mse_loss: 13.5617
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1034
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2941

Epoch 31/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.77s/it] 
train Loss: 849.1200, Seg Loss: 0.1438, Heatmap Loss: 565.7468, Geometric Loss: 0.4450
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3330
  avg_peak_to_mean_ratio: 1.2473
  avg_peak_to_second_ratio: 0.4298
  detection_rate: 0.4286
  Overall Confidence Score: 1.1097
train Heatmap Components:
  mse_loss: 3.8075
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4707
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0890
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.88s/it] 
val Loss: 1984.3005, Seg Loss: 0.1590, Heatmap Loss: 1322.3115, Geometric Loss: 0.8428
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.9415
  avg_peak_to_mean_ratio: 2.9766
  avg_peak_to_second_ratio: 1.0021
  detection_rate: 1.0000
  Overall Confidence Score: 2.7301
val Heatmap Components:
  mse_loss: 11.0382
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1017
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2569

Epoch 32/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.62s/it] 
train Loss: 849.1935, Seg Loss: 0.2086, Heatmap Loss: 565.8716, Geometric Loss: 0.2219
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4784
  avg_peak_to_mean_ratio: 6.8497
  avg_peak_to_second_ratio: 0.4303
  detection_rate: 0.4286
  Overall Confidence Score: 2.5468
train Heatmap Components:
  mse_loss: 3.7277
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4684
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.3166
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.77s/it] 
val Loss: 1982.6263, Seg Loss: 0.1538, Heatmap Loss: 1321.0895, Geometric Loss: 1.0478
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.6144
  avg_peak_to_mean_ratio: 3.0527
  avg_peak_to_second_ratio: 1.0014
  detection_rate: 1.0000
  Overall Confidence Score: 2.6671
val Heatmap Components:
  mse_loss: 9.8115
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1048
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2296

Epoch 33/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.11s/it] 
train Loss: 990.4316, Seg Loss: 0.2137, Heatmap Loss: 659.8991, Geometric Loss: 0.4615
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.9306
  avg_peak_to_mean_ratio: 1.4935
  avg_peak_to_second_ratio: 0.5017
  detection_rate: 0.5000
  Overall Confidence Score: 1.3564
train Heatmap Components:
  mse_loss: 4.2646
  separation_loss: 500.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5482
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1523
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.80s/it] 
val Loss: 1983.8831, Seg Loss: 0.1696, Heatmap Loss: 1321.9625, Geometric Loss: 0.9622
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.6073
  avg_peak_to_mean_ratio: 2.7789
  avg_peak_to_second_ratio: 1.0020
  detection_rate: 1.0000
  Overall Confidence Score: 2.5971
val Heatmap Components:
  mse_loss: 10.6783
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1023
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2614

Epoch 34/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.64s/it] 
train Loss: 848.8857, Seg Loss: 0.1399, Heatmap Loss: 565.5940, Geometric Loss: 0.4434
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4391
  avg_peak_to_mean_ratio: 1.3619
  avg_peak_to_second_ratio: 0.4300
  detection_rate: 0.4286
  Overall Confidence Score: 1.1649
train Heatmap Components:
  mse_loss: 3.6158
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4697
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1383
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.70s/it] 
val Loss: 1981.4312, Seg Loss: 0.1703, Heatmap Loss: 1320.3419, Geometric Loss: 0.9353
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.3312
  avg_peak_to_mean_ratio: 3.0924
  avg_peak_to_second_ratio: 1.0034
  detection_rate: 1.0000
  Overall Confidence Score: 2.6067
val Heatmap Components:
  mse_loss: 9.2812
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0965
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0953

Epoch 35/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.54s/it] 
train Loss: 848.7450, Seg Loss: 0.2070, Heatmap Loss: 565.4588, Geometric Loss: 0.4372
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.3507
  avg_peak_to_mean_ratio: 1.2928
  avg_peak_to_second_ratio: 0.4299
  detection_rate: 0.4286
  Overall Confidence Score: 1.1255
train Heatmap Components:
  mse_loss: 3.3841
  separation_loss: 428.5714
  peak_separation_loss: 0.0000
  edge_suppression_loss: 428.5714
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.4704
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2274
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.72s/it] 
val Loss: 1982.3849, Seg Loss: 0.1760, Heatmap Loss: 1320.9409, Geometric Loss: 0.9969
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.2169
  avg_peak_to_mean_ratio: 2.8146
  avg_peak_to_second_ratio: 1.0043
  detection_rate: 1.0000
  Overall Confidence Score: 2.5089
val Heatmap Components:
  mse_loss: 9.7315
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0926
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2836

Epoch 36/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.98s/it] 
train Loss: 706.8647, Seg Loss: 0.1228, Heatmap Loss: 470.9868, Geometric Loss: 0.3271
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.8095
  avg_peak_to_mean_ratio: 1.1475
  avg_peak_to_second_ratio: 0.3579
  detection_rate: 0.3571
  Overall Confidence Score: 0.9180
train Heatmap Components:
  mse_loss: 2.7682
  separation_loss: 357.1429
  peak_separation_loss: 0.0000
  edge_suppression_loss: 357.1429
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.3933
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.65s/it] 
val Loss: 1980.8590, Seg Loss: 0.1956, Heatmap Loss: 1319.9186, Geometric Loss: 0.9818
=== val Corner Confidence Metrics ===
  avg_peak_value: 5.0033
  avg_peak_to_mean_ratio: 2.6609
  avg_peak_to_second_ratio: 1.0017
  detection_rate: 1.0000
  Overall Confidence Score: 2.4165
val Heatmap Components:
  mse_loss: 8.7666
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.1036
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.1159

Epoch 37/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.97s/it] 
train Loss: 989.2270, Seg Loss: 0.1437, Heatmap Loss: 659.1655, Geometric Loss: 0.4187
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.4862
  avg_peak_to_mean_ratio: 1.6320
  avg_peak_to_second_ratio: 0.5020
  detection_rate: 0.5000
  Overall Confidence Score: 1.2801
train Heatmap Components:
  mse_loss: 3.7001
  separation_loss: 500.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5465
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.69s/it] 
val Loss: 1979.3792, Seg Loss: 0.1637, Heatmap Loss: 1318.9470, Geometric Loss: 0.9939
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.6950
  avg_peak_to_mean_ratio: 2.7686
  avg_peak_to_second_ratio: 1.0028
  detection_rate: 1.0000
  Overall Confidence Score: 2.3666
val Heatmap Components:
  mse_loss: 7.9054
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0989
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0522

Epoch 38/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:10<00:00,  5.04s/it] 
train Loss: 989.1808, Seg Loss: 0.1475, Heatmap Loss: 659.0721, Geometric Loss: 0.5314
=== train Corner Confidence Metrics ===
  avg_peak_value: 2.5094
  avg_peak_to_mean_ratio: 3.4831
  avg_peak_to_second_ratio: 0.5042
  detection_rate: 0.5000
  Overall Confidence Score: 1.7492
train Heatmap Components:
  mse_loss: 3.4903
  separation_loss: 500.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 500.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.5375
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.2071
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.72s/it] 
val Loss: 1978.7189, Seg Loss: 0.1858, Heatmap Loss: 1318.4414, Geometric Loss: 1.0886
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.5176
  avg_peak_to_mean_ratio: 2.4468
  avg_peak_to_second_ratio: 1.0046
  detection_rate: 1.0000
  Overall Confidence Score: 2.2422
val Heatmap Components:
  mse_loss: 7.4665
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0918
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0567

Epoch 39/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:07<00:00,  3.54s/it] 
train Loss: 565.2057, Seg Loss: 0.0992, Heatmap Loss: 376.5798, Geometric Loss: 0.2961
=== train Corner Confidence Metrics ===
  avg_peak_value: 1.3786
  avg_peak_to_mean_ratio: 0.7978
  avg_peak_to_second_ratio: 0.2867
  detection_rate: 0.2857
  Overall Confidence Score: 0.6872
train Heatmap Components:
  mse_loss: 1.9379
  separation_loss: 285.7143
  peak_separation_loss: 0.0000
  edge_suppression_loss: 285.7143
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.3130
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0830
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.72s/it] 
val Loss: 1976.5866, Seg Loss: 0.1527, Heatmap Loss: 1317.0643, Geometric Loss: 1.0469
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.2492
  avg_peak_to_mean_ratio: 2.4769
  avg_peak_to_second_ratio: 1.0064
  detection_rate: 1.0000
  Overall Confidence Score: 2.1831
val Heatmap Components:
  mse_loss: 6.2250
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0839
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000

Epoch 40/40
----------
train: 100%|█████████████████████████████████████████████████████████████████████████████████| 2/2 [00:05<00:00,  2.50s/it] 
train Loss: 282.4730, Seg Loss: 0.0591, Heatmap Loss: 188.1790, Geometric Loss: 0.1818
=== train Corner Confidence Metrics ===
  avg_peak_value: 0.7345
  avg_peak_to_mean_ratio: 0.4004
  avg_peak_to_second_ratio: 0.1433
  detection_rate: 0.1429
  Overall Confidence Score: 0.3553
train Heatmap Components:
  mse_loss: 0.8967
  separation_loss: 142.8571
  peak_separation_loss: 0.0000
  edge_suppression_loss: 142.8571
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 0.1568
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0000
val: 100%|███████████████████████████████████████████████████████████████████████████████████| 2/2 [00:09<00:00,  4.58s/it] 
val Loss: 1976.9191, Seg Loss: 0.1784, Heatmap Loss: 1317.2582, Geometric Loss: 1.0666
=== val Corner Confidence Metrics ===
  avg_peak_value: 4.2219
  avg_peak_to_mean_ratio: 2.4787
  avg_peak_to_second_ratio: 1.0069
  detection_rate: 1.0000
  Overall Confidence Score: 2.1769
val Heatmap Components:
  mse_loss: 6.3677
  separation_loss: 1000.0000
  peak_separation_loss: 0.0000
  edge_suppression_loss: 1000.0000
  peak_enhancement_loss: 0.0000
  peak_to_second_ratio_loss: 1.0815
  detection_rate_loss: 0.0000
  segmentation_guidance_loss: 0.0754

Training complete in 13m 5s
Best val loss: 1973.3640