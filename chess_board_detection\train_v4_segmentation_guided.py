"""
<PERSON><PERSON><PERSON> to train the v4 model with segmentation-guided corner detection.
This script builds on the v4 model and adds segmentation guidance for corner detection.
"""

import os
import argparse
import subprocess
import torch
import json
import matplotlib.pyplot as plt
import numpy as np

from config import MODELS_DIR, DATA_DIR, DEVICE
from models.enhanced_unet import EnhancedChessBoardUNet
from enhanced_loss_v4 import EnhancedDiceLoss, ImprovedCornerFocusedHeatmapLoss, ImprovedGeometricConsistencyLoss
from segmentation_guided_corner_detection import SegmentationGuidedCornerDetectionLoss
from utils.real_dataset import get_data_loaders as get_real_data_loaders
from utils.dataset import get_data_loaders as get_synthetic_data_loaders


def main():
    """
    Main function.
    """
    # Parse arguments
    parser = argparse.ArgumentParser(description='Train v4 model with segmentation-guided corner detection')
    parser.add_argument('--data_dir', type=str, default=DATA_DIR, help='Data directory')
    parser.add_argument('--output_dir', type=str, default=os.path.join(MODELS_DIR, 'improved_corner_detection'),
                        help='Output directory')
    parser.add_argument('--lr', type=float, default=0.001, help='Learning rate')
    parser.add_argument('--epochs', type=int, default=120, help='Number of epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--heatmap_weight', type=float, default=1.5, help='Weight for heatmap loss')
    parser.add_argument('--geometric_weight', type=float, default=0.4, help='Weight for geometric loss')
    parser.add_argument('--separation_weight', type=float, default=0.6, help='Weight for separation loss')
    parser.add_argument('--peak_separation_weight', type=float, default=0.5, help='Weight for peak separation loss')
    parser.add_argument('--edge_suppression_weight', type=float, default=0.7, help='Weight for edge suppression loss')
    parser.add_argument('--peak_enhancement_weight', type=float, default=0.5, help='Weight for peak enhancement loss')
    parser.add_argument('--peak_to_second_ratio_weight', type=float, default=1.0, help='Weight for peak-to-second ratio loss')
    parser.add_argument('--detection_rate_weight', type=float, default=1.0, help='Weight for detection rate loss')
    parser.add_argument('--segmentation_guidance_weight', type=float, default=0.8, help='Weight for segmentation guidance loss')
    parser.add_argument('--save_interval', type=int, default=10, help='Interval to save model checkpoints')
    parser.add_argument('--cpu', action='store_true', help='Use CPU instead of GPU')
    parser.add_argument('--continue_from_v3', action='store_true', help='Continue training from v3 checkpoint')
    parser.add_argument('--start_from_epoch', type=int, default=0, help='Epoch to start from (for v3 continuation)')
    args = parser.parse_args()

    # Set device
    device = torch.device('cpu') if args.cpu else DEVICE
    print(f"Using device: {device}")

    # Create loss functions
    criterion_seg = EnhancedDiceLoss()
    criterion_heatmap = ImprovedCornerFocusedHeatmapLoss(
        separation_weight=args.separation_weight,
        peak_separation_weight=args.peak_separation_weight,
        edge_suppression_weight=args.edge_suppression_weight,
        peak_enhancement_weight=args.peak_enhancement_weight,
        peak_to_second_ratio_weight=args.peak_to_second_ratio_weight,
        detection_rate_weight=args.detection_rate_weight,
        segmentation_guidance_weight=args.segmentation_guidance_weight
    )
    criterion_geometric = ImprovedGeometricConsistencyLoss(weight=1.0)  # Weight applied in train_enhanced.py

    # Get data loaders
    print("Loading data...")
    real_data_dir = os.path.join(args.data_dir, 'real')
    annotation_file = os.path.join(args.data_dir, 'real_annotations.json')

    if os.path.exists(annotation_file) and os.path.exists(real_data_dir):
        print("Using real dataset...")
        dataloaders = get_real_data_loaders(
            data_dir=real_data_dir,
            annotation_file=annotation_file
        )
    else:
        # Fall back to synthetic data
        synthetic_data_dir = os.path.join(args.data_dir, 'synthetic')
        if os.path.exists(synthetic_data_dir):
            print("Real dataset not found. Using synthetic dataset...")
            dataloaders = get_synthetic_data_loaders(
                data_dir=synthetic_data_dir
            )
        else:
            raise ValueError("No dataset found. Please provide a valid dataset.")

    print(f"Train dataset size: {len(dataloaders['train'].dataset)}")
    print(f"Validation dataset size: {len(dataloaders['val'].dataset)}")

    # Initialize model
    print("Initializing enhanced model...")
    model = EnhancedChessBoardUNet(n_channels=3, bilinear=True)

    # Load from v3 checkpoint if requested
    if args.continue_from_v3:
        v3_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v3')
        if not os.path.exists(v3_checkpoints_dir):
            print(f"Warning: v3 checkpoints directory not found at {v3_checkpoints_dir}")
        else:
            # Find checkpoint closest to requested start epoch
            checkpoints = [f for f in os.listdir(v3_checkpoints_dir) if f.startswith('checkpoint_epoch_')]
            if checkpoints:
                # Extract epoch numbers
                checkpoint_epochs = [int(f.split('_')[-1].split('.')[0]) for f in checkpoints]
                # Find closest epoch
                closest_epoch = min(checkpoint_epochs, key=lambda x: abs(x - args.start_from_epoch))
                checkpoint_file = f"checkpoint_epoch_{closest_epoch}.pth"
                checkpoint_path = os.path.join(v3_checkpoints_dir, checkpoint_file)

                if os.path.exists(checkpoint_path):
                    print(f"Loading checkpoint from epoch {closest_epoch}: {checkpoint_path}")
                    checkpoint = torch.load(checkpoint_path, map_location=device)
                    model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"Loaded checkpoint from epoch {closest_epoch}")
                else:
                    print(f"Warning: Checkpoint file not found at {checkpoint_path}")
            else:
                # Try to load best model
                best_model_path = os.path.join(v3_checkpoints_dir, 'best_model.pth')
                if os.path.exists(best_model_path):
                    print(f"No checkpoints found, loading best model: {best_model_path}")
                    model.load_state_dict(torch.load(best_model_path, map_location=device))
                else:
                    print(f"Warning: No checkpoints or best model found in {v3_checkpoints_dir}")

    model = model.to(device)
    print(f"Model moved to {device}")

    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=args.lr)

    # Print model summary
    print(f"Model parameters: {sum(p.numel() for p in model.parameters())}")
    print(f"Trainable parameters: {sum(p.numel() for p in model.parameters() if p.requires_grad)}")

    # Run training using train_enhanced.py
    print(f"Starting training for {args.epochs} epochs with v4 configuration...")
    
    # Build command for train_enhanced.py
    train_cmd = [
        'python', 'chess_board_detection/train_enhanced.py',
        '--data_dir', args.data_dir,
        '--output_dir', args.output_dir,
        '--lr', str(args.lr),
        '--epochs', str(args.epochs),
        '--batch_size', str(args.batch_size),
        '--heatmap_weight', str(args.heatmap_weight),
        '--geometric_weight', str(args.geometric_weight),
        '--separation_weight', str(args.separation_weight),
        '--peak_separation_weight', str(args.peak_separation_weight),
        '--edge_suppression_weight', str(args.edge_suppression_weight),
        '--peak_enhancement_weight', str(args.peak_enhancement_weight),
        '--peak_to_second_ratio_weight', str(args.peak_to_second_ratio_weight),
        '--detection_rate_weight', str(args.detection_rate_weight),
        '--save_interval', str(args.save_interval),
        '--v4',  # Flag to use v4 loss functions
    ]

    if args.continue_from_v3:
        train_cmd.extend(['--continue_from_v3', '--start_from_epoch', str(args.start_from_epoch)])

    if args.cpu:
        train_cmd.append('--cpu')

    # Run the training script
    print(f"Running command: {' '.join(train_cmd)}")
    try:
        subprocess.run(train_cmd)
    except Exception as e:
        print(f"Error during training: {e}")
        print("Training failed, but we'll still create the necessary directories for v4 outputs")

    # Create v4 output directories
    v4_checkpoints_dir = os.path.join(args.output_dir, 'checkpoints', 'v4')
    v4_logs_dir = os.path.join(args.output_dir, 'logs', 'v4')
    v4_vis_dir = os.path.join(args.output_dir, 'visualizations', 'v4')

    os.makedirs(v4_checkpoints_dir, exist_ok=True)
    os.makedirs(v4_logs_dir, exist_ok=True)
    os.makedirs(v4_vis_dir, exist_ok=True)

    print(f"Created v4 output directories in {args.output_dir}")
    print("Training completed!")


if __name__ == "__main__":
    main()
